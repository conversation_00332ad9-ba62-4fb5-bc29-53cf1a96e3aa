<?php

declare(strict_types=1);

use <PERSON>\Config\RectorConfig;
use Rector\Set\ValueObject\LevelSetList;
use <PERSON>\Set\ValueObject\SetList;
use Rector<PERSON>ara<PERSON>\Set\LaravelSetList;
use <PERSON>\Php83\Rector\ClassMethod\AddOverrideAttributeToOverriddenMethodsRector;
use <PERSON>\TypeDeclaration\Rector\ClassMethod\AddVoidReturnTypeWhereNoReturnRector;
use <PERSON>\TypeDeclaration\Rector\Property\TypedPropertyFromStrictConstructorRector;
use Rector\CodeQuality\Rector\Class_\InlineConstructorDefaultToPropertyRector;
use <PERSON>\CodeQuality\Rector\ClassMethod\LocallyCalledStaticMethodToNonStaticRector;
use Rector\DeadCode\Rector\ClassMethod\RemoveUselessParamTagRector;
use Rector\DeadCode\Rector\ClassMethod\RemoveUselessReturnTagRector;
use Rector\DeadCode\Rector\Property\RemoveUselessVarTagRector;
use <PERSON>\Php80\Rector\Class_\ClassPropertyAssignToConstructorPromotionRector;
use <PERSON>\Php81\Rector\Property\ReadOnlyPropertyRector;
use <PERSON>\Php82\Rector\Class_\ReadOnlyClassRector;
use Rector\Php83\Rector\ClassConst\AddTypeToConstRector;

return RectorConfig::configure()
    ->withPaths([
        __DIR__ . '/app/Http/Controllers',
        __DIR__ . '/app/Models',
        __DIR__ . '/app/Console/Commands/Actions',
        __DIR__ . '/config',
        __DIR__ . '/routes',
    ])
    ->withSkip([
        // Skip vendor directory
        __DIR__ . '/vendor',
        // Skip storage and bootstrap cache
        __DIR__ . '/storage',
        __DIR__ . '/bootstrap/cache',
        // Skip specific files that might cause issues
        __DIR__ . '/app/Console/Kernel.php',
        // Skip files with PSR-4 compliance issues mentioned in composer output
        __DIR__ . '/app/Console/Commands/Actions/Reports/iRegisterAction.php',
        __DIR__ . '/app/Console/Commands/Actions/Settings/GeneralSettings/getAppPreferencesDataSource.php',
        __DIR__ . '/app/Console/Commands/Actions/Settings/GeneralSettings/EmailSmsActivityLogDataSource.php',
        __DIR__ . '/app/Console/Commands/Actions/Settings/settingCommonTimestampDataSource.php',
        __DIR__ . '/app/Console/Commands/Actions/Helpdesk/HelpTopic/HelpTopicActivityLogDataSource.php',
        __DIR__ . '/app/Console/Commands/Workflows/Account/GetLedgerByIdWorkflow.php',
        __DIR__ . '/app/Console/Commands/Workflows/Account/GetInvestmentByIdWorkflow.php',
        __DIR__ . '/app/Console/Commands/Workflows/Account/GetGroupByIdWorkflow.php',
        __DIR__ . '/app/Console/Commands/Workflows/Reports/iRegisterWorkflow.php',
        __DIR__ . '/app/Console/Commands/Workflows/Settings/settingCommonTimestampWorkflow.php',
        __DIR__ . '/app/Console/Commands/Workflows/Settings/AutoInvoicingRule/invoiceListingWorkflow.php',
        __DIR__ . '/app/Console/Commands/Workflows/Vendors/VendorBillListWorkflow.php',
        __DIR__ . '/app/Console/Commands/Workflows/Vendors/ViewVendorDetailsWorkflow.php',
        __DIR__ . '/app/Console/Commands/Workflows/Vendors/VendorAgingWorkflow.php',
        __DIR__ . '/app/Console/Commands/Workflows/Helpdesk/Escalation/EscalationAddWorkflow.php',
    ])
    ->withSets([
        // PHP 8.3 upgrade
        LevelSetList::UP_TO_PHP_83,

        // Laravel upgrades (step by step to Laravel 11)
        LaravelSetList::LARAVEL_110,
        LaravelSetList::LARAVEL_CODE_QUALITY,

        // Essential code quality improvements
        SetList::CODE_QUALITY,
        SetList::TYPE_DECLARATION,
    ])
    ->withRules([
        // PHP 8.3 specific rules
        AddOverrideAttributeToOverriddenMethodsRector::class,
        AddTypeToConstRector::class,
        
        // PHP 8.2 features
        ReadOnlyClassRector::class,
        
        // PHP 8.1 features
        ReadOnlyPropertyRector::class,
        
        // PHP 8.0 features
        ClassPropertyAssignToConstructorPromotionRector::class,
        
        // Type declarations
        AddVoidReturnTypeWhereNoReturnRector::class,
        TypedPropertyFromStrictConstructorRector::class,
        
        // Code quality
        InlineConstructorDefaultToPropertyRector::class,
        LocallyCalledStaticMethodToNonStaticRector::class,
        
        // Clean up documentation
        RemoveUselessParamTagRector::class,
        RemoveUselessReturnTagRector::class,
        RemoveUselessVarTagRector::class,
    ])
    ->withPhpSets(php83: true);
