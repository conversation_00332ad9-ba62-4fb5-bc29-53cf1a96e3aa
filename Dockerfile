FROM php:8.3-fpm-alpine3.18 AS builder

# a) System deps + CA certs for HTTPS
RUN apk update && apk upgrade && apk add --no-cache \
      autoconf \
      git \
      build-base \
      zip \
      oniguruma-dev \
      libzip-dev \
      zlib-dev \
      gmp-dev \
      openssl-dev \
      linux-headers \
      mariadb-dev \
      zstd-dev \
      freetype-dev \
      libjpeg-turbo-dev \
      libpng-dev \
      ca-certificates \
  && update-ca-certificates

# b) Built-in PHP extensions
RUN docker-php-ext-configure gd --with-freetype --with-jpeg \
  && docker-php-ext-install -j$(nproc) \
      gd mbstring zip exif pcntl sockets gmp bcmath pdo_mysql

# c) PECL channel-refresh
RUN pecl channel-update pecl.php.net

# d) APCu (explicit for PHP 8.3)
RUN pecl install apcu-5.1.22 \
  && docker-php-ext-enable apcu

# e) MongoDB
RUN pecl install mongodb-1.15.0 \
  && docker-php-ext-enable mongodb


# …then Composer, copy your code, dump-autoload, etc.

# Install Composer
COPY --from=composer:latest /usr/bin/composer /usr/local/bin/composer

# Set working directory
WORKDIR /var/www/html

# Copy composer files
COPY composer.json ./


# Require the S3 adapter and then install everything
#RUN composer install --no-scripts --no-dev --optimize-autoloader  

# Install dependencies
RUN composer install --no-scripts --no-autoloader --no-dev

# Copy application files
COPY . .
# 3. Now require Predis (scripts will succeed, because artisan exists)
RUN composer require predis/predis

# Generate optimized autoload files
RUN composer dump-autoload --optimize

# Set permissions
RUN chmod +x artisan

# Final stage
FROM php:8.3-fpm-alpine3.18

# Install runtime dependencies
RUN apk add --no-cache \
    libzip \
    gmp \
    zstd-libs \
    oniguruma \
    mariadb-connector-c \
    freetype \
    libjpeg-turbo \
    libpng 

# Copy PHP extensions and configurations from builder
COPY --from=builder /usr/local/lib/php/extensions/ /usr/local/lib/php/extensions/
COPY --from=builder /usr/local/etc/php/conf.d/ /usr/local/etc/php/conf.d/

# Force-enable GD if the .ini wasn’t dropped
RUN echo "extension=gd.so" > /usr/local/etc/php/conf.d/20-gd.ini

# Set working directory
WORKDIR /var/www/html

# Copy application files from builder
COPY --from=builder /var/www/html /var/www/html

# Expose port
EXPOSE 8051

# Start Laravel's development server
CMD ["php", "artisan", "serve", "--host", "0.0.0.0", "--port", "8051"]
