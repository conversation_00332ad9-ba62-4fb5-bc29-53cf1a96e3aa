<?php

return [
    /*
    |--------------------------------------------------------------------------
    | MCP Server Configuration
    |--------------------------------------------------------------------------
    |
    | This file contains the configuration for the MCP server.
    |
    */

    'host' => env('MCP_HOST', '127.0.0.1'),
    'port' => env('MCP_PORT', 8080),
    'debug' => env('MCP_DEBUG', false),

    /*
    |--------------------------------------------------------------------------
    | Database Configuration
    |--------------------------------------------------------------------------
    |
    | Configuration for the database connection.
    |
    */
    'database' => [
        'driver' => env('DB_CONNECTION', 'mysql'),
        'host' => '127.0.0.1',
        'port' => env('DB_PORT', '3306'),
        'database' => 'soc_db_8485',
        'username' => 'root',
        'password' => 'YOUR_PASSWORD_HERE',
        'charset' => 'utf8mb4',
        'collation' => 'utf8mb4_unicode_ci',
        'prefix' => '',
        'strict' => true,
        'engine' => null,
    ],
]; 