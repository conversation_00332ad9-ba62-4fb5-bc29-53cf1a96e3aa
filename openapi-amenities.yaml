openapi: 3.0.3
info:
  title: Amenities Booking API
  version: 1.0.0
  description: API for managing amenities, bookings, and pin verification in a society management system.
servers:
  - url: http://localhost/api
paths:
  /amenities:
    get:
      summary: List all amenities
      tags:
        - Amenities
      responses:
        '200':
          description: List of amenities
    post:
      summary: Add a new amenity
      tags:
        - Amenities
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
      responses:
        '201':
          description: Amenity created
  /amenities/{id}:
    get:
      summary: Get amenity details
      tags:
        - Amenities
      parameters:
        - in: path
          name: id
          required: true
          schema:
            type: integer
      responses:
        '200':
          description: Amenity details
    put:
      summary: Edit an amenity
      tags:
        - Amenities
      parameters:
        - in: path
          name: id
          required: true
          schema:
            type: integer
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
      responses:
        '200':
          description: Amenity updated
    delete:
      summary: Delete an amenity
      tags:
        - Amenities
      parameters:
        - in: path
          name: id
          required: true
          schema:
            type: integer
      responses:
        '200':
          description: Amenity deleted
  /amenities/{amenity_id}/bookings:
    post:
      summary: Book an amenity
      tags:
        - Bookings
      parameters:
        - in: path
          name: amenity_id
          required: true
          schema:
            type: integer
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                user_id:
                  type: integer
                slot_id:
                  type: integer
                date:
                  type: string
                  format: date
      responses:
        '201':
          description: Booking created
    get:
      summary: Get bookings and slot configuration for an amenity
      tags:
        - Bookings
      parameters:
        - in: path
          name: amenity_id
          required: true
          schema:
            type: integer
        - in: query
          name: page
          schema:
            type: integer
          description: Page number for pagination
        - in: query
          name: perPage
          schema:
            type: integer
          description: Items per page
      responses:
        '200':
          description: Amenity bookings and slot configuration
  /users/{user_id}/bookings:
    get:
      summary: Get all bookings for a user
      tags:
        - Bookings
      parameters:
        - in: path
          name: user_id
          required: true
          schema:
            type: integer
      responses:
        '200':
          description: List of bookings for user
  /users/{user_id}/amenities/{amenity_id}/bookings:
    get:
      summary: Get bookings for a user for a specific amenity
      tags:
        - Bookings
      parameters:
        - in: path
          name: user_id
          required: true
          schema:
            type: integer
        - in: path
          name: amenity_id
          required: true
          schema:
            type: integer
      responses:
        '200':
          description: List of bookings for user and amenity
  /amenities/{amenity_id}/bookings/{booking_id}:
    patch:
      summary: Cancel a booking
      tags:
        - Bookings
      parameters:
        - in: path
          name: amenity_id
          required: true
          schema:
            type: integer
        - in: path
          name: booking_id
          required: true
          schema:
            type: integer
      responses:
        '200':
          description: Booking cancelled
        '400':
          description: Booking not found or already cancelled
  /amenities/verify-pin:
    post:
      summary: Verify a booking pin
      tags:
        - Bookings
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                booking_id:
                  type: integer
                pin:
                  type: string
      responses:
        '200':
          description: Pin verified
        '400':
          description: Invalid pin or booking is not in a valid time range
