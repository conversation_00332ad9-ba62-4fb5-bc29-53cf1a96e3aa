<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;

class AmenitiesController extends Controller
{
    public function amenitiesList(Request $request)
    {
        return $this->workflow('workflow:amenitiesList', $request->all());
    }

    public function amenitiesAdd(Request $request)
    {   
        return $this->workflow('workflow:amenitiesAdd', $request->all());
    }

    public function amenitiesDetail(Request $request, $id)
    {
        $request = $request->merge(["id" => $id]);
        return $this->workflow('workflow:amenitiesGetById', $request->all());
    }

    public function amenitiesEdit(Request $request, $id)
    {
        $request = $request->merge(["id" => $id]);
        return $this->workflow('workflow:amenitiesUpdate', $request->all());
    }

    public function amenitiesDelete(Request $request, $id)
    {
        $request = $request->merge(["id" => $id]);
        return $this->workflow('workflow:amenitiesDelete', $request->all());
    }

    public function amenitiesBook(Request $request)
    {
        $request = $request->merge(["amenity_id" => $request->amenity_id]);
        
        return $this->workflow('workflow:amenitiesBook', $request->all());
    }

    /**
     * Get all bookings for a user (across all amenities)
     */
    public function getUserBookings(Request $request, $user_id)
    {
        $request = $request->merge(['user_id' => $user_id]);
        $request = $request->merge(['status' => $request->status]);
        
        return $this->workflow('workflow:amenitiesGetUserAllBookings', $request->all());
    }

    /**
     * Get bookings for a user for a given amenity (with pagination)
     */
    public function getUserBooking(Request $request, $user_id, $amenity_id)
    {
        $request = $request->merge(['amenity_id' => $amenity_id, 'user_id' => $user_id]);
        return $this->workflow('workflow:amenitiesGetUserBookings', $request->all());
    }

    public function getSlots(Request $request)
    {
        $request = $request->merge(["amenity_id" => $request->amenity_id]);
        return $this->workflow('workflow:amenitiesGetSlots', $request->all());
    }

    /**
     * Verify booking pin for a booking (validates pin and time range)
     */
    public function verifyBookingPin(Request $request)
    {
        // $requestData = $request->only(['booking_id', 'pin']);
        return $this->workflow('workflow:amenitiesVerifyBookingPin', $request->all());
    }

    /**
     * Cancel a booking by booking_id
     */
    public function cancelBooking(Request $request, $amenity_id, $booking_id)
    {
        $request = $request->merge(['booking_id' => $booking_id]);
        return $this->workflow('workflow:amenitiesCancelBooking', $request->all());
    }
}
