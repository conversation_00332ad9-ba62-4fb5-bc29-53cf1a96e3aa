<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;
use App\Models\Masters\{ChsoneSocDbMaster,ChsoneSocietiesMaster};
use Illuminate\Support\Facades\DB;

class TenantIdVerification
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        if(!empty($request->cookie('company_id'))){
            $request->merge(['company_id' => ltrim($request->cookie('company_id'),0)]);
        }
        $tenant = !empty($request->header('company_id')) ? $request->header('company_id') : $request->input('company_id');
        $tenant = ltrim($tenant,0);
        if ($tenant) {
            $dBCredentials = ChsoneSocDbMaster::where(['soc_id' => $tenant])->first();
            if(!$dBCredentials){
                $checkCompanyInSSO = DB::connection('old_sso')->table('companies')->where('company_id',$tenant)->first();
                if($checkCompanyInSSO){
                    $oldSsoCompanyName = $checkCompanyInSSO->company_name;
                    $societyDetails = ChsoneSocietiesMaster::where('soc_subdomain_name',$oldSsoCompanyName)->orWhere('soc_name',$oldSsoCompanyName)->first();
                    if($societyDetails){
                        $societyId = $societyDetails->soc_id;
                        $request->merge(['company_id' => $societyId]);
                        $dBCredentials = ChsoneSocDbMaster::where(['soc_id' => $societyId])->first();
                    }
                }
            }
            if (!$dBCredentials) {
                return response()->json(['message' => 'Invalid company ID.','status'=>'error','statusCode'=> 400,'data'=>[]], 400);
            }
        } else {
            return response()->json(['message'=>'Missing company ID.','status'=>'error','statusCode'=> 400,'data'=>[]], 400);
        }
        return $next($request);
    }
}
