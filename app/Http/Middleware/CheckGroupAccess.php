<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;
use GuzzleHttp\Client;

class CheckGroupAccess
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        $token = $request->bearerToken() ?? $request->header('x-access-token') ?? $request->cookie('x-access-token');
        $companyId = ltrim($request->company_id,0);

        $groupId = config('constants.KEYCLOAK_REALM_GROUP_PREFIX'). $companyId;
        $groupAccess = $this->checkGroupAccess($request->sso_user_id , $groupId, $token);
        if($groupAccess !== true){
            return response()->json([
                'message' => $groupAccess,
                'status' => 'error',
                'status_code' => Response::HTTP_UNAUTHORIZED,
                'data' => []
            ], Response::HTTP_UNAUTHORIZED);
        }
        return $next($request);
    }

    public function checkGroupAccess($userId, $groupId)
    {

        $client = new Client();
        $baseUrl = config('constants.KEYCLOAK_BASE_URL');
        $realm = config('constants.KEYCLOAK_REALM');
        $clientId = config('constants.KEYCLOAK_CLIENT_ID');
        $clientSecret = config('constants.KEYCLOAK_CLIENT_SECRET');

        $response = $client->post($baseUrl."realms/".$realm."/protocol/openid-connect/token", [
            'headers' => [
                'Content-Type' => 'application/x-www-form-urlencoded',
            ],
            'form_params' => [
                'grant_type' => 'client_credentials',
                'client_id' => $clientId,
                'client_secret' => $clientSecret
            ]
        ]);
        $data = json_decode($response->getBody()->getContents(), true);
        $accessToken = $data['access_token'];

        $response = $client->get($baseUrl."admin/realms/".$realm."/users/".$userId, [
            'headers' => [
                'Authorization' => 'Bearer ' . $accessToken,
                'Content-Type' => 'application/json',
            ]
        ]);

        $result = json_decode($response->getBody()->getContents(), true);
        // $groupAccess = json_decode($result['attributes']['groupAccess'][0], true);

        // $message = $this->hasSocietyAppAccess($groupAccess, $groupId);
        // if($message){
        //     return $message;
        // }
        return true;
    }

    private function hasSocietyAppAccess($groupAccess, $groupId) {
        // if($groupId == "C_14"){
        //     $groupId = "C_8196";
        // }
        
        // // Check if the company exists in the dataset
        // if (!isset($groupAccess[$groupId])) {
        //     return "User has no access to society app for provided company.";
        // }

        // // Check if society_app exists and has at least one role
        // // pending to implement groupAccess
        // if ((!isset($groupAccess[$groupId]['society_app']) || empty($groupAccess[$groupId]['society_app'])) && (!isset($groupAccess[$groupId]['vizlog']) ||  empty($groupAccess[$groupId]['vizlog']))) {
        //     return "User has no access to society app for provided company.";
        // }
    }
}
