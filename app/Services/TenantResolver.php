<?php

namespace App\Services;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Cache;

class TenantResolver
{
    /**
     * The tenant resolver configuration.
     *
     * @var array
     */
    protected $config;

    /**
     * The tenant configuration.
     *
     * @var array
     */
    protected $tenantConfig;

    /**
     * Create a new tenant resolver instance.
     *
     * @param  array  $config
     * @param  array  $tenantConfig
     * @return void
     */
    public function __construct(array $config, array $tenantConfig)
    {
        $this->config = $config;
        $this->tenantConfig = $tenantConfig;
    }

    /**
     * Resolve the tenant from the request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return object|null
     */
    public function resolve(Request $request)
    {
        $tenantId = $this->getTenantIdFromRequest($request);

        if (!$tenantId) {
            return null;
        }

        return Cache::remember("tenant.{$tenantId}", 3600, function () use ($tenantId) {
            return DB::connection('master')
                ->table($this->config['table'])
                ->where('id', $tenantId)
                ->first();
        });
    }

    /**
     * Get the tenant ID from the request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return string|null
     */
    protected function getTenantIdFromRequest(Request $request)
    {
        // Check header
        if ($request->hasHeader($this->tenantConfig['header'])) {
            return $request->header($this->tenantConfig['header']);
        }

        // Check cookie
        if ($request->hasCookie($this->tenantConfig['cookie'])) {
            return $request->cookie($this->tenantConfig['cookie']);
        }

        // Check query parameter
        if ($request->has($this->tenantConfig['query_param'])) {
            return $request->input($this->tenantConfig['query_param']);
        }

        // Check subdomain
        if ($this->tenantConfig['subdomain']) {
            $host = $request->getHost();
            $parts = explode('.', $host);
            
            if (count($parts) > 2) {
                return $parts[0];
            }
        }

        return null;
    }
} 