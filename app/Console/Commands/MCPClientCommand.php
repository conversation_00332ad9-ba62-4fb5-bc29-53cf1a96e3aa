<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;

class MC<PERSON>lientCommand extends Command
{
    protected $signature = 'mcp:client {action} {--table=} {--query=}';
    protected $description = 'MCP Client to interact with MCP Server';

    public function handle()
    {
        $action = $this->argument('action');
        $table = $this->option('table');
        $query = $this->option('query');

        $request = [
            'action' => $action
        ];

        if ($table) {
            $request['table'] = $table;
        }

        if ($query) {
            $request['query'] = $query;
        }

        $socket = socket_create(AF_INET, SOCK_STREAM, SOL_TCP);
        if ($socket === false) {
            $this->error('Failed to create socket');
            return 1;
        }

        $result = socket_connect($socket, '127.0.0.1', 8080);
        if ($result === false) {
            $this->error('Failed to connect to MCP server');
            return 1;
        }

        socket_write($socket, json_encode($request), strlen(json_encode($request)));
        $response = socket_read($socket, 4096);
        socket_close($socket);

        $this->info('Response from MCP server:');
        $this->line($response);
    }
} 