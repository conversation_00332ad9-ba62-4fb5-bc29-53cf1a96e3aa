<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;

class MCPServeCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'mcp:serve {--host=127.0.0.1} {--port=8080}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Start the MCP server';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $host = $this->option('host');
        $port = $this->option('port');

        $this->info("Starting MCP server on {$host}:{$port}...");

        // Create a TCP/IP socket
        $socket = socket_create(AF_INET, SOCK_STREAM, SOL_TCP);
        if ($socket === false) {
            $this->error('Failed to create socket: ' . socket_strerror(socket_last_error()));
            return 1;
        }

        // Set socket options
        socket_set_option($socket, SOL_SOCKET, SO_REUSEADDR, 1);

        // Bind the socket to the address and port
        if (socket_bind($socket, $host, $port) === false) {
            $this->error('Failed to bind socket: ' . socket_strerror(socket_last_error($socket)));
            return 1;
        }

        // Start listening for connections
        if (socket_listen($socket, 5) === false) {
            $this->error('Failed to listen on socket: ' . socket_strerror(socket_last_error($socket)));
            return 1;
        }

        $this->info('MCP server is running...');

        while (true) {
            // Accept incoming connections
            $client = socket_accept($socket);
            if ($client === false) {
                $this->error('Failed to accept connection: ' . socket_strerror(socket_last_error($socket)));
                continue;
            }

            // Read client data
            $data = socket_read($client, 1024);
            if ($data === false) {
                $this->error('Failed to read data: ' . socket_strerror(socket_last_error($client)));
                socket_close($client);
                continue;
            }

            // Process the data
            $response = $this->processData($data);

            // Send response back to client
            socket_write($client, $response, strlen($response));
            socket_close($client);
        }

        socket_close($socket);
        return 0;
    }

    /**
     * Process the received data.
     *
     * @param string $data
     * @return string
     */
    protected function processData($data)
    {
        try {
            $request = json_decode($data, true);
            
            if (!$request || !isset($request['action'])) {
                return json_encode([
                    'status' => 'error',
                    'message' => 'Invalid request format'
                ]);
            }

            switch ($request['action']) {
                case 'get_tables':
                    return $this->getTables();
                case 'get_table_structure':
                    return $this->getTableStructure($request['table'] ?? '');
                case 'execute_query':
                    return $this->executeQuery($request['query'] ?? '');
                default:
                    return json_encode([
                        'status' => 'error',
                        'message' => 'Unknown action'
                    ]);
            }
        } catch (\Exception $e) {
            Log::error('MCP Server Error: ' . $e->getMessage());
            return json_encode([
                'status' => 'error',
                'message' => $e->getMessage()
            ]);
        }
    }

    /**
     * Get all tables in the database.
     *
     * @return string
     */
    protected function getTables()
    {
        try {
            $config = config('mcp-server.database');
            $this->info('Using database config: ' . json_encode($config));
            
            $tables = DB::connection('mcp')->select('SHOW TABLES LIKE "facility_%"');
            return json_encode([
                'status' => 'success',
                'data' => $tables
            ]);
        } catch (\Exception $e) {
            $this->error('Database error: ' . $e->getMessage());
            return json_encode([
                'status' => 'error',
                'message' => $e->getMessage()
            ]);
        }
    }

    /**
     * Get structure of a specific table.
     *
     * @param string $table
     * @return string
     */
    protected function getTableStructure($table)
    {
        if (empty($table)) {
            return json_encode([
                'status' => 'error',
                'message' => 'Table name is required'
            ]);
        }

        $structure = DB::connection('mcp')->select("DESCRIBE {$table}");
        return json_encode([
            'status' => 'success',
            'data' => $structure
        ]);
    }

    /**
     * Execute a SQL query.
     *
     * @param string $query
     * @return string
     */
    protected function executeQuery($query)
    {
        if (empty($query)) {
            return json_encode([
                'status' => 'error',
                'message' => 'Query is required'
            ]);
        }

        $result = DB::connection('mcp')->select($query);
        return json_encode([
            'status' => 'success',
            'data' => $result
        ]);
    }
} 