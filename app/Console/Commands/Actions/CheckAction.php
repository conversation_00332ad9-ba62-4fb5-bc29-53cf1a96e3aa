<?php

namespace App\Console\Commands\Actions;

use Illuminate\Console\Command;
use App\Console\Commands\Action;
use App\Models\WorkflowLogs;

class CheckAction extends Action
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'action:check {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */
    public function apply()
    {
        //
        //echo $this->flowId . ' ' . $this->input;

    }

}
