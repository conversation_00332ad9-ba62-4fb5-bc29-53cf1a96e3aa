<?php

namespace App\Console\Commands\Actions\NOC;

use Illuminate\Console\Command;
use App\Console\Commands\Action;

class ApproveOrRejectNocDataSource extends Action
{
    protected $signature = 'datasource:approveOrRejectNoc {flowId} {parentId} {input}';

    protected $description = 'Action for Approve Or Reject NOC Data Source';

    public function apply()
    {
        $id = $this->input['id'];
        $status = $this->input['status'];

        $this->tenantDB()->table('chsone_noc_requests')
            ->where('noc_request_id', $id)
            ->update(['status' => $status]);
    }
}