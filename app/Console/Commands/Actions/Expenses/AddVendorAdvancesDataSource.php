<?php

namespace App\Console\Commands\Actions\Expenses;
use App\Console\Commands\Action;
use App\Models\Tenants\ChsoneCreditAccount;
use App\Models\Tenants\ExpenseInvoicePaymentTracker;
use App\Models\Tenants\ChsoneVendorsMaster;
use App\Models\Tenants\ChsoneGrpLedgerTree;
use App\Models\Tenants\ChsoneLedgerTransaction;

class AddVendorAdvancesDataSource extends Action
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'datasource:addVendorAdvances {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Action for addVendorAdvances DataSource';

    /**
     * Execute the console command.
     */
    public function apply()
    {
        $data = $this->input;

        // insert the data into expense_invoice_payment_tracker table
        $expenseInvoicePaymentTracker = new ExpenseInvoicePaymentTracker();
        $expenseInvoicePaymentTracker->soc_id = $data['company_id'];
        $expenseInvoicePaymentTracker->vendor_id = $data['vendor_id'];
        $expenseInvoicePaymentTracker->payment_number = $data['payment_number'] ?? '';
        $expenseInvoicePaymentTracker->bill_type = 'Advance';
        $expenseInvoicePaymentTracker->payment_mode = $data['payment_mode'];
        if($data['payment_mode'] == 'cheque'){ 
            $expenseInvoicePaymentTracker->transaction_reference = $data['transaction_reference'];
            $expenseInvoicePaymentTracker->cheque_date = $data['cheque_payment_date'];
            $expenseInvoicePaymentTracker->payment_date = $data['cheque_payment_date'];
        } elseif($data['payment_mode'] == 'cashtransfer'){
            $expenseInvoicePaymentTracker->transaction_reference = $data['payment_reference'];
            $expenseInvoicePaymentTracker->cheque_date = $data['payment_date'];
            $expenseInvoicePaymentTracker->payment_date = $data['payment_date'];
        } elseif($data['payment_mode'] == 'cash'){
            $expenseInvoicePaymentTracker->cheque_date = $data['payment_date'];
            $expenseInvoicePaymentTracker->payment_date = $data['payment_date'];
        } elseif($data['payment_mode'] == 'online'){
            $expenseInvoicePaymentTracker->transaction_reference = $data['payment_reference'];
            $expenseInvoicePaymentTracker->cheque_date = $data['payment_date'];
            $expenseInvoicePaymentTracker->payment_instrument = $data['payment_instrument'];
            $expenseInvoicePaymentTracker->payment_date = $data['payment_date'];
        }
        $expenseInvoicePaymentTracker->paid_by = $data['user_id'];
        $expenseInvoicePaymentTracker->total_due_amount = '0.00';
        $expenseInvoicePaymentTracker->writeoff_amount = '0.00';
        if($data['type'] == 'refundable'){
            $expenseInvoicePaymentTracker->payment_amount = $data['refundable_amount'];
        } elseif($data['type'] == 'adjustable'){
            $expenseInvoicePaymentTracker->payment_amount = $data['adjustable_amount'];
        } elseif($data['type'] == 'refundable_adjustable'){
            $expenseInvoicePaymentTracker->payment_amount = $data['refundable_amount'] + $data['adjustable_amount'];
        }
        $expenseInvoicePaymentTracker->status = 'Y';
        $expenseInvoicePaymentTracker->transaction_status = 'complete';
        $expenseInvoicePaymentTracker->payment_note = $data['narration'];
        // $expenseInvoicePaymentTracker->payment_date = $data['payment_date'];
        $expenseInvoicePaymentTracker->updated_by = $data['user_id'];
        $expenseInvoicePaymentTracker->updated_date = date('Y-m-d 00:00:00');
        $expenseInvoicePaymentTracker->created_by = $data['user_id'];
        $expenseInvoicePaymentTracker->created_date = date('Y-m-d 00:00:00');

        // create other information
        // need to find bank_ledger, card_ledger where payment_mode is cash
        if($data['payment_mode'] == 'cash'){
            $ledgerAccount = ChsoneGrpLedgerTree::where('context', 'bank')->where('entity_type', 'ledger')->first();
            $bank_ledger = $ledgerAccount->ledger_account_id;
            $card_ledger = $ledgerAccount->ledger_account_id;
            
            $cashLedger = ChsoneGrpLedgerTree::where('context', 'cash')->where('entity_type', 'ledger')->first();
            $cash_ledger = $cashLedger->ledger_account_id;
            $data['cr_account_id'] = $cashLedger->ledger_account_id;
            $data['cr_account_name'] = $cashLedger->ledger_account_name;
        } elseif($data['payment_mode'] == 'cheque') {
            $ledgerAccount = ChsoneGrpLedgerTree::where('context', 'bank')->where('entity_type', 'ledger')->first();
            $bank_ledger = $ledgerAccount->ledger_account_id;
            $card_ledger = $ledgerAccount->ledger_account_id;

            $data['cr_account_id'] = $ledgerAccount->ledger_account_id;
            $data['cr_account_name'] = $ledgerAccount->ledger_account_name;
        } elseif($data['payment_mode'] == 'cashtransfer') {
            $ledgerAccount = ChsoneGrpLedgerTree::where('context', 'bank')->where('entity_type', 'ledger')->first();
            $bank_ledger = $ledgerAccount->ledger_account_id;
            $card_ledger = $ledgerAccount->ledger_account_id;

            $data['cr_account_id'] = $ledgerAccount->ledger_account_id;
            $data['cr_account_name'] = $ledgerAccount->ledger_account_name;
        } elseif($data['payment_mode'] == 'online') {
            $ledgerAccount = ChsoneGrpLedgerTree::where('context', 'bank')->where('entity_type', 'ledger')->first();
            $bank_ledger = $ledgerAccount->ledger_account_id;
            $card_ledger = $ledgerAccount->ledger_account_id;

            $data['cr_account_id'] = $ledgerAccount->ledger_account_id;
            $data['cr_account_name'] = $ledgerAccount->ledger_account_name;
        }


        $arrOtherInfo = array();
        $arrOtherInfo['bank_ledger'] = $bank_ledger ?? '';
        $arrOtherInfo['cash_ledger'] = $cash_ledger ?? '';
        $arrOtherInfo['card_ledger'] = $card_ledger ?? '';
        $arrOtherInfo['credit_used_type'] = $data['type'] ?? '';
        $arrOtherInfo['adjustable_date'] = $data['adjust_after_date'] ?? '';

        if($data['type'] == 'refundable'){
            $arrOtherInfo['refundable_vendor_bill_payment_amount'] = $expenseInvoicePaymentTracker->payment_amount;
            $arrOtherInfo['adjustable_vendor_bill_payment_amount'] = '0.00';
        } elseif($data['type'] == 'adjustable'){
            $arrOtherInfo['refundable_vendor_bill_payment_amount'] = '0.00';
            $arrOtherInfo['adjustable_vendor_bill_payment_amount'] = $expenseInvoicePaymentTracker->payment_amount;
        } elseif($data['type'] == 'refundable_adjustable'){
            $arrOtherInfo['refundable_vendor_bill_payment_amount'] = $data['refundable_amount'];
            $arrOtherInfo['adjustable_vendor_bill_payment_amount'] = $data['adjustable_amount'];
        }

        $expenseInvoicePaymentTracker->other_information = serialize($arrOtherInfo);


        $expenseInvoicePaymentTracker->save();

        // check if data is inserted
        if($expenseInvoicePaymentTracker->id){
            
            // insert the data into chsone_credit_accounts table
            // if payment_mode is refundable_adjustable then insert the data into chsone_credit_accounts table twice with type refundable and adjustable
            // explode type refundable_adjustable into refundable and adjustable and insert the data into chsone_credit_accounts table using foreach loop
            if($data['type'] == 'refundable_adjustable'){
                $arrType = explode("_", $data['type']);
                foreach($arrType as $type){
                    $data['type'] = $type;
                    $insertCreditAccount = $this->insertCreditAccount($data, $expenseInvoicePaymentTracker->id);
                    if($type == 'adjustable'){
                        $data['type'] = 'refundable_adjustable';
                    }
                }
            } else {
                $insertCreditAccount = $this->insertCreditAccount($data, $expenseInvoicePaymentTracker->id);
            }

            if(!$insertCreditAccount) {
                $this->message = "Error in creating vendor credit entry in chsone_credit_accounts table.";
                $this->status = 'error';
                $this->statusCode = 400;
                return;
            }

            // insert the data into chsone_ledger_transactions table pending
            if($data['type'] == 'refundable_adjustable'){
                $arrType = explode("_", $data['type']);
                foreach($arrType as $type){
                    $data['type'] = $type;
                    $insertLedgerTransaction = $this->insertLedgerTransaction($data, $expenseInvoicePaymentTracker->id);
                }
            } else {
                $insertLedgerTransaction = $this->insertLedgerTransaction($data, $expenseInvoicePaymentTracker->id);
            }

            if(!$insertLedgerTransaction) {
                $this->message = "Error in creating vendor ledger transaction entry in chsone_ledger_transactions table.";
                $this->status = 'error';
                $this->statusCode = 400;
                return;
            }

            $this->message = "Vendor advance added successfully";
            $this->status = 'success';
            $this->statusCode = 200;   
        } else {
            $this->message = "Error while adding vendor advance";
            $this->status = 'error';
            $this->statusCode = 400;
        }
    }


    /**
     * Insert data into chsone_credit_accounts table
     * 
     * @param array $data
     * @param int $expenseInvoicePaymentTrackerId
     * 
     * @return bool
     */
    private function insertCreditAccount($data, $expenseInvoicePaymentTrackerId)
    {
        // need to fetch the vendor name from chsone_vendor_masters table whose vendor_id is $data['vendor_id']
        $vendor = ChsoneVendorsMaster::where('vendor_id', $data['vendor_id'])->first();
        $account_name = $vendor->vendor_name;
        
        // fetch ledger_account_id from chsone_grp_ledger_tree table whose context is cash
        if($data['payment_mode'] == 'cash'){
            $ledgerAccount = ChsoneGrpLedgerTree::where('context', 'cash')->where('entity_type', 'ledger')->first();
            $income_account_id = $ledgerAccount->ledger_account_id;
        }

        $creditAccount = new ChsoneCreditAccount();
        $creditAccount->soc_id = $data['company_id'];
        $creditAccount->payment_tracker_id = $expenseInvoicePaymentTrackerId;
        if($data['payment_mode'] == 'cash' || $data['payment_mode'] == 'cashtransfer' || $data['payment_mode'] == 'online'){
            $creditAccount->payment_date = $data['payment_date'];
        } elseif($data['payment_mode'] == 'cheque') {
            $creditAccount->payment_date = $data['cheque_payment_date'];
        }
        // $creditAccount->payment_date = $data['payment_date'];
        $creditAccount->account_id = $data['vendor_id'];
        $creditAccount->account_name = $account_name;
        $creditAccount->account_context = 'Vendor';
        if($data['type'] == 'refundable'){
            $creditAccount->amount = $data['refundable_amount'];
            $creditAccount->narration = ucfirst($data['type']).' advance received via '.ucfirst($data['payment_mode']).'('.ucfirst($data['narration']).')';
            $creditAccount->use_credit = $data['type'];
        } elseif($data['type'] == 'adjustable'){
            $creditAccount->amount = $data['adjustable_amount'];
            $creditAccount->narration = ucfirst($data['type']).' advance received via '.ucfirst($data['payment_mode']).'('.ucfirst($data['narration']).')';
            $creditAccount->use_credit = $data['type'];
            $creditAccount->use_credit_after = $data['adjust_after_date'];
        }
        $creditAccount->type = 'expense';
        $creditAccount->payment_mode = $data['payment_mode'];
        $creditAccount->transaction_type = 'dr';

        if($data['payment_mode'] == 'cheque' || $data['payment_mode'] == 'cashtransfer' || $data['payment_mode'] == 'online'){
            $creditAccount->reference_no = $data['transaction_reference'] ?? $data['payment_reference'];
            $creditAccount->income_account_id = $data['bank_account_id'];
        } else {
            $creditAccount->income_account_id = $income_account_id;
        }
        
        $creditAccount->is_locked = '0';
        $creditAccount->context = 'user';
        $creditAccount->updated_by = $data['user_id'];
        $creditAccount->updated_date = date('Y-m-d 00:00:00');
        $creditAccount->created_by = $data['user_id'];
        $creditAccount->created_date = date('Y-m-d 00:00:00');
        $creditAccount->save();

        if($creditAccount){
            return true;
        }else{
            return false;
        }
    }


    /**
     * Insert data into chsone_ledger_transactions table
     * 
     * @param array $data
     * @param int $expenseInvoicePaymentTrackerId
     * 
     * @return bool
     */
    private function insertLedgerTransaction($data, $expenseInvoicePaymentTrackerId)
    {
        // need to fetch dr_account_id and dr_account_name from chsone_vendor_masters table whose vendor_id is $data['vendor_id']
        $vendor = ChsoneVendorsMaster::where('vendor_id', $data['vendor_id'])->first();
        $dr_account_id = $vendor->vendor_ledger_id;
        $dr_account_name = $vendor->vendor_name;

        // now insert the reversal entry in chsone_ledger_transaction table
        $insertFirstLedger = new ChsoneLedgerTransaction();
        $insertFirstLedger->soc_id = $data['company_id'];
        $insertFirstLedger->transaction_date = $data['payment_date'] ?? $data['cheque_payment_date'];
        $insertFirstLedger->ledger_account_id = $dr_account_id;
        $insertFirstLedger->ledger_account_name = $dr_account_name;
        $insertFirstLedger->voucher_type = 'payment';
        $insertFirstLedger->voucher_reference_number = $data['payment_number'] ?? '';
        $insertFirstLedger->voucher_reference_id = $expenseInvoicePaymentTrackerId;
        $insertFirstLedger->transaction_type = 'dr';
        $insertFirstLedger->payment_mode = $data['payment_mode'];
        if($data['type'] == 'refundable'){
            $insertFirstLedger->transaction_amount = $data['refundable_amount'];
        } elseif($data['type'] == 'adjustable'){
            $insertFirstLedger->transaction_amount = $data['adjustable_amount'];
        } elseif($data['type'] == 'refundable_adjustable'){
            $insertFirstLedger->transaction_amount = $data['refundable_amount'] + $data['adjustable_amount'];
        }
        $insertFirstLedger->other_reference_id = $expenseInvoicePaymentTrackerId;
        $insertFirstLedger->txn_from_id = 0;
        $insertFirstLedger->memo_desc = ucfirst($data['type']).' advance received via '.ucfirst($data['payment_mode']).'('.ucfirst($data['narration']).')';
        $insertFirstLedger->is_opening_balance = 0;
        $insertFirstLedger->is_reconciled = 0;
        $insertFirstLedger->is_cancelled = 0;
        $insertFirstLedger->created_by = $this->input['user_id'] ?? 0;
        $insertFirstLedger->added_on = date('Y-m-d H:i:s');

        $insertFirstLedger->save();

        //get last inserted id
        $lastInsertedId = $insertFirstLedger->txn_id;

        // insert second ledger entry
        $insertSecondLedger = new ChsoneLedgerTransaction();
        $insertSecondLedger->soc_id = $data['company_id'];
        $insertSecondLedger->transaction_date = $data['payment_date'] ?? $data['cheque_payment_date'];
        $insertSecondLedger->ledger_account_id = $data['cr_account_id'];
        $insertSecondLedger->ledger_account_name = $data['cr_account_name'];
        $insertSecondLedger->voucher_type = 'payment';
        $insertSecondLedger->voucher_reference_number = $data['payment_number'] ?? '';
        $insertSecondLedger->voucher_reference_id = $expenseInvoicePaymentTrackerId;
        $insertSecondLedger->transaction_type = 'cr';
        $insertSecondLedger->payment_mode = $data['payment_mode'];
        if($data['type'] == 'refundable'){
            $insertSecondLedger->transaction_amount = $data['refundable_amount'];
        } elseif($data['type'] == 'adjustable'){
            $insertSecondLedger->transaction_amount = $data['adjustable_amount'];
        } elseif($data['type'] == 'refundable_adjustable'){
            $insertSecondLedger->transaction_amount = $data['refundable_amount'] + $data['adjustable_amount'];
        }
        $insertSecondLedger->other_reference_id = $expenseInvoicePaymentTrackerId;
        $insertSecondLedger->txn_from_id = $lastInsertedId;
        $insertSecondLedger->memo_desc = ucfirst($data['type']).' advance received via '.ucfirst($data['payment_mode']).'('.ucfirst($data['narration']).')';
        $insertSecondLedger->is_opening_balance = 0;
        $insertSecondLedger->is_reconciled = 0;
        $insertSecondLedger->is_cancelled = 0;
        $insertSecondLedger->created_by = $this->input['user_id'] ?? 0;
        $insertSecondLedger->added_on = date('Y-m-d H:i:s');

        $insertSecondLedger->save();

        if($insertFirstLedger && $insertSecondLedger){
            return true;
        }else{
            return false;
        }
    }
}
