<?php

namespace App\Console\Commands\Actions\Expenses;

use Illuminate\Console\Command;
use App\Console\Commands\Action;

class updatePaymentTrackerDataSource extends Action
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'datasource:updatePaymentTracker {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Update Payment Tracker Data Source';

    /**
     * Execute the console command.
     */
    public function apply()
    {
        $id = $this->input['id'];
        $obj = $this->tenantDB()->table('expense_invoice_payment_tracker as expense_tracker')
            ->selectRaw(
                'expense_tracker.id,
                expense_tracker.vendor_id,
                CAST(expense_tracker.invoice_number AS UNSIGNED) as invoice_number,
                expense_tracker.payment_number,
                expense_tracker.payment_mode,
                CAST(transaction_reference AS UNSIGNED) as transaction_reference,
                expense_tracker.writeoff_amount,
                other_information,
                expense_tracker.payment_amount,
                expense_tracker.payment_note as comments,
                expense_tracker.payment_date,
                IFNULL(SUM(credit_accounts.amount), 0) as advance_amount',
            )
            // leftJoin for advance_amount from chsone_credit_accounts table whose account_id is equal to vendor_id and transaction_type is cr
            ->leftJoin('chsone_credit_accounts as credit_accounts', function ($join) {
                $join->on('credit_accounts.account_id', '=', 'expense_tracker.vendor_id')
                    ->where('credit_accounts.transaction_type', 'cr')
                    ->where('credit_accounts.account_context', 'vendor');
            })
            ->where('expense_tracker.id', $id)
            ->first();

            if($obj){
                $obj->show_writeoff = $obj->writeoff_amount > 0 ? true : false;
                $obj->total_amount = $obj->payment_amount + $obj->advance_amount + $obj->writeoff_amount;
            }

            // Check if the record exists
            if ($obj) {
                // Unserialize the other_information field
                $otherInformation = unserialize($obj->other_information);

                // Fetch the bank_ledger value
                $obj->bank_id = (int)$otherInformation['bank_ledger'] ?? null;
            }

        $this->data = $obj;
    }
}
