<?php

namespace App\Console\Commands\Actions\Expenses;

use Illuminate\Console\Command;
use App\Console\Commands\Action;

class changeVendorBillStatusDataSource extends Action
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'datasource:changeVendorBillStatus {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Change vendor bill status';

    /**
     * Execute the console command.
     */
    public function apply()
    {
        $vendor_bill_id = $this->input['vendor_bill_id'];
        $status = $this->input['status'];

        $result = $this->tenantDB()->table('chsone_vendor_bill_master')
            ->where('vendor_bill_id', $vendor_bill_id)
            ->update([
                'status' => $status
            ]);

        // Unapproved:- status = 0
        // Approved:- status = 1
        // Rejected:- status = 2
        if($status == 0){
        
            if($result){
                $this->status = 'success';
                $this->message = 'Bill added to unapproved';
                $this->statusCode = 200;
                $this->data = [];
            }else{
                $this->status = 'error';
                $this->message = 'Error in adding bill to unapproved list';
                $this->statusCode = 400;
                $this->data = [];
            }

        }elseif($status == 1){
            
            if($result){
                $this->status = 'success';
                $this->message = 'Bill has been approved';
                $this->statusCode = 200;
                $this->data = [];
            }else{
                $this->status = 'error';
                $this->message = 'Error in approving bill';
                $this->statusCode = 400;
                $this->data = [];
            }

        }elseif($status == 2){
                
            if($result){
                $this->status = 'success';
                $this->message = 'Bill has been rejected';
                $this->statusCode = 200;
                $this->data = [];
            }else{
                $this->status = 'error';
                $this->message = 'Error in rejecting bill';
                $this->statusCode = 400;
                $this->data = [];
            }
        }
    }
}
