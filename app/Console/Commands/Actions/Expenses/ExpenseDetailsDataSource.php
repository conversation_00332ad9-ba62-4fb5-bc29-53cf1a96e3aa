<?php

namespace App\Console\Commands\Actions\Expenses;

use Illuminate\Console\Command;
use App\Console\Commands\Action;

class ExpenseDetailsDataSource extends Action
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'datasource:ExpenseDetails {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Get Expense Details by Id';

    protected $formatter = [
        'id' => 'id',
        'other_expenses_id' => '',
        'ledger_account_id' => '',
        'other_expenses_item_name' => '',
        "other_expenses_amount" => "getAmountPaid:other_expenses_amount",
        'other_expenses_purchase_date' => '',
        'other_expenses_item_desc' => '',
        'status' => '',
    ];

    protected $formatterByKeys = ['id'];

    protected $mapper = [
        'id' => 'chsone_other_expenses.other_expenses_id',
    ];

    /**
     * Execute the console command.
     */
    public function apply()
    {
        $expenseId = $this->input['other_expenses_id'];

        $obj = $this->tenantDB()->table('chsone_other_expenses')
        ->select(
            'other_expenses_id AS id',
            'other_expenses_id',
            'ledger_account_id',
            'other_expenses_item_name',
            'other_expenses_amount',
            'other_expenses_purchase_date',
            'other_expenses_item_desc',
            'status'
        )
        ->where('other_expenses_id', $expenseId);

        $obj = $this->filter($obj);

        $result = $obj->get();

        $this->data = $this->format($result->toArray());
    }
    public function getAmountPaid($payment_amount, )
    {

        $payment_amount = (float) $payment_amount;

        return '₹ ' . number_format($payment_amount, 2);
    }
}
