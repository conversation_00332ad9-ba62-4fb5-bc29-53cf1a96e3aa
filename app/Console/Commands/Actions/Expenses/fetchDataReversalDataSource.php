<?php

namespace App\Console\Commands\Actions\Expenses;

use Illuminate\Console\Command;
use App\Console\Commands\Action;

class fetchDataReversalDataSource extends Action
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'datasource:fetchDataReversal {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Fetch Reversal Data Source';

    protected $formatter = [
        "id" => "",
        "invoice_number" => "",
        "payment_number" => "",
        "payment_mode" => "",
        "payment_amount" => "",
        "voucher_type" => "",
    ];

    protected $formatterByKeys = ['id'];

    protected $mapper = [
        'id' => 'id'
    ];

    /**
     * Execute the console command.
     */
    public function apply()
    {
        $id = $this->input['id'];
        $obj = $this->tenantDB()->table('expense_invoice_payment_tracker')
            ->selectRaw(
                'id,
                IFNULL(invoice_number, "N/A") as invoice_number,
                IFNULL(payment_number, 0) as payment_number,
                payment_mode,
                payment_amount',
            )
            ->selectRaw('\'Expense Bill\' AS voucher_type')
            ->where('id', $id)
            ->first();

        $this->data = $obj;
    }
}
