<?php

namespace App\Console\Commands\Actions\Expenses;

use Illuminate\Console\Command;
use App\Console\Commands\Action;
use App\Models\Tenants\ChsoneLedgerTransaction;

class paymentTrackerConfirmationReversalDataSource extends Action
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'datasource:paymentTrackerConfirmationReversal {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Action for expense payment tracker confirmation reversal';

    /**
     * Execute the console command.
     */
    public function apply()
    {
        try {
            $expense_id = $this->input['id'];

            // check id is exist or not in expense_invoice_payment_tracker table if not exist then return error
            $expenseDetails = $this->tenantDB()->table('expense_invoice_payment_tracker')
                ->where('id', $expense_id)
                ->get();

            if (count($expenseDetails) == 0) {
                $this->status = 'error';
                $this->message = 'No record found for this id';
                $this->statusCode = 400;
                $this->data = [];
                return;
            }

            $result = $this->tenantDB()->table('expense_invoice_payment_tracker')
                ->where('id', $expense_id)
                ->first();

            if ($result->status == 'reversed') {
                $this->status = 'error';
                $this->message = 'Payment already reversed.';
                $this->statusCode = 400;
                return;
            }

            $result = $this->tenantDB()->table('expense_invoice_payment_tracker')
            ->where('id', $expense_id)
            ->update([
                'status' => 'reversed',
                'reversal_note' => $this->input['reversal_comment'] ?? null
            ]);

            if ($result == 1) {

                // update the status in chsone_vendor_bill_master table
                $updateStatus = $this->updateStatus($expense_id);

                if (!$updateStatus) {
                    $this->status = 'error';
                    $this->message = 'Error in update status in chsone_vendor_bill_master table.';
                    $this->statusCode = 400;
                    $this->data = [];
                    return;
                }

                //fetch to_ledger_id and from_ledger_id from chsone_vendor_bill_payment_details table whose expense_id is $expense_id and pass to the ledger transaction table
                $paymentDetails = $this->tenantDB()->table('chsone_vendor_bill_payment_details')
                    ->where('expense_tracker_id', $expense_id)
                    ->first();
                    
                $to_ledger_id = $paymentDetails->to_ledger_id;
                $from_ledger_id = $paymentDetails->from_ledger_id;

                // insert reversal entry in chsone_ledger_transaction table
                $insertLedgerTransaction = $this->insertLedgerTransaction($expenseDetails[0], $to_ledger_id, $from_ledger_id);

                if (!$insertLedgerTransaction) {
                    $this->status = 'error';
                    $this->message = 'Error in insert ledger transaction.';
                    $this->statusCode = 400;
                    $this->data = [];
                    return;
                }

                $this->status = 'success';
                $this->message = 'Payment has been reversed.';
                $this->statusCode = 200;
                $this->data = [];
            } else {
                $this->status = 'error';
                $this->message = 'Error in payment reversal';
                $this->statusCode = 400;
                $this->data = [];
            }
        } catch (\Exception $e) {
            $this->status = 'error';
            $this->message = "Error in payment reversal: " . $e->getMessage() . ", at " . $e->getLine() ." in " . $e->getFile();
            $this->statusCode = 400;
            $this->data = [];
        }
    }

    public function updateStatus($expense_id)
    {
        // fecth the payment amount from chsone_vendor_bill_payment_details table whose expense_id is $expense_id
        $paymentDetails = $this->tenantDB()->table('chsone_vendor_bill_payment_details')
            ->where('expense_tracker_id', $expense_id)
            ->get();

        // update the status partialpaid or unpaid according to the payment amount in chsone_vendor_bill_master table
        $paymentAmount = 0;
        $invoiceNumber = '';
        foreach ($paymentDetails as $paymentDetail) {
            $paymentAmount = $paymentDetail->vendor_bill_payment_amount;

            $invoiceNumber = $paymentDetail->vendor_bill_id;

            $result = $this->tenantDB()->table('chsone_vendor_bill_master')
                ->where('vendor_bill_id', $invoiceNumber)
                ->first();

            // firstly need to get the count of payment done for this $invoiceNumber in chsone_vendor_bill_payment_details table
            $paymentCount = $this->tenantDB()->table('chsone_vendor_bill_payment_details')
                ->where('vendor_bill_id', $invoiceNumber)
                ->count();

            if ($paymentCount == 1) {
                $status = 'unpaid';
            } elseif ($result->vendor_bill_amount == $paymentAmount) {
                $status = 'unpaid';
            } elseif ($paymentAmount > 0 && $paymentAmount < $result->vendor_bill_amount) {
                $status = 'partialpaid';
            }

            $updateStatus = $this->tenantDB()->table('chsone_vendor_bill_master')
                ->where('vendor_bill_id', $invoiceNumber)
                ->update([
                    'payment_status' => $status,
                    'updated_on' => date('Y-m-d H:i:s')
                ]);

            /*if(!$updateStatus){
                return false;
            }*/
        }

        return true;
    }

    public function insertLedgerTransaction($expenseDetails, $to_ledger_id, $from_ledger_id)
    {
        // fetch ledger_account_name from chsone_grp_ledger_tree table whose ledger_account_id is from_ledger_id
        $ledgerAccount = $this->tenantDB()->table('chsone_grp_ledger_tree')
            ->where('ledger_account_id', $from_ledger_id)
            ->first();

        $cr_account_id = $from_ledger_id;
        $cr_account_name = $ledgerAccount->ledger_account_name;

        // check payment mode
        if ($expenseDetails->payment_mode == 'cash' || $expenseDetails->payment_mode == 'cheque' || $expenseDetails->payment_mode == 'cashtransfer') {
            // fecth ledger_account name from chsone_grp_ledger_tree table whose ledger_account_id is to_ledger_id
            $ledgerAccount = $this->tenantDB()->table('chsone_grp_ledger_tree')
                ->where('ledger_account_id', $to_ledger_id)
                ->first();
            $dr_account_id = $to_ledger_id;
            $dr_account_name = $ledgerAccount->ledger_account_name;
        }

        // now insert the reversal entry in chsone_ledger_transaction table
        $insertFirstLedger = new ChsoneLedgerTransaction();
        $insertFirstLedger->soc_id = $expenseDetails->soc_id;
        $insertFirstLedger->transaction_date = $expenseDetails->payment_date;
        $insertFirstLedger->ledger_account_id = $dr_account_id;
        $insertFirstLedger->ledger_account_name = $dr_account_name;
        $insertFirstLedger->voucher_type = 'payment';
        $insertFirstLedger->voucher_reference_number = $expenseDetails->payment_number;
        $insertFirstLedger->voucher_reference_id = $expenseDetails->id;
        $insertFirstLedger->transaction_type = 'dr';
        $insertFirstLedger->payment_mode = $expenseDetails->payment_mode;
        $insertFirstLedger->transaction_amount = $expenseDetails->payment_amount;
        $insertFirstLedger->other_reference_id = $expenseDetails->id;
        $insertFirstLedger->txn_from_id = 0;
        $insertFirstLedger->memo_desc = 'Payment number ' . $expenseDetails->payment_number . ' reversed on date ' . $expenseDetails->payment_date . ' [' . $this->input['reversal_comment'] . ']';
        $insertFirstLedger->is_opening_balance = 0;
        $insertFirstLedger->is_reconciled = 0;
        $insertFirstLedger->is_cancelled = 1;
        $insertFirstLedger->created_by = $this->input['user_id'] ?? 0;
        $insertFirstLedger->added_on = date('Y-m-d H:i:s');

        $insertFirstLedger->save();

        //get last inserted id
        $lastInsertedId = $insertFirstLedger->txn_id;

        // insert second ledger entry
        $insertSecondLedger = new ChsoneLedgerTransaction();

        $insertSecondLedger->soc_id = $expenseDetails->soc_id;
        $insertSecondLedger->transaction_date = $expenseDetails->payment_date;
        $insertSecondLedger->ledger_account_id = $cr_account_id;
        $insertSecondLedger->ledger_account_name = $cr_account_name;
        $insertSecondLedger->voucher_type = 'payment';
        $insertSecondLedger->voucher_reference_number = $expenseDetails->payment_number;
        $insertSecondLedger->voucher_reference_id = $expenseDetails->id;
        $insertSecondLedger->transaction_type = 'cr';
        $insertSecondLedger->payment_mode = $expenseDetails->payment_mode;
        $insertSecondLedger->transaction_amount = $expenseDetails->payment_amount;
        $insertSecondLedger->other_reference_id = $expenseDetails->id;
        $insertSecondLedger->txn_from_id = $lastInsertedId;
        $insertSecondLedger->memo_desc = 'Payment number ' . $expenseDetails->payment_number . ' reversed on date ' . $expenseDetails->payment_date . ' [' . $this->input['reversal_comment'] . ']';
        $insertSecondLedger->is_opening_balance = 0;
        $insertSecondLedger->is_reconciled = 0;
        $insertSecondLedger->is_cancelled = 1;
        $insertSecondLedger->created_by = $this->input['user_id'] ?? 0;
        $insertSecondLedger->added_on = date('Y-m-d H:i:s');

        $insertSecondLedger->save();

        if ($insertFirstLedger && $insertSecondLedger) {
            return true;
        } else {
            return false;
        }
    }
}
