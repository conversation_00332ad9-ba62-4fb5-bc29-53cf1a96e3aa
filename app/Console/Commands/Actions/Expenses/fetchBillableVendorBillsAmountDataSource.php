<?php

namespace App\Console\Commands\Actions\Expenses;

use Illuminate\Console\Command;
use App\Console\Commands\Action;

class fetchBillableVendorBillsAmountDataSource extends Action
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'datasource:fetchBillableVendorBillsAmount {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Get the billable vendor bills amount';

    protected $formatter = [
        'id' => '',
        'vendor_bill_type_purchase' => '',
        'vendor_bill_num' => '',
        'vendor_bill_date' => '',
        'vendor_bill_amount' => '',
        'vendor_bill_tds' => '',
        'vendor_bill_writeoff' => '',
        'vendor_bill_due_date' => '',
        'is_billable' => '',
        'is_billed' => '',
        'is_rcm' => '',
        'status' => '',
        'payment_status' => '',
        'vendor_id' => '',
        'vendor_name' => '',
        'write_off' => '',
        'vendor_bill_payment_amount' => '',
        'tds' => '',
        'vendor_bill_payment_amount_final' => 'getPaymentFinalAmountSum:vendor_bill_payment_amount,tds,write_off',
        'vendor_bill_amount_final' => 'getBillFinalAmountSum:vendor_bill_amount,vendor_bill_tds',
        'vendor_bill_due_amount' => 'subtract:vendor_bill_amount_final,vendor_bill_payment_amount_final',
    ];

    protected $formatterByKeys = ['id'];

    protected $mapper = [
        'id' => 'cvbm.vendor_bill_id',
    ];

    /**
     * Execute the console command.
     */
    public function apply()
    {
        // $vendorBillIds = explode(',', trim($this->input["id"], '[]'));

        $vendorBillIds = explode(',', $this->input["id"]);

        $obj = $this->tenantDB()->table('chsone_vendor_bill_master as cvbm')
        ->select(
            'cvbm.vendor_bill_id AS id',
            'vendor_bill_type_purchase',
            'vendor_bill_num',
            'vendor_bill_date',
            'vendor_bill_amount',
            'vendor_bill_tds',
            'vendor_bill_writeoff',
            'vendor_bill_due_date',
            'is_billable',
            'is_billed',
            'is_rcm',
            'cvbm.status',
            'payment_status',
            'cvbm.vendor_id',
        )
        ->leftJoin('chsone_vendors_master as cvm', 'cvbm.vendor_id', '=', 'cvm.vendor_id')
        ->where('cvbm.status', 1)
        ->where('cvbm.is_billable', 1)
        ->whereIn('cvbm.vendor_bill_id', $vendorBillIds)
        ->orderByDesc('vendor_bill_date')
        ->selectRaw("IFNULL(is_billed, 0) AS is_billed")
        ->selectRaw("IFNULL(vendor_name, 'No Vendor') AS vendor_name")
        ->selectRaw("
            IFNULL(
                (
                    SELECT IF(SUM(cvbpd.write_off) = 0, 0, SUM(cvbpd.write_off))
                    FROM chsone_vendor_bill_payment_details AS cvbpd
                    WHERE cvbpd.vendor_bill_id = cvbm.vendor_bill_id
                ), 0
            ) AS write_off
        ")
        ->selectRaw("
            IFNULL(
                (
                    SELECT IF(SUM(cvbpd.vendor_bill_payment_amount) = 0, 0, SUM(cvbpd.vendor_bill_payment_amount))
                    FROM chsone_vendor_bill_payment_details AS cvbpd
                    WHERE cvbpd.vendor_bill_id = cvbm.vendor_bill_id
                ), 0
            ) AS vendor_bill_payment_amount
        ")
        ->selectRaw("
            IFNULL(
                (
                    SELECT IF(SUM(cvbpd.tds) = 0, 0, SUM(cvbpd.tds))
                    FROM chsone_vendor_bill_payment_details AS cvbpd
                    WHERE cvbpd.vendor_bill_id = cvbm.vendor_bill_id
                ), 0
            ) AS tds
        ");

        $result = $obj->get();

        $this->data = $this->format($result->toArray());
    }

    public function subtract($vendor_bill_amount, $vendor_bill_payment_amount_final)
    {
        // return (float)$vendor_bill_amount - (float)$vendor_bill_payment_amount_final;
        $amount = (float) $vendor_bill_amount - (float) $vendor_bill_payment_amount_final;
        // return '₹ ' . number_format($amount, 2);
        return $amount;
    }

    public function getPaymentFinalAmountSum($vendor_bill_payment_amount, $tds, $write_off)
    {
        // return (float)$vendor_bill_payment_amount + (float)$tds;
        $amount = (float) $vendor_bill_payment_amount + (float) $tds;
        $write_off = (float) $write_off;
        // return '₹ ' . number_format($amount, 2) . '(' . number_format($write_off, 2) . ')';
        return $amount;

    }

    public function getBillFinalAmountSum($vendor_bill_amount, $vendor_bill_tds)
    {
        $amount = (float) $vendor_bill_amount; //+ (float) $vendor_bill_tds;
        // return '₹ ' . number_format($amount, 2);
        return $amount;
    }
}
