<?php

namespace App\Console\Commands\Actions\Expenses;

use Illuminate\Console\Command;
use App\Console\Commands\Action;
use App\Models\Tenants\ChsoneGrpLedgerTree;
use App\Models\Tenants\ChsoneOtherExpense;
use App\Models\Tenants\ChsoneUnitsMaster;
use App\Models\Tenants\ChsoneVendorBillMaster;
use App\Models\Tenants\IncomeBillableItem;
use App\Models\Tenants\IncomeInvoiceSetting;
use App\Models\Tenants\IncomeLatePaymentCharge;
use App\Models\Tenants\SocAccountFinancialYearMaster;
use Carbon\Carbon;

class AddBillableDataSource extends Action
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'datasource:addBillable {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Add a new miscellaneous expense to the system.';

    /**
     * Execute the console command.
     */
    public function apply()
    {
        $data = $this->input;

        $data['amountAct'] = number_format($data['billable_amount'], 2, '.', '');
        $data['debit_note_type'] = 'debit_note';
        // decode the $data['id'] and pass it to $data['expense_id']
        $data['expense_id'] = $data['id'];
        $data['particular_name'] = $data['particular_name'] ?? '';

        // need to findout unit count from chsone_units_master table
        $unitCount = ChsoneUnitsMaster::where('soc_id', $data['company_id'])->count();
        $data['unitcount'] = $unitCount;

        // nee to find out currentYear and currentMonth from soc_account_financial_year_master table whose confirmed = 0 and soc_id = $data['company_id'] limit 1
        $socAccountFinancialYearMaster = SocAccountFinancialYearMaster::where('soc_id', $data['company_id'])
            ->where('confirmed', 0)
            ->first();
        $data['currentYear'] = $socAccountFinancialYearMaster->fy_start_date->format('Y');
        $data['currentMonth'] = $socAccountFinancialYearMaster->fy_start_date->format('m');

        // need to fecth invoice setting for the company
        $incomeInvoiceSetting = IncomeInvoiceSetting::where('soc_id', $data['company_id'])
            ->orderBy('effective_date', 'desc')
            ->first();

        $data['selected_inv_freq'] = $incomeInvoiceSetting->invoicing_frequency ?? '';
        
        $arrBillableNoteResponse = $this->addBillableItems(array('arrPostData' => $data, 'soc_id' => $this->input['company_id'], 'user_id' => $this->input['user_id']));

        if ($arrBillableNoteResponse['status'] == 'success') {
            $this->status = 'success';
            $this->statusCode = 200;
            $this->message = 'Billable items added successfully';
            return;
        } else {
            $this->status = 'error';
            $this->statusCode = 400;
            $this->message = 'Billable items already exists';
            return;
        }
    }


    public function addBillableItems($data)
    {
        $arrPost = $data['arrPostData'];
        $dates = $this->getDatesForBillable($arrPost);

        if ($arrPost['debit_note_type'] === 'dpc_debit_note') {
        
            $latePaymentDetails = IncomeLatePaymentCharge::where('type', 'maintenance')
                ->where('soc_id', $data['soc_id'])
                ->whereDate('effective_date', '<=', Carbon::now())
                ->orderByDesc('effective_date')
                ->first();
        
            $arrPost['income_Account_id'] = $latePaymentDetails ? $latePaymentDetails->id : null;
        } else {
            $arrPost['income_Account_id'] = $arrPost['billing_type'];
        }
        
        if(isset($arrPost['note']) && !empty($arrPost['note'])){
			$note = $arrPost['note'];
		}else{
			$note = ($arrPost['debit_note_type'] == 'debit_note') ? (ucfirst(str_replace("_", " ", $arrPost['debit_note_type']))).' for '.$arrPost['particular_name'] :
			ucfirst(str_replace("_", " ", $arrPost['debit_note_type']));
		}
        
        if ($arrPost['billable_item_for'] === 'all') {
            $arrUnitMemberDetails = $this->getUnitMemberDetails($data,$searchstring = '', $building_id = '', $floor_id = '', $is_allotted = 1, $unitwise = '', $unittype = '', $memberType = 'Primary');
        
            $unitMemberId = [];
			foreach($arrUnitMemberDetails as $key=>$value){
                $unitMemberId[$value->unit_id] = $value->id;
			}
			$unitMemArr = $unitMemberId;
        } else {
            $unitIds = is_array($arrPost['unit_id']) ? $arrPost['unit_id'] : explode(',', $arrPost['unit_id']);
            // $memberIds = is_array($arrPost['member_id']) ? $arrPost['member_id'] : explode(',', $arrPost['member_id']);
            // dd($member_Ids);
            $member_Ids = $this->getUnitMemberDetails($data, '', '', '', 1, '', '', 'Primary', $arrPost['unit_id']);
            $memberIds = [];
            // Extracting the `id` values into an array
            $memberIds = array_map(fn($item) => $item->id, $member_Ids);
            $unitMemArr = array_combine($unitIds, $memberIds);
        }

        $finalArray = array();
		$arrMessage = [];
		$savedDrItem = [];
		$savedUnitId = [];
		$failedUnitId = [];

        foreach ($unitMemArr as $unitId => $memberId) {
            foreach ($dates as $dt) {
                [$startDt, $endDt] = explode(' ', $dt);

                // before insertion need to check if the record already exists
                $checkRecord = IncomeBillableItem::where('unit_id', $unitId)
                    ->where('soc_id', $data['soc_id'])
                    ->where('from_date', $startDt)
                    ->where('to_date', $endDt)
                    ->where('income_account_id', $arrPost['income_Account_id'])
                    ->where('member_id', $memberId)
                    ->where('expense_id', $arrPost['expense_id'][0])
                    ->first();

                if ($checkRecord) {
                    $arrMessage['status'] = 'error';
                    $arrMessage['message'] = 'Record already exists';
                    continue;
                }
        
                $debitNote = new IncomeBillableItem([
                    'soc_id'                      => $data['soc_id'],
                    'unit_id'                     => $unitId,
                    'member_id'                   => $memberId,
                    'income_account_id'           => $arrPost['income_Account_id'],
                    'debit_note_type'             => $arrPost['debit_note_type'],
                    'note'                        => $note,
                    'amount'                      => $arrPost['member_amount'],
                    'from_date'                   => $startDt,
                    'to_date'                     => $endDt,
                    'expense_id'                  => $arrPost['expense_id'][0],
                    'status'                      => 1,
                    'is_reversal'                 => 0,
                    'created_date'                => Carbon::now(),
                    'created_by'                  => $data['user_id'],
                    'due_date'                    => $arrPost['due_date'] ?? '0000-00-00',
                    'apply_late_payment_interest' => array_key_exists('latepayment', $arrPost) ? 1 : 0,
                ]);
        
                if (!$debitNote->save()) {
                    $arrMessage = $debitNote->getErrors();
                    $failedUnitId[] = $unitId;
                } else {
                    $savedUnitId[] = $unitId;
                    $savedDrItem[] = $debitNote->id;
                }
            }
        }

        if(empty($savedUnitId)) {
            $arrMessage['status'] = 'error';
            $arrMessage['message'] = 'Failed to update vendor bill';
        } else {
            if($arrPost['amountAct']) {
                $expense_ids = is_array($arrPost['expense_id']) ? $arrPost['expense_id'] : explode(',', $arrPost['expense_id']);
                foreach ($expense_ids as $expense_id) {
                    if($expense_id != '') {
                        $vendor_bill = ChsoneVendorBillMaster::find($expense_id);
                        $vendor_bill->is_billed = 1;
                        $vendor_bill->save();

                        if(!$vendor_bill->save()) {
                            // $this->message = 'Failed to update vendor bill for ID: ' . $expense_id;
                            // $this->status = 'error';
                            // $this->statusCode = 400;
                            // return;
                            $arrMessage['status'] = 'error';
                            $arrMessage['message'] = 'Failed to update vendor bill for ID: ' . $expense_id;
                        } else {
                            // $this->message = 'Vendor bill added successfully';
                            // $this->status = 'success';
                            // $this->statusCode = 200;
                            // return;
                            $arrMessage['status'] = 'success';
                            $arrMessage['message'] = 'Vendor bill added successfully';
                        }
                    }
                }
            } else {
                // $this->message = 'Failed to update vendor bill';
                // $this->status = 'error';
                // $this->statusCode = 400;
                // return;
                $arrMessage['status'] = 'error';
                $arrMessage['message'] = 'Failed to update vendor bill';
            }
        }

        $finalArray['saved_debit_note'] = (!empty($savedDrItem)) ? $savedDrItem : [];
		$finalArray['saved_unit_id'] = (!empty($savedUnitId)) ? $savedUnitId: [];
		$finalArray['status'] = (!empty($savedUnitId)) ? 'success': 'failed';
		$finalArray['failed_unit_id'] = (!empty($failedUnitId)) ? $failedUnitId: [];
		$finalArray['arrMessage'] = $arrMessage;
		
		return $finalArray;
    }


    private function getDatesForBillable($billableData)
    {
		$invoiceFreqArr =[];
		$currentYear = (isset($billableData['currentYear']) && !empty($billableData['currentYear'])) ? $billableData['currentYear'] : date('Y');
		$dt = [];
		switch ($billableData['selected_inv_freq']){
			case 'Monthly':
				{
                    foreach ($billableData['invoice_frequency'] as $v) {
                        [$year, $month] = explode("-", $v);
                        $dtim = strtotime("$year-$month-01");
                        $dt[] = date('Y-m-01', $dtim) . ' ' . date('Y-m-t', $dtim);
                        $invoiceFreqArr['date']= $dt;
                    }
				}
				break;
			case 'Quarterly':
				{
                    foreach ($billableData['invoice_frequency'] as $v) {
                        [$year, $month] = explode("-", $v);
                        $dtim = strtotime("$year-$month-01");
                        $dt[] = date('Y-m-01', $dtim) . ' ' . date('Y-m-t', strtotime("+2 months", $dtim));
                        $invoiceFreqArr['date']= $dt;
                    }
				}
				break;
			case 'Half_yearly':
				{
                    foreach ($billableData['invoice_frequency'] as $v) {
                        [$year, $month] = explode("-", $v);
                        $dtim = strtotime("$year-$month-01");
                        $dt[] = date('Y-m-01', $dtim) . ' ' . date('Y-m-t', strtotime("+5 months", $dtim));
                        $invoiceFreqArr['date']= $dt;
                    }
				}
				break;
			case 'Yearly':
				{
                    foreach ($billableData['invoice_frequency'] as $v) {
                        [$year, $month] = explode("-", $v);
                        $startDate = strtotime("April 1, $year");
                        $endDate = strtotime("March 31, " . ($year + 1));
                        $dt[] = date('Y-m-01', $startDate) . ' ' . date('Y-m-t', $endDate);
                        $invoiceFreqArr['date']= $dt;
                    }
                    break;
				}
				
				break;
		}
		return $invoiceFreqArr['date'];
	}


    public function getUnitMemberDetails($data, $searchstring = '', $building_id = '', $floor_id = '', $is_allotted = '', $unitwise = '', $unittype = '', $memberType = '', $unit_id = '')
    {
        $query = $this->tenantDB()->table('chsone_units_master as units')
        ->select([
            'units.soc_id',
            'units.unit_id',
            'units.unit_flat_number',
            'units.soc_building_floor',
            'units.soc_building_id',
            'units.soc_building_name',
            'memmaster.id',
            'memmaster.member_type_id',
            'memmaster.member_first_name',
            'memmaster.member_last_name'
        ])
        ->where('units.soc_id', $data['soc_id'])
        ->where('units.status', 1)
        ->where(function ($q) {
            $q->where('units.cancel_date', '0000-00-00')
            ->orWhere('units.cancel_date', '>', now());
        })
        ->leftJoin('chsone_members_master as memmaster', 'units.unit_id', '=', 'memmaster.fk_unit_id')
        ->where('memmaster.status', 1);

        if (!empty($is_allotted)) {
            $query->where('units.is_allotted', $is_allotted);
        }

        // if (strpos($searchstring, '-') !== false) {
        //     [$buildingName, $flatNumber] = explode('-', $searchstring);
        //     if (!empty($flatNumber)) {
        //         $query->where('units.unit_flat_number', 'LIKE', "%$flatNumber%");
        //     }
        //     if (!empty($buildingName)) {
        //         $query->where('units.soc_building_name', 'LIKE', "%$buildingName%");
        //     }
        // } elseif (!empty($searchstring)) {
        //     $query->where('units.unit_flat_number', 'LIKE', "%$searchstring%");
        // }

        // if ($unittype === 'Parking') {
        //     $query->where('units.unit_type', '!=', $unittype);
        // }

        // if (!empty($building_id)) {
        //     $query->where('units.soc_building_id', $building_id);
        // }

        // if (!empty($floor_id)) {
        //     $query->where('units.soc_building_floor', 'LIKE', $floor_id);
        // }

        if (!empty($memberType)) {
            $query->leftJoin('chsone_member_type_master as memType', 'memmaster.member_type_id', '=', 'memType.member_type_id')
                ->where('memType.member_type_name', 'primary');
        }

        // if ($unitwise == 1) {
        //     $query->groupBy('units.unit_id');
        // }

        if (!empty($unit_id)) {
            $query->whereIn('units.unit_id', $unit_id);
        }

        $query->orderBy('units.soc_building_id')
            ->orderBy('units.soc_building_floor')
            ->orderBy('units.unit_flat_number');

        return $query->get()->toArray();
    }
}
