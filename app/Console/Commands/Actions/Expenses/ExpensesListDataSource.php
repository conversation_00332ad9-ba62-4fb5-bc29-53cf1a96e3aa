<?php

namespace App\Console\Commands\Actions\Expenses;

use Illuminate\Console\Command;
use App\Console\Commands\Action;

class ExpensesListDataSource extends Action
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'datasource:ExpensesList {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Get the expenses list details';

    protected $formatter = [
        'id' => 'id',
        'other_expenses_item_name' => '',
        "other_expenses_amount" => "getAmountPaid:other_expenses_amount",
        'other_expenses_purchase_date' => '',
        'status' => '',
    ];

    protected $formatterByKeys = ['id'];

    protected $mapper = [
        'id' => 'chsone_other_expenses.other_expenses_id',
    ];

    /**
     * Execute the console command.
     */
    public function apply()
    {
        // add pagination
        $page = $this->input['page'] ?? 1;
        $per_page = !empty($this->input['per_page']) ? $this->input['per_page']: ($this->hugeData ? 100000 : 10);
        $offset = ($page - 1) * $per_page;

        $obj = $this->tenantDB()->table('chsone_other_expenses')
            ->select(
                'other_expenses_id AS id',
                'other_expenses_id',
                'ledger_account_id',
                'other_expenses_item_name',
                'other_expenses_amount',
                'other_expenses_purchase_date',
                'status'
            )
            ->orderBy('other_expenses_id', 'DESC');

        // add count and pagination
        $count = $obj->count();

        $obj = $obj->offset($offset);
        $obj = $obj->limit($per_page);

        $result = $obj->get();

        $this->data = $this->format($result->toArray());
        $this->meta['pagination']['total']=$count;
    }


    public function getAmountPaid($payment_amount, )
    {

        $payment_amount = (float) $payment_amount;

        // return '₹ ' . number_format($payment_amount, 2);
        return $payment_amount;
    }
}
