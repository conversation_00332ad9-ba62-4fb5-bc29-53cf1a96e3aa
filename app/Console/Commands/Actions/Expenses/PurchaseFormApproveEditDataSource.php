<?php

namespace App\Console\Commands\Actions\Expenses;

use App\Console\Commands\Action;

class PurchaseFormApproveEditDataSource extends Action
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'datasource:purchaseFormApproveEdit {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Update the Purchase Form Data Source';


    /**
     * Execute the console command.
     */

    // SELECT soc_db_412.cpf.*, soc_db_412.poa.*
    // FROM soc_db_412.chsone_purchase_form AS cpf
    // JOIN soc_db_412.purchase_order   _approvers AS poa 
    // ON poa.purchase_order_id = cpf.purchase_form_id 
    // AND poa.user_id = 3729;

    public function apply()
    {

        $po_id = json_decode($this->input['po_id']);
        $company_id = $this->input['company_id'];
        $user_id = 34624;

        $data = $this->tenantDB()
            ->table('chsone_purchase_form AS cpf')
            ->whereIn('cpf.purchase_form_id', $po_id)
            ->where('cpf.soc_id', $company_id)
            ->get();

        if ($data === null) {
            $this->status = 'error';
            $this->message = 'Unable to Fetch Purchase Form Data';
            $this->statusCode = 400;
            return;
        }

        $response_data = [];

        foreach ($data as $key => $value) {

            if ($value->status !== 0) {
                $response_data[] = [
                    'purchase_form_id' => $value->purchase_form_id,
                    'status' => 'error',
                    'message' => $value->purchase_form_title . ' ' . 'Cannot be approved',
                    'statusCode' => 400
                ];
                continue;
            }

            $approver_data = $this->tenantDB()
                ->table('purchase_order_approvers AS poa')
                ->where('poa.purchase_order_id', $value->purchase_form_id)
                ->get();


            if (count($approver_data) == 0) {
                $response_data[] = [
                    'purchase_form_id' => $value->purchase_form_id,
                    'status' => 'error',
                    'message' => $value->purchase_form_title . ' ' . 'have not assigned to you',
                    'statusCode' => 400
                ];
                continue;
            } else {
                
                $statuses = collect($approver_data)->pluck('status');
                $statusNotOneCount = $statuses->reject(function ($item) {
                    return $item === 1;
                })->count();

                if ($statusNotOneCount === 1) {
                    $this->tenantDB()->table('chsone_purchase_form')
                        ->where('purchase_form_id', $value->purchase_form_id)
                        ->update([
                            'status' => 3,
                        ]);

                    $this->tenantDB()->table('purchase_order_approvers')
                        ->where('purchase_order_id', $value->purchase_form_id)
                        ->where('user_id', $user_id)
                        ->update([
                            'status' => 1,
                        ]);

                    $response_data[] = [
                        'purchase_form_id' => $value->purchase_form_id,
                        'status' => 'success',
                        'message' => 'Purchase Form Approved successfully',
                        'statusCode' => 200
                    ];
                } else {
                    $this->tenantDB()->table('purchase_order_approvers')
                        ->where('purchase_order_id', $value->purchase_form_id)
                        ->where('user_id', $user_id)
                        ->update([
                            'status' => 1,
                        ]);

                    $response_data[] = [
                        'purchase_form_id' => $value->purchase_form_id,
                        'status' => 'success',
                        'message' => 'Purchase Form Approved successfully',
                        'statusCode' => 200
                    ];
                }
            }
        }

        $this->data = $response_data;
    }


}
