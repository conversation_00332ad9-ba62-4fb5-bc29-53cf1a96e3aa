<?php

namespace App\Console\Commands\Actions\Expenses;

use Illuminate\Console\Command;
use App\Console\Commands\Action;
use App\Models\Tenants\ChsoneGrpLedgerTree;
use App\Models\Tenants\ChsoneOtherExpense;

class addNewExpenseDataSource extends Action
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'datasource:addNewExpense {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Add a new miscellaneous expense to the system.';

    /**
     * Execute the console command.
     */
    public function apply()
    {
        $data = $this->input;

        // need to find out first ledger_account_id from chsone_grp_ledger_tree table whose ledger_account_name is 'Cash in Hand'
        $ledgerDetails = ChsoneGrpLedgerTree::where('soc_id', $data['company_id'])->where('ledger_account_name', 'Cash in Hand')->first();
        
        // insert the data into the chsone_other_expenses table
        $otherExpense = new ChsoneOtherExpense;
        $otherExpense->soc_id=$data['company_id'] ?? '';
        $otherExpense->ledger_account_id=$ledgerDetails->ledger_account_id ?? '';
        $otherExpense->other_expenses_item_name=$data['purchase_item_name'] ?? '';
        $otherExpense->other_expenses_amount=$data['purchase_amount'] ?? '';
        $otherExpense->other_expenses_purchase_date=$data['purchase_date'] ?? '';
        $otherExpense->other_expenses_item_desc=$data['item_description'] ?? '';
        $otherExpense->added_on=date("Y-m-d");
        $otherExpense->status=1;
        $otherExpense->save();

        if ($otherExpense->save()) {
            $this->message = "Miscellaneous expense is added succesfully";
            $this->status = 'success';
            $this->statusCode = 200;
            $this->data = $otherExpense->toArray();
        } else {
            $this->message = "Unable to add miscellaneous expense";
            $this->status = 'error';
            $this->statusCode = 400;
            $this->data = $otherExpense->toArray();
        }
    }
}
