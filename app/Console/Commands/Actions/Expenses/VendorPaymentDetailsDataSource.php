<?php

namespace App\Console\Commands\Actions\Expenses;

use Illuminate\Console\Command;
use App\Console\Commands\Action;
use Illuminate\Support\Facades\DB;

class VendorPaymentDetailsDataSource extends Action
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'datasource:VendorPaymentDetails {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Get the Vendor Bill Payment Details by Id';

    protected $formatter = [
        'id' => '',
        'vendor_bill_payment_id'=>'',
        'vendor_bill_num'=>'setNA:vendor_bill_num',
        'vendor_id'=>'',
        'vendor_name'=>'setNA:vendor_name',
        'vendor_bill_payment_date'=>'setNA:vendor_bill_payment_date',
        'vendor_bill_payment_mode'=>'setNA:vendor_bill_payment_mode',
        'vendor_bill_payment_comments'=>'setNA:vendor_bill_payment_comments',
        'vendor_bill_payment_amount'=>'getAmountPaid:vendor_bill_payment_amount,tds,write_off',
        'vendor_bill_payment_bank_name'=>'setNA:vendor_bill_payment_bank_name',
        'vendor_bill_payment_cheque_number'=>'setNA:vendor_bill_payment_cheque_number',
        'vendor_bill_payment_card_num'=>'setNA:vendor_bill_payment_card_num',
        'vendor_bill_id'=>'',
        'tds'=>'',
        'write_off'=>'',
    ];

    protected $formatterByKeys = ['id'];

    protected $mapper = [
        'id' => 'vendor_bill_id',
    ];

    /**
     * Execute the console command.
     */
    public function apply()
    {
        $VendorDetailId = $this->input['vendor_bill_id'];

        // add pagenation
        $page = $this->input['page'] ?? 1;
        $per_page = !empty($this->input['per_page']) ? $this->input['per_page']: ($this->hugeData ? 100000 : 10);
        $offset = ($page - 1) * $per_page;

        $obj = $this->tenantDB()->table('chsone_vendor_bill_payment_details as payments')
            ->select(
                'payments.vendor_bill_payment_id as id',
                'payments.vendor_bill_payment_id',
                'vendors.vendor_bill_num',
                'payments.vendor_id',
                'master.vendor_name',
                'payments.vendor_bill_payment_date',
                DB::raw("CASE 
                    WHEN payments.vendor_bill_payment_mode = 'cashtransfer' THEN 'Electronic Fund Transfer' 
                    WHEN payments.vendor_bill_payment_mode = 'cash' THEN 'Cash' 
                    WHEN payments.vendor_bill_payment_mode = 'cheque' THEN 'Cheque' 
                    ELSE payments.vendor_bill_payment_mode 
                 END as vendor_bill_payment_mode"),
                'payments.vendor_bill_payment_comments',
                'payments.vendor_bill_payment_amount',
                'payments.vendor_bill_payment_bank_name',
                'payments.vendor_bill_payment_cheque_number',
                'payments.vendor_bill_payment_card_num',
                'payments.vendor_bill_id',
                'payments.tds',
                'payments.write_off'
            )
            ->leftJoin('chsone_vendor_bill_master as vendors', 'payments.vendor_bill_id', '=', 'vendors.vendor_bill_id')
            ->leftJoin('chsone_vendors_master as master', 'payments.vendor_id', '=', 'master.vendor_id')
            ->where('payments.vendor_bill_id', $VendorDetailId);

        // add count and pagination
        $count = $obj->count();

        $obj = $obj->offset($offset);
        $obj = $obj->limit($per_page);

        $result = $obj->get();

        // $formattedData = $this->format($result->toArray());
        $formattedData = $result->toArray();

        // $this->data = $this->format($result->toArray());

        $totalQuery = "SELECT
            SUM(vendor_bill_payment_amount) AS vendor_bill_payment_amount,
            SUM(write_off) AS write_off,
            SUM(tds) AS tds
        FROM
        chsone_vendor_bill_payment_details
        WHERE
            vendor_bill_id = " . $VendorDetailId;

        $total = $this->tenantDB()->select($totalQuery);


        $vendorId = $this->tenantDB()->table('chsone_vendor_bill_master')
            ->where('vendor_bill_id', $this->input['vendor_bill_id'])
            ->value('vendor_id');

        // $totalArray = [
        //     'id' => 'total',
        //     'vendor_bill_payment_mode' => 'TOTAL PAID',
        //     'vendor_id'=> $vendorId,
        //     'vendor_bill_payment_amount' => '₹ ' . number_format($total[0]->vendor_bill_payment_amount, 2) . '(' . number_format($total[0]->tds, 2) . ')(' . number_format($total[0]->write_off, 2) . ')'
        // ];
        $totalArray = [
            'id' => 'total',
            'vendor_bill_payment_mode' => 'TOTAL PAID',
            'vendor_id'=> $vendorId,
            // 'vendor_bill_payment_amount' => '₹ ' . number_format($total[0]->vendor_bill_payment_amount, 2),
            'vendor_bill_payment_amount' => $total[0]->vendor_bill_payment_amount,
            'tds' => number_format($total[0]->tds, 2),
            'write_off' => number_format($total[0]->write_off, 2)
        ];

        $result = $this->format($formattedData);

        $this->data = array_merge($result, [$totalArray]);
        $this->meta['pagination']['total'] = $count;
        // $this->data= array_merge($result, [$totalArray]);

    }

   public function getAmountPaid($amount, $tds, $write_off)
    {
        $amount = (float) $amount ? (float) $amount : 0;
        $tds= (float) $tds ? (float) $tds : 0;
        $write_off= (float) $write_off ? (float) $write_off : 0;
        // return '₹ ' . number_format($amount, 2) . '(' . number_format($tds, 2) . ')(' . number_format($write_off, 2) . ')';
        // return '₹ ' . number_format($amount, 2);
        return $amount;
    }

    public function setNA($value)
    {
        if($value === null || $value === ''){
            return 'N/A';
        }else{
            return $value;
        }
    }
}
