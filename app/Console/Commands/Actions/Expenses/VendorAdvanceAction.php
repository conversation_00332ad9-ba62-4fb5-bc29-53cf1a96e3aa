<?php

namespace App\Console\Commands\Actions\Expenses;

use App\Models\Tenants\ChsoneCreditAccount;
use App\Models\Tenants\ChsoneVendorsMaster;
use Illuminate\Console\Command;
use App\Console\Commands\Action;

class VendorAdvanceAction extends Action
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'datasource:vendorAdvances {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Get Vendor Advance List';

    protected $formatter = [
        "id" => "",
        "account_id" => "",
        "account_name" => "",
        "total_cr" => "",
        "total_dr" => "",
        "total_refundable_dr" => "",
        "total_refundable_cr" => "",
        "total_adjustable_dr" => "",
        "total_adjustable_cr" => "",
        "total_refundable" => "getAmountPaid:total_refundable",
        "total_adjustable" => "getAmountPaid:total_adjustable",
        "total_balance" => "getAmountPaid:total_balance",
        "is_reversable" => "",
        "refund_id" => ""
    ];
    protected $formatterByKeys = ['id'];

    protected $mapper = [
        "id" => "cr_acc.account_id",
        'vendor_name' => 'cr_acc.account_name'
    ];

    /**
     * Execute the console command.
     */
    public function apply()
    {
        $vendorName = '';
        $searchTerm = $this->input['filters']['search'] ?? '';

        if (!empty($this->input['filters'])) {
            $vendorName =
                !empty($this->input['filters']['account_name']) ? $this->input['filters']['account_name'] : '';
            unset($this->input['filters']['account_name']);
        }

        // add pagination
        $page = $this->input['page'] ?? 1;
        $per_page = !empty($this->input['per_page']) ? $this->input['per_page']: ($this->hugeData ? 100000 : 10);
        $offset = ($page - 1) * $per_page;

        $obj = $this->tenantDB()->table('chsone_credit_accounts AS cr_acc')
            ->selectRaw(
                'cr_acc.account_id AS id,
                        cr_acc.account_id,
                        cr_acc.account_name AS account_name'
            )->join('chsone_vendors_master AS vendormaster', 'cr_acc.account_id', '=', 'vendormaster.vendor_id')
            ->where('cr_acc.account_context', '=', 'vendor')
            ->groupBy(
                'cr_acc.account_id'
            )
            ->orderByDesc('cr_acc.credit_account_id')

            ->selectRaw('SUM(IF(cr_acc.transaction_type = "cr", cr_acc.amount, 0)) AS total_cr,
        SUM(IF(cr_acc.transaction_type = "dr", cr_acc.amount, 0)) AS total_dr,
        SUM(IF(cr_acc.transaction_type = "dr" AND cr_acc.use_credit = "refundable", cr_acc.amount, 0)) AS total_refundable_dr,
        SUM(IF(cr_acc.transaction_type = "cr" AND cr_acc.use_credit = "refundable", cr_acc.amount, 0)) AS total_refundable_cr,
        SUM(IF(cr_acc.transaction_type = "dr" AND cr_acc.use_credit = "adjustable", cr_acc.amount, 0)) AS total_adjustable_dr,
        SUM(IF(cr_acc.transaction_type = "cr" AND cr_acc.use_credit = "adjustable", cr_acc.amount, 0)) AS total_adjustable_cr,
        SUM(IF(cr_acc.transaction_type = "dr" AND cr_acc.use_credit = "adjustable", cr_acc.amount, 0)) - SUM(IF(cr_acc.transaction_type = "cr" AND cr_acc.use_credit = "adjustable", cr_acc.amount, 0)) AS total_adjustable,
        SUM(IF(cr_acc.transaction_type = "dr" AND cr_acc.use_credit = "refundable", cr_acc.amount, 0)) - SUM(IF(cr_acc.transaction_type = "cr" AND cr_acc.use_credit = "refundable", cr_acc.amount, 0)) AS total_refundable,
        SUM(IF(cr_acc.transaction_type = "dr", cr_acc.amount, 0)) - SUM(IF(cr_acc.transaction_type = "cr", cr_acc.amount, 0)) AS total_balance
        , IF(
            SUM(IF(cr_acc.transaction_type = "dr" AND cr_acc.use_credit = "refundable", cr_acc.amount, 0)) 
            - SUM(IF(cr_acc.transaction_type = "cr" AND cr_acc.use_credit = "refundable", cr_acc.amount, 0)) > 0, 
            1, 0
        ) AS is_reversable'
        );

         /*   $columns = [
            'cr_acc.account_name',
            'cr_acc.account_id'
        ];

        if ($searchTerm) {
            $obj= $obj->where(function ($q) use ($columns, $searchTerm) {
                foreach ($columns as $column) {
                    $q->orWhere($column, 'LIKE', '%' . $searchTerm . '%');
                }
            });
            
        }*/

        if ($vendorName) {
            $obj = $obj->whereRaw("LOWER(cr_acc.account_name) LIKE '%{$vendorName}%'");
        }
        
        $obj = $obj->havingRaw("
        account_name LIKE ? OR
        total_refundable LIKE ? OR
        total_balance LIKE ? OR
        total_adjustable LIKE ?
        ", ["%{$searchTerm}%", "%{$searchTerm}%", "%{$searchTerm}%", "%{$searchTerm}%"]);

        // add count and pagination
        $count = $obj->count();

        $obj = $obj->offset($offset);
        $obj = $obj->limit($per_page);

        $obj = $this->filter($obj);
        $result = $obj->get();

        // create a function to to get first credit_account_id record from chsone_crdit_accounts table whose account_id is $result->account_id
        // pass refund_id->credit_account_id to the result at every record
        foreach ($result as $value) {
            $refund_id = $this->tenantDB()->table('chsone_credit_accounts')
                ->select('credit_account_id')
                ->where('account_id', $value->account_id)
                ->where('account_context', 'Vendor')
                ->first();
            $value->refund_id = $refund_id->credit_account_id;
        }

        $this->data = $this->format($result->toArray());
        $this->meta['pagination']['total'] = $count;
    }

    public function getAmountPaid($payment_amount)
    {

        $payment_amount = (float) $payment_amount;

        return $payment_amount;
    }
}
