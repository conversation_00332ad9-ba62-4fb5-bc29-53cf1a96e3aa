<?php

namespace App\Console\Commands\Actions\Expenses;

use Illuminate\Console\Command;
use App\Console\Commands\Action;

class cancelVendorBillDataSource extends Action
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'datasource:cancelVendorBill {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Cancel a vendor bill';

    /**
     * Execute the console command.
     */
    public function apply()
    {
        $vendor_bill_id = $this->input['vendor_bill_id'];

        // check id is exist or not if not exist then return error
        $bill = $this->tenantDB()->table('chsone_vendor_bill_master')
            ->where('vendor_bill_id', $vendor_bill_id)
            ->get();

        if(count($bill) == 0){
            $this->status = 'error';
            $this->message = 'Bill not found';
            $this->statusCode = 400;
            $this->data = [];
            return;
        }

        $result = $this->tenantDB()->table('chsone_vendor_bill_master')
            ->where('vendor_bill_id', $vendor_bill_id)
            ->update([
                'status' => 3
            ]);
        
        if($result){
            $this->status = 'success';
            $this->message = 'Bill has been cancelled successfully';
            $this->statusCode = 200;
            $this->data = [];
        }else{
            $this->status = 'error';
            $this->message = 'Bill not deleted';
            $this->statusCode = 400;
            $this->data = [];
        }
    }
}
