<?php

namespace App\Console\Commands\Actions\Expenses;

use Illuminate\Console\Command;
use App\Console\Commands\Action;
use App\Models\Tenants\ChsoneLedgerTransaction;
use Exception;

class paymentTrackerConfirmationClearedDataSource extends Action
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'datasource:paymentTrackerConfirmationCleared {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Action for expense payment tracker confirmation cleared';

    /**
     * Execute the console command.
     */
    public function apply()
    {
        try {
            $expense_id = $this->input['id'];
            $payment_date = $this->input['payment_date'];

            // check expense_id is exist or not in expense_invoice_payment_tracker table if not exist then return error
            $expenseDetails = $this->tenantDB()->table('expense_invoice_payment_tracker')
                ->where('id', $expense_id)
                ->get();

            if (count($expenseDetails) == 0) {
                $this->status = 'error';
                $this->message = 'No record found for this id';
                $this->statusCode = 400;
                $this->data = [];
                return;
            }

            // update the status in expense_payment_tracker table
            $result = $this->tenantDB()->table('expense_invoice_payment_tracker')
                ->where('id', $expense_id)
                ->update([
                    'status' => 'Y',
                    'payment_date' => $payment_date,
                    'updated_date' => $payment_date . ' 00:00:00'
                ]);

            if ($result) {

                // insert the new entry in the chsone_vendor_bill_payment_details table
                $insertVendorBillPaymentDetails = $this->insertVendorBillPaymentDetails($expense_id, $payment_date, $expenseDetails[0]);

                if (!$insertVendorBillPaymentDetails) {
                    $this->status = 'error';
                    $this->message = 'Error in update status in chsone_vendor_bill_payment_details table.';
                    $this->statusCode = 400;
                    $this->data = [];
                    return;
                }

                // update the status in chsone_vendor_bill_master table
                $updateStatus = $this->updateStatus($expense_id);

                if (!$updateStatus) {
                    $this->status = 'error';
                    $this->message = 'Error in update status in chsone_vendor_bill_master table.';
                    $this->statusCode = 400;
                    $this->data = [];
                    return;
                }

                // // insert cleared entry in chsone_ledger_transaction table
                $insertLedgerTransaction = $this->insertLedgerTransaction($expenseDetails[0]);

                if (!$insertLedgerTransaction) {
                    $this->status = 'error';
                    $this->message = 'Error in insert ledger transaction.';
                    $this->statusCode = 400;
                    $this->data = [];
                    return;
                }

                $this->status = 'success';
                $this->message = 'Payment has been cleared successfully';
                $this->statusCode = 200;
                $this->data = [];
            } else {
                $this->status = 'error';
                $this->message = 'Error in payment cleared';
                $this->statusCode = 400;
                $this->data = [];
            }
        } catch (Exception $e) {
            $this->status = 'error';
            $this->message = $e->getMessage();
            $this->statusCode = 400;
            $this->data = [];
        }
    }

    /**
     * Insert the new entry in the chsone_vendor_bill_payment_details table
     */
    public function insertVendorBillPaymentDetails($expense_id, $payment_date, $expenseDetails)
    {
        // find out from_ledger_id from chsone_grp_ledger_tree table whose context_ref_id is expenseDetails->vendor_id and context is vendor
        $ledger_account_id = $this->tenantDB()->table('chsone_grp_ledger_tree')
            ->where('context_ref_id', $expenseDetails->vendor_id)
            ->where('context', 'vendor')
            ->first();
        $from_ledger_id = $ledger_account_id?->ledger_account_id ?? null;

        // find out to_ledger_id from expenseDetails->other_information
        $other_information = unserialize($expenseDetails->other_information);
        $to_ledger_id = $other_information['bank_ledger'];

        // get ledger_account_name from chsone_grp_ledger_tree table whose id is $to_ledger_id
        $ledger_account_name = $this->tenantDB()->table('chsone_grp_ledger_tree')
            ->where('ledger_account_id', $to_ledger_id)
            ->first();
        $to_ledger_account_name = $ledger_account_name?->ledger_account_name ?? null;

        // fetch vendor_bill_num from chsone_vendor_bill_master table whose vendor_bill_id is $expenseDetails->invoice_number
        $vendorBillNum = $this->tenantDB()->table('chsone_vendor_bill_master')
            ->where('vendor_bill_id', $expenseDetails->invoice_number)
            ->first();

        $vendorBillNum = $vendorBillNum?->vendor_bill_num ?? null;

        $insertData = [
            'vendor_bill_id' => $expenseDetails->invoice_number ?? '',
            'vendor_id' => $expenseDetails->vendor_id ?? '',
            'soc_id' => $expenseDetails->soc_id ?? '',
            'from_ledger_id' => $from_ledger_id ?? '',
            'to_ledger_id' => $to_ledger_id ?? '',
            'vendor_bill_payment_amount' => $expenseDetails->payment_amount ?? '0',
            'tds' => $expenseDetails->tds ?? '0',
            'write_off' => $expenseDetails->writeoff_amount ?? '0',
            'expense_tracker_id' => $expense_id,
            'vendor_bill_payment_date' => $payment_date,
            'vendor_bill_payment_mode' => $expenseDetails->payment_mode ?? '',
            'vendor_bill_payment_comments' => 'Bill number ' . $vendorBillNum . ' paid on date' . $payment_date,
            'vendor_bill_payment_bank_name' => $to_ledger_account_name ?? '',
            'vendor_bill_payment_cheque_number' => $expenseDetails->transaction_reference ?? '',
            'added_on' => $payment_date . ' 00:00:00',
            'status' => '1'
        ];

        $result = $this->tenantDB()->table('chsone_vendor_bill_payment_details')
            ->insert($insertData);

        return $result;
    }

    /**
     * Update the status in chsone_vendor_bill_master table
     */
    public function updateStatus($expense_id)
    {
        // fecth the payment amount from chsone_vendor_bill_payment_details table whose expense_id is $expense_id
        $paymentDetails = $this->tenantDB()->table('chsone_vendor_bill_payment_details')
            ->where('expense_tracker_id', $expense_id)
            ->get();

        // update the status partialpaid or unpaid according to the payment amount in chsone_vendor_bill_master table
        
        $paymentAmount = 0;
        $invoiceNumber = '';
        foreach ($paymentDetails as $paymentDetail) {
            $paymentAmount = $paymentDetail->vendor_bill_payment_amount;

            $invoiceNumber = $paymentDetail->vendor_bill_id;

            $result = $this->tenantDB()->table('chsone_vendor_bill_master')
                ->where('vendor_bill_id', $invoiceNumber)
                ->first();

            if ($result->vendor_bill_amount == $paymentAmount) {
                $status = 'paid';
            } elseif ($paymentAmount > 0 && $paymentAmount < $result->vendor_bill_amount) {
                $status = 'partialpaid';
            }

            $updateStatus = $this->tenantDB()->table('chsone_vendor_bill_master')
                ->where('vendor_bill_id', $invoiceNumber)
                ->update([
                    'payment_status' => $status
                ]);

            /*if (!$updateStatus) {
                return false;
            }*/
        }

        return true;
    }

    /**
     * Insert the ledger transaction in chsone_ledger_transaction table
     */
    public function insertLedgerTransaction($expenseDetails)
    {
        // fetch $to_ledger_id, $from_ledger_id from chsone_vendor_bill_payment_details table whose expense_tracker_id is $expenseDetails->id
        $ledgerAccount = $this->tenantDB()->table('chsone_vendor_bill_payment_details')
            ->where('expense_tracker_id', $expenseDetails->id)
            ->first();
        $to_ledger_id = $ledgerAccount?->to_ledger_id;
        $from_ledger_id = $ledgerAccount?->from_ledger_id;


        // fetch ledger_account_name from chsone_grp_ledger_tree table whose ledger_account_id is from_ledger_id
        $ledgerAccount = $this->tenantDB()->table('chsone_grp_ledger_tree')
            ->where('ledger_account_id', $from_ledger_id)
            ->first();

        $dr_account_id = $from_ledger_id;
        $dr_account_name = $ledgerAccount?->ledger_account_name;

        // check payment mode
        if ($expenseDetails->payment_mode == 'cash' || $expenseDetails->payment_mode == 'cheque') {
            // fecth ledger_account name from chsone_grp_ledger_tree table whose ledger_account_id is to_ledger_id
            $ledgerAccount = $this->tenantDB()->table('chsone_grp_ledger_tree')
                ->where('ledger_account_id', $to_ledger_id)
                ->first();
            $cr_account_id = $to_ledger_id;
            $cr_account_name = $ledgerAccount?->ledger_account_name;
        }

        // fetch vendor_bill_num from chsone_vendor_bill_master table whose vendor_bill_id is $expenseDetails->invoice_number
        $vendorBillNum = $this->tenantDB()->table('chsone_vendor_bill_master')
            ->where('vendor_bill_id', $expenseDetails->invoice_number)
            ->first();
        $vendorBillNum = $vendorBillNum?->vendor_bill_num;

        // now insert the reversal entry in chsone_ledger_transaction table
        $insertFirstLedger = new ChsoneLedgerTransaction();
        $insertFirstLedger->soc_id = $expenseDetails->soc_id;
        $insertFirstLedger->transaction_date = $expenseDetails->payment_date;
        $insertFirstLedger->ledger_account_id = $dr_account_id;
        $insertFirstLedger->ledger_account_name = $dr_account_name;
        $insertFirstLedger->voucher_type = 'payment';
        $insertFirstLedger->voucher_reference_number = $expenseDetails->payment_number;
        $insertFirstLedger->voucher_reference_id = $expenseDetails->id;
        $insertFirstLedger->transaction_type = 'dr';
        $insertFirstLedger->payment_mode = $expenseDetails->payment_mode;
        $insertFirstLedger->transaction_amount = $expenseDetails->payment_amount;
        $insertFirstLedger->other_reference_id = $expenseDetails->id;
        $insertFirstLedger->txn_from_id = 0;
        $insertFirstLedger->memo_desc = 'Bill number ' . $vendorBillNum . ' paid on date' . $expenseDetails->payment_date;
        $insertFirstLedger->is_opening_balance = 0;
        $insertFirstLedger->is_reconciled = 0;
        $insertFirstLedger->is_cancelled = 0;
        $insertFirstLedger->created_by = $this->input['user_id'] ?? 0;
        $insertFirstLedger->added_on = date('Y-m-d H:i:s');

        $insertFirstLedger->save();

        //get last inserted id
        $lastInsertedId = $insertFirstLedger->id;

        // insert second ledger entry
        $insertSecondLedger = new ChsoneLedgerTransaction();
        $insertSecondLedger->soc_id = $expenseDetails->soc_id;
        $insertSecondLedger->transaction_date = $expenseDetails->payment_date;
        $insertSecondLedger->ledger_account_id = $cr_account_id;
        $insertSecondLedger->ledger_account_name = $cr_account_name;
        $insertSecondLedger->voucher_type = 'payment';
        $insertSecondLedger->voucher_reference_number = $expenseDetails->payment_number;
        $insertSecondLedger->voucher_reference_id = $expenseDetails->id;
        $insertSecondLedger->transaction_type = 'cr';
        $insertSecondLedger->payment_mode = $expenseDetails->payment_mode;
        $insertSecondLedger->transaction_amount = $expenseDetails->payment_amount;
        $insertSecondLedger->other_reference_id = $expenseDetails->id;
        $insertSecondLedger->txn_from_id = $lastInsertedId;
        $insertSecondLedger->memo_desc = 'Bill number ' . $vendorBillNum . ' paid on date' . $expenseDetails->payment_date;
        $insertSecondLedger->is_opening_balance = 0;
        $insertSecondLedger->is_reconciled = 0;
        $insertSecondLedger->is_cancelled = 0;
        $insertSecondLedger->created_by = $this->input['user_id'] ?? 0;
        $insertSecondLedger->added_on = date('Y-m-d H:i:s');

        $insertSecondLedger->save();

        if ($insertFirstLedger && $insertSecondLedger) {
            return true;
        } else {
            return false;
        }
    }
}
