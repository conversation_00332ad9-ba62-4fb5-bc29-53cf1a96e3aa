<?php

namespace App\Console\Commands\Actions\Expenses;

use Illuminate\Console\Command;
use App\Console\Commands\Action;
use Carbon\Carbon;


class VendorBillDataSource extends Action
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'datasource:VendorBill {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Get Vendor Bill List';

    protected $formatter = [
        'id' => '',
        'vendor_id' => '',
        'vendor_name' => '',
        'vendor_bill_type_purchase' => '',
        'vendor_bill_num' => '',
        'vendor_bill_date' => '',
        'vendor_bill_amount' => '',
        'vendor_bill_tds' => '',
        'vendor_bill_writeoff' => '',
        'vendor_bill_due_date' => '',
        'payment_status' => '',
        'status' => '',
        'write_off' => '',
        'is_rcm' => '',
        'vendor_bill_payment_amount' => '',
        'vendor_bill_payment_amount_final' => 'setRupees:vendor_bill_payment_amount',
        'tds' => '',
        'vendor_bill_amount' => 'add:vendor_bill_amount,vendor_bill_tds',
        'vendor_bill_amount_final' => 'add:vendor_bill_amount,vendor_bill_tds',
        'vendor_bill_due_amount' => 'subtract:vendor_bill_amount,vendor_bill_payment_amount,vendor_bill_tds,write_off,payment_status',
        'voucher_id' => '',
    ];

    protected $formatterByKeys = ['id'];

    protected $mapper = [
        'id' => 'cvbm.vendor_bill_id',
    ];

    protected $schema = [
        "table" => [
            "tableTitle" => "Purchases/Expenses",
            "tabs" => [
                "Approved",
                "Unapproved",
                "Rejected"
            ],
            "select_by" => [
                "vendor_name" => "Vendor Name",
                "vendor_bill_num" => "Vendor Bill Number"
            ],
            "filter_by" => [
                "status" => [
                    "title" => "Status",
                    "options" => [
                        "cancelled" => "Cancelled"
                    ]
                ],
                "payment_status" => [
                    "title" => "Payment Status",
                    "options" => [
                        "paid" => "Paid",
                        "unpaid" => "Unpaid",
                        "partialpaid" => "Partialpaid"
                    ]
                ],
                "payment_mode" => [
                    "title" => "Purchase Mode",
                    "options" => [
                        "credit" => "Credit",
                        "cash" => "Cash"
                    ]
                ],
                "vendor_bill_due_date" => [
                    "title" => "Vendor Bill Due Date",
                    "options" => [
                        "overdue" => "Overdue",
                        "due_in_one_month" => "Due in One month",
                        "due_in_one_week" => "Due in One Week"
                    ]
                ],
                "is_rcm" => [
                    "title" => "RCM",
                    "options" => [
                        "rcm" => "RCM"
                    ]
                ],
                "financial_year" => [
                    "title" => "Financial Year",
                    "options" => [
                        "2019-04-01 to 2020-03-31" => "2019-04-01 to 2020-03-31",
                        "2020-04-01 to 2021-03-31" => "2020-04-01 to 2021-03-31",
                        "2021-04-01 to 2022-03-31" => "2021-04-01 to 2022-03-31"
                    ]
                ]
            ],
            "actions" => [
                [
                    "title" => "New Payment",
                    "icon" => "ri-add-circle-line",
                    "color" => "success",
                    "variant" => "contained",
                    "form" => "vendorNewPayment"
                ],
                [
                    "title" => "New Cash Purchase",
                    "icon" => "ri-add-circle-line",
                    "color" => "primary",
                    "redirect" => "/admin/vendorbill/addBill/",
                    "variant" => "contained"
                ],
                [
                    "title" => "New vendor bill",
                    "icon" => "ri-add-circle-line",
                    "color" => "primary",
                    "redirect" => "/admin/vendorbill/addVendorBill/",
                    "variant" => "contained"
                ]
            ],
            "fields" => [
                "*"
            ],
            "columns" => [
                [
                    "title" => "Voucher No",
                    "key" => "id",
                    "type" => "link",
                    "href" => "/admin/vendorbill/vendorBillDetails/:id"
                ],
                [
                    "title" => "Bill No",
                    "key" => "vendor_bill_num",
                    "type" => "link",
                    "href" => "/admin/vendorbill/vendorBillDetails/:id"
                ],
                [
                    "title" => "Bill Date",
                    "key" => "vendor_bill_date"
                ],
                [
                    "title" => "Vendor Name",
                    "key" => "vendor_name",
                    "type" => "chip_with_string",
                    "options" => [
                        "1" => [
                            "title" => "RCM",
                            "color" => "info"
                        ],
                        "0" => [
                            "title" => "",
                            "color" => "default"
                        ]
                    ]
                ],
                [
                    "title" => "Purchase Mode",
                    "key" => "vendor_bill_type_purchase"
                ],
                [
                    // "title" => "Amt Paid",
                    "key" => "vendor_bill_payment_amount_final",
                    "type" => "colored",
                    "options" => [
                        "vendor_bill_payment_amount_final" => [
                            "title" => "Amt Paid",
                            "type" => "number",
                            "currency" => "inr"
                        ],
                        "(" => [
                            "title" => " (",
                            "color" => "success",
                            "value" => " ("
                        ],
                        "write_off" => [
                            "title" => "W/O",
                            "color" => "success"
                        ],
                        ")" => [
                            "title" => ")",
                            "color" => "success",
                            "value" => ")"
                        ]
                        // ,
                        // "rs" => [
                        //     "title" => "₹"
                        // ]
                    ]
                ],
                [
                    "title" => "Due Amt",
                    "key" => "vendor_bill_due_amount",
                    "type" => "number",
                    "currency" => "inr"
                ],
                [
                    // "title" => "Bill Amt",
                    "key" => "vendor_bill_amount_final",
                    "type" => "colored",
                    "options" => [
                        "vendor_bill_amount_final" => [
                            "title" => "Bill Amt",
                            "type" => "number",
                            "currency" => "inr"
                        ],
                        "(" => [
                            "title" => " (",
                            "color" => "error",
                            "value" => " ("
                        ],
                        "vendor_bill_tds" => [
                            "title" => "TDS",
                            "color" => "error"
                        ],
                        ")" => [
                            "title" => ")",
                            "color" => "error",
                            "value" => ")"
                        ]
                        // ,
                        // "rs" => [
                        //     "title" => "₹"
                        // ]
                    ]
                ],
                [
                    "title" => "Due Date",
                    "key" => "vendor_bill_due_date",
                    "conditions" => [
                        [
                            "key" => "payment_status",
                            "operator" => "equal",
                            "value" => "unpaid",
                            "color" => "error"
                        ]
                    ]
                ],
                [
                    "title" => "Payment Status",
                    "key" => "payment_status",
                    "type" => "chip",
                    "options" => [
                        "paid" => [
                            "title" => "Paid",
                            "color" => "success"
                        ],
                        "unpaid" => [
                            "title" => "Unpaid",
                            "color" => "error"
                        ],
                        "partialpaid" => [
                            "title" => "Partialpaid",
                            "color" => "warning"
                        ]
                    ]
                ],
                [
                    "title" => "Actions",
                    "type" => "actions",
                    "key" => "actions",
                    "actions" => [
                        [
                            "title" => "View",
                            "icon" => "ri-eye-line",
                            "href" => "/admin/vendorbill/vendorBillDetails/:id",
                        ],
                        [
                            "title" => "View Payment",
                            "icon" => "ri-bank-card-line",
                            "href" => "/admin/vendorbill/viewPayments/:id?section=expense",
                        ],
                        [
                            "title" => "Pay Bill",
                            "icon" => "ri-cash-line",
                            "href" => "/admin/vendorbill/payBill/:vendor_id/:id",
                            "disable_on" => [
                                "payment_status" => [
                                    "paid"
                                ]
                            ]
                        ],
                        [
                            "title" => "Cancel",
                            "icon" => "ri-close-line",
                            "form" => "expenseCancelVendorBill",
                            // "api" => [
                            //     'method' => 'delete',
                            //     'url' => '/admin/vendorbill/cancelVendorBill/:id',
                            // ],
                            "hide_on" => [
                                "payment_status" => [
                                    "paid",
                                    "partialpaid"
                                ]
                            ]
                        ]
                    ]
                ]
            ]
        ]
    ];

    /**
     * Execute the console command.
     */
    public function apply()
    {
        if (isset($this->input['current_tab'])) {
            $CurrentTab = $this->input['current_tab'];
        } else {
            $CurrentTab = 'approved';
        }
        $vendorName = '';
        $vendorBillNum = '';
        $status = '';
        $paymentStatus = '';
        $paymentMode = '';
        $vendorBillDueDate = '';
        $isRcm = '';
        $financialYear = '';
        $sortBy = '';
        $sortByKey = '';
        $sortByValue = '';

        if (!empty($this->input['filters'])) {
            $vendorName =
                !empty($this->input['filters']['vendor_name']) ? $this->input['filters']['vendor_name'] : '';
            unset($this->input['filters']['vendor_name']);

            $vendorBillNum =
                !empty($this->input['filters']['vendor_bill_num']) ? $this->input['filters']['vendor_bill_num'] : '';
            unset($this->input['filters']['vendor_bill_num']);

            $status = isset($this->input['filters']['status']) ? explode(',', $this->input['filters']['status']) : [];

            $paymentStatus = isset($this->input['filters']['payment_status']) ? explode(',', $this->input['filters']['payment_status']) : [];

            $paymentMode = isset($this->input['filters']['payment_mode']) ? explode(',', $this->input['filters']['payment_mode']) : [];

            $vendorBillDueDate = isset($this->input['filters']['vendor_bill_due_date']) ? explode(',', $this->input['filters']['vendor_bill_due_date']) : [];

            $isRcm = isset($this->input['filters']['is_rcm']) ? explode(',', $this->input['filters']['is_rcm']) : [];

            $financialYear = isset($this->input['filters']['financial_year']) ? explode(',', $this->input['filters']['financial_year']) : [];
        }

        // add pagination
        $page = $this->input['page'] ?? 1;
        $per_page = !empty($this->input['per_page']) ? $this->input['per_page'] : ($this->hugeData ? 100000 : 10);
        $offset = ($page - 1) * $per_page;

        $obj = $this->tenantDB()->table('chsone_vendor_bill_master as cvbm')
            ->select(
                'cvbm.vendor_bill_id as id',
                'cvbm.vendor_bill_id as voucher_id',
                'cvbm.vendor_id',
                'vendor_bill_type_purchase',
                'vendor_bill_num',
                'vendor_bill_date',
                'vendor_bill_amount',
                'vendor_bill_tds',
                'vendor_bill_writeoff',
                'vendor_bill_due_date',
                'payment_status',
                'cvbm.status',
                'cvbm.is_rcm',
            )
            ->leftJoin('chsone_vendors_master as cvm', 'cvbm.vendor_id', '=', 'cvm.vendor_id')
            ->where(function ($obj) {
                $obj->whereIn('is_billable', [0, 1])
                    ->orWhereNull('is_billable');
            })
            ->where('cvbm.status', 1)
            ->where(function ($obj) {
                $obj->whereNull('is_billed')
                    ->orWhereIn('is_billed', [0, 1]);
            })
            // ->orWhere('is_rcm', 1)
            ->groupBy(
                'cvbm.vendor_bill_id',
                'cvbm.vendor_id',
                'vendor_bill_type_purchase',
                'vendor_bill_num',
                'vendor_bill_date',
                'vendor_bill_amount',
                'vendor_bill_tds',
                'vendor_bill_writeoff',
                'vendor_bill_due_date',
                'payment_status',
                'cvbm.status',
                'vendor_name'
            )
            ->orderByDesc('vendor_bill_date')
            ->selectRaw("IFNULL(vendor_name, 'Petty Cash') AS vendor_name")
            ->selectRaw(
                "(SELECT IF(SUM(cvbpd.vendor_bill_payment_amount) = 0, 0, SUM(cvbpd.vendor_bill_payment_amount))
                  FROM chsone_vendor_bill_payment_details AS cvbpd
                  LEFT JOIN expense_invoice_payment_tracker AS eipt ON cvbpd.expense_tracker_id = eipt.id
                  WHERE cvbpd.vendor_bill_id = cvbm.vendor_bill_id
                  AND eipt.status != 'reversed') AS vendor_bill_payment_amount"
            )
            ->selectRaw(
                "(SELECT COALESCE(CAST(IF(SUM(cvbpd.tds) = 0, 0, SUM(cvbpd.tds)) AS DECIMAL(10,2)), 0.0)
                 FROM chsone_vendor_bill_payment_details AS cvbpd
                 LEFT JOIN expense_invoice_payment_tracker AS eipt ON cvbpd.expense_tracker_id = eipt.id
                 WHERE cvbpd.vendor_bill_id = cvbm.vendor_bill_id
                 AND eipt.status != 'reversed') AS tds"
            )

            ->selectRaw(
                "(SELECT COALESCE(IF(SUM(cvbpd.write_off) = 0, 0, SUM(cvbpd.write_off)),0)
                  FROM chsone_vendor_bill_payment_details AS cvbpd
                  LEFT JOIN expense_invoice_payment_tracker AS eipt ON cvbpd.expense_tracker_id = eipt.id
                  WHERE cvbpd.vendor_bill_id = cvbm.vendor_bill_id
                  AND eipt.status != 'reversed') AS write_off"
            );

        if ($vendorName) {
            $obj = $obj->whereRaw("LOWER(cvm.vendor_name) LIKE '%{$vendorName}%'");
        }

        if ($vendorBillNum) {
            $obj = $obj->where("vendor_bill_num", 'like', '%' . $vendorBillNum . '%');
        }

        if ($status) {
            $obj = $obj->whereIn("cvbm.status", $status);
        }

        if ($paymentStatus) {
            $obj = $obj->whereIn("payment_status", $paymentStatus);
        }
    
        if ($CurrentTab == 'approved') {
            $obj = $obj->where('cvbm.status', 1);
        } elseif ($CurrentTab == 'unapproved') {
            $obj = $obj->where('cvbm.status', 0);
        } else {
            $obj = $obj->where('cvbm.status', 2);
        }


        if ($paymentMode) {
            $obj = $obj->whereIn("vendor_bill_type_purchase", $paymentMode);
        }

        // if($vendorBillDueDate) {
        //     dd($vendorBillDueDate);
        //     if ($vendorBillDueDate==='overdue') {
        //         $obj = $obj->whereRaw("CASE
        //             WHEN vendor_bill_due_date < CURDATE() THEN 'Overdue'
        //             ELSE 'Not Overdue'
        //         END = ?", [$vendorBillDueDate]);
        //     }

        //     if ($vendorBillDueDate==='due_in_one_month') {
        //         $obj = $obj->whereRaw("CASE
        //             WHEN vendor_bill_due_date < CURDATE() AND YEAR(vendor_bill_due_date) = YEAR(CURDATE()) AND MONTH(vendor_bill_due_date) = MONTH(CURDATE()) THEN 'Overdue This Month'
        //             ELSE 'Not Overdue This Month'
        //         END = ?", [$vendorBillDueDate]);
        //     }

        //     if ($vendorBillDueDate==='due_in_one_week') {
        //         $obj = $obj->whereRaw("CASE
        //             WHEN vendor_bill_due_date < CURDATE() AND YEARWEEK(vendor_bill_due_date, 1) = YEARWEEK(CURDATE(), 1) THEN 'Overdue This Week'
        //             ELSE 'Not Overdue This Week'
        //         END = ?", [$vendorBillDueDate]);
        //     }

        //     dd($obj->toSql());
        //     // $obj = $obj->whereIn("vendor_bill_due_date", $vendorBillDueDate);
        // }

        if ($vendorBillDueDate) {
            $obj = $obj->where(function ($query) use ($vendorBillDueDate) {
                foreach ($vendorBillDueDate as $status) {
                    if ($status === 'overdue') {
                        $query->orWhere(function ($subQuery) {
                            $subQuery->where('vendor_bill_due_date', '<', Carbon::now())
                                ->where(function ($subQuery) {
                                    $subQuery->where('payment_status', 'unpaid')
                                        ->orWhere('payment_status', 'partialpaid');
                                });
                        });
                    }
                    if ($status === 'due_in_one_month') {
                        $query->orWhere(function ($subQuery) {
                            $subQuery->whereRaw("vendor_bill_due_date BETWEEN CURDATE() AND DATE_ADD(CURDATE(), INTERVAL 1 MONTH)")
                                ->where(function ($subQuery) {
                                    $subQuery->where('payment_status', 'unpaid')
                                        ->orWhere('payment_status', 'partialpaid');
                                });
                        });
                    }
                    if ($status === 'due_in_one_week') {
                        $query->orWhere(function ($subQuery) {
                            $subQuery->whereRaw("vendor_bill_due_date BETWEEN CURDATE() AND DATE_ADD(CURDATE(), INTERVAL 1 WEEK)")
                                ->where(function ($subQuery) {
                                    $subQuery->where('payment_status', 'unpaid')
                                        ->orWhere('payment_status', 'partialpaid');
                                });
                        });
                    }
                }
            });
        }

        if ($isRcm) {
            $obj = $obj->whereIn("is_rcm", $isRcm);
        }


        if ($financialYear) {
            $obj->where(function ($query) use ($financialYear) {
                foreach ($financialYear as $range) {
                    $year = explode(' to ', $range);
                    $query->orWhereBetween('vendor_bill_date', [$year[0], $year[1]]);
                }
            });
        }

        if (!empty($this->input['sort'])) {
            $sortBy = $this->input['sort'];
            $sortByKey = array_key_first($sortBy);
            $sortByValue = $sortBy[$sortByKey];
            unset($this->input['sort']);
        }

        if ($sortBy) {
            $obj = $obj->orderBy($sortByKey, $sortByValue);
        }

        // add count and pagination
        $count = $obj->get()->count();

        $obj = $obj->offset($offset);
        $obj = $obj->limit($per_page);

        $result = $obj->get();

        // set financial year options in the schema
        $AccountSetTracker = $this->action('datasource:AccountSet', $this->pointer, $this->input);
        // create a foreach loop for set financial year options to the schema
        $years = [];
        foreach ($AccountSetTracker as $value) {
            $years[$value['fy_start_date'] . ' to ' . $value['fy_end_date']] = $value['fy_start_date'] . ' to ' . $value['fy_end_date'];
        }

        $this->data = $this->format($result->toArray());
        $this->meta['schema'] = $this->schema;
        $this->meta['schema']['table']['filter_by']['financial_year']['options'] = $years;
        $this->meta['pagination']['total'] = $count;
    }

    public function add($a, $b)
    {
        return (float)$a + (float)$b;
    }

    public function subtract($vendor_bill_amount, $vendor_bill_payment_amount, $vendor_bill_tds, $write_off, $payment_status)
    {
        if ($write_off > 0) {
            $write_off = (float) $write_off;
        } else {
            $write_off = 0;
        }

        $amount = (float) $vendor_bill_amount - (float) $write_off ;
        if($payment_status=='paid') {
            $amount = 0;
        } else {
            $amount = (float) $vendor_bill_amount - (float) $vendor_bill_payment_amount - (float) $write_off; //  - (float) $vendor_bill_tds
        }
        // return '₹ ' . number_format($amount, 2);
        return $amount;
    }

    public function setRupees($value)
    {
        $value = (float) $value;
        // return '₹ ' . number_format($value, 2);
        return $value;
    }
}
