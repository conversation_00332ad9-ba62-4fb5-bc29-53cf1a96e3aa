<?php

namespace App\Console\Commands\Actions\Expenses;

use Illuminate\Console\Command;
use App\Console\Commands\Action;

class VendorPerticularsDetailsDataSource extends Action
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'datasource:VendorPerticularsDetails {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Get the Vendor Perticulars Details by Id';

    protected $formatter = [
        'id' => '',
        'et_id' => 'setNA:et_id',
        'et_type_name' => 'setNA:et_type_name',
        'amount' => 'getAmountPaid:amount',
        'particular' => 'setNA:particular',
        'hsn_sac' => 'setNA:hsn_sac',
        'total_amount' => 'getAmountPaid:total_amount',
    ];

    protected $formatterByKeys = ['id'];

    protected $mapper = [
        'id' => 'chsone_vendor_bill_particulars.bill_id',
    ];

    /**
     * Execute the console command.
     */
    public function apply()
    {
        $VendorBillId = $this->input['vendor_bill_id'];

        // add pagination
        $page = $this->input['page'] ?? 1;
        $per_page = !empty($this->input['per_page']) ? $this->input['per_page']: ($this->hugeData ? 100000 : 10);
        $offset = ($page - 1) * $per_page;

        $obj = $this->tenantDB()->table('chsone_vendor_bill_particulars as particulars')
        ->select(
            'bill_id as id',
            'particulars.et_id',
            'et_type_name',
            'amount',
            'particular',
            'hsn_sac',
            'total_amount'
        )
        ->leftJoin('chsone_expense_tracker as expenseTracker', 'particulars.et_id', '=', 'expenseTracker.et_id')
        ->where('bill_id', $VendorBillId);

        $obj = $this->filter($obj);
        
        // add count and pagination
        $count = $obj->count();

        $obj = $obj->offset($offset);
        $obj = $obj->limit($per_page);

        $result = $obj->get();
        $this->data = $this->format($result->toArray());
        $this->meta['pagination']['total']=$count;
    }

    public function getAmountPaid($payment_amount)
    {
        $payment_amount = (float) $payment_amount;

        if(empty($payment_amount)) {
            return '₹ ' . number_format(0, 2);
        }

        // return '₹ ' . number_format($payment_amount, 2);
        return $payment_amount;
    }

    public function setNA($value)
    {
        if($value === null || $value === ''){
            return 'N/A';
        }else{
            return $value;
        }
    }
}
