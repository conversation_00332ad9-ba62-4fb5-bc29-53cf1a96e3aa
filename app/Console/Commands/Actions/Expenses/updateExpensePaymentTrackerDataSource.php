<?php

namespace App\Console\Commands\Actions\Expenses;

use Illuminate\Console\Command;
use App\Console\Commands\Action;

class updateExpensePaymentTrackerDataSource extends Action
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'datasource:updateExpensePaymentTracker {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Update the expense payment tracker.';

    /**
     * Execute the console command.
     */
    public function apply()
    {
        $id = $this->input['id'];

        // check expense_id is exist or not in expense_invoice_payment_tracker table if not exist then return error
        $expenseDetails = $this->tenantDB()->table('expense_invoice_payment_tracker')
            ->where('id', $id)
            ->get();

        if(count($expenseDetails) == 0){
            $this->status = 'error';
            $this->message = 'No record found for this id';
            $this->statusCode = 400;
            $this->data = [];
            return;
        }

        if(isset($this->input['bank_id']) && $this->input['bank_id'] != '') {
            // get other_information from expenseDetails
            $otherInformation = unserialize($expenseDetails[0]->other_information);
            // set bank_id in bank_ledger in other_information
            $otherInformation['bank_ledger'] = $this->input['bank_id'];
            // pass this to another variable to update in expense_invoice_payment_tracker table
            $this->input['other_information'] = serialize($otherInformation);
        }

        // update the status in expense_payment_tracker table
        $result = $this->tenantDB()->table('expense_invoice_payment_tracker')
        ->where('id', $id)
        ->update([
            'payment_date' => $this->input['payment_date'],
            'payment_number' => $this->input['payment_number'] ?? '',
            'payment_amount' => $this->input['payment_amount'],
            'writeoff_amount' => $this->input['writeoff_amount'] ?? 0,
            'payment_mode' => $this->input['payment_mode'],
            'other_information' => $this->input['other_information'] ?? '',
            // if payment mode is cheque then update the cheque number and cashtransfer then update the reference number
            // 'transaction_reference' => $this->input['cheque_number'] ?? $this->input['reference_number'],
            'transaction_reference' => $this->input['transaction_reference'] ?? '',
            'payment_note' => $this->input['comments'] ?? '',
            'updated_date' => date('Y-m-d 00:00:00'),
        ]);

        if($result) {
            $this->status = 'success';
            $this->message = 'Expense payment tracker updated successfully';
            $this->statusCode = 200;
            $this->data = [];
        } else {
            $this->status = 'error';
            $this->message = 'Expense payment tracker not updated';
            $this->statusCode = 400;
            $this->data = [];
        }
    }
}
