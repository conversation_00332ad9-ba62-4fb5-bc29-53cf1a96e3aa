<?php

namespace App\Console\Commands\Actions\Expenses;

use App\Console\Commands\Action;

class VendorAdvancesEditDataSource extends Action
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'datasource:vendorAdvancesEdit {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Add a new miscellaneous expense to the system.';

    /**
     * Execute the console command.
     */
    public function apply()
    {
        $id = $this->input['id'];
        $use_credit = $this->input['use_credit'];
        $use_credit_after = $this->input['use_credit_after'];
        $user_id = $this->input['user_id'];

        $update_credit = [
            'use_credit' => $use_credit,
            'updated_by' => $user_id,
            'updated_date' => date('Y-m-d H:i:s')
        ];

        if (!is_null($use_credit_after)) {
            $update_credit['use_credit_after'] = $use_credit_after;
        }

        $updateResult = $this->tenantDB()->table('chsone_credit_accounts')
            ->where('credit_account_id', $id)
            ->update($update_credit);

        if ($updateResult) {
            $this->status = 'success';
            $this->message = 'Credit account updated successfully.';
            $this->statusCode = 200;
            $this->data = [];
        } else {
            $this->status = 'error';
            $this->message = 'Failed to update credit account.';
            $this->statusCode = 400;
            $this->data = [];
        }
    }

}
