<?php

namespace App\Console\Commands\Actions\Expenses;

use Illuminate\Console\Command;
use App\Console\Commands\Action;

class ExpensePaymentTrackerDataSource extends Action
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'datasource:expensePaymentTrackerList {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Get the Expense Payment Tracker List';

    protected $formatter = [
        'id' => '',
        'soc_id' => '',
        'vendor_id' => '',
        'invoice_number' => '',
        'payment_number' => '',
        'bill_type' => '',
        'payment_mode' => '',
        'transaction_reference' => '',
        'payment_instrument' => '',
        'paid_by' => '',
        'total_due_amount' => '',
        'writeoff_amount' => '',
        "payment_amount" => "getAmountPaid:payment_amount,writeoff_amount",
        'other_information' => '',
        'status' => '',
        'transaction_status' => '',
        'payment_token' => '',
        'payment_note' => '',
        'reversal_note' => '',
        'payment_date' => '',
        'cheque_date' => '',
        'created_date' => '',
        'vendor_name' => '',
    ];

    protected $formatterByKeys = ['id'];

    protected $mapper = [
        'id' => 'vendorpayment.id',
    ];

    /**
     * Execute the console command.
     */
    public function apply()
    {
        $paymentNumber = '';
        $voucherNumber = '';
        $paymentDate = '';
        $paymentReference = '';
        $status = '';
        $paymentMode = '';
        $vendorName = '';
        $paymentMode = '';
        $sortBy = '';
        $sortByKey = '';
        $sortByValue = '';
        $searchTerm = $this->input['filters']['search'] ?? '';

        if (!empty($this->input['filters'])) {
            $paymentNumber =
                !empty($this->input['filters']['payment_number']) ? $this->input['filters']['payment_number'] : '';
            unset($this->input['filters']['payment_number']);

            $voucherNumber =
                !empty($this->input['filters']['invoice_number']) ? $this->input['filters']['invoice_number'] : '';
            unset($this->input['filters']['invoice_number']);

            $paymentDate =
                !empty($this->input['filters']['payment_date']) ? $this->input['filters']['payment_date'] : '';
            unset($this->input['filters']['payment_date']);

            $paymentReference =
                !empty($this->input['filters']['transaction_reference']) ? $this->input['filters']['transaction_reference'] : '';
            unset($this->input['filters']['transaction_reference']);

            $vendorName =
                !empty($this->input['filters']['vendor_name']) ? $this->input['filters']['vendor_name'] : '';
            unset($this->input['filters']['vendor_name']);

            $paymentMode = isset($this->input['filters']['payment_mode']) ? explode(',', $this->input['filters']['payment_mode']) : [];


            $status = isset($this->input['filters']['status']) ? explode(',', $this->input['filters']['status']) : [];

        }

        // add pagination
        $page = $this->input['page'] ?? 1;
        $per_page = !empty($this->input['per_page']) ? $this->input['per_page']: ($this->hugeData ? 100000 : 10);
        $offset = ($page - 1) * $per_page;

        $obj = $this->tenantDB()->table('expense_invoice_payment_tracker AS vendorpayment')
            ->select([
                'vendorpayment.id',
                'vendorpayment.soc_id',
                'vendorpayment.vendor_id',
                'vendorpayment.invoice_number',
                'vendorpayment.payment_number',
                'vendorpayment.bill_type',
                'vendorpayment.payment_mode',
                'vendorpayment.transaction_reference',
                'vendorpayment.payment_instrument',
                'vendorpayment.paid_by',
                'vendorpayment.total_due_amount',
                'vendorpayment.writeoff_amount',
                'vendorpayment.payment_amount',
                'vendorpayment.other_information',
                'vendorpayment.status',
                'vendorpayment.transaction_status',
                'vendorpayment.payment_token',
                'vendorpayment.payment_note',
                'vendorpayment.reversal_note',
                'vendorpayment.payment_date',
                'vendorpayment.cheque_date',
                'vendorpayment.created_date',
                'vendor.vendor_name'
            ])
            ->leftJoin('chsone_vendors_master AS vendor', 'vendor.vendor_id', '=', 'vendorpayment.vendor_id')
            ->whereNotIn('vendorpayment.status', ['N', 'reversed'])
            ->orderBy('vendorpayment.id', 'desc');

        if ($paymentNumber) {
            $obj = $obj->where('vendorpayment.payment_number', 'like', '%' . $paymentNumber . '%');
        }

        if ($voucherNumber) {
            $obj = $obj->where('vendorpayment.invoice_number', 'like', '%' . $voucherNumber . '%');
        }

        if ($paymentDate) {
            $obj = $obj->where('vendorpayment.payment_date', 'like', '%' . $paymentDate . '%');
        }

        if ($paymentReference) {
            $obj = $obj->where('vendorpayment.transaction_reference', 'like', '%' . $paymentReference . '%');
        }

        if ($vendorName) {
            $obj = $obj->where('vendor.vendor_name', 'like', '%' . $vendorName . '%');
        }

        if ($paymentMode) {
            $obj = $obj->whereIn('vendorpayment.payment_mode', $paymentMode);
        }

        if ($status) {
            $obj = $obj->whereIn("vendorpayment.status", $status);
        }

        if (!empty($this->input['sort'])) {
            $sortBy = $this->input['sort'];
            $sortByKey = array_key_first($sortBy);
            $sortByValue = $sortBy[$sortByKey];
            unset($this->input['sort']);
        }

        if ($sortBy) {
            $obj = $obj->orderBy($sortByKey, $sortByValue);
        }

        $columns = [
            'vendorpayment.payment_mode',
            'vendorpayment.payment_number',
            'vendorpayment.invoice_number',
            'vendorpayment.transaction_reference',
            'vendor.vendor_name',
            'vendorpayment.payment_amount',
        ];

        if ($searchTerm) {
            $obj->where(function ($q) use ($columns, $searchTerm) {
                foreach ($columns as $column) {
                    $q->orWhere($column, 'LIKE', '%' . $searchTerm . '%');
                }
            });
        }

        // add count and pagination
        $count = $obj->count();

        $obj = $obj->offset($offset);
        $obj = $obj->limit($per_page);

        $result = $obj->get();

        $this->data = $this->format($result->toArray());
        $this->meta['pagination']['total']=$count;
    }

    public function getAmountPaid($payment_amount, $writeoff_amount)
    {
        $writeoff_amount = (float) $writeoff_amount;
        $payment_amount = (float) $payment_amount;

        if(empty($writeoff_amount)){
            //return '₹ ' . number_format($payment_amount, 2);
            return $payment_amount;
        }

        //return '₹ ' . number_format($payment_amount, 2). '('.$writeoff_amount.')';
        return $payment_amount;
    }
}
