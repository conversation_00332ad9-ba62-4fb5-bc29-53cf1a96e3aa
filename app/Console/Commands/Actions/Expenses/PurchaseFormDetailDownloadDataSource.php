<?php

namespace App\Console\Commands\Actions\Expenses;

use App\Console\Commands\Action;
use Illuminate\Support\Facades\DB;

class PurchaseFormDetailDownloadDataSource extends Action
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'datasource:purchaseFormDetailDownload {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Purchase Form Detail Data Source';


    /**
     * Execute the console command.
     */
    public function apply()
    {
        $soc_id = (int) $this->input["company_id"];
        $purchase_form_id = $this->input["id"];

        $query = $this->tenantDB()->table('chsone_purchase_form')
        ->selectRaw(
            'purchase_form_vendor_id,
            purchase_form_id,
            soc_id,
            purchase_form_title,
            purchase_form_desc,
            purchase_form_type,
            purchase_form_amount,
            purchase_form_currency,
            purchase_form_po_number,
            purchase_form_approvals,
            purchase_form_email_sent_approver,
            purchase_form_email_sent_vendor,
            purchase_form_vendor_name,
            purchase_form_ledger_name,
            purchase_form_num_approvals_needed,
            added_on,
            updated_on,
            status'
        )
        ->where('soc_id', $soc_id)->where('purchase_form_id', $purchase_form_id);
        $result = $query->first();

        if ($result == null) {
            $this->status = 'error';
            $this->message = 'Unable to Fetch Purchase Form Data';
            $this->statusCode = 400;
            return;
        }

        $purchase_form_items = $this->tenantDB()->table('chsone_purchase_form_items')->selectRaw('item_name, item_quantity, item_cost, (item_quantity * item_cost) as total_cost')->where('soc_id', $soc_id)->where('purchase_form_id', $purchase_form_id)->get();
        $vendor_data = $this->tenantDB()->table('chsone_vendors_master')->where('soc_id', $soc_id)->where('vendor_id', $result->purchase_form_vendor_id)->first();
        $result->purchase_form_num_approvals_needed = unserialize($result->purchase_form_num_approvals_needed);

        $user_ids = $result->purchase_form_num_approvals_needed;

        if(!is_array($user_ids)){
            $user_ids = [$user_ids];
        }

        $approver_name = $this->masterDB()->table('chsone_users_master')
            ->whereIn('user_id', $user_ids)
            ->pluck('user_first_name') // Pluck user_first_name field values
            ->implode(', '); // Implode values with comma separator

        $result->vendor_email = $vendor_data->vendor_email;
        $result->approver_name = $approver_name;
        $result->purchase_form_items = $purchase_form_items;

        $society = $this->masterDB()->table('chsone_societies_master')->select('soc_name', 'soc_address_1','soc_address_2','soc_landmark','soc_city_or_town','soc_state','soc_pincode')->where('soc_id', $soc_id)->first();
        $result->soc_address = $society->soc_address_1 . ', ' . $society->soc_address_2 . ', ' . $society->soc_landmark . ', ' . $society->soc_city_or_town . ', ' . $society->soc_state . ', ' . $society->soc_pincode.'.';
        $result->soc_address = str_replace(', ,', ',', $result->soc_address);
        $result->soc_address = str_replace(' ,', ',', $result->soc_address);

        $result->soc_name = $society->soc_name;
        $this->data = $result;
    }
}
