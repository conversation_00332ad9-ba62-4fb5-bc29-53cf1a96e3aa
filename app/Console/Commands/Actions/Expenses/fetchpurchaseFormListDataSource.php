<?php

namespace App\Console\Commands\Actions\Expenses;

use Illuminate\Console\Command;
use App\Console\Commands\Action;

class fetchpurchaseFormListDataSource extends Action
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'datasource:fetchpurchaseFormList {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Get the Purchase List';

    protected $formatter = [
        'id' => '',
        'purchase_form_title' => '',
        'purchase_form_amount' => '',
        'purchase_form_currency' => '',
        'purchase_form_po_number' => '',
        'purchase_form_vendor_name' => '',
        'status' => 0,
        'reject_by_name' => '',
        'approval_pending_by_name' => '',
        'approved_by' => '',
        'refused_by_name' => '',
        'reviewed_pending_by_name' => '',
        'reviewed_by' => '',
        'disable' => ''
    ];

    protected $formatterByKeys = ['id'];

    protected $mapper = [
        'id' => 'chsone_purchase_form.purchase_form_id',
    ];

    /**
     * Execute the console command.
     */
    public function apply()
    {
        // fetch vendor_id from input
        $vendor_id = $this->input['vendor_id'] ?? null;

        // check vendor_id is valid or not from chsone_vendor_master table
        $vendor = $this->tenantDB()->table('chsone_vendors_master')
            ->where('vendor_id', $vendor_id)
            ->first();

        if (!$vendor) {
            $this->status = 'error';
            $this->statusCode = 400;
            $this->message = 'Vendor not found or Invalid vendor id';
            return;
        }

        $page = $this->input['page'] ?? 1;
        $per_page = !empty($this->input['per_page']) ? $this->input['per_page'] : ($this->hugeData ? 100000 : 10);

        $offset = ($page - 1) * $per_page;

        $user_id = $this->input['user_id'];
        $obj = $this->tenantDB()->table('chsone_purchase_form')
            ->select(
                'purchase_form_id as id',
                'purchase_form_title',
                'purchase_form_amount',
                'purchase_form_currency',
                'purchase_form_po_number',
                'purchase_form_vendor_name',
                'status'
            )
            ->where('soc_id', $this->input['company_id'])
            ->where('status', '=', 3)
            ->where('purchase_form_type', 'PO')
            ->where('purchase_form_vendor_id', $vendor_id)
            ->orderBy('purchase_form_id', 'desc')
            ->get();

        foreach ($obj as $index => $item) {
            $disable = "true";
            // Initialize fields
            $item->approval_pending_by_name = '';
            $item->approved_by = '';
            $item->reject_by_name = '';
            $item->reviewed_pending_by_name = '';
            $item->reviewed_by = '';
            $item->refused_by_name = '';

            // Process approver and reviewer responses
            $approver_response = $this->processApproverData($item->id);
            $reviewer_response = $this->processReviewerData($item->id);
            $reviewer_data = $this->tenantDB()->table('purchase_order_reviewers')
                ->where('purchase_order_id', $item->id)
                ->get();


            $reviewer_data_user_ids = $reviewer_data->pluck('user_id')->toArray();

            $approver_data = $this->tenantDB()->table('purchase_order_approvers')
                ->where('purchase_order_id', $item->id)
                ->get();

            //collect all user_id from purchase_order_approvers
            $approver_data_user_ids = $approver_data->pluck('user_id')->toArray();

            if (in_array($user_id, $approver_data_user_ids) || in_array($user_id, $reviewer_data_user_ids)) {
                $disable = "false";
            }

            if ($approver_response) {
                foreach ($approver_response as $key => $value) {
                    $item->$key = $value;
                }
            }

            // Update item with reviewer response
            if ($reviewer_response) {
                foreach ($reviewer_response as $key => $value) {
                    $item->$key = $value;
                }
            }

            // Update the object in the array
            $obj[$index] = $item;
            $obj[$index]->disable = $disable;


            switch ($item->status) {
                case 0:
                    $item->status = 'Approval Pending';
                    break;
                case 1:
                    $item->status = 'Approved';
                    break;
                case 2:
                    $item->status = 'Reviewed';
                    if (!empty($item->reject_by_name) || ($item->reject_by_name == "Action By All")) { //if ($item->reject_by_name == "Action By All")
                        $item->status = 'Rejected';
                    }
                    if (!empty($item->reject_by_name) && ($item->reject_by_name == "Action By All")) { //if ($item->reject_by_name == "Action By All")
                        $item->status = 'Aprroved';
                    }
                    break;
                case 3:
                    $item->status = 'Approval Pending';
                    if (!empty($item->approval_pending_by_name) && ($item->approval_pending_by_name == "Action By All")) { //if ($item->reject_by_name == "Action By All")
                        $item->status = 'In Progress';
                    }
                    break;
                case 4:
                    $item->status = 'Reviewed';
                    break;
                case '0':
                    $item->status = 'Inactive';
                    break;
                case '1':
                    $item->status = 'Active';
                    break;
                case 'unpaid':
                    $item->status = 'Unpaid';
                    break;
                case 'paid':
                    $item->status = 'Paid';
                    break;
                case 'Partialpaid':
                    $item->status = 'Partial Paid';
                    break;
                default:
                    // No match found, retain original status
                    $item->status = $item->status;
            }
        }


        $this->data = $obj;
    }
    public function processApproverData($id)
    {
        $response = $this->tenantDB()->table('purchase_order_approvers')
            ->where('purchase_order_id', $id)
            ->get();

        //collect all user_id from purchase_order_approvers
        $user_ids = $response->pluck('user_id')->toArray();

        $new_response[] = [
            "user_ids" => $user_ids
        ];
        $all_one = [];

        foreach ($response as $approver) {
            $all_one[] = $approver->status;
            $user_data = $this->getUserData($approver->user_id);

            if ($approver->status == 2) {
                if (!empty($user_data->user_first_name)) {
                    $new_response[] = [
                        'reject_by_name' => $user_data->user_first_name
                    ];
                } else {
                    $new_response[] = [
                        'reject_by_name' => ""
                    ];
                }
            } else if ($approver->status == 0) {

                if (!empty($user_data->user_first_name)) {
                    $new_response[] = [
                        'approval_pending_by_name' => $user_data->user_first_name,
                        'approved_by' => 'No one has approved this PO.'
                    ];
                } else {
                    $new_response[] = [
                        'approval_pending_by_name' => ""
                    ];
                }
            } else if ($approver->status == 1) {

                if (!empty($user_data->user_first_name)) {
                    $new_response[] = [
                        'approved_by' => $user_data->user_first_name
                    ];
                } else {
                    $new_response[] = [
                        'approved_by' => ""
                    ];
                }
            }
        }

        if (
            collect($all_one)->every(function ($status) {
                return $status === 1;
            })
        ) {
            $new_response[] = [
                'approval_pending_by_name' => "Action By All"
            ];
        }
        $new_response =   $this->mergePurchasedDetail($new_response) ?? [];

        return $new_response;
    }

    public function processReviewerData($id)
    {
        $response = $this->tenantDB()->table('purchase_order_reviewers')
            ->where('purchase_order_id', $id)
            ->get();

        if (count($response) == 0) {
            return [];
        }

        $user_ids = $response->pluck('user_id')->toArray();

        $new_response[] = [
            "user_ids" => $user_ids
        ];

        $all_one = [];
        $reviewed_by_set = false;  // Flag to check if 'reviewed_by' is already set
        $reviewed_by_value = 'No one has approved this PO';  // Default value

        foreach ($response as $reviewer) {
            $all_one[] = $reviewer->status;
            $user_data = $this->getUserData($reviewer->user_id);
            if ($reviewer->status == 2) {
                $new_response[] = [
                    'refused_by_name' => !empty($user_data->user_first_name) ? $user_data->user_first_name : ""
                ];
            } else if ($reviewer->status == 0) {
                $new_response[] = [
                    'reviewed_pending_by_name' => !empty($user_data->user_first_name) ? $user_data->user_first_name : "",
                    'reviewed_by' => $reviewed_by_set ? null : $reviewed_by_value
                ];
                $reviewed_by_set = true;  // Set the flag to true

            } else if ($reviewer->status == 1) {
                // Set reviewed_by only if it hasn't been set already
                if (!$reviewed_by_set) {
                    $new_response[] = [
                        'reviewed_by' => !empty($user_data->user_first_name) ? $user_data->user_first_name : ""
                    ];
                    $reviewed_by_set = true;  // Set the flag to true
                }
            }
        }

        // Check if all reviewers have status 1
        if (collect($all_one)->every(function ($status) {
            return $status == 1;
        })) {
            $new_response[] = [
                'reviewed_pending_by_name' => "Action performed by all"
            ];
        }

        // Clean up extra commas and spaces

        $new_response = $this->mergePurchasedDetail($new_response) ?? [];
        if (isset($new_response['reviewed_pending_by_name'])) {
            $new_response['reviewed_pending_by_name'] = trim(preg_replace('/\s*,\s*(,*)$/', '', $new_response['reviewed_pending_by_name']), ', ');
        }

        if (isset($new_response['reviewed_by'])) {
            $new_response['reviewed_by'] = trim(preg_replace('/\s*,\s*(,*)$/', '', $new_response['reviewed_by']), ', ');
        }

        return $new_response;
    }

    public function getUserData($user_id)
    {
        return $this->masterDB()->table('chsone_users_master')
            ->select('user_id', 'user_first_name', 'user_last_name')
            ->where('user_id', $user_id)
            ->first();
    }

    public function mergePurchasedDetail($data)
    {
        $merged = [];

        foreach ($data as $item) {
            unset($item['user_ids']);
            $singleItem = count($item) === 1;

            foreach ($item as $key => $value) {
                if ($key == 'user_ids') {
                    continue;
                }
                if (is_array($value)) {
                    if (!isset($merged[$key])) {
                        $merged[$key] = $value;
                    } else {
                        $merged[$key] = array_merge($merged[$key], $value);
                    }
                } else {
                    if (!isset($merged[$key])) {
                        $merged[$key] = $value;
                    } else {
                        if ($key === 'approved_by' && $value === "No one has approved this PO.") {
                            // Ensure "No one has approved this PO." is not concatenated
                            if ($merged[$key] !== "No one has approved this PO.") {
                                $merged[$key] = "No one has approved this PO.";
                            }
                        } else {
                            if (!$singleItem) {
                                $merged[$key] .= ", " . $value;
                            }
                        }
                    }
                }
            }
        }
        return $merged;
    }
}


// pof  poa por remark
// 0	0	0	approval pending
// 1    1   x   approved
// 2	2	0	reject //once approve cant reject
// 3	1	0	inprogess
// 4	1	1	review //once review can refuse
// 5	1	2	refuse//we can refuse after approve ,without approve we can't  refuse and after review we cant refuse
