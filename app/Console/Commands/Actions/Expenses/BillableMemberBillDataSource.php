<?php

namespace App\Console\Commands\Actions\Expenses;

use Illuminate\Console\Command;
use App\Console\Commands\Action;

class BillableMemberBillDataSource extends Action
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'datasource:BillableMemberBill {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Get the billable member bill';

    protected $formatter = [
        'id' => '',
        'vendor_bill_type_purchase' => '',
        'vendor_bill_num' => '',
        'vendor_bill_date' => '',
        'vendor_bill_amount' => '',
        'vendor_bill_tds' => '',
        'vendor_bill_writeoff' => '',
        'vendor_bill_due_date' => '',
        'is_billable' => '',
        'is_billed' => '',
        'is_rcm' => '',
        'status' => '',
        'payment_status' => '',
        'vendor_id' => '',
        'vendor_name' => '',
        'write_off' => '',
        'vendor_bill_payment_amount' => '',
        'tds' => '',
        // 'vendor_bill_payment_amount_final' => 'getPaymentFinalAmountSum:vendor_bill_payment_amount,tds,write_off',
        // 'vendor_bill_amount_final' => 'getBillFinalAmountSum:vendor_bill_amount,vendor_bill_tds',
        'vendor_bill_payment_amount_final' => '',
        'vendor_bill_amount_final' => '',
        'vendor_bill_due_amount' => 'subtract:vendor_bill_amount_final,vendor_bill_payment_amount_final',
    ];

    protected $formatterByKeys = ['id'];

    protected $mapper = [
        'id' => 'cvbm.vendor_bill_id',
    ];

    /**
     * Execute the console command.
     */
    public function apply()
    {
        $vendorName = '';
        $vendorBillNo = '';
        $isBill = '';
        $purchaseMode = '';
        $searchTerm = $this->input['filters']['search'] ?? '';

        if (!empty($this->input['filters'])) {
            $vendorName =
                !empty($this->input['filters']['vendor_name']) ? $this->input['filters']['vendor_name'] : '';
            unset($this->input['filters']['vendor_name']);

            $vendorBillNo =
                !empty($this->input['filters']['vendor_bill_num']) ? $this->input['filters']['vendor_bill_num'] : '';
            unset($this->input['filters']['vendor_bill_num']);

            $isBill = isset($this->input['filters']['is_billed']) ? explode(',', $this->input['filters']['is_billed']) : [];


            $purchaseMode = isset($this->input['filters']['vendor_bill_type_purchase']) ? explode(',', $this->input['filters']['vendor_bill_type_purchase']) : [];
        }

        // add pagination
        $page = $this->input['page'] ?? 1;
        $per_page = !empty($this->input['per_page']) ? $this->input['per_page']: ($this->hugeData ? 100000 : 10);
        $offset = ($page - 1) * $per_page;

        $obj = $this->tenantDB()->table('chsone_vendor_bill_master as cvbm')
        ->select(
            'cvbm.vendor_bill_id AS id',
            'vendor_bill_type_purchase',
            'vendor_bill_num',
            'vendor_bill_date',
            'vendor_bill_amount',
            'vendor_bill_tds',
            'vendor_bill_writeoff',
            'vendor_bill_due_date',
            'is_billable',
            'is_billed',
            'is_rcm',
            'cvbm.status',
            'payment_status',
            'cvbm.vendor_id',
        )
        ->leftJoin('chsone_vendors_master as cvm', 'cvbm.vendor_id', '=', 'cvm.vendor_id')
        ->where('cvbm.status', 1)
        ->where('cvbm.is_billable', 1)
        ->orderByDesc('vendor_bill_date')
        ->selectRaw("IFNULL(is_billed, 0) AS is_billed")
        ->selectRaw("IFNULL(vendor_name, 'No Vendor') AS vendor_name")
        ->selectRaw("
            IFNULL(
                (
                    SELECT IF(SUM(cvbpd.write_off) = 0, 0, SUM(cvbpd.write_off))
                    FROM chsone_vendor_bill_payment_details AS cvbpd
                    WHERE cvbpd.vendor_bill_id = cvbm.vendor_bill_id
                ), 0
            ) AS write_off
        ")
        ->selectRaw("
            IFNULL(
                (
                    SELECT IF(SUM(cvbpd.vendor_bill_payment_amount) = 0, 0, SUM(cvbpd.vendor_bill_payment_amount))
                    FROM chsone_vendor_bill_payment_details AS cvbpd
                    WHERE cvbpd.vendor_bill_id = cvbm.vendor_bill_id
                ), 0
            ) AS vendor_bill_payment_amount
        ")
        ->selectRaw("
            IFNULL(
                (
                    SELECT IF(SUM(cvbpd.tds) = 0, 0, SUM(cvbpd.tds))
                    FROM chsone_vendor_bill_payment_details AS cvbpd
                    WHERE cvbpd.vendor_bill_id = cvbm.vendor_bill_id
                ), 0
            ) AS tds
        ")
        ->selectRaw('cvbm.vendor_bill_amount + cvbm.vendor_bill_tds as vendor_bill_amount_final')
        ->selectRaw('
            (
                SELECT IF(SUM(cvbpd.vendor_bill_payment_amount + cvbpd.tds) = 0, 0, SUM(cvbpd.vendor_bill_payment_amount + cvbpd.tds))
                FROM chsone_vendor_bill_payment_details AS cvbpd
                WHERE cvbpd.vendor_bill_id = cvbm.vendor_bill_id
            ) AS vendor_bill_payment_amount_final
        ');


        if ($vendorName) {
            $obj = $obj->whereRaw("LOWER(cvm.vendor_name) LIKE '%{$vendorName}%'");
        }

        if ($vendorBillNo) {
            $obj = $obj->whereRaw("LOWER(vendor_bill_num) LIKE '%{$vendorBillNo}%'");
        }

        if ($purchaseMode) {
            $obj = $obj->whereIn("vendor_bill_type_purchase", $purchaseMode);
        }

        if (!empty($isBill)) {
            $obj = $obj->whereIn("is_billed", $isBill);
        }
        // else {
        //     $obj = $obj->whereIn('is_billed', [0, 1]);
        // }


        $columns = [
            'cvbm.vendor_bill_id',
            'vendor_bill_type_purchase',
            'vendor_bill_num',
            'vendor_bill_date',
            'vendor_bill_amount',
            'vendor_bill_tds',
            'vendor_bill_writeoff',
            'vendor_bill_due_date',
            'is_billable',
            'is_billed',
            'is_rcm',
            'cvbm.status',
            'payment_status',
            'cvbm.vendor_id',
            'vendor_name'
        ];

        if ($searchTerm) {
            $obj->where(function ($q) use ($columns, $searchTerm) {
                foreach ($columns as $column) {
                    $q->orWhere($column, 'LIKE', '%' . $searchTerm . '%');
                }
            });
        }

        // add count and pagination
        $count = $obj->count();

        $obj = $obj->offset($offset);
        $obj = $obj->limit($per_page);

        $result = $obj->get();

        $this->data = $this->format($result->toArray());
        $this->meta['pagination']['total'] = $count;
    }

    public function subtract($vendor_bill_amount, $vendor_bill_payment_amount_final)
    {
        // return (float)$vendor_bill_amount - (float)$vendor_bill_payment_amount_final;
        $amount = (float) $vendor_bill_amount - (float) $vendor_bill_payment_amount_final;
        // return '₹ ' . number_format($amount, 2);
        return $amount;
    }

    public function getPaymentFinalAmountSum($vendor_bill_payment_amount, $tds, $write_off)
    {
        // return (float)$vendor_bill_payment_amount + (float)$tds;
        $amount = (float) $vendor_bill_payment_amount + (float) $tds;
        $write_off = (float) $write_off;
        // return '₹ ' . number_format($amount, 2) . '(' . number_format($write_off, 2) . ')';
        return $amount;

    }

    public function getBillFinalAmountSum($vendor_bill_amount, $vendor_bill_tds)
    {
        $amount = (float) $vendor_bill_amount + (float) $vendor_bill_tds;
        // return '₹ ' . number_format($amount, 2);
        return $amount;
    }
}
