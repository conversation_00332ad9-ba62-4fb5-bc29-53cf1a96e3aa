<?php

namespace App\Console\Commands\Actions\Expenses;

use Illuminate\Console\Command;
use App\Console\Commands\Action;

class VendorDetailsDataSource extends Action
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'datasource:VendorDetails {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Get the Vendor Details by Id';

    protected $schema = [];

    protected $formatter = [
        'id' => '',
        'vendor_bill_id' => '',
        'vendor_id' => '',
        'et_id' => '',
        'type_of_expense' => '',
        'vendor_name' => '',
        'vendor_bill_type_purchase' => '',
        'vendor_bill_num' => '',
        'vendor_bill_date' => '',
        'vendor_bill_amount' => '',
        'vendor_bill_tds' => '',
        'vendor_bill_due_date' => '',
        'payment_status' => '',
        'is_rcm' => '',
        'is_billable' => '',
        'is_billed' => '',
        'vendor_bill_soft_copy_path' => '',
        'advance_consumed' => '',
        'total_tax' => '',
        'paid_amount' => '',
        'due_amount' => 'getDueAmount:vendor_bill_amount,paid_amount',
    ];

    protected $formatterByKeys = ['id'];

    protected $mapper = [
        'id' => 'cvbm.vendor_bill_id',
    ];

    /**
     * Execute the console command.
     */
    public function apply()
    {
        $VendorDetailId = $this->input['vendor_bill_id'];

        $obj = $this->tenantDB()->table('chsone_vendor_bill_master as cvbm')
            ->select('cvbm.vendor_bill_id as id', 'cvbm.vendor_bill_id','cvbm.vendor_id', 'cvbm.et_id', 'et_type_name as type_of_expense', 'vendor_bill_type_purchase', 'vendor_bill_num', 'vendor_bill_date', 'vendor_bill_amount', 'vendor_bill_tds', 'vendor_bill_due_date', 'payment_status', 'is_rcm', 'is_billable', 'is_billed', 'vendor_bill_soft_copy_path', 'advance_consumed','total_tax')
            ->leftJoin('chsone_vendors_master as cvm', 'cvbm.vendor_id', '=', 'cvm.vendor_id')
            ->leftJoin('chsone_vendor_bill_particulars as cvbp', 'cvbm.vendor_bill_id', '=', 'cvbp.bill_id')
            ->leftJoin('chsone_expense_tracker as expense', 'cvbm.et_id', '=', 'expense.et_id')
            ->selectRaw('ifnull(advance_consumed, 0) as advance_consumed')
            ->selectRaw('SUM(cvbp.total_tax) as total_tax')
            ->selectRaw('SUM(cvbp.total_tax) as total_tax')
            ->where(function ($obj) {
                $obj->whereIn('is_billable', [0, 1])
                    ->where('cvbm.status', 1)
                    ->where(function ($obj) {
                        $obj->whereNull('is_billed')
                            ->orWhere('is_billed', 1);
                    })
                    ->orWhereIn('is_rcm', [0, 1]);
            })
            ->where('cvbm.vendor_bill_id', $VendorDetailId)
            ->groupBy(
                'cvbp.bill_id'
            )
            ->orderByDesc('vendor_bill_date')
            ->selectRaw('ifnull(vendor_name, "Petty Cash") as vendor_name');

        $obj = $this->filter($obj);

        $result = $obj->get();
        
        // create a query to caluclate the sum of vendor_bill_payment_amount from chsone_vendor_bill_payment_details and pass into the new variable in the $result array
        $totalQuery = "SELECT
            SUM(vendor_bill_payment_amount) as paid_amount
            FROM chsone_vendor_bill_payment_details
            WHERE vendor_bill_id = $VendorDetailId";

        $total = $this->tenantDB()->select($totalQuery);
        $result[0]->paid_amount = $total[0]->paid_amount ?? 0;
                //dd($result);
        $this->data = $this->format($result->toArray());
    }

    public function getDueAmount($vendor_bill_amount, $paid_amount)
    {
        return $vendor_bill_amount - (float)$paid_amount;
    }
}
