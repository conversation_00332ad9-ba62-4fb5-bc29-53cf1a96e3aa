<?php

namespace App\Console\Commands\Actions\Expenses;

use Illuminate\Console\Command;
use App\Console\Commands\Action;

class paymentTrackerConfirmationDataSource extends Action
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'datasource:paymentTrackerConfirmation {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Action for expense payment tracker confirmation';

    /**
     * Execute the console command.
     */
    public function apply()
    {
        $id = $this->input['id'];

        // check id is exist or not in expense_invoice_payment_tracker table if not exist then return error
        $tracker = $this->tenantDB()->table('expense_invoice_payment_tracker')
            ->where('id', $id)
            ->get();

        if(count($tracker) == 0){
            $this->status = 'error';
            $this->message = 'No record found for this id';
            $this->statusCode = 400;
            $this->data = [];
            return;
        }
       
        $result = $this->tenantDB()->table('expense_invoice_payment_tracker')
        ->where('id', $id)
        ->update([
            'status' => 'N'
        ]);
        
        if($result){
            $this->status = 'success';
            $this->message = 'Cheque has bounced.';
            $this->statusCode = 200;
            $this->data = [];
        }else{
            $this->status = 'error';
            $this->message = 'Error in cheque bounce';
            $this->statusCode = 400;
            $this->data = [];
        }
    }
}
