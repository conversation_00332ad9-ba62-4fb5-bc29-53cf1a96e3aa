<?php

namespace App\Console\Commands\Actions\Expenses;

use App\Console\Commands\Action;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;

class PurchaseFormDetailDataSource extends Action
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'datasource:purchaseFormDetail {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Purchase Form Detail Data Source';


    /**
     * Execute the console command.
     */
    public function apply()
    {

        $soc_id = (int) $this->input["company_id"];
        $purchase_form_id = $this->input["id"];
        $s3Url = env('AWS_S3_URL') . '/';

        $query = $this->tenantDB()->table('chsone_purchase_form')
            ->selectRaw(
                'purchase_form_vendor_id,
            purchase_form_id,
            soc_id,
            purchase_form_title,
            purchase_form_desc,
            purchase_form_type,
            FORMAT(purchase_form_amount, 2) AS purchase_form_amount,
            purchase_form_currency,
            purchase_form_po_number,
            purchase_form_approvals,
            purchase_form_email_sent_approver,
            CONCAT(?, purchase_form_file_upload_path) AS purchase_form_file_upload_path,
            purchase_form_email_sent_vendor,
            purchase_form_vendor_name,
            purchase_form_ledger_name,
            purchase_form_num_approvals_needed,
            added_on,
            updated_on,
            status,
            SUBSTRING_INDEX(purchase_form_file_upload_path, "/", -1) AS attachment',
                [$s3Url]
            )
            ->where('soc_id', $soc_id)->where('purchase_form_id', $purchase_form_id);
        $result = $query->first();

        if ($result == null) {
            $this->status = 'error';
            $this->message = 'Unable to Fetch Purchase Form Data';
            $this->statusCode = 400;
            return;
        }

        // $purchase_form_items = $this->tenantDB()->table('chsone_purchase_form_items')->where('soc_id', $soc_id)->where('purchase_form_id', $purchase_form_id)->get();
        $vendor_data = $this->tenantDB()->table('chsone_vendors_master')->where('soc_id', $soc_id)->where('vendor_id', $result->purchase_form_vendor_id)->first();

        if ($vendor_data == null) {
            $result->vendor_email = "";
        } else {
            $result->vendor_email = $vendor_data->vendor_email;
        }
        $result->purchase_form_num_approvals_needed = unserialize($result->purchase_form_num_approvals_needed);


        $approver_response = $this->processApproverData($purchase_form_id);
        $reviewer_response = $this->processReviewerData($purchase_form_id);


        // Append key-value pairs to the existing object's data property

        if ($approver_response) {
            foreach ($approver_response as $key => $value) {
                $result->$key = $value;
            }
        }

        if ($reviewer_response) {
            foreach ($reviewer_response as $key => $value) {
                $result->$key = $value;
            }
        }


        //dd($result->purchase_form_file_upload_path);
        $result->purchase_form_file_upload_path = ltrim($result->purchase_form_file_upload_path, '/');
        if ($this->hasValidExtension($result->attachment)) {
            if (Str::contains($result->purchase_form_file_upload_path, 'society.chsone.in.s3.amazonaws.com')) {
                $result->attachment_link = $result->purchase_form_file_upload_path;
            } else {
                // If it's already a full URL, use it as is; otherwise, append the base URL
                if (Str::startsWith($result->purchase_form_file_upload_path, ['http://', 'https://'])) {
                    $result->attachment_link = $result->purchase_form_file_upload_path;
                } else {
                    $cleanPath = ltrim($result->purchase_form_file_upload_path, '/');
                    $result->attachment_link = 'http://society.chsone.in.s3.amazonaws.com/' . $cleanPath;
                }
            }
        } else {
            $result->attachment = "";
            $result->attachment_link = "";
            $result->purchase_form_file_upload_path = "";
        }



        $result->print_url = "purchaseform/view/" . $purchase_form_id . "/download/pdf";

        $this->data = $result;
    }


    // public function processApproverData($id)
    // {
    //     $response = $this->tenantDB()->table('purchase_order_approvers')
    //         ->where('purchase_order_id', $id)
    //         ->get();

    //     //collect all user_id from purchase_order_approvers
    //     $user_ids = $response->pluck('user_id')->toArray();

    //     $new_response[] = [
    //         "user_ids" => $user_ids
    //     ];
    //     $all_one = [];

    //     foreach ($response as $approver) {
    //         $all_one[] = $approver->status;
    //         $user_data = $this->getUserData($approver->user_id);

    //         if ($approver->status === 2) {
    //             if (!empty($user_data->user_first_name)) {
    //                 $new_response[] = [
    //                     'reject_by_name' => $user_data->user_first_name
    //                 ];
    //             } else {
    //                 $new_response[] = [
    //                     'reject_by_name' => ""
    //                 ];
    //             }
    //         } else if ($approver->status === 0) {

    //             if (!empty($user_data->user_first_name)) {
    //                 $new_response[] = [
    //                     'approval_pending_by_name' => $user_data->user_first_name,
    //                     'approved_by' => 'No one has approved this PO.'
    //                 ];
    //             } else {
    //                 $new_response[] = [
    //                     'approval_pending_by_name' => ""
    //                 ];
    //             }
    //         } else if ($approver->status === 1) {

    //             if (!empty($user_data->user_first_name)) {
    //                 $new_response[] = [
    //                     'approved_by' => $user_data->user_first_name
    //                 ];
    //             } else {
    //                 $new_response[] = [
    //                     'approved_by' => ""
    //                 ];
    //             }
    //         }
    //     }

    //     if (
    //         collect($all_one)->every(function ($status) {
    //             return $status === 1;
    //         })
    //     ) {
    //         $new_response[] = [
    //             'approval_pending_by_name' => "Action By All"
    //         ];
    //     }
    //     $new_response =   $this->mergePurchasedDetail($new_response) ?? [];

    //     return $new_response;
    // }

    // public function processReviewerData($id)
    // {
    //     $response = $this->tenantDB()->table('purchase_order_reviewers')
    //         ->where('purchase_order_id', $id)
    //         ->get();

    //     if (count($response) == 0) {

    //         return [];
    //     }

    //     $user_ids = $response->pluck('user_id')->toArray();

    //     $new_response[] = [
    //         "user_ids" => $user_ids
    //     ];
    //     $all_one = [];



    //     foreach ($response as $reviewer) {
    //         $all_one[] = $reviewer->status;
    //         $user_data = $this->getUserData($reviewer->user_id);

    //         if ($reviewer->status === 2) {
    //             if (!empty($user_data->user_first_name)) {
    //                 $new_response[] = [
    //                     'refused_by_name' => $user_data->user_first_name
    //                 ];
    //             } else {
    //                 $new_response[] = [
    //                     'refused_by_name' => ""
    //                 ];
    //             }
    //         } else if ($reviewer->status === 0) {
    //             if (!empty($user_data->user_first_name)) {
    //                 $new_response[] = [
    //                     'reviewed_pending_by_name' => $user_data->user_first_name
    //                 ];
    //             } else {
    //                 $new_response[] = [
    //                     'reviewed_pending_by_name' => ""
    //                 ];
    //             }
    //         } else if ($reviewer->status === 1) {
    //             if (!empty($user_data->user_first_name)) {
    //                 $new_response[] = [
    //                     'reviewed_by' => $user_data->user_first_name
    //                 ];
    //             } else {
    //                 $new_response[] = [
    //                     'reviewed_by' => ""
    //                 ];
    //             }
    //         }
    //     }

    //     if (
    //         collect($all_one)->every(function ($status) {

    //             return $status === 1;
    //         })
    //     ) {
    //         $new_response[] = [
    //             'reviewed_pending_by_name' => "Action performed by all"
    //         ];
    //     }
    //     $new_response =   $this->mergePurchasedDetail($new_response) ?? [];

    //     return $new_response;
    // }

    public function processApproverData($id)
    {
        $response = $this->tenantDB()->table('purchase_order_approvers')
            ->where('purchase_order_id', $id)
            ->get();

        if (count($response) == 0) {
            return [];
        }

        //collect all user_id from purchase_order_approvers
        $user_ids = $response->pluck('user_id')->toArray();

        $new_response[] = [
            "user_ids" => $user_ids
        ];
        
        $all_one = [];
        $approved_by_set = false;  // Flag to check if 'approved_by' is already set
        $approved_by_value = 'No one has approved this PO.';  // Default value

        foreach ($response as $approver) {
            $all_one[] = $approver->status;
            $user_data = $this->getUserData($approver->user_id);

            if ($approver->status == 2) {
                // if (!empty($user_data->user_first_name)) {
                //     $new_response[] = [
                //         'reject_by_name' => $user_data->user_first_name
                //     ];
                // } else {
                //     $new_response[] = [
                //         'reject_by_name' => ""
                //     ];
                // }
                $new_response[] = [
                    'reject_by_name' => !empty($user_data->user_first_name) ? $user_data->user_first_name : ""
                ];
            } else if ($approver->status == 0) {

                // if (!empty($user_data->user_first_name)) {
                //     $new_response[] = [
                //         'approval_pending_by_name' => $user_data->user_first_name,
                //         'approved_by' => 'No one has approved this PO.'
                //     ];
                // } else {
                //     $new_response[] = [
                //         'approval_pending_by_name' => "",
                //         'approved_by' => 'No one has approved this PO.'
                //     ];
                // }
                 $new_response[] = [
                    'approval_pending_by_name' => !empty($user_data->user_first_name) ? $user_data->user_first_name : "",
                    'approved_by' => $approved_by_set ? null : $approved_by_value
                ];
                $approved_by_set = true;  // Set the flag to true
            } else if ($approver->status == 1) {

                // if (!empty($user_data->user_first_name)) {
                //     $new_response[] = [
                //         'approved_by' => $user_data->user_first_name
                //     ];
                // } else {
                //     $new_response[] = [
                //         'approved_by' => ""
                //     ];
                // }
                // Set reviewed_by only if it hasn't been set already
                if (!$approved_by_set) {
                    $new_response[] = [
                        'approved_by' => !empty($user_data->user_first_name) ? $user_data->user_first_name : ""
                    ];
                    $approved_by_set = true;  // Set the flag to true
                }
            }
        }

        if (
            collect($all_one)->every(function ($status) {
                // return $status === 1;
                return $status == 1;
            })
        ) {
            $new_response[] = [
                'approval_pending_by_name' => "Action By All"
            ];
        }
        $new_response =   $this->mergePurchasedDetail($new_response) ?? [];

        // Clean up extra commas and spaces
        if (isset($new_response['approval_pending_by_name'])) {
            $new_response['approval_pending_by_name'] = trim(preg_replace('/\s*,\s*(,*)$/', '', $new_response['approval_pending_by_name']), ', ');
        }

        if (isset($new_response['approved_by'])) {
            $new_response['approved_by'] = trim(preg_replace('/\s*,\s*(,*)$/', '', $new_response['approved_by']), ', ');
        }

        // check if approved_by is empty, if so set it to "No one has approved this PO." and approval_pending_by_name is empty, set it to "Action By All"
        if (empty($new_response['approved_by'])) {
            $new_response['approved_by'] = "No one has approved this PO.";
        }

        if (empty($new_response['approval_pending_by_name'])) {
            $new_response['approval_pending_by_name'] = "Action By All";
        }

        if(empty($new_response['reject_by_name'])) {
            $new_response['reject_by_name'] = "NA";
        }

        return $new_response;
    }

    public function processReviewerData($id)
    {
        $response = $this->tenantDB()->table('purchase_order_reviewers')
            ->where('purchase_order_id', $id)
            ->get();

        if (count($response) == 0) {
            return [];
        }

        $user_ids = $response->pluck('user_id')->toArray();

        $new_response[] = [
            "user_ids" => $user_ids
        ];

        $all_one = [];
        $reviewed_by_set = false;  // Flag to check if 'reviewed_by' is already set
        $reviewed_by_value = 'No one has approved this PO.';  // Default value

        foreach ($response as $reviewer) {
            $all_one[] = $reviewer->status;
            $user_data = $this->getUserData($reviewer->user_id);
            if ($reviewer->status == 2) {
                $new_response[] = [
                    'refused_by_name' => !empty($user_data->user_first_name) ? $user_data->user_first_name : ""
                ];
            } else if ($reviewer->status == 0) {
                $new_response[] = [
                    'reviewed_pending_by_name' => !empty($user_data->user_first_name) ? $user_data->user_first_name : "",
                    'reviewed_by' => $reviewed_by_set ? null : $reviewed_by_value
                ];
                $reviewed_by_set = true;  // Set the flag to true

            } else if ($reviewer->status == 1) {
                // Set reviewed_by only if it hasn't been set already
                if (!$reviewed_by_set) {
                    $new_response[] = [
                        'reviewed_by' => !empty($user_data->user_first_name) ? $user_data->user_first_name : ""
                    ];
                    $reviewed_by_set = true;  // Set the flag to true
                }
            }
        }

        // Check if all reviewers have status 1
        if (collect($all_one)->every(function ($status) {
            return $status == 1;
        })) {
            $new_response[] = [
                'reviewed_pending_by_name' => "Action performed by all"
            ];
        }

        // Clean up extra commas and spaces

        $new_response = $this->mergePurchasedDetail($new_response) ?? [];
        if (isset($new_response['reviewed_pending_by_name'])) {
            $new_response['reviewed_pending_by_name'] = trim(preg_replace('/\s*,\s*(,*)$/', '', $new_response['reviewed_pending_by_name']), ', ');
        }

        if (isset($new_response['reviewed_by'])) {
            $new_response['reviewed_by'] = trim(preg_replace('/\s*,\s*(,*)$/', '', $new_response['reviewed_by']), ', ');
        }

        // check if reviewed_by is empty, if so set it to "No one has approved this PO." and reviewed_pending_by_name is empty, set it to "Action By All"
        if (empty($new_response['reviewed_by'])) {
            $new_response['reviewed_by'] = "No one has approved this PO.";
        }

        if (empty($new_response['reviewed_pending_by_name'])) {
            $new_response['reviewed_pending_by_name'] = "Action By All";
        }

        if(empty($new_response['refused_by_name'])) {
            $new_response['refused_by_name'] = "NA";
        }

        return $new_response;
    }

    public function getUserData($user_id)
    {
        return $this->masterDB()->table('chsone_users_master')
            ->select('user_id', 'user_first_name', 'user_last_name')
            ->where('user_id', $user_id)
            ->first();
    }

    public function mergePurchasedDetail($data)
    {
        $merged = [];

        foreach ($data as $item) {
            unset($item['user_ids']);
            $singleItem = count($item) === 1;

            foreach ($item as $key => $value) {
                if ($key == 'user_ids') {
                    continue;
                }
                if (is_array($value)) {
                    if (!isset($merged[$key])) {
                        $merged[$key] = $value;
                    } else {
                        $merged[$key] = array_merge($merged[$key], $value);
                    }
                } else {
                    if (!isset($merged[$key])) {
                        $merged[$key] = $value;
                    } else {
                        if ($key === 'approved_by' && $value === "No one has approved this PO.") {
                            // Ensure "No one has approved this PO." is not concatenated
                            if ($merged[$key] !== "No one has approved this PO.") {
                                $merged[$key] = "No one has approved this PO.";
                            }
                        } else {
                            if (!$singleItem) {
                                $merged[$key] .= ", " . $value;
                            }
                        }
                    }
                }
            }
        }
        return $merged;
    }


    function hasValidExtension($filePath)
    {
        // List of allowed extensions
        $allowedExtensions = ['png', 'jpeg', 'jpg', 'gif', 'bmp', 'tiff', 'webp', 'svg', 'ico'];

        // Get the file extension
        $fileExtension = strtolower(pathinfo($filePath, PATHINFO_EXTENSION));

        // Check if the extension is in the allowed list
        return in_array($fileExtension, $allowedExtensions);
    }
}
