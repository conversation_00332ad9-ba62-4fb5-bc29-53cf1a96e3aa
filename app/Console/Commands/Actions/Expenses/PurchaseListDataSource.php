<?php

namespace App\Console\Commands\Actions\Expenses;

use Illuminate\Console\Command;
use App\Console\Commands\Action;

class PurchaseListDataSource extends Action
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'datasource:PurchaseList {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Get the Purchase List';

    protected $formatter = [
        'id' => '',
        'purchase_form_title' => '',
        'purchase_form_amount' => '',
        'purchase_form_currency' => '',
        'purchase_form_po_number' => '',
        'purchase_form_vendor_name' => '',
        'status' => 0,
        'reject_by_name' => '',
        'approval_pending_by_name' => '',
        'approved_by' => '',
        'refused_by_name' => '',
        'reviewed_pending_by_name' => '',
        'reviewed_by' => '',
        'disable' => ''
    ];

    protected $formatterByKeys = ['id'];

    protected $mapper = [
        'id' => 'chsone_purchase_form.purchase_form_id',
    ];

    /**
     * Execute the console command.
     */
    public function apply()
    {
        $page = $this->input['page'] ?? 1;
        $per_page = !empty($this->input['per_page']) ? $this->input['per_page'] : ($this->hugeData ? 100000 : 10);

        $offset = ($page - 1) * $per_page;

        $user_id = $this->input['user_id'];
        $obj = $this->tenantDB()->table('chsone_purchase_form')
            ->select(
                'purchase_form_id as id',
                'purchase_form_title',
                'purchase_form_amount',
                'purchase_form_currency',
                'purchase_form_po_number',
                'purchase_form_vendor_name',
                'status'
            )
            ->where('soc_id', $this->input['company_id'])
            ->orderBy('purchase_form_id', 'desc')
            ->offset($offset)
            ->limit($per_page)
            ->get();

        $totalCount = $this->tenantDB()->table('chsone_purchase_form')
            ->where('soc_id', $this->input['company_id'])
            ->count();

        // foreach ($obj as $key => $item) {
        //     $disable = "true";
        //     $approver_response = $this->processApproverData($item->id);
        //     $reviewer_response = $this->processReviewerData($item->id);


        //     if(is_array($reviewer_response)){
        //         $reviewer_response[] = [
        //             'user_ids' => []
        //         ];
        //     }


        //     $reviewer_array = $reviewer_response ? $reviewer_response[0]['user_ids'] : [];
        //     $approver_array = $approver_response ? $approver_response[0]['user_ids'] : [];

        // if (in_array($user_id, $reviewer_array) || in_array($user_id, $approver_array)) {
        //     $disable = "false";
        // }

        //     $obj[$key]->approver_data = $approver_response;
        //     $obj[$key]->reviewer_data = $reviewer_response ? $reviewer_response : [];
        //     $obj[$key]->disable = $disable;
        // }

        foreach ($obj as $index => $item) {
            // $disable = "true";
            $disable = "false";
            // Initialize fields
            $item->approval_pending_by_name = '';
            $item->approved_by = '';
            $item->reject_by_name = '';
            $item->reviewed_pending_by_name = '';
            $item->reviewed_by = '';
            $item->refused_by_name = '';

            // Process approver and reviewer responses
            $approver_response = $this->processApproverData($item->id);
            $reviewer_response = $this->processReviewerData($item->id);
            $reviewer_data = $this->tenantDB()->table('purchase_order_reviewers')
                ->where('purchase_order_id', $item->id)
                ->get();


            $reviewer_data_user_ids = $reviewer_data->pluck('user_id')->toArray();

            $approver_data = $this->tenantDB()->table('purchase_order_approvers')
                ->where('purchase_order_id', $item->id)
                ->get();

            //collect all user_id from purchase_order_approvers
            $approver_data_user_ids = $approver_data->pluck('user_id')->toArray();

            // if (in_array($user_id, $approver_data_user_ids) || in_array($user_id, $reviewer_data_user_ids)) {
            //     $disable = "false";
            // }

            // Get current user's rows
            // $user_reviewer_row = $reviewer_data->firstWhere('user_id', $user_id);
            // $user_approver_row = $approver_data->firstWhere('user_id', $user_id);

            // // Default disable = false
            // $disable = "false";

            // // CASE 1: User not in reviewer/approver table
            // if (!$user_reviewer_row && !$user_approver_row) {
            //     $disable = "true";
            // }

            // // Extract current user status
            // $user_reviewer_status = $user_reviewer_row->status ?? null;
            // $user_approver_status = $user_approver_row->status ?? null;

            // // Check for other users with pending status
            // $other_pending_reviewer = $reviewer_data
            //     ->where('user_id', '!=', $user_id)
            //     ->where('status', 0)
            //     ->isNotEmpty();

            // $other_pending_approver = $approver_data
            //     ->where('user_id', '!=', $user_id)
            //     ->where('status', 0)
            //     ->isNotEmpty();

            // // === RULE LOGIC ===

            // if (
            //     // Rule: user status 0 in both reviewer and approver
            //     ($user_reviewer_status === 0 && $user_approver_status === 0) ||

            //     // Rule: user is reviewer status 0, and approver status is 2
            //     ($user_reviewer_status === 0 && $user_approver_status === 2) ||

            //     // Rule: user is reviewer status 0 and other reviewer is also pending
            //     ($user_reviewer_status === 0 && $other_pending_reviewer) ||

            //     // Rule: user is not reviewer, but another reviewer is pending
            //     (!$user_reviewer_row && $other_pending_reviewer)
            // ) {
            //     $disable = "true";
            // }

            // Get current user's row from each table
            $user_reviewer_row = $reviewer_data->firstWhere('user_id', $user_id);
            $user_approver_row = $approver_data->firstWhere('user_id', $user_id);

            // Get current user's status from each table
            $user_reviewer_status = $user_reviewer_row->status ?? null;
            $user_approver_status = $user_approver_row->status ?? null;

            // Check if any other user (not current) has pending status (0)
            $other_pending_reviewer = $reviewer_data
                ->where('user_id', '!=', $user_id)
                ->where('status', 0)
                ->isNotEmpty();

            $other_pending_approver = $approver_data
                ->where('user_id', '!=', $user_id)
                ->where('status', 0)
                ->isNotEmpty();

            // Default to enabled
            $disable = "false";

            // ========================
            //         RULES
            // ========================

            // 1. Not in either approver or reviewer table
            if (!$user_reviewer_row && !$user_approver_row) {
                $disable = "true";
            }

            // 2. User is in both tables and both statuses are 0
            if ($user_reviewer_status === 0 && $user_approver_status === 0) {
                $disable = "true";
            }

            // 3. User is reviewer with status 0 AND is already approved as approver
            if ($user_reviewer_status === 0 && $user_approver_status === 2) {
                $disable = "true";
            }

            // 4. User is reviewer with status 0 AND another reviewer is still pending
            if ($user_reviewer_status === 0 && $other_pending_reviewer) {
                $disable = "true";
            }

            // 5. User is NOT reviewer, but another reviewer is pending
            if (!$user_reviewer_row && $other_pending_reviewer) {
                $disable = "true";
            }

            // ========== ALLOW ==========

            // 6. Allow if user is approver with status 0 (even if reviewers are pending)
            if ($user_approver_status === 0) {
                $disable = "false";
            }

            // 7. Allow if user is reviewer with status 0, not also approver, and no other reviewer is pending
            if (
                $user_reviewer_status === 0 &&
                !$user_approver_row &&
                !$other_pending_reviewer
            ) {
                $disable = "false";
            }

            // 8. Allow if user is approver with status 1 (in-progress) and reviewer with status 0
            if (
                $user_approver_status === 1 &&
                $user_reviewer_status === 0
            ) {
                $disable = "false";
            }

            if ($approver_response) {
                foreach ($approver_response as $key => $value) {
                    $item->$key = $value;
                }
            }

            // Update item with reviewer response
            if ($reviewer_response) {
                foreach ($reviewer_response as $key => $value) {
                    $item->$key = $value;
                }
            }

            // Update the object in the array
            $obj[$index] = $item;
            $obj[$index]->disable = $disable;


            switch ($item->status) {
                case 0:
                    $item->status = 'Approval Pending';
                    break;
                case 1:
                    $item->status = 'Approved';
                    break;
                case 2:
                    $item->status = 'Reviewed';
                    if (!empty($item->reject_by_name) || ($item->reject_by_name == "Action By All")) { //if ($item->reject_by_name == "Action By All")
                        $item->status = 'Rejected';
                    }
                    if (!empty($item->reject_by_name) && ($item->reject_by_name == "Action By All")) { //if ($item->reject_by_name == "Action By All")
                        $item->status = 'Aprroved';
                    }
                    break;
                case 3:
                    $item->status = 'Approval Pending';
                    if (!empty($item->approval_pending_by_name) && ($item->approval_pending_by_name == "Action By All")) { //if ($item->reject_by_name == "Action By All")
                        $item->status = 'In Progress';
                    }
                    break;
                case 4:
                    $item->status = 'Reviewed';
                    break;
                case '0':
                    $item->status = 'Inactive';
                    break;
                case '1':
                    $item->status = 'Active';
                    break;
                case 'unpaid':
                    $item->status = 'Unpaid';
                    break;
                case 'paid':
                    $item->status = 'Paid';
                    break;
                case 'Partialpaid':
                    $item->status = 'Partial Paid';
                    break;
                default:
                    // No match found, retain original status
                    $item->status = $item->status;
            }
        }


        $this->data = $obj;
        $this->meta['pagination']['total'] = $totalCount;
    }
    public function processApproverData($id)
    {
        $response = $this->tenantDB()->table('purchase_order_approvers')
            ->where('purchase_order_id', $id)
            ->get();

        if (count($response) == 0) {
            return [];
        }

        //collect all user_id from purchase_order_approvers
        $user_ids = $response->pluck('user_id')->toArray();

        $new_response[] = [
            "user_ids" => $user_ids
        ];
        
        $all_one = [];
        $approved_by_set = false;  // Flag to check if 'approved_by' is already set
        $approved_by_value = 'No one has approved this PO.';  // Default value

        foreach ($response as $approver) {
            $all_one[] = $approver->status;
            $user_data = $this->getUserData($approver->user_id);

            if ($approver->status == 2) {
                // if (!empty($user_data->user_first_name)) {
                //     $new_response[] = [
                //         'reject_by_name' => $user_data->user_first_name
                //     ];
                // } else {
                //     $new_response[] = [
                //         'reject_by_name' => ""
                //     ];
                // }
                $new_response[] = [
                    'reject_by_name' => !empty($user_data->user_first_name) ? $user_data->user_first_name : ""
                ];
            } else if ($approver->status == 0) {

                // if (!empty($user_data->user_first_name)) {
                //     $new_response[] = [
                //         'approval_pending_by_name' => $user_data->user_first_name,
                //         'approved_by' => 'No one has approved this PO.'
                //     ];
                // } else {
                //     $new_response[] = [
                //         'approval_pending_by_name' => "",
                //         'approved_by' => 'No one has approved this PO.'
                //     ];
                // }
                 $new_response[] = [
                    'approval_pending_by_name' => !empty($user_data->user_first_name) ? $user_data->user_first_name : "",
                    'approved_by' => $approved_by_set ? null : $approved_by_value
                ];
                $approved_by_set = true;  // Set the flag to true
            } else if ($approver->status == 1) {

                // if (!empty($user_data->user_first_name)) {
                //     $new_response[] = [
                //         'approved_by' => $user_data->user_first_name
                //     ];
                // } else {
                //     $new_response[] = [
                //         'approved_by' => ""
                //     ];
                // }
                // Set reviewed_by only if it hasn't been set already
                if (!$approved_by_set) {
                    $new_response[] = [
                        'approved_by' => !empty($user_data->user_first_name) ? $user_data->user_first_name : ""
                    ];
                    $approved_by_set = true;  // Set the flag to true
                }
            }
        }

        if (
            collect($all_one)->every(function ($status) {
                // return $status === 1;
                return $status == 1;
            })
        ) {
            $new_response[] = [
                'approval_pending_by_name' => "Action By All"
            ];
        }
        $new_response =   $this->mergePurchasedDetail($new_response) ?? [];

        // Clean up extra commas and spaces
        if (isset($new_response['approval_pending_by_name'])) {
            $new_response['approval_pending_by_name'] = trim(preg_replace('/\s*,\s*(,*)$/', '', $new_response['approval_pending_by_name']), ', ');
        }

        if (isset($new_response['approved_by'])) {
            $new_response['approved_by'] = trim(preg_replace('/\s*,\s*(,*)$/', '', $new_response['approved_by']), ', ');
        }

        // check if approved_by is empty, if so set it to "No one has approved this PO." and approval_pending_by_name is empty, set it to "Action By All"
        if (empty($new_response['approved_by'])) {
            $new_response['approved_by'] = "No one has approved this PO.";
        }

        if (empty($new_response['approval_pending_by_name'])) {
            $new_response['approval_pending_by_name'] = "Action By All";
        }

        if(empty($new_response['reject_by_name'])) {
            $new_response['reject_by_name'] = "NA";
        }

        return $new_response;
    }

    public function processReviewerData($id)
    {
        $response = $this->tenantDB()->table('purchase_order_reviewers')
            ->where('purchase_order_id', $id)
            ->get();

        if (count($response) == 0) {
            return [];
        }

        $user_ids = $response->pluck('user_id')->toArray();

        $new_response[] = [
            "user_ids" => $user_ids
        ];

        $all_one = [];
        $reviewed_by_set = false;  // Flag to check if 'reviewed_by' is already set
        $reviewed_by_value = 'No one has approved this PO.';  // Default value

        foreach ($response as $reviewer) {
            $all_one[] = $reviewer->status;
            $user_data = $this->getUserData($reviewer->user_id);
            if ($reviewer->status == 2) {
                $new_response[] = [
                    'refused_by_name' => !empty($user_data->user_first_name) ? $user_data->user_first_name : ""
                ];
            } else if ($reviewer->status == 0) {
                $new_response[] = [
                    'reviewed_pending_by_name' => !empty($user_data->user_first_name) ? $user_data->user_first_name : "",
                    'reviewed_by' => $reviewed_by_set ? null : $reviewed_by_value
                ];
                $reviewed_by_set = true;  // Set the flag to true

            } else if ($reviewer->status == 1) {
                // Set reviewed_by only if it hasn't been set already
                if (!$reviewed_by_set) {
                    $new_response[] = [
                        'reviewed_by' => !empty($user_data->user_first_name) ? $user_data->user_first_name : ""
                    ];
                    $reviewed_by_set = true;  // Set the flag to true
                }
            }
        }

        // Check if all reviewers have status 1
        if (collect($all_one)->every(function ($status) {
            return $status == 1;
        })) {
            $new_response[] = [
                'reviewed_pending_by_name' => "Action performed by all"
            ];
        }

        // Clean up extra commas and spaces

        $new_response = $this->mergePurchasedDetail($new_response) ?? [];
        if (isset($new_response['reviewed_pending_by_name'])) {
            $new_response['reviewed_pending_by_name'] = trim(preg_replace('/\s*,\s*(,*)$/', '', $new_response['reviewed_pending_by_name']), ', ');
        }

        if (isset($new_response['reviewed_by'])) {
            $new_response['reviewed_by'] = trim(preg_replace('/\s*,\s*(,*)$/', '', $new_response['reviewed_by']), ', ');
        }

        // check if reviewed_by is empty, if so set it to "No one has approved this PO." and reviewed_pending_by_name is empty, set it to "Action By All"
        if (empty($new_response['reviewed_by'])) {
            $new_response['reviewed_by'] = "No one has approved this PO.";
        }

        if (empty($new_response['reviewed_pending_by_name'])) {
            $new_response['reviewed_pending_by_name'] = "Action By All";
        }

        if(empty($new_response['refused_by_name'])) {
            $new_response['refused_by_name'] = "NA";
        }

        return $new_response;
    }

    public function getUserData($user_id)
    {
        return $this->masterDB()->table('chsone_users_master')
            ->select('user_id', 'user_first_name', 'user_last_name')
            ->where('user_id', $user_id)
            ->first();
    }

    public function mergePurchasedDetail($data)
    {
        $merged = [];

        foreach ($data as $item) {
            unset($item['user_ids']);
            $singleItem = count($item) === 1;

            foreach ($item as $key => $value) {
                if ($key == 'user_ids') {
                    continue;
                }
                if (is_array($value)) {
                    if (!isset($merged[$key])) {
                        $merged[$key] = $value;
                    } else {
                        $merged[$key] = array_merge($merged[$key], $value);
                    }
                } else {
                    if (!isset($merged[$key])) {
                        $merged[$key] = $value;
                    } else {
                        if ($key === 'approved_by' && $value === "No one has approved this PO.") {
                            // Ensure "No one has approved this PO." is not concatenated
                            if ($merged[$key] !== "No one has approved this PO.") {
                                $merged[$key] = "No one has approved this PO.";
                            }
                        } else {
                            if (!$singleItem) {
                                $merged[$key] .= ", " . $value;
                            }
                        }
                    }
                }
            }
        }
        return $merged;
    }
}


// pof  poa por remark
// 0	0	0	approval pending
// 1    1   x   approved
// 2	2	0	reject //once approve cant reject
// 3	1	0	inprogess
// 4	1	1	review //once review can refuse
// 5	1	2	refuse//we can refuse after approve ,without approve we can't  refuse and after review we cant refuse
