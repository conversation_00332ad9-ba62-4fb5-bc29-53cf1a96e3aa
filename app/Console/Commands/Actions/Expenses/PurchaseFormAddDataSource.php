<?php

namespace App\Console\Commands\Actions\Expenses;

use App\Console\Commands\Action;

class PurchaseFormAddDataSource extends Action
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'datasource:purchaseFormAdd {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Add the Purchase Form Data Source';


    /**
     * Execute the console command.
     */
    public function apply()
    {

        $soc_id = (int) $this->input["company_id"];
        $purchase_form_title = $this->input["purchase_form_title"];
        $purchase_form_desc = $this->input["purchase_form_desc"];
        $purchase_form_type = $this->input["purchase_form_type"];
        $purchase_form_vendor = (int) $this->input["purchase_form_vendor"];

        $purchase_form_amount = (float) $this->input["purchase_form_amount"];
        $purchase_form_num_approvals_needed = serialize($this->input["purchase_form_approvers"]);
        $added_on = date('Y-m-d H:i:s');
        $status = 0;

        if (isset($this->input["purchase_order_status"])) {

            $status = (int) $this->input["purchase_order_status"];
        }

        $purchase_form_file_upload_path="";
        if (isset($this->input["attachment"])) {
            $purchase_form_file_upload_path = $this->input["attachment"];
        }

        $vendor_data = $this->tenantDB()->table('chsone_vendors_master')
            ->select(
                "vendor_name",
                "vendor_ledger_id"
            )
            ->where('vendor_id', $purchase_form_vendor)
            ->first();

        $purchase_form_vendor_name = $vendor_data->vendor_name;
        $purchase_form_ledger_name = $this->tenantDB()->table('chsone_grp_ledger_tree')
            ->select(
                "ledger_account_name",
            )
            ->where('ledger_account_id', $vendor_data->vendor_ledger_id)
            ->value("ledger_account_name");

        $purchase_order_data = [
            "soc_id" => $soc_id,
            "purchase_form_title" => $purchase_form_title,
            "purchase_form_desc" => $purchase_form_desc,
            "purchase_form_type" => $purchase_form_type,
            "purchase_form_vendor_id" => $purchase_form_vendor,
            "purchase_form_amount" => $purchase_form_amount,
            "purchase_form_currency" => "INR",
            "purchase_form_vendor_name" => $purchase_form_vendor_name,
            "purchase_form_ledger_name" => $purchase_form_ledger_name,
            "purchase_form_num_approvals_needed" => $purchase_form_num_approvals_needed,
            "purchase_form_file_upload_path" => $purchase_form_file_upload_path,
            "added_on" => $added_on,
            "status" => $status
        ];


        //write a code which insert above data into teant database with table chsone_purchase_form
        $po_id = $this->tenantDB()->table('chsone_purchase_form')->insertGetId($purchase_order_data);

        if ($po_id) {
            $this->purchase_order($this->input["purchase_form_approvers"], "purchase_order_approvers", $po_id);

            if (
                isset($this->input["purchase_form_reviewers"])
                && !empty($this->input["purchase_form_reviewers"])
            ) {
                $this->purchase_order($this->input["purchase_form_reviewers"], "purchase_order_reviewers", $po_id);
            }
            $purchase_form_po_number = $purchase_form_type . "-" . $soc_id . "-" . $po_id;

            $po_id = $this->tenantDB()->table('chsone_purchase_form')
                ->where('purchase_form_id', $po_id)
                ->update([
                    'purchase_form_po_number' => $purchase_form_po_number
                ]);
        }

        $this->data = [];
    }

    public function purchase_order($purchase_order_array, $purchase_order_table, $po_id)
    {
        $purchase_order_data = collect($purchase_order_array)->map(function ($user_id) use ($po_id) {
            return [
                "purchase_order_id" => $po_id,
                "user_id" => $user_id,
                "status" => 0
            ];
        });

        $this->tenantDB()->table($purchase_order_table)->insert($purchase_order_data->toArray());

    }
}

// $approvers = json_decode($this->input["purchase_form_approvers"]);
// $approverData = collect($approvers)->map(function ($approver)use ($po_id) {
//     return [
//         "purchase_order_id" => $po_id,
//         "user_id" => $approver,
//         "status" => 0
//     ];
// });

// $this->tenantDB()->table('purchase_order_approvers')->insert($approverData->toArray());

// $reviewers = json_decode($this->input["purchase_form_reviewers"]);
// $reviewersData = collect($reviewers)->map(function ($reviewer)use ($po_id) {
//     return [
//         "purchase_order_id" =>  $po_id,
//         "user_id" => $reviewer,
//         "status" => 0
//     ];
// });

// $this->tenantDB()->table('purchase_order_reviewers')->insert($reviewersData->toArray());

