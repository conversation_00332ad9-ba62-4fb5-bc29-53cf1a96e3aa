<?php

namespace App\Console\Commands\Actions\Expenses;

use App\Console\Commands\Action;

class VendorAdvancesDetailsDataSource extends Action
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'datasource:vendorAdvancesDetails {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Add a new miscellaneous expense to the system.';

    /**
     * Execute the console command.
     */
    public function apply()
    {

        $id=$this->input['id'];

        $this->data = $this->tenantDB()->table('chsone_credit_accounts as cca')
            ->where('credit_account_id', $id)
            ->select('eipt.payment_number','cca.credit_account_id as id','cca.soc_id','cca.payment_tracker_id','cca.payment_date','cca.account_id','cca.account_name','cca.account_context','cca.amount','cca.type','cca.payment_mode','cca.transaction_type','cca.narration','cca.use_credit','cca.use_credit as use_credit_hide','cca.use_credit_after')
            ->leftjoin('expense_invoice_payment_tracker as eipt', 'eipt.id', '=', 'cca.payment_tracker_id')
            ->first();

    }
}
