<?php

namespace App\Console\Commands\Actions\Expenses;

use App\Console\Commands\Action;

class PurchaseFormReviewDataSource extends Action
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'datasource:purchaseFormReview {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Review the Purchase Form Data Source';


    /**
     * Execute the console command.
     */

    public function apply()
    {

        $po_id = $this->input['id'];
        $company_id = $this->input['company_id'];
        $user_id = 42330;

        $data = $this->tenantDB()
            ->table('chsone_purchase_form AS cpf')
            ->whereIn('cpf.purchase_form_id', $po_id)
            ->where('cpf.soc_id', $company_id)
            ->get();

            if (count($data) === 0 || $data === null){
                $this->status = 'error';
            $this->message = 'Unable to Fetch Purchase Form Data';
            $this->statusCode = 400;
            return;
        }

        $response_data = [];

        foreach ($data as $key => $value) {

            if ($value->status === 4) {
                $response_data[] = [
                    'purchase_form_id' => $value->purchase_form_id,
                    'status' => 'error',
                    'message' => $value->purchase_form_title . ' ' . 'Already Reviewed',
                    'statusCode' => 400
                ];
                $this->data = $response_data;
                return;

            } else if ($value->status !== 3) {
                $response_data[] = [
                    'purchase_form_id' => $value->purchase_form_id,
                    'status' => 'error',
                    'message' => $value->purchase_form_title . ' ' . 'Not approved Yet',
                    'statusCode' => 400
                ];
                $this->data = $response_data;
                return;
            } else if ($value->status === 2 || $value->status === 5) {
                $response_data[] = [
                    'purchase_form_id' => $value->purchase_form_id,
                    'status' => 'error',
                    'message' => $value->purchase_form_title . ' ' . 'Cannot be Reviewed',
                    'statusCode' => 400
                ];
                $this->data = $response_data;
                return;
            }else{

            $reviewer_data = $this->tenantDB()
                ->table('purchase_order_reviewers AS poa')
                ->where('poa.purchase_order_id', $value->purchase_form_id)
                ->where('poa.user_id', $user_id)
                ->get();

            if (count($reviewer_data) == 0) {
                $response_data[] = [
                    'purchase_form_id' => $value->purchase_form_id,
                    'status' => 'error',
                    'message' => $value->purchase_form_title . ' ' . 'have not assigned to you',
                    'statusCode' => 400
                ];
                $this->data = $response_data;
                return;
            } else {

                $statuses = collect($reviewer_data)->pluck('status');
                $statusNotOneCount = $statuses->reject(function ($item) {
                    return $item === 1;
                })->count();

                if ($statusNotOneCount === 1) {
                    $this->tenantDB()->table('chsone_purchase_form')
                        ->where('purchase_form_id', $value->purchase_form_id)
                        ->update([
                                'status' => 4,
                            ]);

                    $this->tenantDB()->table('purchase_order_reviewers')
                        ->where('purchase_order_id', $value->purchase_form_id)
                        ->where('user_id', $user_id)
                        ->update([
                                'status' => 1,
                            ]);

                    $response_data[] = [
                        'purchase_form_id' => $value->purchase_form_id,
                        'status' => 'success',
                        'message' => $value->purchase_form_title . ' ' . 'Review successfully',
                        'statusCode' => 200
                    ];
                } else {
                    $this->tenantDB()->table('purchase_order_reviewers')
                        ->where('purchase_order_id', $value->purchase_form_id)
                        ->where('user_id', $user_id)
                        ->update([
                                'status' => 1,
                            ]);

                    $response_data[] = [
                        'purchase_form_id' => $value->purchase_form_id,
                        'status' => 'success',
                        'message' => $value->purchase_form_title . ' ' . 'Review successfully',
                        'statusCode' => 200
                    ];
                }
            }
        }
    }

        $this->data = $response_data;
    }


}
