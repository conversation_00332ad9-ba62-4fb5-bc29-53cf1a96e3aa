<?php

namespace App\Console\Commands\Actions\Expenses;

use App\Console\Commands\Action;

class PurchaseFormApproveDataSource extends Action
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'datasource:purchaseFormApprove {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Approve the Purchase Form Data Source';


    /**
     * Execute the console command.
     */

    // SELECT soc_db_412.cpf.*, soc_db_412.poa.*
    // FROM soc_db_412.chsone_purchase_form AS cpf
    // JOIN soc_db_412.purchase_order   _approvers AS poa 
    // ON poa.purchase_order_id = cpf.purchase_form_id 
    // AND poa.user_id = 3729;

    public function apply()
    {

        $po_id = $this->input['id'];
        $company_id = $this->input['company_id'];
        $user_id = $this->input['user_id'];


        $data = $this->tenantDB()
            ->table('chsone_purchase_form AS cpf')
            ->whereIn('cpf.purchase_form_id', $po_id)
            ->where('cpf.soc_id', $company_id)
            ->get();

        if (count($data) === 0 || $data === null) {
            $this->status = 'error';
            $this->message = 'Unable to Fetch Purchase Form Data';
            $this->statusCode = 400;
            return;
        }

        $response_data = [];


        foreach ($data as $key => $value) {

            if ($value->status === 3 || $value->status === 1) {
                $response_data[] = [
                    'purchase_form_id' => $value->purchase_form_id,
                    'status' => 'error',
                    'message' => $value->purchase_form_title . ' ' . 'Already Approved',
                    'statusCode' => 400
                ];
                $this->data = $response_data;


            } else if ($value->status !== 0) {
                $response_data[] = [
                    'purchase_form_id' => $value->purchase_form_id,
                    'status' => 'error',
                    'message' => $value->purchase_form_title . ' ' . 'Cannot be approved',
                    'statusCode' => 400
                ];
                $this->data = $response_data;
            } else {

                $approver_data = $this->tenantDB()
                    ->table('purchase_order_approvers AS poa')
                    ->where('poa.purchase_order_id', $value->purchase_form_id)
                    ->where('poa.user_id', $user_id)
                    ->get();


                if (count($approver_data) == 0) {
                    $response_data[] = [
                        'purchase_form_id' => $value->purchase_form_id,
                        'status' => 'error',
                        'message' => $value->purchase_form_title . ' ' . 'have not assigned to you',
                        'statusCode' => 409
                    ];
                    $this->data = $response_data;
                } else {

                    $statuses = collect($approver_data)->pluck('status');
                    $statusNotOneCount = $statuses->reject(function ($item) {
                        return $item === 1;
                    })->count();

                    if ($statusNotOneCount === 1) {


                        $reviewer_data = $this->tenantDB()
                            ->table('purchase_order_approvers AS poa')
                            ->where('poa.purchase_order_id', $value->purchase_form_id)
                            ->get();

                        if (count($reviewer_data) === 0) {
                            $this->tenantDB()->table('chsone_purchase_form')
                                ->where('purchase_form_id', $value->purchase_form_id)
                                ->update([
                                    'status' => 1,
                                ]);
                        } else {
                            $this->tenantDB()->table('chsone_purchase_form')
                                ->where('purchase_form_id', $value->purchase_form_id)
                                ->update([
                                    'status' => 3,
                                ]);
                        }

                        $this->tenantDB()->table('purchase_order_approvers')
                            ->where('purchase_order_id', $value->purchase_form_id)
                            ->where('user_id', $user_id)
                            ->update([
                                'status' => 1,
                            ]);


                        $response_data[] = [
                            'purchase_form_id' => $value->purchase_form_id,
                            'status' => 'success',
                            'message' => $value->purchase_form_title . ' ' . 'Approved successfully',
                            'statusCode' => 200
                        ];
                    } else {
                        $this->tenantDB()->table('purchase_order_approvers')
                            ->where('purchase_order_id', $value->purchase_form_id)
                            ->where('user_id', $user_id)
                            ->update([
                                'status' => 1,
                            ]);

                        $response_data[] = [
                            'purchase_form_id' => $value->purchase_form_id,
                            'status' => 'success',
                            'message' => $value->purchase_form_title . ' ' . 'Approved successfully',
                            'statusCode' => 200
                        ];
                    }
                }
            }
        }

        $this->data = $response_data;
    }


}
