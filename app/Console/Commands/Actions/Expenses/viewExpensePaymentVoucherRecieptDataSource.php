<?php

namespace App\Console\Commands\Actions\Expenses;

use Illuminate\Console\Command;
use App\Console\Commands\Action;
use Illuminate\Support\Facades\DB;

class viewExpensePaymentVoucherRecieptDataSource extends Action
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'datasource:viewExpensePaymentVoucherReciept {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Get the Expense Payment Tracker Details';

    protected $formatter = [
        'id' => '',
        'soc_id' => '',
        'vendor_id' => '',
        'invoice_number' => '',
        'payment_number' => '',
        'bill_type' => '',
        'payment_mode' => '',
        'transaction_reference' => '',
        'payment_instrument' => '',
        'paid_by' => '',
        'total_due_amount' => '',
        'writeoff_amount' => '',
        "payment_amount" => "getAmountPaid:payment_amount,writeoff_amount",
        'other_information' => '',
        'status' => '',
        'transaction_status' => '',
        'payment_token' => '',
        'payment_note' => '',
        'payment_mode' => '',
        'transaction_reference' => '',
        'reversal_note' => '',
        'payment_date' => '',
        'cheque_date' => '',
        'created_date' => '',
        'vendor_name' => '',
        'invoice_particulars' => '',
        'total_due_amount_in_words' => '',
        'et_type_name' => ''
    ];

    protected $formatterByKeys = ['id'];

    protected $mapper = [
        'id' => 'vendorpayment.id',
    ];

    /**
     * Execute the console command.
     */
    public function apply()
    {
        try {
            $obj = $this->tenantDB()->table('expense_invoice_payment_tracker AS vendorpayment')
                ->select([
                    'vendorpayment.id',
                    'vendorpayment.soc_id',
                    'vendorpayment.vendor_id',
                    'vendorpayment.invoice_number',
                    'vendorpayment.payment_number',
                    'vendorpayment.bill_type',
                    'vendorpayment.payment_mode',
                    'vendorpayment.transaction_reference',
                    'vendorpayment.payment_instrument',
                    'vendorpayment.paid_by',
                    'vendorpayment.total_due_amount',
                    'vendorpayment.writeoff_amount',
                    'vendorpayment.payment_amount',
                    'vendorpayment.other_information',
                    'vendorpayment.status',
                    'vendorpayment.transaction_status',
                    'vendorpayment.payment_token',
                    'vendorpayment.payment_note',
                    'vendorpayment.payment_mode',
                    'vendorpayment.transaction_reference',
                    'vendorpayment.reversal_note',
                    'vendorpayment.payment_date',
                    'vendorpayment.cheque_date',
                    'vendorpayment.created_date',
                    'vendor.vendor_name',
                    DB::raw('CASE WHEN vendorpayment.bill_type = "Advance" THEN "Advance Payment" ELSE COALESCE(GROUP_CONCAT(DISTINCT expense.et_type_name SEPARATOR ", "), "") END AS et_type_names'),
                    DB::raw('GROUP_CONCAT(DISTINCT CONCAT("Voucher No.", particulars.bill_id, "- ", IFNULL(particulars.particular, "-")) SEPARATOR "\n") as invoice_particulars')
                ])
                ->leftJoin('chsone_vendors_master AS vendor', 'vendor.vendor_id', '=', 'vendorpayment.vendor_id')
                ->leftJoin('chsone_vendor_bill_particulars AS particulars', function ($join) {
                    $join->on(DB::raw('FIND_IN_SET(particulars.bill_id, vendorpayment.invoice_number)'), '>', DB::raw('0'));
                })
                ->leftJoin('chsone_expense_tracker AS expense',  function ($join) {
                    $join->on('particulars.et_id', '=', 'expense.et_id');
                })
                ->where('vendorpayment.id', $this->input['id'])
                ->groupBy('vendorpayment.id');

            $result = $obj->first();
                
            $result->total_due_amount_in_words = $this->convertToIndianCurrency($result->payment_amount);
            $result->invoice_particulars = $this->updateItemDetails($result->invoice_number, $result->invoice_particulars);
            if (empty($result->invoice_particulars)) {
                $result->invoice_particulars = $result->payment_note;
            }
            $result->payment_amount = number_format($result->payment_amount, 2);
            
            // if( array_key_exists('type', $this->input) && $this->input['type'] == 'pdf'){
            $soc_id = $result->soc_id;
            $society = $this->masterDB()->table('chsone_societies_master')->select('soc_name', 'soc_address_1', 'soc_address_2', 'soc_landmark', 'soc_city_or_town', 'soc_state', 'soc_pincode')->where('soc_id', $soc_id)->first();
            $result->soc_address = $society->soc_address_1 . ', ' . $society->soc_address_2 . ', ' . $society->soc_landmark . ', ' . $society->soc_city_or_town . ', ' . $society->soc_state . ', ' . $society->soc_pincode . '.';
            $result->soc_address = str_replace(', ,', ',', $result->soc_address);
            $result->soc_address = str_replace(' ,', ',', $result->soc_address);
            
            $result->soc_name = $society->soc_name;
            $result->soc_reg_no = $this->input['additional_data']['society_reg_num'];
            
            // }
            $result->payment_date = date('d/m/Y', strtotime($result->payment_date));
            //if payment_mode is cash the Cash else get invoice_number and find using where in, in chsone_vendor_bill_payment_details tables
            $other_info = unserialize($result->other_information);

            if ($result->payment_mode == 'cash') {
                $result->payment_mode = optional(
                    $this->tenantDB()
                        ->table('chsone_grp_ledger_tree')
                        ->select('ledger_account_name')
                        ->where('ledger_account_id', $other_info['cash_ledger'])
                        ->first()
                )->ledger_account_name;
            } else {
                $result->payment_mode = $this->tenantDB()->table('chsone_grp_ledger_tree')->select('ledger_account_name')->where('ledger_account_id', $other_info['bank_ledger'])->first()->ledger_account_name;
            }
            
            $this->data = $result;
        } catch (\Exception $e) {
            $this->statusCode = 400;
            $this->status = 'error';
            $this->message = "Error in getting Expense Payment Tracker Details: " . $e->getMessage() . ", at " . $e->getLine() . " in " . $e->getFile();
        }
    }

    public function getAmountPaid($payment_amount, $writeoff_amount)
    {
        $writeoff_amount = (float) $writeoff_amount;
        $payment_amount = (float) $payment_amount;

        if (empty($writeoff_amount)) {
            return '₹ ' . number_format($payment_amount, 2);
        }

        return '₹ ' . number_format($payment_amount, 2) . '(' . $writeoff_amount . ')';
    }

    public function convertToIndianCurrency($number)
    {
        $number_parts = explode('.', number_format($number, 2, '.', ''));
        $rupees = (int)$number_parts[0];
        $paise = isset($number_parts[1]) ? (int)$number_parts[1] : 0;

        $words = $this->convertNumberToWords($rupees) . ' Rupees';

        if ($paise > 0) {
            $words .= ' And ' . $this->convertNumberToWords($paise) . ' Paise';
        }

        return $words;
    }

    public function convertNumberToWords($num)
    {
        $ones = [
            0 => '',
            1 => 'One',
            2 => 'Two',
            3 => 'Three',
            4 => 'Four',
            5 => 'Five',
            6 => 'Six',
            7 => 'Seven',
            8 => 'Eight',
            9 => 'Nine',
            10 => 'Ten',
            11 => 'Eleven',
            12 => 'Twelve',
            13 => 'Thirteen',
            14 => 'Fourteen',
            15 => 'Fifteen',
            16 => 'Sixteen',
            17 => 'Seventeen',
            18 => 'Eighteen',
            19 => 'Nineteen'
        ];

        $tens = [
            0 => '',
            1 => 'Ten',
            2 => 'Twenty',
            3 => 'Thirty',
            4 => 'Forty',
            5 => 'Fifty',
            6 => 'Sixty',
            7 => 'Seventy',
            8 => 'Eighty',
            9 => 'Ninety'
        ];

        $thousands = [
            '',
            'Thousand',
            'Lakh',
            'Crore'
        ];

        if ($num == 0) {
            return 'Zero';
        }

        $num_str = (string)$num;
        $num_length = strlen($num_str);
        $levels = (int)(($num_length + 2) / 3);
        $max_length = $levels * 3;
        $num_str = substr('00' . $num_str, -$max_length);
        $num_levels = str_split($num_str, 3);

        $words = [];
        for ($i = 0; $i < count($num_levels); $i++) {
            $levels--;
            $hundreds = (int)($num_levels[$i] / 100);
            $hundreds = ($hundreds ? $ones[$hundreds] . ' Hundred ' : '');
            $tens_ones = (int)($num_levels[$i] % 100);
            $tens_words = '';

            if ($tens_ones < 20) {
                $tens_words = $ones[$tens_ones];
            } else {
                $tens_ones_tens = (int)($tens_ones / 10);
                $tens_ones_ones = $tens_ones % 10;
                $tens_words = $tens[$tens_ones_tens];
                if ($tens_ones_ones) {
                    $tens_words .= ' ' . $ones[$tens_ones_ones];
                }
            }

            $words[] = trim($hundreds . $tens_words) . ' ' . $thousands[$levels];
        }

        return implode(' ', array_filter($words));
    }

    public function updateItemDetails($invoiceNumbers, $itemDetails)
    {
        // Split the invoice numbers into an array
        $invoiceArray = explode(',', $invoiceNumbers);

        // Initialize an associative array to store the item details for each invoice number
        $invoiceItems = [];

        // Use a regular expression to match the new format
        preg_match_all('/Voucher No\.(\d+)-\s*([^\s]+)/', $itemDetails, $matches, PREG_SET_ORDER);

        // Loop through each match
        foreach ($matches as $match) {
            $invoice = $match[1];  // Extract the invoice number
            $item = $match[2];     // Extract the item detail

            // Check if the invoice number is in the invoice numbers array
            if (in_array($invoice, $invoiceArray)) {
                // Append the item to the invoice's list of items
                if (!isset($invoiceItems[$invoice])) {
                    $invoiceItems[$invoice] = [];
                }
                $invoiceItems[$invoice][] = trim($item);
            }
        }

        // Initialize the result array
        $result = [];

        // Loop through each invoice number
        foreach ($invoiceArray as $invoice) {
            // Check if we have any items for this invoice number
            if (isset($invoiceItems[$invoice])) {
                // Append the invoice number and the items, separated by commas
                $result[] = 'Voucher No.' . $invoice . '- ' . implode(', ', $invoiceItems[$invoice]) . "\n";
            }
        }

        // Return the result as a single string
        return implode(' ', $result);
    }
}
