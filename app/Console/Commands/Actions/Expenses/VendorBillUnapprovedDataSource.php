<?php

namespace App\Console\Commands\Actions\Expenses;

use Illuminate\Console\Command;
use App\Console\Commands\Action;
use Carbon\Carbon;

class VendorBillUnapprovedDataSource extends Action
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'datasource:VendorBillUnapproved {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Get the unapproved vendor bill list';

    protected $formatter = [
        'id' => '',
        'vendor_id' => '',
        'vendor_name' => '',
        'vendor_bill_type_purchase' => '',
        'vendor_bill_num' => '',
        'vendor_bill_date' => '',
        'vendor_bill_amount' => '',
        'vendor_bill_tds' => '',
        'vendor_bill_due_date' => '',
        'payment_status' => '',
        'status' => '',
        'is_rcm' => '',
        'vendor_bill_amount' => '',
        'vendor_bill_amount_final' => 'add:vendor_bill_amount,vendor_bill_tds',
    ];

    protected $formatterByKeys = ['id'];

    protected $mapper = [
        'id' => 'cvbm.vendor_bill_id',
    ];

    protected $schema = [
        "table" => [
            "tableTitle" => "Purchases/Expenses",
            "tabs" => [
                "Approved",
                "Unapproved",
                "Rejected"
            ],
            "select_by" => [
                "vendor_name" => "Vendor Name",
                "vendor_bill_num" => "Vendor Bill Number"
            ],
            "filter_by" => [
                "status" => [
                    "title" => "Status",
                    "options" => [
                        "cancelled" => "Cancelled"
                    ]
                ],
                "payment_status" => [
                    "title" => "Payment Status",
                    "options" => [
                        "paid" => "Paid",
                        "unpaid" => "Unpaid",
                        "partialpaid" => "Partialpaid"
                    ]
                ],
                "purchase_mode" => [
                    "title" => "Purchase Mode",
                    "options" => [
                        "credit" => "Credit",
                        "cash" => "Cash"
                    ]
                ],
                "vendor_bill_due_date" => [
                    "title" => "Vendor Bill Due Date",
                    "options" => [
                        "overdue" => "Overdue",
                        "due_in_one_month" => "Due in One month",
                        "due_in_one_week" => "Due in One Week"
                    ]
                ],
                "is_rcm" => [
                    "title" => "RCM"
                ],
                "financial_year" => [
                    "title" => "Financial Year",
                    "options" => [
                        "2019-04-01 to 2020-03-31" => "2019-04-01 to 2020-03-31",
                        "2020-04-01 to 2021-03-31" => "2020-04-01 to 2021-03-31",
                        "2021-04-01 to 2022-03-31" => "2021-04-01 to 2022-03-31"
                    ]
                ]
            ],
            "actions" => [
                [
                    "title" => "New Payment",
                    "icon" => "ri-add-circle-line",
                    "color" => "success",
                    "variant" => "contained",
                    "form" => "vendorNewPayment",
                ],
                [
                    "title" => "New Cash Purchase",
                    "icon" => "ri-add-circle-line",
                    "color" => "primary",
                    "redirect" => "/admin/vendorbill/addBill/",
                    "variant" => "contained"
                ],
                [
                    "title" => "New vendor bill",
                    "icon" => "ri-add-circle-line",
                    "color" => "primary",
                    "redirect" => "/admin/vendorbill/addVendorBill/",
                    "variant" => "contained"
                ]
            ],
            "fields" => [
                "*"
            ],
            "columns" => [
                [
                    "title" => "Voucher No",
                    "key" => "id",
                    "type" => "link",
                    "href" => "/admin/vendorbill/vendorBillDetails/:id"
                ],
                [
                    "title" => "Bill No",
                    "key" => "vendor_bill_num",
                    "type" => "link",
                    "href" => "/admin/vendorbill/vendorBillDetails/:id"
                ],
                [
                    "title" => "Bill Date",
                    "key" => "vendor_bill_date"
                ],
                [
                    "title" => "Vendor Name",
                    "key" => "vendor_name",
                    "type" => "chip_with_string",
                    "options" => [
                        "1" => [
                            "title" => "RCM",
                            "color" => "info"
                        ],
                        "0" => [
                            "title" => "",
                            "color" => "default"
                        ]
                    ]
                ],
                [
                    "title" => "Purchase Mode",
                    "key" => "vendor_bill_type_purchase"
                ],
                [
                    // "title" => "Bill Amt",
                    "key" => "vendor_bill_amount_final",
                    "type" => "colored",
                    "options" => [
                        "vendor_bill_amount_final" => [
                            "title" => "Bill Amt",
                            "type" => "number",
                            "currency" => "inr"
                        ],
                        "(" => [
                            "title" => " (",
                            "color" => "error",
                            "value" => " ("
                        ],
                        "vendor_bill_tds" => [
                            "title" => "TDS",
                            "color" => "error"
                        ],
                        ")" => [
                            "title" => ")",
                            "color" => "error",
                            "value" => ")"
                        ]
                        // ,
                        // "rs" => [
                        //     "title" => "₹"
                        // ]
                    ]
                ],
                [
                    "title" => "Due Date",
                    "key" => "vendor_bill_due_date",
                    "conditions" => [
                        [
                            "key" => "payment_status",
                            "operator" => "equal",
                            "value" => "unpaid",
                            "color" => "error"
                        ]
                    ]
                ],
                [
                    "title" => "Payment Status",
                    "key" => "payment_status",
                    "type" => "chip",
                    "options" => [
                        "paid" => [
                            "title" => "Paid",
                            "color" => "success"
                        ],
                        "unpaid" => [
                            "title" => "Unpaid",
                            "color" => "error"
                        ],
                        "partialpaid" => [
                            "title" => "Partialpaid",
                            "color" => "warning"
                        ]
                    ]
                ],
                [
                    "title" => "Actions",
                    "type" => "actions",
                    "key" => "actions",
                    "actions" => [
                        [
                            "title" => "Edit",
                            "icon" => "ri-edit-box-line",
                            "href" => "/admin/vendorbill/editVendorBill/:id",
                        ],
                        [
                            "title" => "Approve",
                            "icon" => "ri-check-line",
                            "api" => [
                                "method" => "put",
                                "url" => "/admin/vendorbill/changeSingleStatus/:id/1",
                                "redirect" => "/admin/vendorbill/vendorBill",
                                "data" => [
                                    "status" => 1
                                ]
                            ]
                        ],
                        [
                            "title" => "Reject",
                            "icon" => "ri-close-line",
                            "api" => [
                                "method" => "put",
                                "url" => "/admin/vendorbill/changeSingleStatus/:id/2",
                                "redirect" => "/admin/vendorbill/vendorBill",
                                "data" => [
                                    "status" => 2
                                ]
                            ]
                        ]
                    ]
                ]
            ]
        ]
    ];

    /**
     * Execute the console command.
     */
    public function apply()
    {
        // $vendorId = $this->input['id'];
        if (isset($this->input['current_tab'])) {
            $CurrentTab = $this->input['current_tab'];
        } else {
            $CurrentTab = 'approved';
        }
        $vendorBillNo = '';
        $paymentStatus = '';
        $purchaseMode = '';
        $isRcm = '';
        $sortBy = '';
        $sortByKey = '';
        $sortByValue = '';
        $vendorBillDueDate = '';
        $vendorName = '';
        $financialYear = '';
        $status = '';

        if (!empty($this->input['filters'])) {

            $vendorBillNo =
                !empty($this->input['filters']['vendor_bill_num']) ? $this->input['filters']['vendor_bill_num'] : '';
            unset($this->input['filters']['vendor_bill_num']);

            $vendorName =
            !empty($this->input['filters']['vendor_name']) ? $this->input['filters']['vendor_name'] : '';
        unset($this->input['filters']['vendor_name']);

            $isRcm = isset($this->input['filters']['is_rcm']) ? explode(',', $this->input['filters']['is_rcm']) : [];

            $paymentStatus = isset($this->input['filters']['payment_status']) ? explode(',', $this->input['filters']['payment_status']) : [];

            $purchaseMode = isset($this->input['filters']['purchase_mode']) ? explode(',', $this->input['filters']['purchase_mode']) : [];

            $vendorBillDueDate = isset($this->input['filters']['vendor_bill_due_date']) ? explode(',', $this->input['filters']['vendor_bill_due_date']) : [];

            $financialYear = isset($this->input['filters']['financial_year']) ? explode(',', $this->input['filters']['financial_year']) : [];
            $status = isset($this->input['filters']['status']) ? explode(',', $this->input['filters']['status']) : [];

        }

        // add pagination
        $page = $this->input['page'] ?? 1;
        $per_page = !empty($this->input['per_page']) ? $this->input['per_page']: ($this->hugeData ? 100000 : 10);
        $offset = ($page - 1) * $per_page;

        $obj = $this->tenantDB()->table('chsone_vendor_bill_master as cvbm')
            ->select('cvbm.vendor_bill_id as id', 'cvbm.vendor_id', 'vendor_bill_type_purchase', 'vendor_bill_num', 'vendor_bill_date', 'vendor_bill_amount', 'vendor_bill_amount as vendor_bill_amount_final', 'vendor_bill_tds', 'vendor_bill_due_date', 'payment_status', 'cvbm.status', 'cvbm.is_rcm')
            ->leftJoin('chsone_vendors_master as cvm', 'cvm.vendor_id', '=', 'cvbm.vendor_id')
            ->whereIn('is_billable', [0, 1])
            ->where('cvbm.status', 0)
            ->where('vendor_bill_num', '!=', '')
            // ->whereNull('is_billed')
            ->orderByDesc('vendor_bill_date')
            ->selectRaw('ifnull(vendor_name, "Petty Cash") as vendor_name');



        if ($vendorName) {
            $obj = $obj->where('vendor_name', 'like', '%' . $vendorName . '%');
        }

        if (isset($this->input['sort']) && is_array($this->input['sort'])) {
            $sortBy = $this->input['sort'];
            $sortByKey = array_key_first($sortBy);
            $sortByValue = $sortBy[$sortByKey];
            unset($this->input['sort']);
        }

        if ($sortBy) {
            $obj = $obj->orderBy($sortByKey, $sortByValue);
        }

        if ($vendorBillNo) {
            $obj = $obj->where('vendor_bill_num', 'like', '%' . $vendorBillNo . '%');
        }

        if ($isRcm) {
            $obj = $obj->where('bill_master.is_rcm', $isRcm);
        }

        if ($paymentStatus) {
            $obj->whereIn('payment_status', $paymentStatus);
        }else{
            // $obj = $obj->when($CurrentTab === 'unapproved', function($q) {
            //     return $q->where('payment_status', 'unpaid');
            // })
            $obj = $obj->when($CurrentTab === 'approved', function($q) {
                    return $q->where('payment_status', 'paid');
                });
        }

        if ($purchaseMode) {
            $obj->whereIn('vendor_bill_type_purchase', $purchaseMode);
        }

        if($status) {
            $obj = $obj->whereIn("cvbm.status", $status);
        }

        if ($vendorBillDueDate) {
            $obj = $obj->where(function($query) use ($vendorBillDueDate) {
                foreach ($vendorBillDueDate as $status) {
                    if ($status === 'overdue') {
                        $query->orWhere(function($subQuery) {
                            $subQuery->where('vendor_bill_due_date', '<', Carbon::now())
                                     ->where(function($subQuery) {
                                         $subQuery->where('payment_status', 'unpaid')
                                                  ->orWhere('payment_status', 'partialpaid');
                                     });
                        });
                    }
                    if ($status === 'due_in_one_month') {
                        $query->orWhere(function($subQuery) {
                            $subQuery->whereRaw("vendor_bill_due_date BETWEEN CURDATE() AND DATE_ADD(CURDATE(), INTERVAL 1 MONTH)")
                                     ->where(function($subQuery) {
                                         $subQuery->where('payment_status', 'unpaid')
                                                  ->orWhere('payment_status', 'partialpaid');
                                     });
                        });
                    }
                    if ($status === 'due_in_one_week') {
                        $query->orWhere(function($subQuery) {
                            $subQuery->whereRaw("vendor_bill_due_date BETWEEN CURDATE() AND DATE_ADD(CURDATE(), INTERVAL 1 WEEK)")
                                     ->where(function($subQuery) {
                                         $subQuery->where('payment_status', 'unpaid')
                                                  ->orWhere('payment_status', 'partialpaid');
                                     });
                        });
                    }

                }
            });
        }

        if ($financialYear) {
            $obj->where(function($query) use ($financialYear) {
                foreach ($financialYear as $range) {
                    $year = explode(' to ', $range);
                    $query->orWhereBetween('vendor_bill_date', [$year[0], $year[1]]);
                }
            });
        }

        // add count and pagination
        $count = $obj->count();

        $obj = $obj->offset($offset);
        $obj = $obj->limit($per_page);

        $result = $obj->get();

        // set financial year options in the schema
        $AccountSetTracker = $this->action('datasource:AccountSet', $this->pointer, $this->input);
        // create a foreach loop for set financial year options to the schema
        $years = [];
        foreach($AccountSetTracker as $value) {
            $years[$value['fy_start_date'] . ' to ' . $value['fy_end_date']] = $value['fy_start_date'] . ' to ' . $value['fy_end_date'];
        }

        $this->data = $this->format($result->toArray());
        $this->meta['schema'] = $this->schema;
        $this->meta['schema']['table']['filter_by']['financial_year']['options'] = $years;
        $this->meta['pagination']['total'] = $count;

    }

    public function add($a, $b)
    {
        return (float)$a + (float)$b;
    }

    public function setRupees($value)
    {
        $value = (float) $value;
        // return '₹ ' . number_format($value, 2);
        return $value;
    }
}
