<?php

namespace App\Console\Commands\Actions\Expenses;

use App\Console\Commands\Action;

class PurchaseFormItemsDataSource extends Action
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'datasource:purchaseFormItems {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Purchase Form Items Data Source';


    /**
     * Execute the console command.
     */
    public function apply()
    {

        $soc_id = (int) $this->input["company_id"];
        $purchase_form_id = $this->input["id"];

        $purchase_form_items = $this->tenantDB()->table('chsone_purchase_form_items')
        ->where('soc_id', $soc_id)->where('purchase_form_id', $purchase_form_id)->get();

        if ($purchase_form_items->isEmpty()) {
            // $this->status = 'error';
            // $this->message = 'Unable to Fetch Purchase Form Data';
            // $this->statusCode = 400;
            // return;
            $this->data = [];
        }else{
            $purchase_form_items = $purchase_form_items->map(function ($item) {
                return [
                    'id' => $item->id,
                    'item_name' => $item->item_name,
                    'item_cost' => number_format($item->item_cost, 2),
                    'item_quantity' => $item->item_quantity,
                    'total_cost' =>number_format($item->item_cost * $item->item_quantity, 2) ,
                ];
            });

            $this->data = $purchase_form_items;
        }




    }
}
