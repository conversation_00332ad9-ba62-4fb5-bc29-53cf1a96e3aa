<?php

namespace App\Console\Commands\Actions\Helpdesk\Complaints;

use App\Console\Commands\Action;
use App\Helpers\UserHelper;
use Illuminate\Support\Facades\DB;

class IssueListDataSource extends Action
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'datasource:issueList {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */

    protected $description = 'Get the helpdesk issues data source';

    /**
     * Execute the console command.
     */
    public function apply()
    {
        $page = $this->input['page'] ?? 1;
        $per_page = !empty($this->input['per_page']) ? $this->input['per_page'] : ($this->hugeData ? 100000 : 10);
        $offset = ($page - 1) * $per_page;
        $soc_id = $this->input['company_id'];
        $ticket_number = '';
        $subject = '';
        $member_name = '';
        $unit_flat_number = '';
        $soc_building_name = '';
        $unit_building_name = '';
        $status = [];
        $priority = [];

        $searchTerm = $this->input['filters']['search'] ?? '';

        if (isset($this->input['filters'])) {
            $ticket_number =
            !empty($this->input['filters']['ticket_number']) ? $this->input['filters']['ticket_number'] : '';
            unset($this->input['filters']['ticket_number']);

            $subject =
            !empty($this->input['filters']['subject']) ? $this->input['filters']['subject'] : '';
            unset($this->input['filters']['subject']);

            $member_name =
            !empty($this->input['filters']['member_name']) ? $this->input['filters']['member_name'] : '';
            unset($this->input['filters']['member_name']);

            $unit_flat_number =
            !empty($this->input['filters']['unit_flat_number']) ? $this->input['filters']['unit_flat_number'] : '';
            unset($this->input['filters']['unit_flat_number']);

            $soc_building_name =
            !empty($this->input['filters']['soc_building_name']) ? $this->input['filters']['soc_building_name'] : '';
            unset($this->input['filters']['soc_building_name']);

            $unit_building_name =
            !empty($this->input['filters']['unit_building_name']) ? $this->input['filters']['unit_building_name'] : '';
            unset($this->input['filters']['unit_building_name']);

            $status = isset($this->input['filters']['status']) ? explode(',', $this->input['filters']['status']) : [];
            $priority = isset($this->input['filters']['priority']) ? explode(',', $this->input['filters']['priority']) : [];

        }

        $userHelper = new UserHelper();
        $obj = $this->tenantDB()->table('helpdesk_issues2 as helpdesk_issues')
            ->select(
                'helpdesk_issues.issue_id as id',
                'helpdesk_issues.ticket_number',
                DB::raw("
                CONCAT(
                    '#',
                    helpdesk_issues.ticket_number
                ) AS ticket_number
            ")
            ,
                'helpdesk_issues.created_date',
                'helpdesk_issues.fk_help_topic_id',
                'helpdesk_issues.title',
                'helpdesk_issues.raised_by_name',
                'helpdesk_issues.assignee_user',
                'helpdesk_issues.status',
                'helpdesk_issues.priority',
                'helpdesk_issues.is_overdue',
                'helpdesk_issues.is_answered',
                'helpdesk_issues.src',
                'memmaster.member_first_name',
                'memmaster.member_last_name',
                'unitmaster.soc_building_name',
                'unitmaster.unit_flat_number',
                'helptopics.help_topic',
                DB::raw("COUNT(attachments.file_name) as attachment_count"),
                DB::raw("
                CASE 
                    WHEN COUNT(attachments.file_name) > 0 
                    THEN CONCAT(helpdesk_issues.title, ' (', COUNT(attachments.file_name), ')') 
                    ELSE helpdesk_issues.title 
                END AS title_with_attachment_count
                "),        
                DB::raw("IF(attachments.file_name IS NOT NULL, 1, 0) as has_attachments"),
                DB::raw("CONCAT(UCASE(LEFT(helpdesk_issues.priority, 1)), LCASE(SUBSTRING(helpdesk_issues.priority, 2))) AS priority"),
                DB::raw("CONCAT(memmaster.member_first_name, ' ', memmaster.member_last_name, '(', unitmaster.soc_building_name, '-', unitmaster.unit_flat_number, ')') AS member_name_unit_building_name"),
                DB::raw("DATE_FORMAT(helpdesk_issues.created_date, '%d/%m/%Y') as created_date"),
                DB::raw("DATE_FORMAT(helpdesk_issues.updated_date, '%d/%m/%Y') as updated_date"),
            )
            ->leftJoin('chsone_members_master AS memmaster', 'helpdesk_issues.raised_by', '=', 'memmaster.id')
            ->leftJoin('helpdesk_attachments as attachments', 'helpdesk_issues.issue_id', '=', 'attachments.fk_issue_id')
            ->leftJoin('chsone_units_master AS unitmaster', 'memmaster.fk_unit_id', '=', 'unitmaster.unit_id')
            ->leftJoin('helpdesk_help_topics AS helptopics', 'helpdesk_issues.fk_help_topic_id', '=', 'helptopics.help_topic_id')
            ->where('helpdesk_issues.is_active', 1)
            ->where('helpdesk_issues.raised_by', '!=', '')
            ->groupBy('helpdesk_issues.issue_id')
            ->orderBy('helpdesk_issues.created_date', 'desc');

        if ($ticket_number) {
            $obj = $obj->where('helpdesk_issues.ticket_number', 'like', '%' . $ticket_number . '%');
        }

        if ($subject) {
            $obj = $obj->where('helpdesk_issues.title', 'like', '%' . $subject . '%');
        }

        if ($member_name) {
            $obj = $obj->where('memmaster.member_first_name', 'like', '%' . $member_name . '%')
                ->orWhere('memmaster.member_last_name', 'like', '%' . $member_name . '%');
        }

        if ($unit_flat_number) {
            $obj = $obj->where('unitmaster.unit_flat_number', 'like', '%' . $unit_flat_number . '%');
        }

        if ($soc_building_name) {
            $obj = $obj->where('unitmaster.soc_building_name', 'like', '%' . $soc_building_name . '%');
        }

        if ($unit_building_name) {
            $obj = $obj->where(DB::raw("CONCAT(unitmaster.soc_building_name, ' - ', unitmaster.unit_flat_number)"), 'like', '%' . $unit_building_name . '%');
        }

        if ($status) {
            if ($status[0] == "onhold") {
                $obj = $obj->where(function ($query) {
                    $query->where('helpdesk_issues.status', 'onhold')
                          ->orWhere('helpdesk_issues.status', 'on hold');
                });            
            } else {
                $obj = $obj->whereIn("helpdesk_issues.status", $status);
            }
        }

        if ($priority) {
            $obj = $obj->whereIn('helpdesk_issues.priority', $priority);
        }

        $count = $obj->count();
        $obj = $obj->offset($offset);
        $obj = $obj->limit($per_page);

        $result = $obj->get();

        if($result->isEmpty()) {
            $this->data = [];
            $this->meta['pagination']['total'] = 0;
            return;
        }

        $assigneeUsers = $result->pluck('assignee_user')->unique()->toArray();

        // Step 3: Fetch user data for unique assignee_user values
        $userData = $this->MasterDB()->table('chsone_users_master as user')
            ->whereIn('user_id', $assigneeUsers)
            ->get();

        // Step 4: Map user data to issue data
        foreach ($result as $issue) {
            $user = $userData->where('user_id', $issue->assignee_user)->first();
            $issue->assignee_user = optional($user, function ($user) use ($userHelper) {
                return $userHelper->concat($user->user_first_name, $user->user_last_name);
            });
        }

        $this->data = $result;
        $this->meta['pagination']['total'] = $count;

    }

}
