<?php

namespace App\Console\Commands\Actions\Helpdesk\Complaints\IssueDetail;

use App\Console\Commands\Action;

class IssueDetailAssignIssueDataSource extends Action
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'datasource:issueDetailAssignIssue {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Get the helpdesk issues detail assign issue datasource';

    /**
     * Execute the console command.
     */
    public function apply()
    {
        $threadData = [
            'soc_id' => $this->input['company_id'],
            'parent_id' => 0,
            'fk_issue_id' => $this->input['issue_id'],
            'body' => $this->input['body'],
            'responder_type' => $this->input['responder_type'] ?? 'System',
            'responder_id' => $this->input['responder_id'] ?? 0,
            'response_date' => $this->getDate(date('Y-m-d H:i:s')),
            'response_time' => $this->getTime(date('Y-m-d H:i:s')),
            'thread_type' => $this->input['thread_type'] ?? 'private',
            'response_text' => $this->input['response_text'] ?? 'Assingment of complaint has been changed',
            'responder_ip' => $this->input['additional_data']['ip'] ?? '',
            'status' => 1,
            'reported_abuse' => 0,
            'created_date' => date('Y-m-d H:i:s'),
            'created_by' => $this->input['responder_id'] ?? 0,
            'updated_date' => date('Y-m-d H:i:s'),
            'updated_by' => $this->input['responder_id'] ?? 0
        ];

        $obj = $this->tenantDB()->table('helpdesk_threads1')->insert($threadData);

        if ($obj) {

            $this->tenantDB()->table('helpdesk_issues2')
                ->where('issue_id', $this->input['issue_id'])
                ->update([
                    'assignee' => $this->input['assignee'] ?? 0,
                    'assignee_user' => $this->input['assignee'] ?? 0,
                    'assignee_type' => $this->input['assignee_type'] ?? 'member',
                    'updated_date' => date('Y-m-d H:i:s'),
                    'updated_by' => $this->input['responder_id'] ?? 0,
                ]);

            $this->status = 'success';
            $this->message = 'Thread Created successfully';
            $this->statusCode = 200;
        } else {
            $this->status = 'error';
            $this->message = 'Unable to Create Thread';
            $this->statusCode = 400;
        }
    }

    public function getDate($created_date)
    {
        return date('Y-m-d', strtotime($created_date));
    }

    public function getTime($created_date)
    {
        return date('H:i:s', strtotime($created_date));
    }
}
