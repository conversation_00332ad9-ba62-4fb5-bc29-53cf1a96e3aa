<?php

namespace App\Console\Commands\Actions\Helpdesk\Complaints\IssueDetail;

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use App\Console\Commands\Action;

class IssueDetailAssignHelpTopicDataSource extends Action
{
    // signature
    protected $signature = 'datasource:issueDetailAssignHelpTopic {flowId} {parentId} {input}';

    // description
    protected $description = 'Issue Detail Assign Help Topic DataSource';


    public function apply()
    {
        try     {
            $issue_id = $this->input['issue_id'] ?? null;
            $fk_help_topic_id = $this->input['fk_help_topic_id'] ?? '';
            $status = $this->input['status'] ?? null;
            if (!$issue_id || !$fk_help_topic_id) {
                $this->status = 'error';
                $this->message = 'issue_id and fk_help_topic_id are required.';
                $this->statusCode = 400;
                return;
            }
    
            $update = [
                'fk_help_topic_id' => $fk_help_topic_id
            ];
            if ($status !== null) {
                $update['status'] = $status;
            }
            $db = $this->tenantDB();
            $schema = \Illuminate\Support\Facades\Schema::connection($db->getName());
            $issue = null;
            $table = null;
            if ($schema->hasTable('helpdesk_issues')) {
                $issue = $db->table('helpdesk_issues')->where('issue_id', $issue_id)->first();
                $table = $issue ? 'helpdesk_issues' : null;
            }
            if (!$issue && $schema->hasTable('helpdesk_issues1')) {
                $issue = $db->table('helpdesk_issues1')->where('issue_id', $issue_id)->first();
                $table = $issue ? 'helpdesk_issues1' : $table;
            }
            if (!$issue && $schema->hasTable('helpdesk_issues2')) {
                $issue = $db->table('helpdesk_issues2')->where('issue_id', $issue_id)->first();
                $table = $issue ? 'helpdesk_issues2' : $table;
            }
            if ($issue && $table) {
                $affected = $db->table($table)->where('issue_id', $issue_id)->update($update);
                if ($affected) {
                    $this->status = 'success';
                    $this->message = 'Help topic assigned successfully.';
                    $this->statusCode = 200;
                } else {
                    $this->status = 'error';
                    $this->message = 'Issue found but update failed.';
                    $this->statusCode = 400;
                }
            }
        } catch (\Exception $e) {
            dd($e);
        }
    }
}
