<?php

namespace App\Console\Commands\Actions\Helpdesk\Complaints\IssueDetail\AssignJobCard;

use App\Console\Commands\Action;

class IssueDetailAssignJobCardEditDataSource extends Action
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'datasource:issueDetailAssignJobCardEdit {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Get the helpdesk issues job card update data source';

    /**
     * Execute the console command.
     */
    public function apply()
    {
        $job_id = $this->input['job_id'];


        $jobCardUpdateData = [
            'attempted_options' => $this->input['attempted_options'],
            'job_options' => $this->input['job_options'],
            'job_card_scan_file' => $this->input['job_card_scan_file'],
            'updated_by' => $this->input['user_id']??0,
            'updated_date' => date('Y-m-d H:i:s')
        ];

        if ($this->input['job_options'] === "attempted") {

            if ($this->input['job_card_scan_file'] === "" || $this->input['job_card_scan_file'] === null || $this->input['job_card_scan_file'] === ':job_card_scan_file') {
                $this->status = 'error';
                $this->message = 'Please provide a valid job card scan file';
                $this->statusCode = 400;
                return;
            } 

            if ($this->input['attempted_options'] === "" || $this->input['attempted_options'] === null || $this->input['attempted_options'] === ':attempted_options') {
                $this->status = 'error';
                $this->message = 'Please provide a valid attempted options';
                $this->statusCode = 400;
                return;
            }

            $jobCardUpdateData['job_card_scan_file'] = $this->input['job_card_scan_file'];
            $jobCardUpdateData['attempted_options'] = $this->input['attempted_options'];

        }

        $obj = $this->tenantDB()->table('chsone_job_card_master')
            ->where('job_id', $job_id)
            ->update($jobCardUpdateData);

        if ($obj) {
            $this->status = 'success';
            $this->message = 'Job Card Updated successfully';
            $this->statusCode = 200;
        } else {
            $this->status = 'error';
            $this->message = 'Job Card Update failed';
            $this->statusCode = 400;
        }
    }
}
