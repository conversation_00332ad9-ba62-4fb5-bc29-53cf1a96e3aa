<?php

namespace App\Console\Commands\Actions\Helpdesk\Complaints\IssueDetail\AssignJobCard;

use App\Console\Commands\Action;

class IssueDetailAssignJobCardAddDataSource extends Action
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'datasource:issueDetailAssignJobCardAdd {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Get the helpdesk issues assign job card data source';

    /**
     * Execute the console command.
     */
    public function apply()
    {
        try{
                $obj = $this->tenantDB()->table('chsone_job_card_master')
                    ->insertGetId([
                        'soc_id' => $this->input['company_id'],
                        'issue_id' => $this->input['issue_id'],
                        'ticket_number' => $this->input['ticket_number'],
                        'handler_name' => $this->input['handler_name'],
                        'job_details' => $this->input['job_details'],
                        'visiting_date' => $this->input['visiting_date'],
                        'created_on' => date('Y-m-d H:i:s'),
                        'created_by' => $this->input['created_by'] ?? 0,
                        'created_date' => date('Y-m-d H:i:s'), // 'created_date' => date('Y-m-d H:i:s'), 'created_by' => $this->input['created_by'] ?? 0, 'updated_date' => date('Y-m-d H:i:s'), 'updated_by' => $this->input['updated_by'] ?? 0
                        'updated_date' => date('Y-m-d H:i:s'),
                        'updated_by' => $this->input['updated_by'] ?? 0
                    ]);

                $threadData = [
                    'soc_id' => $this->input['company_id'],
                    'parent_id' => 0,
                    'fk_issue_id' => $this->input['issue_id'],
                    'body' => $this->input['body'] ?? 'New Job Card #'.$obj.' created and assigned to '.$this->input['handler_name'],
                    'responder_type' => $this->input['responder_type'] ?? 'System',
                    'responder_id' => $this->input['responder_id'] ?? 0,
                    'response_date' => $this->getDate(date('Y-m-d H:i:s')),
                    'response_time' => $this->getTime(date('Y-m-d H:i:s')),
                    'thread_type' => $this->input['thread_type'] ?? 'private',
                    'response_text' => $this->input['response_text'] ?? 'New Job Card Added',
                    'responder_ip' => $this->input['additional_data']['ip'] ?? '',
                    'status' => 1,
                    'reported_abuse' => 0,
                    'created_date' => date('Y-m-d H:i:s'),
                    'created_by' => $this->input['responder_id'] ?? 0,
                    'updated_date' => date('Y-m-d H:i:s'),
                    'updated_by' => $this->input['responder_id'] ?? 0
                ];
            
                $obj = $this->tenantDB()->table('helpdesk_threads1')->insert($threadData);
            
                if ($obj) {
                    $this->status = 'success';
                    $this->message = 'Job Card Assign successfully';
                    $this->statusCode = 200;
                } else {
                    $this->status = 'error';
                    $this->message = 'Unable to Assign Job Card non member account';
                    $this->statusCode = 400;
                }
            }catch(\Exception $e){
                dd($e);
            }
    }

    public function getDate($created_date)
    {
        return date('Y-m-d', strtotime($created_date));
    }

    public function getTime($created_date)
    {
        return date('H:i:s', strtotime($created_date));
    }
}
