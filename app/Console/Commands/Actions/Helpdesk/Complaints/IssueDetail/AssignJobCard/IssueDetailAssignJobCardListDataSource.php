<?php

namespace App\Console\Commands\Actions\Helpdesk\Complaints\IssueDetail\AssignJobCard;

use App\Console\Commands\Action;


class IssueDetailAssignJobCardListDataSource extends Action
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'datasource:issueDetailAssignJobCardList {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Get the Assign Job Card List data source for helpdesk issues';

    /**
     * Execute the console command.
     */
    public function apply()
    {
        $issue_id = $this->input['issue_id'];

        $obj = $this->tenantDB()->table('chsone_job_card_master')
            ->select('handler_name', 'job_details', 'visiting_date', 'created_date', 'created_by', 'updated_date', 'updated_by', 'job_id', 'job_options', 'attempted_options', 'job_card_scan_file')
            ->where('issue_id', $issue_id)
            ->orderBy('created_date', 'desc')
            ->get();

        if (!$obj) {
            $this->status = 'error';
            $this->message = 'Unable to Fetch Help Topic Data';
            $this->statusCode = 400;
            return;
        }

        foreach ($obj as $job) {
            $job->created_date = $this->getDate($job->created_date);
            $job->visiting_date = $this->getDate($job->visiting_date);
        }

        $this->data = $obj;
    }

    public function getDate($created_date)
    {
        return date('d/m/Y', strtotime($created_date));
    }
}
