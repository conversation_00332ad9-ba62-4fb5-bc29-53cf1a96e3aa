<?php

namespace App\Console\Commands\Actions\Helpdesk\Complaints\IssueDetail\AssignJobCard;

use App\Console\Commands\Action;
use App\Helpers\UserHelper;
use Illuminate\Support\Facades\DB;

class IssueDetailAssignJobCardDetailDataSource extends Action
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'datasource:issueDetailAssignJobCardDetail {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Get the helpdesk issues assign job card detail data source';

    /**
     * Execute the console command.
     */
    public function apply()
    {
        try   {
            $job_id = $this->input['job_id'];
            $issue_id = $this->input['issue_id'];
    
            $jobCardDetail = $this->tenantDB()->table('chsone_job_card_master')
                ->select('handler_name', 'job_details', 'visiting_date', 'created_date', 'created_by', 'updated_date', 'updated_by', 'job_id', 'job_options', 'attempted_options', 'job_card_scan_file', 'ticket_number',
                DB::raw("DATE_FORMAT(visiting_date, '%d/%m/%Y') as visiting_date"),
                DB::raw("DATE_FORMAT(created_date, '%d/%m/%Y') as created_date"),
                DB::raw("DATE_FORMAT(updated_date, '%d/%m/%Y') as updated_date")
                )
                ->where('job_id', $job_id)
                ->where('issue_id', $issue_id)
                ->first();
    
                
            // Call issueDetailMemberDetail data source
            $memberDetail = $this->action('datasource:issueDetailMemberDetail', $this->pointer, $this->input);
            
            $assignee_user = '';
            if ($memberDetail['assignee_user'] != 'System') {
                $assignee_user = $memberDetail['assignee_user'];
            }

            $unitDetails = $this->tenantDB()->table('chsone_units_master')
                ->select('unit_flat_number', 'soc_building_name')
                ->where('unit_flat_number', $memberDetail['unit_flat_number'])
                ->first();
    
            $result = [
                'handler_name' => $jobCardDetail->handler_name,
                'job_details' => $jobCardDetail->job_details,
                'visiting_date' => $jobCardDetail->visiting_date,
                'created_date' => $jobCardDetail->created_date,
                'created_by' => $jobCardDetail->created_by,
                'updated_date' => $jobCardDetail->updated_date,
                'updated_by' => $jobCardDetail->updated_by,
                'job_id' => $jobCardDetail->job_id,
                'job_options' => $jobCardDetail->job_options,
                'attempted_options' => $jobCardDetail->attempted_options,
                'job_card_scan_file' => $jobCardDetail->job_card_scan_file,
                'raised_by_name' => $memberDetail['raised_by_name'],
                'assignee_user' => $assignee_user,
                'unit_flat_number' => $memberDetail['unit_flat_number'],
                'member_first_name' => $memberDetail['member_first_name'],
                'member_last_name' => $memberDetail['member_last_name'],
                'member_email_id' => $memberDetail['member_email_id'],
                'member_mobile_number' => $memberDetail['member_mobile_number'],
                'member_name_unit_building_name' => $memberDetail['member_name_unit_building_name'],
                'unit_flat_number' => $unitDetails->unit_flat_number,
                'soc_buidling_name' => $unitDetails->soc_building_name,
                'ticket_number' => $jobCardDetail->ticket_number,
            ];
            if ($jobCardDetail) {
                $this->status = 'success';
                $this->message = 'Job Card Detail';
                $this->statusCode = 200;
                $this->data = $result;
            } else {
                $this->status = 'error';
                $this->message = 'Unable to get Job Card Detail';
                $this->statusCode = 400;
                $this->data = [];
            }
        } catch (\Exception $e) {
            dd($e);
        }
    }
}
