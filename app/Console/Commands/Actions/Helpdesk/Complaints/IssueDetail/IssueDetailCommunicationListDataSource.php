<?php

namespace App\Console\Commands\Actions\Helpdesk\Complaints\IssueDetail;

use App\Console\Commands\Action;
use Illuminate\Support\Facades\DB;
use App\Helpers\UserHelper;

class IssueDetailCommunicationListDataSource extends Action
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'datasource:issueDetailCommunicationList {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Get the helpdesk issues detail communication list data source';

    /**
     * Execute the console command.
     */
    public function apply()
    {
        $userHelper = new UserHelper();

        $issue_id = $this->input['issue_id'];

        $obj = $this->tenantDB()->table('helpdesk_threads1')
            ->select(
                'thread_id',
                'created_date',
                'created_by',
                'updated_date',
                'updated_by',
                'status',
                'response_text',
                'respond_source',
                'response_time',
                'response_date',
                'responder_id',
                'responder_type',
                'body',
                DB::raw("DATE_FORMAT(created_date, '%d/%m/%Y') as created_date"),
                DB::raw("DATE_FORMAT(updated_date, '%d/%m/%Y') as updated_date"),
                DB::raw("DATE_FORMAT(response_date, '%d/%m/%Y') as response_date"),
            )
            ->where('fk_issue_id', $issue_id)
            ->orderBy('thread_id', 'desc')
            ->get();

        if (!$obj) {
            $this->status = 'error';
            $this->message = 'Unable to Fetch Help Topic Data';
            $this->statusCode = 400;
            return;
        }

        $responderUsers = $obj->pluck('responder_id')->unique()->toArray();

        // Step 2: Fetch user data for unique responder user values
        $userData = $this->MasterDB()->table('chsone_users_master as user')
            ->whereIn('user_id', $responderUsers)
            ->get();

        // Step 3: Map user data to thread data
        foreach ($obj as $thread) {
            $user = $userData->where('user_id', $thread->responder_id)->first();
            $thread->responder_name = optional($user, function ($user) use ($userHelper) {
                return $userHelper->concat($user->user_first_name, $user->user_last_name);
            });
        }


        $this->data = $obj;
    }
}
