<?php

namespace App\Console\Commands\Actions\Helpdesk\Complaints\IssueDetail;

use App\Console\Commands\Action;
use Illuminate\Support\Facades\DB;

class IssueDetailTicketDetailDataSource extends Action
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'datasource:issueDetailTicketDetail {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Get the helpdesk issues detail ticket detail data source';

    /**
     * Execute the console command.
     */
    public function apply()
    {
        try     {
            $issue_id = $this->input['issue_id'];
    
            // 1. Fetch the issue data
            $issue = $this->tenantDB()->table('helpdesk_issues2 as helpdesk_issues')
                ->select(
                    'helpdesk_issues.issue_id',
                    'helpdesk_issues.ticket_number',
                    DB::raw("DATE_FORMAT(helpdesk_issues.created_date, '%d/%m/%Y') as created_date"),
                    'helpdesk_issues.fk_help_topic_id',
                    'helpdesk_issues.title',
                    'helpdesk_issues.assignee_user',
                    'helpdesk_issues.status',
                    DB::raw("CONCAT(UCASE(LEFT(helpdesk_issues.priority, 1)), LCASE(SUBSTRING(helpdesk_issues.priority, 2))) AS priority"),
                    'helpdesk_issues.due_date',
                    'helpdesk_issues.src',
                    'help_topic.help_topic'
                )
                ->leftJoin('helpdesk_help_topics AS help_topic', 'helpdesk_issues.fk_help_topic_id', '=', 'help_topic.help_topic_id')
                ->where('helpdesk_issues.is_active', 1)
                ->where('helpdesk_issues.issue_id', $issue_id)
                ->where('helpdesk_issues.raised_by', '!=', '')
                ->orderBy('helpdesk_issues.created_date', 'desc')
                ->first();
    
            if ($issue) {
                // 2. Fetch all attachments for this issue
                $attachments = $this->tenantDB()->table('helpdesk_attachments')
                    ->select('file_name', 'file_path', 'mime_type')
                    ->where('fk_issue_id', $issue->issue_id)
                    ->get();
    
                // 3. Add `has_attachments` flag and attachments array
                $issue->has_attachments = $attachments->isNotEmpty() ? 1 : 0;
                $issue->attachments = $attachments;
            }
    
            if (!$issue) {
                $this->status = 'error';
                $this->message = 'Unable to Fetch Help Topic Data';
                $this->statusCode = 400;
                return;
            }
    
            $this->data = $issue;
        } catch (\Exception $e) {
            dd($e);
        }
    }

}
