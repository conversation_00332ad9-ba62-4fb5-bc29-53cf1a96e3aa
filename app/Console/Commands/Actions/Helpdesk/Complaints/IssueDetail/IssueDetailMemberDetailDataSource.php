<?php

namespace App\Console\Commands\Actions\Helpdesk\Complaints\IssueDetail;

use App\Console\Commands\Action;
use Illuminate\Support\Facades\DB;
use App\Helpers\UserHelper;

class IssueDetailMemberDetailDataSource extends Action
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'datasource:issueDetailMemberDetail {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Get the helpdesk issues detail member detail data source';

    /**
     * Execute the console command.
     */
    public function apply()
    {
        $userHelper = new UserHelper();

        $issue_id = $this->input['issue_id'];

        $obj = $this->tenantDB()->table('helpdesk_issues2 as helpdesk_issues')
            ->select(
                'helpdesk_issues.raised_by_name',
                'helpdesk_issues.assignee_user',
                'unitmaster.unit_flat_number',
                'memmaster.member_first_name',
                'memmaster.member_last_name',
                'memmaster.member_email_id',
                'memmaster.member_mobile_number',
                DB::raw("CONCAT(memmaster.member_first_name, ' ', memmaster.member_last_name, '(', unitmaster.unit_flat_number, ')') AS member_name_unit_building_name"),
            )
            ->leftJoin('chsone_members_master AS memmaster', 'helpdesk_issues.raised_by', '=', 'memmaster.id')
            ->leftJoin('chsone_units_master AS unitmaster', 'memmaster.fk_unit_id', '=', 'unitmaster.unit_id')
            ->where('helpdesk_issues.is_active', 1)
            ->where('issue_id', $issue_id)
            ->where('helpdesk_issues.raised_by', '!=', '')
            ->first();

        // add a new key in obj to fetch user id
        $obj->user_id = $this->input['user_id'];
        if (!$obj) {
            $this->status = 'error';
            $this->message = 'Unable to Fetch Member Data';
            $this->statusCode = 400;
            return;
        }

        $obj->assignee_user = $userHelper->getUserName($obj->assignee_user);
        $this->data = $obj;
    }
}
