<?php

namespace App\Console\Commands\Actions\Helpdesk\Complaints\IssueDetail;

use App\Console\Commands\Action;

class IssueDetailAttachmentDataSource extends Action
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'datasource:issueDetailAttachment {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Get the helpdesk issues detail attachment data source';

    /**
     * Execute the console command.
     */
    public function apply()
    {
        $issue_id = $this->input['issue_id'];

        $obj = $this->tenantDB()->table('helpdesk_attachments as helpdesk_issue_attachments')
            ->select(
                'helpdesk_issue_attachments.attachment_id',
                'helpdesk_issue_attachments.fk_issue_id',
                'helpdesk_issue_attachments.file_name',
                'helpdesk_issue_attachments.file_path',
                'helpdesk_issue_attachments.mime_type',
                'helpdesk_issue_attachments.created_date',
                'helpdesk_issue_attachments.created_by',
                'helpdesk_issue_attachments.updated_date',
                'helpdesk_issue_attachments.updated_by',
                'helpdesk_issue_attachments.fk_thread_id',
                'helpdesk_issue_attachments.soc_id',
            )
            ->where('fk_issue_id', $issue_id)
            ->first();

        if (!$obj) {
            $this->status = 'error';
            $this->message = 'Unable to Fetch Attachment Data';
            $this->statusCode = 400;
            return;
        }

        // Convert to array to make it mutable
        $obj = (array)$obj;
        
        // Construct the full URL
        $obj['full_image_url'] = 'https://s3.ap-south-1.amazonaws.com/society.chsone.in/' . 
            ltrim($obj['file_path'], '/') . 
            $obj['file_name'];

        $this->data = $obj;
    }
}
