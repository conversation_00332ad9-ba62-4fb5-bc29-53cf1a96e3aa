<?php

namespace App\Console\Commands\Actions\Helpdesk\Complaints;

use Illuminate\Support\Facades\Route;
use App\Console\Commands\Action;

class IssueBulkEditDataSource extends Action
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'datasource:issueBulkEdit {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Get the Issue edit data source';

    /**
     * Execute the console command.
     */
    public function apply()
    {
        try {

            $issue_ids = $this->input['ids'] ?? [];

            if (empty($issue_ids)) {
                $this->status = 'error';
                $this->message = 'Please provide a valid ids';
                $this->statusCode = 400;
                return;
            }

            // handle delete issue
            $route = Route::current();
            if ($route->uri() == 'api/admin/helpdesk/bulk' && $route->methods()[0] == 'DELETE') {
                $this->handleDeleteIssue($issue_ids);
                return;
            }
    
           
            $status = $this->input['status'] ?? '';
    
            if (empty($status)) {
                $this->status = 'error';
                $this->message = 'Please provide a valid status';
                $this->statusCode = 400;
                return;
            }
    
            if ($status != 'closed' && $status != 'open' && $status != 'reopened' && $status != 'resolved' && $status != 'onhold') {
                $this->status = 'error';
                $this->message = 'Please provide a valid status closed, open, reopened, resolved, onhold';
                $this->statusCode = 400;
                return;
            }
    
            $updateData = ['status' => $status];
    
            $obj = $this->tenantDB()->table('helpdesk_issues2')
                ->whereIn('issue_id', $issue_ids)
                ->update($updateData);
    
            if ($obj) {
                $this->status = 'success';
                $this->message = 'Issue Updated successfully';
                $this->statusCode = 200;
            } 
        } catch (\Exception $e) {
            dd($e);
        }
    }

    public function handleDeleteIssue($issue_ids)
    {
        try {
            $obj = $this->tenantDB()->table('helpdesk_issues2')
                ->whereIn('issue_id', $issue_ids)
                ->delete();
            
            if ($obj) {
                $this->status = 'success';
                $this->message = 'Issue Deleted successfully';
                $this->statusCode = 200;
            } 
        } catch (\Exception $e) {
            dd($e);
        }
    }
}
