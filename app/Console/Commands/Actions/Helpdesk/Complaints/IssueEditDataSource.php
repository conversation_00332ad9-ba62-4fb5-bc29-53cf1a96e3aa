<?php

namespace App\Console\Commands\Actions\Helpdesk\Complaints;

use App\Console\Commands\Action;

class IssueEditDataSource extends Action
{
    protected $signature = 'datasource:issueEdit {flowId} {parentId} {input}';
    protected $description = 'Edit an existing helpdesk issue';

    public function apply()
    {
        $issue_id = $this->input['issue_id'];
        $soc_id = $this->input['company_id'];
        $ip = $this->input['additional_data']['ip'] ?? '0.0.0.0';
        $updatedBy = $this->input['updated_by'] ?? 0;

        // Handle attachments
        if (!empty($this->input['src'])) {
            foreach ($this->input['src'] as $attachment) {
                $this->tenantDB()->table('helpdesk_attachments')->updateOrInsert(
                    ['fk_issue_id' => $issue_id, 'file_name' => $attachment],
                    [
                        'soc_id' => $soc_id,
                        'fk_thread_id' => 0,
                        'fk_issue_id' => $issue_id,
                        'file_name' => $attachment,
                        'file_path' => $attachment,
                        'created_by' => $updatedBy,
                        'created_date' => date('Y-m-d H:i:s'),
                        'updated_date' => date('Y-m-d H:i:s'),
                        'updated_by' => $updatedBy,
                    ]
                );
            }
        }

        // Prepare update data
        $updateData = [
            'fk_help_topic_id' => $this->input['help_topic_id'],
            'assignee' => $this->input['assignee'],
            'assignee_user' => $this->input['assignee'],
            'assignee_type' => $this->input['assignee_type'],
            'title' => $this->input['title'],
            'body' => $this->input['body'],
            'priority' => $this->input['priority'] ?? 'normal',
            'canned_response_id' => $this->input['canned_response_id'] ?? 0,
            'due_date' => $this->input['due_date'] ?? date('Y-m-d H:i:s'),
            'is_overdue' => $this->input['is_overdue'] ?? 1,
            'updated_by' => $updatedBy,
            'updated_date' => date('Y-m-d H:i:s'),
        ];

        $updated = $this->tenantDB()->table('helpdesk_issues2')
            ->where('issue_id', $issue_id)
            ->update($updateData);

        if ($updated) {
            // Log update as thread
            $this->insertThread(
                $soc_id,
                $issue_id,
                $this->input['body'],
                $this->input['assignee'],
                'public',
                'Issue Updated',
                $ip,
                $updatedBy
            );

            if (!empty($this->input['internal_note'])) {
                $this->insertThread(
                    $soc_id,
                    $issue_id,
                    $this->input['internal_note'],
                    $this->input['assignee'],
                    'private',
                    'Note',
                    $ip,
                    $updatedBy
                );
            }

            $this->status = 'success';
            $this->message = 'Issue updated successfully';
            $this->statusCode = 200;
        } else {
            $this->status = 'error';
            $this->message = 'Unable to update issue';
            $this->statusCode = 400;
        }
    }

    public function insertThread($soc_id, $issue_id, $body, $assignee, $threadType, $responseText, $ip, $createdBy)
    {
        $threadData = [
            'soc_id' => $soc_id,
            'parent_id' => 0,
            'fk_issue_id' => $issue_id,
            'body' => $body,
            'responder_type' => 'System',
            'responder_id' => $assignee,
            'response_date' => $this->getDate(date('Y-m-d H:i:s')),
            'response_time' => $this->getTime(date('Y-m-d H:i:s')),
            'thread_type' => $threadType,
            'response_text' => $responseText,
            'responder_ip' => $ip,
            'status' => 1,
            'reported_abuse' => 0,
            'created_date' => date('Y-m-d H:i:s'),
            'created_by' => $createdBy ?? 0,
            'updated_date' => date('Y-m-d H:i:s'),
            'updated_by' => $createdBy ?? 0
        ];

        $this->tenantDB()->table('helpdesk_threads1')->insert($threadData);
    }

    public function getDate($created_date)
    {
        return date('Y-m-d', strtotime($created_date));
    }

    public function getTime($created_date)
    {
        return date('H:i:s', strtotime($created_date));
    }
}
