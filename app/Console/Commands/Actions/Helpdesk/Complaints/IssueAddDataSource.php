<?php

namespace App\Console\Commands\Actions\Helpdesk\Complaints;

use App\Console\Commands\Action;

class IssueAddDataSource extends Action
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'datasource:issueAdd {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Get the helpdesk issues add datasource';

    /**
     * Execute the console command.
     */
    public function apply()
    {
        $soc_id = $this->input['company_id'];
        $ip = $this->input['additional_data']['ip'];
        $ticketNumber = random_int(100000, 999999);

        $issue_id = $this->tenantDB()->table('helpdesk_issues2')
            ->insertGetId([
                'soc_id' => $soc_id,
                'fk_help_topic_id' => $this->input['help_topic_id'],
                'assignee' => $this->input['assignee'],
                'assignee_user' => $this->input['assignee'],
                'assignee_type' => $this->input['assignee_type'],
                'title' => $this->input['title'],
                'body' => $this->input['body'],
                'status' => $this->input['status'] ?? 'open',
                'is_active' => 1,
                'raised_by' => $this->input['raised_by'],
                'raised_by_name' => $this->input['raised_by_name'],
                'raised_by_email' => $this->input['raised_by_email'],
                'raised_by_phone' => $this->input['raised_by_phone'],
                // 'src' => $this->input['src'],
                'priority' => $this->input['priority'] ?? 'normal',
                'canned_response_id' => $this->input['canned_response_id'] ?? 0,
                'created_by' => $this->input['created_by'] ?? 0,
                'created_date' => date('Y-m-d H:i:s'), 
                'updated_date' => date('Y-m-d H:i:s'),
                'updated_by' => $this->input['created_by'] ?? 0,
                'due_date' => $this->input['due_date'] ?? date('Y-m-d H:i:s'),
                'is_overdue' => $this->input['is_overdue'] ?? 1,
                'ticket_number' => $ticketNumber,
            ]);

            // run a for loop and add attachments into helpdesk_attachments table
            if (!empty($this->input['src'])) {
                foreach ($this->input['src'] as $attachment) {
                    $this->tenantDB()->table('helpdesk_attachments')->insert([
                        'soc_id' => $soc_id,
                        'fk_thread_id' => 0,
                        'fk_issue_id' => $issue_id,
                        'file_name' => $attachment,
                        'file_path' => $attachment,
                        'created_by' => $this->input['raised_by'] ?? 0,
                        'created_date' => date('Y-m-d H:i:s'),
                        'updated_date' => date('Y-m-d H:i:s'),
                        'updated_by' => $this->input['raised_by'] ?? 0,
                    ]);
                }
            }

        if ($issue_id) {

            $this->insertThread($soc_id, $issue_id, $this->input['body'], $this->input['assignee'], 'public', 'Issue Raised', $ip, $this->input['created_by']);

            if (!empty($this->input['internal_note'])) {
                $this->insertThread($soc_id, $issue_id, $this->input['internal_note'], $this->input['assignee'], 'private', 'Note', $ip, $this->input['created_by']);
            }

            $this->status = 'success';
            $this->message = 'Issue Created successfully';
            $this->statusCode = 200;
        } else {
            $this->status = 'error';
            $this->message = 'Unable to Create Issue';
            $this->statusCode = 400;
        }
    }

    public function insertThread($soc_id, $issue_id, $body, $assignee, $threadType, $responseText, $ip, $createdBy)
    {
        $threadData = [
            'soc_id' => $soc_id,
            'parent_id' => 0,
            'fk_issue_id' => $issue_id,
            'body' => $body,
            'responder_type' => 'System',
            'responder_id' => $assignee,
            'response_date' => $this->getDate(date('Y-m-d H:i:s')),
            'response_time' => $this->getTime(date('Y-m-d H:i:s')),
            'thread_type' => $threadType,
            'response_text' => $responseText,
            'responder_ip' => $ip,
            'status' => 1,
            'reported_abuse' => 0,
            'created_date' => date('Y-m-d H:i:s'),
            'created_by' => $createdBy ?? 0,
            'updated_date' => date('Y-m-d H:i:s'),
            'updated_by' => $createdBy ?? 0
        ];

        $this->tenantDB()->table('helpdesk_threads1')->insert($threadData);
    }

    public function getDate($created_date)
    {
        return date('Y-m-d', strtotime($created_date));
    }

    public function getTime($created_date)
    {
        return date('H:i:s', strtotime($created_date));
    }
}
