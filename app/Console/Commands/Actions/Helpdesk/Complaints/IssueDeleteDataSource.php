<?php

namespace App\Console\Commands\Actions\Helpdesk\Complaints;

use App\Console\Commands\Action;

class IssueDeleteDataSource extends Action
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'datasource:issueDelete {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Get the helpdesk issues delete datasource';

    /**
     * Execute the console command.
     */
    public function apply()
    {
        $issue_id = $this->input['issue_id'];

        $result = $this->tenantDB()->table('helpdesk_issues2')
            ->where('issue_id', $issue_id)
            ->update([
                'is_active' => 0
            ]);
        
        if($result){
            $this->status = 'success';
            $this->message = 'Helpdesk Issue deleted successfully';
            $this->statusCode = 200;
            $this->data = [];
        }
        else{
            $this->status = 'error';
            $this->message = 'Helpdesk Issue not deleted';
            $this->statusCode = 400;
            $this->data = [];
        }
    }
}
