<?php

namespace App\Console\Commands\Actions\Helpdesk\Complaints;

use App\Console\Commands\Action;
use Illuminate\Support\Facades\DB;

class StaffMemberTreeDataSource extends Action
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'datasource:staffMemberTree {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Get the staff member tree data source';

    /**
     * Execute the console command.
     */
    public function apply()
    {

        $memberData = $this->tenantDB()->table('chsone_members_master as chsone_members_master')
            ->select(
                'chsone_members_master.user_id as id',
                DB::raw("CONCAT(chsone_members_master.member_first_name, ' ', chsone_members_master.member_last_name, '(', chsone_units_master.soc_building_name, '-', chsone_units_master.unit_flat_number, ')') AS ledger_account_name"),
            )
            ->join('chsone_units_master', 'chsone_units_master.unit_id', '=', 'chsone_members_master.fk_unit_id')
            ->join('chsone_committee_members', 'chsone_committee_members.fk_member_id', '=', 'chsone_members_master.id')
            // ->where('chsone_committee_members.fk_committee_id', function ($query) {
            //     $query->select(DB::raw('MAX(committee_id)'))
            //         ->from('chsone_committees');
            // })
            ->groupBy('chsone_committee_members.fk_member_id')
            ->where('chsone_members_master.status', 1)
            ->whereNotNull('chsone_members_master.user_id')
            ->orderBy('chsone_members_master.user_id', 'DESC')
            ->get();


        $staffData = $this->tenantDB()->table('chsone_staff_master')
            ->select('user_id as id', 'staff_first_name')
            ->where('status', 1)
            ->whereNotNull('user_id')
            ->groupBy('user_id')
            //->orderBy('user_id', 'DESC')
            ->get();


        // Initialize the response array
        $response = [];
        $dataSets = [
            'Members' => $memberData,
            'Staff' => $staffData,
        ];

        // Function to generate a unique random number
        function generateUniqueRandomId(&$existingIds, $min = 1000, $max = 9999)
        {
            do {
                $id = mt_rand($min, $max);
            } while (in_array($id, $existingIds)); // Check if the ID already exists

            $existingIds[] = $id; // Add the new ID to the array to track it

            return $id;
        }

        // Array to track all unique IDs to avoid duplicates
        $existingIds = [];

        // Initialize the response array
        $response = [];

        // Combine both datasets
        $dataSets = [
            'Members' => $memberData,
            'Staff' => $staffData,
        ];

        // Iterate over each dataset
        foreach ($dataSets as $datasetName => $dataset) {
            $rows = [];
            // Iterate over each row in the dataset
            foreach ($dataset as $item) {
                $randomId = generateUniqueRandomId($existingIds); // Generate a unique ID

                // Prepare the row with a unique random ID
                $rows[] = [
                    'id' => $item->id,
                    'ledger_account_name' => $item->ledger_account_name ?? $item->staff_first_name, // Adjust field based on dataset
                    'type' => $datasetName,
                ];
            }

            // Generate a unique ID for the dataset itself
            $datasetId = generateUniqueRandomId($existingIds);

            // Add the dataset to the response
            $response[] = [
                'id' => $datasetId,
                'ledger_account_name' => $datasetName,
                'rows' => $rows,
            ];
        }
        
        // Assign the final response to the `$data` property
        $this->data = $response;
    }
}
