<?php

namespace App\Console\Commands\Actions\Helpdesk\Escalation;

use App\Console\Commands\Action;
use Illuminate\Support\Facades\DB;
use App\Helpers\UserHelper;

class EscalationListDataSource extends Action
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'datasource:escalationList {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Data Source For Escalation Workflow';

    /**
     * Execute the console command.
     */


    public function apply()
    {
        $page = $this->input['page'] ?? 1;
        $per_page = !empty($this->input['per_page']) ? $this->input['per_page']: ($this->hugeData ? 100000 : 10);
        $offset = ($page - 1) * $per_page;
        $obj = $this->tenantDB()
            ->table('helpdesk_help_topics')
            ->select(
                'help_topic_id as id',
                'help_topic',
                'parent_id',
                'first_escalation_to',
                'first_escalation_freq',
                'second_escalation_to',
                'second_escalation_freq',
                'third_escalation_to',
                'third_escalation_freq',
                'escalation_from_date_type',
                DB::raw("CONCAT(first_escalation_freq, ' Hours') AS first_escalation_freq"),
                DB::raw("CONCAT(second_escalation_freq, ' Hours') AS second_escalation_freq"),
                DB::raw("CONCAT(third_escalation_freq, ' Hours') AS third_escalation_freq"),
                DB::raw("(CASE
                WHEN helpdesk_help_topics.parent_id = 0 THEN 'Top'
                ELSE
                    (SELECT help_topic
                    FROM helpdesk_help_topics AS parent_help_topic
                    WHERE parent_help_topic.help_topic_id = helpdesk_help_topics.parent_id)
            END) AS parent_topic"),
            )
            // ->where('is_active', 1)
            ->where('has_escalations', 1);
            // ->orderBy('created_date', 'desc')
            // ->get();
            $queryResult = $obj->get();  // Get the results first
            $count = $obj->count();

        // Fetch all unique user IDs for escalation fields
        $uniqueUserIds = collect($queryResult)->flatMap(function ($helpTopic) {  // Use $queryResult instead of $obj
            return [
                $helpTopic->first_escalation_to,
                $helpTopic->second_escalation_to,
                $helpTopic->third_escalation_to
            ];
        })->unique()->filter()->values()->all();

        // Fetch user data for unique user IDs
        $userData = $this->MasterDB()->table('chsone_users_master')
            ->whereIn('user_id', $uniqueUserIds)
            ->get()
            ->keyBy('user_id');

        // Populate user names for each help topic
        foreach ($queryResult as $helpTopic) {  // Use $queryResult instead of $obj
            $this->populateEscalationUserData($helpTopic, 'first_escalation_to', 'first_escalation_to', $userData);
            $this->populateEscalationUserData($helpTopic, 'second_escalation_to', 'second_escalation_to', $userData);
            $this->populateEscalationUserData($helpTopic, 'third_escalation_to', 'third_escalation_to', $userData);
        }

        $paginatedResult = $queryResult;
        $this->data = $paginatedResult;
        $this->meta['pagination']['total'] = $count;
    }

    private function populateEscalationUserData($helpTopic, $escalationField, $nameField, $userData)
    {
        $userId = $helpTopic->{$escalationField};
        $user = $userData->get($userId);
        $userHelper = new UserHelper();
        $helpTopic->{$nameField} = $user ? $userHelper->concat($user->user_first_name, $user->user_last_name) : null;
    }

}
