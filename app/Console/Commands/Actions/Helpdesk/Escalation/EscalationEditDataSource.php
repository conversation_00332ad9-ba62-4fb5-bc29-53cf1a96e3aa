<?php

namespace App\Console\Commands\Actions\Helpdesk\Escalation;

use App\Console\Commands\Action;

class EscalationEditDataSource extends Action
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'datasource:escalationEdit {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Data Source For Escalation Edit Workflow';

    /**
     * Execute the console command.
     */
    public function apply()
    {

        $help_topic_id = $this->input['help_topic_id'];

        $updateData = [
            'first_escalation_to' => $this->input['first_escalation_to'],
            'first_escalation_freq' => $this->input['first_escalation_freq'],
            'updated_date' => date('Y-m-d H:i:s'),
            'updated_by' => $this->input['updated_by'] ?? 0
        ];

        if(!empty($this->input['second_escalation_to']) && !empty($this->input['second_escalation_freq'])){
            $updateData['second_escalation_to'] = $this->input['second_escalation_to'];
            $updateData['second_escalation_freq'] = $this->input['second_escalation_freq'];
        }

        if(!empty($this->input['third_escalation_to']) && !empty($this->input['third_escalation_freq'])){
            $updateData['third_escalation_to'] = $this->input['third_escalation_to'];
            $updateData['third_escalation_freq'] = $this->input['third_escalation_freq'];
        }
        
        $obj = $this->tenantDB()->table('helpdesk_help_topics')
            ->where('help_topic_id', $help_topic_id)
            ->update($updateData);

        if ($obj) {
            $this->status = 'success';
            $this->message = 'Escalation Updated successfully';
            $this->statusCode = 200;
        } else {
            $this->status = 'error';
            $this->message = 'Unable to Update Escalation';
            $this->statusCode = 400;
        }
    }
}
