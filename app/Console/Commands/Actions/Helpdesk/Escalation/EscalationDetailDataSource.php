<?php

namespace App\Console\Commands\Actions\Helpdesk\Escalation;

use App\Console\Commands\Action;
use Illuminate\Support\Facades\DB;
use App\Helpers\UserHelper;

class EscalationDetailDataSource extends Action
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'datasource:escalationDetail {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Get the helpdesk topic escalation detail data source';

    /**
     * Execute the console command.
     */
    public function apply()
    {
        $userHelper = new UserHelper();

        $obj = $this->tenantDB()
            ->table('helpdesk_help_topics')
            ->select(
                'help_topic_id',
                'help_topic',
                'parent_id',
                'first_escalation_to',
                'first_escalation_freq',
                'second_escalation_to',
                'second_escalation_freq',
                'third_escalation_to',
                'third_escalation_freq',
                'escalation_from_date_type',
                'created_by',
                'created_date',
                'updated_by',
                'assignee_id',
                'updated_date',
                'note', 
                'is_active',
                // DB::raw("CONCAT(first_escalation_freq, ' Hours') AS first_escalation_freq"),
                // DB::raw("CONCAT(second_escalation_freq, ' Hours') AS second_escalation_freq"),
                // DB::raw("CONCAT(third_escalation_freq, ' Hours') AS third_escalation_freq"),
                DB::raw('CAST(first_escalation_freq AS UNSIGNED) AS first_escalation_freq'),
                DB::raw('CAST(second_escalation_freq AS UNSIGNED) AS second_escalation_freq'),
                DB::raw('CAST(third_escalation_freq AS UNSIGNED) AS third_escalation_freq'),
                DB::raw("(CASE 
                WHEN helpdesk_help_topics.parent_id = 0 THEN 'Top' 
                ELSE 
                    (SELECT help_topic 
                    FROM helpdesk_help_topics AS parent_help_topic
                    WHERE parent_help_topic.help_topic_id = helpdesk_help_topics.parent_id)
                END) AS parent_topic"),
                DB::raw("DATE_FORMAT(helpdesk_help_topics.created_date, '%d/%m/%Y') as created_date"),
                DB::raw("DATE_FORMAT(helpdesk_help_topics.updated_date, '%d/%m/%Y') as updated_date"),
            )
            // ->where('is_active', 1)
            ->where('has_escalations', 1)
            ->where('help_topic_id', $this->input['help_topic_id'])
            ->first();

        // cast first_escalation_to and second_escalation_to and third_escalation_to to integer
        $obj->first_escalation_to = (int)$obj->first_escalation_to;
        $obj->second_escalation_to = (int)$obj->second_escalation_to;
        $obj->third_escalation_to = (int)$obj->third_escalation_to;
        

        if (!$obj) {
            $this->status = 'error';
            $this->message = 'Unable to Fetch Escalations Data';
            $this->statusCode = 400;
            return;
        }

        $obj->auto_assignee_name = $userHelper->getUserName($obj->assignee_id);
        $obj->first_escalation_name = $userHelper->getUserName($obj->first_escalation_to);
        $obj->second_escalation_name = $userHelper->getUserName($obj->second_escalation_to);
        $obj->third_escalation_name = $userHelper->getUserName($obj->third_escalation_to);
        $obj->created_by = $userHelper->getUserName($obj->created_by);
        $obj->updated_by = $userHelper->getUserName($obj->updated_by);

        $this->data = $obj;
    }

}
