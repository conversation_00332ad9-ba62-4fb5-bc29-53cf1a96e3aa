<?php

namespace App\Console\Commands\Actions\Helpdesk\Escalation;

use App\Console\Commands\Action;

class EscalationDeleteDataSource extends Action
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'datasource:escalationDelete {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Escalation Delete DataSource';

    /**
     * Execute the console command.
     */
    public function apply()
    {
        $help_topic_id = $this->input['help_topic_id'];

        $result = $this->tenantDB()->table('helpdesk_help_topics')
            ->where('help_topic_id', $help_topic_id)
            ->update([
                'is_active' => 0,
                'has_escalations'=>0
            ]);
        
        if($result){
            $this->status = 'success';
            $this->message = 'Helpdesk Topic deleted successfully';
            $this->statusCode = 200;
            $this->data = [];
        }
        else{
            $this->status = 'error';
            $this->message = 'Helpdesk Topic not deleted';
            $this->statusCode = 400;
            $this->data = [];
        }
    }
}
