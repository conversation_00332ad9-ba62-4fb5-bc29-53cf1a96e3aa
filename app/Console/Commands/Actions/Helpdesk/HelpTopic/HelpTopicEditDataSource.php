<?php

namespace App\Console\Commands\Actions\Helpdesk\HelpTopic;

use App\Console\Commands\Action;

class HelpTopicEditDataSource extends Action
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'datasource:helpTopicEdit {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Helpdesk Edit Help Topic Data Source';

    /**
     * Execute the console command.
     */
    public function apply()
    {

        $help_topic_id = $this->input['help_topic_id'];

        $soc_id = $this->input['company_id'];
        if ($this->input['is_public'] == 'Public') {
            $this->input['is_public'] = 1;
        } else {
            $this->input['is_public'] = 0;
        }
        $obj = $this->tenantDB()->table('helpdesk_help_topics')
        ->where('help_topic_id', $help_topic_id)        
            ->update([
                'soc_id' => $soc_id,
                'help_topic' => $this->input['help_topic'],
                'parent_id' => $this->input['parent_id'],
                'assignee_id' => $this->input['assignee_id'],
                'priority' => $this->input['priority'],
                'is_active' => $this->input['is_active'],
                'is_public' => $this->input['is_public'],
                'note' => $this->input['note'],
                'assignee_type' => $this->input['assignee_type'],
                'created_by' => $this->input['created_by'] ?? 0,
                'created_date' => date('Y-m-d H:i:s'), // 'created_date' => date('Y-m-d H:i:s'), 'created_by' => $this->input['created_by'] ?? 0, 'updated_date' => date('Y-m-d H:i:s'), 'updated_by' => $this->input['updated_by'] ?? 0
                'updated_date' => date('Y-m-d H:i:s'),
                'updated_by' => $this->input['updated_by'] ?? 0,
                'due_hours' => $this->input['due_hours'] ?? 0
            ]);
        
        if ($obj) {
            $this->status = 'success';
            $this->message = 'Help Topic Updated successfully';
            $this->statusCode = 200;
        } else {
            $this->status = 'error';
            $this->message = 'Unable to Update Help Topic';
            $this->statusCode = 400;
        }
    }
}
