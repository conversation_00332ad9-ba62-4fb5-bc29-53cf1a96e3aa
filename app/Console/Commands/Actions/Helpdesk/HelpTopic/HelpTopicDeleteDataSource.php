<?php

namespace App\Console\Commands\Actions\Helpdesk\HelpTopic;

use App\Console\Commands\Action;

class HelpTopicDeleteDataSource extends Action
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'datasource:helpTopicDelete {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Helpdesk Delete Help Topic Data Source';

    /**
     * Execute the console command.
     */
    public function apply()
    {
        try{
            $help_topic_id = $this->input['help_topic_id'];

            $result = $this->tenantDB()->table('helpdesk_help_topics')
                ->where('help_topic_id', $help_topic_id)
                ->first();

            if (!$result) {
                $this->status = 'error';
                $this->message = 'Helpdesk Topic not found';
                $this->statusCode = 400;
                $this->data = [];
                return;
            }

            // Initialize result1
            $result1 = 0;

            // Check if 'helpdesk_issues1' table exists
            if ($this->tenantDB()->getSchemaBuilder()->hasTable('helpdesk_issues1')) {
                $result1 = $this->tenantDB()->table('helpdesk_issues1')
                    ->where('fk_help_topic_id', $help_topic_id)
                    ->delete();
            } 
            // If not, check if 'helpdesk_issues2' exists
            elseif ($this->tenantDB()->getSchemaBuilder()->hasTable('helpdesk_issues2')) {
                $result1 = $this->tenantDB()->table('helpdesk_issues2')
                    ->where('fk_help_topic_id', $help_topic_id)
                    ->delete();
            } 
            // Default to helpdesk_issues if neither 1 or 2 exist
            else {
                $result1 = $this->tenantDB()->table('helpdesk_issues')
                    ->where('fk_help_topic_id', $help_topic_id)
                    ->delete();
            }

            // Delete the help topic itself
            $result = $this->tenantDB()->table('helpdesk_help_topics')
                ->where('help_topic_id', $help_topic_id)
                ->delete();

            if ($result) {
                $this->status = 'success';
                $this->message = 'Helpdesk Topic deleted successfully';
                $this->statusCode = 200;
                $this->data = [];
            } else {
                $this->status = 'error';
                $this->message = 'Helpdesk Topic not deleted';
                $this->statusCode = 400;
                $this->data = [];
            }

        }catch(\Exception $e){
            dd($e);
        }
    }
}
