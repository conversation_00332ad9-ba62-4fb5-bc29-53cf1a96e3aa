<?php
namespace App\Console\Commands\Actions\Helpdesk\HelpTopic;

use App\Console\Commands\Action;

class HelpTopicDropdownDataSource extends Action
{
    protected $signature = 'datasource:helpTopicDropdown {flowId} {parentId} {input}';
    protected $description = 'Help Topic Dropdown';
    public function apply()
    {
        $db = $this->tenantDB();
        $results = $db->table('helpdesk_help_topics')
            ->where('is_active', 1)
            ->orderByDesc('help_topic_id')
            ->get();

        // change helptopic_id to id
        foreach ($results as $result) {
            $result->id = $result->help_topic_id;
        }
        $this->data = $results;
    }
}
