<?php

namespace App\Console\Commands\Actions\Helpdesk\HelpTopic;

use App\Console\Commands\Action;
use Illuminate\Support\Facades\DB;
use App\Helpers\UserHelper;

class HelpTopicDetailDataSource extends Action
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'datasource:helpTopicDetail {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Get HelpTopic Detail List';

    /**
     * Execute the console command.
     */

    public function apply()
    {

        // SELECT tb_1.*, tb_2.*
        // FROM soc_db_001.helpdesk_help_topics tb_1
        // LEFT JOIN chsone_master_db.chsone_users_master tb_2 ON tb_1.assignee_id = tb_2.user_id;
        $userHelper = new UserHelper();

        $help_topic_id = $this->input['id'];

        $obj = $this->tenantDB()->table('helpdesk_help_topics')
            ->select(
                'help_topic_id',
                'help_topic',
                'parent_id',
                'note',
                'assignee_id',
                'priority',
                'is_active',
                'is_public',
                'created_by',
                'created_date',
                'updated_by',
                'updated_date',
                "due_hours",
                DB::raw("(CASE 
                    WHEN helpdesk_help_topics.parent_id = 0 THEN 'Top' 
                    ELSE 
                        (SELECT help_topic 
                        FROM helpdesk_help_topics AS parent_help_topic
                        WHERE parent_help_topic.help_topic_id = helpdesk_help_topics.parent_id)
                END) AS parent_topic"),
                DB::raw("(CASE 
                    WHEN helpdesk_help_topics.is_public = 1 THEN 'Public' 
                    ELSE 'Private' 
                    END) AS is_public"),
                DB::raw("DATE_FORMAT(helpdesk_help_topics.created_date, '%d/%m/%Y') as created_date"),
                DB::raw("DATE_FORMAT(helpdesk_help_topics.updated_date, '%d/%m/%Y') as updated_date"),
            )
            // ->where('is_active', 1)
            ->where('help_topic_id', $help_topic_id)
            ->first();

        if (!$obj) {
            $this->status = 'error';
            $this->message = 'Unable to Fetch Help Topic Data';
            $this->statusCode = 400;
            return;
        }
        $obj->assignee_name = $userHelper->getUserName($obj->assignee_id);
        $obj->created_by = $userHelper->getUserName($obj->created_by);
        $obj->updated_by = $userHelper->getUserName($obj->updated_by);

        $this->data = $obj;
    }

}
