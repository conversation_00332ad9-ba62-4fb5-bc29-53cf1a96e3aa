<?php

namespace App\Console\Commands\Actions\helpdesk\HelpTopic;

use App\Console\Commands\Action;
use App\Http\Traits\MongoTraits;

class HelpTopicActivityLogDataSource extends Action
{
    use MongoTraits; // Use the MongoTraits trait in this class

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'datasource:helpTopicActivityLog {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Help Topic Activity Log Data Source';

    /**
     * Execute the console command.
     */
    public function apply()
    {
        $module = $this->input['module'];
        $company_id = $this->input['company_id'];
        $help_topic_id = $this->input['help_topic_id'];
        $fields = $this->input['fields'] ?? [];
        if (!empty($fields)) {
            $fields = json_decode($fields);
        }

        $this->data = $this->showNotification($module, $company_id, $help_topic_id, $fields);
    }
}
