<?php

namespace App\Console\Commands\Actions\Helpdesk\HelpTopic;

use App\Console\Commands\Action;
use Illuminate\Support\Facades\DB;
use App\Helpers\UserHelper;


class HelpTopicListDataSource extends Action
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'datasource:helpTopicList {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Get HelpTopic List';

    /**
     * Execute the console command.
     */

    public function apply()
    {

        // SELECT tb_1.*, tb_2.*
        // FROM soc_db_001.helpdesk_help_topics tb_1
        // LEFT JOIN chsone_master_db.chsone_users_master tb_2 ON tb_1.assignee_id = tb_2.user_id;
        $page = $this->input['page'] ?? 1;
        $per_page = !empty($this->input['per_page']) ? $this->input['per_page']: ($this->hugeData ? 100000 : 10);
        $offset = ($page - 1) * $per_page;

        $userHelper = new UserHelper();
        $sub = $this->tenantDB()->table('helpdesk_help_topics')
            ->select(
                'help_topic',
                DB::raw('MAX(created_date) as max_created_date')
            )
            // ->where('is_active', 1)
            ->groupBy('help_topic');

        $obj = $this->tenantDB()->table('helpdesk_help_topics as h')
            ->joinSub($sub, 'latest', function($join) {
                $join->on('h.help_topic', '=', 'latest.help_topic')
                     ->on('h.created_date', '=', 'latest.max_created_date');
            })
            ->select(
                'h.help_topic_id as id',
                'h.help_topic',
                'h.parent_id',
                'h.assignee_id',
                'h.is_active',
                'h.is_public',
                DB::raw("CONCAT(UCASE(LEFT(h.priority, 1)), LCASE(SUBSTRING(h.priority, 2))) AS priority"),
                DB::raw("(CASE
                    WHEN h.parent_id = 0 THEN 'Top'
                    ELSE
                        (SELECT help_topic
                        FROM helpdesk_help_topics AS parent_help_topic
                        WHERE parent_help_topic.help_topic_id = h.parent_id)
                END) AS parent_topic"),
            )
            // ->where('h.is_active', 1)
            ->orderBy('h.created_date', 'desc');

        $is_active = $this->input['is_active'] ?? null;
        if ($is_active == true) {
            $obj->where('h.is_active', 1);
        }

        if (!$obj) {
            $this->status = 'error';
            $this->message = 'Unable to Fetch Help Topic Data';
            $this->statusCode = 400;
            return;
        }
        $count = $obj->count(); // This will return the count of unique help_topic records

        $obj = $obj->offset($offset);
        $obj = $obj->limit($per_page);

        $result = $obj->get();
        $assigneeUsers = $result->pluck('assignee_id')->unique()->toArray();

        // Step 3: Fetch user data for unique assignee_user values
        $userData = $this->MasterDB()->table('chsone_users_master as user')
            ->whereIn('user_id', $assigneeUsers)
            ->get();

        // Step 4: Map user data to issue data
        foreach ($result as $issue) {
            $user = $userData->where('user_id', $issue->assignee_id)->first();
            $issue->assignee_name = optional($user, function ($user) use ($userHelper) {
                return $userHelper->concat($user->user_first_name, $user->user_last_name);
            });
        }


        $this->data = $result;
        $this->meta['pagination']['total']=$count;



    }

}
