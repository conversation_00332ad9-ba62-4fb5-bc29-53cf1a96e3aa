<?php

namespace App\Console\Commands\Actions\Helpdesk\CannedResponse;

use App\Console\Commands\Action;

class CannedResponseAddDataSource extends Action
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'datasource:cannedResponseAdd {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Canned Response Add DataSource';

    /**
     * Execute the console command.
     */
    public function apply()
    {
        $updateData = [
            'canned_title' => $this->input['canned_title'],
            'canned_response' => $this->input['canned_response'],
            'sms_response' => $this->input['sms_response'],
            'status' => $this->input['status'],
            'created_date' => date('Y-m-d H:i:s'),
            'created_by' => $this->input['created_by'] ?? 0,
            'soc_id' => $this->input['company_id']
        ];

        $obj = $this->tenantDB()->table('helpdesk_canned_responses')
            ->insert($updateData);

        if ($obj) {
            $this->status = 'success';
            $this->message = 'Canned Response Crated successfully';
            $this->statusCode = 200;
        } else {
            $this->status = 'error';
            $this->message = 'Unable to Create Canned Response';
            $this->statusCode = 400;
        }
    }
}
