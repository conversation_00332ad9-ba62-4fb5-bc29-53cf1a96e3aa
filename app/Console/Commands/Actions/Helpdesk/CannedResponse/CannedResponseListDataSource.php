<?php

namespace App\Console\Commands\Actions\Helpdesk\CannedResponse;

use App\Console\Commands\Action;

class CannedResponseListDataSource extends Action
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'datasource:cannedResponselist {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Data Source for Canned Response List ';

    /**
     * Execute the console command.
     */


    public function apply()
    {

        $page = $this->input['page'] ?? 1;
        $per_page = !empty($this->input['per_page']) ? $this->input['per_page']: ($this->hugeData ? 100000 : 10);
        $offset = ($page - 1) * $per_page;

        $obj =  $this->tenantDB()->table('helpdesk_canned_responses')
            ->select(
                'canned_response_id as id',
                'canned_title',
                'canned_response',
                'status',
                'sms_response'
            )
            ->orderBy('created_date', 'desc');

            $count = $obj->count();

            $obj = $obj->offset($offset);
            $obj = $obj->limit($per_page);

            $result = $obj->get();
            $this->data = $result;
            $this->meta['pagination']['total']=$count;
    }
}
