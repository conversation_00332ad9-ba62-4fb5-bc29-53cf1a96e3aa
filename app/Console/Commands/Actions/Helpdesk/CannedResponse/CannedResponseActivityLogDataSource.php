<?php

namespace App\Console\Commands\Actions\Helpdesk\CannedResponse;

use App\Console\Commands\Action;
use App\Http\Traits\MongoTraits;

class CannedResponseActivityLogDataSource extends Action
{
    use MongoTraits; // Use the MongoTraits trait in this class

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'datasource:cannedResponseActivityLog {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Canned Response Activity Log Data Source';

    /**
     * Execute the console command.
     */
   
    public function apply()
    {
        $module = $this->input['module'];
        $company_id = $this->input['company_id'];
        $canned_response_id = $this->input['canned_response_id'];
        $fields = $this->input['fields'] ?? [];
        if(!empty($fields)){
            $fields = json_decode($fields);
        }

        $this->data = $this->showNotification($module, $company_id, $canned_response_id, $fields);
    }
}
