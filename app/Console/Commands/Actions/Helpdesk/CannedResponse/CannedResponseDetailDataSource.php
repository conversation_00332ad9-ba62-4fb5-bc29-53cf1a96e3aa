<?php

namespace App\Console\Commands\Actions\Helpdesk\CannedResponse;

use App\Console\Commands\Action;
use Illuminate\Support\Facades\DB;
use App\Helpers\UserHelper;

class CannedResponseDetailDataSource extends Action
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'datasource:cannedResponseDetail {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Data Source for Canned Response Detail';

    /**
     * Execute the console command.
     */
    public function apply()
    {

        $userHelper = new UserHelper();

        $canned_response_id = $this->input['canned_response_id'];

        $obj = $this->tenantDB()->table('helpdesk_canned_responses')
            ->select(
                'canned_response_id',
                'canned_title',
                'canned_response',
                'status',
                'created_by',
                'created_date',
                'updated_by',
                'updated_date',
                'sms_response',
                DB::raw("DATE_FORMAT(helpdesk_canned_responses.created_date, '%d/%m/%Y') as created_date"),
                DB::raw("DATE_FORMAT(helpdesk_canned_responses.updated_date, '%d/%m/%Y') as updated_date"),
            )
            ->where('canned_response_id', $canned_response_id)
            ->first();

        if (!$obj) {
            $this->status = 'error';
            $this->message = 'Unable to Fetch Help Topic Data';
            $this->statusCode = 400;
            return;
        }

        $obj->created_by = $userHelper->getUserName($obj->created_by);
        $obj->updated_by = $userHelper->getUserName($obj->updated_by);

        $this->data = $obj;
    }

}
