<?php

namespace App\Console\Commands\Actions\Helpdesk\CannedResponse;

use App\Console\Commands\Action;

class CannedResponseEditDataSource extends Action
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'datasource:cannedResponseEdit {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Canned Response Edit DataSource';

    /**
     * Execute the console command.
     */
    public function apply()
    {

        $canned_response_id = $this->input['canned_response_id'];

        $updateData = [
            'canned_title' => $this->input['canned_title'],
            'canned_response' => $this->input['canned_response'],
            'sms_response' => $this->input['sms_response'],
            'status' => $this->input['status'],
            'updated_date' => date('Y-m-d H:i:s'),
            'updated_by' => $this->input['updated_by'] ?? 0
        ];


        $obj = $this->tenantDB()->table('helpdesk_canned_responses')
            ->where('canned_response_id', $canned_response_id)
            ->update($updateData);

        if ($obj) {
            $this->status = 'success';
            $this->message = 'Canned Response Updated successfully';
            $this->statusCode = 200;
        } else {
            $this->status = 'error';
            $this->message = 'Unable to Update Canned Response';
            $this->statusCode = 400;
        }
    }
}
