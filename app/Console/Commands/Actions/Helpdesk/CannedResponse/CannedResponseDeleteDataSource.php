<?php

namespace App\Console\Commands\Actions\Helpdesk\CannedResponse;

use App\Console\Commands\Action;

class CannedResponseDeleteDataSource extends Action
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'datasource:cannedResponseDelete {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Canned Response Delete DataSource';

    /**
     * Execute the console command.
     */
    public function apply()
    {
        $canned_response_id = $this->input['canned_response_id'];

        $result = $this->tenantDB()->table('helpdesk_canned_responses')
            ->where('canned_response_id', $canned_response_id)
            ->delete();
        
        if($result){
            $this->status = 'success';
            $this->message = 'Canned Response deleted successfully';
            $this->statusCode = 200;
            $this->data = [];
        }
        else{
            $this->status = 'error';
            $this->message = 'Canned Response not deleted';
            $this->statusCode = 400;
            $this->data = [];
        }
    }
}
