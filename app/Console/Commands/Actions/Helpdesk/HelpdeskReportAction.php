<?php

namespace App\Console\Commands\Actions\Helpdesk;

use Illuminate\Console\Command;
use App\Console\Commands\Action;

class HelpdeskReportAction extends Action
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'datasource:helpdeskReport {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Helpdesk Report list';

    protected $formatterByKeys = ['id'];

    protected $mapper = [];

    public function apply()
    {
        $ticketNo = '';
        $fromDate = date("Y-m-01");
        $toDate = date("Y-m-t");
        $isOverdue = '';
        $isEscalated = '';

        if (!empty($this->input['filters'])) {
            $ticketNo =
                !empty($this->input['filters']['ticket_no']) ? $this->input['filters']['ticket_no'] : '';
            unset($this->input['filters']['ticket_no']);

            $isOverdue =
                !empty($this->input['filters']['is_overdue']) ? $this->input['filters']['is_overdue'] : '';
            unset($this->input['filters']['is_overdue']);

            $isEscalated =
                !empty($this->input['filters']['is_escalated']) ? $this->input['filters']['is_escalated'] : '';
            unset($this->input['filters']['is_escalated']);

            $date = $this->getFromAndToDate($this->input['filters']);
            $fromDate = $date[0];
            $toDate = $date[1];
        }

        $obj = $this->tenantDB()->table('helpdesk_issues2 AS issues')
                                ->selectRaw(
                                    'issues.issue_id as id,
                                    issues.soc_id,
                                    issues.fk_help_topic_id,
                                    issues.title,
                                    issues.body,
                                    CONCAT("#", issues.ticket_number) AS ticket_number,
                                    issues.raised_by,
                                    issues.raised_by_name,
                                    issues.raised_by_email,
                                    issues.raised_by_phone,
                                    issues.raised_by_flat,
                                    issues.canned_response_id,
                                    issues.user_email_id,
                                    issues.assignee_type,
                                    issues.assignee,
                                    issues.assignee_user,
                                    issues.committee_id,
                                    issues.priority,
                                    issues.status,
                                    issues.src,
                                    issues.is_overdue,
                                    issues.is_answered,
                                    issues.last_message,
                                    issues.last_message_body,
                                    issues.is_active,
                                    issues.due_date,
                                    issues.is_escalated,
                                    issues.last_message_date,
                                    issues.created_by,
                                    issues.updated_date,
                                    issues.updated_by,
                                    issues.created_date,
                                    members.id as member_id,
                                    members.fk_unit_id,
                                    members.member_first_name,
                                    members.member_last_name,
                                    members.member_type_id,
                                    members.member_last_name,
                                    members.member_email_id,
                                    members.member_mobile_number,
                                    members.salute,
                                    members.gstin,
                                    members.member_intercom,
                                    members.effective_date,
                                    members.member_dob,
                                    members.member_gender,
                                    members.user_id,
                                    units.fk_unit_category_id,
                                    units.soc_building_id,
                                    units.soc_building_floor,
                                    units.unit_id,
                                    units.unit_flat_number,
                                    units.soc_building_name,
                                    units.unit_category'
                                )
                                ->leftJoin('chsone_members_master AS members', 'members.id', '=', 'issues.raised_by')
                                ->leftJoin('chsone_units_master AS units', 'units.unit_id', '=', 'members.fk_unit_id')
                                ->where('issues.is_active', '1');

        if ($ticketNo) {
            $ticketNo = trim(ltrim($ticketNo, "#"));
            $obj = $obj->whereRaw("issues.ticket_number LIKE '%{$ticketNo}%'");
        }

        if ($isOverdue) {
            $obj = $obj->where("issues.is_overdue", '1');
        }

        if ($isEscalated) {
            $obj = $obj->where("issues.is_escalated", '1');
        }

        if ($fromDate && $toDate) {
            $obj = $obj->where("issues.created_date", ">=", $fromDate)
                       ->where("issues.created_date", "<=", $toDate);
        }

        $obj = $obj->orderByDesc("issues.issue_id");
        // $obj = $this->filter($obj);
        $helpdeskReport = $obj->get()->toArray();
        // dd($helpdeskReport);

        $counts = [];
        $counts['open'] = $this->tenantDB()->table('helpdesk_issues2 AS issues')
                        ->where('status', 'open')
                        ->where('is_active', '1')
                        ->count();
        $counts['reopened'] = $this->tenantDB()->table('helpdesk_issues2 AS issues')
                        ->where('status', 'reopened')
                        ->where('is_active', '1')
                        ->count();
        $counts['closed'] = $this->tenantDB()->table('helpdesk_issues2 AS issues')
                        ->where('status', 'closed')
                        ->where('is_active', '1')
                        ->count();
        $counts['on_hold'] = $this->tenantDB()->table('helpdesk_issues2 AS issues')
                        ->where('status', 'onhold')
                        ->where('is_active', '1')
                        ->count();
        $counts['resolved'] = $this->tenantDB()->table('helpdesk_issues2 AS issues')
                        ->where('status', 'resolved')
                        ->where('is_active', '1')
                        ->count();
        $summary = [
            "open" => $counts['open'],
            "closed" => $counts['closed'],
            "on_hold" => $counts['on_hold'],
            "resolved" => $counts['resolved'],
            "reopened" => $counts['reopened'],
        ];
        $results = [];

        if (count($helpdeskReport) == 0) {
            $summary = [];
        }
        if (!empty($helpdeskReport)) {
            foreach ($helpdeskReport as &$record) {
                $record->created_date = $this->getDisplayDate(explode(" ", $record->created_date)[0]);
                $results[] = $record;
            }
        }
        // dd($results);
        $final = [
            $results,
            [$summary]
        ];
        $this->data = $final;
    }


    public function getFromAndToDate($inputs){
        // $date = $inputs['transaction_date'];
        // $dateRange = explode(',', $date);
        $dateRange = $inputs;
        $from_date = $dateRange['startDate'] ?? date("Y-m-01");
        $to_date = $dateRange['endDate'] ?? date("Y-m-t");
        return array($from_date, $to_date);
    }
}
