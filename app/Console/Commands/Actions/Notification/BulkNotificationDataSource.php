<?php

namespace App\Console\Commands\Actions\Notification;

use App\Console\Commands\Action;
use Illuminate\Support\Facades\DB;
use Curl\Curl; // Import Curl class

class BulkNotificationDataSource extends Action
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'datasource:bulkNotification {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Bulk Notification Data Source';

    /**
     * Execute the console command.
     */
    public function apply()
    {

        $fcm_url = "https://fcm.googleapis.com/fcm/send";
        $fcm_authkey = env('FCM_SERVER_KEY');
        $priority = $this->input['priority'] ?? 'high';
        $title = $this->input['title'];
        $message = $this->input['message'];
        $databaseName = env('DB_AUTH_CONNECTION');

        $tenant_id = 'soc_db_' . $this->input['company_id']; // 'soc_db_
        // $result = DB::table($databaseName . '.device_registry as dr')
        //     ->select('dr.firebase_token', 'dr.device_key', 'dr.app_id', 'dr.status', 'dr.device_id', 'm.soc_id', 'm.member_first_name')
        //     ->join($tenant_id . '.chsone_members_master as m', 'dr.user_id', '=', 'm.user_id')
        //     ->where('dr.app_id', $this->input['app_ids'])
        //     ->where('m.member_type_id', 1)
        //     ->where('m.status', 1)
        //     ->where('dr.status', 'active')
        //     ->get();

        $app_ids = json_decode($this->input['app_ids']);
        $result = $this->authDB()->table('device_registry as dr')
            ->select('dr.firebase_token', 'dr.device_key', 'dr.app_id', 'dr.status', 'dr.device_id', 'm.soc_id', 'm.member_first_name')
            ->join($tenant_id . '.chsone_members_master as m', 'dr.user_id', '=', 'm.user_id')
            ->where('m.member_type_id', 1)
            ->where('dr.status', 'active')
            ->where('dr.app_id', $app_ids)
            ->get();

        // $result =   $this->authDB()->table('.device_registry as dr')
        //     ->join($tenant_id . '.chsone_members_master as m', 'dr.user_id', '=', 'm.user_id')
        //     ->whereIN('app_id', $this->input['app_ids'])
        //     ->where('m.member_type_id', 1)
        //     ->where('dr.status', 'active')
        //     ->where('m.status', 1)
        //     ->get();


        $registration_ids = $result->pluck('firebase_token')->toArray();

        $data = [
            'notification' => [
                'title' => $title,
                'body' => $message,
            ],
            'priority' => $priority,
            'registration_ids' => $registration_ids,
        ];

        $data = json_encode($data);

        // Set up CURL request
        // $curl = new Curl();
        // $curl->setUserAgent('');
        // $curl->setReferrer('');
        // $curl->setOpt(CURLOPT_RETURNTRANSFER, true);
        // $curl->setOpt(CURLOPT_MAXREDIRS, 10);
        // $curl->setOpt(CURLOPT_TIMEOUT, 30);
        // $curl->setHeader('Authorization', 'key=' . $fcm_authkey); // Set the Authorization header
        // $curl->setHeader('Content-Type', 'application/json');
        // $curl->post($fcm_url, $data);
        // $this->data= $curl->response;

        // Initialize cURL
        $curl = curl_init();

        // Set the URL to send the POST request
        $url = "https://fcm.googleapis.com/fcm/send";
        curl_setopt($curl, CURLOPT_URL, $url);

        // Set the request method to POST
        curl_setopt($curl, CURLOPT_POST, true);

        // Set the request data
        curl_setopt($curl, CURLOPT_POSTFIELDS, $data);

        // Set additional cURL options
        curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($curl, CURLOPT_MAXREDIRS, 10);
        curl_setopt($curl, CURLOPT_TIMEOUT, 30);

        // Set request headers
        $headers = [
            'Authorization: key=' . $fcm_authkey,
            'Content-Type: application/json',
        ];
        curl_setopt($curl, CURLOPT_HTTPHEADER, $headers);

        // Execute the cURL request
        $response = curl_exec($curl);

        // Check for errors
        if ($response === false) {
            $error = curl_error($curl);
            // Handle the error
            // echo "cURL Error: $error";
        }

        // Close cURL session
        curl_close($curl);

        // Assign the response to $this->data

    }
}
