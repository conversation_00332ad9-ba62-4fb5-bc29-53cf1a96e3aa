<?php

namespace App\Console\Commands\Actions\Notification;

use App\Console\Commands\Action;
use Curl\Curl; // Import Curl class

class FcmNotificationDataSource extends Action
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'datasource:fcmNotification {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Fcm Notification Data Source';

    /**
     * Execute the console command.
     */

    public function apply()
    {

        $fcm_authkey = env('FCM_SERVER_KEY');
        $title = $this->input['title'];
        $message = $this->input['message'];
        $user_id = trim($this->input['user_id'] ?? '', ',');
        $priority = $this->input['priority'] ?? 'high';
        $app_ids = $this->input['app_ids'];
        $fcm_url = "https://fcm.googleapis.com/fcm/send";
        $mobile = $this->input['mobile'] ?? NULL;
        $hide_ios = $this->input['hide_ios'] ?? NULL;
        $sound = $this->input['sound_file'] ?? 'custom_notification.wav';
        $redirect_data = $this->input['data'] ?? NULL;
        $redirect_data_object = json_decode($redirect_data);

        if (!isset($user_id) && $mobile) {
            $user = $this->masterDB()->table('chsone_users_master')->select('user_id')->where('user_mobile_number', $mobile)->first();
            $user_id = $user->user_id;
        }

        if ($hide_ios) {
            $device_keys = $this->authDB()->table('device_registry')
                ->whereIN('user_id', $user_id)
                ->whereIN('app_id', $app_ids)
                ->whereNotEqual('platform', 'ios')->get();
        } else {
            $device_keys = $this->authDB()->table('device_registry')->where('user_id', $user_id)->where('app_id', $app_ids)->get();
        }

        $fcm_tokens = [];
        foreach ($device_keys as $device_key) {
            if ($device_key->firebase_token && $device_key->firebase_token !== '') {
                array_push($fcm_tokens, $device_key->firebase_token);
            }
        }

        $data = [
            'notification' => [
                'title' => $title,
                'body' => $message,
                'sound' => $sound,
            ],
            'priority' => $priority,
            'registration_ids' => $fcm_tokens,
            'data' => $redirect_data_object
        ];

        $data = json_encode($data);

        // // Set up CURL request
        // $curl = new Curl();
        // $curl->setUserAgent('');
        // $curl->setReferrer('');
        // $curl->setOpt(CURLOPT_RETURNTRANSFER, true);
        // $curl->setOpt(CURLOPT_MAXREDIRS, 10);
        // $curl->setOpt(CURLOPT_TIMEOUT, 30);
        // $curl->setHeader('Authorization', 'key=' . $fcm_authkey); // Set the Authorization header
        // $curl->setHeader('Content-Type', 'application/json');
        // $curl->post($fcm_url, $data);



        // Initialize cURL
        $curl = curl_init();

        // Set the URL to send the POST request
        $url = "https://fcm.googleapis.com/fcm/send";
        curl_setopt($curl, CURLOPT_URL, $url);

        // Set the request method to POST
        curl_setopt($curl, CURLOPT_POST, true);

        // Set the request data
        curl_setopt($curl, CURLOPT_POSTFIELDS, $data);

        // Set additional cURL options
        curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($curl, CURLOPT_MAXREDIRS, 10);
        curl_setopt($curl, CURLOPT_TIMEOUT, 30);

        // Set request headers
        $headers = [
            'Authorization: key=' . $fcm_authkey,
            'Content-Type: application/json',
        ];
        curl_setopt($curl, CURLOPT_HTTPHEADER, $headers);

        // Execute the cURL request
        $response = curl_exec($curl);


        // Check for errors
        if ($response === false) {
            $error = curl_error($curl);
            // Handle the error
            // echo "cURL Error: $error";
        } else {
            // Process the response
            // echo "Response: $response";
        }

        // Close cURL session
        curl_close($curl);


    }


}
