<?php

namespace App\Console\Commands\Actions\Notification;
use App\Console\Commands\Action;
use Illuminate\Support\Facades\DB;
use Curl\Curl; // Import Curl class


class BroadcastNotificationDataSource extends Action
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'datasource:broadcastNotification  {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Broadcast Notification Data Source';

    /**
     * Execute the console command.
     */
    public function apply()
    {
        $fcm_url = "https://fcm.googleapis.com/fcm/send";
        $fcm_authkey = env('FCM_SERVER_KEY');
        $title = $this->input['title'];
        $message = $this->input['message'];
        $databaseName = env('DB_AUTH_CONNECTION');
        $result = DB::table($databaseName . '.device_registry as dr')
            ->select('dr.firebase_token', 'dr.device_key', 'dr.app_id', 'dr.status', 'dr.device_id', 'm.soc_id', 'm.member_first_name')
            ->where('dr.app_id', $this->input['app_ids'])
            ->where('m.member_type_id', 1)
            ->where('m.status', 1)
            ->where('dr.status', 'active')
            ->get();

        $registration_ids = $result->pluck('firebase_token')->toArray();

        $data = [
            'notification' => [
                'title' => $title,
                'body' => $message,
            ],
            'priority' => 'high',
            'registration_ids' => $registration_ids,
        ];

        $data = json_encode($data);

        // Set up CURL request
        $curl = new Curl();
        $curl->setUserAgent('');
        $curl->setReferrer('');
        $curl->setOpt(CURLOPT_RETURNTRANSFER, true);
        $curl->setOpt(CURLOPT_MAXREDIRS, 10);
        $curl->setOpt(CURLOPT_TIMEOUT, 30);
        $curl->setHeader('Authorization', 'key=' . $fcm_authkey); // Set the Authorization header
        $curl->setHeader('Content-Type', 'application/json');
        $curl->post($fcm_url, $data);
        $this->data= $curl->response;


    }
}
