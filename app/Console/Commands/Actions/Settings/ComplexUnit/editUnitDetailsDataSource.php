<?php

namespace App\Console\Commands\Actions\Settings\ComplexUnit;

use Illuminate\Console\Command;
use App\Console\Commands\Action;
use App\Models\Tenants\ChsoneGrpLedgerTree;
use App\Models\Tenants\ChsoneLedgerTransaction;
use App\Models\Tenants\ChsoneMembersMaster;
use App\Models\Tenants\ChsoneParkingAllotmentDetail;
use App\Models\Tenants\ChsoneSocietyUnitsTpl;
use App\Models\Tenants\ChsoneUnitsMaster;
use App\Models\Tenants\FacilityBookingAccount;

class editUnitDetailsDataSource extends Action
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'datasource:editUnitDetails {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Edit unit details';

    /**
     * Execute the console command.
     */
    public function apply()
    {
        $unit_id = $this->input['id'];
        $companyId = $this->input['company_id'];
        $socBldgName = $this->input['soc_building_id'];
        $soc_units_type_id = $this->input['fk_unit_category_id'];
        $floor = $this->input['soc_building_floor'];
        $identifiedAs = $this->input['unit_flat_number'];
        $occupancyType = $this->input['occupancy_type'];
        $chargingType = $this->input['charge_type'];
        $unitWEF = $this->input['effective_date'];
        $area = $this->input['unit_area'] ?? null;
        $openArea = $this->input['unit_open_area'] ?? null;
        $waterInlets = $this->input['unit_total_water_inlets'] ?? null;
        $chargeable = $this->input['chargeable'] ?? 0;
        $lateCharge = $this->input['income_late_charge_id'] ?? null;
        $extraAttr = isset($this->input['extra_attributes']) ? $this->input['extra_attributes'] : null;
        $status = $this->input['status'] ?? 1;
        $currentDate = $this->getCurrentDate('database');
        
        // first check if the unit id details is exist or not
        $unitDetails = $this->tenantDB()->table('chsone_units_master')->where('unit_id', $unit_id)->first();

        if(!$unitDetails)
        {
            $this->status = 'error';
            $this->message = 'No unit found with this ID';
            $this->statusCode = 400;
            $this->data = [];
        }
        else
        {
            // get unit details
            $units = ChsoneUnitsMaster::where('unit_id', $unit_id)
            ->where('soc_id', $companyId)
            ->where(function($query) use ($currentDate){
                $query->where('cancel_date', '0000-00-00')
                ->orWhere('cancel_date', '>', $currentDate);
            })
            ->first();
            $unitData = $units->toArray();

            // fetch society building name
            $socBldg = $this->tenantDB()->table("chsone_societies_building_master")
            ->where("soc_building_id", $socBldgName)
            ->select("soc_building_name")
            ->first();
            $socBuildingName = $socBldg->soc_building_name;

            // get unit category details
            $unitCat = $this->tenantDB()->table("chsone_society_units_tpl")
            ->where("soc_units_type_id", $soc_units_type_id)
            ->first();
            $unitCatId = $unitCat->soc_units_type_id;
            $unitCatType = $unitCat->soc_unit_type;
            $unitType = $unitCat->type;

            // if (($units->soc_building_name != $socBuildingName) || ($units->unit_flat_number != $identifiedAs))
            // {
                $units->created_date = $currentDate;
                $units->soc_id = $companyId;
                $units->fk_unit_category_id = $unitCatId;
                $units->unit_category = $unitType;
                $units->unit_type = $unitCatType;
                // $units->ledger_account_id = $units->ledger_account_id;
                $units->soc_building_id = $socBldgName;
                $units->soc_building_name = $socBuildingName;
                $units->soc_building_floor = $floor;
                $units->unit_flat_number = $identifiedAs;
                $units->unit_area = $area;
                $units->unit_open_area = $openArea;
                $units->unit_total_water_inlets = $waterInlets;
                $units->occupancy_type = $occupancyType;
                $units->income_late_charge_id = $lateCharge;
                // $units->income_late_charge_id = isset($lateCharge) ? $lateCharge : $units->income_late_charge_id;
                $units->chargeable = $chargeable;
                $units->charge_type = $chargingType;
                $units->extra_attributes = $extraAttr;
                $units->updated_by = $this->input['user_id'];
                $units->updated_date = $this->getCurrentDate('database') . " " . date('H:i:s');
                $units->effective_date = $this->getDatabaseDate($unitWEF);
                if(isset($status)) {
                    $units->status = $status;
                } else {
                    $units->status = 1;
                }

                // check member allocation
                $memberAlotted = ChsoneMembersMaster::where('soc_id', $companyId)->where('fk_unit_id', $unit_id)->where('status', 1)->first();

                // check tenant allocation
                $tenantAlottedCount = ChsoneMembersMaster::where('soc_id', $companyId)->where('fk_unit_id', $unit_id)->where('member_type_id', 4)->where('status', 1)->first();
                if (!empty($memberAlotted)) { // && count($memberAlotted) > 0
                    $units->is_occupied = 1;
                    $units->is_allotted = 1;
                    if($tenantAlottedCount > 0){
                        $units->occupied_by = "tenant";
                    }else{
                        $units->occupied_by = "owner";
                    }
                } else {
                    $units->is_occupied = $unitData['is_occupied'];
                    $units->is_allotted = $unitData['is_allotted'];
                    $units->occupied_by = $unitData['occupied_by'];
                    // if ($occupancyType == 'common') {
                    //     $units->occupied_by = "guest";
                    // } else {
                    //     if ($unitData['occupancy_type'] == 'reserved') {
                    //         $units->occupied_by = $unitData['occupied_by'];
                    //     }else{
                    //          $units->is_occupied = 0;
                    //          $units->is_allotted = 0;
                    //          $units->occupied_by = "vacant";
                    //     }
                    // }
                }

                if (strtolower($unitCatType) != 'parking') {
                    $ledge_name = array();

                    if (!empty($socBuildingName)) {
                        $ledgerName[] = "BLDG#".$socBuildingName;
                    }
                    if (!empty($identifiedAs)) {
                        $ledgerName[] = $identifiedAs;
                    }
                    $ledgerName = strtoupper(implode("-", $ledgerName));

                    // update chsone_grp_ledger_tree table
                    $ledgerGroupTree = ChsoneGrpLedgerTree::where('soc_id', $companyId)->where('ledger_account_id', $units->ledger_account_id)->first();
                    $ledgerGroupTree->ledger_account_name = $ledgerName;
                    $ledgerGroupTree->save();

                    // update chsone_ledger_transaction table
                    $ledgerGroupTxn = ChsoneLedgerTransaction::where('soc_id', $companyId)->where('ledger_account_id', $units->ledger_account_id)->get();
                    foreach($ledgerGroupTxn as $txn){
                        $txn->ledger_account_name = $ledgerName;
                        $txn->save();
                    }
                }

                $units->save();
                if (!$units->Save()) {
                    $this->statusCode = 400;
                    $this->status = "error";
                    $this->message = "Error while adding unit";
                } else {
                    $this->updateParking($identifiedAs,$units->unit_id);
                    if (strtolower($occupancyType) == 'common') {
                        $facilityAccounts = new FacilityBookingAccount();
                        $facilityAccounts->soc_id = $this->input['company_id'];
                        $facilityAccounts->fk_unit_id = $units->unit_id;
                        if(isset($ledgerId) && !empty($ledgerId)) {
                            $facilityAccounts->fk_income_ledger_id = (isset($ledgerId) && !empty($ledgerId)) ? $ledgerId : 0;
                        }
                        $facilityAccounts->account_name = 'Rentals';
                        $facilityAccounts->account_type = 'member';
                        // $facilityAccounts->fk_ledger_id = $this->getBankledger('bank');
                        // $facilityAccounts->fk_cash_ledger_id = $this->getBankledger('cash');
                        $facilityAccounts->created_date = $this->getCurrentDate('database');
                        $facilityAccounts->updated_date = $this->getCurrentDate('database');
                        $facilityAccounts->created_by = $this->input['user_id'];
                        $facilityAccounts->updated_by = $this->input['user_id'];
                        if($facilityAccounts->save())
                        {
                            $this->statusCode = 200;
                            $this->status = "success";
                            $this->message = "Unit Updated successfully";
                            $this->data = $units->toArray();
                        }
                    }

                    // pass unit data to the vizlog api
                    // $send_sync = $this->sendUnitsData(array('unitIds' => $units->unit_id, 'soc_id' => $this->input['company_id']));

                    $this->statusCode = 200;
                    $this->status = "success";
                    $this->message = "Unit updated successfully";
                    $this->data = $units->toArray();
                }
            // } else {
            //     $this->statusCode = 400;
            //     $this->status = "error";
            //     $this->message = "No changes found with this unit ID";
            // }
        }
    }

    public function updateParking($identifiedAs, $unitId)
    {
        // Find the first record with the given unit ID and status = 1
        $parkingAllotment = ChsoneParkingAllotmentDetail::where('fk_parking_unit_id', $unitId)->where('status', 1)->first();

        if (!empty($parkingAllotment)) {
            // Update the parking number
            $parkingAllotment->parking_number = $identifiedAs;

            // Save the changes
            if ($parkingAllotment->save()) {
                return true;
            }
        }

        return false;
    }

    public function sendUnitsData($data)
    {
        $unitIds= $data['unitIds'];
        $soc_id= $data['soc_id'];
        // $unitIds=implode(',', $unitIds);
        $units = ChsoneUnitsMaster::where('unit_id', $unitIds)->get();
        if(!empty($units)){
            $unitsdata['units'] = $units->toArray();
            try{
                $param_data = array('data'=>json_encode($unitsdata));
                // $res = $client->post(VIZLOG_URL.$soc_id.'/syncdata?api_key='.VIZLOG_API_KEY, ['form_params' => $param_data]);
                // add curl request here
                $param_data = ['form_params' => $param_data];
                $curl = curl_init();
                curl_setopt_array($curl, array(
                    CURLOPT_URL => env('VIZLOG_URL').$soc_id.'/syncdata?api_key='.env('VIZLOG_API_KEY'),
                    CURLOPT_RETURNTRANSFER => true,
                    CURLOPT_ENCODING => "",
                    CURLOPT_MAXREDIRS => 10,
                    CURLOPT_TIMEOUT => 0,
                    CURLOPT_FOLLOWLOCATION => true,
                    CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                    CURLOPT_CUSTOMREQUEST => "POST",
                    CURLOPT_POSTFIELDS => $param_data,
                    CURLOPT_HTTPHEADER => array(
                        "Content-Type: application/json",
                    ),
                ));
                $response = curl_exec($curl);
                curl_close($curl);
                $res = json_decode($response);
                $jsonResponse = $res->getBody()->getContents();
                if($this->updateChsoneIds($jsonResponse)){
                    return true;
                }
            }catch (\Exception $e) {
               return false;
           }
        }
    }

    public function updateChsoneIds($data){
        $data= json_decode($data, true);
        $data= $data['data'];
        // $result =$this->setup->setupmainsetting('setupsetting:saveSyncdata',$data);
        $result =$this->saveSyncdata($data);
        if($result['error'])
        {
            return false;
        }
        return true;
    }

    public function saveSyncdata($data=array())
    {
        try
        {
            $success = array();
            $failed = array();
            $failedFlag=false;
            if(isset($data['buildings']) && !empty($data['buildings']))
            {
                $success['buildings']= array();
                $failed['buildings']= array();
                foreach($data['buildings'] as $building)
                {
                    $buildingData = ChsoneSocietyUnitsTpl::where('soc_building_id', $building['vendor_reference_id'])->first();
                    if($buildingData)
                    {
                        $buildingData->vizlog_building_id = $building['building_id'];
                        if($buildingData->save())
                        {
                            $success['buildings'][]=$building['building_id'];
                        }
                        else
                        {
                           $failed['buildings'][]=$building['building_id'];
                           $failedFlag=true;
                        }
                    }
                    else
                    {
                        $failed[] = $building['soc_building_id'];
                        $failedFlag=true;
                    }
                }
            }

            if(isset($data['units']) && !empty($data['units']))
            {
                $success['units']= array();
                $failed['units']= array();
                foreach($data['units'] as $unit)
                {
                    $unitData = ChsoneUnitsMaster::where('unit_id', $unit['vendor_reference_id'])->first();
                    if($unitData)
                    {
                        $unitData->vizlog_unit_id = $unit['building_unit_id'];
                        if($unitData->save())
                        {
                            $success['units'][]=$unit['building_unit_id'];
                        }
                        else
                        {
                           $failed['units'][]=$unit['building_unit_id'];
                           $failedFlag=true;
                        }
                    }
                    else
                    {
                        $failed[] = $unit['building_unit_id'];
                        $failedFlag=true;
                    }
                }
            }

            if(isset($data['members']) && !empty($data['members']))
            {
                $success['members']= array();
                $failed['members']= array();
                foreach($data['members'] as $member)
                {
                    $memberData = ChsoneMembersMaster::where('id', $member['vendor_reference_id'])->first();
                    if($memberData)
                    {
                        $memberData->vizlog_member_id = $member['member_id'];
                        if($memberData->save())
                        {
                            $success['members'][]=$member['member_id'];
                        }
                        else
                        {
                           $failed['members'][]=$member['member_id'];
                           $failedFlag=true;
                        }
                    }
                    else
                    {
                        $failed[] = $member['member_id'];
                        $failedFlag=true;
                    }
                }
            }
            return array('error' => false,'success' => $success,'failed' => $failed,'is_failed'=> $failedFlag );
        }
        catch (\Exception $e) {
            return ['error'=>true,'message'=>$e->getMessage()];
        }
    }
}
