<?php

namespace App\Console\Commands\Actions\Settings\ComplexUnit;

use Illuminate\Console\Command;
use App\Console\Commands\Action;

class GenerateVPADataSource extends Action
{
    protected $signature = 'datasource:generateVPA {flowId} {parentId} {input}';

    protected $description = 'Generate VPA for a unit';

    public function apply()
    {
        $id = $this->input['id'];
        $data = $this->action('datasource:getUnitDetail', $this->pointer, $this->input);

        $societyId = $this->input['company_id'];
        // get the society details
        $society = $this->masterDB()->table("chsone_societies_master")
            ->where('soc_id', $societyId)
            ->first();

        // where CHSONE is the fix string then the first 8 letters of the society name without space then unit building id then unit flat number except the last digit and make it uppercase
        $vpa = strtoupper("CHSONE" . substr(str_replace(' ', '', $society->soc_name), 0, 7) . $data['soc_building_id'] . substr($data['unit_flat_number'], 0, -1));
        
        // check if the vpa is 0 then update the new vpa
        if($data['vpa'] == 0)
        {
            $this->tenantDB()->table("chsone_units_master")
                ->where('unit_id', $id)
                ->update([
                    'vpa' => $vpa
                ]);

            $this->status = 'success';
            $this->message = 'VPA generated for all unit successfully';
            $this->statusCode = 200;
            $this->data = [
                'vpa' => $vpa
            ];
        }
        else{
            $this->status = 'error';
            $this->message = 'VPA already generated';
            $this->statusCode = 400;
            $this->data = [];
        }
    }
}