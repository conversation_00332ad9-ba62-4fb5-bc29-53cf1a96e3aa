<?php

namespace App\Console\Commands\Actions\Settings\ComplexUnit;

use Illuminate\Console\Command;
use App\Console\Commands\Action;
use Illuminate\Support\Facades\DB;

class fetchLatePaymentChargesListDataSource extends Action
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'datasource:fetchLatePaymentChargesList {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Fetch late payment charges list for edit unit details';

    /**
     * Execute the console command.
     */
    public function apply()
    {
        // Fetch late payment charges list whose status is active
        $latePaymentRules = $this->tenantDB()->table('income_late_payment_charges')
        ->select(
            "id",
            DB::raw('CASE WHEN interest_amount_type = "percentage" THEN CONCAT(simple_interest, "(%)") ELSE simple_interest END as late_charges'),
            'effective_date',
            DB::raw('CONCAT(grace_period, " days") as grace_period'),
            DB::raw('CASE WHEN calculate_from = "billdate" THEN "Bill Date" ELSE "Due Date" END as late_charges_will_apply_from'),
            DB::raw('CASE WHEN calculate_for = "billperiod" THEN "Bill Period" ELSE "Per Day" END as late_charges_will_apply_for'),
            DB::raw('CASE WHEN effective_date >= (SELECT fy_start_date FROM soc_account_financial_year_master WHERE closed = 0)
                    AND effective_date <= (SELECT fy_end_date FROM soc_account_financial_year_master WHERE closed = 0)
                    THEN "Active" ELSE "Past" END as status')
        )
        ->where('type','maintenance')
        ->having('status', 'Active')
        ->orderBy('effective_date', 'desc')
        ->get();


        $this->data = $latePaymentRules;
    }
}
