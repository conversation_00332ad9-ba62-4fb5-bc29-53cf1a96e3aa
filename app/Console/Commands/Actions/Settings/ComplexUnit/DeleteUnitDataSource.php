<?php

namespace App\Console\Commands\Actions\Settings\ComplexUnit;

use Illuminate\Console\Command;
use App\Console\Commands\Action;
use App\Models\Tenants\ChsoneMembersMaster;
use App\Models\Tenants\ChsoneMemberVehicleDetail;
use App\Models\Tenants\ChsoneParkingAllotmentDetail;
use App\Models\Tenants\ChsoneUnitsMaster;

class DeleteUnitDataSource extends Action
{
    protected $signature = 'datasource:deleteUnit {flowId} {parentId} {input}';

    protected $description = 'Delete a unit';

    public function apply()
    {
        $unit_id = $this->input['id'];
        $soc_id = $this->input['company_id'];

        // check unit_id is exist or not in chsone_units_master table if not exist then return error
        $unitDetails = $this->tenantDB()->table('chsone_units_master')
            ->where('unit_id', $unit_id)
            ->get();

        // get flat number and flat type for message
        $flat_number = $unitDetails[0]->unit_flat_number;
        $flat_type = $unitDetails[0]->unit_type;

        if(count($unitDetails) == 0){
            $this->status = 'error';
            $this->message = 'No unit details found for this id';
            $this->statusCode = 400;
            $this->data = [];
            return;
        }

        // remove unit from chsone_units_master table
        $result = $this->tenantDB()->table("chsone_units_master")
        ->where('unit_id', $unit_id)
        ->update([
            'status' => 0,
            'cancel_date' => $this->input['cancel_date'],
            'cancellation_reason' => $this->input['cancellation_reason'],
        ]);
        if ($result) {

            //remove parking allotments with unit from chsone_parking_allotment_details table
            // $parking_allottment = new ChsoneParkingAllotmentDetail();
            $parking_allottment_status = $this->removeParkingAllottmentWithUnit($soc_id, $unit_id);

            $parkingAllotments = ChsoneParkingAllotmentDetail::where('soc_id', $soc_id)->where('fk_unit_id', $unit_id)->get();
            if (!empty($parkingAllotments)) {
                foreach ($parkingAllotments as $parkingAllotment) {
                    $units = ChsoneUnitsMaster::where('unit_id', $parkingAllotment->fk_parking_unit_id)->where('soc_id', $soc_id)->first();
                    $units->is_allotted = 0;
                    $units->is_occupied = 0;
                    $units->occupied_by = 'vacant';
                    $units->save();
                }
            }

            //remove the members with the units to be cancelled
            $membersWithUnit = $this->removeRegisteredMember($unit_id, $soc_id);
            if(!empty($membersWithUnit)) {
                foreach ($membersWithUnit as $key => $member_stat) {
                    $member_stat->status = 0;
                    $member_stat->cancel_date = $this->input['cancel_date'];
                    $member_stat->save();
                }
                $memberUnitArray = $membersWithUnit->toArray();
                $member_ids = implode(',', array_column($membersWithUnit, 'id'));
                // $member_ids = '';
                // for ($cnt = 0; $cnt < count($memberUnitArray); $cnt++) {
                //     $member_ids .= (($cnt == 0) ? '' : ',') . $memberUnitArray[$cnt]['id'];
                // }

                //cancel vehicles with member
                $vehicleCollection = ChsoneMemberVehicleDetail::where('soc_id', $soc_id)->where('status', 1)->whereIn('fk_member_id', $member_ids)->get()->toArray();

                // $member_vehicle = new ChsoneMemberVehicleDetail();
                $vehicle_status = $this->removeRegisteredVehiclesWithUnit($vehicleCollection);
                $vehicleNumberString = '';
                $i = 0;
                if (!empty($vehicleCollection)) {
                    foreach ($vehicleCollection as $key => $vehicle) {
                        // $vehicle->status = 0;
                        $vehicleNumberString .= (($key == 0) ? '' : ',') . $vehicle->vehicle_registration_number;
                        $i++;
                    }
                }

                $strMessage = 'Unit no ' . $flat_number . '(' . $flat_type . ') is successfully revoked. ';
                if (!empty($vehicleNumberString)) {
                    $strMessage.= 'Vehicles with Registration number ' . $vehicleNumberString . ' also cancelled.';
                }
            } else {
                $strMessage = 'Unit no ' . $flat_number . '(' . $flat_type . ') is successfully revoked.';
            }

            $this->status = 'success';
            $this->message = $strMessage;
            $this->statusCode = 200;
            $this->data = [];
        } else {
            $this->status = 'error';
            $this->message = 'Unit no ' . $flat_number . '(' . $flat_type . ') is not revoked.';
            $this->statusCode = 400;
            $this->data = [];
        }
    }

    public function removeParkingAllottmentWithUnit($soc_id, $unit_id) {
        // Find all parking allotments for the given society ID and unit ID
        $parkingAllotments = ChsoneParkingAllotmentDetail::where('soc_id', $soc_id)->where('fk_unit_id', $unit_id)->get()->toArray();

        // Check if there are any parking allotments
        if (!empty($parkingAllotments)) {
            // Loop through each parking allotment and delete it
            foreach ($parkingAllotments as $parkingAllotment) {
                // only update query for parking allotment set status to 0 and update date in chsone_parking_allotment_detail table
                ChsoneParkingAllotmentDetail::where('parking_allotment_id', $parkingAllotment->fk_unit_id)
                    ->update([
                        'status' => 0,
                        'updated_date' => $this->input['cancel_date'],
                        'updated_by' => $this->input['user_id'],
                    ]);
            }
        }
        return true;
    }


    public function removeRegisteredMember($unit_id, $soc_id) {
        $query = ChsoneMembersMaster::where('soc_id', $soc_id)->where('fk_unit_id', $unit_id);

        $resultSet = $query->get();

        // Optionally, convert the result set to an array
        $resultSet = $resultSet->toArray();

        return $resultSet;
    }

    public function removeRegisteredVehiclesWithUnit($vehicleCollection) {
        // Check if there are any vehicles
        if (!empty($vehicleCollection)) {
            // Loop through each vehicle and delete it
            foreach ($vehicleCollection as $vehicle) {
                // only update query for vehicle details set status to 0 and update date in chsone_parking_allotment_detail table
                ChsoneMemberVehicleDetail::where('parking_allotment_id', $vehicle->fk_member_id)
                    ->update([
                        'status' => 0,
                        'updated_date' => $this->input['cancel_date'],
                        'updated_by' => $this->input['user_id'],
                    ]);
            }
        }

        return true;
    }
}
