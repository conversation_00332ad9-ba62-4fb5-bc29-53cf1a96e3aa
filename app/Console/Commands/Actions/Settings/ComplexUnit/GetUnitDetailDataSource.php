<?php

namespace App\Console\Commands\Actions\Settings\ComplexUnit;

use Illuminate\Console\Command;
use App\Console\Commands\Action;

class GetUnitDetailDataSource extends Action
{
    protected $signature = 'datasource:getUnitDetail {flowId} {parentId} {input}';

    protected $description = 'Get unit details';

    protected $formatter = [
        "id" => "",
        "unit_id" => "",
        "soc_id" => "",
        "unit_category" => "",
        "unit_type" => "",
        "soc_building_id" => "",
        "soc_building_name" => "",
        "soc_building_floor" => "",
        "building" => "",
        "unit_flat_number" => "",
        "unit_area" => "",
        "unit_open_area" => "",
        "effective_date" => "",
        "is_allotted" => "",
        "is_occupied" => "",
        "occupancy_type" => "",
        "occupied_by" => "",
        "income_late_charge_id" => "",
        "vpa" => "",
        "status" => ""
    ];

    protected $formatterByKeys = ['id'];

    public function apply()
    {
        $id = $this->input['id'];

        $societyId = $this->input['company_id'];

        $obj = $this->tenantDB()->table("chsone_units_master as unitmaster")
            ->selectRaw(
                "unitmaster.unit_id AS id,
                unitmaster.unit_id,
                unitmaster.soc_id,
                unitmaster.fk_unit_category_id,
                unitmaster.unit_category,
                unitmaster.unit_type,
                unitmaster.soc_building_id,
                buildings.soc_building_name,
                
                unitmaster.soc_building_floor,
                
                CONCAT(buildings.soc_building_name,', Floor: ',unitmaster.soc_building_floor) AS building,
                unitmaster.unit_flat_number,
                unitmaster.unit_area,
                unitmaster.unit_open_area,
                unitmaster.effective_date,
                CAST(unitmaster.is_allotted AS UNSIGNED) AS is_allotted,
                CAST(unitmaster.is_occupied AS UNSIGNED) AS is_occupied,
                unitmaster.occupancy_type,
                unitmaster.occupied_by,
                unitmaster.charge_type,
                unitmaster.extra_attributes,
                unitmaster.income_late_charge_id,
                unitmaster.vpa,
                unitmaster.unit_total_water_inlets,
                CAST(unitmaster.chargeable AS UNSIGNED) AS chargeable,  -- Converted to number
                unitmaster.status,
                CONCAT(buildings.soc_building_name, '-', unitmaster.soc_building_floor, '-', unitmaster.unit_flat_number) AS unit_label"

            )
            ->where('unitmaster.soc_id', $societyId)
            ->where('unitmaster.unit_id', $id)
            ->leftJoin('chsone_societies_building_master as buildings', 'buildings.soc_building_id', '=', 'unitmaster.soc_building_id')
            ->distinct('unitmaster.unit_type');

        $result = $obj->first();
        
        //take this key from result soc_building_floor and if they having value 10 so create array of 1 to 10
        $result->floor_array = range(0, $result->soc_building_floor);
    
        // $this->data = $this->format($result->toArray());
        $this->data = $result;
    }
}
