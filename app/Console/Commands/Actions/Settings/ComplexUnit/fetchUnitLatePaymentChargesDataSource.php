<?php

namespace App\Console\Commands\Actions\Settings\ComplexUnit;

use Illuminate\Console\Command;
use App\Console\Commands\Action;
use App\Models\Tenants\IncomeLatePaymentCharge;

class fetchUnitLatePaymentChargesDataSource extends Action
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'datasource:fetchUnitLatePaymentCharges {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Fetch late payment charges list for edit unit details';

    /**
     * Execute the console command.
     */
    public function apply()
    {
        $soc_id = $this->input['company_id'];
        $currentDate = date('Y-m-d');

        // Fetch the last late payment charge with effective_date <= current date
        $arrLastLatePayment = IncomeLatePaymentCharge::where('type', 'maintenance')
        ->where('soc_id', $soc_id)
        ->where('effective_date', '<=', $currentDate)
        ->orderBy('effective_date', 'desc')
        ->first();

        // Fetch the next late payment charges with effective_date >= current date
        $arrNextLatePayment = IncomeLatePaymentCharge::where('type', 'maintenance')
        ->where('soc_id', $soc_id)
        ->where('effective_date', '>=', $currentDate)
        ->orderBy('effective_date', 'desc')
        ->get();

        $selectLateChargeArr = [];
        $arrDt = [];

        // Convert the last late payment charge to an array if it exists
        if (!empty($arrLastLatePayment)) {
            $arrDt[0] = $arrLastLatePayment->toArray();
        }

        // Convert the next late payment charges to an array if they exist
        if (!empty($arrNextLatePayment)) {
            foreach ($arrNextLatePayment as $val) {
                $arrDt[] = $val->toArray();
            }
        }

        // Build the selectLateChargeArr array
        if (!empty($arrDt)) {
            $interestTypes = [
                'simple_interest' => 'Simple Interest',
                'Monthly' => 'Compound Monthly',
                'Quarterly' => 'Compound Quarterly',
                'Half_yearly' => 'Compound Half Yearly',
                'Yearly' => 'Compound Yearly'
            ];

            $data = [];

            foreach ($arrDt as $value) {
                $lateChargeStr = $value['simple_interest'];

                // Append percentage or fixed type
                if ($value['interest_amount_type'] === 'percentage') {
                    $lateChargeStr .= ' %';
                }

                // Append interest type if valid
                if (!empty($value['interest_type']) && isset($interestTypes[$value['interest_type']])) {
                    $lateChargeStr .= ' - ' . $interestTypes[$value['interest_type']];
                }

                $lateChargeStr .= ' - ';

                // Add calculation basis
                if ($value['calculate_from'] === 'duedate') {
                    $dueBill = $lateChargeStr . 'Due Date';
                } else {
                    $dueBill = $lateChargeStr . 'Bill Date';
                }

                // Add to result array
                $selectLateChargeArr['id'] = $value['id'];
                $selectLateChargeArr['late_charge'] = $dueBill;

                $data[] = $selectLateChargeArr;
            }
        }

        $this->data = $data;
    }
}
