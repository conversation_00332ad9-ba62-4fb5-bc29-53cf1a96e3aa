<?php

namespace App\Console\Commands\Actions\Settings\GeneralSettings;

use App\Console\Commands\Action;
use App\Models\Tenants\{
    IncomeInvoiceGeneralSetting,
    IncomeInvoiceSetting,
};

class AddGeneralSettingsDatasource extends Action
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'action:addGeneralSettings {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Read General Settings';

    /**
     * Execute the console command.
     */


    public function apply(){
        $company_id = $this->input['company_id'];
        $INCOME_PAYMENT_MODE = implode(',', $this->input['INCOME_PAYMENT_MODE']);

            $this->tenantDB()->table("income_invoice_settings")
        ->where('soc_id', $company_id)
        ->update([
            "effective_date"=> $this->input['effective_date'],
            "invoicing_frequency"=> $this->input['invoicing_frequency'],
        ]);

        $this->input['INCOME_PAYMENT_MODE'] = $INCOME_PAYMENT_MODE;
            $data = (array) $this->input;

            // Step 1: Fetch existing keys from the table
            $existingKeys = $this->tenantDB()
                ->table('income_invoice_general_settings')
                ->where('soc_id', $company_id)
                ->pluck('setting_key')
                ->toArray();


            // Step 2: Filter the input data to include only valid keys
            $validData = array_filter($data, function ($key) use ($existingKeys) {
                return in_array($key, $existingKeys);
            }, ARRAY_FILTER_USE_KEY);

            // Step 3: Construct the SQL query
            $caseStatements = [];

            foreach ($validData as $key => $value) {
                $caseStatements[] = "WHEN setting_key = '{$key}' THEN '{$value}'";
            }

            $sql = "
                UPDATE income_invoice_general_settings
                SET setting_value = CASE
                    " . implode(' ', $caseStatements) . "
                    ELSE setting_value
                END
                WHERE soc_id = ? AND setting_key IN (" . implode(', ', array_map(function ($key) {
                    return "'{$key}'";
                }, array_keys($validData))) . ")
            ";

            // Execute the raw query with the company_id as parameter
            $this->tenantDB()->statement($sql, [$company_id]);
            $this->message = "General Settings updated successfully";

    }

}
