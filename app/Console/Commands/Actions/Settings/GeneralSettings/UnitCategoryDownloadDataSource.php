<?php

namespace App\Console\Commands\Actions\Settings\GeneralSettings;

use App\Console\Commands\Action;
use Illuminate\Support\Facades\DB;

class UnitCategoryDownloadDataSource extends Action
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'datasource:downloadSocUnits {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Get list of Complex Unit List';

    protected $formatter = [
        "id" => "",
        "soc_units_type_id" => "",
        "soc_id" => "",
        "type" => "",
        "soc_unit_type" => "",
        "quantity" => "",
        "quantity_unalloted" => "",
        "area_sq_ft" => "",
        "open_space_area_sq_ft" => "",
        "water_inlets_num" => "",
        "status" => "",
        "use_category" => "",
        "chargeable" => ""
    ];

    protected $formatterByKeys = ['id'];

    protected $mapper = [
        'id' => 'soc_units_type_id',
    ];

    /**
     * Execute the console command.
     */
    public function apply()
    {
        $societyId = $this->input['company_id'];
        $type = $this->input['type'] ?? '';

        $obj = $this->tenantDB()->table("chsone_society_units_tpl")
                    ->select(
                        "soc_units_type_id as id",
                        "soc_units_type_id",
                        "soc_id",
                        "type",
                        "soc_unit_type",
                        "quantity",
                        "quantity_unalloted",
                        DB::raw('CAST(area_sq_ft AS DECIMAL(10,2)) as area_sq_ft'),
                        DB::raw('CAST(open_space_area_sq_ft AS DECIMAL(10,2)) as open_space_area_sq_ft'),
                        "water_inlets_num",
                        "status",
                        "use_category",
                        DB::raw('CASE WHEN chargeable = 1 THEN "Yes" ELSE "No" END as chargeable')
                        )
                    ->where('soc_id', $societyId)
                    ->orderBy('type');


        $result = $obj->get();

        $this->data = $result;

    }
}
