<?php

namespace App\Console\Commands\Actions\Settings\GeneralSettings;

use Illuminate\Console\Command;
use App\Console\Commands\Action;

class emailTemplateDisableDataSource extends Action
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'datasource:emailTemplateDisable {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Disable Email Template';

    /**
     * Execute the console command.
     */
    public function apply()
    {
        $id = $this->input['id'];
        $status = $this->input['status'];
        $obj = $this->tenantDB()->table('chsone_email_template')->where('id', $id)->update(['is_active' => $status]);

        if($obj) {
            $this->status = 'success';
            $this->message = 'Email template is Successfully Updated';
            $this->statusCode = 200;
        } else {
            $this->status = 'error';
            $this->message = 'Email template is Failed to Update ';
            $this->statusCode = 400;
        }
    }
}
