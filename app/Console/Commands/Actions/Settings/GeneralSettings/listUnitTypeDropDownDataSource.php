<?php

namespace App\Console\Commands\Actions\Settings\GeneralSettings;

use App\Console\Commands\Action;
use Illuminate\Support\Facades\DB;

class listUnitTypeDropDownDataSource extends Action
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'datasource:listUnitTypeDropDown {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'List Unit Type Drop Down Data Source';

    protected $hugeData = true;

    /**
     * Execute the console command.
     */
    public function apply()
    {
        $societyId = $this->input['company_id'];
        $type = $this->input['type'] ?? '';

        $result = array();

        // fetch the type os society as per comoany_id from chsone_societies_master table
        $societyType = $this->masterDB()->table("chsone_societies_master")
            ->select("soc_type_id as type")
            ->where("soc_id", $societyId)
            ->first();

        // now according to the society type we will create a result array using switch case
        switch ($societyType->type) {
            case 'residential':
                $result = [
                    ["id" => "flat", "soc_unit_type" => "Flat"],
                    ["id" => "duplex", "soc_unit_type" => "Duplex"],
                    ["id" => "bedsit", "soc_unit_type" => "Bedsit"],
                    ["id" => "pent_house", "soc_unit_type" => "Pent House"],
                    ["id" => "swimming_pool", "soc_unit_type" => "Swimming pool"],
                    ["id" => "hall", "soc_unit_type" => "Hall"],
                    ["id" => "garden", "soc_unit_type" => "Garden"],
                    ["id" => "clubhouse", "soc_unit_type" => "Clubhouse"],
                    ["id" => "parking", "soc_unit_type" => "Parking"],
                    ["id" => "park", "soc_unit_type" => "Park"],
                ];
                break;
            case 'commercial':
                $result = [
                    ["id" => "flat", "soc_unit_type" => "Flat"],
                    ["id" => "duplex", "soc_unit_type" => "Duplex"],
                    ["id" => "bedsit", "soc_unit_type" => "Bedsit"],
                    ["id" => "pent_house", "soc_unit_type" => "Pent House"],
                    ["id" => "swimming_pool", "soc_unit_type" => "Swimming pool"],
                    ["id" => "hall", "soc_unit_type" => "Hall"],
                    ["id" => "garden", "soc_unit_type" => "Garden"],
                    ["id" => "clubhouse", "soc_unit_type" => "Clubhouse"],
                    ["id" => "shop", "soc_unit_type" => "Shop"],
                    ["id" => "office", "soc_unit_type" => "Office"],
                    ["id" => "parking", "soc_unit_type" => "Parking"],
                    ["id" => "gala", "soc_unit_type" => "Gala"]
                ];
                break;
            case 'IT':
                $result = [
                    ["id" => "shop", "soc_unit_type" => "Shop"],
                    ["id" => "office", "soc_unit_type" => "Office"],
                    ["id" => "parking", "soc_unit_type" => "Parking"],
                    ["id" => "gala", "soc_unit_type" => "Gala"]
                ];
                break;
            case 'resicumcomm':
                $result = [
                    ["id" => "flat", "soc_unit_type" => "Flat"],
                    ["id" => "duplex", "soc_unit_type" => "Duplex"],
                    ["id" => "bedsit", "soc_unit_type" => "Bedsit"],
                    ["id" => "pent_house", "soc_unit_type" => "Pent House"],
                    ["id" => "swimming_pool", "soc_unit_type" => "Swimming pool"],
                    ["id" => "hall", "soc_unit_type" => "Hall"],
                    ["id" => "garden", "soc_unit_type" => "Garden"],
                    ["id" => "clubhouse", "soc_unit_type" => "Clubhouse"],
                    ["id" => "shop", "soc_unit_type" => "Shop"],
                    ["id" => "office", "soc_unit_type" => "Office"],
                    ["id" => "parking", "soc_unit_type" => "Parking"],
                    ["id" => "gala", "soc_unit_type" => "Gala"]
                ];
                break;
            case 'resicumit':
                $result = [
                    ["id" => "flat", "soc_unit_type" => "Flat"],
                    ["id" => "duplex", "soc_unit_type" => "Duplex"],
                    ["id" => "bedsit", "soc_unit_type" => "Bedsit"],
                    ["id" => "pent_house", "soc_unit_type" => "Pent House"],
                    ["id" => "swimming_pool", "soc_unit_type" => "Swimming pool"],
                    ["id" => "hall", "soc_unit_type" => "Hall"],
                    ["id" => "garden", "soc_unit_type" => "Garden"],
                    ["id" => "clubhouse", "soc_unit_type" => "Clubhouse"],
                    ["id" => "shop", "soc_unit_type" => "Shop"],
                    ["id" => "office", "soc_unit_type" => "Office"],
                    ["id" => "parking", "soc_unit_type" => "Parking"],
                    ["id" => "gala", "soc_unit_type" => "Gala"]
                ];
                break;
            case 'commcumit':
                $result = [
                    ["id" => "flat", "soc_unit_type" => "Flat"],
                    ["id" => "duplex", "soc_unit_type" => "Duplex"],
                    ["id" => "bedsit", "soc_unit_type" => "Bedsit"],
                    ["id" => "pent_house", "soc_unit_type" => "Pent House"],
                    ["id" => "swimming_pool", "soc_unit_type" => "Swimming pool"],
                    ["id" => "hall", "soc_unit_type" => "Hall"],
                    ["id" => "garden", "soc_unit_type" => "Garden"],
                    ["id" => "clubhouse", "soc_unit_type" => "Clubhouse"],
                    ["id" => "shop", "soc_unit_type" => "Shop"],
                    ["id" => "office", "soc_unit_type" => "Office"],
                    ["id" => "parking", "soc_unit_type" => "Parking"],
                    ["id" => "gala", "soc_unit_type" => "Gala"]
                ];
                break;
            case 'resi_cum_comm_cum_it':
                $result = [
                    ["id" => "flat", "soc_unit_type" => "Flat"],
                    ["id" => "duplex", "soc_unit_type" => "Duplex"],
                    ["id" => "bedsit", "soc_unit_type" => "Bedsit"],
                    ["id" => "pent_house", "soc_unit_type" => "Pent House"],
                    ["id" => "swimming_pool", "soc_unit_type" => "Swimming pool"],
                    ["id" => "hall", "soc_unit_type" => "Hall"],
                    ["id" => "garden", "soc_unit_type" => "Garden"],
                    ["id" => "clubhouse", "soc_unit_type" => "Clubhouse"],
                    ["id" => "shop", "soc_unit_type" => "Shop"],
                    ["id" => "office", "soc_unit_type" => "Office"],
                    ["id" => "parking", "soc_unit_type" => "Parking"],
                    ["id" => "gala", "soc_unit_type" => "Gala"]
                ];
                break;
            default:
                $result = [];
                break;
        }

        //$this->data = array_merge($result->toArray(), $totalArray);
        $this->data = $result;

    }
}
