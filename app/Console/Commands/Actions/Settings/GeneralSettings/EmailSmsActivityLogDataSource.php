<?php

namespace App\Console\Commands\Actions\Settings\Generalsettings;
use App\Console\Commands\Action;
use App\Http\Traits\MongoTraits;

class EmailSmsActivityLogDataSource extends Action
{
    use MongoTraits;

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'datasource:emailSmsActivityLog {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Email Sms Activity Log Data Source';

    /**
     * Execute the console command.
     */


     protected $hugeData = true;
     public function apply()
    {

        $company_id = $this->input['company_id'];
        $limit = $this->input['per_page'];
        $page = $this->input['page'];
        $fields = $this->input['fields'] ?? [];

        if (!empty($fields)) {
            $fields = json_decode($fields);
        }

    $data = $this->emailSmsLogs( $company_id,$fields,$limit,$page);
    $count = $this->emailSmsLogsCount($company_id);

        $results = $data->map(function ($item) {
            // Ensure $item is an object
            $item = (object) $item;

            $new_object = new \stdClass();

            // Handle _id conversion to id
            $new_object->id = (string) ($item->_id ?? '');

            // Handle created_at conversion
            $created_at = $item->created_at ?? null;
            if ($created_at instanceof \MongoDB\BSON\UTCDateTime) {
                $phpDate = $created_at->toDateTime();
                $phpDate->setTimezone(new \DateTimeZone('Asia/Kolkata'));
                $new_object->date = $phpDate->format('Y-m-d H:i:s');
            }else {
                $new_object->date = null;
            }

            // Handle building_info
            $parameters = $item->parameters ?? [];
            if (isset($parameters['soc_building_name'], $parameters['unit_flat_number'])) {
                $new_object->building_info = $parameters['soc_building_name'] . ' / ' . $parameters['unit_flat_number'];
            } else {
                $new_object->building_info = 'N/A';
            }

            // Handle email_phone based on channel
            if ($item->channel == 'sms') {
                $new_object->email_phone = $item->to ?? '';
                $new_object->content = $item->content ?? '';

            } else {
                $emails = $parameters['email_setup']['email'] ?? [];
                $new_object->email_phone = is_array($emails) ? reset($emails) : '';
                $new_object->content = $item->subject ?? '';
            }

            // Handle other fields
            $new_object->type = $this->convertToReadableString($item->type ?? '');
            $new_object->channel = strtoupper($item->channel ?? ''); // Convert channel to uppercase
            $new_object->status = $item->status ?? '';
            $new_object->name = $item->name ?? '';
            $new_object->building_info = $new_object->building_info ?? '';

            return $new_object;
        });

        $this->data= $results;
        $this->meta['pagination']['total'] = $count;

    }
}
