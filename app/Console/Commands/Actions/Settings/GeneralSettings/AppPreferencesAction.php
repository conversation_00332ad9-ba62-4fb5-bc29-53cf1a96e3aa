<?php

namespace App\Console\Commands\Actions\Settings\GeneralSettings;

use App\Console\Commands\Action;
use App\Models\Tenants\SocietyPreference;

class AppPreferencesAction extends Action
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'action:setAppPreferences {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Set App Preferences';

    /**
     * Execute the console command.
     */
    public function apply()
    {
        $data = $this->input;
        foreach($data as $key => $param) {
            $preferences = new SocietyPreference();
            $preferencesData = $preferences->where("status", '1')
                                           ->where('key', $key)
                                           ->first();

            if (!empty($preferencesData)) {
                if ($key == 'DATABASE_DATE_FROMAT') {
                    $param = 'Y-m-d';
                }
                if ($key == 'DATABASE_TIME_FORMAT') {
                    $param = 'H:i:s';
                }
                $preferencesData->key = $key;
                $preferencesData->value = $param;
                $preferencesData->status = '1';
                $preferencesData->description = str_replace("_", " ", $key);

                if ($preferencesData->save()) {
                    $this->statusCode = "200";
                    $this->status = "success";
                    $this->message = "Preferences updated successfully";
                    $this->data = [];
                } else {
                    $this->statusCode = "400";
                    $this->status = "error";
                    $this->message = "Preferences failed to update";
                    $this->data = [];
                }
            } else {
                $this->statusCode = "400";
                $this->status = "error";
                $this->message = "No preferences details found";
                $this->data = [];
            }
        }
    }
}
