<?php

namespace App\Console\Commands\Actions\Settings\GeneralSettings;

use Illuminate\Support\Facades\DB;
use App\Console\Commands\Action;

class SmstemplateDataSource extends Action
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'datasource:autoresponsesmslist {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Get SMS Template List';

    protected $formatter = [
        'id' => '',
        'variable_id' => '',
        'title' => '',
        'purpose' => '',
        'sms_content' => '',
        'is_approved' => '',
        'is_active' => ''
    ];

    protected $formatterByKeys =  ['sms_template_id'];

    protected $mapper = [
        'id' => 'sms_template_id'
    ];


    /**
     * Execute the console command.
     */
    public function apply()
    {
        $id = $this->input['id'] ?? null;

        $page = $this->input['page'] ?? 1;
        $per_page = !empty($this->input['per_page']) ? $this->input['per_page']: ($this->hugeData ? 100000 : 10);
        $offset = ($page - 1) * $per_page;
        
        $obj = $this->tenantDB()->table('chsone_sms_template')
        ->select(
            'sms_template_id as id',
            'variable_id',
            'title',
            'purpose',
            'sms_content',
            'is_approved',
            DB::raw('CAST(is_active AS UNSIGNED) as is_active')
        );

        if (!empty($id)) {
            $obj = $obj->where('sms_template_id', $id)->first();
            $this->data = $obj;
            return;
        } else {
            $obj = $obj->orderBy('sms_template_id', 'asc')
            ->offset($offset)
            ->limit($per_page)
            ->get();   
        }

        // get total count    
        $totalCount = $this->TenantDB()->table('chsone_sms_template')->count();

        $this->data =$obj;
        $this->meta['pagination']['total'] = $totalCount;
    }
}
