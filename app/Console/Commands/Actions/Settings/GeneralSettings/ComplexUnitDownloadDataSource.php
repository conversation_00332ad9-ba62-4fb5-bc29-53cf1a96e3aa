<?php

namespace App\Console\Commands\Actions\Settings\GeneralSettings;

use App\Console\Commands\Action;

class ComplexUnitDownloadDataSource extends Action
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'datasource:downloadSocUnitList {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Get list of Complex Unit List';

    protected $formatterByKeys = ['id'];

    protected $mapper = [
        "id" => "unitmaster.unit_id",
        'unit_number' => 'unitmaster.unit_flat_number',
        'unit_category' => 'unitmaster.unit_category',
        'building' => 'unitmaster.soc_building_name',
        'soc_building_floor' => 'unitmaster.soc_building_floor'
    ];


    protected $hugeData = true;


    /**
     * Execute the console command.
     */
    public function apply()
    {
        $company_id = $this->input['company_id'] ?? null;

        $obj = $this->tenantDB()->table('chsone_units_master as unitmaster')
            ->selectRaw(
                "unitmaster.unit_id AS id,
                unitmaster.unit_id,
                unitmaster.soc_id,
                unitmaster.unit_category,
                unitmaster.unit_type,
                unitmaster.soc_building_id,
                unitmaster.soc_building_name,
                unitmaster.soc_building_floor,
                CONCAT(unitmaster.soc_building_name, ', Floor: ', unitmaster.soc_building_floor) AS building,
                unitmaster.unit_flat_number,
                CAST(unitmaster.unit_area AS DECIMAL(10,2)) AS unit_area,
                CAST(unitmaster.unit_open_area AS DECIMAL(10,2)) AS unit_open_area,
                unitmaster.effective_date,
                unitmaster.is_allotted,
                unitmaster.is_occupied,
                unitmaster.occupancy_type,
                unitmaster.occupied_by,
                unitmaster.vpa,
                unitmaster.status"
            )
            ->where('unitmaster.soc_id', $company_id)
            ->where('unitmaster.status', 1);


        $result = $obj->get();

        $this->data = $result;


    }
}
