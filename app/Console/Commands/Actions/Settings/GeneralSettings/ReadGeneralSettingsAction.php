<?php

namespace App\Console\Commands\Actions\Settings\GeneralSettings;

use App\Console\Commands\Action;
use App\Models\Tenants\ {
    IncomeInvoiceGeneralSetting,
    IncomeInvoiceSetting,
    ChsoneTaxClass,
    ChsoneTaxCategory
}
;

class ReadGeneralSettingsAction extends Action {
    /**
    * The name and signature of the console command.
    *
    * @var string
    */
    protected $signature = 'action:readGeneralSettings {flowId} {parentId} {input}';

    /**
    * The console command description.
    *
    * @var string
    */
    protected $description = 'Read General Settings';

    /**
    * Execute the console command.
    */

    public function apply() {
        $company_id = $this->input[ 'company_id' ];

        // $this->data = $this->data2( $company_id );
        // return $this->data;
        $incomeInvoiceSettings =  $this->tenantDB()->table( 'income_invoice_settings' )
        ->where( 'soc_id', $company_id )
        ->orderByDesc( 'effective_date' )
        ->first();

        $incomeInvoiceGeneralSettings =   $this->tenantDB()->table( 'income_invoice_general_settings' )
        ->where( 'soc_id', $company_id )
        ->get();

        $invoicing_frequency = $incomeInvoiceSettings->invoicing_frequency;
        $PARKING_CHARGES_CALCULATION_BY = $this->getSettingValueAndDescription( $incomeInvoiceGeneralSettings, 'PARKING_CHARGES_CALCULATION_BY' );
        $PARTICULAR_AMOUNT_ROUNDOFF = $this->getSettingValueAndDescription( $incomeInvoiceGeneralSettings, 'PARTICULAR_AMOUNT_ROUNDOFF' );
        $PARTICULAR_AMOUNT_ROUNDOFF_TYPE = $this->getSettingValueAndDescription( $incomeInvoiceGeneralSettings, 'PARTICULAR_AMOUNT_ROUNDOFF_TYPE' );
        $INVOICE_AMOUNT_ROUNDOFF = $this->getSettingValueAndDescription( $incomeInvoiceGeneralSettings, 'INVOICE_AMOUNT_ROUNDOFF' );
        $INVOICE_AMOUNT_ROUNDOFF_TYPE = $this->getSettingValueAndDescription( $incomeInvoiceGeneralSettings, 'INVOICE_AMOUNT_ROUNDOFF_TYPE' );
        $ALLOWED_PARTIAL_PAYMENT = $this->getSettingValueAndDescription( $incomeInvoiceGeneralSettings, 'ALLOWED_PARTIAL_PAYMENT' );
        $Late_Charges_Calculation_From = $this->getSettingValueAndDescription( $incomeInvoiceGeneralSettings, 'Late_Charges_Calculation_From' );
        $INCOME_RECEIPT_PREFIX = $this->getSettingValueAndDescription( $incomeInvoiceGeneralSettings, 'INCOME_RECEIPT_PREFIX' );
        $INCOME_RECEIPT_START_NUMBER = ( int ) $this->getSettingValueAndDescription( $incomeInvoiceGeneralSettings, 'INCOME_RECEIPT_START_NUMBER' );
        $INCOME_RECEIPT_NUMBER_INCREMENT = ( int )$this->getSettingValueAndDescription( $incomeInvoiceGeneralSettings, 'INCOME_RECEIPT_NUMBER_INCREMENT' );
        $RECEIPT_LOGO = $this->getSettingValueAndDescription( $incomeInvoiceGeneralSettings, 'RECEIPT_LOGO' );
        $SHOW_SOCIETY_SIGNATURE = $this->getSettingValueAndDescription( $incomeInvoiceGeneralSettings, 'SHOW_SOCIETY_SIGNATURE' );
        $INCOME_RECEIPT_INSTRUCTION = $this->getSettingValueAndDescription( $incomeInvoiceGeneralSettings, 'INCOME_RECEIPT_INSTRUCTION' );
        $INCOME_PAYMENT_MODE = $this->getSettingValueAndDescription( $incomeInvoiceGeneralSettings, 'INCOME_PAYMENT_MODE' );
        $INCOME_NONMEMBER_INVOICE_PREFIX = $this->getSettingValueAndDescription( $incomeInvoiceGeneralSettings, 'INCOME_NONMEMBER_INVOICE_PREFIX' );
        $INCOME_NONMEMBER_INVOICE_START_NUMBER = ( int )$this->getSettingValueAndDescription( $incomeInvoiceGeneralSettings, 'INCOME_NONMEMBER_INVOICE_START_NUMBER' );
        $INCOME_NONMEMBER_INVOICE_NUMBER_INCREMENT = ( int ) $this->getSettingValueAndDescription( $incomeInvoiceGeneralSettings, 'INCOME_NONMEMBER_INVOICE_NUMBER_INCREMENT' );
        $INCOME_INVOICE_PREFIX = $this->getSettingValueAndDescription( $incomeInvoiceGeneralSettings, 'INCOME_INVOICE_PREFIX' );
        $INCOME_INVOICE_START_NUMBER = ( int )$this->getSettingValueAndDescription( $incomeInvoiceGeneralSettings, 'INCOME_INVOICE_START_NUMBER' );
        $INCOME_INVOICE_NUMBER_INCREMENT = ( int ) $this->getSettingValueAndDescription( $incomeInvoiceGeneralSettings, 'INCOME_INVOICE_NUMBER_INCREMENT' );
        $INCOME_INCIDENTAL_INVOICE_PREFIX = $this->getSettingValueAndDescription( $incomeInvoiceGeneralSettings, 'INCOME_INCIDENTAL_INVOICE_PREFIX' );
        $INCOME_INCIDENTAL_INVOICE_START_NUMBER = ( int ) $this->getSettingValueAndDescription( $incomeInvoiceGeneralSettings, 'INCOME_INCIDENTAL_INVOICE_START_NUMBER' );
        $INCOME_INCIDENTAL_INVOICE_NUMBER_INCREMENT = ( int ) $this->getSettingValueAndDescription( $incomeInvoiceGeneralSettings, 'INCOME_INCIDENTAL_INVOICE_NUMBER_INCREMENT' );
        $INCOME_NONMEMBER_NAME_LABEL = $this->getSettingValueAndDescription( $incomeInvoiceGeneralSettings, 'INCOME_NONMEMBER_NAME_LABEL' );
        $INCOME_NONMEMBER_HIDE_GSTUIN = ( int ) $this->getSettingValueAndDescription( $incomeInvoiceGeneralSettings, 'INCOME_NONMEMBER_HIDE_GSTUIN' );
        $INCOME_NONMEMBER_GSTUIN_LABEL = $this->getSettingValueAndDescription( $incomeInvoiceGeneralSettings, 'INCOME_NONMEMBER_GSTUIN_LABEL' );
        $INCOME_NONMEMBER_HIDE_HSNSAC = ( int ) $this->getSettingValueAndDescription( $incomeInvoiceGeneralSettings, 'INCOME_NONMEMBER_HIDE_HSNSAC' );
        $INCOME_NONMEMBER_HSNSAC_LABEL = $this->getSettingValueAndDescription( $incomeInvoiceGeneralSettings, 'INCOME_NONMEMBER_HSNSAC_LABEL' );
        $INCOME_NONMEMBER_HIDE_EMAIL = ( int ) $this->getSettingValueAndDescription( $incomeInvoiceGeneralSettings, 'INCOME_NONMEMBER_HIDE_EMAIL' );
        $INCOME_NONMEMBER_EMAIL_LABEL = $this->getSettingValueAndDescription( $incomeInvoiceGeneralSettings, 'INCOME_NONMEMBER_EMAIL_LABEL' );
        $INCOME_NONMEMBER_HIDE_ADDRESS = ( int ) $this->getSettingValueAndDescription( $incomeInvoiceGeneralSettings, 'INCOME_NONMEMBER_HIDE_ADDRESS' );
        $INCOME_NONMEMBER_ADDRESS_LABEL = $this->getSettingValueAndDescription( $incomeInvoiceGeneralSettings, 'INCOME_NONMEMBER_ADDRESS_LABEL' );
        $INCOME_NONMEMBER_HIDE_CONTACTNO = ( int ) $this->getSettingValueAndDescription( $incomeInvoiceGeneralSettings, 'INCOME_NONMEMBER_HIDE_CONTACTNO' );
        $INCOME_NONMEMBER_CONTACTNO_LABEL = $this->getSettingValueAndDescription( $incomeInvoiceGeneralSettings, 'INCOME_NONMEMBER_CONTACTNO_LABEL' );
        $INCOME_NONMEMBER_SIGNATURE_LABEL =  $this->getSettingValueAndDescription( $incomeInvoiceGeneralSettings, 'INCOME_NONMEMBER_SIGNATURE_LABEL' );
        $INCOME_MAINTENANCE_NAME_LABEL = $this->getSettingValueAndDescription( $incomeInvoiceGeneralSettings, 'INCOME_MAINTENANCE_NAME_LABEL' );
        $INCOME_MAINTENANCE_UNIT_NUMBER_LABEL =  $this->getSettingValueAndDescription( $incomeInvoiceGeneralSettings, 'INCOME_MAINTENANCE_UNIT_NUMBER_LABEL' );
        $INCOME_MAINTENANCE_HIDE_PARKING_UNIT = ( int ) $this->getSettingValueAndDescription( $incomeInvoiceGeneralSettings, 'INCOME_MAINTENANCE_HIDE_PARKING_UNIT' );
        $INCOME_MAINTENANCE_PARKING_UNIT_LABEL = $this->getSettingValueAndDescription( $incomeInvoiceGeneralSettings, 'INCOME_MAINTENANCE_PARKING_UNIT_LABEL' );
        $INCOME_MAINTENANCE_HIDE_BUILTUP_AREA = ( int ) $this->getSettingValueAndDescription( $incomeInvoiceGeneralSettings, 'INCOME_MAINTENANCE_HIDE_BUILTUP_AREA' );
        $INCOME_MAINTENANCE_SHOW_INCIDENTAL_RECEIPT = ( int ) $this->getSettingValueAndDescription( $incomeInvoiceGeneralSettings, 'INCOME_MAINTENANCE_SHOW_INCIDENTAL_RECEIPT' );
        $INCOME_MAINTENANCE_BUILTUP_AREA_LABEL = $this->getSettingValueAndDescription( $incomeInvoiceGeneralSettings, 'INCOME_MAINTENANCE_BUILTUP_AREA_LABEL' );
        $INCOME_MAINTENANCE_HIDE_GSTUIN = ( int ) $this->getSettingValueAndDescription( $incomeInvoiceGeneralSettings, 'INCOME_MAINTENANCE_HIDE_GSTUIN' );
        $INCOME_MAINTENANCE_GSTUIN_LABEL = $this->getSettingValueAndDescription( $incomeInvoiceGeneralSettings, 'INCOME_MAINTENANCE_GSTUIN_LABEL' );
        $INCOME_MAINTENANCE_SIGNATURE_LABEL = $this->getSettingValueAndDescription( $incomeInvoiceGeneralSettings, 'INCOME_MAINTENANCE_SIGNATURE_LABEL' );
        $INCOME_INCIDENTAL_NAME_LABEL = $this->getSettingValueAndDescription( $incomeInvoiceGeneralSettings, 'INCOME_INCIDENTAL_NAME_LABEL' );
        $INCOME_INCIDENTAL_UNIT_NUMBER_LABEL = $this->getSettingValueAndDescription( $incomeInvoiceGeneralSettings, 'INCOME_INCIDENTAL_UNIT_NUMBER_LABEL' );
        $INCOME_INCIDENTAL_HIDE_GSTUIN = ( int ) $this->getSettingValueAndDescription( $incomeInvoiceGeneralSettings, 'INCOME_INCIDENTAL_HIDE_GSTUIN' );
        $INCOME_INCIDENTAL_GSTUIN_LABEL = $this->getSettingValueAndDescription( $incomeInvoiceGeneralSettings, 'INCOME_MAINTENANCE_GSTUIN_LABEL' );
        $INCOME_INCIDENTAL_SIGNATURE_LABEL = $this->getSettingValueAndDescription( $incomeInvoiceGeneralSettings, 'INCOME_MAINTENANCE_SIGNATURE_LABEL' );
        $INVOICE_LOGO = $this->getSettingValueAndDescription( $incomeInvoiceGeneralSettings, 'INVOICE_LOGO' );
        $PAYMENT_TRANSACTION_RECEIPT_LAST_PERIOD = $this->getSettingValueAndDescription( $incomeInvoiceGeneralSettings, 'PAYMENT_TRANSACTION_RECEIPT_LAST_PERIOD' );
        $SHOW_RECEIPT_FOOTER = $this->getSettingValueAndDescription( $incomeInvoiceGeneralSettings, 'SHOW_RECEIPT_FOOTER' );
        $INVOICE_DPC = $this->getSettingValueAndDescription( $incomeInvoiceGeneralSettings, 'INVOICE_DPC' );
        $SHOW_RULE_CHARGES = $this->getSettingValueAndDescription( $incomeInvoiceGeneralSettings, 'SHOW_RULE_CHARGES' );
        $SHOW_BANK_DETAIL = $this->getSettingValueAndDescription( $incomeInvoiceGeneralSettings, 'SHOW_BANK_DETAIL' );
        $SHOW_NEW_INVOICE_TEMPLATE = $this->getSettingValueAndDescription( $incomeInvoiceGeneralSettings, 'SHOW_NEW_INVOICE_TEMPLATE' );
        $SHOW_PAY_URL = $this->getSettingValueAndDescription( $incomeInvoiceGeneralSettings, 'SHOW_PAY_URL' );
        $INVOICE_FONT_SIZE = ( int ) $this->getSettingValueAndDescription( $incomeInvoiceGeneralSettings, 'INVOICE_FONT_SIZE' );
        $INCOME_PUBLISH_RESIDENT_DUES = ( int ) $this->getSettingValueAndDescription( $incomeInvoiceGeneralSettings, 'INCOME_PUBLISH_RESIDENT_DUES' );
        $INCOME_SHOW_INTEREST_BREAKUP = ( int ) $this->getSettingValueAndDescription( $incomeInvoiceGeneralSettings, 'INCOME_SHOW_INTEREST_BREAKUP' );
        $INCOME_PAYMENT_INSTRUCTION = $this->getSettingValueAndDescription( $incomeInvoiceGeneralSettings, 'INCOME_PAYMENT_INSTRUCTION' );
        $COMMON_INCOME_PAYMENT_INSTRUCTION = $this->getSettingValueAndDescription( $incomeInvoiceGeneralSettings, 'COMMON_INCOME_PAYMENT_INSTRUCTION' );
        $NON_MEMBER_INCOME_PAYMENT_INSTRUCTION =  $this->getSettingValueAndDescription( $incomeInvoiceGeneralSettings, 'NON_MEMBER_INCOME_PAYMENT_INSTRUCTION' );

        $INCOME_PAYMENT_MODE = explode(',', $INCOME_PAYMENT_MODE);
        $INCOME_PUBLISH_RESIDENT_DUES = $INCOME_PUBLISH_RESIDENT_DUES ? true :false;
        $INCOME_SHOW_INTEREST_BREAKUP   = $INCOME_SHOW_INTEREST_BREAKUP ? true :false;
        $created_by =        $this->getUserData( $incomeInvoiceGeneralSettings[ 0 ]->created_by );
        $created_date =      $incomeInvoiceGeneralSettings[ 0 ]->created_date;
        $updated_by =        $this->getUserData( $incomeInvoiceGeneralSettings[ 0 ]->updated_by );
        $updated_date =      $incomeInvoiceGeneralSettings[ 0 ]->updated_date;
        $data = [
            'invoice_generation' => [
                'invoicing_frequency' => $invoicing_frequency,
                'effective_date'    => $incomeInvoiceSettings->effective_date,
                'PARKING_CHARGES_CALCULATION_BY'=> $PARKING_CHARGES_CALCULATION_BY,
                'PARTICULAR_AMOUNT_ROUNDOFF'=> $PARTICULAR_AMOUNT_ROUNDOFF,
                'PARTICULAR_AMOUNT_ROUNDOFF_TYPE'=> $PARTICULAR_AMOUNT_ROUNDOFF_TYPE,
                'INVOICE_AMOUNT_ROUNDOFF'=> $INVOICE_AMOUNT_ROUNDOFF,
                'INVOICE_AMOUNT_ROUNDOFF_TYPE'=> $INVOICE_AMOUNT_ROUNDOFF_TYPE,
                'ALLOWED_PARTIAL_PAYMENT'=> $ALLOWED_PARTIAL_PAYMENT,
                'Late_Charges_Calculation_From'=> $Late_Charges_Calculation_From,
                'INCOME_RECEIPT_PREFIX'=> $INCOME_RECEIPT_PREFIX,
                'INCOME_RECEIPT_START_NUMBER'=> $INCOME_RECEIPT_START_NUMBER,
                'INCOME_RECEIPT_NUMBER_INCREMENT'=> $INCOME_RECEIPT_NUMBER_INCREMENT,
                'RECEIPT_LOGO'=> $RECEIPT_LOGO,
                'SHOW_SOCIETY_SIGNATURE'=> $SHOW_SOCIETY_SIGNATURE,
                'INCOME_RECEIPT_INSTRUCTION'=> $INCOME_RECEIPT_INSTRUCTION,
                'INCOME_PAYMENT_MODE'=> $INCOME_PAYMENT_MODE,
                'INCOME_NONMEMBER_INVOICE_PREFIX'=> $INCOME_NONMEMBER_INVOICE_PREFIX,
                'INCOME_NONMEMBER_INVOICE_START_NUMBER'=> $INCOME_NONMEMBER_INVOICE_START_NUMBER,
                'INCOME_NONMEMBER_INVOICE_NUMBER_INCREMENT'=> $INCOME_NONMEMBER_INVOICE_NUMBER_INCREMENT,
                'INCOME_INVOICE_PREFIX'=> $INCOME_INVOICE_PREFIX,
                'INCOME_INVOICE_START_NUMBER'=> $INCOME_INVOICE_START_NUMBER,
                'INCOME_INVOICE_NUMBER_INCREMENT'=> $INCOME_INVOICE_NUMBER_INCREMENT,
                'INCOME_INCIDENTAL_INVOICE_PREFIX'=> $INCOME_INCIDENTAL_INVOICE_PREFIX,
                'INCOME_INCIDENTAL_INVOICE_START_NUMBER'=> $INCOME_INCIDENTAL_INVOICE_START_NUMBER,
                'INCOME_INCIDENTAL_INVOICE_NUMBER_INCREMENT'=> $INCOME_INCIDENTAL_INVOICE_NUMBER_INCREMENT,
                'INCOME_NONMEMBER_NAME_LABEL'=> $INCOME_NONMEMBER_NAME_LABEL,
                'INCOME_NONMEMBER_HIDE_GSTUIN'=> $INCOME_NONMEMBER_HIDE_GSTUIN,
                'INCOME_NONMEMBER_GSTUIN_LABEL'=> $INCOME_NONMEMBER_GSTUIN_LABEL,
                'INCOME_NONMEMBER_HIDE_HSNSAC'=> $INCOME_NONMEMBER_HIDE_HSNSAC,
                'INCOME_NONMEMBER_HSNSAC_LABEL'=> $INCOME_NONMEMBER_HSNSAC_LABEL,
                'INCOME_NONMEMBER_HIDE_EMAIL'=> $INCOME_NONMEMBER_HIDE_EMAIL,
                'INCOME_NONMEMBER_EMAIL_LABEL'=> $INCOME_NONMEMBER_EMAIL_LABEL,
                'INCOME_NONMEMBER_HIDE_ADDRESS'=> $INCOME_NONMEMBER_HIDE_ADDRESS,
                'INCOME_NONMEMBER_ADDRESS_LABEL'=> $INCOME_NONMEMBER_ADDRESS_LABEL,
                'INCOME_NONMEMBER_HIDE_CONTACTNO'=> $INCOME_NONMEMBER_HIDE_CONTACTNO,
                'INCOME_NONMEMBER_CONTACTNO_LABEL'=> $INCOME_NONMEMBER_CONTACTNO_LABEL,
                'INCOME_NONMEMBER_SIGNATURE_LABEL'=> $INCOME_NONMEMBER_SIGNATURE_LABEL,
                'INCOME_MAINTENANCE_NAME_LABEL'=> $INCOME_MAINTENANCE_NAME_LABEL,
                'INCOME_MAINTENANCE_UNIT_NUMBER_LABEL'=> $INCOME_MAINTENANCE_UNIT_NUMBER_LABEL,
                'INCOME_MAINTENANCE_HIDE_PARKING_UNIT'=> $INCOME_MAINTENANCE_HIDE_PARKING_UNIT,
                'INCOME_MAINTENANCE_PARKING_UNIT_LABEL'=> $INCOME_MAINTENANCE_PARKING_UNIT_LABEL,
                'INCOME_MAINTENANCE_HIDE_BUILTUP_AREA'=> $INCOME_MAINTENANCE_HIDE_BUILTUP_AREA,
                'INCOME_MAINTENANCE_SHOW_INCIDENTAL_RECEIPT'=> $INCOME_MAINTENANCE_SHOW_INCIDENTAL_RECEIPT,
                'INCOME_MAINTENANCE_BUILTUP_AREA_LABEL'=> $INCOME_MAINTENANCE_BUILTUP_AREA_LABEL,
                'INCOME_MAINTENANCE_HIDE_GSTUIN'=> $INCOME_MAINTENANCE_HIDE_GSTUIN,
                'INCOME_MAINTENANCE_GSTUIN_LABEL'=> $INCOME_MAINTENANCE_GSTUIN_LABEL,
                'INCOME_MAINTENANCE_SIGNATURE_LABEL'=> $INCOME_MAINTENANCE_SIGNATURE_LABEL,
                'INCOME_INCIDENTAL_NAME_LABEL'=> $INCOME_INCIDENTAL_NAME_LABEL,
                'INCOME_INCIDENTAL_UNIT_NUMBER_LABEL'=> $INCOME_INCIDENTAL_UNIT_NUMBER_LABEL,
                'INCOME_INCIDENTAL_HIDE_GSTUIN'=> $INCOME_INCIDENTAL_HIDE_GSTUIN,
                'INCOME_INCIDENTAL_GSTUIN_LABEL'=> $INCOME_INCIDENTAL_GSTUIN_LABEL,
                'INCOME_INCIDENTAL_SIGNATURE_LABEL'=> $INCOME_INCIDENTAL_SIGNATURE_LABEL,
                'INVOICE_LOGO'=> $INVOICE_LOGO,
                'PAYMENT_TRANSACTION_RECEIPT_LAST_PERIOD'=> $PAYMENT_TRANSACTION_RECEIPT_LAST_PERIOD,
                'SHOW_RECEIPT_FOOTER'=> $SHOW_RECEIPT_FOOTER,
                'INVOICE_DPC'=> $INVOICE_DPC,
                'SHOW_RULE_CHARGES'=> $SHOW_RULE_CHARGES,
                'SHOW_BANK_DETAIL'=> $SHOW_BANK_DETAIL,
                'SHOW_NEW_INVOICE_TEMPLATE'=> $SHOW_NEW_INVOICE_TEMPLATE,
                'SHOW_PAY_URL'=> $SHOW_PAY_URL,
                'INVOICE_FONT_SIZE'=> $INVOICE_FONT_SIZE,
                'INCOME_PUBLISH_RESIDENT_DUES'=> $INCOME_PUBLISH_RESIDENT_DUES,
                'INCOME_SHOW_INTEREST_BREAKUP'=> $INCOME_SHOW_INTEREST_BREAKUP,
                'INCOME_PAYMENT_INSTRUCTION'=> $INCOME_PAYMENT_INSTRUCTION,
                'COMMON_INCOME_PAYMENT_INSTRUCTION'=> $COMMON_INCOME_PAYMENT_INSTRUCTION,
                'NON_MEMBER_INCOME_PAYMENT_INSTRUCTION'=> $NON_MEMBER_INCOME_PAYMENT_INSTRUCTION,
                'created_by'=> $created_by,
                'created_date'=> $created_date,
                'updated_by'=> $updated_by,
                'updated_date'=> $updated_date
            ]
        ];
        $this->data = $data[ 'invoice_generation' ];
    }

    public function getUserData ( $id ) {

        $obj = $this->MasterDB()->table( 'chsone_users_master as user' )
        ->select( 'user.user_id as id', 'user.user_name', 'user.user_first_name', 'user.user_last_name', 'user.user_email_id', 'user.user_mobile_number', 'user.user_type', 'user.user_source', 'user.user_lang_iso_639_3', 'user.user_gmt_time_zone', 'user.added_on', 'user.modified_on', 'user.role', 'user.status' )
        ->where( 'user.user_id', $id )
        ->first();

        if ( $id && $obj ) {
            return  $obj->user_first_name. ' '.$obj->user_last_name;
        } else {
            return '-';
        }

    }

    public function data2( $socId ) {
        $dataArray = [];
        $settings = $this->getGeneralSettings( $socId );
        $taxClass = $this->getTaxDetails( $socId );
        array_push( $dataArray, [ 'generalSettings' => $settings ] );
        array_push( $dataArray, [ 'applicableTaxes' => $taxClass ] );
        array_push( $dataArray, [ 'billPaymentMode' => [
            'cash' => 'Cash',
            'cheque' => 'Cheque',
            'cashtransfer' => 'Electronic Fund Transfer',
            'online' => 'Online'
        ] ] );
        array_push( $dataArray, [ 'onlinePaymentMode' => [
            'payu' => 'Payu',
            'CCAvenue' => 'CCAvenue',
            'EBS' => 'EBS',
            'Billdesk' => 'Billdesk',
            'CHSONE' => 'CHSONE'
        ] ] );
        array_push( $dataArray, [ 'invoiceFrequency' => [
            'Monthly' => 'Monthly',
            'Quarterly' => 'Quarterly',
            'Half_Yearly' => 'Half Yearly',
            'Yearly' => 'Yearly'
        ] ] );
        array_push( $dataArray, [ 'parkingChargesBy' => [
            'parking' => 'No. of Parking Alloted',
            'vehicle' => 'No. of Vehicle Registered',
            'both' => 'Max(Whichever is higher No. of Parking Alloted Or No. of Vehicle Registered)'
        ] ] );
        array_push( $dataArray, [ 'fontSize' => [
            'small' => 'Small',
            'medium' => 'Medium',
            'large' => 'Large'
        ] ] );
        $message = 'General Settings fetched successfully';
        return $dataArray;
        $socId = $this->input[ 'company_id' ];
        if ( $this->input[ 'method' ] == 'get' ) {
            $settings = $this->getGeneralSettings( $socId );
            $taxClass = $this->getTaxDetails( $socId );
            array_push( $dataArray, [ 'generalSettings' => $settings ] );
            array_push( $dataArray, [ 'applicableTaxes' => $taxClass ] );
            array_push( $dataArray, [ 'billPaymentMode' => [
                'cash' => 'Cash',
                'cheque' => 'Cheque',
                'cashtransfer' => 'Electronic Fund Transfer',
                'online' => 'Online'
            ] ] );
            array_push( $dataArray, [ 'onlinePaymentMode' => [
                'payu' => 'Payu',
                'CCAvenue' => 'CCAvenue',
                'EBS' => 'EBS',
                'Billdesk' => 'Billdesk',
                'CHSONE' => 'CHSONE'
            ] ] );
            array_push( $dataArray, [ 'invoiceFrequency' => [
                'Monthly' => 'Monthly',
                'Quarterly' => 'Quarterly',
                'Half_Yearly' => 'Half Yearly',
                'Yearly' => 'Yearly'
            ] ] );
            array_push( $dataArray, [ 'parkingChargesBy' => [
                'parking' => 'No. of Parking Alloted',
                'vehicle' => 'No. of Vehicle Registered',
                'both' => 'Max(Whichever is higher No. of Parking Alloted Or No. of Vehicle Registered)'
            ] ] );
            array_push( $dataArray, [ 'fontSize' => [
                'small' => 'Small',
                'medium' => 'Medium',
                'large' => 'Large'
            ] ] );
            $message = 'General Settings fetched successfully';
            return $this->responseSuccess( $message, $dataArray );
        } else {
            $invoiceSettings = new IncomeInvoiceSetting();
            $id = $this->input[ 'general_setting_id' ] ?? '';
            $currentDate = date( 'Y-m-d' );
            if ( $id != '' ) {
                $invoiceSettings = $invoiceSettings->where( 'id', $id )
                ->first();
                if ( strtotime( $this->input[ 'effective_date' ] ) < strtotime( $currentDate ) ) {
                    $invoiceSettings->id = $id;
                }
            }
            $invoiceSettings->invoicing_frequency = ucwords( $this->input[ 'invoice_frequency' ] );
            $invoiceSettings->effective_date = $this->getDatabaseDate( $this->input[ 'effective_date' ] );
            $invoiceSettings->soc_id = $this->input[ 'company_id' ];
            $invoiceSettings->updated_date = date( 'Y-m-d H:i:s' );

            if ( !$invoiceSettings->save() ) {
                $message = 'General settings unable to save';
                return $this->responseError( $message );
            }

            foreach ( $this->input[ 'generalSettings' ] as $key => $value ) {
                $generalSettings = IncomeInvoiceGeneralSetting::where( 'setting_key', $key )->first();
                if ( isset( $key ) ) {
                    if ( empty( $generalSettings ) ) {
                        $generalSettings->setting_key = $key;
                    }
                } else {
                    $generalSettings = new IncomeInvoiceGeneralSetting();
                    $generalSettings->setting_key = $key;
                }
                if ( is_array( $value ) ) {
                    $value = implode( ',', $value );
                }
                $generalSettings->setting_value = $value;
                $generalSettings->soc_id = $this->input[ 'company_id' ];
                if ( !$generalSettings->save() ) {
                    $message = 'Unable to save settings';
                    return $this->responseError( $message );
                }
            }
            $message = 'General Settings added successfully';
            return $this->responseSuccess( $message );
        }
    }

    function getSettingValueAndDescription( $array, $key ) {
        foreach ( $array as $item ) {
            if ( $item->setting_key == $key ) {
                return $item->setting_value;
            }
        }
        // Return null or an appropriate response if the key is not found
        return null;
    }

    public function responseError( $message ) {
        $this->statusCode = '400';
        $this->status = 'error';
        $this->message = $message;
        $this->data = [];
        return false;
    }

    public function responseSuccess( $message, $data = [] ) {
        $this->statusCode = '200';
        $this->status = 'success';
        $this->message = $message;
        $this->data = $data;
        return true;
    }

    public function getGeneralSettings( $socId ) {
        $finalData = $this->getAllSettings( $socId );
        $settings = $this->tenantDB()->table( 'income_invoice_settings' )
        ->where( 'soc_id', $socId )
        ->orderByDesc( 'effective_date' )
        ->first();

        $finalData[ 'generalSettingId' ] = $settings->id;
        $finalData[ 'invoiceFrequency' ] = ( isset( $settings->invoicing_frequency ) &&
        $settings->invoicing_frequency != '' ) ? $settings->invoicing_frequency : '';
        $finalData[ 'effectiveDate' ] = $settings->effective_date;

        $finalData[ 'generalSettingKeyIds' ] = $this->getAllSettingsWithIds( $socId );
        return $finalData;
    }

    public function getAllSettingsWithIds( $socId ) {
        $data = [];
        $settingsWithIds = $this->tenantDB()->table( 'income_invoice_general_settings' )
        ->where( 'soc_id', $socId )
        ->get()->toArray();
        foreach ( $settingsWithIds as $settingsWithId ) {
            $data[ $settingsWithId->setting_key ] = $settingsWithId->id;
        }
        return $data;
    }

    public function getAllSettings( $socId ) {
        $data = [];
        $allSettings = $this->tenantDB()->table( 'income_invoice_general_settings' )
        ->where( 'soc_id', $socId )
        ->get()->toArray();
        foreach ( $allSettings as $allSetting ) {
            $data[ $allSetting->setting_key ] = $allSetting->setting_value;
        }
        return $data;
    }

    public function getTaxDetails( $socId ) {
        $taxClass = $this->tenantDB()->table( 'chsone_tax_classes AS ctcl' )
        ->where( 'ctcl.soc_id', $socId )
        ->where( 'ctcl.status', '1' )
        ->whereRaw( '(current_date() BETWEEN ctcat.tax_categories_from_date
                                                    AND ctcat.tax_categories_to_date)
                                                OR (ctcat.tax_categories_from_date IS NULL)
                                                OR (ctcat.tax_categories_to_date IS NULL)' )
        ->leftJoin( 'chsone_tax_categories AS ctcat',
        'ctcl.tax_class_id', '=', 'ctcat.fk_tax_class_id' )
        ->orderBy( 'ctcl.tax_class_id' )
        ->get()->toArray();
        return $taxClass;
    }
}
