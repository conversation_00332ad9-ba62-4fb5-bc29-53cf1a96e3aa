<?php

namespace App\Console\Commands\Actions\Settings\Generalsettings;

use Illuminate\Console\Command;
use App\Console\Commands\Action;
use App\Models\Tenants\SocietyPreference;

class getAppPreferencesDataSource extends Action
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'datasource:getAppPreferences {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Get App Preferences';

    /**
     * Execute the console command.
     */
    public function apply()
    {
        // get app preferences from the society preference table
        $finalArray = [];
        $preferencesData = SocietyPreference::where('status', '1')->get()->toArray();
        foreach ($preferencesData as $key => $preference) {
            if($preference['key'] == 'DISPLAY_TIME_FORMAT' && $preference['value'] == 'H:i:s') {
                $preference['value'] = '24 hours';
            } elseif($preference['key'] == 'DISPLAY_TIME_FORMAT' && $preference['value'] == 'h:i:s a') {
                $preference['value'] = '12 hours';
            }
            $finalArray[$preference['key']] = $preference['value'];
        }

        $this->statusCode = "200";
        $this->status = "success";
        $this->message = "App preferences fetched successfully";
        $this->data = $finalArray;
    }
}
