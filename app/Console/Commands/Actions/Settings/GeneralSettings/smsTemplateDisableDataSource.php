<?php

namespace App\Console\Commands\Actions\Settings\GeneralSettings;

use Illuminate\Console\Command;
use App\Console\Commands\Action;

class smsTemplateDisableDataSource extends Action
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'datasource:smsTemplateDisable {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Disable SMS Template';

    /**
     * Execute the console command.
     */
    public function apply()
    {
        $id = (int) $this->input['id'];
        $status = $this->input['status'];

        $obj = $this->tenantDB()->table('chsone_sms_template')
        ->where('sms_template_id', $id)
        ->update(['is_active' => $status]);

        if($obj) {
            $this->status = 'success';
            $this->message = 'SMS template is Successfully Updated';
            $this->statusCode = 200;
        } else {
            $this->status = 'success';
            $this->message = 'Nothing here to update';
            $this->statusCode = 200;
        }
    }
}
