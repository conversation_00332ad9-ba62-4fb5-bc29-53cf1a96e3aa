<?php

namespace App\Console\Commands\Actions\Settings\GeneralSettings;

use Illuminate\Console\Command;
use App\Console\Commands\Action;

class EmailtemplateDataSource extends Action
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'datasource:autoresponsemaillist {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Get Email Template List';

    protected $formatter = [
        'id' => '',
        'variable_id' => '',
        'title' => '',
        'purpose' => '',
        'subject' => '',
        'body' => '',
        'content_type' => '',
        'is_active' => '',
        'send_to_admin' => '',
        'type' => ''
    ];

    protected $formatterByKeys =  ['email_template_id'];

    protected $mapper = [
        'id' => 'email_template_id'
    ];


    /**
     * Execute the console command.
     */
    public function apply()
    {
        $id = $this->input['id'] ?? null;

        $page = $this->input['page'] ?? 1;
        $per_page = !empty($this->input['per_page']) ? $this->input['per_page']: ($this->hugeData ? 100000 : 10);
        $offset = ($page - 1) * $per_page;

        $totalCount = $this->TenantDB()->table('chsone_email_template')
            ->count();

        $obj = $this->tenantDB()->table('chsone_email_template')
        ->select('*');

        if (!empty($id)) {
            $obj = $obj->where('id', $id)->first();
            $this->data = $obj;
            return;
        } else {
            $obj = $obj->offset($offset)
            ->limit($per_page)
            ->get();   
        }

        $this->meta['pagination']['total'] = $totalCount;
        $this->data = $obj;
    }

}
