<?php

namespace App\Console\Commands\Actions\Settings\GeneralSettings;

use App\Console\Commands\Action;

class EmailtemplateDetailDataSource extends Action
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'datasource:emailTemplateDetail {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Get Email Template List';

    /**
     * Execute the console command.
     */
    public function apply()
    {
        $id = $this->input['id'];

        $obj = $this->tenantDB()->table('chsone_email_template')
            ->select('id',
                'variable_id', 'title',
                'purpose',
                'subject',
                'body',
                'content_type',
                // 'is_active',
                $this->tenantDB()->raw('CASE WHEN is_active = 1 THEN "Active" ELSE "Inactive" END as is_active_status'),
                'send_to_admin',
                'type'
            )
            ->where('id', $id)
            ->first();

        if ($obj && isset($obj->body)) {
            $obj->body = str_replace('Â', '', $obj->body);
        }
        $this->data = $obj;
    }

}
