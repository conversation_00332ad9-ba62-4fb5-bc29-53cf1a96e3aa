<?php

namespace App\Console\Commands\Actions\Settings\GeneralSettings;

use App\Console\Commands\Action;

class EmailTemplateAction extends Action
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'action:emailTemplateEdit {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Email Template Edit';

    /**
     * Execute the console command.
     */
    public function apply()
    {
        $id = $this->input['id'];

        $obj = $this->tenantDB()->table('chsone_email_template')->where('id', $id)
            ->update([
                "title" => $this->input['title'],
                "purpose" => $this->input['purpose'],
                "subject" => $this->input['subject'],
                "body" => $this->input['body'],
            ]);

        if ($obj) {
            $this->status = 'success';
            $this->message = 'Email template is Successfully Updated';
            $this->statusCode = 200;
        } else {
            $this->status = 'success';
            $this->message = 'Nothing here to update';
            $this->statusCode = 200;
        }
    }

    public function responseError($message)
    {
        $this->statusCode = "400";
        $this->status = "error";
        $this->message = $message;
        $this->data = [];
        return false;
    }

    public function responseSuccess($message)
    {
        $this->statusCode = "200";
        $this->status = "success";
        $this->message = $message;
        $this->data = [];
        return true;
    }
}
