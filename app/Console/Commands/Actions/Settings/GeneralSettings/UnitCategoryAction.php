<?php

namespace App\Console\Commands\Actions\Settings\GeneralSettings;

use App\Console\Commands\Action;
use Illuminate\Support\Facades\DB;

class UnitCategoryAction extends Action
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'datasource:listUnitType {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Get list of Complex Unit List';


    protected $formatterByKeys = ['id'];

    protected $mapper = [
        'id' => 'soc_units_type_id',
    ];
    protected $hugeData = true;

    /**
     * Execute the console command.
     */
    public function apply()
    {
        $societyId = $this->input['company_id'];
        $type = $this->input['type'] ?? '';

        // add pagination
        $page = $this->input['page'] ?? 1;
        $per_page = !empty($this->input['per_page']) ? $this->input['per_page'] : ($this->hugeData ? 100000 : 10);

        $offset = ($page - 1) * $per_page;

        // get the count of total users
        $totalCount = $this->tenantDB()->table("chsone_society_units_tpl")
            ->select(
                "soc_units_type_id as id",
                "soc_units_type_id",
                "soc_id",
                "type",
                "soc_unit_type",
                "quantity",
                "quantity_unalloted",
                DB::raw('CAST(area_sq_ft AS DECIMAL(10,2)) as area_sq_ft'),
                DB::raw('CAST(open_space_area_sq_ft AS DECIMAL(10,2)) as open_space_area_sq_ft'),
                "water_inlets_num",
                "status",
                "use_category",
                DB::raw('CASE WHEN chargeable = 1 THEN "Yes" ELSE "No" END as chargeable')
            )
            ->where('soc_id', $societyId)
            ->count();

        $obj = $this->tenantDB()->table("chsone_society_units_tpl")
            ->select(
                "soc_units_type_id as id",
                "soc_units_type_id",
                "soc_id",
                "type",
                "soc_unit_type",
                "quantity",
                "quantity_unalloted",
                DB::raw('CAST(area_sq_ft AS DECIMAL(10,2)) AS area_sq_ft'),
                DB::raw('CAST(open_space_area_sq_ft AS DECIMAL(10,2)) AS open_space_area_sq_ft'),
                "water_inlets_num",
                "status",
                "use_category",
                DB::raw('CASE WHEN chargeable = 1 THEN "Yes" ELSE "No" END as chargeable')
            )
            ->where('soc_id', $societyId)
            ->orderBy('type')
            ->offset($offset)
            ->limit($per_page);

        $result = $obj->get();
        // if ($type === 'edit_unit_category') {
        //     $result = $result->unique('soc_unit_type')->map(function ($item) {
        //         $item->type = $item->soc_unit_type;
        //         return $item;
        //     });
        // }
        $result = $obj->get()->map(function ($item) {
            $item->area_sq_ft = (float) $item->area_sq_ft; // Ensure numeric type
            $item->open_space_area_sq_ft = (float) $item->open_space_area_sq_ft; // Ensure numeric type
            return $item;
        });


        $total = $this->tenantDB()->table("chsone_society_units_tpl")
            ->select(
                DB::raw('SUM(quantity) OVER() as total_quantity') // Add total quantity calculation
            )
            ->where('soc_id', $societyId)
            ->pluck('total_quantity')
            ->first();

        $totalArray = [[
            'id' => 'total',
            'type' => 'Total',
            'quantity' => $total,
        ]];

        //$this->data = array_merge($result->toArray(), $totalArray);
        $this->data = $result->toArray();
        $this->meta['pagination']['total'] = $totalCount;

    }
}
