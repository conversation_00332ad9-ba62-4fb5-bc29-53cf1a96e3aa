<?php

namespace App\Console\Commands\Actions\Settings\GeneralSettings;

use App\Console\Commands\Action;
use App\Models\Tenants\ChsoneSocietyUnitsTpl;
use Illuminate\Support\Facades\Validator;

class EditUnitTypeAction extends Action
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'action:editUnitType {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Edit Unit Type';

    /**
     * Execute the console command.
     */
    public function apply()
    {
        $id = $this->input['id'];
        // $validator = Validator::make($this->input, [
        //     'id' => 'required|numeric|exists:chsone_society_units_tpl,soc_units_type_id'
        // ]);

        // if ($validator->fails()) {
        //     return $this->responseError($validator->messages());

        // }
        // $socUnitsTpl = ChsoneSocietyUnitsTpl::where('soc_units_type_id', $id)->first();
        // $socUnitsTpl->soc_id = $this->input['company_id'];
        // $socUnitsTpl->type = $this->input['type'];
        // $socUnitsTpl->soc_unit_type = $this->input['soc_unit_type'];
        // $socUnitsTpl->quantity = $this->input['quantity'];
        // $socUnitsTpl->area_sq_ft = $this->input['area_sq_ft'];
        // $socUnitsTpl->quantity_unalloted = $this->input['quantity_unalloted'] ?? 0;
        // $socUnitsTpl->open_space_area_sq_ft = $this->input['open_space_area_sq_ft'];
        // $socUnitsTpl->water_inlets_num = $this->input['water_inlets_num'] ?? null;
        // $socUnitsTpl->status = $this->input['status'] ?? 1;
        // $socUnitsTpl->use_category = $this->input['use_category'] ?? "residential";
        // $socUnitsTpl->chargeable = $this->input['chargeable'];
        // $socUnitsTpl->updated_date = date('Y-m-d H:i:s');
        // $socUnitsTpl->updated_by = $this->input['user_id'];

        $unit_data = [
            'soc_id' => $this->input['company_id'],
            'type' => $this->input['type'],
            'soc_unit_type' => $this->input['soc_units_type_id'],
            'quantity' => $this->input['quantity'],
            'area_sq_ft' => $this->input['area_sq_ft'],
            'open_space_area_sq_ft' => $this->input['open_space_area_sq_ft'],
            'water_inlets_num' => $this->input['water_inlets_num'] ?? null,
            'status' => $this->input['status'] ?? 1,
            'use_category' => $this->input['use_category'] ?? "residential",
            'chargeable' => $this->input['chargeable'],
            'updated_date' => date('Y-m-d H:i:s'),
            'updated_by' => $this->input['user_id']
        ];

        $unit_update = $this->tenantDB()->table('chsone_society_units_tpl')
            ->where('soc_units_type_id', $id)
            ->update($unit_data);

        if ($unit_update) {
            $this->status = 'success';
            $this->message = 'Unit Type updated successfully';
            $this->statusCode = 200;
        } else {
            $this->status = 'error';
            $this->message = 'Unable to update Unit Type';
            $this->statusCode = 400;
        }
    }
}
