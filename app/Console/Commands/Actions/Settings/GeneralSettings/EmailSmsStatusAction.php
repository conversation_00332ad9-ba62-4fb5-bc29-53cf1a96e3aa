<?php

namespace App\Console\Commands\Actions\Settings\GeneralSettings;

use App\Console\Commands\Action;
use App\Models\Tenants\ChsoneEmailTemplate;
use App\Models\Tenants\ChsoneSmsTemplate;

class EmailSmsStatusAction extends Action
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'action:emailSmsStatus {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Email/SMS Status change';

    /**
     * Execute the console command.
     */
    public function apply()
    {
        $id = $this->input['id'];
        $status = ($this->input['is_active'] == '1') ? "Active" : "Inacive";
        if ($this->input['is_email']) {
            $emailTemplate = ChsoneEmailTemplate::where('id', $id)->first();
            $emailTemplate->is_active = $this->input['is_active'];
            if ($emailTemplate->save()) {
                $message = "Email Template is ".$status. " now";
                return $this->responseSuccess($message);
            }
        }
        if ($this->input['is_sms']) {
            $emailTemplate = ChsoneSmsTemplate::where('id', $id)->first();
            $emailTemplate->is_active = $this->input['is_active'];
            if ($emailTemplate->save()) {
                $message = "SMS Template is ".$status. " now";
                return $this->responseSuccess($message);
            }
        }
    }

    public function responseError($message)
    {
        $this->statusCode = "400";
        $this->status = "error";
        $this->message = $message;
        $this->data = [];
        return false;
    }

    public function responseSuccess($message)
    {
        $this->statusCode = "200";
        $this->status = "success";
        $this->message = $message;
        $this->data = [];
        return true;
    }
}
