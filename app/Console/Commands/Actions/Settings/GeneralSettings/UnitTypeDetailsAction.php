<?php

namespace App\Console\Commands\Actions\Settings\GeneralSettings;

use App\Console\Commands\Action;
use App\Models\Tenants\ChsoneSocietyUnitsTpl;
use Illuminate\Support\Facades\DB;

class UnitTypeDetailsAction extends Action
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'action:unitTypeDetails {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Get Unit Type Details';

    /**
     * Execute the console command.
     */
    public function apply()
    {
        $id = $this->input['id'];
        $obj = new ChsoneSocietyUnitsTpl;
        $obj = $obj->where('soc_units_type_id', $id)
                   ->select(
                        "soc_units_type_id AS id",
                        "soc_units_type_id",
                        "soc_id",
                        "type",
                        "soc_unit_type",
                        "quantity",
                        "quantity_unalloted",
                        "area_sq_ft",
                        "open_space_area_sq_ft",
                        "water_inlets_num",
                        "status",
                        "use_category",
                        DB::raw("CAST(chargeable AS UNSIGNED) AS chargeable") // Convert to numeric
                        )
                   ->first();

                $obj->chargeable = $obj->chargeable ? true : false;
        $result = $obj;
        if ($result) {
            $this->status = 'success';
            $this->message = 'Unit Type fetched successfully';
            $this->statusCode = 200;
            $this->data = $result;
        } else {
            $this->status = 'error';
            $this->message = 'Unable to fetch Unit Type';
            $this->statusCode = 400;
        }
    }
}
