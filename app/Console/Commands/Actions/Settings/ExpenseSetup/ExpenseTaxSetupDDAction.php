<?php

namespace App\Console\Commands\Actions\Settings\ExpenseSetup;

use App\Console\Commands\Action;
use App\Models\Tenants\GstRatesMaster;

class ExpenseTaxSetupDDAction extends Action
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'action:expenseTaxSetupDD {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Expense Tax setup drop down list';

    /**
     * Execute the console command.
     */
    public function apply()
    {


        // $type = array_column($obj, "type");
        // $rule = array_column($obj, "name");
        // $resultType = array_values(array_unique(array_map("strtoupper", $type)));
        // $resultRule = array_map("strtoupper", $rule);
        // array_push($result, [
        //     "type" => $resultType,
        //     "rule" => $resultRule
        // ]);
        // $message = "Rule type listed successfully";
        // return $this->responseSuccess($message, $result);
        $page = $this->input['page'] ?? 1;
        $per_page = !empty($this->input['per_page']) ? $this->input['per_page']: ($this->hugeData ? 100000 : 10);

        $offset = ($page - 1) * $per_page;

        $obj = $this->tenantDB()->table('gst_rates_master')
            ->select("type", "name","rate","id")
            ->distinct()
            ->offset($offset)
            ->limit($per_page)
            ->get();

            $totalCount = $this->tenantDB()->table('gst_rates_master')
            ->distinct()
            ->count();

            $obj=json_decode(json_encode($obj), true);

        $IGST = [
            [
            "id" => "a",
            "ledger_account_name" => "IGST - 0%",
            "igst" => 0,
            ]
        ];
        $GST = [
            [
            "id" => "b",
            "ledger_account_name" => "GST - 0%",
            'cgst' => 0,
            'sgst' => 0,
            ]
        ];
        foreach ($obj as $key => $value) {
            if($value["type"] == "igst"){
                $IGST[]= [
                    "id" => $value['id'],
                    "ledger_account_name" => strToUpper($value["type"]). '-'.$value["rate"]. '%',
                    "igst" => $value["rate"],
                ];
            }
            else if($value["type"] == "gst"){
                $GST[] = [
                    "id" => $value['id'],
                    "ledger_account_name" => strToUpper($value["type"]). '-'.$value["rate"]. '%',
                    'cgst' => $value["rate"]/2,
                    'sgst' => $value["rate"]/2,
                ];
            }

        }


        $response = [
            [
                'id' => "B",
                'ledger_account_name' => 'GST',
                'rows' =>  $GST
            ],
            [
                'id' => "A",
                'ledger_account_name' => 'IGST',
                'rows' => $IGST


            ],
        ];

        $this->data = $response;
        $this->meta['pagination']['total'] = $totalCount;

    }

    public function responseError($message)
    {
        $this->statusCode = "400";
        $this->status = "error";
        $this->message = $message;
        $this->data = [];
        return false;
    }

    public function responseSuccess($message, $data = [])
    {
        $this->statusCode = "200";
        $this->status = "success";
        $this->message = $message;
        $this->data = $data;
        return true;
    }
}
