<?php

namespace App\Console\Commands\Actions\Settings\ExpenseSetup;

use Illuminate\Console\Command;
use App\Console\Commands\Action;

class ExpensetdsrateDataSource extends Action
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'datasource:expensetdsrate {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Get Tds Rate List';

    protected $formatter = [
        'id' => '',
        'rate' => '',
        'section' => ''
    ];

    protected $formatterByKeys =  ['id'];

    protected $mapper = [];


    /**
     * Execute the console command.
     */
    public function apply()
    {
        $obj = $this->TenantDB()->table('tds_rates_master')
                ->select('*');

        $obj = $this->filter($obj);
        $obj = $obj->orderBy('id', 'asc');
        $result = $obj->get();
        $this->data = $this->format($result->toArray());
    }

}
