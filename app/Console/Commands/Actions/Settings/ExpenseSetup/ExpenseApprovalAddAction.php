<?php

namespace App\Console\Commands\Actions\Settings\ExpenseSetup;

use Illuminate\Console\Command;
use App\Console\Commands\Action;
use App\Models\Tenants\ExpenseLimitRange;
use Illuminate\Support\Facades\Validator;

class ExpenseApprovalAddAction  extends Action
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'action:expenseApprovalAdd {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Expense Approval';

    /**
     * Execute the console command.
     */
    public function apply()
    {
        $success = [];
        $error = [];

        // $this->validateParams($this->input);
        $params = $this->input['params'];
        
        foreach ($params as $input) {
                $expenseRange = new ExpenseLimitRange();

                if (
                    !isset($input['limit_from'])
                    || !isset($input['limit_to'])
                    || !isset($input['limit_name'])
                    || !isset($input['approvers'])
                    || !isset($input['warning'])
                ) {
                    continue;
                }
                if ($input['_action'] === 'edit') {
                    // Fetch the record for the current ID and limit_name
                    $expenseRangeData = $expenseRange->where('id', $input['id'])->first();
                
                    // Check if `limit_name` already exists for a different ID
                    $expenseRangeDataExist = $expenseRange
                        ->where('id', '!=', $input['id']) // Exclude the current ID
                        ->where('limit_name', $input['limit_name']) // Check for duplicate limit_name
                        ->first();

                    if ($expenseRangeDataExist) {
                        // If it exists, set an appropriate error message
                        $message = $input['limit_name'] . " already exists with a different ID.";
                        $this->meta['errors'] = [
                            'limit_name' => $message,
                        ];
                    } elseif ($expenseRangeData) {
                        // Perform updates only if the data has changed
                        if (
                            $input['limit_name'] != $expenseRangeData['limit_name'] ||
                            $input['limit_from'] != explode(".", $expenseRangeData['limit_from'])[0] ||
                            $input['limit_to'] != explode(".", $expenseRangeData['limit_to'])[0] ||
                            $input['approvers'] != $expenseRangeData['approvers'] ||
                            $input['warning'] != $expenseRangeData['warning']
                        ) {
                            // Update the record
                            $expenseRangeData->soc_id = $this->input['company_id'];
                            $expenseRangeData->limit_name = $input['limit_name'];
                            $expenseRangeData->limit_from = explode('.', $input['limit_from'])[0];
                            $expenseRangeData->limit_to = explode('.', $input['limit_to'])[0];
                            $expenseRangeData->approvers = $input['approvers'];
                            $expenseRangeData->warning = $input['warning'];
                
                            // Save the updated record
                            if ($expenseRangeData->save()) {
                                $message = $input['limit_name'] . " changed successfully";
                                array_push($success, ["success" => true, "message" => $message]);
                            }
                        }
                    }
                } else if ($input['_action'] === 'add') {
                $expenseRangeData = $expenseRange->whereRaw('LOWER(limit_name) = "' . strtolower($input['limit_name']) . '"')
                    ->where('status', '1')
                    ->first();
                if (empty($expenseRangeData)) {
                    $expenseRange->soc_id = $this->input['company_id'];
                    $expenseRange->limit_name = $input['limit_name'];
                    $expenseRange->limit_from = explode('.', $input['limit_from'])[0];
                    $expenseRange->limit_to = explode('.', $input['limit_to'])[0];
                    $expenseRange->approvers = $input['approvers'];
                    $expenseRange->warning = $input['warning'];
                    if ($expenseRange->save()) {
                        $message = $input['limit_name'] . " added successfully";
                        array_push($success, ["success" => true, "message" => $message]);
                    } else {
                        /*$message = $input['limit_name'] . " failed to add." . $expenseRange->getMessages();
                        array_push($error, ["success" => false, "message" => $message]);*/
                    }
                } else {
                    $message = $input['limit_name'] . " already exist.";
                    array_push($success, ["success" => false, "message" => $message]);
                }
            } else if ($input["_action"] == "delete") {
                $expenseRangeData = $expenseRange->where('id', $input['id'])->first();
                if (is_null($expenseRangeData)) {
                    $message = isset($input['limit_name'])
                        ? $input['limit_name'] . " does not exist."
                        : "The limit_name field is missing.";

                    $this->meta['errors'] = [
                        'limit_name' => $message,
                    ];
                    // array_push($error, ["error" => true, "message" => $message]);
                } else {
                    if ($expenseRangeData->delete()) {
                        $message = $input['limit_name'] . " deleted successfully";
                        array_push($success, ["success" => true, "message" => $message]);
                    } else {
                        /*$message = $input['limit_name'] . " failed to delete." . $expenseRange->getMessages();
                        array_push($error, ["success" => false, "message" => $message]);*/
                    }
                }
            }
        }
        if (!empty($success)) {
            return $this->responseSuccess(array_column($success, "message"));
        }

        if (!empty($error)) {
            return $this->responseError(array_column($error, "message"));
        }
    }



    public function validateParams($data)
    {
        $errors = [];
        $params = $data['params'] ?? [];

        // Check if params is an array
        if (!is_array($params)) {
            $errors['params'] = ['Params should be an array'];
        } else {
            foreach ($params as $index => $param) {
                dd($param);
                // Validate each key individually
                if (empty($param['limit_name'])) {
                    $errors["params.$index.limit_name"][] = 'Limit Name is required.';
                } elseif (!is_string($param['limit_name'])) {
                    $errors["params.$index.limit_name"][] = 'Limit Name should be a string.';
                }

                if (!isset($param['limit_from'])) {
                    $errors["params.$index.limit_from"][] = 'Limit From is required.';
                } elseif (!is_numeric($param['limit_from'])) {
                    $errors["params.$index.limit_from"][] = 'Limit From should be numeric.';
                } elseif ($param['limit_from'] < 0) {
                    $errors["params.$index.limit_from"][] = 'Limit From must be greater than or equal to 0.';
                }

                if (!isset($param['limit_to'])) {
                    $errors["params.$index.limit_to"][] = 'Limit To is required.';
                } elseif (!is_numeric($param['limit_to'])) {
                    $errors["params.$index.limit_to"][] = 'Limit To should be numeric.';
                } elseif ($param['limit_to'] < 0) {
                    $errors["params.$index.limit_to"][] = 'Limit To must be greater than or equal to 0.';
                } elseif (isset($param['limit_from']) && $param['limit_to'] <= $param['limit_from']) {
                    $errors["params.$index.limit_to"][] = 'Limit To must be greater than Limit From.';
                }

                if (empty($param['approvers'])) {
                    $errors["params.$index.approvers"][] = 'Approvers is required.';
                } elseif (!is_string($param['approvers'])) {
                    $errors["params.$index.approvers"][] = 'Approvers should be a string.';
                }

                if (empty($param['warning'])) {
                    $errors["params.$index.warning"][] = 'Warning is required.';
                } elseif (!is_string($param['warning'])) {
                    $errors["params.$index.warning"][] = 'Warning should be a string.';
                }
            }
        }

        // Return validation errors
        if (!empty($errors)) {
            $this->statusCode = "400";
            $this->status = "error";
            $this->message = $errors;
            $this->data = [];
            return false;
        }

        $this->statusCode = "200";
        $this->status = "success";
        $this->message = "Params validated successfully";
        $this->data = $data;
        return true;
    }

    public function responseError($message)
    {
        $this->statusCode = "400";
        $this->status = "error";
        $this->message = $message;
        $this->data = [];
        return false;
    }

    public function responseSuccess($message, $data = [])
    {
        $this->statusCode = "200";
        $this->status = "success";
        $this->message = $message;
        $this->data = $data;
        return true;
    }
}
