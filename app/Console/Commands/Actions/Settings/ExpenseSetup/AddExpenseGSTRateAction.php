<?php

    namespace App\Console\Commands\Actions\Settings\ExpenseSetup;

    use App\Console\Commands\Action;
    use App\Models\Tenants\{GstRatesMaster, ChsoneGrpLedgerTree};

    class AddExpenseGSTRateAction extends Action
    {
        /**
         * The name and signature of the console command.
         *
         * @var string
         */
        protected $signature = 'action:addExpenseGSTRate {flowId} {parentId} {input}';

        /**
         * The console command description.
         *
         * @var string
         */
        protected $description = 'Add Expense GST Rate';

        /**
         * Execute the console command.
         */
        public function apply()
        {
            $page = $this->input['page'] ?? 1;
            $per_page = !empty($this->input['per_page']) ? $this->input['per_page']: ($this->hugeData ? 100000 : 10);

            $offset = ($page - 1) * $per_page;
            if ($this->input['method'] == 'get') {
                $gstRates = $this->tenantDB()->table('gst_rates_master')
                                        ->where('soc_id', $this->input['company_id'])
                                        ->select('id', 'soc_id', 'type', 'name', 'rate')
                                        ->limit($per_page)
                                        ->offset($offset)
                                        ->get();

                $count = $this->tenantDB()->table('gst_rates_master')
                                        ->where('soc_id', $this->input['company_id'])
                                        ->count();

                $this->meta['pagination']['total'] = $count;

                $message = "GST Rates listed successfully";
                return $this->responseSuccess($message, $gstRates);
            }
        }

        public function responseError($message)
        {
            $this->statusCode = "400";
            $this->status = "error";
            $this->message = $message;
            $this->data = [];
            return false;
        }

        public function responseSuccess($message, $data = [])
        {
            $this->statusCode = "200";
            $this->status = "success";
            $this->message = $message;
            $this->data = $data;
            return true;
        }
}
