<?php

namespace App\Console\Commands\Actions\Settings\ExpenseSetup;

use App\Console\Commands\Action;
use App\Models\Tenants\{GstRatesMaster, ChsoneGrpLedgerTree};

class AddExpenseGstAction extends Action
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'action:addExpenseGst {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Add Expense GST Rate';

    /**
     * Execute the console command.
     */
    public function apply()
    {

        $rules = $this->input['params'];
        $error = false;
        $ledgerName = "Dueties And Taxes";
        $entityType = "group";
        $context = "membercontri";
        $behaviour = "liability";
        $parentId = "";
        $arrTaxParentLedgerDetail = $this->getLedgerDetail($ledgerName, $entityType, $context, $behaviour);
        if (!empty($arrTaxParentLedgerDetail)) {
            $parentId = $arrTaxParentLedgerDetail;
        }
        foreach ($rules as $key => $rule) {
            if(!isset($rule['type']) || !isset($rule['name']) || !isset($rule['rate'])) {
                continue;
            }
            $gstRule = new GstRatesMaster();
            if (strtolower($rule['type']) == "igst") {
                $rule['name'] = "igst";
            }

            if($rule['_action'] == 'edit') {
                $gstRule = $gstRule->where('id', $rule['id'])->first();
                if (empty($gstRule)) {
                    $error = true;
                    $message = "Tax Rule not found";
                    break;
                }else{
                    //update the data with new with new values with this id
                    $gstRule->type         = $rule['type'];
                    $gstRule->name         = $rule['name'];
                    $gstRule->rate         = $rule['rate'];
                    $gstRule->updated_on   = $this->getCurrentDate('database');
                    if ($gstRule->save()) {
                        $message = "Tax Rule updated successfully";
                    } else {
                        $error = true;
                        $message = $gstRule->getMessages();
                    }   
                }                 
                
            }
            $ledgerName = trim(str_replace(' ','', strtoupper($rule['name']))).$rule['rate'].'Percentage';
            $ledgerId = $this->createNewLedgerExit($ledgerName, $parentId, $context, $behaviour);
            $gstRule->soc_id       = $this->input['company_id'];
            $gstRule->type         = $rule['type'];
            $gstRule->name         = $rule['name'];
            $gstRule->rate         = $rule['rate'];
            $gstRule->status       = 1;
            $gstRule->to_ledger_id = $ledgerId;
            $gstRule->created_on   = $this->getCurrentDate('database');
            $gstRule->updated_on   = $this->getCurrentDate('database');
            if ($gstRule->save()) {
                $message = "Tax Rule added successfully";
            } else {
                $error = true;
                $message = $gstRule->getMessages();
            }
        }
        if ($error) {
            return $this->responseError($message);
        }
        return $this->responseSuccess($message);
    }

    public function responseError($message)
    {
        $this->statusCode = "400";
        $this->status = "error";
        $this->message = $message;
        $this->data = [];
        return false;
    }

    public function responseSuccess($message, $data = [])
    {
        $this->statusCode = "200";
        $this->status = "success";
        $this->message = $message;
        $this->data = $data;
        return true;
    }

    public function createNewLedgerExit($ledgerName, $parentId, $context, $behaviour)
    {
        $grpLedgerTree = new ChsoneGrpLedgerTree();
        $grpLedgerTreeData = $grpLedgerTree->where("ledger_account_name", $ledgerName)
                                            ->where("behaviour", $behaviour)
                                            ->where("parent_id", $parentId)
                                            ->where("entity_type", "ledger")
                                            ->first();

        if (!empty($grpLedgerTreeData)) {
            return $grpLedgerTreeData['ledger_account_id'];
        } else {
            $ledgerId = $this->dataManipulation($ledgerName, "ledger", "", $parentId, $behaviour, "", 0, "", "", null, $context);
            if ((gettype($ledgerId) != 'boolean') && (strpos('DUP', $ledgerId) === false)) {
                return $ledgerId;
            }
        }
        return false;
    }

    public function getLedgerDetail($ledgerName, $entityType, $context, $behaviour)
    {
        $grpLedgerTree = new ChsoneGrpLedgerTree();
        $grpLedgerTreeData = $grpLedgerTree->where("ledger_account_name", $ledgerName)
                      ->where("behaviour", $behaviour)
                      ->where("context", $context)
                      ->where("entity_type", $entityType)
                      ->first();

        if (!empty($grpLedgerTreeData)) {
            return $grpLedgerTreeData['ledger_account_id'];
        }
        return false;
    }
}
