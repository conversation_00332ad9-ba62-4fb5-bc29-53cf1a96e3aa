<?php

namespace App\Console\Commands\Actions\Settings\ExpenseSetup;

use Illuminate\Console\Command;
use App\Console\Commands\Action;
use App\Models\Tenants\SocAccountFinancialYearMaster;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;

class ListExpenseBudgetAction extends Action
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'action:listExpenseBudget {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'List Expense Budget';

    /**
     * Execute the console command.
     */
    public function apply()
    {

        // $financialMonth = $this->input['financial_month'] ?? 0;

        // $defaultFinancialYear = $this->tenantDB()
        //                             ->table('soc_account_financial_year_master')
        //                             ->select('account_closing_id')
        //                             ->where('closed', 1)
        //                             ->where('confirmed', 0)
        //                             ->orderBy('account_closing_id', 'desc')
        //                             ->first();

        // $financialYear = $this->input['financial_year'] ?? $defaultFinancialYear->account_closing_id;

        $financialYear = isset($this->input['filters']['financial_year']) ? $this->input['filters']['financial_year'] : null;

        if ($financialYear) {
            [$startYear, $endYear] = explode('-', $financialYear);
            //$endDate = $endYear . '-03-31';
        
            $financialYearData = $this->tenantDB()
                ->table('soc_account_financial_year_master')
                ->select('fy_start_date', 'fy_end_date', 'account_closing_id')
                ->whereYear('fy_start_date', $startYear)
                ->whereYear('fy_end_date', $endYear)        
                ->first();
                
        }
        // Get the start and end date of the financial year
        // $startDate = $financialYearData->fy_start_date; // Example: '2021-04-01'
        // $endDate = $financialYearData->fy_end_date;     // Example: '2022-03-31'

        // Extract the month ID from the financial month
        $financialMonth = explode('-', isset($this->input['filters']['financial_month']) ? $this->input['filters']['financial_month'] : 0);
        $monthId = (int) $financialMonth[0];

        // if financialYearData is empty then set default start and end date
        if(!empty($financialYearData)){
            if (empty($monthId)) {
                $monthId = 0;
                $startDate = Carbon::createFromDate($financialYearData->fy_start_date)->startOfMonth();
                $endDate = Carbon::createFromDate($financialYearData->fy_end_date)->endOfMonth();
            } else {
                // suppose my month is 9 then start date will be 2021-09-01 and end date will be 2021-09-30
                $startDate = Carbon::createFromDate($financialYearData->fy_start_date)->month($monthId)->startOfMonth();
                $endDate = Carbon::createFromDate($financialYearData->fy_start_date)->month($monthId)->endOfMonth();
            }  
            
            $page = $this->input['page'] ?? 1;
            $per_page = !empty($this->input['per_page']) ? $this->input['per_page']: ($this->hugeData ? 100000 : 10);
            $offset = ($page - 1) * $per_page;

            // Execute the query
            $result = $this->tenantDB()->table('chsone_expense_accounts_budgets as eba')
            ->selectRaw(
                "eba.id, 
                l.ledger_account_name, 
                et.et_type_name, 
                CAST(eba.budget AS DECIMAL(10,2)) as budget,
                (
                    SELECT IFNULL(SUM(drledgertxn.transaction_amount), 0)
                    FROM chsone_ledger_transactions AS drledgertxn
                    WHERE drledgertxn.ledger_account_id = et.et_ledger_account_id
                    AND drledgertxn.transaction_type = 'dr'
                    AND drledgertxn.transaction_date >= ?
                    AND drledgertxn.transaction_date <= ?
                ) -
                (
                    SELECT IFNULL(SUM(crledgertxn.transaction_amount), 0)
                    FROM chsone_ledger_transactions AS crledgertxn
                    WHERE crledgertxn.ledger_account_id = et.et_ledger_account_id
                    AND crledgertxn.transaction_type = 'cr'
                    AND crledgertxn.transaction_date >= ?
                    AND crledgertxn.transaction_date <= ?
                ) AS actual_expense",
                [$startDate, $endDate, $startDate, $endDate]
            )
            ->join('chsone_expense_tracker as et', 'eba.et_id', '=', 'et.et_id')
            ->join('chsone_grp_ledger_tree as l', 'l.ledger_account_id', '=', 'et.et_ledger_account_id')
            ->where('eba.fy_id', $financialYearData->account_closing_id)
            ->where('eba.month_id', $monthId)
            ->offset($offset)
            ->limit($per_page)
            ->get()
            ->map(function ($item) {
                $item->budget = (float) $item->budget;
                $item->actual_expense = (float) $item->actual_expense;
                return $item;
            });

            // Get the total count
            $totalCount = $this->tenantDB()->table('chsone_expense_accounts_budgets as eba')
            ->selectRaw(
                "eba.id, 
                l.ledger_account_name, 
                et.et_type_name, 
                CAST(eba.budget AS DECIMAL(10,2)) as budget,
                (
                    SELECT IFNULL(SUM(drledgertxn.transaction_amount), 0)
                    FROM chsone_ledger_transactions AS drledgertxn
                    WHERE drledgertxn.ledger_account_id = et.et_ledger_account_id
                    AND drledgertxn.transaction_type = 'dr'
                    AND drledgertxn.transaction_date >= ?
                    AND drledgertxn.transaction_date <= ?
                ) -
                (
                    SELECT IFNULL(SUM(crledgertxn.transaction_amount), 0)
                    FROM chsone_ledger_transactions AS crledgertxn
                    WHERE crledgertxn.ledger_account_id = et.et_ledger_account_id
                    AND crledgertxn.transaction_type = 'cr'
                    AND crledgertxn.transaction_date >= ?
                    AND crledgertxn.transaction_date <= ?
                ) AS actual_expense",
                [$startDate, $endDate, $startDate, $endDate]
            )
            ->join('chsone_expense_tracker as et', 'eba.et_id', '=', 'et.et_id')
            ->join('chsone_grp_ledger_tree as l', 'l.ledger_account_id', '=', 'et.et_ledger_account_id')
            ->where('eba.fy_id', $financialYearData->account_closing_id)
            ->where('eba.month_id', $monthId)
            ->count();

            $this->data = $result;
            $this->meta['pagination']['total'] = $totalCount;
        }
    }
}
