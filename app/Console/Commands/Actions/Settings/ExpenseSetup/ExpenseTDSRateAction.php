<?php

    namespace App\Console\Commands\Actions\Settings\ExpenseSetup;

    use App\Console\Commands\Action;
    use App\Models\Tenants\TdsRatesMaster;
    use Illuminate\Support\Facades\Validator;

    class ExpenseTDSRateAction extends Action
    {
        /**
         * The name and signature of the console command.
         *
         * @var string
         */
        protected $signature = 'action:expenseTdsRate {flowId} {parentId} {input}';

        /**
         * The console command description.
         *
         * @var string
         */
        protected $description = 'Add Expense TDS Rate';

        /**
         * Execute the console command.
         */
        public function apply()
        {
            $id = $this->input['id'] ?? null;
            if ($this->input['method'] == 'get') {
                if (!is_null($id)) {
                    $this->data = $this->tenantDB()->table('tds_rates_master')
                                    ->where('id', $id)
                                    ->select('id', 'rate', 'section')
                                    ->first();
                } else {
                    $page = $this->input['page'] ?? 1;
                    $per_page = !empty($this->input['per_page']) ? $this->input['per_page']: ($this->hugeData ? 100000 : 10);

                    $offset = ($page - 1) * $per_page;
                    $this->data = $this->tenantDB()->table('tds_rates_master')
                                        ->selectRaw('id, rate, CONCAT(section, " - ", rate ,"%") as section')
                                        ->offset($offset)
                                        ->limit($per_page)
                                        ->get();

                    $totalCount = $this->tenantDB()->table('tds_rates_master')
                                    ->count();

                    $this->meta['pagination']['total'] = $totalCount;

                }
                $this->message = "TDS Rate fetched successfully";
            } elseif (isset($this->input['method']) && $this->input['method'] == 'delete') {
                $data = TdsRatesMaster::where('id', $id)->first();
                if (is_null($data)) {
                    $this->message = "Record does not exist";
                    $this->statusCode = 400;
                } elseif ($data->delete()) {
                    $this->message = "TDS Rate deleted successfully";
                }
            } else {
                $validator = Validator::make($this->input, [
                    'rate' => 'required|numeric|min:1',
                    'section' => 'required',
                ], [
                    'rate.numeric' => 'The rate must be a number.',
                    'rate.min' => 'The rate must be at least 1.',
                    'rate.required' => 'The rate field is required.',
                    'section.required' => 'The section field is required.',
                ]);

                if ($validator->fails()) {
                    $this->message = $validator->messages();
                }
                $rate = $this->input['rate'];
                $section = $this->input['section'];
                $data = [
                    'rate' => $rate,
                    'section' => $section,
                    'created_on' => $this->getCurrentDate('database'),
                    'updated_on' => $this->getCurrentDate('database'),
                    'created_by' => $this->input['user_id'],
                    'updated_by' => $this->input['user_id'],
                ];
                if (!is_null($id)) {
                    $tdsRates = TdsRatesMaster::where('id', $id)
                                            ->first();
                    if (!empty($tdsRates)) {
                        $obj = TdsRatesMaster::where('id', $id)->update($data);
                        $this->message = "TDS Rate updated successfully";
                    }
                }else{
                        $obj = $this->tenantDB()->table('tds_rates_master')->insert($data);
                        if ($obj) {
                            $this->message = "TDS Rate added successfully";
                        }
                   }
            }
        }

        public function responseError($message)
        {
            $this->statusCode = "400";
            $this->status = "error";
            $this->message = $message;
            $this->data = [];
            return false;
        }
}
