<?php

namespace App\Console\Commands\Actions\Settings\ExpenseSetup;

use Illuminate\Console\Command;
use App\Console\Commands\Action;

class ExpenselistDataSource extends Action
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'datasource:expenselist {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Get Expense Tracker List';

    protected $formatter = [
        'id' => '',
        'expense_type' => '',
        'expense_ledger' => '',
        'activate' => '',
    ];

    protected $formatterByKeys = ['id'];

    protected $mapper = [
        'id' => 'expt.et_id',
        'expense_type' => 'expt.et_type_name',
        'expense_ledger' => 'chsone_grp_ledger_tree.ledger_account_name',
        'activate' => 'expt.status'
    ];


    /**
     * Execute the console command.
     */
    public function apply()
    {
        $page = $this->input['page'] ?? 1;
        $per_page = !empty($this->input['per_page']) ? $this->input['per_page']: ($this->hugeData ? 100000 : 10);

        $offset = ($page - 1) * $per_page;
        $company_id = $this->input['company_id'];

        $obj = $this->TenantDB()->table('chsone_expense_tracker as expt')
            ->select('expt.et_id as id', 'expt.et_type_name', 'chsone_grp_ledger_tree.ledger_account_name','expt.et_ledger_account_id', 'expt.status')
            ->join('chsone_grp_ledger_tree', 'chsone_grp_ledger_tree.ledger_account_id', '=', 'expt.et_ledger_account_id')
            ->where('expt.soc_id', $company_id)
            ->orderBy('expt.et_id', 'DESC')
            ->limit($per_page)
            ->offset($offset)
            ->get();

        $totalCount = $this->TenantDB()->table('chsone_expense_tracker as expt')
            ->select('expt.et_id as id', 'expt.et_type_name as expense_type', 'chsone_grp_ledger_tree.ledger_account_name as expense_ledger', 'expt.status as activate')
            ->join('chsone_grp_ledger_tree', 'chsone_grp_ledger_tree.ledger_account_id', '=', 'expt.et_ledger_account_id')
            ->where('expt.soc_id', $company_id)
            ->count();

        $this->data = $obj;
        $this->meta['pagination']['total'] = $totalCount;

    }
}
