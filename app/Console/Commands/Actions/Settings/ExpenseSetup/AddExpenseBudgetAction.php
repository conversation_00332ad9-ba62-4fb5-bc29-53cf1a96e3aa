<?php

namespace App\Console\Commands\Actions\Settings\ExpenseSetup;

use Illuminate\Console\Command;
use App\Console\Commands\Action;
use App\Models\Tenants\ChsoneExpenseAccountsBudget;
use Illuminate\Support\Facades\DB;

class AddExpenseBudgetAction extends Action
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'action:addExpenseBudget {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Add Expense Budget';

    /**
     * Execute the console command.
     */
    public function apply()
    {
        $expense_budget = $this->input['params'];

        // Prepare the CASE statements and parameters
        $caseStatements = [];
        $ids = [];
        $parameters = [];

        // Build CASE statements and collect IDs and budget values
        foreach ($expense_budget as $item) {
            $id = $item['id'];
            $budget = (float) $item['budget']; // Ensure budget is a float

            // check if id exists
            $expenseBudget = ChsoneExpenseAccountsBudget::find($id);

            if (!$expenseBudget) {
                $this->status = 'error';
                $this->statusCode = 400;
                $this->message = 'Expense Budget ID ' . $id . ' not found';
                return;
            }

            // need to first update the budget amount in the chsone_expense_accounts_budgets table whose month_id is not 0
            // check if month_id is 0
            if ($expenseBudget->month_id == 0){
                $etbudget3 = ChsoneExpenseAccountsBudget::where('month_id', '!=', 0)
                ->where('fy_id', $expenseBudget->fy_id)
                ->where('et_id', $expenseBudget->et_id)
                ->get();

                if (!empty($etbudget3)) {
                    foreach ($etbudget3 as $k1 => $v1) {
                        if ($k1 == 11) {
                            $v1->budget = $expenseBudget->budget - 11 * $budget / 12;
                        } else {
                            $v1->budget = $budget / 12;
                        }
                
                        // Set the updated_date field
                        $v1->updated_date = date('Y-m-d');
                
                        // Save the record to the database
                        $v1->save();
                    }
                }
            } else { 
                // if month_id is not 0
                // check previous budget amount and input budget amount
                $previous_budget = $expenseBudget->budget;
                $input_budget = $budget;
                $diff = 0;
                
                // if previous budget amount is less than input budget amount then find the difference
                if ($previous_budget < $input_budget) {
                    $diff = $input_budget - $previous_budget;
                } else {
                    $diff = $previous_budget - $input_budget;
                }
                
                // before updating budget need to fetch previous budget amount from chsone_expense_accounts_budgets table whose month_id = 0, fy_id, et_id
                $previpusAmountOfMonthIsZero = ChsoneExpenseAccountsBudget::select('budget')
                ->where('month_id', '=', 0)
                ->where('fy_id', $expenseBudget->fy_id)
                ->where('et_id', $expenseBudget->et_id)
                ->first();

                $previpusAmountOfMonthIsZero = $previpusAmountOfMonthIsZero->budget;

                // according to the diff add or subtract the diff amount from chsone_expense_accounts_budgets table whose the month_id = 0, fy_id, et_id
                $newAmount = 0;
                if ($previous_budget < $input_budget) {
                    $newAmount = $previpusAmountOfMonthIsZero + $diff;
                } else {
                    $newAmount = $previpusAmountOfMonthIsZero - $diff;
                }

                // update new amount as a budget in chsone_expense_accounts_budgets table whose month_id = 0, fy_id, et_id
                $updateBudget =  ChsoneExpenseAccountsBudget::where('month_id', '=', 0)->where('fy_id', $expenseBudget->fy_id)->where('et_id', $expenseBudget->et_id)->first();
                $updateBudget->budget = $newAmount;
                $updateBudget->save();
            }

            // Add each expense budget id to IDs array
            $ids[] = $id;
            // Add each id and budget to parameters array
            $parameters[] = $id;
            $parameters[] = $budget;

            // Use placeholders in the CASE statement
            $caseStatements[] = "WHEN id = ? THEN ?";
        }

        // Construct the SQL query
        $sql = "
            UPDATE chsone_expense_accounts_budgets
            SET
                budget = CASE " . implode(' ', $caseStatements) . " ELSE budget END
            WHERE id IN (" . implode(',', array_fill(0, count($ids), '?')) . ")
        ";

        // Append IDs to parameters array
        $parameters = array_merge($parameters, $ids);

        // Execute the query
        $result = $this->tenantDB()->statement($sql, $parameters);

        if($result === false) {
            $this->status = 'error';
            $this->statusCode = 400;
            $this->message = 'Failed to update Expense Budget';
            return;
        } else {
            $this->status = 'success';
            $this->statusCode = 200;
            $this->message = 'Expense Budget updated successfully';
            return;
        }
    }
}
