<?php 
    
    namespace App\Console\Commands\Actions\Settings\ExpenseSetup;

    use Illuminate\Console\Command;
    use App\Console\Commands\Action;
    use Illuminate\Support\Facades\Validator;
    use App\Models\Tenants\{
        SocAccountFinancialYearMaster,
        ChsoneExpenseAccountsBudget,
        ChsoneExpenseTracker
    };
 
 
    class ExpenseSettingsAddAction extends Action
    {
        /**
         * The name and signature of the console command. 
         *
         * @var string 
         */
        protected $signature = 'action:expensesettingsadd {flowId} {parentId} {input}';

        /**
         * The console command description.
         *
         * @var string
         */
        protected $description = 'Add Expense Settings';

        /**
         * Execute the console command.
         */
        public function apply()
        {
            $params = $this->input['params'];
            $financialYear = SocAccountFinancialYearMaster::orderByDesc('account_closing_id')->get()->toArray();
            foreach ($params as $key => $param) {

                $expenseTracker = new ChsoneExpenseTracker();
                if (
                    !isset($param['et_type_name'])
                    || !isset($param['et_ledger_account_id'])
                    || !isset($param['status'])
                ) {
                    continue;
                }
                if($param["_action"]=="edit"){
                    $expenseTracker = ChsoneExpenseTracker::where('et_id', $param['id'])->first();
                    if(!$expenseTracker){
                        return $this->responseError("Record not found");
                    }
                    $expenseTracker->et_ledger_account_id = $param["et_ledger_account_id"];
                    $expenseTracker->et_type_name = $param["et_type_name"];
                    $expenseTracker->status = $param["status"];
                    $expenseTracker->added_on = date("Y-m-d H:i:s");
                    $expenseTracker->updated_on = date("Y-m-d H:i:s");
                    if (!$expenseTracker->save()) {
                        return $this->responseError("Unable to save record");
                    }

                }

                if ($param["_action"]=="add") { 
                
                $expenseTracker->et_type_name = $param['et_type_name'];
                $expenseTracker->et_ledger_account_id = $param['et_ledger_account_id'];
                $expenseTracker->soc_id = $this->input['company_id'];
                $expenseTracker->et_annual_budget = "0.00";
                $expenseTracker->status = $param['status'] ?? '0';
                $expenseTracker->added_on = date("Y-m-d H:i:s");
                $expenseTracker->updated_on = date("Y-m-d H:i:s");
                if (!$expenseTracker->save()) {
                    return $this->responseError("Unable to save record");
                }else{
                    $id = $expenseTracker->et_id;
                foreach ($financialYear as $key => $fYear) {
                    for ($i = 4; $i <= 16; $i++) {
                        $etBudget = new ChsoneExpenseAccountsBudget();
                        $etBudget->et_id = $id;
                        $etBudget->fy_id = $fYear['account_closing_id'];
                        if ($i == 16) {
                            $etBudget->month_id = 0;
                            $etBudget->budget = 0;
                        } else {
                            $etBudget->month_id = ($i <= 12) ? $i : $i - 12;
                            $etBudget->budget = 0;
                        } 
                        $etBudget->created_date = date("Y-m-d H:i:s");
                        $etBudget->updated_date = date("Y-m-d H:i:s");
                        $etBudget->updated_by = $this->input['user_id'];
                        /*$etBudgetNew = ChsoneExpenseAccountsBudget::where("et_id", $id)
                            ->where("fy_id", $fYear['account_closing_id'])
                            ->where("month_id", $etBudget->month_id)
                            ->get()->toArray();
                        */   
                        if (!empty($etBudget)) { //etBudgetNew
                            if (!$etBudget->save()) {
                                return $this->responseError("Unable to save record");
                            }
                       }
                    }
                }

                }
            }
                            } 
            return $this->responseSuccess("Expense Trackers Saved Successfully");
        }

        public function responseError($message)
        {
            $this->statusCode = "400";
            $this->status = "error";
            $this->message = $message;
            $this->data = [];
            return false;
        }

    public function responseSuccess($message, $data = [])
        {
            $this->statusCode = "200";
            $this->status = "success";
            $this->message = $message;
            $this->data = $data;
            return true;
        }
  }
