<?php

    namespace App\Console\Commands\Actions\Settings\ExpenseSetup;

    use Illuminate\Console\Command;
    use App\Console\Commands\Action;
    use App\Models\Tenants\ExpenseLimitRange;
    use Illuminate\Support\Facades\Validator;

    class ExpenseApprovalAction extends Action
    {
        /**
         * The name and signature of the console command.
         *
         * @var string
         */
        protected $signature = 'action:expenseApproval {flowId} {parentId} {input}';

        /**
         * The console command description.
         *
         * @var string
         */
        protected $description = 'Expense Approval';

        /**
         * Execute the console command.
         */
        public function apply()
        {
            $success = [];
            $error = [];

            if ($this->input['method'] == 'get') {
                $page = $this->input['page'] ?? 1;
                $per_page = !empty($this->input['per_page']) ? $this->input['per_page']: ($this->hugeData ? 100000 : 10);

                $offset = ($page - 1) * $per_page;
                $expenseRangeData = $this->tenantDB()->table('expense_limit_ranges')
                ->selectRaw('id, soc_id, limit_name, CONVERT(limit_from, UNSIGNED) AS limit_from, CONVERT(limit_to, UNSIGNED) AS limit_to, approvers, warning, status')
                ->where('soc_id', $this->input['company_id'])
                                        ->where('status', 1)
                                        ->offset($offset)
                                        ->limit($per_page)
                                        ->get();

                $totalCount =  $this->tenantDB()->table('expense_limit_ranges')
                ->where('soc_id', $this->input['company_id'])
                ->where('status', 1)
                ->count();


                $message = "Expense range listed successfully";
                $this->meta['pagination']['total'] = $totalCount;

                return $this->responseSuccess($message, $expenseRangeData);
            }
                if (!empty($success)) {
                    return $this->responseSuccess(array_column($success, "message"));
                }

                if (!empty($error)) {
                    return $this->responseError(array_column($error, "message"));
                }
        }

        public function responseError($message)
        {
            $this->statusCode = "400";
            $this->status = "error";
            $this->message = $message;
            $this->data = [];
            return false;
        }

        public function responseSuccess($message, $data=[])
        {
            $this->statusCode = "200";
            $this->status = "success";
            $this->message = $message;
            $this->data = $data;
            return true;
        }

    }
