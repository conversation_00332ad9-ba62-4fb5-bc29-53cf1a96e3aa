<?php

namespace App\Console\Commands\Actions\Settings\ExpenseSetup;

use Illuminate\Console\Command;
use App\Console\Commands\Action;

class ExpenseAddAccountDataSource extends Action
{
   /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'datasource:ExpenseAddAccountDataSource {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Expense Add Account Workflow';

    /**
     * Execute the console command.
     */
    public function apply()
    {
        $request = $this->input;

        if(!empty($request)) {
            $et_type_name = $request['et_type_name'];
            $et_ledger_account_id = $request['et_ledger_account_id'];
            $et_status = $request['et_check'];

            if(isset($et_status) && !empty($et_status)) {
                $et_status = '1';
            } else {
                $et_status = '0';
            }
            
            $data = [
                'et_type_name' => $et_type_name,
                'et_ledger_account_id' => $et_ledger_account_id,
                'et_annual_budget' => '0.000',
                'added_on' => date('Y-m-d H:i:s'),
                'updated_on' => date('Y-m-d H:i:s'),
                'status' => $et_status,
            ];

            $obj = $this->tenantDB()->table('chsone_expense_tracker')->insert($data);

            if($obj = true){
                return response()->json(['message' => 'Expense data inserted successfully.'], 200);
            } else {
                return response()->json(['message' => 'Expense data not inserted.'], 400);
            }
        }
    }
}
