<?php

namespace App\Console\Commands\Actions\Settings\ExpenseSetup;

use Illuminate\Console\Command;
use App\Console\Commands\Action;

class getDataExpenseAccountsBudgetsDataSource extends Action
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'datasource:getDataExpenseAccountsBudgets {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Expense Accounts Budgets Get Data';

    /**
     * Execute the console command.
     */
    public function apply()
    {
        $obj = $this->tenantDB()->table('soc_account_financial_year_master')
        ->selectRaw('account_closing_id as id, YEAR(fy_start_date) as start_year, YEAR(fy_end_date) as end_year, closed, confirmed, CONCAT(YEAR(fy_start_date), "-", YEAR(fy_end_date)) as year_range')
        ->orderBy('account_closing_id', 'asc');

        $result = $obj->get();
        $this->data = $result->toArray();
    }
}
