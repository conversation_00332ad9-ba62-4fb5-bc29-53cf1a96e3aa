<?php

namespace App\Console\Commands\Actions\Settings\ExpenseSetup;

use Illuminate\Console\Command;
use App\Console\Commands\Action;
use App\Models\Tenants\ChsoneSocietiesBuildingMaster;
use App\Models\Tenants\ChsoneUnitsMaster;
use App\Models\Tenants\ChsoneSocietiesMaster;
use App\Models\Tenants\ExpenseLimitRange;
    use Illuminate\Support\Facades\Validator;


class ExpenseApprovalDeleteDataSource extends Action
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'datasource:expenseApprovalDelete {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Delete Expense Approval';

    public function apply()
    {
        
        /*$validator = Validator::make($this->input, [
            'id' => 'required|numeric|exists:expense_limit_ranges,id'
        ],[
            "id.required" => "Id is required",
            "id.numeric" => "Invalid Id",
            "id.exists" => "No Id found"
        ]);
        
        if ($validator->fails()) {
            return $this->responseError($validator->messages(),400);

        }*/
        $expenseRangeData = ExpenseLimitRange::where('soc_id', $this->input['company_id'])
                                ->where('id', $this->input['id'])->first();
        $expenseRangeData->status = '0';
        if ($expenseRangeData->save()) {
            $message = $expenseRangeData->limit_name." deleted successfully";
            return $this->responseSuccess($message,200);
        }
        $message = "Limit name does not exist";
        return $this->responseError($message,400);
    }

    private function responseError($message, $statusCode, $data = [])
    {
        $this->message = $message;
        $this->status = "error";
        $this->statusCode = $statusCode;
        $this->data = $data;
        return false;
    }

    private function responseSuccess($message, $statusCode, $data = [])
    {
        $this->message = $message;
        $this->status = "success";
        $this->statusCode = $statusCode;
        $this->data = $data;
        return true;
    }
    
}
