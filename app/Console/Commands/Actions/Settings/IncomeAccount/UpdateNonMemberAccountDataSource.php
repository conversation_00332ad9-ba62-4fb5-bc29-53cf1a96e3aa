<?php

namespace App\Console\Commands\Actions\Settings\IncomeAccount;

use Illuminate\Console\Command;
use App\Console\Commands\Action;
use App\Models\Tenants\ChsoneGrpLedgerTree;
use App\Models\Tenants\IncomeAccount;
use App\Models\Tenants\SocAccountFinancialYearMaster;
// use Illuminate\Support\Facades\Config;
use Config;

class UpdateNonMemberAccountDataSource extends Action
{
    protected $constants;

    public function __construct() {
        parent::__construct();
        $this->constants = Config::get("constants");
    }

    protected $signature = 'datasource:updateNonMemberAccount {flowId} {parentId} {input}';

    protected $description = 'Update Non Member Account';

    public function apply()
    {
        $data['postdata'] = $this->input;

        // get all member income accounts
        $arrmemberincomeaccount = $this->arrmemberincomeaccount($this->input['company_id']);
        
        // get all non member income accounts
        $arrnonmemberIncomeAccountLedger = $this->arrnonmemberIncomeAccountLedger($this->input['company_id']);

        $data['memberIncomeAccounts'] = $arrmemberincomeaccount;
        $data['nonmemberIncomeAccounts'] = $arrnonmemberIncomeAccountLedger;

        $arrreturnsuccess = array();
        $arrreturnsuccess['success'] = 1;

        //add member income account
        if (!empty($data['postdata']['member'])) {
            $arrmemreturnsuccess = $this->_memberincomeaccountSettings($data);
            if (!isset($arrmemreturnsuccess)) {
                $arrreturnsuccess['success'] = 1;
            }
        }

        //add non member account 
        if (!empty($data['postdata']['nonmember'])) {
            $membersuccessflag = $arrreturnsuccess['success'];
            $arrnonmemberreturnsuccess = $this->_nonmemberincomeaccountSettings($data);
            if ($arrnonmemberreturnsuccess['success'] == 1) {
                $arrreturnsuccess['success'] = $membersuccessflag;
            } else {
                $arrreturnsuccess['success'] = 0;
            }
        }

        if ((isset($arrmemreturnsuccess) || isset($arrnonmemberreturnsuccess)) && $arrreturnsuccess['success'] == 1) {
            $this->statusCode = 200;
            $this->status = 'success';
            $this->message = 'Income Account setting added/updated successfully';
        } else {
            $this->statusCode = 400;
            $this->status = 'error';
            $this->message = 'No changes made in Income Account setting OR Ledger name already exist, Try different Income Account name.';
        }

        // $obj = $this->tenantDB()->table('income_accounts')
        //     ->where('account_id', $this->input['id'])
        //     ->update([
        //         'account_name' => $this->input['account_name'],
        //         'fk_income_ledger_id' => $this->input['fk_income_ledger_id'],
        //         'updated_date' => date('Y-m-d H:i:s'),
        //         'updated_by' => $this->input['updated_by'] ?? 0
        //     ]);

        // if($obj) {
        //     $this->status = 'success';
        //     $this->message = 'Non member account updated successfully';
        //     $this->statusCode = 200;
        // } else {
        //     $this->status = 'error';
        //     $this->message = 'Unable to update non member account';
        //     $this->statusCode = 400;
        // }
    }

    public function arrmemberincomeaccount($soc_id) {
        $memberincomeaccount = $this->constants['memberincomeaccount'];

        $incomeAccounts = IncomeAccount::where('soc_id', $soc_id)->where('account_type', 'member')->get();
        $arrincomeaccount = $incomeAccounts;

        $arrmemberincomeaccount = array();
        $newarrmemberincomeaccount = array();

        if (!empty($arrincomeaccount)) {
            foreach ($arrincomeaccount as $key => $value) {
                if (array_key_exists($value->account_name, $memberincomeaccount)) {
                    $arrmemberincomeaccount['memberincome_account_key'] = $value['account_name'];
                    $arrmemberincomeaccount['memberincome_account_value'] = $memberincomeaccount[$value->account_name];
                    $arrmemberincomeaccount['memberincome_account_bank'] = $value['fk_ledger_id'];
                    $arrmemberincomeaccount['memberincome_account_cash'] = $value['fk_cash_ledger_id'];
                    $arrmemberincomeaccount['display_name'] = $value['display_name'];
                    $arrmemberincomeaccount['fk_income_ledger_id'] = $value['fk_income_ledger_id'];
                } else {
                    $arrmemberincomeaccount['memberincome_account_key'] = $value['account_name'];
                    $arrmemberincomeaccount['memberincome_account_value'] = ltrim(preg_replace('/[A-Z]/', ' $0', $value['account_name'])); //$memberincomeaccount[$value['account_name']];
                    $arrmemberincomeaccount['memberincome_account_bank'] = $value['fk_ledger_id'];
                    $arrmemberincomeaccount['memberincome_account_cash'] = $value['fk_cash_ledger_id'];
                    $arrmemberincomeaccount['display_name'] = $value['display_name'];
                    $arrmemberincomeaccount['fk_income_ledger_id'] = $value['fk_income_ledger_id'];
                }

                $newarrmemberincomeaccount[] = $arrmemberincomeaccount;
            }
        } else {
            $newarrincomeaccount = array();
            foreach ($memberincomeaccount as $key => $value) {
                $newarrincomeaccount[$key] = ['bank' => '', 'cash' => ''];
                $arrmemberincomeaccount = array();
                $arrmemberincomeaccount['memberincome_account_key'] = $key;
                $arrmemberincomeaccount['memberincome_account_value'] = $value;
                $arrmemberincomeaccount['memberincome_account_bank'] = $newarrincomeaccount[$key]['bank'];
                $arrmemberincomeaccount['memberincome_account_cash'] = $newarrincomeaccount[$key]['cash'];
                $newarrmemberincomeaccount[] = $arrmemberincomeaccount;
            }
        }

        return $newarrmemberincomeaccount;
    }

    public function arrnonmemberIncomeAccountLedger($soc_id) {
        $arrincomeaccount = array();
        $incomeAccounts = IncomeAccount::where('soc_id', $soc_id)->where('account_type', 'nonmember')->get();
        if (!empty($incomeAccounts)) {
            foreach ($incomeAccounts as $key=>$value) {
                $arrincomeaccount[$key]['nonmemberincome_account_key'] = $value->account_name;
                $arrincomeaccount[$key]['nonmemberincome_account_value'] = ltrim(preg_replace('/[A-Z]/', ' $0', $value->account_name)); //$memberincomeaccount[$value['account_name']];
                $arrincomeaccount[$key]['nonmemberincome_account_bank'] = $value->fk_ledger_id;
                $arrincomeaccount[$key]['nonmemberincome_account_cash'] = $value->fk_cash_ledger_id;
                $arrincomeaccount[$key]['nondisplay_name'] = $value->display_name;
                $arrincomeaccount[$key]['non_fk_income_ledger_id'] = $value->fk_income_ledger_id;
            }
        }

        return $arrincomeaccount;
    }

    public function _memberincomeaccountSettings($data) {
        if (!empty($data['postdata']['member'])) {
            $arrfinal = array();
            foreach($data['postdata']['member'] as $key => $value) {
                $currentLedgerValue = null;

                foreach ($data['memberIncomeAccounts'] as $currentLedgerData) {
                    if (in_array($key, $currentLedgerData)) {
                        $currentLedgerValue = $currentLedgerData['fk_income_ledger_id'];
                    }
                }

                if (!empty($value) && !empty($currentLedgerValue) && $currentLedgerValue != $value) {
                    $objincomeaccount = IncomeAccount::where('soc_id', $data['postdata']['company_id'])
                    ->where('account_name', $key)
                    ->where('account_type', 'member')
                    ->first();

                    $objincomeaccount->account_name = $key;
                    $objincomeaccount->fk_income_ledger_id = $value;
                    $objincomeaccount->updated_by = $data['postdata']['user_id'];
                    $objincomeaccount->updated_date = date('Y-m-d H:i:s');
                    
                    if ($objincomeaccount->save()) {
                        $arrfinal['success'] = 1;
                    } else {
                        $arrfinal['success'] = 0;
                    }
                } else {
                    $arrfinal['success'] = 0;
                }
            }
            return $arrfinal;
        }
    }

    public function _nonmemberincomeaccountSettings($data) {
        $arrfinal = array();
        // dd($data['nonmemberIncomeAccounts']);
        if (!empty($data['postdata']['nonmember'])) {
            foreach ($data['postdata']['nonmember'] as $key => $value) {
                $currentLedgerValue = null;
                foreach ($data['nonmemberIncomeAccounts'] as $currentLedgerData) {
                    if (in_array($key, $currentLedgerData)) {
                        $currentLedgerValue = $currentLedgerData['non_fk_income_ledger_id'];
                    }
                }
                if (!empty($value) && !empty($currentLedgerValue) && $currentLedgerValue != $value) {
                    $objincomeaccount = IncomeAccount::where('soc_id', $data['postdata']['company_id'])
                    ->where('account_name', $key)
                    ->where('account_type', 'nonmember')
                    ->first();

                    $objincomeaccount->account_name = $key;
                    $objincomeaccount->fk_income_ledger_id = $value;
                    $objincomeaccount->updated_by = $data['postdata']['user_id'];
                    $objincomeaccount->updated_date = date('Y-m-d H:i:s');

                    if ($objincomeaccount->save()) {
                        $arrfinal['success'] = 1;
                    } else {
                        $arrfinal['success'] = 0;
                    }
                } else {
                    $arrfinal['success'] = 0;
                }
            }
        }

        //Adding data not created ledger.
        if (!empty($data['postdata']['newnonmemberincome'][0])) {
            foreach ($data['postdata']['newnonmemberincome'] as $key => $value) {
                if(isset($data['postdata']['newnonmemberledgers'][$key]) && !empty($data['postdata']['newnonmemberledgers'][$key])) {
                    $groupledger = ChsoneGrpLedgerTree::where('ledger_account_id', $data['postdata']['newnonmemberledgers'][$key])->first();
                    if (empty($groupledger)) {
                        $this->statusCode = 400;
                        $this->status = 'error';
                        $this->message = 'Ledger not found';
                    } else {
                        $incomeaccount = new IncomeAccount();
                        $incomeaccount->account_id = '';
                        $incomeaccount->account_name = $value;
                        $incomeaccount->soc_id = $data['postdata']['company_id'];
                        $incomeaccount->fk_income_ledger_id = $data['postdata']['newnonmemberledgers'][$key];

                        $incomeaccount->account_type = 'nonmember';
                        $incomeaccount->created_by = $data['postdata']['user_id'];
                        $incomeaccount->created_date = date('Y-m-d H:i:s');
                        $incomeaccount->updated_by = $data['postdata']['user_id'];
                        $incomeaccount->updated_date = date('Y-m-d H:i:s');

                        if ($incomeaccount->save()) {

                            // insert ledger entry into chsone_ledger_transaction
                            $ledgerTransaction = $this->ledgerTransaction($incomeaccount);

                            $arrfinal['success'] = 1;
                        } else {
                            $arrfinal['success'] = 0;
                        }
                    }
                } else {
                    $arrfinal['success'] = 0;
                }
            }
        }

        return $arrfinal;
    }

    public function ledgerTransaction($incomeaccount) 
    {
        // fetch parent_id from chsone_grp_ledger_tree whose ledger_account_name is 'Non-Member Contribution'
        $ledgerData = ChsoneGrpLedgerTree::where('ledger_account_name', 'Non-Member Contribution')->first();
        $ledgerData = json_decode(json_encode($ledgerData));
        $parent_id = $ledgerData->ledger_account_id;
        $nature_of_account = $ledgerData->nature_of_account;
        $report_head = $ledgerData->report_head;
        $context = $ledgerData->context;

        // fetch fy_start_date from soc_account_financial_year_master model where soc_id is $incomeaccount->soc_id
        $soc_account_financial_year_master = $this->tenantDB()->table('soc_account_financial_year_master')
            ->where('soc_id', $incomeaccount->soc_id)
            ->first();
        $ledger_start_date = $soc_account_financial_year_master->fy_start_date;

        $ledger = new ChsoneGrpLedgerTree();
        $ledger->soc_id = $incomeaccount->soc_id;
        $ledger->ledger_account_name = $incomeaccount->account_name;
        $ledger->nature_of_account = 'cr';
        $ledger->parent_id = $parent_id;
        $ledger->report_head = $report_head;
        $ledger->context_ref_id = $incomeaccount->account_id;
        $ledger->context = $context;
        $ledger->ledger_start_date = $ledger_start_date;
        $ledger->added_on = date('Y-m-d H:i:s');
        $ledger->status = 1;
        $ledger->created_by = $incomeaccount->created_by;
        $ledger->entity_type = 'ledger';
        $ledger->behaviour = 'income';
        $ledger->defined_by = isset($incomeaccount->created_by) ? 'user' : 'system';

        if ($ledger->save()) {
            return true;
        } else {
            return false;
        }
    }
}