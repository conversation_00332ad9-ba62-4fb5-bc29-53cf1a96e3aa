<?php

namespace App\Console\Commands\Actions\Settings\IncomeAccount;

use Illuminate\Console\Command;
use App\Console\Commands\Action;
use App\Models\Tenants\ChsoneGrpLedgerTree;
use App\Models\Tenants\IncomeAccount;

class AddnonmemberaccountDataSource extends Action
{
    protected $signature = 'datasource:addNonMemberAccount {flowId} {parentId} {input}';

    protected $description = 'Add Non Member Account';

    public function apply()
    {
        $success = [];
        $error = [];

        $params = $this->input['params'] ?? []; // Default to an empty array if 'params' is not set
        $currentTab = $this->input['current_tab'] ?? 'Member Income Account';
        if($currentTab === 'Member Income Account') {
            $accountType = 'member';
        } else {
            $accountType = 'nonmember';

        }
        

        // Iterate over each input record
        foreach ($params as $input) {
            // Ensure 'input' is an array and contains the required fields
            if (
                !is_array($input) ||
                !isset($input['account_name']) ||
                !isset($input['fk_income_ledger_id']) ||
                !isset($input['_action'])
            ) {
                continue; // Skip invalid or incomplete records
            }

            $incomeAccount = new IncomeAccount();
            if ($input['_action'] === 'edit') {
                // Update existing record
                $incomeAccountData = $incomeAccount->where('account_id', $input['id'])->first();
                $incomeAccountDataExists = $incomeAccount
                    ->where('account_id', '!=', $input['id'])
                    ->where('account_name', $input['account_name'])
                    ->first();

                if ($incomeAccountDataExists) {
                    $message = $input['account_name'] . " already exists with a different ID.";
                    $this->meta['errors'] = ['account_name' => $message];
                } elseif ($incomeAccountData) {
                    // Only update if data has changed
                    if (
                        $input['account_name'] != $incomeAccountData['account_name'] ||
                        $input['fk_income_ledger_id'] != $incomeAccountData['fk_income_ledger_id'] ||
                        $input['display_name'] != $incomeAccountData['ledger_account_name']
                    ) {
                        $incomeAccountData->soc_id = $this->input['company_id'];
                        $incomeAccountData->account_name = $input['account_name'];
                        $incomeAccountData->fk_income_ledger_id = $input['fk_income_ledger_id'];
                        $incomeAccountData->fk_ledger_id = $input['fk_income_ledger_id'];
                        $incomeAccountData->display_name = $input['ledger_account_name'];
                        $incomeAccountData->account_type = $accountType;
                        $incomeAccountData->updated_date = date('Y-m-d H:i:s');
                        $incomeAccountData->updated_by = $this->input['user_id'] ?? 0;
                        $incomeAccountData->created_by = $this->input['user_id'] ?? 0;

                        if ($incomeAccountData->save()) {
                            $insertGrpLedgerEntry = $this->insertGrpLedgerEntry($incomeAccountData);

                            if (!$insertGrpLedgerEntry) {
                                $this->status = 'error';
                                $this->message = 'Unable to add non-member account in chsone_grp_ledger_tree table';
                                $this->statusCode = 400;
                            }
                            $message = $input['account_name'] . " changed successfully";
                            array_push($success, ["success" => true, "message" => $message]);
                        }
                    }
                }
            } elseif($input['_action'] === 'add') {
                $incomeAccount->soc_id = $this->input['company_id'];
                $incomeAccount->account_name = $input['account_name'];
                $incomeAccount->fk_income_ledger_id = $input['fk_income_ledger_id'];
                $incomeAccount->fk_ledger_id = $input['fk_income_ledger_id'];
                $incomeAccount->display_name = $input['account_name'];
                $incomeAccount->account_type = $accountType;
                $incomeAccount->created_date = date('Y-m-d H:i:s');
                $incomeAccount->updated_date = date('Y-m-d H:i:s');
                $incomeAccount->updated_by = $this->input['user_id'] ?? 0;

                if ($incomeAccount->save()) {
                    $insertGrpLedgerEntry = $this->insertGrpLedgerEntry($incomeAccount);

                    if (!$insertGrpLedgerEntry) {
                        $this->status = 'error';
                        $this->message = 'Unable to add non-member account in chsone_grp_ledger_tree table';
                        $this->statusCode = 400;
                    } else {
                        $message = $input['account_name'] . " added successfully";
                        array_push($success, ["success" => true, "message" => $message]);
                    }
                } else {
                    /*$message = "Failed to add new record for account_name: " . $input['account_name'];
                    array_push($error, ["success" => false, "message" => $message]);*/
                }
            }
        }

        if (!empty($success)) {
            return $this->responseSuccess(array_column($success, "message"));
        }

        if (!empty($error)) {
            return $this->responseError(array_column($error, "message"));
        }

        return $this->responseError(["No records were processed."]);
    }




    public function insertGrpLedgerEntry($obj)
    {
        // firstly get the parent_id from chsone_grp_ledger_tree table whose soc_id is equal to company_id and ledger_account_name is like %$obj->account_type% and entity_type is equal to 'group'
        $parentGrpLedgerTree = $this->tenantDB()->table('chsone_grp_ledger_tree')
            ->where('soc_id', $this->input['company_id'])
            ->where('ledger_account_name', 'like', 'Non-Member Contribution')
            ->where('entity_type', 'group')
            ->first();
        $parent_id = $parentGrpLedgerTree->ledger_account_id;
        $context = $parentGrpLedgerTree->context;
        $behaviour = $parentGrpLedgerTree->behaviour;

        // get first fy_start_date from soc_account_financial_year_master table
        $fyStartDate = $this->tenantDB()->table('soc_account_financial_year_master')
            ->where('soc_id', $this->input['company_id'])
            ->orderBy('fy_start_date', 'asc')
            ->first();
        $fyStartDate = $fyStartDate->fy_start_date;
        // insert the data into chsone_grp_ledger_tree table
        $grpLedgerTree = new ChsoneGrpLedgerTree();
        $grpLedgerTree->soc_id = $this->input['company_id'];
        $grpLedgerTree->ledger_account_name = $obj['account_name'];
        $grpLedgerTree->nature_of_account = 'cr';
        $grpLedgerTree->parent_id = $parent_id;
        $grpLedgerTree->report_head = 'Profit & Loss';
        $grpLedgerTree->context_ref_id = $obj->account_id ?? 0;
        $grpLedgerTree->context = $context;
        $grpLedgerTree->ledger_start_date = $fyStartDate;
        $grpLedgerTree->added_on = date('Y-m-d H:i:s');
        $grpLedgerTree->status = 1;
        $grpLedgerTree->created_by = $this->input['user_id'] ?? 0;
        $grpLedgerTree->entity_type = 'ledger';
        $grpLedgerTree->behaviour = $behaviour;
        $grpLedgerTree->defined_by = isset($this->input['user_id']) ? 'user' : 'system';
        $grpLedgerTree->save();

        if ($grpLedgerTree) {
            return $grpLedgerTree;
        } else {
            return false;
        }
    }

    public function responseError($message)
    {
        $this->statusCode = "400";
        $this->status = "error";
        $this->message = $message;
        $this->data = [];
        return false;
    }

    public function responseSuccess($message, $data = [])
    {
        $this->statusCode = "200";
        $this->status = "success";
        $this->message = $message;
        $this->data = $data;
        return true;
    }
}
