<?php

namespace App\Console\Commands\Actions\Settings;

use Illuminate\Console\Command;
use App\Console\Commands\Action;
use App\Models\Tenants\GateSettings;

class updateGateSettingsDataSource extends Action
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'datasource:updateGateSettings {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Update Gate Settings Data Source';

    /**
     * Execute the console command.
     */
    public function apply()
    {
        $obj = array();
        $gateId = $this->input['gate_id'];
        $data = json_decode($this->input['data']);

        foreach ($data as $item) {
            $id = $item->id;
            $settingsName = $item->settings_name;
            $options = $item->options;
            $type = $item->type;
    
            // Update the settings based on conditions
            $obj = GateSettings::where('gate_id', $gateId)
                ->where('id', $id)
                ->update([
                    'settings_name' => $settingsName,
                    'options' => $options,
                    'type' => $type,
                ]);
        }

        if ($obj) {
            $message = "Error in updating gate settings";
            return $this->responseError($message, 400,);
        } else {
            $message = "Gate settings updated successfully";
            return $this->responseSuccess($message, 200);
        }

    }

    private function responseError($message, $statusCode, $data = [])
    {
        $this->message = $message;
        $this->status = "error";
        $this->statusCode = $statusCode;
        $this->data = $data;
        return false;
    }

    private function responseSuccess($message, $statusCode, $data = [])
    {
        $this->message = $message;
        $this->status = "success";
        $this->statusCode = $statusCode;
        $this->data = $data;
        return true;
    }
}
