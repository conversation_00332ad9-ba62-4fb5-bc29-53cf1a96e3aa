<?php

namespace App\Console\Commands\Actions\Settings\ComplexSetup;

use Illuminate\Console\Command;
use App\Console\Commands\Action;
use App\Models\Tenants\ChsoneSocietiesBuildingMaster;
use App\Models\Tenants\ChsoneUnitsMaster;


class DeleteUnitsAction extends Action
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'datasource:deleteUnits {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Delete Units';

    public function apply()
    {
        $unitId = $this->input['id'];
        $cancelDate = $this->input['cancel_date'];
        $cancellationReason = $this->input['cancellation_reason'];
        $response = $this->removeUnitAllocation($unitId, $cancelDate, $cancellationReason);
        if ($response) {
            $facilityBookings = $this->tenantDB()->table("facility_bookings")
                 ->where("fk_unit_id", $unitId)
                 ->where("booking_period_end_date", ">", "'".$this->getCurrentDate('database')."'")
                 ->get()->toArray();
            
            if (!empty($facilityBookings)) {
                $this->message = "This unit is booked as facility. Thus cannot be deleted";
                return $this->responseError($this->message, 400);
            }
            $units = $this->tenantDB()->table("chsone_units_master")
                          ->where("unit_id", $unitId)
                          ->first();
            if (empty($units)) {
                $this->message = "No such Unit found";
                return $this->responseError($this->message, 400);
            } else {
                $unitsData = $this->responseSuccess($this->message, 200, $units);
                if ($unitsData && !empty($units)) {
                    $parkingAllotment = $this->tenantDB()->table("chsone_parking_allotment_detail")
                                             ->where("status", "1")
                                             ->where("fk_unit_id", $units->unit_id);
                    $parkingAllotmentCnt = $parkingAllotment->count();
                    $parkingAllotmentArr = $parkingAllotment->get()->toArray();
                    if (!empty($parkingAllotmentArr)) {
                        $this->message = "If revoked then ". $parkingAllotmentCnt." parking allottments also be cancelled";
                    }
                    $memberDetail = $this->tenantDB()->table("chsone_members_master")
                                         ->where("fk_unit_id", $units->unit_id)
                                         ->where("status", "1")
                                         ->get()->toArray();
                    $memberIds = array_column($memberDetail, "id");

                    $vehicleCollection = $this->tenantDB()->table("chsone_member_vehicle_detail")
                                         ->whereIn("fk_member_id", $memberIds)
                                         ->where("status", "1");
                    $vehicleCollectionCnt = $vehicleCollection->count();
                    $vehicleCollectionArr = $vehicleCollection->get()->toArray();

                    $memberDetailCnt = count($memberDetail);

                    if ($memberDetailCnt == 0) {
                        $this->message.= " Unit will be discarded";
                    } else {
                        $this->message.= "If revoked then, registration to ". $memberDetailCnt. " member will be cancelled";
                    }

                    if (!empty($vehicleCollectionCnt) && $vehicleCollectionCnt > 0) {
                        $this->message.= "If revoked then, registration to ". $vehicleCollectionCnt. " member will be cancelled";
                    }
                    return $this->responseSuccess($this->message, 200);
                }
            }
        }
    }

    private function responseError($message, $statusCode, $data = [])
    {
        $this->message = $message;
        $this->status = "error";
        $this->statusCode = $statusCode;
        $this->data = $data;
        return false;
    }

    private function responseSuccess($message, $statusCode, $data = [])
    {
        $this->message = $message;
        $this->status = "success";
        $this->statusCode = $statusCode;
        $this->data = $data;
        return true;
    }

    public function removeUnitAllocation($unitId, $cancelDate, $cancellationReason)
    {
        //Remove Unit Allocation
        $units = $this->tenantDB()->table("chsone_units_master")
                      ->where("unit_id", $unitId)
                      ->first();
        $unitAllocation = $this->tenantDB()->table("chsone_units_master")
                               ->where("unit_id", $unitId);
        $update = $unitAllocation->update([
                                "status" => 0,
                                "cancel_date" => $cancelDate,
                                "cancellation_reason" => $cancellationReason,
                                "updated_date" => $this->getCurrentDate('database') . ' ' . date('H:i:s')
                            ]);
        $getUnitAllocation = $unitAllocation->first();
        $flatNumber = $getUnitAllocation->unit_flat_number;
        $flatType = $getUnitAllocation->unit_type;
        $flatUnitId = $getUnitAllocation->unit_id;

        //Remove Parking Allocation
        $parkingAllotment = $this->tenantDB()->table("chsone_parking_allotment_detail")
                                 ->where("fk_unit_id", $flatUnitId)
                                 ->get()->toArray();
        if (!empty($parkingAllotment)) {
            $parkingUnitIds = array_column($parkingAllotment, "fk_parking_unit_id");
            $updateUnits = $this->tenantDB()->table("chsone_units_master")
                                ->whereIn("unit_id", $parkingUnitIds)
                                ->update([
                                    "is_allocated" => 0,
                                    "is_occupied" => 0,
                                    "occupied_by" => "vacant",
                                ]);

        }

        //Remove Member with the Unit that is to be cancelled
        $members = $this->tenantDB()->table("chsone_members_master")
                        ->where("fk_unit_id", $flatUnitId)
                        ->get()->toArray();
        if (!empty($members)) {
            $memberIds = array_column($members, "id");
            $updateMembers = $this->tenantDB()->table("chsone_members_master")
                                  ->whereIn("id", $memberIds)
                                  ->update([
                                    "status" => 0,
                                    "cancel_date" => $this->getDatabaseDate($cancelDate),
                                  ]);
            //cancel Member vehicles allocation
            $vehiclesCollection = $this->tenantDB()->table("chsone_member_vehicle_detail")
                                        ->where('status', "1")
                                        ->whereIn("fk_member_id", $memberIds)
                                        ->get()->toArray();
            if (!empty($vehiclesCollection)) {
                $vehicles = array_column($vehiclesCollection, "vehicle_registration_number");
                $vehiclesNums = implode(", ", $vehicles);
                $removeVehicles = $this->tenantDB()->table("chsone_member_vehicle_detail")
                                        ->where('status', "1")
                                        ->whereIn("fk_member_id", $memberIds)
                                        ->delete();
            }
        }
        $this->message = "Unit allocation for ". $flatNumber. " (".$flatType.") is successfully revoked.";
        if (isset($vehiclesNums) && !empty($vehiclesNums)) {
            $this->message.= "Vehicles with regitration number ".$vehiclesNums." are also cancelled";
        }
        return $this->responseSuccess($this->message, 200);
    }
}
