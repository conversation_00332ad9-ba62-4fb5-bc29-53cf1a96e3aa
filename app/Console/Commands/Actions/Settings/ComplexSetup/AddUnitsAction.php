<?php

namespace App\Console\Commands\Actions\Settings\ComplexSetup;

use Illuminate\Console\Command;
use App\Console\Commands\Action;
use App\Models\Tenants\ChsoneSocietiesBuildingMaster;
use App\Models\Tenants\ChsoneUnitsMaster;
use App\Models\Tenants\ChsoneSocietyUnitsTpl;
use App\Models\Tenants\ChsoneGrpLedgerTree;
use App\Models\Tenants\ChsoneLedgerTransaction;
use App\Models\Tenants\ChsoneMembersMaster;
use App\Models\Tenants\ChsoneParkingAllotmentDetail;
use App\Models\Tenants\FacilityBookingAccount;
use Config;

class AddUnitsAction extends Action
{
    protected $constants;

    public function __construct() {
        parent::__construct();
        $this->constants = Config::get("constants");
    }
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'datasource:addUnits {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Add Units';

    public function apply()
    {

        $this->input['unit_area'] = $this->input['area'] ?? null;
        $this->input['unit_open_area'] = $this->input['open_space_area'] ?? null;

        $unit_id = $this->input['id'] ?? null;
        $companyId = $this->input['company_id'];
        $socBldgName = $this->input['soc_building_name'];
        $categoryName = $this->input['fk_unit_category_id'];
        $floor = $this->input['soc_building_floor'];
        $identifiedAs = $this->input['unit_flat_number'];
        $occupancyType = $this->input['occupancy_type'];
        $chargingType = $this->input['charge_type'];
        $unitWEF = $this->input['unit_wef'];
        $area = $this->input['unit_area'] ?? null;
        $openArea = $this->input['unit_open_area'] ?? null;
        $waterInlets = $this->input['unit_total_water_inlets'] ?? null;
        $chargeable = $this->input['chargeable'] ?? 0;
        $lateCharge = $this->input['income_late_charge_id'] ?? null;
        $extraAttr = isset($this->input['extra_attributes']) ? $this->input['extra_attributes'] : null;
        $status = $this->input['status'] ?? 1;
        $currentDate = $this->getCurrentDate('database');

        // check unit is already exists
        $checkUnitExists = $this->tenantDB()->table("chsone_units_master")
                        ->where("soc_building_id", $socBldgName)
                        ->where("unit_flat_number", $identifiedAs)
                        ->get()->toArray();

        // fetch society building name
        $socBldg = $this->tenantDB()->table("chsone_societies_building_master")
                        ->where("soc_building_id", $socBldgName)
                        ->select("soc_building_name")
                        ->first();
        $socBuildingName = $socBldg->soc_building_name;

        // get unit category details
        $unitCat = $this->tenantDB()->table("chsone_society_units_tpl")
                                ->where("soc_units_type_id", $categoryName)
                                ->first();
        $unitCatId = $unitCat->soc_units_type_id;
        $unitCatType = $unitCat->soc_unit_type;
        $unitType = $unitCat->type;

        if (!empty($checkUnitExists)) {
            $this->statusCode = 400;
            $this->status = "error";
            $this->message = "Unit already exists";
        } else {
            $unitAdd = new ChsoneUnitsMaster;
            $unitAdd->created_date = $currentDate;
            $unitAdd->soc_id = $companyId;
            $unitAdd->fk_unit_category_id = $unitCatId;
            $unitAdd->unit_category = $unitType;
            $unitAdd->unit_type = $unitCatType;
            $unitAdd->soc_building_id = $socBldgName;
            $unitAdd->soc_building_name = $socBuildingName;
            $unitAdd->soc_building_floor = $floor;
            $unitAdd->unit_flat_number = $identifiedAs;
            $unitAdd->unit_area = $area;
            $unitAdd->unit_open_area = $openArea;
            $unitAdd->unit_total_water_inlets = $waterInlets;
            $unitAdd->occupancy_type = $occupancyType;
            $unitAdd->income_late_charge_id = $lateCharge;
            $unitAdd->chargeable = $chargeable;
            $unitAdd->charge_type = $chargingType;
            $unitAdd->extra_attributes = $extraAttr;
            $unitAdd->status = $status;
            $unitAdd->created_by = ($this->input['user_id']) ? $this->input['user_id'] : 0;
            $unitAdd->updated_by = ($this->input['user_id']) ? $this->input['user_id'] : 0;
            $unitAdd->cancel_date = '0000-00-00';
            if ($occupancyType == "common") {
                $unitAdd->occupied_by = "guest";
            } elseif($occupancyType == "reserved") {
                $unitAdd->occupied_by = "vacant";
            }
            $unitAdd->updated_date = $currentDate. " ". date("H:i:s");
            $unitAdd->effective_date = $this->getDatabaseDate($unitWEF);

            // fetch ledger id if unit category is not parking
            $ledgerName = [];
            if (strtolower($unitCatType) != "parking") {
                if (!empty($socBuildingName)) {
                    $ledgerName[] = "BLDG#".$socBuildingName;
                }
                if (!empty($identifiedAs)) {
                    $ledgerName[] = $identifiedAs;
                }
                $ledgerName = strtoupper(implode("-", $ledgerName));
                if ($occupancyType != "common") {
                    $parentId = $this->getGroupId($this->constants['ACCOUNT_RECEIVABLE_GROUP'], true);
                } else {
                    $parentId = $this->getGroupId($this->constants['COMMON_AMENITY'], true);
                }
                if ($parentId && $parentId->ledger_account_id) {
                    $ledgerId = $this->dataManipulation($ledgerName, $this->constants['ENTITY_TYPE_LEDGER'], "", $parentId->ledger_account_id, "", "", "");
                    $ledgerId = str_replace("DUP", "", $ledgerId);
                }
                if(isset($ledgerId) && !empty($ledgerId)) {
                    $unitAdd->ledger_account_id = $ledgerId;
                }
            }
            // save unit
            $unitAdd->save();
            if (!$unitAdd->Save()) {
                $this->statusCode = 400;
                $this->status = "error";
                $this->message = "Error while adding unit";
            } else {
                $this->updateParking($identifiedAs,$unitAdd->unit_id);
                if (strtolower($occupancyType) == 'common') {
                    $facilityAccounts = new FacilityBookingAccount();
                    $facilityAccounts->soc_id = $this->input['company_id'];
                    $facilityAccounts->fk_unit_id = $unitAdd->unit_id;
                    if(isset($ledgerId) && !empty($ledgerId)) {
                        $facilityAccounts->fk_income_ledger_id = $ledgerId;
                    }
                    $facilityAccounts->account_name = 'Rentals';
                    $facilityAccounts->account_type = 'member';
                    // $facilityAccounts->fk_ledger_id = $this->getBankledger('bank');
                    // $facilityAccounts->fk_cash_ledger_id = $this->getBankledger('cash');
                    $facilityAccounts->created_date = $this->getCurrentDate('database');
                    $facilityAccounts->updated_date = $this->getCurrentDate('database');
                    $facilityAccounts->created_by = $this->input['user_id'];
                    $facilityAccounts->updated_by = $this->input['user_id'];
                    if($facilityAccounts->save())
                    {
                        $this->statusCode = 200;
                        $this->status = "success";
                        $this->message = "Unit added successfully";
                        // $this->data = $unitAdd->toArray();
                    }
                }

                // pass unit data to the vizlog api
                // $send_sync = $this->sendUnitsData(array('unitIds' => $unitAdd->unit_id, 'soc_id' => $this->input['company_id']));

                $this->statusCode = 200;
                $this->status = "success";
                $this->message = "Unit added successfully";
                // $this->data = $unitAdd->toArray();
            }
        }
    }

    public function getGroupId($grpname, $cntxt = false, $soc_id = '')
    {
        if (empty($soc_id)) {
            $soc_id = $this->input['company_id'];
        }

        if ($cntxt) {
            $parent = ChsoneGrpLedgerTree::where("context", strtolower($grpname))->where("soc_id", $soc_id)->first();
        } else {
            $parent = ChsoneGrpLedgerTree::where("ledger_account_name", strtolower($grpname))->where("soc_id", $soc_id)->first();
        }
        if ($parent) {
            return $parent;
        } else {
            return false;
        }
    }

    public function dataManipulation($name, $entity_type =ENTITY_TYPE_GROUP , $grp_ledg_id = "", $parent_group = 0, $behaviour = "", $ledg_id = '', $opening_balance = 0, $update_led_id = '', $ledger_start_date = '', $ledger_type = '', $context = '', $txn_id = 0, $soc_id = 0, $is_reco = 0)
    {
        $config = Config::get("constants");
        if(empty($soc_id)) {
            $soc_id = $this->input['company_id'];
        }

        $dup = $this->_checkGroupNameDuplication($parent_group, $name, $ledg_id, $update_led_id);
        $arrFYDetail = $this->getCurrentFinancialYear(array('soc_id' => $soc_id));
        $arrFYDetail = json_decode(json_encode($arrFYDetail), true);
        $ledger_start_date = $arrFYDetail['fy_start_date'];
        if ($dup == 0) {
            $behaviour = trim(strtolower($behaviour));

            $grp_ledg_tree = new ChsoneGrpLedgerTree();

            if ($ledg_id) {
                $grp_ledg_tree = $this->getLedger($ledg_id);
            }

            $grp_ledg_tree->entity_type = $entity_type;
            $grp_ledg_tree->soc_id = $soc_id;
            $grp_ledg_tree->ledger_account_name = $name;
            $grp_ledg_tree->ledger_start_date = $ledger_start_date;
            $grp_ledg_tree->context_ref_id = 0;

            if (!empty($ledger_type) && $ledger_type != "NULL" && $ledger_type != '') {
                $grp_ledg_tree->operating_type = $ledger_type;
            } else {
                $grp_ledg_tree->operating_type = '';
            }

            if (!empty($grp_ledg_id)) {
                $grp_ledg_tree->ledger_account_id = $grp_ledg_id;
            }

            if ($update_led_id != '') {
                $grp_ledg_tree->ledger_account_id = $update_led_id;
            }
            if (!empty($parent_group)) {
                $grp_ledg_tree->parent_id = $parent_group;
                $ledger_props = $this->_getLedgerProps($parent_group);
                $ledger_props = json_decode(json_encode($ledger_props), true);
            } else {
                $grp_ledg_tree->parent_id = $this->constants['HEAD_GROUP_VAL'];
            }
            if (!empty($behaviour)) {
                $grp_ledg_tree->behaviour = $behaviour;
                $grp_ledg_tree->nature_of_account = $config->nature_account->$behaviour;
                $grp_ledg_tree->report_head = $config->report_head->$behaviour;
            } else {
                $grp_ledg_tree->behaviour = $ledger_props["behaviour"];
                $grp_ledg_tree->nature_of_account = $ledger_props["nature_of_account"];
                $grp_ledg_tree->report_head = $ledger_props["report_head"];
            }

            if (!empty($context) && $context != '') {
                $grp_ledg_tree->context = $context;
            } else {
                $grp_ledg_tree->context = $ledger_props["context"];
            }
            $grp_ledg_tree->defined_by =$this->constants['USER'] ;
            $grp_ledg_tree->status = $this->constants['ACTIVE'];
            $grp_ledg_tree->added_on = date("Y-m-d H:i:s");
            $grp_ledg_tree->created_by = ($this->input['user_id']) ? $this->input['user_id'] : 0;


            if ($grp_ledg_tree->save()) {
                //Opening balance should not go in case on Income Expense ledger
                // if (!in_array(strtolower($grp_ledg_tree->behaviour), array(INCOME ?? 'cr',$this->constants['EXPENSE'] ))) {
                    if (!in_array(strtolower($grp_ledg_tree->behaviour), array('cr','dr' ))) {

                $txn_date = $ledger_start_date;
                    $narration = 'entry for opening balance';
                    $txn_id = $this->addTxn($grp_ledg_tree->ledger_account_id, $opening_balance, $narration, $txn_date, "", $grp_ledg_tree->nature_of_account, "", "", "", "", $name, $is_opening = 1, $is_reco);
                }

                return $grp_ledg_tree->ledger_account_id;
            } else {
                return false;
            }
        } else {
            return "DUP" . $dup;
        }
    }

    private function _checkGroupNameDuplication($parent, $name, $ledg_id = "", $update_led_id = "")
    {

        $soc_id = $this->input['company_id'];

        $bind[1] = strtolower(trim($name));
        $ledgers_count = 0;
        if ($ledg_id) {
            $ledgers_count = ChsoneGrpLedgerTree::where("soc_id", $soc_id)->where('ledger_account_name', strtolower(trim($name)))->where('ledger_account_id', '!=', $ledg_id)->count();
        }
        if ($update_led_id) {
            $ledgers_count = ChsoneGrpLedgerTree::where("soc_id", $soc_id)->where('ledger_account_name', strtolower(trim($name)))->where('ledger_account_id', '!=', $update_led_id)->count();
        }

        return $ledgers_count;
    }

    public function getLedger($ledger_acct_id, $return_object = 1)
    {
        //fetch ledger details from chsone_grp_ledger_tree whose ledger_account_id = $ledger_acct_id
        $ledger = ChsoneGrpLedgerTree::where("ledger_account_id", $ledger_acct_id)->first();

        if ($ledger) {
            if ($return_object) {
                return $ledger;
            } else {
                return $ledger->toArray();
            }
        } else {
            return false;
        }
    }

    public function _getLedgerProps($parent_group)
    {
        $array = array();

        if (is_numeric($parent_group)) {
            $ledger_props = ChsoneGrpLedgerTree::select('ledger_account_name', 'nature_of_account', 'behaviour', 'report_head', 'context')
            ->where('ledger_account_id', $parent_group)
            ->first();
        }

        $array = $ledger_props;
        return $array;
    }

    public function addTxn($ledger_id, $txn_amt, $narration, $txn_date, $voucher_type =VOUCHER_JOURNAL , $txn_type = "", $from_txn_id = "", $pay_mode = "", $pay_ref = "", $opening_balance = "", $ledger_name = "", $is_opening = 1, $is_reco = 0)
    {
        return $this->_addTransaction($is_reco, $ledger_id, $txn_amt, $narration, $txn_date, $voucher_type, $txn_type, $from_txn_id, $pay_mode, $pay_ref, $ledger_name, $is_opening = 1);
    }

    private function _addTransaction($is_reco = 0, $ledger_id, $txn_amt, $narration, $txn_date, $voucher_type = VOUCHER_JOURNAL, $txn_type = "", $from_txn_id = "", $pay_mode = "", $pay_ref = "", $name = "", $is_opening = "", $other_recp_ref = "", $soc_id = "", $created_by = "", $voucher_reference_id = "", $voucher_reference_number = "", $is_cancelled = 0)
    {
        $txn = new ChsoneLedgerTransaction();
        if (empty($soc_id)) {
            $soc_id = $this->input['company_id'];
        }

        if (!empty($txn_type) || $txn_type != "") {
            $type_txn = $txn_type;
        } else {
            if ($from_txn_id) {
                $mode     = MODE_TO;
                $type_txn = "cr";
            } else {
                $mode     = MODE_FROM;
                $type_txn = "dr";
            }
        }

        //echo "s_opning".$is_opening;exit;
        if ($is_opening == 1) {

            $txn_entrt = ChsoneLedgerTransaction::where('soc_id', $soc_id)
            ->where('is_opening_balance', 1)
            ->where('ledger_account_id', $ledger_id)
            ->where('transaction_date', $txn_date)
            ->first();

            $txn->value_date = $txn_date;
            $txn->txn_id = (isset($txn_entrt->txn_id)  && !empty($txn_entrt->txn_id)) ? $txn_entrt->txn_id : '';
            if (!empty($txn->txn_id)) {
                $txn = ChsoneLedgerTransaction::where('txn_id', $txn->txn_id)->first();
            }
        }

        $txn->soc_id = $soc_id;
        $txn->transaction_date = $txn_date;
        $txn->ledger_account_id = $ledger_id;
        $txn->ledger_account_name = $name;
        $txn->voucher_type = $voucher_type;
        $txn->transaction_type = $type_txn; //$ledger_name_obj->nature_of_account;//$txn_type;
        $txn->payment_mode = $pay_mode;
        $txn->payment_reference = $pay_ref;
        $txn->transaction_amount = (is_null($txn_amt)) ? $txn->transaction_amount : $txn_amt;
        $txn->other_reference_id = $other_recp_ref;
        $txn->txn_from_id = $from_txn_id;
        $txn->memo_desc = $narration;
        $txn->is_opening_balance = $is_opening;
        $txn->is_reconciled = $is_reco;
        /*Added New Column in transaction*/
        $txn->voucher_reference_number = $voucher_reference_number;
        $txn->voucher_reference_id = $voucher_reference_id;
        $txn->is_cancelled = $is_cancelled;
        /*End New Column in transaction*/
        $txn->created_by = $this->input['user_id'];
        if (isset($created_by) && !empty($created_by)) {
            $txn->created_by = $created_by;
        }
        $txn->added_on = date("Y-m-d H:i:s");

        if ($txn->save()) {
            return $txn->txn_id;
        } else {
            print_r($txn->getMessages);
        }
        //echo "<pre>";print_r($a->getMessages);exit;
        return false;
    }

    public function updateParking($identifiedAs, $unitId)
    {
        // Find the first record with the given unit ID and status = 1
        $parkingAllotment = ChsoneParkingAllotmentDetail::where('fk_parking_unit_id', $unitId)->where('status', 1)->first();

        if (!empty($parkingAllotment)) {
            // Update the parking number
            $parkingAllotment->parking_number = $identifiedAs;

            // Save the changes
            if ($parkingAllotment->save()) {
                return true;
            }
        }

        return false;
    }

    public function getBankledger($context = 'bank', $soc_id = null)
    {
        if (empty($soc_id)) {
            $soc_id = $this->input['company_id'];
        }

        // Build the query
        $bankLedger = ChsoneGrpLedgerTree::where('soc_id', $soc_id)
        ->where('context', $context)
        ->where('entity_type', 'ledger')
        ->get();

        if (!empty($bankLedger)) {
            return $bankLedger->toArray();
        } else {
            return false;
        }
    }

    public function sendUnitsData($data)
    {
        $unitIds= $data['unitIds'];
        $soc_id= $data['soc_id'];
        // $unitIds=implode(',', $unitIds);
        $units = ChsoneUnitsMaster::where('unit_id', $unitIds)->get();
        if(!empty($units)){
            $unitsdata['units'] = $units->toArray();
            try{
                $param_data = array('data'=>json_encode($unitsdata));
                // $res = $client->post(VIZLOG_URL.$soc_id.'/syncdata?api_key='.VIZLOG_API_KEY, ['form_params' => $param_data]);
                // add curl request here
                $param_data = ['form_params' => $param_data];
                $curl = curl_init();
                curl_setopt_array($curl, array(
                    CURLOPT_URL => env('VIZLOG_URL') ?? 'https://stggateapi.cubeone.biz/api/v1/apps/'.$soc_id.'/syncdata?api_key='.env('VIZLOG_API_KEY') ?? '7640443c5dd28adb4f28bbc8d5c0b00263bdc4d5c56e31ef4f78b83bb2b42e67',
                    CURLOPT_URL => 'https://stggateapi.cubeone.biz/api/v1/apps/'.$soc_id.'/syncdata?api_key='.'7640443c5dd28adb4f28bbc8d5c0b00263bdc4d5c56e31ef4f78b83bb2b42e67',
                    CURLOPT_RETURNTRANSFER => true,
                    CURLOPT_ENCODING => "",
                    CURLOPT_MAXREDIRS => 10,
                    CURLOPT_TIMEOUT => 0,
                    CURLOPT_FOLLOWLOCATION => true,
                    CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                    CURLOPT_CUSTOMREQUEST => "POST",
                    CURLOPT_POSTFIELDS => $param_data,
                    CURLOPT_HTTPHEADER => array(
                        "Content-Type: application/json",
                    ),
                ));
                $response = curl_exec($curl);
                curl_close($curl);
                $res = json_decode($response);
                dd($res);
                $jsonResponse = $res->getBody()->getContents();
                if($this->updateChsoneIds($jsonResponse)){
                    return true;
                }
            }catch (\Exception $e) {
               return false;
           }
        }
    }

    public function updateChsoneIds($data){
        $data= json_decode($data, true);
        $data= $data['data'];
        // $result =$this->setup->setupmainsetting('setupsetting:saveSyncdata',$data);
        $result =$this->saveSyncdata($data);
        if($result['error'])
        {
            return false;
        }
        return true;
    }

    public function saveSyncdata($data=array())
    {
        try
        {
            $success = array();
            $failed = array();
            $failedFlag=false;
            if(isset($data['buildings']) && !empty($data['buildings']))
            {
                $success['buildings']= array();
                $failed['buildings']= array();
                foreach($data['buildings'] as $building)
                {
                    $buildingData = ChsoneSocietyUnitsTpl::where('soc_building_id', $building['vendor_reference_id'])->first();
                    if($buildingData)
                    {
                        $buildingData->vizlog_building_id = $building['building_id'];
                        if($buildingData->save())
                        {
                            $success['buildings'][]=$building['building_id'];
                        }
                        else
                        {
                           $failed['buildings'][]=$building['building_id'];
                           $failedFlag=true;
                        }
                    }
                    else
                    {
                        $failed[] = $building['soc_building_id'];
                        $failedFlag=true;
                    }
                }
            }

            if(isset($data['units']) && !empty($data['units']))
            {
                $success['units']= array();
                $failed['units']= array();
                foreach($data['units'] as $unit)
                {
                    $unitData = ChsoneUnitsMaster::where('unit_id', $unit['vendor_reference_id'])->first();
                    if($unitData)
                    {
                        $unitData->vizlog_unit_id = $unit['building_unit_id'];
                        if($unitData->save())
                        {
                            $success['units'][]=$unit['building_unit_id'];
                        }
                        else
                        {
                           $failed['units'][]=$unit['building_unit_id'];
                           $failedFlag=true;
                        }
                    }
                    else
                    {
                        $failed[] = $unit['building_unit_id'];
                        $failedFlag=true;
                    }
                }
            }

            if(isset($data['members']) && !empty($data['members']))
            {
                $success['members']= array();
                $failed['members']= array();
                foreach($data['members'] as $member)
                {
                    $memberData = ChsoneMembersMaster::where('id', $member['vendor_reference_id'])->first();
                    if($memberData)
                    {
                        $memberData->vizlog_member_id = $member['member_id'];
                        if($memberData->save())
                        {
                            $success['members'][]=$member['member_id'];
                        }
                        else
                        {
                           $failed['members'][]=$member['member_id'];
                           $failedFlag=true;
                        }
                    }
                    else
                    {
                        $failed[] = $member['member_id'];
                        $failedFlag=true;
                    }
                }
            }
            return array('error' => false,'success' => $success,'failed' => $failed,'is_failed'=> $failedFlag );
        }
        catch (\Exception $e) {
            return ['error'=>true,'message'=>$e->getMessage()];
        }
    }
}
