<?php

namespace App\Console\Commands\Actions\Settings\ComplexSetup;

use Illuminate\Console\Command;
use App\Console\Commands\Action;
use App\Models\Tenants\ChsoneSocietiesBuildingMaster;
use App\Models\Tenants\ChsoneUnitsMaster;


class DeleteBuildingAction extends Action
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'datasource:deleteBuilding {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Delete Building';

    public function apply()
    {
        $socBldgId = $this->input['id'];
        $cancelDate = $this->input['cancel_allotment'];
        $cancellationReason = $this->input['reason'];

        $building = $this->tenantDB()->table('chsone_societies_building_master')
        ->where("soc_building_id", $socBldgId)
        ->update([
            "cancellation_reason" => $cancellationReason,
            "cancel_date" => $cancelDate,
            "status" => 0,
            "updated_by" => $this->input['user_id'],
        ]);
        if ($building) {
            if ($this->input['cancel_allotment'] > $this->getCurrentDate('display')) {
                $this->message = "Building will be discarded from ". $cancelDate;
                return $this->responseSuccess($this->message, 200);
            } else {
                $this->message = "Building discarded successfully";
                return $this->responseSuccess($this->message, 200);
            }
        }
    }

    private function responseError($message, $statusCode, $data = [])
    {
        $this->message = $message;
        $this->status = "error";
        $this->statusCode = $statusCode;
        $this->data = $data;
        return false;
    }

    private function responseSuccess($message, $statusCode, $data = [])
    {
        $this->message = $message;
        $this->status = "success";
        $this->statusCode = $statusCode;
        $this->data = $data;
        return true;
    }
}
