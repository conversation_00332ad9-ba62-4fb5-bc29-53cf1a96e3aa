<?php

namespace App\Console\Commands\Actions\Settings\ComplexSetup;

use Illuminate\Console\Command;
use App\Console\Commands\Action;
use App\Models\Tenants\ChsoneSocietiesBuildingMaster;
use App\Models\Tenants\ChsoneUnitsMaster;


class AddBuildingAction extends Action
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'datasource:buildingAdd {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Add Building';

    public function apply()
    {
        $soc_building_id = $this->input['soc_building_id'] ?? null;
        $soc_building_name = $this->input["soc_building_name"];
        $soc_building_floors = $this->input["soc_building_floors"];

        $building = ChsoneSocietiesBuildingMaster::find($soc_building_id);

        if ($building) {
            // Check if the building name exists in the society (excluding the current building)
            $duplicateBuilding = ChsoneSocietiesBuildingMaster::where('soc_building_name', $soc_building_name)
                ->where('soc_building_id', '<>', $soc_building_id)
                ->exists();

            if ($duplicateBuilding) {
                $message = "Building name already exists in your society";
                return $this->responseError($message, 400, $building->toArray());
            }

            // Check if any unit is present on the floor being edited
            $maxUnits = ChsoneUnitsMaster::where('soc_building_id', $soc_building_id)
                ->max('soc_building_floor');

            if ($soc_building_floors < $maxUnits) {
                $message = "Unit is present on floor you are editing. Thus Building floors can't be edited";
                return $this->responseError($message, 400, $building->toArray());
            }

            // Update the building
            $building->update([
                "soc_building_name" => $soc_building_name,
                "soc_building_floors" => $soc_building_floors,
                "soc_id" => $this->input["company_id"],
                "status" => 1,
                "updated_date" => $this->getCurrentDate("database") . " " . date("H:i:s")
            ]);
            $arr = [
                "bldg_id" => $soc_building_id,
                "bldg_name" => $soc_building_name
            ];

            $message = "Building name has been updated in Society";
            return $this->responseSuccess($message, 200);
        } else {
            // Check if the building name already exists in the society (when adding a new building)
            $duplicateBuilding = ChsoneSocietiesBuildingMaster::where('soc_building_name', $soc_building_name)->exists();

            if ($duplicateBuilding) {
                $message = "Building name already exists in your society";
                return $this->responseError($message, 400,);
            }

            // Create a new building
            $building = new ChsoneSocietiesBuildingMaster;
            $building->created_date = $this->getCurrentDate("database") . " " . date("H:i:s");
            $building->soc_building_name = $soc_building_name;
            $building->soc_building_floors = $soc_building_floors;
            $building->soc_id = $this->input["company_id"];
            $building->status = 1;
            $building->updated_date = $this->getCurrentDate("database") . " " . date("H:i:s");

            if ($building->save()) {
                $message = "New Building named '$building->soc_building_name' with '$building->soc_building_floors floors' is added in Society";
                return $this->responseSuccess($message, 200, $building->toArray());
            }
        }
    }

    private function responseError($message, $statusCode, $data = [])
    {
        $this->message = $message;
        $this->status = "error";
        $this->statusCode = $statusCode;
        $this->data = $data;
        return false;
    }

    private function responseSuccess($message, $statusCode, $data = [])
    {
        $this->message = $message;
        $this->status = "success";
        $this->statusCode = $statusCode;
        $this->data = $data;
        return true;
    }
}
