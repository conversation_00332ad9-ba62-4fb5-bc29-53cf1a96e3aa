<?php

namespace App\Console\Commands\Actions\Settings\AutoInvoicingRule;

use Illuminate\Console\Command;
use App\Console\Commands\Action;
use Illuminate\Support\Facades\DB;

class MemberIncomeAccountDataSource extends Action
{
    protected $signature = 'datasource:memberIncomeAccount {flowId} {parentId} {input}';

    protected $description = 'Get Member Income Account Data Source';

    public function apply()
    {
        $incomeAccount = $this->tenantDB()->table('income_accounts')
            ->select('account_id', 'account_name')
            ->where('account_type','=','member')
            ->get();

        $incomeAccount->transform(function ($account) {
            // Insert spaces before each uppercase letter
            $account->account_name = preg_replace('/(?<!^)([A-Z])/', ' $1', $account->account_name);
            return $account;
        });
        
        if(!$incomeAccount)
        {
            $this->status = 'error';
            $this->message = 'Member Income Account not found';
            $this->statusCode = 400;
        }
        else{
            $this->data = $incomeAccount;
        }
    }
}