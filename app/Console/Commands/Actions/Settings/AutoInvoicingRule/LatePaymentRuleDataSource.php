<?php

namespace App\Console\Commands\Actions\Settings\AutoInvoicingRule;

use App\Console\Commands\Action;
use Illuminate\Support\Facades\DB;

class LatePaymentRuleDataSource extends Action
{
    protected $signature = 'datasource:latePaymentRule {flowId} {parentId} {input}';

    protected $description = 'Invoice Listing';

    public function apply()
    {
        $page = $this->input['page'] ?? 1;
        $per_page = !empty($this->input['per_page']) ? $this->input['per_page']: ($this->hugeData ? 100000 : 10);

        $offset = ($page - 1) * $per_page;
        $result = $this->tenantDB()->table('income_late_payment_charges')
            ->select(
                "id",
                DB::raw('CASE WHEN interest_amount_type = "percentage" THEN CONCAT(simple_interest, "(%)") ELSE simple_interest END as late_charges'),
                'effective_date',
                DB::raw('CONCAT(grace_period, " days") as grace_period'),
                DB::raw('CASE WHEN calculate_from = "billdate" THEN "Bill Date" ELSE "Due Date" END as late_charges_will_apply_from'),
                DB::raw('CASE WHEN calculate_for = "billperiod" THEN "Bill Period" ELSE "Per Day" END as late_charges_will_apply_for'),
                // DB::raw('CASE WHEN effective_date >= (SELECT fy_start_date FROM soc_account_financial_year_master WHERE closed = 0)
                //         AND effective_date <= (SELECT fy_end_date FROM soc_account_financial_year_master WHERE closed = 0)
                //         THEN "Active" ELSE "Past" END as status')
            )
            ->where('type','maintenance')
            ->orderBy('effective_date', 'desc')
            ->offset($offset)
            ->limit($per_page)
            ->get();

            // create forreach loop for set status
            $isActiveSet = false;
            foreach($result as $key => $value){
                if($value->effective_date > date('Y-m-d')){
                    $result[$key]->status = 'Upcoming';
                }elseif($value->effective_date <= date('Y-m-d') && $isActiveSet == true){
                    $result[$key]->status = 'Past';
                }elseif($value->effective_date <= date('Y-m-d') && $isActiveSet == false){
                    $result[$key]->status = 'Active';
                    $isActiveSet = true;
                }
            }

            $this->data = $result;

            $offset = ($page - 1) * $per_page;
            $totalCount = $this->tenantDB()->table('income_late_payment_charges')
                ->where('type','maintenance')
                ->count();

            $this->meta['pagination']['total'] = $totalCount;


    }
}
