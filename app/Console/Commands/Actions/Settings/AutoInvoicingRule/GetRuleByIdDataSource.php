<?php

namespace App\Console\Commands\Actions\Settings\AutoInvoicingRule;

use Illuminate\Console\Command;
use App\Console\Commands\Action;
use Illuminate\Database\Eloquent\Casts\Json;
use Illuminate\Support\Facades\DB;

class GetRuleByIdDataSource extends Action
{
    protected $signature = 'datasource:getRuleById {flowId} {parentId} {input}';

    protected $description = 'Invoice Rule Get Data Source';

    public function apply()
    {
        // Ensure that $this->input is properly populated.
        if (!is_array($this->input)) {
            $this->input = json_decode($this->argument('input'), true);
        }

        $soc_id = $this->input['company_id'];
        $ruleId = $this->input['rule_id'];

        $query = $this->tenantDB()->table('income_invoice_rules')
            ->leftJoin('income_accounts', 'income_invoice_rules.income_account_id', '=', 'income_accounts.account_id')
            ->leftJoin('chsone_tax_classes', 'income_invoice_rules.applicable_taxes', '=', 'chsone_tax_classes.tax_class_id')
            ->leftJoin('income_parking_charges', 'income_parking_charges.rule_id', '=', 'income_invoice_rules.id')
            ->select(
            // income_invoice_rules columns with explicit aliases
                'income_invoice_rules.id as invoice_rule_id',
                'income_invoice_rules.soc_id as soc_id',
                'income_invoice_rules.soc_units_type_id as unittype',
                'income_invoice_rules.unit_type as unit_type',
                'income_invoice_rules.income_account_id as income_account_id',
                DB::raw("CONCAT(income_accounts.account_id, '_', income_accounts.account_name) AS incomeaccount"),
                'income_invoice_rules.applicable_taxes as applicable_taxes',
                'income_invoice_rules.apply_late_payment_interest as apply_late_payment_interest',
                'income_invoice_rules.effective_date as effective_date',
                'income_invoice_rules.rule as rule',
                'income_invoice_rules.optional_rule as optional_rule',
                'income_invoice_rules.tax_exemptions as tax_exemptions',
                'income_invoice_rules.fee_carpet_area as area',
                'income_invoice_rules.fee_open_area as open_area',
                'income_invoice_rules.fee_open_area as nocarea',
                'income_invoice_rules.fee_open_area as other_open_income_fee',
                'income_invoice_rules.fee_open_area as open_nocarea',
                'income_invoice_rules.fee_per_unit as flat_area',
                'income_invoice_rules.fee_noc_owner as fee_noc_owner',
                'income_invoice_rules.fee_noc_tenant as noc_tenant',
                'income_invoice_rules.fee_noc_vacant as noc_vacant',
                'income_invoice_rules.fee_noc_electricity as fee_noc_electricity',
                'income_invoice_rules.fee_noc_water as fee_noc_water',
                'income_invoice_rules.fee_noc_maintenance as fee_noc_maintenance',
                'income_invoice_rules.other_income_fee as other_income_fee',
                'income_invoice_rules.other_income_fee_rate as other_income_fee_rate',
                'income_invoice_rules.other_percent_of_income_account as other_percent_of_income_account',
                'income_invoice_rules.created_date as created_date',
                'income_invoice_rules.created_by as created_by',
                'income_invoice_rules.updated_date as invoice_rule_updated_date',
                'income_invoice_rules.selection as selection',

                // income_accounts columns
                'income_accounts.display_name as account_display_name',
                'income_accounts.account_id as account_id',
                'income_accounts.account_name as account_name',

                // chsone_tax_classes columns
                'chsone_tax_classes.tax_class_name as tax_class_name',
                'chsone_tax_classes.tax_class_id as selected_taxes',

                // income_parking_charges columns
                'income_parking_charges.parking_type as parking_type',
                'income_parking_charges.fee_2_wheeler as two_wheeler',
                'income_parking_charges.fee_4_wheeler as four_wheeler',
                'income_parking_charges.vehicle_number as vehicle_number',
                'income_parking_charges.member_type as member_type',
                'income_parking_charges.updated_date as parking_updated_date'
            )
            ->where('income_accounts.soc_id', $soc_id)
            ->where('income_invoice_rules.id', $ruleId)
            ->where(function ($query) use ($soc_id, $ruleId) {
                $query->where('income_accounts.account_name', '<>', 'Parking')
                    ->orWhere(function ($query) use ($soc_id, $ruleId) {
                        $query->where('income_accounts.account_name', '=', 'Parking')
                            ->where('income_parking_charges.updated_date', '=', function ($subquery) use ($soc_id, $ruleId) {
                                $subquery->select(DB::raw('MAX(updated_date)'))
                                    ->from('income_parking_charges as ii2')
                                    ->where('ii2.soc_id', $soc_id)
                                    ->where('ii2.rule_id', $ruleId);
                            });
                    });
            })
            ->orderBy('effective_date', 'desc');

        // Get all records (expected to be multiple rows for Parking).
        $objInvoiceRuleDetails = $query->get();

        foreach($objInvoiceRuleDetails as $rules) {
            if ($rules->account_name == 'ElectionFund') {
                $overrideData = $this->tenantDB()->table('income_invoice_rules')->where('id', $ruleId)->get();
                $rules->other_income_fee = $overrideData[0]->other_income_fee;
            }elseif($rules->account_name == 'CommonElectricityCharges'){
                $overrideData = $this->tenantDB()->table('income_invoice_rules')->where('id', $ruleId)->get();
                $rules->other_income_fee = $overrideData[0]->fee_carpet_area;
            }
        }

        //check if entry is present in income_floorbased_charges of rule then fetch data.
        $floorBasedCharges = $this->tenantDB()
            ->table('income_floorbased_charges')
            ->where('rule_id', $ruleId)
            ->where('status', 1)
            ->get();

        if ($floorBasedCharges->isNotEmpty() && $objInvoiceRuleDetails->isNotEmpty()) {
            // Grab the single stdClass inside the Collection
            $base = $objInvoiceRuleDetails->first();

            foreach ($floorBasedCharges as $index => $item) {
                $i = $index + 1;
                $base->{"floor_from_{$i}"}         = $item->floor_from;
                $base->{"floor_to_{$i}"}           = $item->floor_to;
                $base->{"floors_amount_charge_{$i}"} = $item->fee_carpet_area;
            }
        }

        // If the account is 'Parking', then aggregate the parking details.
        if ($objInvoiceRuleDetails->isNotEmpty() && $objInvoiceRuleDetails->first()->account_name === 'Parking') {
            // All rows belong to the same invoice rule so we use the first row as the base.
            $baseRecord = $objInvoiceRuleDetails->first();
            // Build an array of parking details from all rows.
            $parkingDetails = $objInvoiceRuleDetails->map(function ($item) {
                return [
                    'parking_type'   => $item->parking_type,
                    'two_wheeler'  => $item->two_wheeler,
                    'four_wheeler'  => $item->four_wheeler,
                    'vehicle_number' => $item->vehicle_number,
                    'member_type'    => $item->member_type,
                    //'updated_date'   => $item->parking_updated_date,
                ];
            })->toArray();

            // Convert the base record to an array and add parking_details.
            $aggregatedRecord = (array) $baseRecord;
            $aggregatedRecord['parking_details'] = $parkingDetails;

            // Optionally, you might want to remove the individual parking columns:
            unset(
                $aggregatedRecord['parking_type'],
                $aggregatedRecord['two_wheeler'],
                $aggregatedRecord['four_wheeler'],
                $aggregatedRecord['vehicle_number'],
                $aggregatedRecord['member_type'],
                $aggregatedRecord['updated_date']
            );

            // Set a single aggregated record as the response.
            $this->data = [$aggregatedRecord];
        } else {
            // If account_name is not 'Parking' or no rows, return the data as is.
            $this->data = $objInvoiceRuleDetails;
        }
    }
}
