<?php

namespace App\Console\Commands\Actions\Settings\AutoInvoicingRule;

use Illuminate\Console\Command;
use App\Console\Commands\Action;
use App\Models\Tenants\ChsoneSocietyUnitsTpl;
use App\Models\Tenants\IncomeAccount;
use App\Models\Tenants\IncomeFloorbasedCharge;
use App\Models\Tenants\IncomeInvoiceNocRule;
use App\Models\Tenants\IncomeInvoiceRule;
use App\Models\Tenants\IncomeParkingCharge;
use App\Models\Tenants\ChsoneGrpLedgerTree;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Config;

class AddRuleDataSource extends Action
{

    protected $constants;

    public function __construct()
    {
        parent::__construct();
        $this->constants = Config::get('constants');
    }
    protected $signature = 'datasource:addRule {flowId} {parentId} {input}';

    protected $description = 'Add Auto Invoicing Rule';

    protected $parking_account_id = 2;
    protected $noc_id = 8;


    public function apply()
    {
        try {
            $ruleID = $this->input['invoiceruleid'] ?? '';
            $arrPostData['id'] = $ruleID;
            $arrPostData = $this->input;

            $soc_id = $this->input['company_id'];
            if (!isset($arrListenerSavedata['arrPostData']['create_custom_rule'])) {
                $arrListenerSavedata['arrPostData']['create_custom_rule'] = 0;
            }
            $arrInvoiceSetting = $this->getInvoiceSetting(array('soc_id' => $soc_id));
            $options['arrparkingowner'] = array("owner" => "Owner Parking", "tenant" => "Tenant Parking");

            $arrGetRuleInfo['ruleid'] = $ruleID;
            $arrListenerdataForParking['selectperticular'] = 'parking';
            $arrallparkingunittypes  = $this->getAllUnitType($soc_id, $arrListenerdataForParking);

            $arrallunittypes = $arrallparkingunittypes;
            $options['arrallparkingunittypes'] = $arrallparkingunittypes;
            $vcount = 1; // Default vehicle count

            if (!empty($ruleID)) {

                // Query to fetch vehicle numbers for the given rule ID
                $parkingCharges = $this->tenantDB()
                    ->table('income_parking_charges')
                    ->where('rule_id', $ruleID)
                    ->pluck('vehicle_number')
                    ->toArray();

                if (!empty($parkingCharges)) {
                    $vcount = max($parkingCharges);
                }
            }


            $options['vehicleCount'] = $vcount;
            $arrallmemberIncomeAccount  = $this->getselectmemberdetails($soc_id, $arrListenerdataForParking);
            $transferData = [];
            $transferKey = [];
            $rules = [];
            if ($ruleID != '') {
                $rules = $this->tenantDB()
                    ->table('income_invoice_noc_rules')
                    ->where('rule_id', $ruleID)
                    ->get()
                    ->toArray();
            }
            foreach ($arrallmemberIncomeAccount as $allmemberIA) {
                $transferData[$allmemberIA] = $allmemberIA;
                array_push($transferKey, $allmemberIA);
            }
            $arrextracherge = $transferData; //array("Electricity"=>"Electricity","Water"=>"Water","Maintenance"=>"Maintenance");
            unset($arrextracherge['Noc']);
            $options['arrextracherge'] = $arrextracherge;
            $options['values'] = $rules;
            $options['arrallunittypes'] = $arrallunittypes;
            $arrRuleData  = $this->getAllRuledetails($ruleID, $soc_id);



            //applicable taxes
            $arrListenerdataForApplicableTaxes = [
                "soc_id" => $soc_id,
            ];

            $arrListenerdataForApplicableTaxes['effective_date'] = ($ruleID != '') ? $arrRuleData['arrIncomeRuleDetails']['effective_date'] : $this->getCurrentDate('database');


            $arrApplicableTaxes  = $this->getAllTaxClasses(array('soc_id' => $soc_id));

            $arrApplicableTaxes  = $this->getTaxClassSelect(array('arrTaxClass' => $arrApplicableTaxes));

            $options['arrApplicableTaxes'] = $arrApplicableTaxes;

            $arrCheckEntries['soc_id'] = $soc_id;
            $dateCurrentFYdate = $this->getCurrentFinantialYear($arrCheckEntries);

            //get Maximum floors for building in society
            $arrListenerdataForBuildingFoor = $soc_id;

            $intMaxFloor  = $this->getMaxFloorFromSociety($arrListenerdataForBuildingFoor);

            $options['MaxFloor'] = $intMaxFloor;

            /*Add new rule start*/
            $arrmemberincomeaccount = [];
            if (empty($ruleID)) {
                //$arrListenerdata = array('auth'=>$this->session->get("auth"));
                $options['memberNewIncomeAccount'] = true;
                $bankAccount  = $this->getbankaccountdetails();
                $options['arrbankAccount'] = $bankAccount;

                $cashAccount  = $this->getcashaccountdetails();
                $options['arrcashAccount'] = $cashAccount;
            } else {

                $arrListenerdataForIncomeAccounts = [
                    'soc_id' => $soc_id
                ];
                $arrListenerdataForIncomeAccounts['useAccountID'] = 1;
                $arrmemberincomeaccount  = $this->getselectmemberdetails($arrListenerdataForIncomeAccounts);
            }

            /*Add new rule end*/


            //$arrGeneratedInvoice  = $this->incometrackerevent->incometracker('InvoiceGenerator:getFirstGeneratedInvoice', array('auth'=>$auth));
            //get invoice setting details
            $arrInvoiceSetting = $this->getInvoiceSetting(array('soc_id' => $soc_id));
            $arrCategoryId = (!empty($arrRuleData['arrIncomeRuleDetails']['soc_units_type_id'])) ? array($arrRuleData['arrIncomeRuleDetails']['soc_units_type_id']) : array();
            $arrRecentInvoiceDate = $this->getRecentGeneratedInvoiceDate(array('soc_id' => $soc_id, 'arrCategoryId' => $arrPostData['unittype']));

            if (!empty($arrRuleData) && strtolower($arrRuleData['arrIncomeRuleDetails']['unit_type']) == 'parking' && empty($arrRuleData['arrIncomeRuleDetails']['soc_units_type_id'])) {
                $arrRecentParkingInvoiceDate = $this->getParkingInvoiceRuleId(array('soc_id' => $soc_id, 'rule_id' => $arrPostData['invoiceruleid']));

                $arrRecentInvoiceDate = $this->getRecentlyUsedRuleDetail(array('arrRecentInvoiceDate' => $arrRecentInvoiceDate, 'arrRecentParkingInvoiceDate' => $arrRecentParkingInvoiceDate));
            }
            $InvoiceEffectiveDate = (!empty($arrRecentInvoiceDate['maxEffectiveDate'])) ? $arrRecentInvoiceDate['maxEffectiveDate'] : $this->getDisplayDate($arrInvoiceSetting['effective_date']);
            $arrPostData = $this->input;
            if (isset($arrPostData['vehicles'])) {
                $convertedPayload = $this->convertPayload(['vehicles' => $arrPostData['vehicles']]);
                // Merge the converted payload into $arrPostData
                //
                $arrPostData = array_merge($arrPostData, $convertedPayload);
            }

            $options['arrallmemberIncomeAccount'] = $arrallmemberIncomeAccount;

            // $options['arrextracherge'] = $arrextracherge;
            // $options['values'] = $rules;
            // $options['arrRuleData'] = $arrRuleData;
            // $arrRecentInvoiceDate = [];
            if (!empty($arrPostData['unittype'][0])) {
                $arrPostData['unittype'] = (!is_array($arrPostData['unittype'])) ? array($arrPostData['unittype']) : $arrPostData['unittype'];
                $arrRecentInvoiceDate = $this->getRecentGeneratedInvoiceDate(array('soc_id' => $soc_id, 'arrCategoryId' => $arrPostData['unittype']));
            } elseif (!empty($arrPostData['IsParking']) && strtolower($arrPostData['IsParking']) == 'parking' && empty($arrPostData['unittype'][0]) && $arrPostData['invoiceruleid']) {
                $arrRecentInvoiceDate = $this->getParkingInvoiceRuleId(array('soc_id' => $soc_id, 'rule_id' => $arrPostData['invoiceruleid']));
            }

            $InvoiceEffectiveDate = (!empty($arrRecentInvoiceDate['maxEffectiveDate']))
                ? $arrRecentInvoiceDate['maxEffectiveDate']
                : $arrInvoiceSetting->effective_date;
            //check passed dated rule or not
            if (!empty($InvoiceEffectiveDate) && ($arrPostData['effective_date'] < $InvoiceEffectiveDate)) {
                $arrPostData['isRulePassedDated'] = 1;
            }
            $arrPostData['generatedInvoice'] = count($arrRecentInvoiceDate);
            //If edited rule is past or active rule then create new rule in case of edit
            $arrPostData['previousRuleId'] = $arrPostData['invoiceruleid'];
            if (
                !empty($arrRuleData['arrIncomeRuleDetails']['effective_date'])
                && !empty($arrmemberincomeaccount) && !empty($arrRecentInvoiceDate)
            ) {
                //echo $this->getDatabaseDate($arrPostData['effective_date']).'||'.$this->getDatabaseDate($arrRuleData['arrIncomeRuleDetails']['effective_date']).'||'.$this->getDatabaseDate($InvoiceEffectiveDate);exit;
                if (
                    ($arrPostData['effective_date'] >= $InvoiceEffectiveDate) && !empty($ruleID)
                    && ($arrRuleData['arrIncomeRuleDetails']['effective_date'] < $InvoiceEffectiveDate)
                ) {
                    $arrPostData['invoiceruleid'] = $ruleID = '';
                    $arrPostData['incomeaccount'] = $arrRuleData['arrIncomeRuleDetails']['income_account_id'] . '_' . $arrmemberincomeaccount[$arrRuleData['arrIncomeRuleDetails']['income_account_id']];
                }
            }
            if (!isset($arrPostData['unittype'])) {
                $arrPostData['unittype'] = array();
            }
            //temporary code remove after multiselect front end fix
            $arrListenerSavedata = [
                "soc_id" => $soc_id,
            ];
            $arrListenerSavedata['arrPostData'] = $arrPostData;
            $arrListenerSavedata  = $this->getNewRulePostdata($arrListenerSavedata);

            $arrListenerSavedata['arrparkingmembertype'] = $options['arrparkingowner'];
            $arrListenerSavedata['arrallparkingunittypes'] = $arrallparkingunittypes;
            $arrListenerSavedata['arrallunittypes'] = $arrallunittypes;
            $arrListenerSavedata['arrextracherge'] = $arrextracherge;


            if (!empty($arrmemberincomeaccount)) {
                $arrListenerSavedata['arrAllIncomeAccounts'] = $arrmemberincomeaccount;
            }
            //$arrPostData = $this->request->getPost();
            if (isset($arrListenerSavedata['other_income_deadlock']) && !empty($arrListenerSavedata['other_income_deadlock'])) {
                $arrPostData['other_income_deadlock'] = $arrListenerSavedata['other_income_deadlock'];
            }

            $arrPostData['edit_rule'] = $arrRuleData['arrIncomeRuleDetails']['edit_rule'];

            //Adding ledger for new custom rule
            if ($arrListenerSavedata['arrPostData']['create_custom_rule'] == 1) {
                $arrListenerSavedata['arrPostData']['select']['member']['bankaccount'][$arrListenerSavedata['arrPostData']['member_incomeaccount']] = $arrListenerSavedata['arrPostData']['select']['member']['bankaccount']['memberIncomeAccount'];
                $arrListenerSavedata['arrPostData']['select']['member']['cashaccount'][$arrListenerSavedata['arrPostData']['member_incomeaccount']] = $arrListenerSavedata['arrPostData']['select']['member']['cashaccount']['memberIncomeAccount'];
                unset($arrListenerSavedata['arrPostData']['select']['member']['bankaccount']['memberIncomeAccount']);
                unset($arrListenerSavedata['arrPostData']['select']['member']['cashaccount']['memberIncomeAccount']);

                $arrListenerLedgerdata['soc_id'] = $soc_id;
                $arrListenerLedgerdata['postdata']['select'] = $arrListenerSavedata['arrPostData']['select'];

                $flagsuccess  = $this->saveincomeaccount($arrListenerLedgerdata);
                if ($flagsuccess == 1) {
                    $arrListenerLedgerdata['postdata']['account_type'] = 'member';
                    $arrListenerLedgerdata['postdata']['account_name'] = $arrListenerSavedata['arrPostData']['member_incomeaccount'];
                    $arrIncomeAccountList  = $this->getIncomeAccountDetails($arrListenerLedgerdata);
                    $arrListenerSavedata['arrPostData']['incomeaccount'] = $arrIncomeAccountList['account_id'] . '_' . $arrIncomeAccountList['account_name'];
                }
            }
            // $arrSocUsers = $this->session->get("socUsers");
            // $arrListenerSavedata['socAdminUsers'] = $arrSocUsers['socAdminUsers'];
            $result  = $this->saveAddRule($arrListenerSavedata);
            //dd("hi",$result);
            if (
                !empty($arrListenerSavedata['arrPostData']['effective_date_to'])
                && !empty($result) && isset($result['success']) && $result['success'] == 1
            ) {
                $arrDeactivatedRule  = $this->autoDeactivationRuleDetail($arrListenerSavedata);
                if (!empty($arrDeactivatedRule['arrPostData'])) {
                    //$result  = $this->saveAddRule($arrDeactivatedRule);
                }
            }

            //exit;
            if (!empty($result) && isset($result['error']) && $result['error'] == 1) {
                $this->message = $result['message'] ?? "Failed to add rule";
                $this->status = "error";
                $this->statusCode = 400;
                return;
                // foreach($result['message'] as $message)
                // {
                //     $errflag[] = (string) $message;
                // }
                // $this->view->setVar("errflag", $errflag);
                //return $this->dispatcher->forward("income-tracker-invoice-setting/add_rule");
            } elseif (!empty($result) && $result['success'] == 1) {
                if ($arrPostData['invoiceruleid'] > 0) {
                    $this->message = "Rule updated successfully";
                } else {
                    $this->message = "Rule added successfully";
                }
                $this->status = "success";
                $this->statusCode = 200;
                $this->data = $result;
                return;
                // $successmsg=(isset($arrListenerSavedata['invoiceruleid']) && !empty($arrListenerSavedata['invoiceruleid']))  ?  RULE_EDIT_SUCC : RULE_ADD_SUCC;
                // $this->session->set("succ_rule", $successmsg);
                // $this->response->redirect("admin/income-tracker-invoice-setting/invoicelisting");
            } else {
                $this->message = $result['message'] ?? "Failed to add rule1";
                $this->status = "error";
                $this->statusCode = 400;
                return;
                // $errmsg = 'Unbale to save rule due to some technincal issue, Please try later.';
                // $this->session->set("err_rule", $errmsg);
                // $this->response->redirect("admin/income-tracker-invoice-setting/invoicelisting");
            }
        } catch (\Exception $e) {
            dd($e);
            $this->message = $e->getMessage();
            $this->status = "error";
            $this->statusCode = 400;
            return;
        }
    }




    public function getSelectMemberDetails($socId, $useAccountID = 0)
    {
        // Fetch constants (assuming it's defined in a config file)
        $memberIncomeAccount = config('constants.memberincomeaccount');
        // Fetch income accounts with the given conditions
        $incomeAccounts = $this->tenantDB()->table('income_accounts')->where('soc_id', $socId)
            ->where('account_type', 'member')
            ->get();

        // Process the income accounts into the required format
        $newIncomeAccounts = [];
        foreach ($incomeAccounts as $account) {
            if ($useAccountID == 1) {
                $newIncomeAccounts[$account->account_id] = $account->account_name;
            } else {
                $newIncomeAccounts[$account->account_id . '_' . $account->account_name] = $account->account_name;
            }
        }

        return $newIncomeAccounts;
    }


    public function getRecentGeneratedInvoiceDate($socId, $arrCategoryId = [])
    {
        $arrResult = [];

        // Building the query
        $query = $this->tenantDB()->table('income_unit_invoices as I')
            ->selectRaw('U.fk_unit_category_id, U.unit_category, MAX(I.to_date) as end_date')
            ->join('chsone_units_master as U', 'U.unit_id', '=', 'I.fk_unit_id')
            ->where('I.soc_id', $socId)
            ->where('I.status', '!=', 'cancelled');

        // Adding category filter if applicable
        if (!empty($arrCategoryId)) {
            $query->whereIn('U.fk_unit_category_id', $arrCategoryId);
        }

        $query->groupBy('U.fk_unit_category_id');

        // Executing the query
        $resultset = $query->get();

        // Processing the results
        if (!$resultset->isEmpty()) {
            $maxEffectiveDate = '';
            foreach ($resultset as $row) {
                $row = (array) $row; // Cast object to array
                $arrResult[$row['fk_unit_category_id']] = $row;
                $arrResult[$row['fk_unit_category_id']]['effective_date'] = date('Y-m-d', strtotime("+1 day", strtotime($row['end_date'])));

                if (empty($maxEffectiveDate)) {
                    $maxEffectiveDate = $arrResult[$row['fk_unit_category_id']]['effective_date'];
                } else {
                    $maxEffectiveDate = ($arrResult[$row['fk_unit_category_id']]['effective_date'] > $maxEffectiveDate)
                        ? $arrResult[$row['fk_unit_category_id']]['effective_date']
                        : $maxEffectiveDate;
                }
            }

            // Format maxEffectiveDate for display
            $arrResult['maxEffectiveDate'] = $maxEffectiveDate;
        }

        return $arrResult;
    }

    public function getParkingInvoiceRuleId($socId)
    {
        $arrResult = [];

        // Building the query using Laravel's query builder
        $resultset = $this->tenantDB()->table('income_invoice_rules as R')
            ->selectRaw('MAX(I.to_date) as end_date, MAX(I.invoice_number) as invoice_number')
            ->join('income_invoice_particular as P', 'R.id', '=', 'P.fk_rule_id')
            ->leftJoin('income_unit_invoices as I', 'I.unit_invoice_id', '=', 'P.fk_unit_invoice_id')
            ->where('R.soc_id', $socId)
            ->where('I.status', '!=', 'cancelled')
            ->where('R.unit_type', 'parking')
            ->get();

        // Processing the resultset
        if (!$resultset->isEmpty()) {
            $arrResponse = $resultset->toArray();
            if (!empty($arrResponse[0]->invoice_number)) {
                $endDate = $arrResponse[0]->end_date;

                $arrResult[0] = [
                    'fk_unit_category_id' => 0,
                    'unit_category' => 'Parking',
                    'end_date' => $endDate,
                    'effective_date' => date('Y-m-d', strtotime("+1 day", strtotime($endDate))),
                ];

                // Add formatted maxEffectiveDate
                $arrResult['maxEffectiveDate'] = $arrResult[0]['effective_date'];
            }
        }

        return $arrResult;
    }


    protected function getNewRulePostdata($data = [])
    {
        $soc_id = $this->input['company_id'];
        $incomeAccount = explode('_', $data['arrPostData']['incomeaccount']);

        // Handle default cases based on `incomeaccount` and `create_custom_rule`
        if (
            isset($incomeAccount[1]) &&
            in_array(strtolower($incomeAccount[1]), ['', 'maintenancefee', 'parking', 'propertytax']) &&
            $data['arrPostData']['create_custom_rule'] == 0
        ) {
            $data['arrPostData']['other_income_fee'] = '';
            $data['arrPostData']['other_income_fee_rate'] = '';
            $data['arrPostData']['other_percent_of_income_account'] = '';
        } elseif ($data['arrPostData']['create_custom_rule'] == 1) {
            $data['arrPostData']['member_incomeaccount'] = preg_replace(
                '/[^A-Za-z0-9\-]/',
                '',
                ucwords(trim($data['arrPostData']['member_incomeaccount']))
            );
        }

        // Check for other income fee rate as a percentage
        if (
            !empty($data['arrPostData']['other_income_fee']) &&
            !empty($data['arrPostData']['other_income_fee_rate']) &&
            strtolower($data['arrPostData']['other_income_fee_rate']) === 'percentage'
        ) {
            if (!empty($data['arrPostData']['invoiceruleid'])) {
                // Find specific Income Invoice Rule by ID
                $incomeInvoiceRule = $this->tenantDB()->table('income_invoice_rules')->where('id', $data['arrPostData']['invoiceruleid'])
                    ->where('soc_id', $soc_id)
                    ->first();

                if ($incomeInvoiceRule) {
                    $incomeAccountId = $incomeInvoiceRule->income_account_id;
                    $effectiveDate = $data['arrPostData']['effective_date'];
                    $otherIncomeAccount = $data['arrPostData']['other_percent_of_income_account'];

                    $incomeRule = $this->tenantDB()->table('income_invoice_rules')->where('soc_id', $data['auth']['soc_id'])
                        ->where('soc_units_type_id', $incomeInvoiceRule->soc_units_type_id)
                        ->where('income_account_id', $otherIncomeAccount)
                        ->where('effective_date', '<=', $effectiveDate)
                        ->orderByDesc('effective_date')
                        ->first();

                    if ($incomeRule && $incomeRule->other_percent_of_income_account == $incomeAccountId) {
                        $data['other_income_deadlock'] = $incomeRule->unit_type;
                    }
                }
            } else {
                // Handle unit types for other income accounts
                $otherIncomeAccount = $data['arrPostData']['other_percent_of_income_account'];
                $otherIncomeDeadlock = '';

                foreach ($data['arrPostData']['unittype'] as $unitType) {
                    $effectiveDate = $data['arrPostData']['effective_date'];
                    $incomeRule = $this->tenantDB()->table('income_invoice_rules')->where('soc_id', $soc_id)
                        ->where('soc_units_type_id', $unitType)
                        ->where('income_account_id', $otherIncomeAccount)
                        ->where('effective_date', '<=', $effectiveDate)
                        ->orderByDesc('effective_date')
                        ->first();

                    if ($incomeRule && $incomeRule->other_percent_of_income_account == $incomeAccount) {
                        $otherIncomeDeadlock .= $incomeRule->unit_type . ',';
                    }
                }

                $otherIncomeDeadlock = trim($otherIncomeDeadlock, ',');
                if (!empty($otherIncomeDeadlock)) {
                    $data['other_income_deadlock'] = $otherIncomeDeadlock;
                }
            }
        }

        return $data;
    }


    public function autoDeactivationRuleDetail($data = [])
    {
        // Initialize $arrRuleDetail with input data
        $arrRuleDetail = $data;
        // Update `arrPostData` values
        $arrRuleDetail['arrPostData']['create_custom_rule'] = 0;
        $arrRuleDetail['arrPostData']['new_incomeaccount'] = '';
        $arrRuleDetail['arrPostData']['effective_date'] = $arrRuleDetail['arrPostData']['effective_date_to'] ?? null;
        $arrRuleDetail['arrPostData']['selected_taxes'] = '';

        // Remove unnecessary keys
        unset(
            $arrRuleDetail['arrPostData']['applicableTaxes'],
            $arrRuleDetail['arrPostData']['taxexemption'],
            $arrRuleDetail['arrPostData']['latepayment'],
            $arrRuleDetail['arrPostData']['select']
        );

        // Default values for `arrPostData`
        $arrRuleDetail['arrPostData'] = array_merge($arrRuleDetail['arrPostData'], [
            'area' => 0.00,
            'open_area' => 0.00,
            'flat_area' => 0.00,
            'noc' => 'simplenoc',
            'noc_tenant' => 0.00,
            'noc_vacant' => 0.00,
            'other_income_fee' => 0.00,
            'other_income_fee_rate' => '',
            'other_percent_of_income_account' => '',
            'selected_tax_exemptions' => '',
        ]);

        // Handle parking unit types if they exist
        if (!empty($arrRuleDetail['arrallparkingunittypes'])) {
            foreach ($arrRuleDetail['arrallparkingunittypes'] as $key => $value) {
                $arrRuleDetail['arrPostData']["ownerparkingid_$key"] = '';
                $arrRuleDetail['arrPostData']["ownertwowheelerparkingamount_$key"] = 0.00;
                $arrRuleDetail['arrPostData']["ownerfourwheelerparkingamount_$key"] = 0.00;

                $arrRuleDetail['arrPostData']["tenantparkingid_$key"] = '';
                $arrRuleDetail['arrPostData']["tenanttwowheelerparkingamount_$key"] = 0.00;
                $arrRuleDetail['arrPostData']["tenantfourwheelerparkingamount_$key"] = 0.00;
            }
        }

        return $arrRuleDetail;
    }


    public function getInvoiceSetting($socId)
    {
        // Use Eloquent's `first` method for retrieving the first matching record
        $invoiceSetting = $this->tenantDB()->table('income_invoice_settings')->where('soc_id', $socId)->first();

        // If found, convert to array; otherwise, return an empty array
        return $invoiceSetting ? $invoiceSetting  : [];
    }

    public function getAllRuledetails($rule_id, $soc_id)
    {

        $arrRuleData = [];
        $arrRuleData['arrIncomeRuleDetails'] = $this->getIncomeinvoicerule($rule_id, $soc_id);

        $arrRuleData['parkingruledetails'] = $this->getParkingRuleDetails($rule_id, $soc_id);
        $arrRuleData['arrFloorBasedRule'] = $this->getFloorBasedRuleDetails($rule_id, $soc_id, array(), 1);

        return $arrRuleData;
    }


    private function getIncomeinvoicerule($id = '', $soc_id)
    {
        $parameters = [];
        $conditions = '';

        if (!empty($id)) {
            $conditions = "id = ?";
            $parameters[] = $id;
        }

        if (!empty($soc_id)) {
            $conditions .= ($conditions != "") ? ' and ' : '';
            $conditions .= "soc_id = ?";
            $parameters[] = $soc_id;
        }

        // Fetch Income Invoice Rule Details
        $incomeInvoiceRuleDetails = $this->tenantDB()
            ->table('income_invoice_rules')
            ->whereRaw($conditions, $parameters)
            ->first();

        $arrInvoiceRuleDetails = [];
        if (!empty($incomeInvoiceRuleDetails)) {
            $arrInvoiceRuleDetails = (array) $incomeInvoiceRuleDetails;

            // Fetch NOC rules associated with the invoice rule
            $nocRules = $this->tenantDB()
                ->table('income_invoice_noc_rules')
                ->where('rule_id', $id)
                ->get()
                ->toArray();

            $arrInvoiceRuleDetails['edit_rule'] = 0;
            $arrInvoiceRuleDetails['noc_rules'] = $nocRules;

            if ($arrInvoiceRuleDetails['effective_date'] > $this->getCurrentDate('database')) {
                $arrInvoiceRuleDetails['edit_rule'] = 1;
            }
        }

        return $arrInvoiceRuleDetails;
    }

    private function getParkingRuleDetails($ruleid, $soc_id)
    {
        // Parking details
        $parkingRuleDetails = $this->tenantDB()
            ->table('income_parking_charges')
            ->where('rule_id', $ruleid)
            ->where('soc_id', $soc_id)
            ->get();

        return $parkingRuleDetails->toArray();
    }

    private function getFloorBasedRuleDetails($ruleid, $soc_id, $fields = [], $status = null)
    {
        // Initialize the query with base conditions
        $query = $this->tenantDB()
            ->table('income_floorbased_charges')
            ->where('rule_id', $ruleid)
            ->where('soc_id', $soc_id)
            ->orderBy('floor_from', 'ASC');

        // Select specific columns if provided
        if (!empty($fields)) {
            $query->select($fields);
        }

        // Add status condition if provided
        if (!is_null($status)) {
            $query->where('status', $status);
        }

        // Execute the query and get the results
        $floorBasedRuleDetails = $query->get();

        return $floorBasedRuleDetails->toArray();
    }

    public function getAllUnitType($socId, $selectPerticular = null)
    {
        // Define constants or fetch from a config file
        $reservedUnits = config('constants.reserved_units');
        unset($reservedUnits['Parking']);
        $reserveAreaUnitType = array_keys($reservedUnits);

        // Build the query
        $query = $this->tenantDB()->table('chsone_society_units_tpl')->where('soc_id', $socId)->where('status', '1');

        // Add conditions based on the `selectPerticular` parameter
        switch ($selectPerticular['selectperticular']) {
            case 'parking':
                $query->where('soc_unit_type', 'parking');
                break;
            case 'notparking':
                $query->whereIn('soc_unit_type', $reserveAreaUnitType);
                break;
        }

        // Fetch the results
        $societyUnits = $query->get();
        // Format the results as needed
        $formattedUnits = [];
        foreach ($societyUnits as $unit) {
            $formattedUnits[$unit->soc_units_type_id] = $unit->type;
        }

        return $formattedUnits;
    }


    public function saveAddRule($data = array())
    {
        //        echo '<pre>'; print_r($data['arrPostData']); exit;
        try {
            $arrResponse = array('error' => 0, 'success' => 0);
            $soc_id = $this->input['company_id'];
            $arrPostData = $data['arrPostData'];
            $auth = $data['auth'] ?? [];
            $arrallunittypes = $data['arrallunittypes'];
            $arrPreviousIncomeTrackerRecord = array();
            $arrPreviousIncomeTrackerRecord = array();
            $arrNewIncomeTrackerRecord = array();
            $arrInvoicechanges = array('changed' => array(), 'previous' => array());
            //Adding Rule details
            $arrRemovedList = array("created_date", "created_by", "updated_date", "updated_by"); // remove this fields while checking new fields
            foreach ($arrPostData['unittype'] as $eachUnitType) {
                $arrincomeaccount = explode("_", $arrPostData['incomeaccount']);

                if (isset($arrPostData['invoiceruleid']) && !empty($arrPostData['invoiceruleid'])) {
                    $conditions = [
                        'id' => $arrPostData['invoiceruleid'],
                        'soc_id' => $soc_id,
                    ];
                    // Fetch the record using query builder
                    $objincomeinvoicerule = IncomeInvoiceRule::where($conditions)->first();
                    // Convert to an array if record exists
                    $arrPreviousIncomeTrackerRecord = $objincomeinvoicerule ? $objincomeinvoicerule->toArray() : [];
                    $this->resetOldValues($objincomeinvoicerule, $arrPostData['type_of_rule']);
                } else {
                    $todaysDate = date('Y-m-d');
                    $effectiveDate = $arrPostData['effective_date'];
                    //dd($arrincomeaccount);
                    if ($effectiveDate > $todaysDate) {
                        $objincomeinvoicerule = IncomeInvoiceRule::where('soc_id', $soc_id)
                            ->where('soc_units_type_id', $eachUnitType)
                            ->where('income_account_id', $arrincomeaccount[0])
                            ->where('effective_date', '>', $todaysDate)
                            ->orderBy('effective_date', 'desc')
                            ->first();
                    } else {
                        $objincomeinvoicerule = IncomeInvoiceRule::where('soc_id', $soc_id)
                            ->where('soc_units_type_id', $eachUnitType)
                            ->where('income_account_id', $arrincomeaccount[0])
                            ->where('effective_date', $arrPostData['effective_date'])
                            ->orderBy('effective_date', 'desc')
                            ->first();
                    }

                    // Fetch the first matching record

                    if (empty($objincomeinvoicerule->id)) {
                        $objincomeinvoicerule = new IncomeInvoiceRule();
                        $objincomeinvoicerule['created_date'] = date('Y-m-d H:i:s');
                        $objincomeinvoicerule['created_by'] = $this->input['user_id'] ?? 0;
                    } else {
                        $this->resetOldValues($objincomeinvoicerule, $arrPostData['type_of_rule']);
                    }
                    ////print_r($objIncomeinvoicerule->toArray());exit;
                    $objincomeinvoicerule->soc_units_type_id = $eachUnitType;
                    //get unit type from chsone_society_units_tpl table
                    $soc_unit_type_name = ChsoneSocietyUnitsTpl::where('soc_id', $soc_id)
                        ->where('soc_units_type_id', $eachUnitType)
                        ->first()->type;

                    $objincomeinvoicerule->unit_type = ($arrincomeaccount[1] == 'Parking') ? 'Parking' : $soc_unit_type_name;
                    $objincomeinvoicerule->income_account_id = $arrincomeaccount[0];
                    ////print_r($objincomeinvoicerule->toArray());exit;

                }

                $rule = explode('|', $arrPostData['type_of_rule']);
                $ruleName = $rule[0];
                $tab = $rule[1];
                $subTab = $rule[2];

                $objincomeinvoicerule->soc_id = $soc_id;
                $objincomeinvoicerule->applicable_taxes = (isset($arrPostData['selected_taxes']) && is_array($arrPostData['selected_taxes']))
                    ? implode(",", $arrPostData['selected_taxes'])
                    : (isset($arrPostData['selected_taxes']) ? $arrPostData['selected_taxes'] : null);

                $objincomeinvoicerule->selection = $arrPostData['type_of_rule'];

                $objincomeinvoicerule->apply_late_payment_interest = (isset($arrPostData['latepayment']) && $arrPostData['latepayment'] == 1) ? 1 : 0;

                //$this->getDatabaseDate($arrPostData['effective_date']);
                $objincomeinvoicerule->effective_date = $arrPostData['effective_date'];

                //code for other charges
                if (isset($arrPostData['other_income_fee']) && !empty($arrPostData['other_income_fee'])) {
                    //$objincomeinvoicerule->other_income_fee = ($arrPostData['other_income_fee_rate'] == 'persqft') ? 0 : $arrPostData['other_income_fee']; suraj test
                    $objincomeinvoicerule->other_income_fee = $arrPostData['other_income_fee'];
                }


                if (isset($arrPostData['other_income_fee_rate']) && !empty($arrPostData['other_income_fee_rate'])) {
                    if (isset($arrPostData['noc'])) {
                        $objincomeinvoicerule->other_income_fee_rate = $arrPostData['noc'];
                    } else
                        $objincomeinvoicerule->other_income_fee_rate = $arrPostData['other_income_fee_rate'];
                }

                //other_income_fee_rate can be set when other fixed/percentage/pertap
                if ($ruleName != 'MaintenanceFee' && $ruleName != 'PropertyTax' && $ruleName != 'Noc' && $ruleName != 'Parking') {
                    if ($subTab == 'Fixed' || $subTab == 'Percentage' || $subTab == 'Per Taps') {
                        //1
                        $objincomeinvoicerule->other_income_fee = $arrPostData['other_income_fee'];
                    } elseif ($subTab == 'persqft') {
                        //2
                        $objincomeinvoicerule->fee_carpet_area = $arrPostData['fee_carpet_area'];
                        //3
                        $objincomeinvoicerule->fee_open_area = $arrPostData['open_area'];
                        $objincomeinvoicerule->other_income_fee = $arrPostData['other_income_fee'];//check for new income account
                    }
                }


                if (isset($arrPostData['other_percent_of_income_account']) && !empty($arrPostData['other_percent_of_income_account'])) { // && $subTab == 'Percentage'
                    $otherChargesIncomeaccountId = $arrPostData['other_percent_of_income_account'];
                    $objincomeinvoicerule->other_percent_of_income_account = ($arrPostData['other_income_fee_rate'] == 'persqft') ? 0 : $otherChargesIncomeaccountId;
                }

                if ($arrincomeaccount[1] == 'Parking') {
                    $rule = "parking";
                } else {
                    $rule = ($arrPostData['selected_tab'] == '') ? 'standard' : $arrPostData['selected_tab'];
                }

                if ($rule == "standard") {
                    $innerRule = $arrPostData['standardrule'];
                } elseif ($rule == "floorbased") {
                    $innerRule = $arrPostData['floorbaserule'];
                }

                if (isset($arrPostData['nocflag'])) {
                    $optionalrule = $arrPostData['nocflag'];
                } else {
                    $optionalrule = '';
                }

                if (isset($arrPostData['noc']) && $arrPostData['noc'] == 'advancenoc') {
                    $arrPostData['noc_owner'] = $arrPostData['noc_tenant'] = $arrPostData['noc_vacant'] = 0;
                }

                $selectedtab = 'standard';
                if ($arrincomeaccount[1] == 'Parking') {
                    $arrPostData['flat_area'] = 0;
                    $arrPostData['area'] = 0;
                    $arrPostData['open_area'] = 0;
                    $selectedtab = 'parking';
                } else {
                    if ($rule == 'standard') {
                        switch ($arrPostData['standardrule']) {
                            case 'sqftarea':
                                $arrPostData['flat_area'] = 0;
                                break;

                            case 'flat':
                                $arrPostData['area'] = 0;
                                $arrPostData['open_area'] = 0;
                                break;

                            default:
                                $arrPostData['flat_area'] = 0;
                                $arrPostData['area'] = 0;
                                $arrPostData['open_area'] = 0;
                        }
                    } elseif ($rule == 'floorbased') {
                        $arrPostData['flat_area'] = 0;
                        $arrPostData['area'] = 0;
                        $arrPostData['open_area'] = 0;
                        $selectedtab = 'floorbased';
                    } else $rule = 'standard';
                }

                $arrPostData['area'] = ($arrPostData['area'] == 0.00 && $arrPostData['nocarea']) ? $arrPostData['nocarea'] : $arrPostData['area'];
                //$arrPostData['open_area'] = (!$arrPostData['open_area'] == 0.00 && $arrPostData['open_nocarea']) ? $arrPostData['open_nocarea'] : $arrPostData['open_area'];

                $objincomeinvoicerule->rule = $rule; //standard,floorbased,parking
                $objincomeinvoicerule->optional_rule = $optionalrule;
                $objincomeinvoicerule->tax_exemptions = isset($arrPostData['selected_tax_exemptions']) ? $arrPostData['selected_tax_exemptions'] : '';
                $ruleName = rtrim($ruleName);
                if ($ruleName == 'MaintenanceFee' || $ruleName == 'PropertyTax') {
                    //echo $ruleName.'|'.$tab.'|'.$subTab.'|';print_r($arrPostData['flat_area']);exit;
                    if ($tab == 'Standard Rule') {
                        if ($subTab == 'persqft') {
                            //2
                            $objincomeinvoicerule->fee_carpet_area = $arrPostData['area'];
                            //3
                            $objincomeinvoicerule->fee_open_area   = $arrPostData['open_area'];
                        } elseif ($subTab == 'Flat Fee') {
                            //4

                            $objincomeinvoicerule->fee_per_unit    = $arrPostData['flat_area'];
                        }
                    }
                }
                if ($ruleName == 'Noc') {
                    if ($subTab == 'Simple NOC') {
                        //6
                        $objincomeinvoicerule->fee_noc_tenant  = $arrPostData['noc_tenant'];
                        //7
                        $objincomeinvoicerule->fee_noc_vacant  = $arrPostData['noc_vacant'];
                    } elseif ($subTab == 'persqft') {
                        //2
                        $objincomeinvoicerule->fee_carpet_area = $arrPostData['nocarea'];
                        //3
                        $objincomeinvoicerule->fee_open_area   = $arrPostData['open_nocarea'];
                    }
                }

                $objincomeinvoicerule->fee_carpet_area = ($arrPostData['other_income_fee_rate'] == 'persqft') ? $arrPostData['other_income_fee'] : $arrPostData['area'];
                //$objincomeinvoicerule->fee_open_area = ($arrPostData['other_income_fee_rate'] == 'persqft') ? $arrPostData['other_open_income_fee'] : $arrPostData['open_area'];


                $objincomeinvoicerule->fee_per_unit = $arrPostData['flat_area'];
                $objincomeinvoicerule->fee_noc_owner = $arrPostData['noc_owner'] ?? '';
                $objincomeinvoicerule->fee_noc_tenant = $arrPostData['noc_tenant'] ?? '';
                $objincomeinvoicerule->fee_noc_vacant = $arrPostData['noc_vacant'] ?? '';
                $totalNOC = $arrPostData['totaladvanceNOC'];
                $allParticulars = array();
                for ($i = 1; $i <= $totalNOC; $i++) {
                    $nocadvancefeearea = isset($arrPostData['noc_extracharge_area_' . $i]) &&
                    in_array($arrPostData['noc_extracharge_area_' . $i], $data['arrextracherge'])
                        ? $arrPostData['noc_extracharge_area_' . $i]
                        : '';

                    if (isset($arrPostData['nocadvamount_' . $i])) {
                        $allParticulars[$nocadvancefeearea] = $arrPostData['nocadvamount_' . $i];

                        if ($nocadvancefeearea != '') {
                            if (isset($arrPostData['noc']) && $arrPostData['noc'] == 'simplenoc') {
                                $arrPostData['nocadvamount_' . $i] = 0;
                            }

                            switch ($nocadvancefeearea) {
                                case 'Electricity':
                                    $noc_advance_electricity = $arrPostData['nocadvamount_' . $i];
                                    break;

                                case 'Water':
                                    $noc_advance_water = $arrPostData['nocadvamount_' . $i];
                                    break;

                                case 'Maintenance':
                                    $noc_advance_maintenance = $arrPostData['nocadvamount_' . $i];
                                    break;
                            }
                        }
                    }
                }

                $objincomeinvoicerule->fee_noc_electricity = isset($noc_advance_electricity) ? $noc_advance_electricity : 0;
                $objincomeinvoicerule->fee_noc_water = isset($noc_advance_water) ? $noc_advance_water : 0;
                $objincomeinvoicerule->fee_noc_maintenance = isset($noc_advance_maintenance) ? $noc_advance_maintenance : 0;

                //suraj
                $objincomeinvoicerule->fee_carpet_area = ($arrPostData['other_income_fee_rate'] == 'persqft') ? $arrPostData['other_income_fee'] : $arrPostData['area'];
                $objincomeinvoicerule->fee_open_area = ($arrPostData['other_income_fee_rate'] == 'persqft') ? $arrPostData['other_open_income_fee'] : $arrPostData['open_area'];

                if (isset($arrPostData['invoiceruleid']) && !empty($arrPostData['invoiceruleid'])) {
                    /****************************New Record details **************************/
                    if (!empty($arrPreviousIncomeTrackerRecord) && !empty($arrPreviousIncomeTrackerRecord)) {
                        $arrNewIncomeTrackerRecord = $objincomeinvoicerule->toArray();

                        $arrPreviouschanges = array_diff_assoc($arrPreviousIncomeTrackerRecord, $arrNewIncomeTrackerRecord);

                        $arrNewchanges = array_diff_assoc($arrNewIncomeTrackerRecord, $arrPreviousIncomeTrackerRecord);

                        foreach ($arrRemovedList as $k => $v) {
                            unset($arrNewchanges[$v]);
                            unset($arrPreviouschanges[$v]);
                        } //end of foreach

                        //Removeing all changes which are not added like 0.00 == 0.000
                        foreach ($arrNewchanges as $k => $v) {
                            if (isset($arrPreviouschanges[$k])) {
                                if ($arrNewchanges[$k] == $arrPreviouschanges[$k]) {
                                    unset($arrNewchanges[$k]);
                                    unset($arrPreviouschanges[$k]);
                                }
                            }
                        }

                        if ($rule == 'standard') {
                            if (isset($arrNewchanges['fee_per_unit']) && (isset($arrNewchanges['fee_per_unit']) || isset($arrNewchanges['fee_per_unit']))) {
                                if ($arrPostData['standardrule'] == 'sqftarea') {
                                    $arrNewchanges['standard_rule'] = 'Per Sqft Area';
                                    $arrPreviouschanges['standard_rule'] = 'Flat Fee';
                                } else {
                                    $arrPreviouschanges['standard_rule'] = 'Per Sqft Area';
                                    $arrNewchanges['standard_rule'] = 'Flat Fee';
                                }
                            }

                            /*if (isset($arrPreviouschanges['rule'])) {
                                unset($arrNewchanges['standard_rule']);
                                unset($arrPreviouschanges['standard_rule']);
                            } // unset due to whole rule changed*/
                        }
                        if (!empty($arrPreviouschanges) && !empty($arrNewchanges)) {
                            array_push($arrInvoicechanges['changed'], $arrNewchanges);
                            array_push($arrInvoicechanges['previous'], $arrPreviouschanges);
                        }
                    }
                    /*************************** End New record Details *********************/
                }

                $objincomeinvoicerule->updated_date = date('Y-m-d H:i:s');
                $objincomeinvoicerule->updated_by = $this->input['user_id'] ?? 0;

                if ($objincomeinvoicerule->save()) {
                    //if ($ruleName == 'Noc' && $subTab == 'Advance NOC') {
                    foreach ($allParticulars as $k => $v) {
                        $condition = [
                            ['soc_id', '=', $soc_id],
                            ['rule_id', '=', $objincomeinvoicerule->id],
                            ['particular', '=', $k]
                        ];

                        // Check if a record exists
                        $nocRule = $this->tenantDB()->table('income_invoice_noc_rules')
                            ->where($condition)
                            ->first();

                        if ($nocRule) {
                            // Update existing record
                            $nocRule->soc_id = $objincomeinvoicerule->soc_id;
                            $nocRule->rule_id = $objincomeinvoicerule->id;
                            $nocRule->particular = $k;
                            $nocRule->particular_id = IncomeAccount::where('account_name', $k)
                                ->where('soc_id', $objincomeinvoicerule->soc_id)
                                ->value('account_id');
                            $nocRule->amount = $v;

                            if (!$nocRule->save()) {
                                return false;
                                //print_r($nocRule->getMessages()); exit;
                            }
                        } else {
                            // Insert new record
                            $conditions = [
                                ['soc_id', '=', $objincomeinvoicerule->soc_id],
                                ['rule_id', '=', $objincomeinvoicerule->id],
                            ];

                            // Count the records
                            $nocRuleCount = IncomeInvoiceNocRule::where($conditions)->count();
                            $i = 0;

                            if ($nocRuleCount > 0) {
                                // Fetch the records
                                $nocRules = IncomeInvoiceNocRule::where($conditions)->get();

                                foreach ($nocRules as $nocRule) {
                                    $nocRule->amount = 0.00;

                                    // Save and count the successful saves
                                    if ($nocRule->save()) {
                                        $i++;
                                    }
                                }
                            }
                        }
                    }
                    //}


                    $arrNewlyAddedFloorDetails = array();
                    $arrFlorrBasedNewAdded = array();
                    $arrRemovedFloorDetails1 = array();
                    $arrRemovedFloorDetails = array();

                    if ($selectedtab == 'floorbased') {
                        //Adding Floor based rule
                        if ($arrPostData['floorbaserule'] == "slabbaserule") {
                            $iteration = $arrPostData['totalslabbaserecord'];
                            $floor_id = 'slabbasedfloorid_';
                            $ffloor_to = 'floors_to_';
                            $floor_from = 'floors_from_';
                            $amount = 'slab_based_amount_';
                            $floorbasedselected = 'slab_based';
                            $keyFrom = "floor_from";
                            $keyTo = "floor_to";
                            $keyFee = "fee_carpet_area";
                        } else {
                            $iteration = $arrPostData['totalsimplefloorbase'];
                            $floor_id = 'floorid_';
                            $floor_from = 'floors_';
                            $amount = 'floors_amount_charge_';
                            $floorbasedselected = 'simple_multiplier';
                            $keyFrom = "floor_from";
                            $keyTo = "floor_to";
                            $keyFee = "fee_carpet_area";
                        }

                        //get previous inserted Id
                        $arrFloorBasedID = $this->getFloorBasedRuleDetails($arrPostData['invoiceruleid'], $soc_id, array('id'), 1);
                        $arrDBFloorInsertID = $this->_getFloorRecords($arrFloorBasedID);
                        $arrNewlyInsertID = array();
                        $arrPreviousFloorDetails = array();
                        $arrFloorbasedDetailsChanges = array('changed' => array(), 'previous' => array());
                        $arrNewFloorDetails = array();
                        $countRotate = 0;
                        if (isset($objincomeinvoicerule->id)) {
                            for ($i = 1; $i <= $iteration; $i++) {
                                $floor_to = ($arrPostData['floorbaserule'] == "slabbaserule") ? $arrPostData[$ffloor_to . $i] : '';
                                $conditions = "";
                                $parameters = array();
                                $objfloorbaserule = null; // Initialize explicitly
                                $arrPreviousFloorDetails = [];
                                $newly_added = 0;

                                // Existing record check
                                if ($arrPostData[$floor_id . "" . $i]) {
                                    $conditions = [
                                        'id' => $arrPostData[$floor_id . $i],
                                        'soc_id' => $soc_id,
                                    ];

                                    $objfloorbaserule = $this->tenantDB()->table('income_floorbased_charges')
                                        ->where($conditions)
                                        ->first();

                                    if ($objfloorbaserule) {
                                        $arrPreviousFloorDetails = (array)$objfloorbaserule;
                                    }
                                } else {
                                    if (isset($arrPostData[$floor_from . '' . $i]) && $arrPostData[$floor_from . '' . $i] != '') {
                                        $query = $this->tenantDB()->table('income_floorbased_charges')
                                            ->where('rule_id', $objincomeinvoicerule->id)
                                            ->where('soc_id', $soc_id)
                                            ->where('floor_from', $arrPostData[$floor_from . $i]);

                                        if ($arrPostData['floorbaserule'] === "slabbaserule") {
                                            $query->where('floor_to', $floor_to);
                                        }
                                        $objfloorbaserule = $query->first();
                                    }
                                }

                                // Handle new records
                                if (empty($objfloorbaserule)) {
                                    $arrFlorrBasedNewAddedTemp = [
                                        'rule_id' => $objincomeinvoicerule->id,
                                        'soc_id' => $soc_id,
                                        'floor_from' => $arrPostData[$floor_from . $i],
                                        'floor_to' => ($arrPostData['floorbaserule'] === "slabbaserule") ? $floor_to : 0,
                                        'fee_carpet_area' => $arrPostData[$amount . $i],
                                        'status' => 1,
                                        'created_date' => now()->toDateTimeString(),
                                        'created_by' => $this->input['user_id'] ?? 0,
                                        'updated_date' => now()->toDateTimeString(),
                                        'updated_by' => $this->input['user_id'] ?? 0,
                                    ];

                                    // FIXED: Insert and get ID
                                    $insertedId = $this->tenantDB()->table('income_floorbased_charges')
                                        ->insertGetId($arrFlorrBasedNewAddedTemp);

                                    if ($insertedId) {
                                        $objfloorbaserule = (object)$arrFlorrBasedNewAddedTemp;
                                        $objfloorbaserule->id = $insertedId;
                                        $newly_added = 1;
                                        $arrFlorrBasedNewAdded[] = $arrFlorrBasedNewAddedTemp;
                                    }
                                } else {
                                    // FIXED: Update existing record
                                    $updateData = [
                                        'floor_from' => $arrPostData[$floor_from . $i],
                                        'floor_to' => ($arrPostData['floorbaserule'] === "slabbaserule") ? $floor_to : 0,
                                        'fee_carpet_area' => $arrPostData[$amount . $i],
                                        'updated_date' => now()->toDateTimeString(),
                                        'updated_by' => $this->input['user_id'] ?? 0,
                                        'created_by' => $this->input['user_id'] ?? 0,
                                        'created_date' => now()->toDateTimeString(),
                                        'status' => 1
                                    ];

                                    $this->tenantDB()->table('income_floorbased_charges')
                                        ->where('id', $objfloorbaserule->id)
                                        ->update($updateData);

                                    // Keep object updated
                                    $objfloorbaserule = (object)array_merge((array)$objfloorbaserule, $updateData);
                                }

                                // Change tracking logic (preserved)
                                if (isset($arrPostData[$floor_from . '' . $i]) && isset($arrPostData[$amount . '' . $i])) {
                                    /***************************Start change tracking*********************/
                                    $arrNewFloorChangesKeys = [];
                                    $arrPreviousFloorChangesKeys = [];
                                    $arrNewFloorDetails = json_decode(json_encode($objfloorbaserule), true);

                                    if (!empty($arrPreviousFloorDetails)) {
                                        $arrNewFloorChangesKeys = array_diff_assoc($arrNewFloorDetails, $arrPreviousFloorDetails);
                                        $arrPreviousFloorChangesKeys = array_diff_assoc($arrPreviousFloorDetails, $arrNewFloorDetails);
                                    }

                                    foreach ($arrRemovedList as $k => $v) {
                                        unset($arrNewFloorChangesKeys[$v]);
                                        unset($arrPreviousFloorChangesKeys[$v]);
                                    }

                                    foreach ($arrNewFloorChangesKeys as $k => $v) {
                                        if (isset($arrPreviousFloorChangesKeys[$k])){
                                            if ($arrNewFloorChangesKeys[$k] == $arrPreviousFloorChangesKeys[$k]) {
                                                unset($arrNewFloorChangesKeys[$k]);
                                                unset($arrPreviousFloorChangesKeys[$k]);
                                            }
                                        }
                                    }

                                    if (!empty($arrNewFloorChangesKeys) && !empty($arrPreviousFloorChangesKeys) && $newly_added == 0) {
                                        $arrFloorbasedDetailsChanges['changed'][] = [
                                            'from_floor' => $arrNewFloorDetails['floor_from'],
                                            'floor_to' => $arrNewFloorDetails['floor_to'],
                                            'fee_carpet_area' => $arrNewFloorDetails['fee_carpet_area']
                                        ];
                                        $arrFloorbasedDetailsChanges['previous'][] = [
                                            'from_floor' => $arrPreviousFloorDetails['floor_from'],
                                            'floor_to' => $arrPreviousFloorDetails['floor_to'],
                                            'fee_carpet_area' => $arrPreviousFloorDetails['fee_carpet_area']
                                        ];
                                    }
                                    /***************************End change tracking*********************/

                                    $arrNewlyInsertID[] = $objfloorbaserule->id;
                                }
                            }
                        }
                        $arrDeletingRecords = array_diff($arrDBFloorInsertID, $arrNewlyInsertID);
                        $arrRemovedFloorDetails[] = $this->_removeFloorBasedDetails($arrDeletingRecords, $auth);
                    } else {
                        $arrFloorBasedID = $this->getFloorBasedRuleDetails($arrPostData['invoiceruleid'], $soc_id, array('id'), 1);
                        $arrFloorDeletingID = $this->_getFloorRecords($arrFloorBasedID);

                        $arrRemovedFloorDetails1[] = $this->_removeFloorBasedDetails($arrFloorDeletingID, $auth);
                    } //ends Floor

                    if ($rule == 'floorbased') {
                        if ($arrPostData['floorbaserule'] == "slabbaserule") {
                            $arrNewchanges['floorbase_rule'] = 'Slab based';
                            $arrPreviouschanges['floorbase_rule'] = 'Simple Multiplier';
                        } else {
                            $arrPreviouschanges['floorbase_rule'] = 'Simple Multiplier';
                            $arrNewchanges['floorbase_rule'] = 'Slab based';
                        }
                        /*if ($arrPreviouschanges['rule']) {
                            unset($arrNewchanges['standard_rule']);
                            unset($arrPreviouschanges['standard_rule']);
                        } // unset due to whole rule changed*/
                    }
                    $arrAllRemovedFloorDetails = array();
                    if (is_array($arrRemovedFloorDetails1) && is_array($arrRemovedFloorDetails)) {
                        $arrAllRemovedFloorDetails = array_merge($arrRemovedFloorDetails1, $arrRemovedFloorDetails);
                    }

                    ////echo"<pre> New Added ";//print_r($arrFlorrBasedNewAdded);//echo"<br>Removed ";//print_r($arrAllRemovedFloorDetails);//echo"Changed Array";//print_r($arrFloorbasedDetailsChanges);exit();

                    //Adding Parking data
                    if ($arrPostData['IsParking'] == 'Parking') {
                        $arrParkingDetailsChanges = array("changed" => array(), "previous" => array());
                        $arrPreviousParkingChangesKeys = array();
                        $arrNewParkingChangesKeys = array();
                        $arrNewParkingRecords = array();
                        $arrPreviousParkingRecords = array();
                        $arrallparkingunittypes = $data['arrallparkingunittypes'];

                        for ($count = 1; $count <= $arrPostData['vehicle_count']; $count++) {

                            foreach ($data['arrparkingmembertype'] as $keyparkmembertype => $valueparkmembertype) {
                                foreach ($data['arrallparkingunittypes'] as $keyparkingtype => $valueparkingtype) {
                                    $parking1 = $keyparkmembertype . "parkingid_$keyparkingtype-$count";
                                    $twowheelerparking1 = $keyparkmembertype . "twowheelerparkingamount_$keyparkingtype-$count";
                                    $fourwheelerparking1 = $keyparkmembertype . "fourwheelerparkingamount_$keyparkingtype-$count";

                                    if (isset($arrPostData[$parking1]) && $arrPostData[$parking1] != '') {
                                        dd($arrPostData);
                                        $conditions = [
                                            'id' => $arrPostData[$keyparkmembertype . "parkingid_$keyparkingtype-$count"],
                                            'soc_id' => $soc_id,
                                        ];

                                        $objparkingrule = IncomeParkingCharge::where($conditions)
                                            ->first();

                                        $arrPreviousParkingRecords = $objparkingrule ? (array)$objparkingrule : [];
                                    } else {
                                        $objparkingrule = new IncomeParkingCharge();
                                        $objparkingrule->created_date = date("Y-m-d H:i:s");
                                        $objparkingrule->created_by = $this->input['user_id'] ?? 0;
                                    }
                                    //dd($soc_id);
                                    // Check what the value is before this line
                                    $objparkingrule->soc_id = $soc_id;

                                    $objparkingrule->rule_id = $objincomeinvoicerule->id;
                                    $objparkingrule->parking_type = $arrallparkingunittypes[$keyparkingtype];
                                    if (isset($arrPostData[$twowheelerparking1]) && isset($arrPostData[$fourwheelerparking1])) {
                                        $objparkingrule->fee_2_wheeler = $arrPostData[$twowheelerparking1];
                                        $objparkingrule->fee_4_wheeler = $arrPostData[$fourwheelerparking1];
                                    } else {
                                        $objparkingrule->fee_2_wheeler = 0;
                                        $objparkingrule->fee_4_wheeler = 0;
                                    }

                                    $objparkingrule->vehicle_number = $count;
                                    $objparkingrule->member_type = $keyparkmembertype;
                                    $objparkingrule->updated_date = date('Y-m-d H:i:s');
                                    $objparkingrule->updated_by = $this->input['user_id'] ?? 0;
                                    $objparkingrule->status = 1;
                                    $arrNewParkingRecords = $objparkingrule->toArray();
                                    /***********************************************************************************************/
                                    $arrNewParkingChangesKeys = array_diff_assoc($arrNewParkingRecords, $arrPreviousParkingRecords);
                                    $arrPreviousParkingChangesKeys = array_diff_assoc($arrPreviousParkingRecords, $arrNewParkingRecords);

                                    foreach ($arrRemovedList as $k => $v) {
                                        unset($arrNewParkingChangesKeys[$v]);
                                        unset($arrPreviousParkingChangesKeys[$v]);
                                    } //end of foreach

                                    //Removeing all changes which are not added changedlike 0.00 == 0.000
                                    foreach ($arrNewParkingChangesKeys as $k => $v) {
                                        if (isset($arrPreviousParkingChangesKeys[$k])) {
                                            if ($arrNewParkingChangesKeys[$k] == $arrPreviousParkingChangesKeys[$k]) {
                                                unset($arrNewParkingChangesKeys[$k]);
                                                unset($arrPreviousParkingChangesKeys[$k]);
                                            }
                                        }
                                    }
                                    foreach ($arrNewParkingChangesKeys as $key => $value) {
                                        unset($arrNewParkingChangesKeys[$key]);
                                        $arrNewParkingChangesKeys[$keyparkmembertype . "_" . $key] = $value;
                                    }
                                    foreach ($arrPreviousParkingChangesKeys as $key => $value) {
                                        unset($arrPreviousParkingChangesKeys[$key]);
                                        $arrPreviousParkingChangesKeys[$keyparkmembertype . "_" . $key] = $value;
                                    }
                                    foreach ($arrNewParkingChangesKeys as $key => $value) {
                                        unset($arrNewParkingChangesKeys[$key]);
                                        $arrNewParkingChangesKeys[$key] = $value;
                                    }
                                    if (!empty($arrNewParkingChangesKeys) && !empty($arrPreviousParkingChangesKeys)) {
                                        array_push($arrParkingDetailsChanges['changed'], $arrNewParkingChangesKeys);
                                        array_push($arrParkingDetailsChanges['previous'], $arrPreviousParkingChangesKeys);
                                    }
                                    /**********************************************************************************************/
                                    if (!$objparkingrule->Save()) {
                                        //print_r($objparkingrule->getMessages());exit();
                                    } else {
                                    }
                                }
                            }
                        }
                    }

                    $arrFinalBeforeChangeLogArray = array();
                    $arrFinalAfterChangeLogArray = array();

                    if (isset($arrPostData['invoiceruleid']) && !empty($arrPostData['invoiceruleid'])) {
                        $changeaddlogflag = 0;
                        if (!empty($arrInvoicechanges['changed']) || !empty($arrInvoicechanges['previous'])) {
                            array_push($arrFinalBeforeChangeLogArray, $arrInvoicechanges['previous']);
                            array_push($arrFinalAfterChangeLogArray, $arrInvoicechanges['changed']);
                            $changeaddlogflag = 1;
                        }
                        if (!empty($arrFloorbasedDetailsChanges['changed']) && !empty($arrFloorbasedDetailsChanges['previous'])) {
                            foreach ($arrFloorbasedDetailsChanges['previous'] as $k => $v) {
                                if (is_array($v)) {
                                    array_push($arrFinalBeforeChangeLogArray, $v);
                                } else {
                                    array_push($arrFinalBeforeChangeLogArray, $arrFloorbasedDetailsChanges['previous'][$k]);
                                }
                            }
                            foreach ($arrFloorbasedDetailsChanges['changed'] as $k => $v) {
                                if (is_array($v)) {
                                    array_push($arrFinalAfterChangeLogArray, $v);
                                } else {
                                    array_push($arrFinalAfterChangeLogArray, $arrFloorbasedDetailsChanges['changed'][$k]);
                                }
                            }
                            //array_push($arrFinalBeforeChangeLogArray,$arrFloorbasedDetailsChanges['previous']);
                            //array_push($arrFinalAfterChangeLogArray,$arrFloorbasedDetailsChanges['changed']);
                            $changeaddlogflag = 1;
                        }
                        if (!empty($arrParkingDetailsChanges['changed']) && !empty($arrParkingDetailsChanges['previous'])) {
                            array_push($arrFinalBeforeChangeLogArray, $arrParkingDetailsChanges['previous']);
                            array_push($arrFinalAfterChangeLogArray, $arrParkingDetailsChanges['changed']);
                            $changeaddlogflag = 1;
                        }

                        if (!empty($arrAllRemovedFloorDetails)) {
                            array_push($arrFinalBeforeChangeLogArray, $arrAllRemovedFloorDetails);
                            $changeaddlogflag = 1;
                        }

                        if (!empty($arrFlorrBasedNewAdded)) {
                            array_push($arrFinalAfterChangeLogArray, $arrFlorrBasedNewAdded);
                            $changeaddlogflag = 1;
                        }

                        if ($changeaddlogflag == 1) {

                            /*$arrFinalBeforeChangeLogArray = $this->_findChangeLogKey($arrFinalBeforeChangeLogArray, 'other_percent_of_income_account', $data['arrAllIncomeAccounts']);
                            $arrFinalAfterChangeLogArray = $this->_findChangeLogKey($arrFinalAfterChangeLogArray, 'other_percent_of_income_account', $data['arrAllIncomeAccounts']);
                            $insertData = [
                                'soc_id' => $soc_id,
                                'rule_id' => $objincomeinvoicerule->id,
                                'changed_on' => date('Y-m-d H:i:s'), // Current date and time
                                'changed_by' => $this->input['user_id'] ?? 0,
                                'before_change' => json_encode($arrFinalBeforeChangeLogArray),
                                'after_change' => json_encode($arrFinalAfterChangeLogArray),
                            ];

                            $insertResult = $this->tenantDB()
                                ->table('invoice_change_logs')
                                ->insert($insertData);

                            if (!$insertResult) {
                                $arrResponse = ['error' => 1, 'message' => 'Failed to insert the change log.'];
                            }*/
                        }
                    }
                    $arrResponse = array('success' => 1, 'message' => 'Record Added Successfully');
                } else {
                    $arrResponse = array('error' => 1, 'message' => $objincomeinvoicerule->getMessages());
                }
            }
            return $arrResponse;
        } catch (\Exception $e) {
            return $arrResponse = array('error' => 1, 'message' => $e->getMessage());
        }
    }

    function convertPayload($payload)
    {
        $arrPostData = []; // Initialize the array for posted data
        $vehicleCounter = 1; // Counter for dynamic suffixes
        foreach ($payload['vehicles'] as $vehicle) {
            foreach (['owner', 'tenant'] as $type) {
                foreach (['open_parking', 'shaded_parking'] as $parkingType) {
                    $suffix = $parkingType === 'open_parking' ? "6-{$vehicleCounter}" : "7-{$vehicleCounter}";

                    // Add parking ID key
                    $parkingIdKey = "{$type}parkingid_{$suffix}";
                    $arrPostData[$parkingIdKey] = $parkingType === 'shaded_parking' ? "" : "";

                    // Add two-wheeler parking amount key
                    $twoWheelerKey = "{$type}twowheelerparkingamount_{$suffix}";
                    $arrPostData[$twoWheelerKey] = $vehicle[$type][$parkingType]['two_wheeler'];

                    // Add four-wheeler parking amount key
                    $fourWheelerKey = "{$type}fourwheelerparkingamount_{$suffix}";
                    $arrPostData[$fourWheelerKey] = $vehicle[$type][$parkingType]['four_wheeler'];
                }
            }
            $vehicleCounter++; // Increment the counter for the next vehicle
        }
        $arrPostData['vehicle_count'] = count($payload['vehicles']);
        return $arrPostData; // Return the array containing the posted data
    }



    /**
     * Find change log key
     * @method  _findChangeLogKey
     * @access private
     * @param type $event
     * @param type $component
     * @param array $data
     * @return array
     */
    private function _findChangeLogKey($array, $keySearch, $arrAllIncomeAccounts)
    { // check whether input is an array
        if (is_array($arrAllIncomeAccounts)) {
            foreach ($array as $firstkey => $firstvalue) {
                foreach ($firstvalue as $secondkey => $secondvalue) {
                    foreach ($secondvalue as $key => $value) {
                        //adding new key for other income account name
                        if ($key == $keySearch) {

                            $array[$firstkey][$secondkey]['other_income_account_name'] = ltrim(preg_replace('/[A-Z]/', ' $0', $arrAllIncomeAccounts[$value]));
                        }
                        if ($key == 'selection') {
                            unset($array[$firstkey][$secondkey]['selection']);
                        }
                    }
                }
            }
            return $array;
        }
    }

    public function resetOldValues($objincomeinvoicerule, $type)
    {
        $objincomeinvoicerule->other_income_fee    = 0.00;
        $objincomeinvoicerule->fee_carpet_area     = 0.00;
        $objincomeinvoicerule->fee_open_area       = 0.00;
        $objincomeinvoicerule->fee_per_unit        = 0.00;
        $objincomeinvoicerule->fee_noc_owner       = 0.00;
        $objincomeinvoicerule->fee_noc_tenant      = 0.00;
        $objincomeinvoicerule->fee_noc_vacant      = 0.00;
        $objincomeinvoicerule->fee_noc_electricity = 0.00;
        $objincomeinvoicerule->fee_noc_water       = 0.00;
        $objincomeinvoicerule->fee_noc_maintenance = 0.00;
        $type = explode('|', $type);

        if (isset($objincomeinvoicerule->id)) {
            $objincomeinvoicerule = IncomeInvoiceNocRule::where('rule_id', $objincomeinvoicerule->id)->first();
            if ($objincomeinvoicerule) {
                if ($type[0] == 'Noc') {
                    if ($type[2] == 'Advance NOC') {
                        $advnocs = IncomeInvoiceNocRule::where('rule_id', $objincomeinvoicerule->id)->get();

                        foreach ($advnocs as $advnoc) {
                            $advnoc->amount = 0.00;
                            $advnoc->save();
                        }
                    }
                }

                $objincomeinvoicerule->save();
                return true;
            }
        }
    }

    public function _removeFloorBasedDetails($arrDeletingRecords, $auth)
    {
        $arrdeleteFloorBased = [];
        $soc_id = $this->input['company_id'];
        if (!empty($arrDeletingRecords)) {
            foreach ($arrDeletingRecords as $value) {
                // Use Eloquent to find the record by ID and soc_id
                $objfloorbaserule = IncomeFloorbasedCharge::where('id', $value)
                    ->where('soc_id', $soc_id)
                    ->first();

                if ($objfloorbaserule) {
                    // Update fields
                    $objfloorbaserule->status = 0;
                    $objfloorbaserule->updated_date = date('Y-m-d H:i:s');
                    $objfloorbaserule->updated_by = $soc_id;

                    // Collect the data to be returned
                    $arrFloorbaserule = $objfloorbaserule->toArray();
                    $arrdeleteFloorBased[] = [
                        "Floor From" => $arrFloorbaserule['floor_from'],
                        "Floor To" => $arrFloorbaserule['floor_to'] ?? '',
                        "Fee per Month" => $arrFloorbaserule['fee_carpet_area']
                    ];

                    // Save the updated record
                    if (!$objfloorbaserule->save()) {
                        // Handle any save failure if needed
                        // return false;
                    }
                }
            }
        }

        return $arrdeleteFloorBased;
    }


    public function _getFloorRecords($arrFloorBasedID)
    {
        $arrDBFloorInsertID = array();
        $arrFloorBasedID = json_decode(json_encode($arrFloorBasedID), true);
        foreach ($arrFloorBasedID as $k => $val) {
            $arrDBFloorInsertID[] = $val['id'];
        }
        return $arrDBFloorInsertID;
    }

    public function saveIncomeAccount($data = [])
    {
        $arrReturnSuccess = [];
        $arrReturnSuccess['success'] = 1;
        // Add member income account
        if (!empty($data['postdata']['select']['member'])) {
            $arrReturnSuccess = $this->_memberIncomeAccount($data);
        }

        // Add non-member account
        if (
            !empty($data['postdata']['select']['nonmember']['bankaccount'])
            || !empty($data['postdata']['select']['nonmember']['cashaccount'])
            || !empty($data['postdata']['newnonmemberincome'])
        ) {

            $memberSuccessFlag = $arrReturnSuccess['success'];
            $arrReturnSuccess = $this->_nonMemberIncomeAccount($data);
            if ($arrReturnSuccess['success'] == 1) {
                $arrReturnSuccess['success'] = $memberSuccessFlag;
            }
        }

        // Handle transaction commit/rollback
        if ($arrReturnSuccess['success'] == 1) {
            return 1; // Commit successful changes
        } else {
            return 0; // Rollback due to failure
        }
    }

    private function _memberincomeaccount($data)
    {
        //Add into table IncomeAccounts for memeber income
        $soc_id = $this->input['company_id'];
        if (!empty($data['postdata']['select']['member']['bankaccount'])) {
            foreach ($data['postdata']['select']['member']['bankaccount'] as $key => $value) {
                //get first ledger details for key
                $objledgerdetails = $this->getGroupId($key, FALSE);
                if (!empty($objledgerdetails)) {
                    $arrledgerdetails = json_decode(json_encode($objledgerdetails), true);
                    $ledger_account_id = $arrledgerdetails['ledger_account_id'];
                } else {
                    if (strtolower($key) == 'sinkingfund') {
                        $groupledger = $this->tenantDB()->table('chsone_grp_ledger_tree')
                            ->where([
                                ['ledger_account_name', '=', 'liability'],
                                ['entity_type', '=', 'main'],
                                ['behaviour', '=', 'liability'],
                                ['soc_id', '=', $soc_id],
                                ['context', '=', 'liability']
                            ])
                            ->first();

                        if ($groupledger) {
                            $arrgroupledger = (array)$groupledger; // Convert to array if necessary
                        } else {
                            $arrgroupledger = [];
                        }

                        $ledger_account_id = $this->manipulate($key, 'ledger', "", $arrgroupledger['ledger_account_id'], $arrgroupledger['behaviour'], '', 0, '', date("Y-m-d"), $arrgroupledger['operating_type'], $arrgroupledger['context']);
                    } else {
                        $groupledger = $this->tenantDB()->table('chsone_grp_ledger_tree')
                            ->where([
                                ['entity_type', '=', 'group'],
                                ['behaviour', '=', 'income'],
                                ['soc_id', '=', $soc_id],
                                ['context', '=', 'membercontri']
                            ])
                            ->first();

                        if ($groupledger) {
                            $arrgroupledger = (array)$groupledger; // Convert to an array if necessary
                        } else {
                            $arrgroupledger = [];
                        }

                        $ledger_account_id = $this->manipulate($key, 'ledger', "", $arrgroupledger['ledger_account_id'], "income", '', 0, '', date("Y-m-d"), 'direct', 'membercontri');
                    }
                }

                // Check if income account exists
                $existingIncomeAccount = $this->tenantDB()
                    ->table('income_accounts')
                    ->where('soc_id', $soc_id)
                    ->where('account_name', $key)
                    ->where('account_type', 'member')
                    ->first();

                if (!$existingIncomeAccount) {
                    // Prepare new record data
                    $incomeAccountData = [
                        'created_date' => date('Y-m-d H:i:s'),
                        'account_name' => $key,
                        'display_name' => $key,
                        'soc_id' => $soc_id,
                        'fk_income_ledger_id' => $ledger_account_id,
                        'fk_ledger_id' => $value,
                        'fk_cash_ledger_id' => $data['postdata']['select']['member']['cashaccount'][$key],
                        'account_type' => 'member',
                        'created_by' => $this->input['user_id'] ?? 0,
                        'updated_date' => date('Y-m-d H:i:s'),
                        'updated_by' => $this->input['user_id'] ?? 0
                    ];

                    // Insert the new income account
                    $insertedId = $this->tenantDB()
                        ->table('income_accounts')
                        ->insertGetId($incomeAccountData);

                    // Get the maximum display_order_id and increment it
                    $maxDisplayOrder = $this->tenantDB()
                        ->table('income_accounts')
                        ->max('display_order_id');

                    $newDisplayOrder = ($maxDisplayOrder ?? 0) + 1; // If null, set to 1

                    // Update the newly inserted row with the calculated display_order_id
                    $this->tenantDB()
                        ->table('income_accounts')
                        ->where('account_id', $insertedId)
                        ->update(['display_order_id' => $newDisplayOrder]);

                    if (!$insertedId) {
                        $arrfinal['success'] = 0;
                        $arrfinal['getMessages'] = 'Failed to save income account.';
                        return $arrfinal;
                    }
                } else {
                    // Update existing record
                    $incomeAccountData = [
                        'fk_income_ledger_id' => $ledger_account_id,
                        'fk_ledger_id' => $value,
                        'fk_cash_ledger_id' => $data['postdata']['select']['member']['cashaccount'][$key],
                        'updated_date' => date('Y-m-d H:i:s'),
                        'updated_by' => $this->input['user_id'] ?? 0
                    ];

                    $updateResult = $this->tenantDB()
                        ->table('income_accounts')
                        ->where('account_id', $existingIncomeAccount->account_id)
                        ->update($incomeAccountData);

                    if (!$updateResult) {
                        $arrfinal['success'] = 0;
                        $arrfinal['getMessages'] = 'Failed to update income account.';
                        return $arrfinal;
                    }
                }

                // Commit the transaction and return success
                $arrfinal['success'] = 1;
                return $arrfinal;
            }
        }
    }

    public function getGroupId($grpname, $cntxt = false, $soc_id = '')
    {
        $soc_id = $soc_id ? $soc_id : $this->input['company_id'];
        // Initialize query conditions
        $query = $this->tenantDB()
            ->table('chsone_grp_ledger_tree')
            ->where('soc_id', $soc_id);

        // Add condition based on context or ledger_account_name
        if ($cntxt) {
            $query->whereRaw('LOWER(context) = ?', [strtolower($grpname)]);
        } else {
            $query->whereRaw('LOWER(ledger_account_name) = ?', [strtolower($grpname)]);
        }

        // Execute query
        $parent = $query->first();

        // Return result or false
        return $parent ? $parent : false;
    }


    public function getIncomeAccountDetails($data = [])
    {
        $soc_id = $this->input['company_id'];
        $query = $this->tenantDB()
            ->table('income_accounts')
            ->where('soc_id', $soc_id);

        // Add conditions dynamically
        if (isset($data['postdata']['account_type'])) {
            $query->where('account_type', $data['postdata']['account_type']);
        }

        if (isset($data['postdata']['account_name'])) {
            $query->where('account_name', $data['postdata']['account_name']);
        }

        // Retrieve the first matching record
        $incomeAccount = $query->first();

        // Convert to array and return, or return an empty array if no record is found
        return $incomeAccount ? (array)$incomeAccount : [];
    }

    public function manipulate($name, $entity_type, $grp_ledg_id = "", $parent_group = 0, $behaviour = "", $ledg_id = '', $opening_balance = 0, $update_led_id = '', $ledger_start_date = '', $ledger_type = '', $context = '', $txn_id = 0, $soc_id = 0, $is_reco = 0)
    {
        $soc_id = $this->input['company_id'];
        $dup = $this->_checkGroupNameDuplication($parent_group, $name, $ledg_id, $update_led_id, $soc_id);

        $arrFYDetail = $this->getCurrentFinancialYear(['soc_id' => $soc_id]);
        $arrFYDetail = json_decode(json_encode($arrFYDetail), true);
        $ledger_start_date = $arrFYDetail['fy_start_date'];

        if ($dup == 0) {
            $behaviour = trim(strtolower($behaviour));

            // Check if we need to update or create a new ledger
            $grp_ledg_tree = $this->tenantDB()->table('chsone_grp_ledger_tree');
            if ($ledg_id) {
                $grp_ledg_tree = $this->getLedger($ledg_id);
            }

            // Prepare data for insertion or update
            $grpLedgData = [
                'entity_type' => $entity_type,
                'soc_id' => $soc_id,
                'ledger_account_name' => $name,
                'ledger_start_date' => $ledger_start_date,
                'context_ref_id' => 0,
                'operating_type' => (!empty($ledger_type) && $ledger_type != "NULL" && $ledger_type != '') ? $ledger_type : '',
                'parent_id' => !empty($parent_group) ? $parent_group : HEAD_GROUP_VAL,
                'behaviour' => !empty($behaviour) ? $behaviour : $this->_getLedgerProps($parent_group)['behaviour'],
                'nature_of_account' => $config->nature_account->$behaviour ?? $this->_getLedgerProps($parent_group)['nature_account'],
                'report_head' => $config->report_head->$behaviour ?? $this->_getLedgerProps($parent_group)['report_head'],
                'context' => !empty($context) ? $context : $this->_getLedgerProps($parent_group)['context'],
                'defined_by' => 'user',
                'status' => 1,
                'added_on' => date("Y-m-d H:i:s"),
                'created_by' => $this->input['user_id'] ?? 0,
            ];

            // Set ledger_account_id if provided
            if (!empty($grp_ledg_id)) {
                $grpLedgData['ledger_account_id'] = $grp_ledg_id;
            }
            if ($update_led_id != '') {
                $grpLedgData['ledger_account_id'] = $update_led_id;
            }
            // Insert or update ledger in the database
            //$result = $grp_ledg_tree->insertOrIgnore($grpLedgData);
            $result = $this->insertOrUpdateLedger($grpLedgData);
            $grpLedgData['ledger_account_id'] = $result;

            if ($result) {
                // Handle transaction for opening balance if applicable
                if (!in_array(strtolower($grpLedgData['behaviour']), [INCOME, EXPENSE])) {
                    $txn_date = $ledger_start_date;
                    $narration = 'entry for opening balance';
                    if ($txn_id = $this->addTxn($grpLedgData['ledger_account_id'], $opening_balance, $narration, $txn_date, "", $grpLedgData['nature_of_account'], "", "", "", "", $name, $is_opning = 1, $is_reco, $soc_id)) {
                        // Transaction successful
                    } else {
                        // Handle failure
                    }
                }
                return $grpLedgData['ledger_account_id'];
            } else {
                // Handle save failure
                return false;
            }
        } else {
            return "DUP" . $dup;
        }
    }

    public function insertOrUpdateLedger(array $data)
    {
        // Check if the ledger account exists
        $ledger = $this->tenantDB()->table("chsone_grp_ledger_tree")->where('ledger_account_name', $data['ledger_account_name'])
            ->where('soc_id', $data['soc_id'])
            ->first();

        if ($ledger) {
            // Update existing ledger record
            $this->tenantDB()->table('chsone_grp_ledger_tree')
                ->where('ledger_account_name', $data['ledger_account_name'])
                ->where('soc_id', $data['soc_id'])
                ->update([
                    'ledger_start_date' => (string) $data['ledger_start_date'],
                    'context_ref_id' => $data['context_ref_id'],
                    'operating_type' => (string) $data['operating_type'],
                    'parent_id' => $data['parent_id'],
                    'behaviour' => (string) $data['behaviour'],
                    'nature_of_account' => (string) $data['nature_of_account'],
                    'report_head' => (string) $data['report_head'],
                    'context' => (string) $data['context'],
                    'defined_by' => (string) $data['defined_by'],
                    'status' => $data['status'],
                    'added_on' => Carbon::now()->toDateTimeString(),
                    'created_by' => $data['created_by'],
                    'entity_type' => (string) $data['entity_type'],
                ]);

            // Fetch and return the ledger_account_id after update
            $ledgerId = $this->tenantDB()->table('chsone_grp_ledger_tree')
                ->where('ledger_account_name', $data['ledger_account_name'])
                ->where('soc_id', $data['soc_id'])
                ->value('ledger_account_id');
        } else {
            // Insert a new ledger record and get its ID
            try {
                $ledgerId = $this->tenantDB()->table('chsone_grp_ledger_tree')->insertGetId([
                    'soc_id' => $data['soc_id'],
                    'ledger_account_name' => (string)$data['ledger_account_name'],
                    'ledger_start_date' => (string)$data['ledger_start_date'],
                    'context_ref_id' => $data['context_ref_id'],
                    'operating_type' => (string)$data['operating_type'],
                    'parent_id' => $data['parent_id'],
                    'behaviour' => (string)$data['behaviour'],
                    'nature_of_account' => (string)$data['nature_of_account'],
                    'report_head' => (string)$data['report_head'],
                    'context' => (string)$data['context'],
                    'defined_by' => (string)$data['defined_by'],
                    'status' => $data['status'],
                    'added_on' => Carbon::now()->toDateTimeString(),
                    'created_by' => $data['created_by'],
                    'entity_type' => (string)$data['entity_type'],
                ]);

            }catch (\Exception $e) {
                dd($e->getMessage());
            }
        }

// Return the ledger_account_id (updated or newly created)
        return $ledgerId;

    }

    public function _getLedgerProps($parentGroup)
    {
        // Initialize an empty array
        $array = [];

        // Ensure $parentGroup is a valid numeric value
        if (is_numeric($parentGroup)) {
            $ledgerProp = ChsoneGrpLedgerTree::where('ledger_account_id', $parentGroup)
                ->select(['ledger_account_name', 'nature_of_account', 'behaviour', 'report_head', 'context'])
                ->first();

            // If record is found, populate the array
            if ($ledgerProp) {
                $array = [
                    "ledger_account_name" => $ledgerProp->ledger_account_name,
                    "behaviour" => $ledgerProp->behaviour,
                    "nature_account" => $ledgerProp->nature_of_account,
                    "report_head" => $ledgerProp->report_head,
                    "context" => $ledgerProp->context,
                ];
            }
        }

        return $array;
    }

    public function _checkGroupNameDuplication($parent, $name, $ledgId = null, $updateLedId = null)
    {
        $socId = $this->input['company_id'];

        // Base Query
        $query = ChsoneGrpLedgerTree::where('soc_id', $socId)
            ->whereRaw('LOWER(ledger_account_name) = ?', [strtolower(trim($name))]);

        // Exclude specific ledger IDs if provided
        if (!empty($ledgId)) {
            $query->where('ledger_account_id', '!=', $ledgId);
        }
        if (!empty($updateLedId)) {
            $query->where('ledger_account_id', '!=', $updateLedId);
        }

        // Count matching ledgers
        return $query->count();
    }


    public function getAllTaxClasses($data)
    {
        // Fetch the authenticated user's society ID
        $socId = $this->input['company_id'];
        $status = $data['status'] ?? null;

        // Validate required fields
        if (!$socId) {
            // Handle missing required data scenario
            return [];
        }

        // Build the query using Eloquent
        $query = $this->tenantDB()->table('chsone_tax_classes')->where('soc_id', $socId);

        // Add status condition if provided
        if (!is_null($status)) {
            $query->where('status', $status);
        }

        // Fetch results ordered by tax_class_id descending
        $taxClasses = $query->orderBy('tax_class_id', 'desc')->get();

        // Convert results to array
        return $taxClasses->toArray();
    }


    public function getTaxClassSelect(array $data = [])
    {
        // Initialize the array with a default option
        $arrTaxClass = ['' => 'Select Tax Class'];

        // Check if 'arrTaxClass' is not empty and iterate over the classes
        if (!empty($data['arrTaxClass'])) {
            // Use Laravel's collection map function if you're working with collections
            foreach ($data['arrTaxClass'] as $eachTaxClass) {
                // Format the tax class name using ucwords and add it to the array
                $arrTaxClass[$eachTaxClass->tax_class_id] = ucwords($eachTaxClass->tax_class_name);
            }
        }

        return $arrTaxClass;
    }

    public function getCurrentFinantialYear($data)
    {

        // Fetch the account start master record
        $objAccountStartMaster = $this->tenantDB()->table('soc_account_start_master')->where('soc_id', $data['soc_id'])->first();

        $objAccountStartMaster = json_decode(json_encode($objAccountStartMaster), true);
        // Convert to array or set as an empty array if null
        $arrAccountStartMaster = $objAccountStartMaster ? $objAccountStartMaster : [];

        if (!empty($arrAccountStartMaster)) {
            $currentMonth = Carbon::now()->month; // Get the current month as a number (1-12)

            // Get fiscal year start month from 'fy_start_from' (assuming it's 'MM-DD' format)
            $fiscalStartMonth = (int) explode("-", $arrAccountStartMaster['fy_start_from'])[0];

            // Set the base year for the fiscal start date
            $currentYear = Carbon::now()->year;

            // Adjust the fiscal year start date based on the current month
            if ($currentMonth < $fiscalStartMonth) {
                $currentYear--; // Set to previous year if before fiscal start month
            }

            $currentFinancialDate = Carbon::create($currentYear, $fiscalStartMonth, 1)->toDateString(); // Create date in 'YYYY-MM-DD' format
        } else {
            $currentFinancialDate = null; // Return null if no data is found
        }

        return $currentFinancialDate;
    }

    public function getMaxFloorFromSociety($soc_id)
    {

        // Query to fetch the maximum number of building floors for the society
        $maxFloor = $this->tenantDB()
            ->table('chsone_societies_building_master')
            ->where('soc_id', $soc_id)
            ->max('soc_building_floors');

        return $maxFloor;
    }


    public function formatTaxClassDetail($data = [])
    {
        $arrTaxClass = [];

        if (!empty($data['arrTaxClassDetail']) && is_array($data['arrTaxClassDetail'])) {
            foreach ($data['arrTaxClassDetail'] as $eachTaxDetail) {
                if (isset($eachTaxDetail['tax_class_id'], $eachTaxDetail['tax_class_name'])) {
                    $arrTaxClass[$eachTaxDetail['tax_class_id']] = ucwords($eachTaxDetail['tax_class_name']);
                }
            }
        }

        return $arrTaxClass;
    }

    public function getLedgGroupTree($voucher_type, $mode, $entity_type = "", $ledger_id = '', $soc_id = 0, $behaviour = '')
    {

        $conditions = [];
        $cnd_arr = $this->contextVoucherTypes($voucher_type, $mode, $soc_id);

        // Context conditions
        if (!empty($cnd_arr['context'])) {
            $conditions[] = "context IN ('" . implode("','", $cnd_arr['context']) . "')";
        }
        if (!empty($cnd_arr['context_not'])) {
            $conditions[] = "context NOT IN ('" . implode("','", $cnd_arr['context_not']) . "')";
        }

        // Ledger account conditions
        if (!empty($ledger_id)) {
            $conditions[] = "ledger_account_id IN (" . implode(",", (array) $ledger_id) . ")";
        } elseif (!empty($cnd_arr['group'])) {
            $conditions[] = "ledger_account_id IN (" . implode(",", $cnd_arr['group']) . ")";
        }

        // Entity type conditions
        // if ($entity_type == ACC_TYPE_BANK || $entity_type == ACC_TYPE_CASH) {
        if ($entity_type == 'bank' || $entity_type == 'cash') {

            $conditions[] = "(behaviour = 'asset' OR behaviour = 'liability')";
            $conditions[] = "entity_type != '" . strtolower($this->constants['ENTITY_TYPE_LEDGER'] ?? 'ledger') . "'";
        } elseif ($entity_type == $this->constants['ENTITY_TYPE_GROUP'] ?? 'ledger') {
            $conditions[] = "entity_type != '" . strtolower($this->constants['ENTITY_TYPE_LEDGER'] ?? 'ledger') . "'";
        }


        // Final condition
        $fin_cnd = implode(' AND ', $conditions);
        $fin_cnd .= " AND status = " . $this->constants['ACTIVE']  ??  1 . " AND soc_id = $soc_id";

        // Handle voucher type 'contra'
        if ($voucher_type == 'contra') {
            $fin_cnd = "parent_id IN (" . implode(',', $cnd_arr['group']) . ") OR $fin_cnd";
        }


        if (!empty($ledger_id)) {

            return $this->getLedgerTreeByIds($ledger_id);
        }



        if (!empty($cnd_arr["group"]) && empty($cnd_arr["context"]) && empty($cnd_arr["context_not"])) {

            if (count($cnd_arr["group"]) == 1) {
                return $this->getLedgerTreeById($cnd_arr["group"][0]);
            } else {
                return $this->getLedgerTreeByIds($cnd_arr["group"]);
            }
        } else {
            return $this->getLedgerTreeByCondition($fin_cnd);
        }
    }

    public function getbankaccountdetails()
    {
        // $nested_array_from = $this->getLedgGroupTree(ACC_TYPE_BANK, MODE_FROM);
        $nested_array_from = $this->getLedgGroupTree('bank', 'from');

        $filter_param = array('bank');

        $filter_ledgers_from = $this->sorted_array($nested_array_from, 0, $filter_param);
        //        print_r($filter_ledgers_from);exit();
        return $filter_ledgers_from;
    }

    public function sorted_array($elements, $parentId = 0, $filter_param)
    {
        foreach ($elements as $elementKey => $elementValue) {
            // Check if the current element's context matches the filter parameters
            if (in_array($elementValue['context'] ?? null, $filter_param) !== false) {
                // If the element is a parent with children, process children recursively
                if (($elementValue['is_parent'] ?? 'no') === 'yes' && !empty($elementValue['children'])) {
                    $elements[$elementKey]['children'] = $this->sorted_array(
                        $elementValue['children'],
                        $elementValue['ledger_account_id'] ?? null,
                        $filter_param
                    );
                }
                // If the element matches but has no children, keep it and continue
                continue;
            }

            // If the context doesn't match but it's a parent with children
            if (($elementValue['is_parent'] ?? 'no') === 'yes' && !empty($elementValue['children'])) {
                // Recursively process the children
                $elements[$elementKey]['children'] = $this->sorted_array(
                    $elementValue['children'],
                    $elementValue['ledger_account_id'] ?? null,
                    $filter_param
                );

                // If no children remain after filtering, remove the parent
                if (empty($elements[$elementKey]['children'])) {
                    unset($elements[$elementKey]);
                }
            } else {
                // If the context doesn't match and it's not a parent, remove the element
                unset($elements[$elementKey]);
            }
        }
        return $elements;
    }

    public function contextVoucherTypes($voucherType, $mode, $soc_id)
    {
        $context = "";
        $contextNot = "";
        $grpId = [];
        $strings = [];
        $soc_id = $soc_id ? $soc_id : $this->input['company_id'];

        if ($mode == "from") {
            $voucherType = "payment";
            if (isset($this->constants['context_from_conf_arr'][$voucherType])) {
                $context = $this->constants['context_from_conf_arr'][$voucherType];
            }
            if (isset($this->constants['context_from_not_in_conf_arr'][$voucherType])) {
                $contextNot = $this->constants['context_from_not_in_conf_arr'][$voucherType];
            }
            if (isset($this->constants['context_from_grp_array'][$voucherType])) {
                $strings = $this->constants['context_from_grp_array'][$voucherType];
            }
        }

        if ($mode == "to") {
            if (isset($this->constants['context_to_conf_arr'][$voucherType])) {
                $context = $this->constants['context_to_conf_arr'][$voucherType];
            }
            if (isset($this->constants['context_to_not_in_conf_arr'][$voucherType])) {
                $contextNot = $this->constants['context_to_not_in_conf_arr'][$voucherType];
            }
            if (isset($this->constants['context_to_grp_array'][$voucherType])) {
                $strings = $this->constants['context_to_grp_array'][$voucherType];
            }
        }

        $groups = $this->tenantDB()->table("chsone_grp_ledger_tree")
            ->whereIn("ledger_account_name", $strings)
            ->where("soc_id", $soc_id)
            ->select("ledger_account_id")
            ->get();

        if (!empty($groups)) {
            foreach ($groups as $group) {
                $grpId[] = $group->ledger_account_id;
            }
        } else {
            $grpId = [];
        }
        return [
            "context" => $context,
            "context_not" => $contextNot,
            "group" => $grpId
        ];
    }

    public function getLedgerTreeById($id = '')
    {
        // Validate the ID to prevent SQL injection and ensure it's a valid integer
        if (empty($id) || !is_numeric($id)) {
            return [];
        }

        // Initialize the @idlist variable using your tenantDB connection
        $this->tenantDB()->statement("SET @idlist = ''");

        // Prepare the raw SQL query
        $query_string = "
                        SELECT
                            ledger_account_name,
                            nature_of_account,
                            behaviour,
                            entity_type,
                            ledger_account_id,
                            parent_id as parent,
                            status,
                            context,
                            operating_type
                        FROM (
                            SELECT *,
                                CASE
                                    WHEN ledger_account_id = ? THEN @idlist := CONCAT(ledger_account_id)
                                    WHEN FIND_IN_SET(parent_id, @idlist) THEN @idlist := CONCAT(@idlist, ',', ledger_account_id)
                                END as checkId
                            FROM chsone_grp_ledger_tree
                            ORDER BY ledger_account_id ASC
                        ) as T
                        WHERE checkId IS NOT NULL
                    ";

        // Execute the raw query using your tenantDB connection and pass the ID as a parameter
        $results = $this->tenantDB()->select($query_string, [$id]);

        // Convert the results to an array and return
        return !empty($results) ? json_decode(json_encode($results), true) : [];
    }

    public function getLedgerTreeByIds($ids = [])
    {
        // Validate and sanitize input
        if (empty($ids) || !is_array($ids)) {
            return [];
        }

        // Convert the array of IDs into a comma-separated string for use in the query
        $condition = implode(',', array_map('intval', $ids));

        // Prepare the raw SQL query
        $query_string = "
                    SELECT
                        ledger_account_name,
                        nature_of_account,
                        behaviour,
                        entity_type,
                        ledger_account_id,
                        parent_id as parent,
                        status,
                        context,
                        operating_type
                    FROM (
                        SELECT *,
                            CASE
                                WHEN ledger_account_id IN ($condition) THEN @idlist := CONCAT(ledger_account_id)
                                WHEN FIND_IN_SET(parent_id, @idlist) THEN @idlist := CONCAT(@idlist, ',', ledger_account_id)
                            END as checkId
                        FROM chsone_grp_ledger_tree, (SELECT @idlist := '') AS init
                        ORDER BY parent_id, ledger_account_name, ledger_account_id ASC
                    ) as T
                    WHERE checkId IS NOT NULL
                ";

        // Execute the query using Laravel's DB facade
        $results = $this->tenantDB()->select(DB::raw($query_string));

        // Convert the results to an array
        return $results ? json_decode(json_encode($results), true) : [];
    }


    public function getLedgerTreeByCondition($fin_cnd)
    {
        // Validate the input condition to prevent potential SQL injection
        if (empty($fin_cnd)) {
            return [];
        }

        try {
            $this->tenantDB()->statement("SET @idlist = ''");
            // \Log::info("Executing query with condition: $fin_cnd");

            $query_string = "
            SELECT ledger_account_name, nature_of_account, behaviour, entity_type,
            ledger_account_id, parent_id as parent, status, context, operating_type
            FROM (
                SELECT *, CASE
                    WHEN $fin_cnd THEN @idlist := CONCAT(ledger_account_id)
                    WHEN FIND_IN_SET(parent_id, @idlist) THEN @idlist := CONCAT(@idlist, ',', ledger_account_id)
                END as checkId
                FROM chsone_grp_ledger_tree
                ORDER BY parent_id, ledger_account_name ASC
            ) as T
            WHERE checkId IS NOT NULL
        ";

            // \Log::info("Executing SQL Query: ", [$query_string]);

            $results = $this->tenantDB()->select($query_string);
            // \Log::info("Results: ", $results);

            return !empty($results) ? json_decode(json_encode($results), true) : [];
        } catch (\Exception $e) {
            // \Log::error("Error in getLedgerTreeByCondition: " . $e->getMessage());
            return [];
        }
    }

    public function prepareTree(array $tree, $root = 0)
    {
        $result = [];

        foreach ($tree as $key => $parent) {
            if ($parent['parent'] == $root) {
                unset($tree[$key]);

                $modifiedArray = [
                    "name" => $parent["ledger_account_name"],
                    "nature" => $parent["nature_of_account"],
                    "behaviour" => $parent["behaviour"],
                    "entity_type" => $parent["entity_type"],
                    "ledger_account_id" => $parent["ledger_account_id"],
                    "parent" => $parent["parent"],
                    "status" => $parent["status"],
                    "context" => preg_replace('/[0-9]+/', '', $parent["context"]),
                    "is_parent" => "yes",
                    "operating_type" => $parent["operating_type"]
                ];

                // Recursively find children
                $children = $this->prepareTree($tree, $parent['ledger_account_id']);
                $modifiedArray['is_parent'] = empty($children) ? 'no' : 'yes';
                $modifiedArray['children'] = $children;

                $result[$parent['ledger_account_id']] = $modifiedArray;
            }
        }

        return $result;
    }

    public function getcashaccountdetails()
    {
        // $nested_array_from = $this->getLedgGroupTree(ACC_TYPE_CASH, MODE_FROM);

        $nested_array_from = $this->getLedgGroupTree('cash', 'from ');
        $filter_param = array('cash');
        $filter_ledgers_from = $this->sorted_array($nested_array_from, 0, $filter_param);
        return $filter_ledgers_from;
    }


    public function getRecentlyUsedRuleDetail(array $data = []): array
    {
        // Check if recent invoice dates exist
        if (!empty($data['arrRecentInvoiceDate']) && count($data['arrRecentInvoiceDate']) > 0) {
            // Check if recent parking invoice dates exist
            if (!empty($data['arrRecentParkingInvoiceDate']) && count($data['arrRecentParkingInvoiceDate']) > 0) {
                // Replace the first recent invoice date with the parking invoice date
                $data['arrRecentInvoiceDate'][0] = $data['arrRecentParkingInvoiceDate'][0];

                // Sort the array by keys
                ksort($data['arrRecentInvoiceDate']);

                // Calculate the max effective date
                $data['arrRecentInvoiceDate']['maxEffectiveDate'] = ($data['arrRecentInvoiceDate']['maxEffectiveDate'] >=
                    $data['arrRecentParkingInvoiceDate']['maxEffectiveDate'])
                    ? $data['arrRecentInvoiceDate']['maxEffectiveDate']
                    : $data['arrRecentParkingInvoiceDate']['maxEffectiveDate'];
            }

            return $data['arrRecentInvoiceDate'];
        }

        return [];
    }
}
