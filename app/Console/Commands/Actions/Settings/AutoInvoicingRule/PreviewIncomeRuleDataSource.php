<?php

namespace App\Console\Commands\Actions\Settings\AutoInvoicingRule;

use App\Console\Commands\Action;

use function PHPUnit\Framework\isEmpty;

class PreviewIncomeRuleDataSource extends Action
{
    protected $signature = 'datasource:previewIncomeRule {flowId} {parentId} {input}';

    protected $description = 'Preview income rule';

    public function apply()
    {

        $ruleid = $this->input['id'];
        $soc_id = $this->input['company_id'];

        $arrGetRuleInfo = [];
        $arrGetRuleInfo['ruleid'] = $ruleid;
        $arrGetRuleInfo['sorted'] = 1;

        // Fetch Income Invoice Rule Details
        $incomeInvoiceRuleDetails = $this->tenantDB()
            ->table('income_invoice_rules')
            ->where('id', $ruleid)
            ->where('soc_id', $soc_id)
            ->first();

        if (empty($incomeInvoiceRuleDetails)) {
            $this->message = "Rule Data Not Found";
            $this->status = "error";
            $this->statusCode = 400;
            return;
        }

        $arrRuleData = $this->getAllRuledetails($ruleid, $soc_id);

        $formattedResponse = $this->formatResponse($arrRuleData, $arrRuleData['arrIncomeRuleDetails']['income_account_name']);
        $arrRuleData['rule_data'] = $formattedResponse;


        // dd($formattedResponse);
        // $arrListenerdataForParking = array('soc_id' => $soc_id, 'useAccountID' => 1);
        // $arrallmemberIncomeAccount  = $this->getselectmemberdetails($arrListenerdataForParking);

        // // Tax data
        // if (!empty($arrRuleData['arrIncomeRuleDetails']['applicable_taxes'])) {
        //     $arrTax = $this->getTaxClassById(array('soc_id' => $soc_id, 'tax_class_id' => array($arrRuleData['arrIncomeRuleDetails']['applicable_taxes'])));
        //     $arrTax = $this->formatTaxClassDetail(array('arrTaxClassDetail' => $arrTax));
        // }

        // //tax exemption
        // $arrTaxexemption = $this->getAllTaxexEmptionPreview($soc_id);


        // if ($arrRuleData['arrIncomeRuleDetails']['rule'] == 'standard' && $arrRuleData['arrIncomeRuleDetails']['other_income_fee_rate'] == 'advancenoc') {
        //     $arrRuleData['rule'] = 'standard_noc';
        //     $arrRuleData['label'] = ['Non-Occupancy Charges'];
        //     $arrRuleData['standard_noc'] = [
        //         'label' => 'Non-Occupancy Charges',
        //         'data' => $arrRuleData['arrIncomeRuleDetails']['noc_rules']
        //     ];
        // }

        // if ($arrRuleData['arrIncomeRuleDetails']['rule'] == 'standard' && $arrRuleData['arrIncomeRuleDetails']['other_income_fee_rate'] == 'fixed') {
        //     $arrRuleData['rule'] = 'standard_fixed';
        //     $selection = $arrRuleData['arrIncomeRuleDetails']['selection'];
        //     $key = explode('|', $selection);
        //     $output = $this->formatCamelCaseToWords($key);
        //     $arrRuleData['standard_fixed'] =
        //         [
        //             'label' => $output[0],
        //             'data' => [
        //                 'title' => "Amount",
        //                 'type' => $arrRuleData['arrIncomeRuleDetails']['other_income_fee_rate'],
        //                 'amount' => $arrRuleData['arrIncomeRuleDetails']['other_income_fee']
        //             ]
        //         ];
        // }

        // if ($arrRuleData['arrIncomeRuleDetails']['rule'] == 'standard' && $arrRuleData['arrIncomeRuleDetails']['other_income_fee_rate'] == 'none') {
        //     $arrRuleData['rule'] = 'standard_none';
        //     $selection = $arrRuleData['arrIncomeRuleDetails']['selection'];
        //     $key = explode('|', $selection);
        //     $output = $this->formatCamelCaseToWords($key);
        //     $arrRuleData['standard_none'] =
        //         [
        //             'label' => $output[0],
        //             'data' => [
        //                 'title' => "Flat Fee / Month",
        //                 'type' => "",
        //                 'amount' => $arrRuleData['arrIncomeRuleDetails']['fee_per_unit']
        //             ]

        //         ];
        // }

        // if ($arrRuleData['arrIncomeRuleDetails']['rule'] == 'standard' && $arrRuleData['arrIncomeRuleDetails']['other_income_fee_rate'] == 'simplenoc' || $arrRuleData['arrIncomeRuleDetails']['other_income_fee_rate'] == '') {
        //     $arrRuleData['rule'] = 'standard_simple_noc';
        //     $selection = $arrRuleData['arrIncomeRuleDetails']['selection'];
        //     $key = explode('|', $selection);
        //     $output = $this->formatCamelCaseToWords($key);
        //     $arrRuleData['standard_simple_noc'] =
        //         [
        //             'label' => $output[0],
        //             'data' => [
        //                 [
        //                     'title' => "Tenant occupied flat :",
        //                     'type' => "per month",
        //                     'amount' => $arrRuleData['arrIncomeRuleDetails']['fee_noc_tenant']
        //                 ],
        //                 [
        //                     'title' => 'Tenant unoccupied flat :',
        //                     'type' => 'per month',
        //                     'amount' => $arrRuleData['arrIncomeRuleDetails']['fee_noc_vacant']
        //                 ]
        //             ]
        //         ];
        // }

        // if ($arrRuleData['arrIncomeRuleDetails']['rule'] == 'parking') {
        //     $arrRuleData['rule'] = 'parking';
        //     $selection = $arrRuleData['arrIncomeRuleDetails']['selection'];
        //     $key = explode('|', $selection);
        //     $output = $this->formatCamelCaseToWords($key);
        //     $arrRuleData['parking'] =
        //         [
        //             'label' => "Parking Rule",
        //             "data" => $arrRuleData['parkingruledetails']
        //         ];
        // }

        // if ($arrRuleData['arrIncomeRuleDetails']['rule'] == 'floorbased') {
        //     $arrRuleData['rule'] = 'floorbased';
        //     $selection = $arrRuleData['arrIncomeRuleDetails']['selection'];
        //     $key = explode('|', $selection);
        //     $output = $this->formatCamelCaseToWords($key);
        //     $arrRuleData['floorbased'] =
        //         [
        //             'label' => "Floor Based Rule",
        //             "data" => $arrRuleData['arrFloorBasedRule']
        //         ];
        // }

        $this->data = $arrRuleData;
    }


    public function getAllRuledetails($rule_id, $soc_id)
    {

        $arrRuleData = [];
        $arrRuleData['arrIncomeRuleDetails'] = $this->getIncomeinvoicerule($rule_id, $soc_id);

        $arrRuleData['parkingruledetails'] = $this->getParkingRuleDetails($rule_id, $soc_id);
        $arrRuleData['arrFloorBasedRule'] = $this->getFloorBasedRuleDetails($rule_id, $soc_id, array(), 1);

        return $arrRuleData;
    }

    private function getIncomeinvoicerule($id = '', $soc_id)
    {
        $parameters = [];
        $conditions = '';

        if (!empty($id)) {
            $conditions = "id = ?";
            $parameters[] = $id;
        }

        if (!empty($soc_id)) {
            $conditions .= ($conditions != "") ? ' and ' : '';
            $conditions .= "soc_id = ?";
            $parameters[] = $soc_id;
        }

        // Fetch Income Invoice Rule Details
        $incomeInvoiceRuleDetails = $this->tenantDB()
            ->table('income_invoice_rules')
            ->whereRaw($conditions, $parameters)
            ->first();


        $arrInvoiceRuleDetails = [];
        if (!empty($incomeInvoiceRuleDetails)) {
            $arrInvoiceRuleDetails = (array)$incomeInvoiceRuleDetails;

            $nocRules = $this->tenantDB()
                ->table('income_invoice_noc_rules')
                ->where('rule_id', $id)
                ->get()
                ->toArray();

            $income_account_data = $this->tenantDB()
                ->table('income_accounts')
                ->where('account_id', $incomeInvoiceRuleDetails->income_account_id)
                ->first();

            $taxClasses = $this->tenantDB()
                ->table('chsone_tax_classes')
                ->where('soc_id', $soc_id)
                ->where('tax_class_id', $incomeInvoiceRuleDetails->applicable_taxes)
                ->first();


            $arrInvoiceRuleDetails['edit_rule'] = 0;
            $arrInvoiceRuleDetails['noc_rules'] = $nocRules;
            $arrInvoiceRuleDetails['income_account_name'] = $income_account_data->account_name ?? '';
            $arrInvoiceRuleDetails['tax_class_name'] = $taxClasses->tax_class_name ?? '';

            if ($arrInvoiceRuleDetails['effective_date'] > $this->getCurrentDate('database')) {
                $arrInvoiceRuleDetails['edit_rule'] = 1;
            }
        }

        return $arrInvoiceRuleDetails;
    }


    private function getParkingRuleDetails($ruleid, $soc_id)
    {
        // Parking details
        $parkingRuleDetails = $this->tenantDB()
            ->table('income_parking_charges')
            ->where('rule_id', $ruleid)
            ->where('soc_id', $soc_id)
            ->get();

        return $parkingRuleDetails->toArray();
    }

    private function getFloorBasedRuleDetails($ruleid, $soc_id, $fields = [], $status = null)
    {
        // Initialize the query with base conditions
        $query = $this->tenantDB()
            ->table('income_floorbased_charges')
            ->where('rule_id', $ruleid)
            ->where('soc_id', $soc_id)
            ->orderBy('floor_from', 'ASC');

        // Select specific columns if provided
        if (!empty($fields)) {
            $query->select($fields);
        }

        // Add status condition if provided
        if (!is_null($status)) {
            $query->where('status', $status);
        }

        // Execute the query and get the results
        $floorBasedRuleDetails = $query->get();

        return $floorBasedRuleDetails->toArray();
    }

    public function getselectmemberdetails($data = [])
    {

        // Fetch income accounts from the database
        $incomeAccounts = $this->tenantDB()
            ->table('income_accounts')
            ->where('soc_id', $data['soc_id'])
            ->where('account_type', 'member')
            ->get(['account_id', 'account_name']);

        $newIncomeAccounts = [];

        foreach ($incomeAccounts as $account) {
            if ($data['useAccountID'] == 1) {
                $newIncomeAccounts[$account->account_id] = $account->account_name;
            } else {
                $newIncomeAccounts[$account->account_id . "_" . $account->account_name] = $account->account_name;
            }
        }

        return $newIncomeAccounts;
    }


    public function getTaxClassById($data = [])
    {
        // Validate the required data
        if (empty($data['soc_id']) || empty($data['tax_class_id']) || !is_array($data['tax_class_id'])) {
            return [];
        }

        // Fetch tax classes from the database
        $taxClasses = $this->tenantDB()
            ->table('chsone_tax_classes')
            ->where('soc_id', $data['soc_id'])
            ->whereIn('tax_class_id', $data['tax_class_id'])
            ->get();

        return $taxClasses->toArray();
    }

    public function formatTaxClassDetail($data = [])
    {
        $arrTaxClass = [];

        if (!empty($data['arrTaxClassDetail']) && is_array($data['arrTaxClassDetail'])) {
            foreach ($data['arrTaxClassDetail'] as $eachTaxDetail) {
                if (isset($eachTaxDetail['tax_class_id'], $eachTaxDetail['tax_class_name'])) {
                    $arrTaxClass[$eachTaxDetail['tax_class_id']] = ucwords($eachTaxDetail['tax_class_name']);
                }
            }
        }

        return $arrTaxClass;
    }

    public function getAllTaxexEmptionPreview($soc_id)
    {
        $arrTaxexemption = [];

        // Fetch tax exemptions for the given society ID
        $taxExemptions = $this->tenantDB()->table('tax_exemptions')
            ->where('soc_id', $soc_id)
            ->get();

        // Map tax exemption ID to description
        foreach ($taxExemptions as $taxExemption) {
            $arrTaxexemption[$taxExemption->id] = $taxExemption->description;
        }

        return $arrTaxexemption;
    }


    function formatCamelCaseToWords($input)
    {
        return preg_replace('/(?<!^)([A-Z])/', ' $1', $input);
    }


    function formatResponse($arrRuleData, $incomeAccountName)
    {
        $response = [];

        // Base details
        $response['id'] = $arrRuleData['arrIncomeRuleDetails']['id'] ?? null;
        $response['income_account_name'] = $arrRuleData['arrIncomeRuleDetails']['income_account_name'] ?? '';
        $response['rule'] = $arrRuleData['arrIncomeRuleDetails']['rule'] ?? '';

        $incomeAccountName = strtolower($incomeAccountName);

        // Handle NOC Rule
        if ($incomeAccountName === 'noc') {
            $response['type'] = 'noc';
            $response['label'] = 'Non-Occupancy Charges';
            $response['details'] = [];

            $incomeDetails = $arrRuleData['arrIncomeRuleDetails'];
            if ($incomeDetails['other_income_fee_rate'] === 'advancenoc') {
                $response['details'] = [
                    'type' => 'advanced',
                    'rules' => array_map(function ($rule) {
                        $rule = json_decode(json_encode($rule), true);
                        return [
                            'amount' => number_format($rule['amount'] ?? 0, 3, '.', ''),
                            'particular' => $rule['particular'] ?? '',
                        ];
                    }, $incomeDetails['noc_rules'] ?? [])
                ];
            } elseif (in_array($incomeDetails['other_income_fee_rate'], ['simplenoc', 'fixed'])) {
                $response['details'] = [
                    'type' => 'simple',
                    'noc_tenant' => number_format($incomeDetails['fee_noc_tenant'] ?? 0, 3, '.', ''),
                    'noc_vacant' => number_format($incomeDetails['fee_noc_vacant'] ?? 0, 3, '.', ''),
                ];
            } elseif ($incomeDetails['other_income_fee_rate'] === 'persqft') {
                $response['details'] = [
                    'type' => 'persqft',
                    'area' => number_format($incomeDetails['fee_carpet_area'] ?? 0, 3, '.', ''),
                    'open_area' => number_format($incomeDetails['fee_open_area'] ?? 0, 3, '.', ''),
                ];
            }
            return $response;
        }

        // Handle Standard Rule
        if ($response['rule'] === 'standard') {
            $response['type'] = 'standard';
            $response['label'] = 'Standard Rule';

            $feePerUnit = $arrRuleData['arrIncomeRuleDetails']['fee_per_unit'] ?? 0;
            if ($feePerUnit == 0) {
                $response['details'] = [
                    'type' => 'persqft',
                    'area' => number_format($arrRuleData['arrIncomeRuleDetails']['fee_carpet_area'] ?? 0, 3, '.', ''),
                    'open_area' => number_format($arrRuleData['arrIncomeRuleDetails']['fee_open_area'] ?? 0, 3, '.', ''),
                ];
            } else {
                $response['details'] = [
                    'type' => 'flat_fee',
                    'charges' => [
                        'amount' => number_format($feePerUnit, 3, '.', ''),
                        'unit' => 'per month',
                    ]
                ];
            }
        }

        // Handle Parking Rule
        if ($response['rule'] === 'parking') {
            $response['type'] = 'parking';
            $response['label'] = 'Parking Rule';
            $response['details'] = [
                'data' => $arrRuleData['parkingruledetails'] ?? []
            ];
            return $response;
        }

        // Handle Other Charges Head Rule
        if (!in_array($incomeAccountName, ['maintenancefee', 'parking', 'propertytax'])) {
            $response['type'] = 'other_charges';
            $response['label'] = ucwords(preg_replace('/([A-Z])/', ' $1', $incomeAccountName)) . ' Rule';
            $response['details'] = [];

            $feeRate = $arrRuleData['arrIncomeRuleDetails']['other_income_fee_rate'] ?? '';

            if ($feeRate === 'fixed') {
                $response['details'] = [
                    'type' => 'fixed',
                    'charges' => [
                        [
                            'label' => 'Fixed Amount',
                            'value' => number_format($arrRuleData['arrIncomeRuleDetails']['other_income_fee'] ?? 0, 3, '.', ''),
                            'unit' => 'per month'
                        ]
                    ]
                ];
            } elseif ($feeRate === 'persqft') {
                $response['details'] = [
                    'type' => 'persqft',
                    'charges' => [
                        [
                            'label' => 'Fee / Sqft Area / Month',
                            'value' => number_format($arrRuleData['arrIncomeRuleDetails']['fee_carpet_area'] ?? 0, 3, '.', ''),
                            'unit' => ''
                        ],
                        [
                            'label' => 'Fee / Sqft Open Area / Month',
                            'value' => number_format($arrRuleData['arrIncomeRuleDetails']['fee_open_area'] ?? 0, 3, '.', ''),
                            'unit' => ''
                        ]
                    ]
                ];
            } elseif ($feeRate === 'percentage') {
                $response['details'] = [
                    'type' => 'percentage',
                    'charges' => [
                        [
                            'label' => 'Percentage Of',
                            'value' => ucwords(preg_replace('/([A-Z])/', ' $1', $arrallmemberIncomeAccount[$arrRuleData['arrIncomeRuleDetails']['other_percent_of_income_account']] ?? '')),
                            'unit' => '%'
                        ]
                    ]
                ];
            } elseif ($feeRate === 'fixed_and_inlet') {
                $response['details'] = [
                    'type' => 'fixed_and_inlet',
                    'charges' => [
                        [
                            'label' => 'Fixed Amount + Inlets',
                            'value' => number_format($arrRuleData['arrIncomeRuleDetails']['other_income_fee'] ?? 0, 3, '.', ''),
                            'unit' => '/per inlet + fixed'
                        ]
                    ]
                ];
            } elseif ($feeRate === 'inlet') {
                $response['details'] = [
                    'type' => 'inlet',
                    'charges' => [
                        [
                            'label' => 'Per Water Inlet',
                            'value' => number_format($arrRuleData['arrIncomeRuleDetails']['other_income_fee'] ?? 0, 3, '.', ''),
                            'unit' => '/per water inlet'
                        ]
                    ]
                ];
            } elseif ($feeRate === 'custom_rule') {
                $response['details'] = [
                    'type' => 'custom_rule',
                    'charges' => [
                        [
                            'label' => 'Custom Rule Amount',
                            'value' => number_format($arrRuleData['arrIncomeRuleDetails']['other_income_fee'] ?? 0, 3, '.', ''),
                            'unit' => 'as per custom rule'
                        ]
                    ]
                ];
            } else {
                $response['details'] = [
                    'type' => 'unknown',
                    'charges' => [
                        [
                            'label' => 'Amount',
                            'value' => number_format($arrRuleData['arrIncomeRuleDetails']['other_income_fee'] ?? 0, 3, '.', ''),
                            'unit' => $feeRate
                        ]
                    ]
                ];
            }
        }

        // Handle Floor Based Rule
        if ($response['rule'] === 'floorbased') {
            $response['type'] = 'floor_based';
            $response['label'] = 'Floor Based Rule';
            $response['details'] = [];

            $floorBasedRules = $arrRuleData['arrFloorBasedRule'] ?? [];
            $response['details']['type'] = 'simple'; // Default to simple
            $response['details']['charges'] = [];

            foreach ($floorBasedRules as $rule) {
                $rule = json_decode(json_encode($rule), true);

                $ruleType = ($rule['floor_to'] != 0) ? 'slab' : 'simple';
                $response['details']['type'] = $ruleType; // Update to slab if applicable

                $response['details']['charges'][] = [
                    'floor_from' => $rule['floor_from'] ?? null,
                    'floor_to' => $rule['floor_to'] ?? null,
                    'amount' => number_format($rule['fee_carpet_area'] ?? 0, 3, '.', ''),
                    'unit' => '/Sqft Total Area /Month',
                ];
            }

            return $response;
        }


        if (!empty($arrRuleData['arrIncomeRuleDetails']['tax_exemptions'])) {
            $taxExemptions = explode(',', $arrRuleData['arrIncomeRuleDetails']['tax_exemptions']);
            $response['tax_exemptions'] = array_map(function ($tax) use ($arrRuleData) {
                return $arrRuleData['arrTaxexemption'][$tax] ?? '';
            }, $taxExemptions);
        }

        return $response;
    }


}
