<?php

namespace App\Console\Commands\Actions\Settings\AutoInvoicingRule;

use Illuminate\Console\Command;
use App\Console\Commands\Action;

class LatePaymentChargeRuleDataSource extends Action
{
    protected $signature = 'datasource:latePaymentChargeRule {flowId} {parentId} {input}';

    protected $description = 'Late payment charge rule';

    protected $formatter = [
        "id" => "",
        "soc_id" => "",
        "type" => "setNa:type",
        "simple_interest" => "setPercentage:simple_interest",
        "grace_period" => "setDays:grace_period",
        "effective_date" => "setNa:effective_date",
        "calculate_from" => "setNa:calculate_from",
        "calculate_for" => "setNa:calculate_for",
        "status" => "",
    ];

    protected $formatterKey = ['id'];

    protected $mapper = [
        "id" => "id",
    ];

    protected $schema = [
        'table' => [
            'tableTitle' => '',
            "tabs" => [
                "Active Rules",
                "Upcoming Rules",
                "Past Rules",
                "Late Payment Rule"
            ],
            "actions" => [
                [
                    "title" => "New Rule",
                    "icon" => "ri-add-circle-line",
                    "color" => "primary",
                    "form" => "newRule",
                    "variant" => "contained"
                ],
                [
                    "title" => "New Late Charges Rule",
                    "icon" => "ri-add-circle-line",
                    "color" => "success",
                    "form" => "newLateChargesRule",
                    "variant" => "contained"
                ]
            ],
            'fields' => [
                '*'
            ],
            'columns' => [
                [
                    'title' => 'Late Charges',
                    'key' => 'simple_interest'
                ],
                [
                    'title' => 'Grace Period',
                    'key' => 'grace_period'
                ],
                [
                    'title' => 'Effective Date',
                    'key' => 'effective_date'
                ],
                [
                    'title' => 'Late charges will apply from',
                    'key' => 'calculate_from',
                    'options' => [
                        'duedate' => [
                            'title' => 'Due Date',
                        ],
                        'billdate' => [
                            'title' => 'Bill Date',
                        ],
                        'both' => [
                            'title' => 'Both',
                        ]
                    ]
                ],
                [
                    'title' => 'Late charges will apply for',
                    'key' => 'calculate_for',
                    'options' => [
                        'perday' => [
                            'title' => 'Per Day',
                        ],
                        'billperiod' => [
                            'title' => 'Bill Period',
                        ]
                    ]
                ],
                [
                    'title' => 'Status',
                    'key' => 'status',
                    "type" => "chip",
                    "options" => [
                        "Active" => [
                            "title" => "Active",
                            "color" => "success"
                        ],
                        "Past" => [
                            "title" => "Past",
                            "color" => "error"
                        ],
                        "Upcoming" => [
                            "title" => "Upcoming",
                            "color" => "warning"
                        ],
                    ]
                ],
                [
                    "title" => "Actions",
                    "type" => "actions",
                    "key" => "actions",
                    "actions" => [
                        [
                            "title" => "Edit Rule",
                            "icon" => "ri-edit-box-line",
                            "form" => "edit_late_charge_rule",
                        ],

                    ]
                ]
            ]
        ]
    ];

    public function apply()
    {
        // $type = $this->input['late_payment_charge_type'] ?? 'maintenance';
        $type = $this->input['late_payment_charge_type'] ?? 'incidental';

        // $financialYear = $this->tenantDB()->table('soc_account_financial_year_master')
        //     ->select('fy_start_date', 'fy_end_date')
        //     ->where('confirmed', 1)
        //     ->first();


        $page = $this->input['page'] ?? 1;
        $per_page = !empty($this->input['per_page']) ? $this->input['per_page']: ($this->hugeData ? 100000 : 10);
        $offset = ($page - 1) * $per_page;

        $this->data = $this->tenantDB()->table('income_late_payment_charges')
            ->where('soc_id', $this->input['company_id'])
            // ->where('effective_date' , '<=', date('Y-m-d'))
            ->where('type', $type)
            ->orderBy('effective_date', 'desc')
            ->offset($offset)
            ->limit($per_page)
            ->get();

            // dd($this->data);

        // add a status column to the data on basis of effective date and financial year
        $isActiveSet = false;
        foreach ($this->data as $key => $value) {
            // calculate_from billdate => bill_date, duedate => due_date, both => both
            $this->data[$key]->calculate_from = $this->data[$key]->calculate_from == 'billdate' ? 'Bill Date' : ($this->data[$key]->calculate_from == 'duedate' ? 'Due Date' : 'Both');

            // calculate_for perday => per_day, billperiod => bill_period
            $this->data[$key]->calculate_for = $this->data[$key]->calculate_for == 'perday' ? 'Per Day' : 'Bill Period End';

            if($value->effective_date > date('Y-m-d')){
                 $this->data[$key]->status = 'Upcoming';
            }elseif($value->effective_date <= date('Y-m-d') && $isActiveSet == true){
                 $this->data[$key]->status = 'Past';
            }elseif($value->effective_date <= date('Y-m-d') && $isActiveSet == false){
                 $this->data[$key]->status = 'Active';
                $isActiveSet = true;
            }
        }

        $totalCount = $this->tenantDB()->table('income_late_payment_charges')
            ->where('soc_id', $this->input['company_id'])
            ->where('effective_date' , '<=', date('Y-m-d'))
            ->where('type', $type)
            ->count();

        $this->data=$this->format($this->data);
        $this->meta['schema'] = $this->schema;
        $this->meta['pagination']['total'] = $totalCount;

    }

    public function setDays($value)
    {
       return  $value . ' Days';
    }

    public function setNa($value)
    {
        if($value===null || $value===''){
            return 'N\A';
        }else{
            return $value;
        }
    }

    public function setPercentage($value)
    {

        $value = (float) $value;
        return number_format($value, 2). "(%)" ;
    }



}
