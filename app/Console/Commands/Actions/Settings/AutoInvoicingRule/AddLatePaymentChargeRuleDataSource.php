<?php

namespace App\Console\Commands\Actions\Settings\AutoInvoicingRule;

use Illuminate\Console\Command;
use App\Console\Commands\Action;

class AddLatePaymentChargeRuleDataSource extends Action
{
    protected $signature = 'datasource:addLatePaymentChargeRule {flowId} {parentId} {input}';

    protected $description = 'Add late payment charge rule';

    public function apply()
    {
        $simple_interest = $this->input['simple_interest'];
        $grace_period = $this->input['grace_period'];
        $effective_date = $this->input['effective_date'];
        $calculate_from = $this->input['calculate_from'];
        $calculate_for = $this->input['calculate_for'];
        $interest_amount_type = $this->input['interest_amount_type'];
        $interest_type = $this->input['interest_type'] ?? '';
        $type = $this->input['type'] ?? '';
        $applicable_taxes = $this->input['applicable_taxes'] ?? '';

        $rule = $this->tenantDB()->table('income_late_payment_charges')
            ->insert([
                'simple_interest' => $simple_interest,
                'grace_period' => $grace_period,
                'grace_duration' => $grace_period,
                'effective_date' => $effective_date,
                'calculate_from' => $calculate_from,
                'calculate_for' => $calculate_for,
                'interest_amount_type' => $interest_amount_type,
                'interest_type' => $interest_type,
                'type' => $type,
                'soc_id' => $this->input['company_id'],
                'applicable_taxes' => $applicable_taxes,
                'created_date' => date('Y-m-d H:i:s'),
                'created_by' => $this->input['created_by'] ?? 0,
                'updated_date' => date('Y-m-d H:i:s'),
                'updated_by' => $this->input['updated_by'] ?? 0,
            ]);

        if($rule)
        {
            $this->status = 'success';
            $this->message = 'Late payment charge rule added successfully';
            $this->statusCode = 200;
        }
        else
        {
            $this->status = 'error';
            $this->message = 'Unable to add late payment charge rule';
            $this->statusCode = 400;
        }

    }
}