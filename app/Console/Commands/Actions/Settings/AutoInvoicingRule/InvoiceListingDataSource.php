<?php

namespace App\Console\Commands\Actions\Settings\AutoInvoicingRule;

use Illuminate\Console\Command;
use App\Console\Commands\Action;
use Illuminate\Database\Eloquent\Casts\Json;
use Illuminate\Support\Facades\DB;

class InvoiceListingDataSource extends Action
{
    protected $signature = 'datasource:invoiceListing {flowId} {parentId} {input}';

    protected $description = 'Invoice Listing';

    public function apply()
    {
        $autoInvoicingRules = [];

        // Preload unit types
        $unitTypes = $this->tenantDB()->table('income_invoice_rules')
            ->select('unit_type', 'soc_units_type_id')
            ->distinct()
            ->get();

        // Prepare Recent Invoice Data Lookup
        $arrRecentInvoiceDate = $this->getRecentGeneratedInvoiceDate(array('soc_id' => $this->input['company_id']));
        $arrRecentParkingInvoiceDate = $this->getParkingInvoiceRuleId(array('soc_id' => $this->input['company_id']));
        $arrRecentInvoiceDate = $this->getRecentlyUsedRuleDetail(array(
            'arrRecentInvoiceDate' => $arrRecentInvoiceDate,
            'arrRecentParkingInvoiceDate' => $arrRecentParkingInvoiceDate,
        ));

        $arrListenerdata = [];
        $arrListenerdata['effective_date'] = isset($this->input['effective_date']) ? $this->input['effective_date'] : date('Y-m-d');
        $arrUnitwiseRule  = $this->getAllUnitwiseRule($arrListenerdata);

        $this->data = $arrUnitwiseRule['data'];
    }

    public function getAllUnitwiseRule($data)
    {
        if (!isset($data['effective_date']) || !isset($this->input['company_id'])) {
            return array();
        }
        $soc_id = $this->input['company_id'];
        $effective_date = $data['effective_date']; // Assign your $data['effective_date'] value

        // Query using Laravel Eloquent
        $objInvoiceRuleDetails = $this->tenantDB()->table('income_invoice_rules') // Replace 'income_invoice_rules' with the actual table name
            ->select('soc_units_type_id', 'unit_type', 'income_account_id', 'id', 'effective_date')
            ->where('soc_id', $soc_id)
            ->where('effective_date', '<=', $effective_date)
            ->orderBy('effective_date', 'ASC')
            ->get();

        // Convert result to an array, if needed
        $arrInvoiceRule = json_decode(json_encode($objInvoiceRuleDetails), true);

        if (!empty($objInvoiceRuleDetails)) {
            $arrInvoiceRuleDetails = $arrInvoiceRule;
            foreach ($arrInvoiceRuleDetails as $eachRule) {
                $arrInvoiceRule[$eachRule['soc_units_type_id']][$eachRule['income_account_id']] = $eachRule;
            }
            foreach ($arrInvoiceRule as $key => $eachRuleDetail) {
                foreach ($eachRuleDetail as $singleRule) {
                    $arrAllActiveRule[] = $singleRule;
                }
            }
        }
        $arrDisplayInvoiceListing = $this->getUnitwisedetails($arrAllActiveRule, $soc_id);
        return $arrDisplayInvoiceListing;
    }

    private function getUnitwisedetails($objInvoiceRuleDetail, $soc_id)
    {
        $arrDisplayInvoiceListing = [];

        // Convert to array if it's not already
        $arrInvoiceRuleDetails = is_array($objInvoiceRuleDetail)
            ? $objInvoiceRuleDetail
            : $objInvoiceRuleDetail->toArray();

        // Collect all Rule IDs
        $arrRuleIDs = collect($arrInvoiceRuleDetails)->pluck('id')->toArray();
        // Build the query
        $query = $this->tenantDB()->table('income_invoice_rules')
            ->leftJoin('income_accounts', 'income_invoice_rules.income_account_id', '=', 'income_accounts.account_id')
            ->leftJoin('chsone_tax_classes', 'income_invoice_rules.applicable_taxes', '=', 'chsone_tax_classes.tax_class_id')
            ->select('income_invoice_rules.*', 'income_accounts.display_name','income_accounts.account_id','income_accounts.account_name','chsone_tax_classes.tax_class_name') // Select all columns from income_invoice_rules and display_name from income_accounts
            ->where('income_accounts.soc_id', $soc_id)
            ->orderBy('effective_date', 'desc');

        if (!empty($arrRuleIDs)) {
            $query->whereIn('id', $arrRuleIDs);
        }

        $objInvoiceRuleDetails = $query->get();

        $arrFinalArray = [
            'arrDisplayInvoiceListing' => [],
            'applicableTax' => []
        ];

        $arrFinalArray = ['data' => []];

        foreach ($objInvoiceRuleDetails as $arrRuleDetails) {
            // Format the display name
            $arrRuleDetails->display_name = preg_replace('/([a-z])([A-Z])/', '$1 $2', $arrRuleDetails->display_name);
            // Find or create the parent group based on unit_type
            $foundKey = null;
            foreach ($arrFinalArray['data'] as $key => $parent) {
                if ($parent['account_name'] === $arrRuleDetails->unit_type) {
                    $foundKey = $key;
                    break;
                }
            } 
        
            // If not found, add a new parent group
            if ($foundKey === null) {
                $arrFinalArray['data'][] = [
                    // 'id' => $arrRuleDetails->soc_units_type_id,
                    'id' => $arrRuleDetails->unit_type, // as per discussion, use unit_type as ID
                    'account_name' => $arrRuleDetails->unit_type,
                    'disable' => 1,
                    'rows' => []
                ];
                $foundKey = count($arrFinalArray['data']) - 1; // Get the last index
            }
        
            // Append the current row to the parent's "rows"
            $arrFinalArray['data'][$foundKey]['rows'][] = [
                'id' => $arrRuleDetails->id,
                'account_name' => $arrRuleDetails->account_name,
                'soc_id' => $arrRuleDetails->soc_id,
                'soc_units_type_id' => $arrRuleDetails->soc_units_type_id,
                'unit_type' => $arrRuleDetails->unit_type,
                'income_account_id' => $arrRuleDetails->income_account_id,
                'applicable_taxes' => $arrRuleDetails->applicable_taxes,
                'tax_class_name' => $arrRuleDetails->tax_class_name,
                'late_payment_charges' => $arrRuleDetails->apply_late_payment_interest == 1 ? 'Y' : 'N',
                'effective_date' => $arrRuleDetails->effective_date,
                'rule' => $arrRuleDetails->rule,
                'optional_rule' => $arrRuleDetails->optional_rule,
                'tax_exemptions' => $arrRuleDetails->tax_exemptions,
                'fee_carpet_area' => $arrRuleDetails->fee_carpet_area,
                'fee_open_area' => $arrRuleDetails->fee_open_area,
                'fee_per_unit' => $arrRuleDetails->fee_per_unit,
                'fee_noc_owner' => $arrRuleDetails->fee_noc_owner,
                'fee_noc_tenant' => $arrRuleDetails->fee_noc_tenant,
                'fee_noc_vacant' => $arrRuleDetails->fee_noc_vacant,
                'fee_noc_electricity' => $arrRuleDetails->fee_noc_electricity,
                'fee_noc_water' => $arrRuleDetails->fee_noc_water,
                'fee_noc_maintenance' => $arrRuleDetails->fee_noc_maintenance,
                'other_income_fee' => $arrRuleDetails->other_income_fee,
                'other_income_fee_rate' => $arrRuleDetails->other_income_fee_rate,
                'other_percent_of_income_account' => $arrRuleDetails->other_percent_of_income_account,
                'created_date' => date('Y-m-d H:i:s'),
                'created_by' => $this->input['user_id'] ?? 0,
                'updated_date' => date('Y-m-d H:i:s'),
                'updated_by' => $this->input['user_id'] ?? 0,
                'selection' => $arrRuleDetails->selection
            ];
        }
        

        // Ensure "applicableTax" is processed separately
        if (!isset($arrFinalArray['applicableTax'])) {
            $arrFinalArray['applicableTax'] = [];
        }


        return $arrFinalArray;
    }



    public function getRecentGeneratedInvoiceDate(array $data): array
    {

        // Build the base query
        $queryBuilder = $this->tenantDB()->table('income_unit_invoices AS I')
            ->select(
                'U.fk_unit_category_id',
                'U.unit_category',
                DB::raw('MAX(I.to_date) AS end_date')
            )
            ->join('chsone_units_master AS U', 'U.unit_id', '=', 'I.fk_unit_id')
            ->where('I.soc_id', $data['soc_id'])
            ->where('I.status', '!=', 'cancelled');

        // Add condition for category IDs if provided
        if (!empty($data['arrCategoryId']) && count($data['arrCategoryId']) > 0) {
            $queryBuilder->whereIn('U.fk_unit_category_id', $data['arrCategoryId']);
        }

        // Group by category ID
        $queryBuilder->groupBy('U.fk_unit_category_id');


        // Execute the query
        $resultset = $queryBuilder->get();
        
        // Replace dd with a safer debug approach
        $arrResult = [];
        if (!$resultset->isEmpty()) {
            $maxEffectiveDate = null;

            foreach ($resultset as $eachRow) {
                $arrResult[$eachRow->fk_unit_category_id] = (array) $eachRow;
                $effectiveDate = date('Y-m-d', strtotime("+1 day", strtotime($eachRow->end_date)));
                $arrResult[$eachRow->fk_unit_category_id]['effective_date'] = $effectiveDate;

                if (is_null($maxEffectiveDate)) {
                    $maxEffectiveDate = $effectiveDate;
                } else {
                    $maxEffectiveDate = max($maxEffectiveDate, $effectiveDate);
                }
            }

            // Add max effective date to the result
            $arrResult['maxEffectiveDate'] = $this->getDisplayDate($maxEffectiveDate);
        }

        return $arrResult;
    }
    public function getParkingInvoiceRuleId(array $data): array
    {
        $arrResult = [];

        // Build the query
        $queryBuilder = $this->tenantDB()->table('income_invoice_rules AS R')
            ->select(
                DB::raw('MAX(I.to_date) AS end_date'),
                DB::raw('MAX(I.invoice_number) AS invoice_number')
            )
            ->join('income_invoice_particular AS P', 'R.id', '=', 'P.fk_rule_id')
            ->leftJoin('income_unit_invoices AS I', 'I.unit_invoice_id', '=', 'P.fk_unit_invoice_id')
            ->where('R.soc_id', $data['soc_id'])
            ->where('I.status', '!=', 'cancelled')
            ->where('R.unit_type', 'parking');

        // Execute the query
        $resultset = $queryBuilder->first();

        // Process the result
        if ($resultset && !empty($resultset->invoice_number)) {
            $arrResult[0]['fk_unit_category_id'] = 0;
            $arrResult[0]['unit_category'] = 'Parking';
            $arrResult[0]['end_date'] = $resultset->end_date;
            $arrResult[0]['effective_date'] = date('Y-m-d', strtotime("+1 day", strtotime($arrResult[0]['end_date'])));
            $arrResult['maxEffectiveDate'] = $this->getDisplayDate($arrResult[0]['effective_date']);
        }

        return $arrResult;
    }


    public function getRecentlyUsedRuleDetail(array $data = []): array
    {
        // Check if recent invoice dates exist
        if (!empty($data['arrRecentInvoiceDate']) && count($data['arrRecentInvoiceDate']) > 0) {
            // Check if recent parking invoice dates exist
            if (!empty($data['arrRecentParkingInvoiceDate']) && count($data['arrRecentParkingInvoiceDate']) > 0) {
                // Replace the first recent invoice date with the parking invoice date
                $data['arrRecentInvoiceDate'][0] = $data['arrRecentParkingInvoiceDate'][0];

                // Sort the array by keys
                ksort($data['arrRecentInvoiceDate']);

                // Calculate the max effective date
                $data['arrRecentInvoiceDate']['maxEffectiveDate'] = $this->getDatabaseDate($data['arrRecentInvoiceDate']['maxEffectiveDate']) >=
                    $this->getDatabaseDate($data['arrRecentParkingInvoiceDate']['maxEffectiveDate'])
                    ? $data['arrRecentInvoiceDate']['maxEffectiveDate']
                    : $data['arrRecentParkingInvoiceDate']['maxEffectiveDate'];
            }

            return $data['arrRecentInvoiceDate'];
        }

        return [];
    }

    public function getDisplayDate($date)
    {
        return date('d/m/Y', strtotime($date)); // Adjust format as per requirements
    }
}
