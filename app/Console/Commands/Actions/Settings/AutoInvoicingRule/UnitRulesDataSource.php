<?php

namespace App\Console\Commands\Actions\Settings\AutoInvoicingRule;

use Illuminate\Console\Command;
use App\Console\Commands\Action;
use Illuminate\Support\Facades\DB;

class UnitRulesDataSource extends Action
{
    protected $signature = 'datasource:unitRules {flowId} {parentId} {input}';

    protected $description = 'Unit Rules Data Source';

    public function apply()
    {
        $id = $this->input['id'];

        $id = explode('_', $id);
        if (count($id) > 1) {
            $id = $id[2];
        }
        $newObj = [];

        $query = $this->tenantDB()->table('income_invoice_rules as iir')
            ->select(
                'iir.id',
                'ia.display_name',
                'ia.account_name',
                'chsone_tax_classes.tax_class_name as tax_class_name',
                'iir.effective_date',
                DB::raw('CASE WHEN iir.rule = "standard" THEN "Standard Rule" ELSE "Parking" END as rule'),
                DB::raw('CASE WHEN iir.apply_late_payment_interest = 1 THEN "Y" ELSE "N" END as late_payment_charges')
            )
            ->leftJoin('income_accounts as ia', 'iir.income_account_id', '=', 'ia.account_id')
            ->leftJoin('chsone_tax_classes', 'iir.applicable_taxes', '=', 'chsone_tax_classes.tax_class_id')
            ->where('iir.soc_units_type_id', $id)
            ->orderBy('ia.display_name')
            ->orderBy('iir.effective_date');


        $result = $query->get();
        
        $unitTypes = $this->tenantDB()->table('income_invoice_rules')
            ->select('unit_type', 'soc_units_type_id')
            ->where('soc_units_type_id', $id)
            ->distinct()
            ->first();


        // Track the latest effective date for each display_name
        $currentDate = date('Y-m-d');
        $latestEffectiveDates = [];

        // First, find the latest effective dates for each display name that are not in the future
        foreach ($result as $row) {
            if ($row->effective_date <= $currentDate) {
                if (!isset($latestEffectiveDates[$row->display_name]) || $row->effective_date > $latestEffectiveDates[$row->display_name]) {
                    $latestEffectiveDates[$row->display_name] = $row->effective_date;
                }
            }
        }

        $newObj = [
            "id" => "",
            "display_name" =>  $unitTypes ? $unitTypes->unit_type : "",
            "account_name" => "Rule for - " . ($unitTypes ? $unitTypes->unit_type : ""),
            "tax_class_name" => "",
            "effective_date" => "",
            "rule" => "",
            "late_payment_charges" => "",
            "status" => "",
            "disable" => 1,
        ];

        // Then, update the status for each row
        foreach ($result as $row) {

            if ($row->effective_date > $currentDate) {
                $row->status = "Upcoming";
            } else {
                if (isset($latestEffectiveDates[$row->display_name]) && $row->effective_date == $latestEffectiveDates[$row->display_name]) {
                    $row->status = "Active";
                } else {
                    $row->status = "Past";
                }
            }
        }

        $result->transform(function ($account) {
            // Insert spaces before each uppercase letter
            $account->account_name = preg_replace('/(?<!^)([A-Z])/', ' $1', $account->account_name);
            return $account;
        });

        $newObj['rows'] = $result;

        //$this->data = $newObj;
        $this->data = [$newObj];
    }
}
