<?php

namespace App\Console\Commands\Actions\Settings\AutoInvoicingRule;

use Illuminate\Console\Command;
use App\Console\Commands\Action;
use Illuminate\Support\Facades\DB;

class GetUnitTypesDataSource extends Action
{
    protected $signature = 'datasource:getUnitTypes {flowId} {parentId} {input}';

    protected $description = 'Get Unit Types Data Source';

    public function apply()
    {
        $unitTypes = $this->tenantDB()->table('chsone_society_units_tpl')
            ->select('soc_units_type_id', 'type')
            ->whereIn('soc_unit_type', ['flat', 'shop','gym','penthouse','office','duplex','bedsit'])
            ->get();
        
        if(!$unitTypes)
        {
            $this->status = 'error';
            $this->message = 'Unit Types not found';
            $this->statusCode = 400;
        }
        else{
            $this->data = $unitTypes;
        }
    }
}