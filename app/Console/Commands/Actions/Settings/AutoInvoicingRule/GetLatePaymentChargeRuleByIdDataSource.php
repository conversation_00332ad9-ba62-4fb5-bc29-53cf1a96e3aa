<?php

namespace App\Console\Commands\Actions\Settings\AutoInvoicingRule;
use Illuminate\Support\Facades\DB;

use Illuminate\Console\Command;
use App\Console\Commands\Action;

class GetLatePaymentChargeRuleByIdDataSource extends Action
{
    protected $signature = 'datasource:getLatePaymentChargeRuleById {flowId} {parentId} {input}';

    protected $description = 'Get late payment charge rule by id';

    public function apply()
    {

        $id = $this->input['id'];

        $rule = $this->tenantDB()->table('income_late_payment_charges')
        ->select(
            'id',
            'simple_interest',
            'grace_period',
            DB::raw('CAST(grace_duration AS UNSIGNED) AS grace_duration'),
            'effective_date',
            'calculate_from',
            'calculate_for',
            'created_date',
            'interest_type',
            'created_by',
            'updated_date',
            'updated_by',
            'soc_id',
            'interest_amount_type',
            'interest_type',
            'type',
            DB::raw('CAST(applicable_taxes AS UNSIGNED) AS applicable_taxes'),
        )
         ->where('id', $id)
        ->first();

        if (!$rule) {
            $this->message = "Late payment charge rule not found";
            $this->statusCode = 400;
            $this->status = "error";
            return ;

        }else{
            $this->message = "Late payment charge rule found successfully";
            $this->statusCode = 200;
            $this->status = "success";
            $this->data = $rule;
            
        }

    }
}
