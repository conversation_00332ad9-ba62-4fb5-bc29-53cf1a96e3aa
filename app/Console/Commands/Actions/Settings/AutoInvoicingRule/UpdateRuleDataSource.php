<?php

namespace App\Console\Commands\Actions\Settings\AutoInvoicingRule;

use Illuminate\Console\Command;
use App\Console\Commands\Action;

class UpdateRuleDataSource extends Action
{
    protected $signature = 'datasource:updateRule {flowId} {parentId} {input}';

    protected $description = 'Update rule';

    protected $parking_account_id = 2;

    public function apply()
    {
        $id = $this->input['invoiceruleid'];

        $applicable_taxes = $this->input['applicable_taxes'] ?? '';
        $apply_late_payment_interest = $this->input['apply_late_payment_interest'] ?? 0;
        $effective_date = $this->input['effective_date'] ?? date('Y-m-d');
        $tax_exemptions = $this->input['tax_exemptions'] ?? '';
        $fee_carpet_area = $this->input['fee_carpet_area'] ?? 0;
        $fee_open_area = $this->input['fee_open_area'] ?? 0;
        $fee_per_unit = $this->input['fee_per_unit'] ?? 0;
        $fee_noc_owner = $this->input['fee_noc_owner'] ?? 0;
        $fee_noc_tenant = $this->input['fee_noc_tenant'] ?? 0;
        $fee_noc_vacant = $this->input['fee_noc_vacant'] ?? 0;
        $fee_noc_electricity = $this->input['fee_noc_electricity'] ?? 0;
        $fee_noc_water = $this->input['fee_noc_water'] ?? 0;
        $fee_noc_maintenance = $this->input['fee_noc_maintenance'] ?? 0;
        $other_income_fee = $this->input['other_income_fee'] ?? 0;
        $other_income_fee_rate = $this->input['other_income_fee_rate'] ?? 'none';
        $other_percent_of_income_account = $this->input['other_percent_of_income_account'] ?? 0;
        $updated_date = date('Y-m-d H:i:s');
        $updated_by = $this->input['updated_by'] ?? 0;
        
        $obj = $this->tenantDB()->table('income_invoice_rules')
            ->where('id', $id)
            ->update([
                'applicable_taxes' => $applicable_taxes,
                'apply_late_payment_interest' => $apply_late_payment_interest,
                'effective_date' => $effective_date,
                'tax_exemptions' => $tax_exemptions,
                'fee_carpet_area' => $fee_carpet_area,
                'fee_open_area' => $fee_open_area,
                'fee_per_unit' => $fee_per_unit,
                'fee_noc_owner' => $fee_noc_owner,
                'fee_noc_tenant' => $fee_noc_tenant,
                'fee_noc_vacant' => $fee_noc_vacant,
                'fee_noc_electricity' => $fee_noc_electricity,
                'fee_noc_water' => $fee_noc_water,
                'fee_noc_maintenance' => $fee_noc_maintenance,
                'other_income_fee' => $other_income_fee,
                'other_income_fee_rate' => $other_income_fee_rate,
                'other_percent_of_income_account' => $other_percent_of_income_account,
                'updated_date' => $updated_date,
                'updated_by' => $updated_by
            ]);

        $rule = $this->tenantDB()->table('income_invoice_rules')->where('id', $id)->first();

        if($rule->income_account_id == $this->parking_account_id)
        {
            $vehiclesData = $this->input['vehicles'];

            foreach($vehiclesData as $vehicle)
            {
                $parking_rule = $this->tenantDB()->table('income_parking_charges')
                    ->where('rule_id', $id)
                    ->where('member_type', 'owner')
                    ->update([
                        'fee_2_wheeler' => $vehicle['owner_fee_2_wheeler'] ?? 0,
                        'fee_4_wheeler' => $vehicle['owner_fee_4_wheeler'] ?? 0,
                        'updated_date' => $updated_date,
                        'updated_by' => $updated_by,
                        'status' => 1
                    ]);
                
                $parking_rule = $this->tenantDB()->table('income_parking_charges')
                    ->where('rule_id', $id)
                    ->where('member_type', 'tenant')
                    ->update([
                        'fee_2_wheeler' => $vehicle['tenant_fee_2_wheeler'] ?? 0,
                        'fee_4_wheeler' => $vehicle['tenant_fee_4_wheeler'] ?? 0,
                        'updated_date' => $updated_date,
                        'updated_by' => $updated_by,
                        'status' => 1
                    ]);
            }
        }

    }
}