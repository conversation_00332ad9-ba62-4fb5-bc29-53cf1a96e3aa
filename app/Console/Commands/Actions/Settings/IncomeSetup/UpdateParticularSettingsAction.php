<?php

namespace App\Console\Commands\Actions\Settings\IncomeSetup;
use Illuminate\Support\Facades\DB;

use App\Console\Commands\Action;

class UpdateParticularSettingsAction extends Action
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'action:updateParticularSettings {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Update Particular Settings';

    /**
     * Execute the console command.
     */

     public function apply()
    {
        $company_id = $this->input['company_id']; // Replace with actual company_id
        $data = $this->input['params'] ?? [];

        // If there is no data, exit
        if (empty($data)) {
            return;
        }

        // Prepare the CASE statements and parameters
        $caseStatements = [
            'display_name' => [],
            'display_order_id' => []
        ];
        $ids = [];

        foreach ($data as $item) {
            $account_id = $item['account_id'];
            $display_name = $item['display_name'];
            $display_order_id = $item['display_order_id'];

            $ids[] = $account_id;

            // Build CASE statements for each column
            $caseStatements['display_name'][] = "WHEN account_id = {$account_id} THEN '{$display_name}'";
            $caseStatements['display_order_id'][] = "WHEN account_id = {$account_id} THEN {$display_order_id}";
        }

        // Construct the SQL query
        $sql = "
            UPDATE income_accounts
            SET
                display_name = CASE " . implode(' ', $caseStatements['display_name']) . " ELSE display_name END,
                display_order_id = CASE " . implode(' ', $caseStatements['display_order_id']) . " ELSE display_order_id END
            WHERE soc_id = ? AND account_id IN (" . implode(',', array_fill(0, count($ids), '?')) . ")
        ";

        // Execute the raw query with the company_id and ids as parameters
        $this->tenantDB()->statement($sql, array_merge([$company_id], $ids));
}




    // public function bulkUpdateAccounts($company_id, $particularData)
    // {
    //     // Ensure $particularData is an array of objects
    //     $particularData = array_map(function($item) {
    //         return (object)$item;
    //     }, $particularData);

    //     // Initialize variables
    //     $caseStatements = [];
    //     $ids = [];
    //     $params = [];

    //     // Iterate over the data to build the CASE statements and parameters
    //     foreach ($particularData as $object) {
    //         if (isset($object->id)) {
    //             $id = $object->id;
    //             unset($object->id); // Remove the id key

    //             $ids[] = $id;

    //             foreach ($object as $key => $value) {
    //                 if (!isset($caseStatements[$key])) {
    //                     $caseStatements[$key] = [];
    //                 }
    //                 $caseStatements[$key][] = "WHEN account_id = ? THEN ?";
    //                 $params[] = $id;
    //                 $params[] = $value;
    //             }
    //         }
    //     }

    //     // If there are no ids, return early
    //     if (empty($ids)) {
    //         return;
    //     }

    //     // Construct the SQL query
    //     $sql = "UPDATE income_accounts SET ";
    //     $updateParts = [];

    //     foreach ($caseStatements as $key => $statements) {
    //         $updateParts[] = "{$key} = CASE " . implode(" ", $statements) . " ELSE {$key} END";
    //     }

    //     $sql .= implode(", ", $updateParts);
    //     $sql .= " WHERE soc_id = ? AND account_id IN (" . implode(',', array_fill(0, count($ids), '?')) . ")";

    //     // Execute the raw query with the parameters
    //     $this->tenantDB()->statement($sql, array_merge($params, [$company_id], $ids));
    // }





    // public function apply()
    // {
    //     $incomeAccount = $this->getParticularMemberDetails();
    //     $success = false;
    //     $error = false;
    //     $message = "";
    //     $data = [];

    //     if ($this->input['method'] == 'get') {
    //         return $this->responseSuccess($message, $incomeAccount);
    //     }
    //     $this->input['params'] = $this->input['params'] ?? "";
    //     $validator = Validator::make($this->input['params'], [
    //         'account_id.*' => 'required|numeric|exists:income_accounts,account_id',
    //         'display_name.*' => 'required',
    //         ],
    //         [
    //             "account_id.required" => "Account Id is required",
    //             "account_id.numeric" => "Invalid Account Id",
    //             "account_id.exists" => "No Account Id found"
    //         ]
    //     );
    //     if ($validator->fails()) {
    //         return $this->responseError($validator->messages());

    //     }
    //     foreach ($incomeAccount as $value) {
    //         $data[$value['account_id']] = $value['display_name'];
    //     }


    //     $params = $this->input['params'] ?? "";
    //     $reindexedParams = array_values($params);
    //     usort($reindexedParams, function($a, $b) {
    //         return $a['display_order_id'] - $b['display_order_id'];
    //     });

    //     foreach ($data as $accountId => $value) {
    //         $particular = IncomeAccount::where('account_id', $accountId)
    //                                     ->where('account_type', 'member')->first();

    //         if (!$particular) {
    //             continue;
    //         }

    //         $displayOrderIndex = array_search($accountId, array_column($params, 'account_id'));
    //         if ($displayOrderIndex !== false) {
    //             $param = $reindexedParams[$displayOrderIndex];
    //             $particular->display_name = $param['display_name'];
    //             $particular->display_order_id = $param['display_order_id'];
    //         }

    //         $particular->updated_date = now();
    //         if ($particular->save()) {
    //             $message = "Particular setting updated successfully.";
    //             $success = true;
    //         } else {
    //             $message = $particular->getMessages();
    //             $error = true;
    //         }
    //     }

    //     if ($success) {
    //         return $this->responseSuccess($message);
    //     }

    //     if ($error) {
    //         return $this->responseError($message);
    //     }
    // }

    public function responseError($message)
    {
        $this->statusCode = "400";
        $this->status = "error";
        $this->message = $message;
        $this->data = [];
        return false;
    }

    public function responseSuccess($message, $data=[])
    {
        $this->statusCode = "200";
        $this->status = "success";
        $this->message = $message;
        $this->data = $data;
        return true;
    }


}
