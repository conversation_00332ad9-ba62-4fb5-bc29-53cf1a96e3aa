<?php

namespace App\Console\Commands\Actions\Settings\IncomeSetup;

use App\Console\Commands\Action;
use App\Models\Tenants\{IncomeAccount};
use Illuminate\Support\Facades\Validator;

class ReadParticularSettingsLogAction extends Action
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'action:readParticularSettingsLog {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Read Particular Settings';

    /**
     * Execute the console command.
     */

    public function apply()
    {


        $result = $this->tenantDB()->table('income_accounts')
                                          ->where('account_type', 'member')
                                          ->select('account_id as id','account_id', 'account_name', 'display_name', 'display_order_id','created_date', 'created_by', 'updated_date', 'updated_by')
                                          ->orderBy('display_order_id', 'asc')
                                          ->first();


                                          $result = json_decode(json_encode($result), true);

                                          // dd($result);
                                          $created_by =        $this->getUserData( $result['created_by'] );
                                          $created_date =      $result['created_date'];
                                          $updated_by =        $this->getUserData( $result['updated_by'] );
                                          $updated_date =      $result['updated_date'];

                                          $result['created_by']= $created_by;
                                          $result['created_date']= $created_date;
                                          $result['updated_by'] = $updated_by;
                                          $result['updated_date'] = $updated_date;


                                          $this->data = $result;

    }

    public function getUserData ( $id ) {

        $obj = $this->MasterDB()->table( 'chsone_users_master as user' )
        ->select( 'user.user_id as id', 'user.user_name', 'user.user_first_name', 'user.user_last_name', 'user.user_email_id', 'user.user_mobile_number', 'user.user_type', 'user.user_source', 'user.user_lang_iso_639_3', 'user.user_gmt_time_zone', 'user.added_on', 'user.modified_on', 'user.role', 'user.status' )
        ->where( 'user.user_id', $id )
        ->first();

        if ( $id && $obj ) {
            return  $obj->user_first_name. ' '.$obj->user_last_name;
        } else {
            return '-';
        }

    }

    public function responseError($message)
    {
        $this->statusCode = "400";
        $this->status = "error";
        $this->message = $message;
        $this->data = [];
        return false;
    }

    public function responseSuccess($message, $data=[])
    {
        $this->statusCode = "200";
        $this->status = "success";
        $this->message = $message;
        $this->data = $data;
        return true;
    }


}
