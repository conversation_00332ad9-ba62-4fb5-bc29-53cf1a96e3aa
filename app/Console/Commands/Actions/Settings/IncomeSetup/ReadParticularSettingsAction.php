<?php

namespace App\Console\Commands\Actions\Settings\IncomeSetup;

use App\Console\Commands\Action;
use App\Models\Tenants\{IncomeAccount};
use Illuminate\Support\Facades\Validator;

class ReadParticularSettingsAction extends Action
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'action:readParticularSettings {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Read Particular Settings';

    /**
     * Execute the console command.
     */

    public function apply()
    {

        $incomeAccount = $this->tenantDB()->table('income_accounts')
                                          ->where('account_type', 'member')
                                          ->select('account_id as id','account_id', 'account_name', 'display_name', 'display_order_id')
                                          ->orderBy('display_order_id', 'asc')
                                          ->get();

        $totalCount =  $this->tenantDB()->table('income_accounts')
        ->where('account_type', 'member')
        ->count();
        $this->data = $incomeAccount;
        $this->meta['pagination']['total'] = $totalCount;

    }

    // public function apply()
    // {
    //     $incomeAccount = $this->getParticularMemberDetails();
    //     $success = false;
    //     $error = false;
    //     $message = "";
    //     $data = [];

    //     if ($this->input['method'] == 'get') {
    //         return $this->responseSuccess($message, $incomeAccount);
    //     }
    //     $this->input['params'] = $this->input['params'] ?? "";
    //     $validator = Validator::make($this->input['params'], [
    //         'account_id.*' => 'required|numeric|exists:income_accounts,account_id',
    //         'display_name.*' => 'required',
    //         ],
    //         [
    //             "account_id.required" => "Account Id is required",
    //             "account_id.numeric" => "Invalid Account Id",
    //             "account_id.exists" => "No Account Id found"
    //         ]
    //     );
    //     if ($validator->fails()) {
    //         return $this->responseError($validator->messages());

    //     }
    //     foreach ($incomeAccount as $value) {
    //         $data[$value['account_id']] = $value['display_name'];
    //     }


    //     $params = $this->input['params'] ?? "";
    //     $reindexedParams = array_values($params);
    //     usort($reindexedParams, function($a, $b) {
    //         return $a['display_order_id'] - $b['display_order_id'];
    //     });

    //     foreach ($data as $accountId => $value) {
    //         $particular = IncomeAccount::where('account_id', $accountId)
    //                                     ->where('account_type', 'member')->first();

    //         if (!$particular) {
    //             continue;
    //         }

    //         $displayOrderIndex = array_search($accountId, array_column($params, 'account_id'));
    //         if ($displayOrderIndex !== false) {
    //             $param = $reindexedParams[$displayOrderIndex];
    //             $particular->display_name = $param['display_name'];
    //             $particular->display_order_id = $param['display_order_id'];
    //         }

    //         $particular->updated_date = now();
    //         if ($particular->save()) {
    //             $message = "Particular setting updated successfully.";
    //             $success = true;
    //         } else {
    //             $message = $particular->getMessages();
    //             $error = true;
    //         }
    //     }

    //     if ($success) {
    //         return $this->responseSuccess($message);
    //     }

    //     if ($error) {
    //         return $this->responseError($message);
    //     }
    // }

    public function responseError($message)
    {
        $this->statusCode = "400";
        $this->status = "error";
        $this->message = $message;
        $this->data = [];
        return false;
    }

    public function responseSuccess($message, $data=[])
    {
        $this->statusCode = "200";
        $this->status = "success";
        $this->message = $message;
        $this->data = $data;
        return true;
    }


}
