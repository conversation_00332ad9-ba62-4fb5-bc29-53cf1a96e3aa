<?php

namespace App\Console\Commands\Actions\Settings\IncomeSetup;

use App\Console\Commands\Action;
use App\Models\Tenants\{
    IncomeAccount,
    IncomeInvoiceGeneralSetting
};
use GuzzleHttp\Client;
use Illuminate\Support\Facades\Validator;
use Config;

class PaymentReminderAction extends Action
{
    protected $constants;

    public function __construct() {
        parent::__construct();
        $this->constants = Config::get("constants");
    }
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'action:paymentReminder {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Payment Reminder';

    /**
     * Execute the console command.
     */
    public function apply()
    {
        $errors = [];
        $client = new Client();
        // $id = rand(10,100);
        $params = $this->input['params'] ?? "";

        if (in_array($this->input['method'], ['post', 'put', 'delete'])) {
            $validator = Validator::make($this->input, [
                'params' => 'required|array',
                'params.*.title' => 'required',
                'params.*.day' => 'required|numeric',
                'params.*.interval' => 'required|numeric|in:1,3',
                'params.*.type' => 'required|in:invoice_payment_reminder_before_due_date,invoice_payment_reminder_on_due_date,invoice_payment_reminder_after_due_date',
                'params.*.recuring' => 'boolean'
            ], [
                // Custom error messages
                'params.required' => 'Params field is required and should be an array.',
                'params.*.title.required' => 'Title is required.',
                'params.*.day.required' => 'Day is required.',
                'params.*.day.numeric' => 'Day should be a numeric value.',
                'params.*.interval.required' => 'Interval is required.',
                'params.*.interval.numeric' => 'Interval should be a numeric value.',
                'params.*.interval.in' => 'Interval must be either 1 or 3.',
                'params.*.type.required' => 'Type is required.',
                'params.*.type.in' => 'Type must be one of: invoice_payment_reminder_before_due_date, invoice_payment_reminder_on_due_date, invoice_payment_reminder_after_due_date.',
                'params.*.recuring.boolean' => 'Recuring should be a boolean value.'
            ]);

            // Check if validation fails
            if ($validator->fails()) {
                $this->status = "error";
                $this->statusCode = 400;
                $this->message = "Validation failed";
                $this->meta['errors'] = $validator->errors();
                //$this->message = $validator->errors(); // Retrieve validation errors
                return;
            }
        }


        $reminders = [];
        $store_reminders = IncomeInvoiceGeneralSetting::where('setting_key', "PAYMENT_REMAINDERS")
                                      ->first();
        if (empty($store_reminders)) {
            $settingObj = new IncomeInvoiceGeneralSetting();
            $settingObj->soc_id = $this->input['company_id'];
            $settingObj->setting_key = "PAYMENT_REMAINDERS";
            $settingObj->setting_value = json_encode($reminders);
            $settingObj->description = "";
            $settingObj->save();
            $store_reminders = $this->tenantDB()->table("income_invoice_general_settings")
                                          ->where('setting_key', "PAYMENT_REMAINDERS")
                                          ->first();
        } else {
            $reminderSetting = $store_reminders;
            $reminders = json_decode($reminderSetting->setting_value, true);
        }
        if ($this->input['method'] == 'get') {

            // set recuring to boolean value for frontend from 1 to true and 0 to false
            foreach ($reminders as $key => $reminder) {
                $reminders[$key]['recuring'] = $reminder['recuring'] == 1 ? true : false;
            }

            $message = 'Payment Reminders listed successfully';
            return $this->responseSuccess($message, $reminders);
        } else {
            // if method is delete then set day to empty for single reminder only
            foreach ($params as $key => $param) {
                if (isset($param["_action"]) && $param["_action"] == "delete") {
                    unset($params[$key]); // Remove the entire param
                } else {
                    $param['day'] = isset($param['day']) ? intval($param['day']) : 0;
                }
            }

            // dd($params);

            if(count($reminders)  > 0) {
                foreach ($reminders as $reminder) {
                    $reminder = (array)$reminder;
                    if (array_key_exists('queue_id', $reminder) && array_key_exists('job_id', $reminder)) {
                        if (!empty($reminder['queue_id']) && !empty($reminder['job_id'])) {
                            $removeData['queue_id'] = $reminder['queue_id'];
                            $removeData['job_id'] = $reminder['job_id'];
                            $removeData['log_id'] = $reminder['queue_id'];
                            $api = $this->constants['MEETING_URL']."api/v1/removeSociety";
                            $response = $client->post($api, ["form_params" => $removeData]);
                            $result = json_decode($response->getBody()->getContents(), true);
                        }
                    }
                }
            }

            $prepareArray = [];
            foreach ($params as $param) {
                $param['day'] = intval($param['day']);
                if (!empty($param['day']) && $param['day'] < 32) {
                    $data = [];
                    $data = [
                        'soc_id' => $this->input['company_id'],
                        'title' => $param['title'],
                        'day' => $param['day'],
                        'type' => $param['type'],
                        'recuring' => !empty($param['recuring']) && $param['recuring'] === true ? 1 : 0,
                        'id' => rand(10,100),
                        'interval' => $param['interval']
                    ];
                    $day = $data['day'];
                    $yearMonth = explode("-", date('Y-m-d'));
                    $year = $yearMonth[0];
                    $month = $yearMonth[1];
                    $interval = $data['interval'];
                    if($interval == '3') {
                        if($yearMonth[2] > $day) {
                            $month+=1;
                        }
                    } else {
                        if($yearMonth[2] > $day) {
                            $month++;
                        }
                    }
                    // if($yearMonth[2] > $day) {
                    //     $month++;
                    // }
                    if(!checkdate($month, $day, $year)) {
                        do {
                            if($month >= 12) {
                            $month = 1;
                            $year++;
                            } else {
                                $month++;
                            }
                        } while(!checkdate($month, $day, $year));
                    }
                    $date =  $year ."-".$month."-".$day." 12:00:00";

                    $data['date'] = $date;
                    $data['day'] = $day;

                    $arrReminderData = [
                        'soc_id' => $data['soc_id'],
                        'day' => $data['day'],
                        'type' => $data['type'],
                        'recuring' => $data['recuring'],
                        'date' => $data['date'],
                        'id' => $data['id'],
                        'interval' => $data['interval']
                    ];

                    $response = $this->addSocietyReminder($arrReminderData);

                    if ($response['success']['value'] == '1') {
                        $resIds = explode(",", $response['success']['message']);
                        if (count($resIds) > 1) {
                            $data['queue_id'] = $resIds[0];
                            $data['job_id'] = $resIds[1];
                            array_push($prepareArray, $data);
                        } else {
                            // array_push($errors, "Error in Payment Reminder saving.");
                            $message = 'Error in Payment Reminder saving.';
                            return $this->responseError($message);
                        }
                    } else {
                        // array_push($errors, "Error in Payment Reminder saving.");
                        $message = 'Error in Payment Reminder saving.';
                        return $this->responseError($message);
                    }
                } else {
                    // array_push($errors, "Day is required and should be less than 32.");
                    $message = 'Day is required and should be less than 32.';
                    return $this->responseError($message);
                }
            }
            $reminderSetting->setting_value = json_encode($prepareArray);
            $reminderSetting->save();
            if ($this->input['method'] == 'post') {
                $message = 'Payment Reminders Saved';
            } else if($this->input['method'] == 'put') {
                $message = 'Payment Reminders Updated';
            } else {
                $message = 'Payment Reminders Deleted';
            }
            return $this->responseSuccess($message);
        }
    }

    public function responseError($message)
    {
        $this->statusCode = "400";
        $this->status = "error";
        $this->message = $message;
        $this->data = [];
        return false;
    }

    public function responseSuccess($message, $data=[])
    {
        $this->statusCode = "200";
        $this->status = "success";
        $this->message = $message;
        $this->data = $data;
        return true;
    }

    public function addSocietyReminder($data)
    {
        $arrResponse = [];

        if (!empty($data['soc_id']) && !empty($data['day']) && !empty($data['type']) && $data['day'] < 32) {
            $formRequest = [
                intval($data['soc_id']),
                intval($data['day']),
                $data['type'],
                intval($data['recuring']),
                intval($data['id']),
                time(),
                intval($data['interval'])
            ];
            $date = !empty($data['date']) ? $data['date'] : date('Y-m-'.$data['day'].' 12:00:00');

            $apiRequest = [
                'type' => 'single',
                'action' => $this->constants['LIVE_SOCIETY_URL'].'meetingapi/v1/societyforreminder',
                'params' => json_encode($formRequest),
                'priority' => 'high',
                'wait_until' => $date
            ];
            $api = $this->constants['MEETING_URL']."api/v1/addSociety";
            $client = new Client();
            $result = $client->post($api, ['form_params' => $apiRequest]);
            $apiResponse = json_decode($result->getBody()->getContents(), true);
            if ($apiResponse['status_code'] == 200 || $apiResponse['status_code'] == 201) {
                $arrResponse = [
                    "success" => [
                        "value" => '1',
                        "message" => $apiResponse['data']['log_id'].','.$apiResponse['data']['job_id']
                    ]
                ];
            } else {
                $arrResponse = [
                    "error" => [
                        "value" => '1',
                        "message" => $apiResponse
                    ]
                ];
            }
        } else {
            $arrResponse = [
                "error" => [
                    "value" => '1',
                    "message" => $apiResponse
                ]
            ];
        }
        return $arrResponse;
    }
}
