<?php

namespace App\Console\Commands\Actions\Settings\Building;

use App\Console\Commands\Action;
use Illuminate\Support\Facades\DB;

class BuildingListDownloadDataSource extends Action
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'datasource:downloadBuildingList {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Get Buildings List';

    protected $formatter = [
        'id' => '',
        'soc_id' => '',
        'vizlog_building_id' => '',
        'soc_building_name' => '',
        'soc_building_floors' => '',
        'units_per_floor' => '',
        'cancel_date' => '',
        'cancellation_reason' => '',
        'status' => '',
        'created_date' => '',
        'created_by' => '',
        'updated_date' => '',
        'updated_by' => '',
        'unit_count' => '',
        'no_allotted_units' => '',
        'disabled' => '',
        'floor_array' => '',
    ];

    protected $formatterByKeys = ['id'];
    protected $hugeData = true;

    public function apply()
    {

        $result = $this->tenantDB()->table('chsone_societies_building_master as building')
            ->select('soc_building_id as id', 'soc_id', 'vizlog_building_id', 'soc_building_name', 'soc_building_floors', 'units_per_floor')
            ->selectRaw("CASE WHEN cancel_date LIKE '%0000-00-00' THEN 'NA' ELSE cancel_date END AS cancel_date")
            ->addSelect('cancellation_reason', 'status', DB::raw('DATE_FORMAT(created_date, "%d/%m/%Y") as created_date'), 'created_by', 'updated_date', 'updated_by')
            ->where('status', 1)
            ->orderBy('soc_building_name', 'asc')
            ->get();

        // get the unit count
        $result->map(function ($item) {
            $item->unit_count = $this->tenantDB()->table('chsone_units_master')->where('soc_building_id', $item->id)->count();
            return $item;
        });

        // get no_allotted_units count
        $result->map(function ($item) {
            $item->no_allotted_units = $this->tenantDB()->table('chsone_units_master')->where('soc_building_id', $item->id)->where('is_allotted', 1)->count();
            return $item;
        });

        // check the count of units and no_allotted_units and set the status
        $result->map(function ($item) {
            if ($item->unit_count == 0   || $item->no_allotted_units ==0) {
                $item->disabled =1;
            } else {
                $item->disabled = 0;
            }
            return $item;
        });

        // add new column floor_array to the result e.g if floor is 10 then floor_array will be [0,1,2,3,4,5,6,7,8,9,10] and if floor is 0 then floor_array will be [0]
        $result->map(function ($item) {
            $item->floor_array = range(0, $item->soc_building_floors);
            return $item;
        });


        $this->data = $result;

    }
}
