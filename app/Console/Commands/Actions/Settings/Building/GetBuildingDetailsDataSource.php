<?php

namespace App\Console\Commands\Actions\Settings\Building;

use Illuminate\Console\Command;
use App\Console\Commands\Action;

class GetBuildingDetailsDataSource extends Action
{
    protected $signature = 'datasource:getBuildingDetails {flowId} {parentId} {input}';

    protected $description = 'Get Building Details';

    protected $formatter = [
        'id' => '',
        'soc_building_name' => '',
        'soc_building_floors' => ''
    ];

    protected $formatterByKeys = ['id'];

    public function apply()
    {
        $id = $this->input['id'];
        $result = $this->tenantDB()->table('chsone_societies_building_master')
        ->select('soc_building_id as id', 'soc_building_name', 'soc_building_floors')
        ->where('soc_building_id', $id)
        ->first();
        // ->get();

        // $this->data = $this->format($result->toArray());
        $this->data = $result;
    }

}