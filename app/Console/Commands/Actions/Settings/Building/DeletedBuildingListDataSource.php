<?php

namespace App\Console\Commands\Actions\Settings\Building;

use Illuminate\Console\Command;
use App\Console\Commands\Action;
use Illuminate\Support\Facades\DB;

class DeletedBuildingListDataSource extends Action {
    /**
    * The name and signature of the console command.
    *
    * @var string
    */
    protected $signature = 'datasource:deletedBuildingList {flowId} {parentId} {input}';

    /**
    * The console command description.
    *
    * @var string
    */
    protected $description = 'Get Deleted Buildings List';

    protected $formatter = [
        'id' => '',
        'soc_id' => '',
        'vizlog_building_id' => '',
        'soc_building_name' => '',
        'soc_building_floors' => '',
        'units_per_floor' => '',
        'cancel_date' => '',
        'cancellation_reason' => '',
        'status' => '',
        'created_date' => '',
        'created_by' => '',
        'updated_date' => '',
        'updated_by' => ''
    ];

    protected $formatterByKeys = [ 'id' ];

    public function apply() {
        $page = $this->input['page'] ?? 1;
        $per_page = !empty($this->input['per_page']) ? $this->input['per_page']: ($this->hugeData ? 100000 : 10);
        $offset = ($page - 1) * $per_page;
        $obj = $this->tenantDB()->table( 'chsone_societies_building_master as building' )
        ->select( 'soc_building_id as id', 'soc_id', 'vizlog_building_id', 'soc_building_name', 'soc_building_floors', 'units_per_floor' )
        ->selectRaw( "
                CASE
                    WHEN cancel_date LIKE '%0000-00-00%' THEN 'NA'
                    ELSE DATE_FORMAT(cancel_date, '%d/%m/%Y')
                END AS cancel_date
            " )
        ->where( 'status', 0 )
        ->addSelect(
            'cancellation_reason',
            'status',
            DB::raw( "DATE_FORMAT(created_date, '%d/%m/%Y') as created_date" ),
            'created_by',
            DB::raw( value: "DATE_FORMAT(updated_date, '%d/%m/%Y') as updated_date" ),
            'updated_by'
        );

        $obj = $this->filter( $obj );
        $obj = $obj->orderBy( 'soc_building_id', 'asc' );

        $count = $obj->count();

       $obj=$obj->offset($offset);
       $obj=$obj->limit($per_page);

        $result = $obj->get();
        $this->data = $this->format( $result->toArray() );
        $this->meta['pagination']['total'] = $count;

    }
}
