<?php

namespace App\Console\Commands\Actions\Settings;

use Illuminate\Console\Command;
use App\Console\Commands\Action;

class SettingCommonTimestampDataSource extends Action
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'datasource:settingCommonTimestamp {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Get Current Timestamp for settings';

    protected $formatter = [
        'created_date' => '',
        'updated_date' => '',
        'created_by' => '',
        'updated_by' => '',
    ];

    protected $formatterByKeys = [];

    /**
     * Execute the console command.
     */
    public function apply()
    {
        $result = $this->tenantDB()->table('income_invoice_general_settings')
        ->selectRaw('MIN(created_date) as created_date, MAX(updated_date) as updated_date, created_by, updated_by')
        ->get();

        $created_by =        $this->getUserData( $result[ 0 ]->created_by );
        $updated_by =        $this->getUserData( $result[ 0 ]->updated_by );

        $result[ 0 ]->created_by = $created_by;
        $result[ 0 ]->updated_by = $updated_by;

        $this->data = $this->format($result->toArray());
    }

    public function getUserData ( $id ) {

        $obj = $this->MasterDB()->table( 'chsone_users_master as user' )
        ->select( 'user.user_id as id', 'user.user_name', 'user.user_first_name', 'user.user_last_name', 'user.user_email_id', 'user.user_mobile_number', 'user.user_type', 'user.user_source', 'user.user_lang_iso_639_3', 'user.user_gmt_time_zone', 'user.added_on', 'user.modified_on', 'user.role', 'user.status' )
        ->where( 'user.user_id', $id )
        ->first();

        if ( $id && $obj ) {
            return  $obj->user_first_name. ' '.$obj->user_last_name;
        } else {
            return '-';
        }

    }
}
