<?php

namespace App\Console\Commands\Actions\Settings\IncidentalInvoicingRule;

use Illuminate\Console\Command;
use App\Console\Commands\Action;

class UpcomingRuleDataSource extends Action
{
    public $signature = 'datasource:upcomingRule {flowId} {parentId} {input}';

    public $description = 'Get active rule data source';
    protected $formatter = [
        'id' => '',
        'particular' => 'setNA:particular',
        'rate' => 'getAmountPaid:rate',
        'rate_type' => 'setNA:rate_type',
        'duration' => 'setNA:duration',
        'effective_date' => 'setDate:effective_date',
        'ledger_id' => '',
        'tax_class_id' => '',
        'apply_late_payment_interest' => '',
        'created_date' => 'setDate:created_date',
        'created_by' => 'setNA:created_by',
        'updated_date' => 'setDate:updated_date',
        'updated_by' => 'setNA:updated_by',
        'ledger_account_name' => 'setNA:ledger_account_name',
        'tax_class_name' => 'setTax:tax_class_name',
    ];

    protected $schema = [
        'table' => [
            'tableTitle' => 'Incidental Invoicing Rules',
            "tabs" => [
                "Active Rules",
                "Upcoming Rules",
                "Past Rules",
                "Late Payment Rule"
            ],
            "actions" => [
                [
                    "title" => "New Rule",
                    "icon" => "ri-add-circle-line",
                    "color" => "primary",
                    "form" => "newRule",
                    "variant" => "contained"
                ],
                [
                    "title" => "New Late Charges Rule",
                    "icon" => "ri-add-circle-line",
                    "color" => "success",
                    "form" => "newLateChargesRule",
                    "variant" => "contained"
                ]
            ],
            'fields' => [
                '*'
            ],
            'columns' => [
                [
                    'title' => 'Particular',
                    'key' => 'particular'
                ],
                [
                    'title' => 'Charges ₹',
                    'key' => 'rate'
                ],
                [
                    'title' => 'Income Account',
                    'key' => 'ledger_account_name'
                ],
                [
                    'title' => 'Effective Date',
                    'key' => 'effective_date'
                ],
                [
                    'title' => 'Tax Class',
                    'key' => 'tax_class_name'
                ],
                [
                    'title' => 'Late charges',
                    'key' => 'apply_late_payment_interest',
                    'type' => 'checkbox',
                    'disable_on' => [
                        "apply_late_payment_interest" =>
                        [
                            1,
                            0
                        ]
                    ]
                ],
                [
                    'title' => 'Action',
                    'type' => 'actions',
                    'key' => 'actions',
                    'actions' => [
                        [
                          'title' => 'Edit Rule',
                          'icon' => 'ri-edit-box-line',
                          "form" => "editRule"
                        ],
                    ]
                ]
            ]
        ]
    ];

    public function apply()
    {
        // $financialYear = $this->tenantDB()->table('soc_account_financial_year_master')
        //     ->select('fy_start_date', 'fy_end_date')
        //     ->where('closed', 0)
        //     ->first();

        $page = $this->input['page'] ?? 1;
        $per_page = !empty($this->input['per_page']) ? $this->input['per_page']: ($this->hugeData ? 100000 : 10);
        $offset = ($page - 1) * $per_page;

        $result = $this->tenantDB()->table('income_common_area_charges as icac')
            ->select('icac.id',
            // 'icac.particular',
            $this->tenantDB()->raw('CONCAT(UPPER(LEFT(icac.particular, 1)), SUBSTRING(icac.particular, 2)) as particular'),
            'icac.rate',
            'icac.rate_type',
            'icac.duration',
            'icac.effective_date',
            'icac.ledger_id',
            'icac.tax_class_id',
            'icac.apply_late_payment_interest',
            'icac.created_date',
            'icac.created_by',
            'icac.updated_date',
            'icac.updated_by',
            $this->tenantDB()->raw('CONCAT(UPPER(LEFT(cglt.ledger_account_name, 1)), SUBSTRING(cglt.ledger_account_name, 2)) as ledger_account_name'),
            // 'cglt.ledger_account_name',
            $this->tenantDB()->raw('CONCAT(UPPER(LEFT(ctc.tax_class_name, 1)), SUBSTRING(ctc.tax_class_name, 2)) as tax_class_name'),
            // 'ctc.tax_class_name'
            )
            ->leftJoin('chsone_grp_ledger_tree as cglt', 'icac.ledger_id', '=', 'cglt.ledger_account_id')
            ->leftJoin('chsone_tax_classes as ctc', 'icac.tax_class_id', '=', 'ctc.tax_class_id')
            ->where('icac.effective_date', '>', now())
            ->orderByDesc('icac.effective_date')
            ->offset($offset)
            ->limit($per_page)
            ->get();

         $totalCount =$this->tenantDB()->table('income_common_area_charges as icac')
         ->leftJoin('chsone_grp_ledger_tree as cglt', 'icac.ledger_id', '=', 'cglt.ledger_account_id')
         ->leftJoin('chsone_tax_classes as ctc', 'icac.tax_class_id', '=', 'ctc.tax_class_id')
         ->where('icac.effective_date', '>', now())
         ->count();


        $this->data = $this->format($result);
        $this->meta['schema'] = $this->schema;
        $this->meta['pagination']['total'] = $totalCount;

    }

    public function getAmountPaid($payment_amount)
    {
        $payment_amount = (float) $payment_amount;

        return '₹ ' . number_format($payment_amount, 2);
    }

    public function setNA($value)
    {
        if($value === null || $value === ''){
            return 'N/A';
        }else{
            return $value;
        }
    }

    public function setTax($tax_class_name)
    {
        if($tax_class_name === null || $tax_class_name === ''){
            return 'No tax';
        }else{
            return $tax_class_name;
        }
    }

    public function setDate($value)
    {
        if($value === null || $value === ''){
            return 'N/A';
        }else{
            return date('d/m/Y', strtotime($value));
        }
    }

}
