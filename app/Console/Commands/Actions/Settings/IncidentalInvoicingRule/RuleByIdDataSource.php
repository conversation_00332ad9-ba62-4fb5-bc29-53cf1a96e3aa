<?php

namespace App\Console\Commands\Actions\Settings\IncidentalInvoicingRule;

use Illuminate\Console\Command;
use App\Console\Commands\Action;

class RuleByIdDataSource extends Action
{
    protected $signature = 'datasource:ruleById {flowId} {parentId} {input}';

    protected $description = 'Get a rule by id';

    public function apply()
    {
        $id = $this->input['id'];
        $result = $this->tenantDB()->table('income_common_area_charges as icac')
            ->select('icac.id', 'icac.particular', 'icac.rate', 'icac.rate_type', 'icac.duration', 'icac.effective_date', 'icac.ledger_id', 'icac.tax_class_id', 'icac.apply_late_payment_interest', 'icac.created_date', 'icac.created_by', 'icac.updated_date', 'icac.updated_by', 'cglt.ledger_account_name', 'ctc.tax_class_name')
            ->leftJoin('chsone_grp_ledger_tree as cglt', 'icac.ledger_id', '=', 'cglt.ledger_account_id')
            ->leftJoin('chsone_tax_classes as ctc', 'icac.tax_class_id', '=', 'ctc.tax_class_id')
            ->where('icac.id', $id)
            // ->get();
            ->first();

        if (!$result) {
            $this->message = 'Rule not found';
            $this->statusCode=400;
            $this->status='error';
            return;
        }
        else{
           $result = json_decode(json_encode($result), true);
            $result['apply_late_payment_interest'] = $result['apply_late_payment_interest'] ? true: false;
            $this->data = $result;

        }
    }
}