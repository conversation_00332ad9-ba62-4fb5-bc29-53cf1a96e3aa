<?php

namespace App\Console\Commands\Actions\Settings\IncidentalInvoicingRule;

use Illuminate\Console\Command;
use App\Console\Commands\Action;
use App\Models\Tenants\ChsoneGrpLedgerTree;
use App\Models\Tenants\IncomeCommonAreaCharge;

class CreateIncidentalRuleDataSource extends Action
{
    protected $signature = 'datasource:createIncidentalRule {flowId} {parentId} {input}';

    protected $description = 'Create Incidental Rule';

    public function apply()
    {
        $postdata = $this->input;
        $postNewActiveUpcomingDate = $postdata['effective_date'];
        $todaysdate = date("Y-m-d");

        $postNewLedgerId = $postdata['ledger_id'];

        // check ledger id is valid or not from ChsoneGrpLedgerTree table
        $ledgerId = ChsoneGrpLedgerTree::where('ledger_account_id', $postNewLedgerId)->count();
        if ($ledgerId == 0) {
            $this->status = 'error';
            $this->message = 'Ledger Id is invalid';
            $this->statusCode = 400;
            $this->data = [];
            return;
        }

        if ($postNewLedgerId != '') {

            if ($postNewActiveUpcomingDate >= $todaysdate) {

                // check existing data
                $incomeCommonBilling = IncomeCommonAreaCharge::where('soc_id', trim($postdata['company_id']))
                ->where('particular', trim($postdata['particular']))
                ->where('effective_date', trim($postdata['effective_date']))
                ->count();

                if ($incomeCommonBilling == 0) {
                    $incomeCommonBilling = new IncomeCommonAreaCharge();
                    $incomeCommonBilling->created_date = now(); // Use Laravel's `now()` helper for the current timestamp
                    $incomeCommonBilling->created_by = $postdata['user_id'];
                } else {
                    $this->status = 'error';
                    $this->message = 'Incidental Rule already exists';
                    $this->statusCode = 400;
                    $this->data = [];
                    return;
                }

            } 
            // else {
            //     $this->status = 'error';
            //     $this->message = 'Effective date should be greater than or equal to todays date';
            //     $this->statusCode = 400;
            //     $this->data = [];
            //     return;
            // }

        } else {
            $this->status = 'error';
            $this->message = 'Ledger Id is required or invalid';
            $this->statusCode = 400;
            $this->data = [];
            return;
        }


        $incomeCommonBilling->soc_id = $postdata['company_id'];
        $incomeCommonBilling->particular = $postdata['particular'];
        $incomeCommonBilling->rate = $postdata['rate'];
        $incomeCommonBilling->rate_type = $postdata['rate_type'] ?? 'fixed';
        $incomeCommonBilling->duration = $postdata['duration'] ?? 'day';
        $incomeCommonBilling->tax_class_id = $postdata['tax_class_id'] ?? 0;
        $incomeCommonBilling->add_to_maintenance = 0;
        $incomeCommonBilling->effective_date = $postdata['effective_date'];
        $incomeCommonBilling->till_date = '0000-00-00';
        $incomeCommonBilling->ledger_id = $postdata['ledger_id'];
        $incomeCommonBilling->apply_late_payment_interest = (int)($postdata['apply_late_payment_charge'] ?? false);
        $incomeCommonBilling->updated_date = date('Y-m-d H:i:s');
        $incomeCommonBilling->updated_by = $postdata['user_id'];

        if(!$incomeCommonBilling->save()) {
            $this->status = 'error';
            $this->message = 'Error in creating Incidental Rule';
            $this->statusCode = 400;
            $this->data = [];
            return;
        } else {
            $this->status = 'success';
            $this->message = 'Incidental Rule created successfully';
            $this->statusCode = 200;
            $this->data = [];
            return;

            if ($incomeCommonBilling == 0) {
                // Created new record
                $notificationTitle = 'Common bill rule added';
                $desc = "New {$postdata['rule_name']} rule has been created w.e.f. " . $incomeCommonBilling->effective_date;
            } else {
                // Updated existing record
                $notificationTitle = 'Common bill rule modified';
                $desc = "{$postdata['rule_name']} rule was modified w.e.f. " . $incomeCommonBilling->effective_date;
            }

            // Add notification to the database here
        }
    }
}