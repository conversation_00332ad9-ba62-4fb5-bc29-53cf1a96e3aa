<?php

namespace App\Console\Commands\Actions\Settings\IncidentalInvoicingRule;

use Illuminate\Console\Command;
use App\Console\Commands\Action;
use Illuminate\Support\Facades\DB;

class ActiveRuleDataSource extends Action
{
    public $signature = 'datasource:activeRule {flowId} {parentId} {input}';

    public $description = 'Get active rule data source';

    protected $formatter = [
        'id' => '',
        'particular' => 'setNA:particular',
        'rate' => 'getAmountPaid:rate',
        'rate_type' => 'setNA:rate_type',
        'duration' => 'setNA:duration',
        'effective_date' => 'setDate:effective_date',
        'ledger_id' => '',
        'tax_class_id' => '',
        'apply_late_payment_interest' => '',
        'created_date' => 'setDate:created_date',
        'created_by' => 'setNA:created_by',
        'updated_date' => 'setDate:updated_date',
        'updated_by' => 'setNA:updated_by',
        'ledger_account_name' => 'setNA:ledger_account_name',
        'tax_class_name' => 'setTax:tax_class_name',
    ];

    protected $formatterByKeys = ['id'];

    protected $mapper = [
        'id' => 'id',
    ];

    protected $schema = [
        'table' => [
            'tableTitle' => 'Incidental Invoicing Rules',
            "tabs" => [
                "Active Rules",
                "Upcoming Rules",
                "Past Rules",
                "Late Payment Rule"
            ],
            'fields' => [
                '*'
            ],
            "actions" => [
                [
                    "title" => "New Rule",
                    "icon" => "ri-add-circle-line",
                    "color" => "primary",
                    "form" => "newRule",
                    "variant" => "contained"
                ],
                [
                    "title" => "New Late Charges Rule",
                    "icon" => "ri-add-circle-line",
                    "color" => "success",
                    "form" => "newLateChargeRule",
                    "variant" => "contained"
                ]
            ],
            'columns' => [
                [
                    'title' => 'Particular',
                    'key' => 'particular'
                ],
                [
                    'title' => 'Charges ₹',
                    'key' => 'rate'
                ],
                [
                    'title' => 'Income Account',
                    'key' => 'ledger_account_name'
                ],
                [
                    'title' => 'Effective Date',
                    'key' => 'effective_date'
                ],
                [
                    'title' => 'Tax Class',
                    'key' => 'tax_class_name'
                ],
                [
                    'title' => 'Late charges',
                    'key' => 'apply_late_payment_interest',
                    'type' => 'checkbox',
                    'disable_on' => [
                        "apply_late_payment_interest" =>
                        [
                            1,
                            0
                        ]
                    ]
                ],
                [
                    "title" => "Actions",
                    "type" => "actions",
                    "key" => "actions",
                    "actions" => [
                        [
                            "title" => "Edit Rule",
                            "icon" => "ri-edit-box-line",
                            "form" => "edit_invoicing_rule",
                        ],

                    ]
                ]

            ]
        ]
    ];

    protected $hugeData = true;

    public function apply()
    {
        $page = $this->input['page'] ?? 1;
        $per_page = !empty($this->input['per_page']) ? $this->input['per_page'] : ($this->hugeData ? 100000 : 10);
        $company_id = $this->input['company_id'];
        $offset = ($page - 1) * $per_page;

        // $obj = $this->tenantDB()->table('income_common_area_charges as icac')
        //     ->leftJoin('chsone_grp_ledger_tree AS cglt', 'icac.ledger_id', '=', 'cglt.ledger_account_id')
        //     ->leftJoin('chsone_tax_classes AS ctc', 'icac.tax_class_id', '=', 'ctc.tax_class_id')
        //     ->where('icac.effective_date', '<=', now())
        //     ->where('icac.soc_id', $company_id)
        //     // ->groupBy('particular')
        //     // ->havingRaw('effective_date >= MAX(effective_date)')
        //     ->select(
        //         'id',
        //         'particular',
        //         'rate',
        //         'rate_type',
        //         'duration',
        //         'effective_date',
        //         'ledger_id',
        //         'icac.tax_class_id',
        //         'apply_late_payment_interest',
        //         'icac.created_date',
        //         'icac.created_by',
        //         'icac.updated_date',
        //         'icac.updated_by',
        //         'ledger_account_name',
        //         'tax_class_name'
        //     )
        //     ->offset($offset)
        //     ->limit($per_page)
        //     ->orderBy('icac.effective_date', 'desc')
        //     ->get();

        $obj = $this->tenantDB()->table('income_common_area_charges as icac')
            ->leftJoin('chsone_grp_ledger_tree AS cglt', 'icac.ledger_id', '=', 'cglt.ledger_account_id')
            ->leftJoin('chsone_tax_classes AS ctc', 'icac.tax_class_id', '=', 'ctc.tax_class_id')
            ->where('icac.effective_date', '<=', now())
            ->where('icac.soc_id', $company_id)
            ->whereIn('icac.id', function ($query) use ($company_id) {
                $query->select('icac.id')
                    ->from('income_common_area_charges as icac')
                    ->where('icac.soc_id', $company_id)
                    ->where('icac.effective_date', '<=', now())
                    ->whereRaw('icac.effective_date = (
                select max(inner_icac.effective_date)
                from income_common_area_charges as inner_icac
                where inner_icac.particular = icac.particular
                and inner_icac.soc_id = icac.soc_id
            )')
                    ->groupBy('icac.particular');
            })
            ->select(
                'icac.id',
                // 'icac.particular',
                $this->tenantDB()->raw('CONCAT(UPPER(LEFT(icac.particular, 1)), SUBSTRING(icac.particular, 2)) as particular'),
                'icac.rate',
                'icac.rate_type',
                'icac.duration',
                'icac.effective_date',
                'icac.ledger_id',
                'icac.tax_class_id',
                'icac.apply_late_payment_interest',
                'icac.created_date',
                'icac.created_by',
                'icac.updated_date',
                'icac.updated_by',
                $this->tenantDB()->raw('CONCAT(UPPER(LEFT(cglt.ledger_account_name, 1)), SUBSTRING(cglt.ledger_account_name, 2)) as ledger_account_name'),
                // 'cglt.ledger_account_name',
                $this->tenantDB()->raw('CONCAT(UPPER(LEFT(ctc.tax_class_name, 1)), SUBSTRING(ctc.tax_class_name, 2)) as tax_class_name'),
                // 'ctc.tax_class_name'
            )
            ->orderBy('icac.updated_date', 'desc')
            ->offset($offset)
            ->limit($per_page)
            ->get();

            //read above code and write count query to get total count of records
        $totalCount = $this->tenantDB()->table('income_common_area_charges as icac')
            ->leftJoin('chsone_grp_ledger_tree AS cglt', 'icac.ledger_id', '=', 'cglt.ledger_account_id')
            ->leftJoin('chsone_tax_classes AS ctc', 'icac.tax_class_id', '=', 'ctc.tax_class_id')
            ->where('icac.effective_date', '<=', now())
            ->where('icac.soc_id', $company_id)
            ->whereIn('icac.id', function ($query) use ($company_id) {
                $query->select('icac.id')
                    ->from('income_common_area_charges as icac')
                    ->where('icac.soc_id', $company_id)
                    ->where('icac.effective_date', '<=', now())
                    ->whereRaw('icac.effective_date = (
                    select max(inner_icac.effective_date)
                    from income_common_area_charges as inner_icac
                    where inner_icac.particular = icac.particular
                    and inner_icac.soc_id = icac.soc_id
                )')
                    ->groupBy('icac.particular');
            })
            ->select(
                'icac.id',
                'icac.particular',
                'icac.rate',
                'icac.rate_type',
                'icac.duration',
                'icac.effective_date',
                'icac.ledger_id',
                'icac.tax_class_id',
                'icac.apply_late_payment_interest',
                'icac.created_date',
                'icac.created_by',
                'icac.updated_date',
                'icac.updated_by',
                'cglt.ledger_account_name',
                'ctc.tax_class_name'
            )
            ->get()
            ->count();


        $this->data = $this->format($obj);
        $this->meta['schema'] = $this->schema;
        $this->meta['pagination']['total'] = $totalCount;
    }

    public function getAmountPaid($payment_amount)
    {
        $payment_amount = (float) $payment_amount;

        return '₹ ' . number_format($payment_amount, 2);
    }

    public function setNA($value)
    {
        if ($value === null || $value === '') {
            return 'N/A';
        } else {
            return $value;
        }
    }

    public function setTax($tax_class_name)
    {
        if ($tax_class_name === null || $tax_class_name === '') {
            return 'No tax';
        } else {
            return $tax_class_name;
        }
    }

    public function setDate($value)
    {
        if ($value === null || $value === '') {
            return 'N/A';
        } else {
            return date('d/m/Y', strtotime($value));
        }
    }
}
