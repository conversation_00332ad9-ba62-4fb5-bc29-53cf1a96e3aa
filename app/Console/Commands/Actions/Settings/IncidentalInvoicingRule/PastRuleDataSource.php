<?php

namespace App\Console\Commands\Actions\Settings\IncidentalInvoicingRule;

use Illuminate\Console\Command;
use App\Console\Commands\Action;
use Illuminate\Support\Facades\DB;

class PastRuleDataSource extends Action
{
    public $signature = 'datasource:pastRule {flowId} {parentId} {input}';

    public $description = 'Get past rule data source';

    protected $formatter = [
        'id' => '',
        'particular' => 'setNA:particular',
        'rate' => 'getAmountPaid:rate',
        'rate_type' => 'setNA:rate_type',
        'duration' => 'setNA:duration',
        'effective_date' => 'setDate:effective_date',
        'ledger_id' => '',
        'tax_class_id' => '',
        'apply_late_payment_interest' => '',
        'created_date' => 'setDate:created_date',
        'created_by' => 'setNA:created_by_username',
        'updated_date' => 'setDate:updated_date',
        'updated_by' => 'setNA:updated_by_username',
        'ledger_account_name' => 'setNA:ledger_account_name',
        'tax_class_name' => 'setTax:tax_class_name',
        'created_by_username' => '',
        'updated_by_username' => '',
        'ledger_account_name' => 'setNA:ledger_account_name',
    ];

    protected $schema = [
        'table' => [
            'tableTitle' => 'Incidental Invoicing Rules',
            "tabs" => [
                "Active Rules",
                "Upcoming Rules",
                "Past Rules",
                "Late Payment Rule"
            ],
            "actions" => [
                [
                    "title" => "New Rule",
                    "icon" => "ri-add-circle-line",
                    "color" => "primary",
                    "form" => "newRule",
                    "variant" => "contained"
                ],
                [
                    "title" => "New Late Charges Rule",
                    "icon" => "ri-add-circle-line",
                    "color" => "success",
                    "form" => "newLateChargesRule",
                    "variant" => "contained"
                ]
            ],
            'fields' => [
                '*'
            ],
            'columns' => [
                [
                    'title' => 'Particular',
                    'key' => 'particular'
                ],
                [
                    'title' => 'Charges ₹',
                    'key' => 'rate'
                ],
                [
                    'title' => 'Income Account',
                    'key' => 'ledger_account_name'
                ],
                [
                    'title' => 'Effective Date',
                    'key' => 'effective_date'
                ],
                [
                    'title' => 'Tax Class',
                    'key' => 'tax_class_name'
                ],
                [
                    'title' => 'Created Date',
                    'key' => 'created_date'
                ],
                [
                    'title' => 'Created By',
                    'key' => 'created_by'
                ],
                [
                    'title' => 'Updated Date',
                    'key' => 'updated_date'
                ],
                [
                    'title' => 'Updated By',
                    'key' => 'updated_by'
                ]
            ]
        ]
    ];

    public function apply()
    {
        // $financialYear = $this->tenantDB()->table('soc_account_financial_year_master')
        //     ->select('fy_start_date', 'fy_end_date')
        //     ->where('confirmed', 1)
        //     ->first();

        $page = $this->input['page'] ?? 1;
        $per_page = !empty($this->input['per_page']) ? $this->input['per_page']: ($this->hugeData ? 100000 : 10);
        $offset = ($page - 1) * $per_page;
        $activeRulesData = $this->input['active_rule'];

        // Fetch user data from the master connection
        $users = $this->masterDB()->table('chsone_users_master')
        ->select(DB::raw("user_id, CONCAT(user_first_name, ' ', LEFT(user_last_name, 1), '.') as username"))
        ->pluck('username', 'user_id');
        $bind = '';
        foreach ($activeRulesData as $par) :
            $bind .= $par['id'] . ',';
        endforeach;

        $result = $this->tenantDB()->table('income_common_area_charges as icac')
            ->select('icac.id',
            // 'icac.particular',
            $this->tenantDB()->raw('CONCAT(UPPER(LEFT(icac.particular, 1)), SUBSTRING(icac.particular, 2)) as particular'),
            'icac.rate',
            'icac.rate_type',
            'icac.duration',
            'icac.effective_date',
            'icac.ledger_id',
            'icac.tax_class_id',
            'icac.apply_late_payment_interest',
            'icac.created_date',
            'icac.created_by',
            'icac.updated_date',
            'icac.updated_by',
            $this->tenantDB()->raw('CONCAT(UPPER(LEFT(cglt.ledger_account_name, 1)), SUBSTRING(cglt.ledger_account_name, 2)) as ledger_account_name'),
            // 'cglt.ledger_account_name',
            $this->tenantDB()->raw('CONCAT(UPPER(LEFT(ctc.tax_class_name, 1)), SUBSTRING(ctc.tax_class_name, 2)) as tax_class_name'),
            // 'ctc.tax_class_name'
            )
            ->leftJoin('chsone_grp_ledger_tree as cglt', 'icac.ledger_id', '=', 'cglt.ledger_account_id')
            ->leftJoin('chsone_tax_classes as ctc', 'icac.tax_class_id', '=', 'ctc.tax_class_id')
            ->where('icac.effective_date', '<=', now())
            ->whereNotIn('id', explode(',', $bind))
            ->orderByDesc('icac.updated_date')
            ->offset($offset)
            ->limit($per_page)
            ->get();

        // Map the username from the master connection data
        $result->transform(function ($item) use ($users) {
            $item->created_by_username = $users->get($item->created_by);
            $item->updated_by_username = $users->get($item->updated_by);
            return $item;
        });
        //$this->data = $result;

        $totalCount = $this->tenantDB()->table('income_common_area_charges as icac')
        ->select('icac.id', 'icac.particular', 'icac.rate', 'icac.rate_type', 'icac.duration', 'icac.effective_date', 'icac.ledger_id', 'icac.tax_class_id', 'icac.apply_late_payment_interest', 'icac.created_date', 'icac.created_by', 'icac.updated_date', 'icac.updated_by', 'cglt.ledger_account_name', 'ctc.tax_class_name')
        ->leftJoin('chsone_grp_ledger_tree as cglt', 'icac.ledger_id', '=', 'cglt.ledger_account_id')
        ->leftJoin('chsone_tax_classes as ctc', 'icac.tax_class_id', '=', 'ctc.tax_class_id')
        ->where('icac.effective_date', '<=', now())
        ->whereNotIn('id', explode(',', $bind))
        ->count();

        $this->data = $this->format($result);

        $this->meta['schema'] = $this->schema;
        $this->meta['pagination']['total'] = $totalCount;

    }

    public function getAmountPaid($payment_amount)
    {
        $payment_amount = (float) $payment_amount;

        return '₹ ' . number_format($payment_amount, 2);
    }

    public function setNA($value)
    {
        if($value === null || $value === ''){
            return 'N/A';
        }else{
            return $value;
        }
    }

    public function setTax($tax_class_name)
    {
        if($tax_class_name === null || $tax_class_name === ''){
            return 'No tax';
        }else{
            return $tax_class_name;
        }
    }

    public function setDate($value)
    {
        if($value === null || $value === ''){
            return 'N/A';
        }else{
            return date('d/m/Y', strtotime($value));
        }
    }
}
