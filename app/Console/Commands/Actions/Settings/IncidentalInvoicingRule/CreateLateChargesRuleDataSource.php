<?php

namespace App\Console\Commands\Actions\Settings\IncidentalInvoicingRule;

use Illuminate\Console\Command;
use App\Console\Commands\Action;

class CreateLateChargesRuleDataSource  extends Action
{
    protected $signature = 'datasource:createLateChargesRule {flowId} {parentId} {input}';

    protected $description = 'Create Late Payment Charges';

    public function apply()
    {
        $soc_id = $this->input['company_id'];
        $type = $this->input['type'] ?? null;
        $interest_amount_type = $this->input['interestamount_type'] ?? null;
        $interest_amount = $this->input['interest_amount']??null;
        $interestType = $this->input['interestType']??null;
        $grace_period = $this->input['grace_period']??null;
        $effective_date = $this->input['effective_date'] ?? '0000-00-00';
        $calculatefrom = $this->input['calculatefrom']??null;
        $calculatefor = $this->input['calculatefor'] ?? 0;
        $applicable_taxes = $this->input['applicable_taxes'] ?? 0;
        $created_date = date('Y-m-d H:i:s');
        $created_by = $this->input['created_by'] ?? 0;
        $updated_date = date('Y-m-d H:i:s');
        $updated_by = $this->input['updated_by'] ?? 0;

        $result = $this->tenantDB()->table('income_late_payment_charges')
            ->insert([
                'soc_id' => $soc_id,
                'type' => $type,
                'interest_amount_type' => $interest_amount_type,
                'simple_interest' => $interest_amount,
                'interest_type' => $interestType,
                'grace_period' => $grace_period, // 'add_to_maintenance' => '0',
                'grace_duration' => $grace_period,
                'effective_date' => $effective_date,
                'calculate_from' => $calculatefrom,
                'calculate_for' => $calculatefor,
                'applicable_taxes' => $applicable_taxes,
                'created_date' => $created_date,
                'created_by' => $created_by,
                'updated_date' => $updated_date,
                'updated_by' => $updated_by
            ]);

        if ($result) {
            $this->status = 'success';
            $this->message = 'New Late Charges Rule created successfully';
            $this->statusCode = 200;
            $this->data = [];
        } else {
            $this->status = 'error';
            $this->message = 'New Late Charges Rule could not be created';
            $this->statusCode = 400;
            $this->data = [];
        }

    }
    
}