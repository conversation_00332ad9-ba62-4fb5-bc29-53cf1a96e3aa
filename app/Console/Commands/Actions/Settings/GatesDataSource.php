<?php

namespace App\Console\Commands\Actions\Settings;

use Illuminate\Console\Command;
use App\Console\Commands\Action;

class GatesDataSource extends Action
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'datasource:gates {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Get Gates List';

    protected $formatter = [
        'id' => '',
        'company_id' => '',
        'gate_name' => '',
        'gate_type' => '',
        'user_id' => '',
        'status' => '',
        'tag' => '',
        'created_at' => '',
        'updated_at' => '',
    ];

    protected $formatterByKeys = ['gate_id as id'];

    /**
     * Execute the console command.
     */
    public function apply()
    {
        $obj = $this->tenantDB()->table('gates')
        ->select('gate_id as id', 'company_id', 'gate_name', 'gate_type', 'user_id', 'status', 'tag', 'created_at', 'updated_at');

        $obj = $obj->orderBy('gate_id', 'asc');

        $result = $obj->get();
        $this->data = $this->format($result->toArray());
    }
}
