<?php

namespace App\Console\Commands\Actions\Settings;

use Illuminate\Console\Command;
use App\Console\Commands\Action;

class GateSettingsDataSource extends Action
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'datasource:gateSettings {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Get Gate Settings List';

    protected $formatter = [
        'id' => '',
        'settings_name' => '',
        'options' => '',
        'type' => '',
        'gate_id' => '',
        'status' => '',
        'created_at' => '',
        'updated_at' => '',
    ];

    protected $formatterByKeys = ['id'];

    /**
     * Execute the console command.
     */
    public function apply()
    {
        $obj = $this->tenantDB()->table('gate_settings')
        ->select('id', 'company_id', 'settings_name', 'options', 'type', 'gate_id', 'status', 'created_at', 'updated_at');

        $obj = $obj->orderBy('id', 'asc');

        $result = $obj->get();
        $this->data = $this->format($result->toArray());
    }
}
