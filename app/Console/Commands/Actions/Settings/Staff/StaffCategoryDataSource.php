<?php

namespace App\Console\Commands\Actions\Settings\Staff;

use Illuminate\Console\Command;
use App\Console\Commands\Action;

class StaffCategoryDataSource extends Action
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'datasource:staffcategorylist {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Get Staff Category List';

    protected $formatter = [
        'id' => '',
        'soc_id' => '',
        'category' => '',
        'status' => ''
    ];

    protected $formatterByKeys = ['staff_type_id'];

    protected $mapper = [
        'category' => 'staff_type_name',
        'id' => 'staff_type_id'
    ];


    /**
     * Execute the console command.
     */
    public function apply()
    {
        // add pagination
        $page = $this->input['page'] ?? 1;
        $per_page = !empty($this->input['per_page']) ? $this->input['per_page']: ($this->hugeData ? 100000 : 10);

        $offset = ($page - 1) * $per_page;

        // get the count of total staff category
        $totalCount = $this->TenantDB()->table('chsone_staff_type_master as staffcategory')
        ->select('staffcategory.staff_type_id as id', 'staffcategory.soc_id', 'staffcategory.staff_type_name as category', 'staffcategory.status')
        ->count();
        
        $obj = $this->TenantDB()->table('chsone_staff_type_master as staffcategory')
            ->select('staffcategory.staff_type_id as id', 'staffcategory.soc_id', 'staffcategory.staff_type_name as category', 'staffcategory.status')
            ->orderBy('staffcategory.staff_type_id', 'desc')
            ->offset($offset)
            ->limit($per_page)
            ->get();

        $this->data = $obj;
        $this->meta['pagination']['total'] = $totalCount;
    }

}
