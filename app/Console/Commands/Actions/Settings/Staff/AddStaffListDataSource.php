<?php

namespace App\Console\Commands\Actions\Settings\Staff;

use Illuminate\Console\Command;
use App\Console\Commands\Action;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Log;
use Endroid\QrCode\QrCode;
use Endroid\QrCode\Writer\PngWriter;
use Aws\S3\S3Client;

class AddStaffListDataSource extends Action
{
    protected $signature   = 'datasource:AddStaffListDataSource {flowId} {parentId} {input}';
    protected $description = 'Add or update a staff member';

    public function apply()
    {
        try {
            $request       = $this->input;
            $staffId       = $request['id']            ?? null;
            $contactNumber = $request['staff_contact_number'];
            $memberId      = (string)($request['member_id'] ?? '0');
            $from          = $request['from_time']     ?? null;
            $to            = $request['to_time']       ?? null;
            $newSlot       = trim("{$from} - {$to}");
            $addedOn       = date('Y-m-d H:i:s');

            // 1) Find existing record by ID if given, otherwise by contact number
            if ($staffId) {
                $existing = $this->tenantDB()
                    ->table('chsone_staff_master')
                    ->where('staff_id', $staffId)
                    ->first();
            } else {
                $existing = $this->tenantDB()
                    ->table('chsone_staff_master')
                    ->where('staff_contact_number', $contactNumber)
                    ->first();
            }

            if (!$existing) {
                // --- INSERT PATH ---
                $qrUrl = $this->generateStaffQrToS3([
                    'mobile' => $contactNumber,
                    'badge'         => $request['staff_badge_number'] ?? null,
                    'is_staff' => true,
                ]);

                $insert = [
                    'user_id'                     => $request['user_id']             ?? 0,
                    'staff_type_id'               => $request['staff_type_id'],
                    'staff_first_name'            => $request['staff_first_name'],
                    'staff_badge_number'          => $request['staff_badge_number'],
                    'staff_contact_number'        => $contactNumber,
                    'staff_email_id'              => $request['staff_email_id']      ?? null,
                    'staff_address_1'             => $request['staff_address_1'],
                    'staff_gender'                => $request['staff_gender']        ?? null,
                    'staff_dob'                   => $request['staff_dob'],
                    'staff_proof'                 => $request['staff_proof'],
                    'staff_image'                 => $request['staff_image']         ?? '',
                    'staff_qualification'         => $request['staff_qualification'] ?? null,
                    'staff_skill'                 => $request['staff_skill']         ?? null,
                    'staff_lang_iso_639_3'        => $request['staff_lang_iso_639_3']?? null,
                    'staff_rfid'                  => $request['staff_rfid']          ?? null,
                    'staff_note'                  => $request['staff_note']          ?? null,
                    'added_on'                    => $addedOn,
                    'status'                      => $request['status']              ?? 1,
                    'soc_id'                      => $request['company_id']          ?? null,
                    'chsone_users_master_user_id' => $memberId,
                    'qr_code'                     => $qrUrl,
                    // key slots by member_id
                    'time_slot'                   => json_encode([$memberId => $newSlot]),
                    'is_off_duty'                 => $request['is_off_duty']         ?? 0,
                ];

                $res = $this->tenantDB()
                    ->table('chsone_staff_master')
                    ->insert($insert);

                if ($res) {
                    $this->message    = "Staff created successfully.";
                    $this->status     = "success";
                    $this->statusCode = 200;
                } else {
                    $this->message    = "Failed to insert staff.";
                    $this->status     = "error";
                    $this->statusCode = 400;
                }

            } else {
                // --- UPDATE PATH ---
                // decode existing slots (member_id => "HH:MM - HH:MM")
                $slots = json_decode($existing->time_slot ?? '{}', true) ?: [];

                // check overlap against all other slots
                if (!$this->isTimeSlotValid(array_values($slots), $newSlot)) {
                    $this->message    = "Time slot conflicts with an existing one. Please select other time for allocation of staff";
                    $this->status     = "error";
                    $this->statusCode = 400;
                    return;
                }

                // overwrite (or add) this member's slot
                $slots[$memberId] = $newSlot;

                // force object encoding:
                $tsJson = json_encode($slots, JSON_FORCE_OBJECT);

                // you can also update other fields if needed:
                $update = [
                    'staff_type_id'               => $request['staff_type_id'],
                    'staff_first_name'            => $request['staff_first_name'],
                    'staff_badge_number'          => $request['staff_badge_number'],
                    'staff_contact_number'        => $contactNumber,
                    'staff_email_id'              => $request['staff_email_id']      ?? null,
                    'staff_address_1'             => $request['staff_address_1'],
                    'staff_gender'                => $request['staff_gender']        ?? null,
                    'staff_dob'                   => $request['staff_dob'],
                    'staff_proof'                 => $request['staff_proof'],
                    'staff_image'                 => $request['staff_image']         ?? '',
                    'staff_qualification'         => $request['staff_qualification'] ?? null,
                    'staff_skill'                 => $request['staff_skill']         ?? null,
                    'staff_lang_iso_639_3'        => $request['staff_lang_iso_639_3']?? null,
                    'staff_rfid'                  => $request['staff_rfid']          ?? null,
                    'staff_note'                  => $request['staff_note']          ?? null,
                    'status'                      => $request['status']              ?? $existing->status,
                    'is_off_duty'                 => $request['is_off_duty']         ?? $existing->is_off_duty,
                    // just overwrite the JSON object
                    'time_slot'                   => $tsJson,
                ];

                $updated = $this->tenantDB()
                    ->table('chsone_staff_master')
                    ->where('staff_id', $staffId)
                    ->update($update);


                if ($updated > 0) {
                    $this->message    = "Staff details updated successfully.";
                    $this->status     = "success";
                    $this->statusCode = 200;
                } else {
                    $this->message    = "No changes applied to staff";
                    $this->status     = "success";
                    $this->statusCode = 200;
                }
            }

        } catch (\Exception $e) {
            $this->message    = "Error: " . $e->getMessage();
            $this->status     = "error";
            $this->statusCode = 500;
        }
    }

    // public function generateStaffQrToS3(array $staffData): ?string
    // {
    //     try {
    //         $qrContent = json_encode(array_merge($staffData, ['is_staff'=>true]));
    //         $qrCode    = new QrCode($qrContent);
    //         $writer    = new PngWriter();
    //         $image     = $writer->write($qrCode);

    //         $fname     = 'qrcodes/staff_'.($staffData['badge']??'x').'_'.time().'.png';
    //         Storage::disk('s3')->put($fname, $image->getString());
    //         return Storage::disk('s3')->url($fname);

    //         } catch (\Exception $e) {
    //             dd('try block 1', $e->getMessage());
    //             Log::error("QR S3 upload failed: ".$e->getMessage());
    //             return null;
    //     }
    // }

    public function generateStaffQrToS3(array $staffData): ?string
    {
        try {
            $qrContent = json_encode(array_merge($staffData, ['is_staff' => true]));
            $qrCode    = new QrCode($qrContent);
            $writer    = new PngWriter();
            $image     = $writer->write($qrCode);

            $fileName  = 'qrcodes/staff_' . ($staffData['badge'] ?? 'x') . '_' . time() . '.png';
            $bucket    = env('AWS_BUCKET'); // or set your bucket name here

            // Instantiate the AWS SDK S3 client
            $s3 = new S3Client([
                'version'     => 'latest',
                'region'      => env('AWS_DEFAULT_REGION'),
                'credentials' => [
                    'key'    => env('AWS_ACCESS_KEY_ID'),
                    'secret' => env('AWS_SECRET_ACCESS_KEY'),
                ],
            ]);

           
            // Upload to S3 directly
            $result = $s3->putObject([
                'Bucket'      => $bucket,
                'Key'         => $fileName,
                'Body'        => $image->getString(),
                'ContentType' => 'image/png',
                'ACL'         => 'public-read', // optional, only if you want public access
            ]);

            return $result['ObjectURL'] ?? null;

        } catch (\Exception $e) {
            \Log::error("QR S3 upload failed: " . $e->getMessage());
            return null;
        }
    }

    public function isTimeSlotValid(array $existingSlots, string $newSlot): bool
    {
        if ($newSlot === '-') {
            return true; // empty slot is always valid
        }
        list($nStart, $nEnd) = explode(' - ', $newSlot);
        foreach ($existingSlots as $slot) {
            list($sStart, $sEnd) = explode(' - ', $slot);
            if (! (strtotime($sEnd) <= strtotime($nStart) || strtotime($nEnd) <= strtotime($sStart))) {
                return false;
            }
        }
        return true;
    }
}
