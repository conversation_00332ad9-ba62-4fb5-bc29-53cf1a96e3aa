<?php

namespace App\Console\Commands\Actions\Settings\Staff;

use Illuminate\Support\Facades\DB;
use App\Console\Commands\Action;
use Illuminate\Support\Facades\Request;

class StaffDetailsDataSource extends Action
{
    protected $signature = 'datasource:staffDetails {flowId} {parentId} {input}';

    protected $description = 'Get Staff Details';

    protected $rules = [
        'staff_id' => 'required|integer'
    ];

    protected $rulesMessage = [
        'staff_id.required' => 'Staff ID is required',
        'staff_id.integer' => 'Staff ID must be an integer'
    ];

    protected $formatter = [
        'id' => '',
        'staff_first_name' => '',
        'staff_last_name' => '',
        'category' => '',
        'staff_badge_number' => '',
        'staff_contact_number' => '',
        'staff_dob' => '',
        'staff_qualification' => '',
        'staff_skill' => '',
        'language_spoken' => '',
        'status' => '',
        'staff_email_id' => '',
        'staff_address_1' => '',
        'staff_image' => '',
        'staff_proof' => '',
        'gender' => '',
        'skill' => '',
        'biomatricid' => '',
        'note' => '',
        'staff_mobile_number' => '',
        'time_slot' => '',
        'qr_code' => '',
        'is_verified' => '',
    ];

    protected $formatterByKeys = ['staff.staff_id'];

    protected $mapper = [
        'id' => 'staff_id',
        'staff_lang_iso_639_3' => 'staff_lang_iso_639_3',
        'staff_type_id' => 'staff_type_id',
    ];

    public function apply()
    {
        $staff_id = $this->input['id'] ?? null;
        $s3Url = env('AWS_S3_URL', '') . '/';

        // Determine the filter field and value
        $filterField = 'staff.staff_id';
        $filterValue = isset($this->input['id']) ? $this->input['id'] : null;

        if (Request::is('api/admin/staffs/staffDetailsByMobile') && $staff_id == null) {
            $filterField = 'staff.staff_contact_number';
            $filterValue = $this->input['mobile'];
        }

        $obj = $this->TenantDB()->table('chsone_staff_master as staff')
            ->select([
                'staff.staff_id as id',
                'staff.staff_image',
                'staff.staff_proof',
                DB::raw('COALESCE(staffcategory.staff_type_name, "") AS category'),
                DB::raw('COALESCE(staff.staff_badge_number, "") AS staff_badge_number'),
                DB::raw('COALESCE(staff.staff_first_name, "") AS name'),
                DB::raw('COALESCE(staff.staff_first_name, "") AS staff_first_name'),
                DB::raw('COALESCE(staff.staff_last_name, "") AS staff_last_name'),
                DB::raw('COALESCE(staff.staff_email_id, "") AS staff_email_id'),
                // DB::raw('CONCAT("' . $s3Url . '", COALESCE(staff.staff_proof, "")) AS staff_proof'),
                'staff.staff_image',
                'staff.qr_code',
                'staff.is_verified',
                DB::raw('CAST(COALESCE(staff.staff_contact_number, "") AS UNSIGNED) AS staff_contact_number'),
                DB::raw('COALESCE(staff.staff_dob, "") AS staff_dob'),
                DB::raw('COALESCE(staff.staff_qualification, "") AS staff_qualification'),
                DB::raw('COALESCE(staff.staff_skill, "") AS staff_skill'),
                DB::raw('COALESCE(staff.staff_lang_iso_639_3, "") AS language_spoken'),
                DB::raw('CAST(COALESCE(staff.status, 0) AS UNSIGNED) AS status'),
                DB::raw('COALESCE(staff.staff_address_1, "") AS staff_address_1'),
                DB::raw('UPPER(LEFT(COALESCE(NULLIF(staff.staff_gender, ""), "M"), 1)) AS staff_gender'),
                DB::raw('COALESCE(staff.staff_skill, "") AS skill'),
                DB::raw('COALESCE(staff.staff_rfid, "") AS biomatricid'),
                DB::raw('COALESCE(staff.staff_note, "") AS note'),
                'staff.staff_contact_number as staff_mobile_number',
                'staff.time_slot',
                'staff.qr_code',
                'staff.is_verified',
                DB::raw('CAST(COALESCE(staff.staff_type_id, 0) AS UNSIGNED) AS staff_type_id')
            ])
            ->leftJoin('chsone_staff_type_master as staffcategory', 'staffcategory.staff_type_id', '=', 'staff.staff_type_id')
            ->where($filterField, $filterValue)
            ->first();

        if (!$obj) {
            $this->statusCode = 400;
            $this->status = 'error';
            $this->message = 'Staff not found';
            return;
        }

        $this->data = $obj;
    }

    public function concat($staff_first_name, $staff_last_name)
    {
        return $staff_first_name . ' ' . $staff_last_name;
    }
}
