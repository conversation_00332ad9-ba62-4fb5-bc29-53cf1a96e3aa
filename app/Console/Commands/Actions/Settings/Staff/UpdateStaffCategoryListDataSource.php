<?php

namespace App\Console\Commands\Actions\Settings\Staff;

use App\Console\Commands\Action;
use Illuminate\Console\Command;
use Carbon\Carbon;

class UpdateStaffCategoryListDataSource extends Action
{
    protected $signature = 'datasource:updateStaffCategoryList {flowId} {parentId} {input}';

    protected $description = 'Update Staff Category List';

    public function apply()
    {
        $category = $this->input['category'];

        $id = $this->input['id'];

        $checkIfCategoryExists = $this->checkIfStaffCategoryExists($category);
            if ($checkIfCategoryExists) {
                $this->message = "Category $category already exists.";
                $this->status = 'error';
                $this->statusCode = 400;
                return false;
            }

          $update_staff=  $this->tenantDB()->table('chsone_staff_type_master')->where('staff_type_id',$id)->update([
                'staff_type_name' => $category,
                'updated_on' => date('Y-m-d H:i:s'),
            ]);

            if($update_staff===1){
                $this->status = 'success';
                $this->message = 'Staff Category List updated successfully';
                $this->statusCode = 200;
            }else{
                $this->status = 'error';
                $this->message = 'Staff Category List not updated';
                $this->statusCode = 500;
            }

    }

    public function checkIfStaffCategoryExists($categoryName)
    {
        $result = $this->tenantDB()->table('chsone_staff_type_master')
            ->where('staff_type_name', $categoryName)->exists();

        if ($result) {
            // The ID exists in the table
            return "Category $categoryName exists in the table.";
        } else {
            // The ID does not exist in the table
            return false;
        }
    }
}
