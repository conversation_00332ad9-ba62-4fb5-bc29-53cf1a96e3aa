<?php

namespace App\Console\Commands\Actions\Settings\Staff;

use App\Models\Tenants\ChsoneStaffMaster;
use Illuminate\Console\Command;
use App\Console\Commands\Action;
use Illuminate\Support\Facades\DB;

class StaffDataSource extends Action
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'datasource:stafflist {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Get Staff List';

    protected $formatter = [
        'id' => '',
        'name' => 'concat:staff_first_name,staff_last_name',
        'category' => '',
        'staff_badge_number' => '',
        'staff_contact_number' => '',
        'staff_dob' => '',
        'staff_email_id' => '',
        'staff_address_1' => '',
        'staff_image' => '',
        'staff_gender'=> '',
        'staff_proof' => '',
        'staff_qualification' => "getNa:staff_qualification",
        'staff_skill' => "getNa:staff_skill",
        'language_spoken' => "getNa:language_spoken",
        'status' => '',
        'members_data' => [],
        'time_slot' => '',
        'qr_code' => '',
        'is_verified' => '',
        'is_off_duty' => '',
    ];


    protected $formatterByKeys = ['user.user_id'];

    protected $mapper = [
        'id' => 'staff_id',
        'language_spoken' => 'staff_lang_iso_639_3',
        'category' => 'staff_type_name',
    ];

    // protected $hugeData = true;

    /**
     * Execute the console command.
     */
    public function apply()
    {
        try{
        $memberId = $this->input['member_id'] ?? null;
        $page = $this->input['page'] ?? 1;
        $per_page = !empty($this->input['per_page']) ? $this->input['per_page'] : ($this->hugeData ? 100000 : 10);
        $offset = ($page - 1) * $per_page;

        // Count total staff rows
        $totalCount = $this->tenantDB()->table('chsone_staff_master as staff')
            ->whereIn('staff.status', ['1', '0'])
            ->count();

        // Query to get the paginated staff data along with member info.
        $data = $this->tenantDB()->table('chsone_staff_master as staff')
            ->select(
                'staff.staff_id as id',
                'staff.staff_badge_number',
                'staff.staff_gender',
                'staff.staff_first_name',
                'staff.staff_last_name',
                'staff.staff_proof',
                'staff.staff_image',
                'staff.staff_email_id',
                'staff.staff_address_1',
                'staff.staff_contact_number',
                'staff.staff_dob',
                'staff.staff_qualification',
                'staff.staff_skill',
                'staff.staff_lang_iso_639_3 as language_spoken',
                'staff.status',
                'staff.chsone_users_master_user_id',
                'staff.time_slot',
                'staff.qr_code',
                'staff.is_verified',
                'staff.staff_type_id',
                'staff.is_off_duty',
            )
            // Include the staff type name from the staff type master
            ->selectRaw('(SELECT IFNULL(staffcategory.staff_type_name, "")
                      FROM chsone_staff_type_master as staffcategory
                      WHERE staffcategory.staff_type_id = staff.staff_type_id) AS category')
            // Fetch member info by joining chsone_member_master using FIND_IN_SET on the comma-separated field.
            // This example concatenates member first and last names.
            ->selectRaw('(
            SELECT JSON_ARRAYAGG(
                JSON_OBJECT(
                    "id", member.id,
                    "vizlog_member_id", member.vizlog_member_id,
                    "member_type_id", member.member_type_id,
                    "soc_id", member.soc_id,
                    "user_id", member.user_id,
                    "fk_unit_id", member.fk_unit_id,
                    "fk_unit_id", member.fk_unit_id,
                    "salute", member.salute,
                    "member_first_name", member.member_first_name,
                    "member_last_name", member.member_last_name,
                    "member_email_id", member.member_email_id,
                    "member_email_id_sec", member.member_email_id_sec,
                    "member_contact_number", member.member_contact_number,
                    "member_mobile_number", member.member_mobile_number,
                    "member_intercom", member.member_intercom,
                    "member_gender", member.member_gender,
                    "member_dob", member.member_dob,
                    "member_occupation", member.member_occupation,
                    "gstin", member.gstin,
                    "effective_date", member.effective_date,
                    "cancel_date", member.cancel_date,
                    "is_tenant", member.is_tenant,
                    "approved", member.approved,
                    "user_account_id", member.user_account_id,
                    "is_user_created", member.is_user_created,
                    "status", member.status,
                    "created_date", member.created_date,
                    "created_by", member.created_by,
                    "updated_date", member.updated_date,
                    "updated_by", member.updated_by,
                    "make_public", member.make_public,
                    "member_status", member.member_status,
                    "unique_code", member.unique_code,
                    "unit_number", (
                        SELECT CONCAT(u.soc_building_name, "-", u.unit_flat_number)
                        FROM chsone_units_master as u
                        WHERE u.unit_id = member.fk_unit_id
                    )
                )
            )
            FROM chsone_members_master AS member
            WHERE FIND_IN_SET(member.id, staff.chsone_users_master_user_id)
        ) AS members_data')
            ->leftJoin('chsone_members_master as member', DB::raw("FIND_IN_SET(member.id, staff.chsone_users_master_user_id)"), '>', DB::raw('0'))
            ->leftJoin('chsone_units_master as unit', 'unit.unit_id', '=', 'member.fk_unit_id')
            ->whereIn('staff.status', ['1', '0']);
     


            if ($memberId) {
                $data->whereRaw("FIND_IN_SET(?, staff.chsone_users_master_user_id)", [$memberId]);
            }       

            $data = $data->orderBy('staff.staff_id', 'desc')
            ->offset($offset)
            ->limit($per_page)
            ->get();
        // Convert the members_data JSON string to an actual PHP array or object.
        $data = $data->map(function($row) {
            $decodedMembers = json_decode($row->members_data); // returns an object by default; use second parameter true for an array
            $row->members_data = is_array($decodedMembers) ? $decodedMembers : [];
            return $row;
        });

        $this->data = $this->format($data->toArray());
        $this->meta['pagination']['total'] = $totalCount;
    }
        catch(\Exception $e){
            dd($e);
        }
    }

    public function concat($staff_first_name, $staff_last_name)
    {
        return $staff_first_name . ' ' . $staff_last_name;
    }

    public function getNa($value){

        return $value ? $value : 'N/A';

    }
}
