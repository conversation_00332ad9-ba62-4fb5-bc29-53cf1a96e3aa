<?php

namespace App\Console\Commands\Actions\Settings\Staff;

use App\Console\Commands\Action;
use Illuminate\Console\Command;

class UpdateCategoryStatusDataSource extends Action
{
    protected $signature = 'datasource:updateCategoryStatus {flowId} {parentId} {input}';

    protected $description = 'Update Category Status';

    public function apply()
    {
        $id = $this->input['id'];
        $status = $this->input['status'];

        $obj = $this->tenantDB()->table('chsone_staff_type_master')->where('staff_type_id', $id)->update([
            'status' => $status
        ]);

        if($obj) {
            $this->status = 'success';
            $this->message = 'Category status updated successfully';
            $this->statusCode = 200;
        } else {
            $this->status = 'error';
            $this->message = 'Category status not updated';
            $this->statusCode = 400;
        }
    }
}