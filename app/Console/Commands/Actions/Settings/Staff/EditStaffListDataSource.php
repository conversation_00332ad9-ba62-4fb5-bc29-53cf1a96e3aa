<?php

namespace App\Console\Commands\Actions\Settings\Staff;

use Illuminate\Console\Command;
use App\Console\Commands\Action;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\Rule;

class EditStaffListDataSource extends Action
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'datasource:EditStaffListDataSource {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Edit Staff List';

    /**
     * Execute the console command.
     */
    public function apply()
    {
        $request = $this->input;

        // Retrieve input data with defaults
        $staffId = (int)($request['id'] ?? 0);

        $data = [
            'user_id' => $request['user_id'] ?? 0,
            'staff_type_id' => $request['staff_type_id'] ?? null,
            'staff_first_name' => $request['staff_first_name'] ?? null,
            'staff_badge_number' => $request['staff_badge_number'] ?? null,
            'staff_contact_number' => $request['staff_contact_number'] ?? null,
            'staff_email_id' => $request['staff_email_id'] ?? null,
            'staff_address_1' => $request['staff_address_1'] ?? null,
            'staff_gender' => $request['staff_gender'] ?? null,
            'staff_dob' => $request['staff_dob'] ?? null,
            'staff_proof' => $request['staff_proof'] ?? null,
            'staff_image' => $request['staff_image'] ?? '',
            'staff_qualification' => $request['staff_qualification'] ?? null,
            'staff_skill' => $request['staff_skill'] ?? null,
            'staff_lang_iso_639_3' => $request['staff_lang_iso_639_3'] ?? null,
            'staff_rfid' => $request['staff_rfid'] ?? null,
            'staff_note' => $request['staff_note'] ?? null,
            'added_on' => date('Y-m-d H:i:s'),
            'modified_on' => date('Y-m-d H:i:s'),
            'soc_id' => $request['company_id'] ?? null,
            'is_off_duty' => $request['is_off_duty'] ?? 0,
        ];

        // Check if the staff ID exists
        $existingStaff = $this->tenantDB()->table('chsone_staff_master')->where('staff_id', (int) $staffId)->first();

        if (!$existingStaff) {
            $this->status = 'error';
            $this->message = 'Staff ID not found';
            $this->statusCode = 404;
            return;
        }

        //check staff type exists
        $staffType = $this->tenantDB()->table('chsone_staff_type_master')->where('staff_type_id', $data['staff_type_id'])->first();

        if(!$staffType){
            $this->status = 'error';
            $this->message = 'Staff Type not found';
            $this->statusCode = 404;
            return;
        }

        // check staff mobile number exists
        $existsMobile = $this->tenantDB()
            ->table('chsone_staff_master')
            ->where('staff_contact_number', $data['staff_contact_number'])
            ->where('staff_id', '<>', $staffId)
            ->exists();

        if ($existsMobile) {
            $this->status = 'error';
            $this->message = 'Mobile number already exists';
            $this->statusCode = 400;
            return;
        }

// check staff email exists for a *different* staff
        $existsEmail = $this->tenantDB()
            ->table('chsone_staff_master')
            ->where('staff_email_id', $data['staff_email_id'])
            ->where('staff_id', '<>', $staffId)
            ->exists();

        if ($existsEmail) {
            $this->status = 'error';
            $this->message = 'Email already exists';
            $this->statusCode = 400;
            return;
        }

        // Update staff data
        $isUpdated = $this->tenantDB()->table('chsone_staff_master')->where('staff_id', $staffId)->update($data);

        if ($isUpdated) {
            $this->status = 'success';
            $this->message = 'Staff data updated successfully';
            $this->statusCode = 200;
            $this->data = $data;
        } else {
            $this->status = 'error';
            $this->message = 'Unable to update staff data';
            $this->statusCode = 400;
            $this->data = $data;
        }
    }

}
