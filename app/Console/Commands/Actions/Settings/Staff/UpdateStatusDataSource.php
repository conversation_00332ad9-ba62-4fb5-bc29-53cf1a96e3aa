<?php

namespace App\Console\Commands\Actions\Settings\Staff;

use Illuminate\Console\Command;
use App\Console\Commands\Action;

class UpdateStatusDataSource extends Action
{
    protected $signature = 'datasource:updateStatus {flowId} {parentId} {input}';

    protected $description = 'Update Sattus Data Source';

    public function apply()
    {
        $staff_ids = explode(',',$this->input['id']);
        $status = $this->input['status'];
        $obj = $this->tenantDB()->table('chsone_staff_master')->whereIn('staff_id', $staff_ids)->update(['status' => $status]);

        if($obj) {
            $this->status = 'success';
            $this->message = 'Staff status updated successfully';
            $this->statusCode = 200;
        } else {
            $this->status = 'error';
            $this->message = 'Unable to update staff status';
            $this->statusCode = 400;
        }
    }
}