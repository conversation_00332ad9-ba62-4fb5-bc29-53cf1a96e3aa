<?php

namespace App\Console\Commands\Actions\Settings\Staff;

use App\Console\Commands\Action;

class StaffDataDownloadSource extends Action
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'datasource:downloadStaff {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Get Staff List';

    protected $formatter = [
        'id' => '',
        'name' => 'concat:staff_first_name,staff_last_name',
        'category' => '',
        'staff_badge_number' => '',
        'staff_contact_number' => '',
        'staff_dob' => '',
        'staff_qualification' => "getNa:staff_qualification",
        'staff_skill' => "getNa:staff_skill",
        'language_spoken' => "getNa:language_spoken",
        'status' => ''
    ];

    protected $formatterByKeys = ['user.user_id'];

    protected $mapper = [
        'id' => 'staff_id',
        'language_spoken' => 'staff_lang_iso_639_3',
        'category' => 'staff_type_name',
    ];

    // protected $hugeData = true;

    /**
     * Execute the console command.
     */
    public function apply()
    {


        // Query to get the paginated data
        $data = $this->TenantDB()->table('chsone_staff_master as staff')
            ->select('staff.staff_id as id', 'staff.staff_badge_number', 'staff.staff_first_name', 'staff.staff_last_name', 'staff.staff_email_id', 'staff.staff_contact_number', 'staff.staff_dob', 'staff.staff_qualification', 'staff.staff_skill', 'staff.staff_lang_iso_639_3 as language_spoken', 'staff.status')
            ->whereIn('staff.status', ['1', '0'])
            ->selectRaw('(SELECT IFNULL(staffcategory.staff_type_name, "") FROM chsone_staff_type_master as staffcategory WHERE staffcategory.staff_type_id = staff.staff_type_id) AS category')
            ->orderBy('staff.staff_id', 'desc')
            ->get();


        $this->data = $this->format($data->toArray());
    }

    public function concat($staff_first_name, $staff_last_name)
    {
        return $staff_first_name . ' ' . $staff_last_name;
    }

    public function getNa($value){

        return $value ? $value : 'N/A';

    }
}
