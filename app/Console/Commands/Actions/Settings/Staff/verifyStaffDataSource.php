<?php

namespace App\Console\Commands\Actions\Settings\Staff;

use Illuminate\Console\Command;
use App\Console\Commands\Action;

class verifyStaffDataSource extends Action
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'datasource:verifyStaff {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Get Staff Category List for add/edit staff form';

    protected $formatter = [
    ];

    protected $formatterByKeys = ['staff_type_id'];

    protected $mapper = [
    ];

    /**
     * Execute the console command.
     */
    public function apply()
    {
        try     {   
            $staff_id = $this->input['staff_id'];
            
            $obj = $this->TenantDB()->table('chsone_staff_master as staff')
                ->where('staff.staff_id', $staff_id)
                ->where('staff.is_verified', 0)
                ->where('staff.staff_otp', $this->input['passcode'])
    
                ->update([
                    'staff.staff_otp' => null,
                    'staff.is_verified' => 1,
                ]);

    
            if ($obj == 1) {
                $this->status = 'success';
                $this->message = 'Staff verified successfully';
                $this->data = [
                    'staff_id' => $staff_id,
                ];
                $this->status_code = 200;
            } else {
                $this->status = 'error';
                $this->message = 'Staff not found or already verified';
                $this->status_code = 400;
                $this->data = [
                    'staff_id' => $staff_id,
                ];
            }
        } catch (\Exception $e) {
            $this->status = 'error';
            $this->message = 'Failed to verify staff';
            $this->status_code = 500;
            $this->data = [
                'error' => $e->getMessage(),
            ];
        }
    }
}
