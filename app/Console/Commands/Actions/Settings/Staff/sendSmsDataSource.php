<?php
namespace App\Console\Commands\Actions\Settings\Staff;

use App\Console\Commands\Action;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class sendSmsDataSource extends Action
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'datasource:sendSms {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Send SMS to staff for verification';

    protected $formatter = [
    ];

    protected $mapper = [
    ];

    /**
     * Execute the console command.
     */
    public function apply()
    {
        try {
            $request  = $this->input;
            $staff_id = $request['staff_id'];

            // generate a random 6 digit OTP
            $otp = rand(100000, 999999);
            // send the OTP to the staff's mobile number
            $mobile_number = $this->input['mobile_number'];
            $message       = "Your staff verification OTP is $otp. Do not share this code with anyone. - oneapp";

            $is_sent = $this->sendMessage($message, $mobile_number);
            // update the staff record with the OTP
            if (! $is_sent) {
                $this->status      = 'error';
                $this->message     = 'Failed to send OTP';
                $this->status_code = 500;
                return;
            }
            // if ($is_sent === true) {

                $obj = $this->TenantDB()->table('chsone_staff_master as staff')
                    ->where('staff.staff_id', $staff_id)
                    ->update([
                        'staff.staff_otp'   => $otp,
                        'staff.is_verified' => 0,
                    ]);
                if (! $obj) {
                    $this->status      = 'error';
                    $this->message     = 'Failed to update staff record';
                    $this->status_code = 500;
                    return;
                }
                $this->status  = 'success';
                $this->message = 'OTP sent successfully';
                $this->data    = [
                    'staff_id' => $staff_id,
                ];
                $this->status_code = 200;
            // } else {
            //     $this->status      = 'error';
            //     $this->message     = 'Failed to send OTP';
            //     $this->status_code = 500;
            // }
        } catch (\Exception $e) {
            // Optionally log the exception for debugging purposes
            $this->status      = 'error';
            $this->message     = 'Failed to send SMS: ' . $e->getMessage();
            $this->status_code = 500;
            Log::error('Failed to send SMS: ' . $e->getMessage());
        }
    }

    public function sendMessage($message, $mobile_number)
    {
        try {
            // Check if required fields are present
            if (empty($message) || empty($mobile_number)) {
                return false;
            }

            // Retrieve SMS API configuration from Laravel's config system
            $email       = env('SMS_EMAIL');
            $password    = env('SMS_EMAIL_PASSWORD');
            $SenderId    = env('SMS_SENDER_ID');
            $ServiceName = env('SMS_SERVICE_NAME');

            // Build the full message with OTP if necessary
            $finalMessage = $message;

            // Build the full URL with query parameters
            $url = env('SMS_URL') . '?' . http_build_query([
                'EmailID'     => $email,
                'Password'    => $password,
                'SenderID'    => $SenderId,
                'MobileNo'    => $mobile_number,
                'Message'     => $finalMessage,
                'ServiceName' => $ServiceName,
            ]);

            try {
                $response = Http::timeout(30)->get($url);

                if ($response->successful()) {
                    return $response->body();
                }

                return false;

            } catch (\Exception $e) {
                return false;
            }
        } catch (\Exception $e) {
            // Optionally log the exception for debugging purposes
            $this->status      = 'error';
            $this->message     = 'Failed to send SMS: ' . $e->getMessage();
            $this->status_code = 500;
            Log::error('Failed to send SMS: ' . $e->getMessage());
            return false;
        }
    }
}
