<?php

namespace App\Console\Commands\Actions\Settings\Staff;

use Illuminate\Console\Command;
use App\Console\Commands\Action;

class getStaffCategoryListDataSource extends Action
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'datasource:getStaffCategoryList {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Get Staff Category List for add/edit staff form';

    protected $formatter = [
        'id' => '',
        'soc_id' => '',
        'category' => '',
        'status' => ''
    ];

    protected $formatterByKeys = ['staff_type_id'];

    protected $mapper = [
        'category' => 'staff_type_name',
        'id' => 'staff_type_id'
    ];

    /**
     * Execute the console command.
     */
    public function apply()
    {   
        $obj = $this->TenantDB()->table('chsone_staff_type_master as staffcategory')
            ->select('staffcategory.staff_type_id as id', 'staffcategory.soc_id', 'staffcategory.staff_type_name as category', 'staffcategory.status')
            ->where('staffcategory.status', '1')
            ->orderBy('staffcategory.staff_type_id', 'desc')
            ->get();

        $this->data = $obj;
    }
}
