<?php

namespace App\Console\Commands\Actions\Settings\Staff;

use App\Console\Commands\Action;
use Illuminate\Console\Command;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;

class SaveStaffCategoryListDataSource extends Action
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'datasource:savestaffcategorylist {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Input Staff Category List';

    /**
     * Execute the console command.
     */

     protected $hugeData = true;
    public function apply()
    {
        $staff_type_names = $this->input['staff_type_names'];
        $soc_id = $this->input['company_id'];
        $myData = [];
        //


        foreach($staff_type_names as $value) {
            $checkIfCategoryExists = $this->checkIfStaffCategoryExists($value);
            if ($checkIfCategoryExists) {
                $this->message = "Category $value already exists.";
                $this->status = 'error';
                $this->statusCode = 400;
                return false;
            }

            $myData[] = [
                'staff_type_name' => $value,
                'soc_id' => $soc_id,
                'status' => 1,
                'added_on' => date('Y-m-d H:i:s'),
                'updated_on' => date('Y-m-d H:i:s'),
            ];
        }

        $obj = $this->tenantDB()->table('chsone_staff_type_master');
        $obj->insert($myData);

        if($obj){
            $this->status = 'success';
            $this->message = 'Staff Category List saved successfully';
            $this->statusCode = 200;
        } else {
            $this->status = 'error';
            $this->message = 'Unable to save Staff Category List';
            $this->statusCode = 400;
        }
    }

    public function checkIfStaffCategoryExists($categoryName)
    {
        $result = $this->tenantDB()->table('chsone_staff_type_master')
            ->where('staff_type_name', $categoryName)->exists();

        if ($result) {
            // The ID exists in the table
            return "Category $categoryName exists in the table.";
        } else {
            // The ID does not exist in the table
            return false;
        }
    }
}
