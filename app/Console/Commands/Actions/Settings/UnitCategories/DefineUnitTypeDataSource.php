<?php

namespace App\Console\Commands\Actions\Settings\UnitCategories;

use Illuminate\Console\Command;
use App\Console\Commands\Action;

class DefineUnitTypeDataSource extends Action
{
    protected $signature = 'datasource:defineUnitType {flowId} {parentId} {input}';

    protected $description = 'Create Unit Type';

    public function apply()
    {
        $societyId = $this->input['company_id'];

        $unitCategories = $this->input['params'];

        if(count($unitCategories) > 1)
        {
            foreach ($unitCategories as &$unitCategory) {
                $unitCategory['soc_id'] = $societyId;
                $unitCategory['status'] = 1;
                $unitCategory['created_date'] = date('Y-m-d H:i:s');
                $unitCategory['created_by'] = $unitCategory['user_id'] ?? $this->input['user_id'];
                $unitCategory['updated_date'] = date('Y-m-d H:i:s');
                $unitCategory['updated_by'] = $unitCategory['user_id'] ?? $this->input['user_id'];
                $unitCategory['chargeable'] = $unitCategory['chargeable'] ?? 0;
                $unitCategory['water_inlets_num'] = $unitCategory['water_inlets_num'] ?? 0;
            }
        }
        else if(count($unitCategories) == 1)
        {
            $unitCategories = $unitCategories[0];
        }

        $unitCategories['soc_id'] = $societyId;
        $unitCategories['status'] = 1;
        $unitCategories['created_date'] = date('Y-m-d H:i:s');
        $unitCategories['created_by'] = $unitCategories['user_id'] ?? $this->input['user_id'];
        $unitCategories['updated_date'] = date('Y-m-d H:i:s');
        $unitCategories['updated_by'] = $unitCategories['user_id'] ?? $this->input['user_id'];

        $obj = $this->tenantDB()->table("chsone_society_units_tpl")
                    ->insert($unitCategories);

        if($obj)
        {
            $this->status = 'success';
            $this->message = 'Unit Type created successfully';
            $this->statusCode = 200;
        }
        else
        {
            $this->status = 'error';
            $this->message = 'Unable to create Unit Type';
            $this->statusCode = 400;
        }
    }
}
