<?php

namespace App\Console\Commands\Actions\Settings\Users;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use App\Console\Commands\Action;

class ListUserrolesDataSource extends Action
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'datasource:ListUserroles {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Detail User Roles';

    protected $formatter = [
        'id' => '',
        'role_name' => '',
        'role_display_name' => '',
        'role_type' => '',
        'status' => ''
    ];

    protected $formatterByKeys = ['role_id'];

    protected $mapper = [
        'id' => 'role_id'
    ];

    /**
     * Execute the console command.
     */
    public function apply()
    {
        $obj = $this->TenantDB()->table('soc_roles as roles')
            ->select('roles.role_id as id', 'roles.role_name', 'roles.role_display_name', 'roles.role_type', 'roles.status');
        $obj = $this->filter($obj);
        $obj = $obj->orderBy('roles.role_id', 'asc');
        $result = $obj->get();

        $this->data = $this->format($result->toArray());
    }
}
