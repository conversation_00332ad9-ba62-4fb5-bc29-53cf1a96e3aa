<?php

namespace App\Console\Commands\Actions\Settings\Users;

use Illuminate\Console\Command;
use App\Console\Commands\Action;
use Illuminate\Support\Facades\DB;

class UsersDataSource extends Action
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'datasource:userlist {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Get Users List';

    protected $formatter = [
        'id' => '',
        'user_first_name' => '',
        'user_last_name' => '',
        'user_name' => 'uesr_name',
        'name' => 'concat:user_first_name,user_last_name',
        'user_email_id' => '',
        'user_mobile_number' => '',
        'source' => '',
        'role' => '',
        'status' => ''
    ];

    protected $formatterByKeys = ['user.user_id'];

    protected $mapper = [
        'id' => 'user.user_id',
        'status' => 'society.user_status',
        'role' => 'society.user_role',
        'source' => 'user.user_source'
    ];

    protected $hugeData = true;

    /**
     * Execute the console command.
     */
    public function apply()
    {
        $soc_id = $this->input['company_id'];
        $userName ='';
        $userFirstName = '';
        $userLastName = '';
        $userEmailId = '';
        $userMobileNumber = '';
        $userRole = '';
        $status = '';
        $sortBy = '';
        $sortByKey = '';
        $sortByValue = '';
        $searchTerm = $this->input['filters']['search'] ?? '';


        if (isset($this->input['filters'])) {
            $userName =
            !empty($this->input['filters']['user_name']) ? $this->input['filters']['user_name'] : '';
        unset($this->input['filters']['user_name']);

        $userFirstName =
            !empty($this->input['filters']['user_first_name']) ? $this->input['filters']['user_first_name'] : '';
        unset($this->input['filters']['user_first_name']);

        $userLastName =
            !empty($this->input['filters']['user_last_name']) ? $this->input['filters']['user_last_name'] : '';
        unset($this->input['filters']['user_last_name']);

        $userEmailId =
            !empty($this->input['filters']['user_email_id']) ? $this->input['filters']['user_email_id'] : '';
        unset($this->input['filters']['user_email_id']);

        $userMobileNumber =
            !empty($this->input['filters']['user_mobile_number']) ? $this->input['filters']['user_mobile_number'] : '';
        unset($this->input['filters']['user_mobile_number']);

        $userRole =
            !empty($this->input['filters']['user_role']) ? $this->input['filters']['user_role'] : '';
        unset($this->input['filters']['user_role']);

        $status = isset($this->input['filters']['status']) ? explode(',', $this->input['filters']['status']) : [];
        }

        $page = $this->input['page'] ?? 1;
        $per_page = !empty($this->input['per_page']) ? $this->input['per_page']: ($this->hugeData ? 100000 : 10);
        $offset = ($page - 1) * $per_page;

        $obj = $this->MasterDB()->table('chsone_societies_users as society')
            ->select('user.user_id as id', 'user.user_name', 'user.user_first_name', 'user.user_last_name', 'user.user_email_id', 'user.user_mobile_number', 'user.user_type', 'user.user_source as source', 'user.role as role',
            DB::raw('COALESCE(society.user_status, 0) as status'))
            // 'society.user_status as status')
            ->join('chsone_users_master as user', 'society.user_id', '=', 'user.user_id')
            ->where('society.soc_id', '=', $soc_id);

            //->where('user.user_type', '!=', '')
            //->groupBy('user.user_id', 'user.user_name', 'user.user_first_name', 'user.user_last_name', 'user.user_email_id', 'user.user_mobile_number', 'user.user_type', 'user.user_source', 'society.user_status')
            //->orderBy('user.user_id', 'asc');

        if (!empty($this->input['sort'])) {
            $sortBy = $this->input['sort'];
            $sortByKey = array_key_first($sortBy);
            $sortByValue = $sortBy[$sortByKey];
            unset($this->input['sort']);
        }

        if ($sortBy) {
            $obj = $obj->orderBy($sortByKey, $sortByValue);
        }

        if ($userName) {
            $obj = $obj->where('user.user_name', 'like', '%' . $userName . '%');
        }

        if ($userFirstName) {
            $obj = $obj->where('user.user_first_name', 'like', '%' . $userFirstName . '%');
        }

        if ($userLastName) {
            $obj = $obj->where('user.user_last_name', 'like', '%' . $userLastName . '%');
        }

        if ($userEmailId) {
            $obj = $obj->where('user.user_email_id', 'like', '%' . $userEmailId . '%');
        }

        if ($userMobileNumber) {
            $obj = $obj->where('user.user_mobile_number', 'like', '%' . $userMobileNumber . '%');
        }

        if ($userRole) {
            $obj = $obj->where('society.user_role', 'like', '%' . $userRole . '%');
        }

        if ($status) {
            if (isset($this->input['filters']['status'])) {
                $status = explode(',', $this->input['filters']['status']);
                $status = array_map('intval', $status); // Ensure values are integers
            }
            $obj = $obj->whereIn("society.user_status", $status);
        }


        $columns = ['user.user_name', 'user.user_first_name', 'user.user_last_name', 'user.user_email_id', 'user.user_mobile_number', 'society.user_role', 'society.user_status'];

        if ($searchTerm) {
            $obj->where(function ($q) use ($columns, $searchTerm) {
                foreach ($columns as $column) {
                    $q->orWhere($column, 'LIKE', '%' . $searchTerm . '%');
                }
            });
        }


        $count = $obj->count();

        $obj = $obj->offset($offset);
        $obj = $obj->limit($per_page);
        $result = $obj->get();
        $this->data = $this->format($result->toArray());
        $this->meta['pagination']['total']=$count;
    }

    public function concat($user_first_name, $user_last_name)
    {
        return $user_first_name . ' ' . $user_last_name;
    }
}
