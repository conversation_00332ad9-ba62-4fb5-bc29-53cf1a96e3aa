<?php

namespace App\Console\Commands\Actions\Settings\Users;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use App\Console\Commands\Action;

class UsersRolesDataSource extends Action
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'datasource:usersroles {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Detail User Roles';

    protected $formatter = [
        'id' => '',
        'role_name' => '',
        'role_display_name' => '',
        'role_type' => '',
        'status' => '',
        'users' => '',
        'sr_no' => ''
    ];

    protected $formatterByKeys = ['role_id'];

    protected $mapper = [
        'id' => 'role_id'
    ];

    /**
     * Execute the console command.
     */
    public function apply()
    {
        // add pagination
        $page = $this->input['page'] ?? 1;
        $per_page = !empty($this->input['per_page']) ? $this->input['per_page']: ($this->hugeData ? 100000 : 10);

        $offset = ($page - 1) * $per_page;

        // get the count of total users
        $totalCount = $this->tenantDB()->table('soc_roles as roles')
        ->select('roles.role_id as id', 'roles.role_name', 'roles.role_display_name', 'roles.role_type', 'roles.status')
        ->count();

        // Fetch roles from the tenant database
        $roles = $this->tenantDB()->table('soc_roles as roles')
            ->select('roles.role_id as id', 'roles.role_name', 'roles.role_display_name', 'roles.role_type', 'roles.status')
            ->orderBy('roles.role_id', 'asc')
            ->offset($offset)
            ->limit($per_page)
            ->get();

        // Extract role names
        $roleNames = $roles->pluck('role_name')->toArray();

        // Fetch user counts from the master database
        $userCounts = $this->MasterDB()->table('chsone_users_master as member')
            ->select('member.role', DB::raw('COUNT(member.user_id) as user_count'))
            ->whereIn('member.role', $roleNames)
            ->where('member.soc_id', $this->input['company_id'])
            ->groupBy('member.role')
            ->pluck('user_count', 'member.role')->toArray();

        // Assign user counts to roles

        $id=0;

        foreach ($roles as $role) {

            $id++;
            $role->sr_no = $id;
            // $role->role_id=$role->id;
            $role->users = $userCounts[$role->role_name] ?? 0;
        }

        $this->data = $roles;
        $this->meta['pagination']['total'] = $totalCount;


        // $result = $obj->map(function ($role, $soc_id) {
        //     $role = $this->MasterDB()->table('chsone_users_master as member')
        //         ->where('member.role', $role->role_name)
        //         ->where('soc_id', $soc_id)
        //         ->count();


        //         dd($role);
        //     return $role;
        // });

    }
}
