<?php

namespace App\Console\Commands\Actions\Settings\Users;

use Illuminate\Console\Command;
use App\Console\Commands\Action;

class AssignUserRolesDataSource extends Action
{
    protected $signature = 'datasource:assignUserRoles {flowId} {parentId} {input}';

    protected $description = 'Assign user roles';

    /*public function apply()
    {
        $user_id = $this->input['user_id'];
        $role_ids = $this->input['role_ids'];

        $roles = $this->TenantDB()->table('soc_roles as roles')
            ->whereIn('roles.role_id', $role_ids)
            ->pluck('roles.role_name')
            ->all();

        $insertData = [];

        $userRoles = $this->MasterDB()->table('chsone_societies_users as user')
            ->where('user.user_id', $user_id)
            ->where('user.soc_id', $this->input['company_id'])
            ->update(['user_status' => 0]);

        foreach ($roles as $role) {
            $userRole = $this->MasterDB()->table('chsone_societies_users as user')
            ->select('user.id', 'user.user_id', 'user.user_role')
            ->where('user.user_id', $user_id)
            ->where('user.soc_id', $this->input['company_id'])
            ->where('user.user_role', $role)
            ->first();

            if($userRole === null){
                $insertData[] = [
                    'user_id' => $user_id,
                    'soc_id' => $this->input['company_id'],
                    'user_role' => $role,
                    'user_status' => 1
                ];
            }
            else{
                // update user status
                $this->MasterDB()->table('chsone_societies_users as user')
                    ->where('user.user_id', $user_id)
                    ->where('user.soc_id', $this->input['company_id'])
                    ->where('user.user_role', $role)
                    ->update(['user_status' => 1]);
            }
        }
        $this->MasterDB()->table('chsone_societies_users')->insert($insertData);
    }*/
    public function apply()
    {
        $user_id = $this->input['user_id'];
        $role_ids = $this->input['role_ids'];
        $role_names = $this->tenantDB()->table('soc_roles')->whereIn('role_id', $role_ids)->pluck('role_display_name')->toArray();
        $role_names = implode(',', $role_names);

        $userRole = $this->MasterDB()->table('chsone_societies_users as user')
            ->where('user.user_id', $user_id)
            ->where('user.soc_id', $this->input['company_id'])
            ->first();

        // Use the unique ID of the fetched record to perform the update
        $updatedRows = $this->MasterDB()->table('chsone_societies_users')
            ->where('id', $userRole->id) // Assuming `id` is the unique identifier
            ->update(['user_role' => $role_names]); // Update the user_role field

        $updatedRows1 = $this->MasterDB()->table('chsone_users_master')
            ->where('user_id', $user_id) // Assuming `id` is the unique identifier
            ->update(['role' => $role_names]); // Update the user_role field

        // Set the appropriate message based on the update
        if ($updatedRows > 0) {
            $this->message = 'User role updated successfully.';
        } else {
            $this->message = 'No changes were made to the user role.';
        }
    }
}
