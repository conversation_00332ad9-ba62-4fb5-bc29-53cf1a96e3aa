<?php

namespace App\Console\Commands\Actions\Settings\Users;

use App\Console\Commands\Action;
use Illuminate\Support\Facades\DB;

class UsersRolesUpdateDataSource extends Action
{
    protected $signature = 'datasource:usersRolesUpdate {flowId} {parentId} {input}';

    protected $description = 'Update User Role Details for the given role id';

    public function apply()
    {
        $role_id = $this->input['id'];
        $role_name = $this->input['role_name'];

        // Fetch the existing role by ID
        $existingRole = $this->tenantDB()->table('soc_roles')
            ->where('role_id', $role_id)
            ->first();

        if ($existingRole) {
            // Check if no changes were made
            if (
                $existingRole->role_name === $role_name &&
                strtolower($existingRole->role_type) === strtolower($this->input['role_type']) &&
                $existingRole->status == (int)$this->input['status'] &&
                $existingRole->role_display_name === $this->input['role_display_name']
            ) {
               $this->message = 'No changes were made. Role already exists.';
                $this->status = 'error';
                $this->statusCode = 400;
                $this->data = [];
                return;
            }
            // Check if another role with the same name exists (excluding this role_id)
            $existingRoleName = $this->tenantDB()->table('soc_roles')
                ->where('role_name', $role_name)
                ->where('role_id', '!=', $role_id)
                ->first();

            if ($existingRoleName) {
                $this->message = 'Role name already exists with a different ID.';
                $this->status = 'error';
                $this->statusCode = 400;
                $this->data = [];
                return;
            }

            // Update the role since there are changes
            $this->tenantDB()->table('soc_roles')
                ->where('role_id', $role_id)
                ->update([
                    'role_name' => $role_name,
                    'role_display_name' => $this->input['role_display_name'],
                    'role_type' => $this->input['role_type'],
                    'status' => $this->input['status'],
                ]);

            $oldRole = $existingRole->role_display_name;
            $newRole = $this->input['role_display_name'];

            $this->masterDB()->statement("
                UPDATE chsone_users_master
                SET role = TRIM(BOTH ',' FROM REPLACE(CONCAT(',', role, ','), ?, ?))
                WHERE FIND_IN_SET(?, role) > 0
            ", [",$oldRole,", ",$newRole,", $oldRole]);

            $this->message = 'Role updated successfully.';
            $this->status = 'success';
            $this->statusCode = 200;
            $this->data = [];
            return;
        }

        // Handle case where the role_id does not exist
        $this->message = 'Role not found.';
        $this->status = 'error';
        $this->statusCode = 404;
        $this->data = [];
    }
}
