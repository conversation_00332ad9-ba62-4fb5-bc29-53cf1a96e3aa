<?php

namespace App\Console\Commands\Actions\Settings\Users;

use Illuminate\Console\Command;
use App\Console\Commands\Action;
use Illuminate\Support\Facades\DB;

class userDetailsDataSource extends Action
{
    protected $signature = 'datasource:userDetails {flowId} {parentId} {input}';

    protected $description = 'Fetch User Details for the given user id';

    public function apply()
    {
        $obj = $this->MasterDB()->table('chsone_users_master as user')
            ->select('user.user_id as id', 'user.user_name', 'user.user_first_name', 'user.user_last_name', 'user.user_email_id', 'user.user_mobile_number', 'user.user_type', 'user.user_source', 'user.user_lang_iso_639_3', 'user.user_gmt_time_zone', 'user.added_on', 'user.modified_on', 'user.role', DB::raw('IF(society.user_status IS NULL, 0, society.user_status) as status'))
            ->join('chsone_societies_users as society', 'user.user_id', '=', 'society.user_id')
            ->where('user.user_id', $this->input['id'])
            ->where('society.soc_id', $this->input['company_id']);

        $result = $obj->first();

        $this->data = $result;
    }

    public function concat($user_first_name, $user_last_name)
    {
        return $user_first_name . ' ' . $user_last_name;
    }
}