<?php

namespace App\Console\Commands\Actions\Settings\Users;

use Illuminate\Console\Command;
use App\Console\Commands\Action;
use Illuminate\Support\Facades\DB;

class RoleDetailsDataSource extends Action
{
    protected $signature = 'datasource:roleDetails {flowId} {parentId} {input}';

    protected $description = 'Role Details';

    public function apply()
    {
        $id = $this->input['id'];
        $obj = $this->TenantDB()->table('soc_roles as roles')
            ->select('roles.role_id as id', 'roles.role_name',     \DB::raw('LOWER(roles.role_type) as role_type'), 'roles.status','roles.role_display_name')
            ->where('roles.role_id', $id)
            ->first();

        $this->data = $obj;
    }
}
