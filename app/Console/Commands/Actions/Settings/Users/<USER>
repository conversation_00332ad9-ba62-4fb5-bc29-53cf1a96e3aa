<?php

namespace App\Console\Commands\Actions\Settings\Users;

use App\Console\Commands\Action;
use Illuminate\Support\Facades\DB;

class listSocRoleMembersDownloadDataSource extends Action
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'datasource:dowloadListSocRoleMembers {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Fetch list of society role members';

    protected $formatter = [
        'id' => '',
        'name' => '',
        'role' => '',
        'user_email_id' => '',
        'user_mobile_number' => '',
        'user_type' => '',
        'user_source' => '',
        'user_dob' => '',
        'status' => ''
    ];

    protected $formatterByKeys = ['user_id'];

    protected $mapper = [
        'id' => 'user_id'
    ];

    /**
     * Execute the console command.
     */
    public function apply()
    {

        $obj = $this->MasterDB()->table('chsone_users_master')
        ->select('user_id as id', DB::raw('CONCAT(user_first_name, " ", user_last_name) as name'), 'role', 'user_email_id', 'user_mobile_number', 'user_type', 'user_source', 'user_dob','status')
        ->where('role', $this->input['role_name'])
        ->where('soc_id', $this->input['company_id'])
        ->get();

        $this->data = $this->format($obj->toArray());

    }
}
