<?php

namespace App\Console\Commands\Actions\Settings\Users;

use Illuminate\Console\Command;
use App\Console\Commands\Action;
use Illuminate\Support\Facades\DB;

class listSocRoleMembersDataSource extends Action
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'datasource:listSocRoleMembers {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Fetch list of society role members';

    protected $formatter = [
        'id' => '',
        'name' => '',
        'role' => '',
        'user_email_id' => '',
        'user_mobile_number' => '',
        'user_type' => '',
        'user_source' => '',
        'user_dob' => '',
        'status' => ''
    ];

    protected $formatterByKeys = ['user_id'];

    protected $mapper = [
        'id' => 'user_id'
    ];

    /**
     * Execute the console command.
     */
    public function apply()
    {
        $page = $this->input['page'] ?? 1;
        $per_page = !empty($this->input['per_page']) ? $this->input['per_page']: ($this->hugeData ? 100000 : 10);

        $offset = ($page - 1) * $per_page;
        $obj = $this->MasterDB()->table('chsone_users_master')
        ->select('user_id as id', DB::raw('CONCAT(user_first_name, " ", user_last_name) as name'), 'role', 'user_email_id', 'user_mobile_number', 'user_type', 'user_source', 'user_dob','status')
        ->where('role', $this->input['role_name'])
        ->where('soc_id', $this->input['company_id'])
        ->offset($offset)
        ->limit($per_page)
        ->get();

        $totalCount =$this->MasterDB()->table('chsone_users_master')
        ->where('role', $this->input['role_name'])
        ->where('soc_id', $this->input['company_id'])
        ->count();

        $this->data = $this->format($obj->toArray());
        $this->meta['pagination']['total'] = $totalCount;

    }
}
