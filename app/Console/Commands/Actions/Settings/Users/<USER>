<?php

namespace App\Console\Commands\Actions\Settings\Users;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use App\Console\Commands\Action;

class UserRolesDataSource extends Action
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'datasource:userRoles {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Detail User Roles';

    /**
     * Execute the console command.
     */
    public function apply()
    {
        // $obj = $this->MasterDB()->table('chsone_users_master as user')
        //     ->select('user.user_id', 'user.role')
        //     ->where('user.user_id', $this->input['id'])
        //     ->where('user.soc_id', $this->input['company_id']);
            //->where('user.user_status', '1');

        $obj = $this->MasterDB()->table('chsone_societies_users as user')
            ->select('user.user_id', 'user.user_role as role')
            ->where('user.user_id', $this->input['id'])
            ->where('user.soc_id', $this->input['company_id']);
            
        $results = $obj->get();
        $transformedResults = [];

        foreach ($results as $result) {
            // Split the user_role string into an array
            $userRolesArray = array_unique(explode(',', $result->role));

            // Add the new_role_array key to the result item
            $result->user_role_array = $userRolesArray;

            // Add the transformed result to the array
            $transformedResults[] = $result;
        }

        // Optionally, convert the transformed results to a collection
        $transformedResults = collect($transformedResults);
        //dd($transformedResults);
        $this->data = $transformedResults;
        // $this->data = $result;
    }
}
