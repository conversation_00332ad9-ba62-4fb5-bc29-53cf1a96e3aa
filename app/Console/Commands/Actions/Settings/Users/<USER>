<?php

namespace App\Console\Commands\Actions\Settings\Users;

use App\Console\Commands\Action;

class UsersRolesAddDataSource extends Action
{
    protected $signature = 'datasource:usersrolesadd {flowId} {parentId} {input}';

    protected $description = 'Users Roles Add DataSource';

    public function apply()
    {
        $role_name = $this->input['role_name'];
        $company_id = $this->input['company_id'];

        $existingRole = $this->tenantDB()->table('soc_roles')
            ->where('role_name', $role_name)
            ->first();

        if ($existingRole) {
            $this->message = 'Role already exists';
            $this->status = 'error';
            $this->statusCode = 400;
            $this->data = [];
        } else {
            $obj = $this->tenantDB()->table('soc_roles')
                ->insert([
                    'role_name' => $role_name,
                    'role_display_name' => $this->input['role_display_name'],
                    'role_type' => $this->input['role_type'],
                    'status' => $this->input['status'],
                    'soc_id' => $company_id
                ]);

            $this->message = 'Role Created Successfully';
            $this->status = 'success';
            $this->statusCode = 200;
            $this->data = [];

        }


    }
}
