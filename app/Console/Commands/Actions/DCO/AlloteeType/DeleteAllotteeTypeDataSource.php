<?php

namespace App\Console\Commands\Actions\DCO\AlloteeType;

use Illuminate\Console\Command;
use App\Console\Commands\Action;

class DeleteAllotteeTypeDataSource extends Action
{
    protected $signature = 'datasource:deleteAllotteeType {flowId} {parentId} {input}';

    protected $description = 'Delete Allottee Type';

    public function apply()
    {
        //if id is not present in table then shows error
        $checkId = $this->tenantDB()->table('chsone_member_type_master')
            ->where('member_type_id', $this->input['id'])
            ->first();
        if(!$checkId)
        {
            $this->status = 'error';
            $this->message = 'Member Type not found';
            $this->statusCode = 400;
            return;
        }

        $obj = $this->tenantDB()->table('chsone_member_type_master')
            ->where('member_type_id', $this->input['id'])
            ->update([
                'status' => 0,
                'updated_date' => date('Y-m-d H:i:s'),
                'updated_by' => $this->input['created_by'] ?? 0
            ]);

        if ($obj)
        {
            $this->status = 'success';
            $this->message = 'Allottee Type deleted successfully';
            $this->statusCode = 200;
        }
        else
        {
            $this->status = 'error';
            $this->message = 'Allottee Type not deleted';
            $this->statusCode = 500;
        }
    }
}
