<?php

namespace App\Console\Commands\Actions\DCO\AlloteeType;

use Illuminate\Console\Command;
use App\Console\Commands\Action;

class CreateAllotteeTypeDataSource extends Action
{
    protected $signature = 'datasource:createAllotteeType {flowId} {parentId} {input}';

    protected $description = 'Create Allottee Type';

    public function apply()
    {
        $allottee_type = $this->input['allottee_type'];
        $data = [];
        foreach ($allottee_type as $type) {
            $data[] = [
                'soc_id' => $this->input['company_id'],
                'member_type_name' => $type,
                'defined_by' => 'user',
                'status' => 1, // '1' => 'Active', '0' => 'Inactive
                'created_date' => date('Y-m-d H:i:s'),
                'created_by' => $this->input['user_id'] ?? 0,
                'updated_date' => date('Y-m-d H:i:s'),
                'updated_by' => $this->input['user_id'] ?? 0
            ];
        }

        if (!empty($data))
        {
            $this->tenantDB()->table('chsone_member_type_master')
                ->insert($data);

            $this->status = 'success';
            $this->message = 'Allottee Type created successfully';
            $this->statusCode = 200;
        }
        else
        {
            $this->status = 'error';
            $this->message = 'Allottee Type not created';
            $this->statusCode = 400;
        }


    }
}
