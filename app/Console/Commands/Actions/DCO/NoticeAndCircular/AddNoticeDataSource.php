<?php

namespace App\Console\Commands\Actions\DCO\NoticeAndCircular;

use App\Console\Commands\Action;
use App\Http\Traits\NotificationTraits;

class AddNoticeDataSource extends Action
{
    use NotificationTraits;

    protected $signature = 'datasource:addNotice {flowId} {parentId} {input}';

    protected $description = 'Add Notice Data Source';

    public function apply()
    {
        $companyId = $this->input['company_id'];
        $type = $this->input['type'];
        $subject = $this->input['subject'];
        $body = $this->input['body'];
        $effective_from = $this->input['effective_from'];
        $published_on = $this->input['published_on'];
        $visibility = $this->input['visibility'] ?? '';

        // New notification parameters based on actual input structure
        $notifyVia = $this->input['notify_via'] ?? []; // Array: ['app', 'sms', 'email']
        $sendTo = $this->input['send_to'] ?? null; // 'individual_members', 'committee_members', 'all', etc.
        $selectedMembers = $this->input['members'] ?? []; // Array of member IDs

        // Determine if notifications should be sent
        // $sendNotification = !empty($notifyVia) && in_array($sendTo, ['individual_members', 'committee_members', 'all_members']);
        $sendNotification = false;

        // Convert notify_via array to notification method
        $notificationMethod = $this->determineNotificationMethod($notifyVia);

        $company = $this->masterDB()->table('chsone_societies_master')
            ->select(
                'soc_id as id',
                'soc_type_id',
                'soc_name',
                'soc_reg_num',
                'soc_address_1',
                'soc_address_2',
                'soc_landmark',
                'soc_city_or_town',
                'soc_state',
                'soc_pincode',
                'status',
                'completed',
            )
            ->where('soc_id', $companyId)
            ->where('status', 1)->first();

        $company_initials = $this->getCompanyInitials($company->soc_name);
        
        $notice_ref_no = $this->tenantDB()->table('chsone_notices')->max('notice_ref_no');
        $ref_no = str_replace($company_initials, '', $notice_ref_no);
        // $ref_no = $ref_no + 1;
        // add the company initials to the ref no and keep the number of digits to 4
        $notice_ref_no = $company_initials . str_pad($ref_no, 4, '0', STR_PAD_LEFT);
        $fk_member_id = $this->input['fk_member_id'] ?? null;

        $obj = $this->tenantDB()->table('chsone_notices')
            ->insert([
                'notice_ref_no' => $notice_ref_no,
                'soc_id' => $companyId,
                'fk_member_id' => $fk_member_id,
                'type' => $type,
                'subject' => $subject,
                'body' => $body,
                'status' => 1,
                'effective_from' => $effective_from,
                'published_on' => $published_on,
                'visibility' => $visibility,
                'created_by' => $this->input['created_by'] ?? 0,
                'created_date' => date('Y-m-d H:i:s'),
                'updated_by' => $this->input['updated_by'] ?? 0,
                'updated_date' => date('Y-m-d H:i:s'),
            ]);

        // Send notifications if requested
        if ($sendNotification) {

            $this->sendNoticeNotifications(
                $companyId,
                $subject,
                $body,
                $type,
                $notificationMethod,
                $sendTo,
                $selectedMembers
            );
        }
        if($obj) {
            $this->status = 'success';
            $this->message = 'Notice added successfully';
            $this->statusCode = 200;
        } else {
            $this->status = 'error';
            $this->message = 'Failed to add notice';
            $this->statusCode = 400;
        }
    }

    /**
     * Send notifications to members based on scope and method
     */
    private function sendNoticeNotifications($companyId, $subject, $body, $type, $notificationMethod, $sendTo, $selectedMembers)
    {
        try {
            // Get members based on scope
            $members = $this->getMembersForNotification($companyId, $sendTo, $selectedMembers);

            if (empty($members)) {
                logger()->warning('No members found for notification', [
                    'company_id' => $companyId,
                    'send_to' => $sendTo,
                    'selected_members' => $selectedMembers
                ]);
                return;
            }

            // Send notifications to each member
            foreach ($members as $member) {
                $this->sendNotificationToMember($member, $subject, $body, $type, $notificationMethod);
            }

            logger()->info('Notice notifications sent successfully', [
                'company_id' => $companyId,
                'type' => $type,
                'members_count' => count($members),
                'method' => $notificationMethod
            ]);

        } catch (\Exception $e) {
            logger()->error('Failed to send notice notifications', [
                'error' => $e->getMessage(),
                'company_id' => $companyId,
                'type' => $type
            ]);
        }
    }

    /**
     * Get members based on notification scope
     */
    private function getMembersForNotification($companyId, $sendTo, $selectedMembers)
    {
        $query = $this->tenantDB()->table('chsone_members_master as members')
            ->select(
                'members.id',
                'members.member_first_name',
                'members.member_last_name',
                'members.member_email_id',
                'members.member_mobile_number',
                'units.unit_flat_number',
                'units.soc_building_name',
                'member_type.member_type_name'
            )
            ->leftJoin('chsone_units_master as units', 'units.unit_id', '=', 'members.fk_unit_id')
            ->leftJoin('chsone_member_type_master as member_type', 'member_type.member_type_id', '=', 'members.member_type_id')
            ->where('members.soc_id', $companyId)
            ->where('members.status', 1);

        switch ($sendTo) {
            case 'all_members':
                // Send to all active members
                break;

            case 'individual_members':
                if (!empty($selectedMembers)) {
                    $query->whereIn('members.id', $selectedMembers);
                } else {
                    return []; // No members selected
                }
                break;

            case 'committee_members':
                // Get committee members from committee master table
                return $this->getCommitteeMembers($companyId);

            default:
                // Default to primary members
                $query->where('member_type.member_type_name', 'Primary');
                break;
        }

        return $query->get()->toArray();
    }

    /**
     * Get committee members from committee master table
     */
    private function getCommitteeMembers($companyId)
    {
        return $this->tenantDB()->table('chsone_committee_master as committee')
            ->select(
                'members.id',
                'members.member_first_name',
                'members.member_last_name',
                'members.member_email_id',
                'members.member_mobile_number',
                'units.unit_flat_number',
                'units.soc_building_name',
                'member_type.member_type_name',
                'committee.designation'
            )
            ->leftJoin('chsone_members_master as members', 'members.id', '=', 'committee.member_id')
            ->leftJoin('chsone_units_master as units', 'units.unit_id', '=', 'members.fk_unit_id')
            ->leftJoin('chsone_member_type_master as member_type', 'member_type.member_type_id', '=', 'members.member_type_id')
            ->where('committee.soc_id', $companyId)
            ->where('committee.status', 1)
            ->where('members.status', 1)
            ->get()->toArray();
    }

    /**
     * Determine notification method from notify_via array
     */
    private function determineNotificationMethod($notifyVia)
    {
        $hasEmail = in_array('email', $notifyVia);
        $hasSms = in_array('sms', $notifyVia);

        if ($hasEmail && $hasSms) {
            return 'both';
        } elseif ($hasEmail) {
            return 'email';
        } elseif ($hasSms) {
            return 'sms';
        }

        return 'both'; // Default fallback
    }

    /**
     * Send notification to individual member
     */
    private function sendNotificationToMember($member, $subject, $body, $type, $method)
    {
        try {
            // Prepare notification data
            $notificationData = [
                'member_name' => trim($member->member_first_name . ' ' . $member->member_last_name),
                'email' => $member->member_email_id,
                'mobile_number' => $member->member_mobile_number,
                'unit_flat_number' => $member->unit_flat_number,
                'soc_building_name' => $member->soc_building_name,
                'subject' => $subject,
                'body' => $body,
                'type' => $type,
                'title' => ucfirst($type) . ' Notification'
            ];

            // Send email notification
            if ($method === 'email' || $method === 'both') {
                if (!empty($member->member_email_id)) {
                    $this->sendEmailNotification($notificationData);
                }
            }

            // Send SMS notification
            if ($method === 'sms' || $method === 'both') {
                if (!empty($member->member_mobile_number)) {
                    $this->sendSMSNotification($notificationData);
                }
            }

        } catch (\Exception $e) {
            logger()->error('Failed to send notification to member', [
                'member_id' => $member->id,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Send email notification
     */
    private function sendEmailNotification($data)
    {
        try {
            $emailData = [
                'email' => $data['email'],
                'member_name' => $data['member_name'],
                'unit_flat_number' => $data['unit_flat_number'],
                'soc_building_name' => $data['soc_building_name'],
                'subject' => $data['subject'],
                'body' => $data['body'],
                'type' => $data['type'],
                'title' => $data['title']
            ];

            $this->sendEmailFromTemplate($emailData);

        } catch (\Exception $e) {
            logger()->error('Failed to send email notification', [
                'email' => $data['email'],
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Send SMS notification
     */
    private function sendSMSNotification($data)
    {
        try {
            $smsMessage = "New {$data['type']}: {$data['subject']}. Please check your society app for details.";

            $smsData = [
                'mobile_number' => $data['mobile_number'],
                'message' => $smsMessage,
                'member_name' => $data['member_name'],
                'type' => $data['type'],
                'subject' => $data['subject']
            ];

            // $this->sendSMS($smsData);

        } catch (\Exception $e) {
            logger()->error('Failed to send SMS notification', [
                'mobile' => $data['mobile_number'],
                'error' => $e->getMessage()
            ]);
        }
    }
}