<?php

namespace App\Console\Commands\Actions\DCO;

use Illuminate\Console\Command;
use App\Console\Commands\Action;

class CommitteeListDataSource extends Action
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'datasource:committeeList {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Get Committee List';

    protected $formatter =  [
        "id" => "",
        "committee_id" => "",
        "parent_id" => "",
        "committee_name" => "",
        "resolution_of_form_date" => "",
        "effective_date" => "concat:effective_date,onwards",
        "dissolve_date" => "",
        "resolution_to_dissolve_date" => "",
        "status" => ""
    ];
    protected $formatterByKeys =  ['id'];

    protected $mapper =  [
        'id' => "committees.committee_id"
    ];

    /**
     * Execute the console command.
     */
    public function apply()
    {
        $obj = $this->tenantDB()->table("chsone_committees")
            ->selectRaw("
                committee_id AS id,
                committee_id,
                parent_id,
                committee_name,
                resolution_of_form_date,
                effective_date,
                dissolve_date,
                resolution_to_dissolve_date,
                IF(dissolve_date = '0000-00-00', 'Active', 'Dissolved') AS status
            ")
            ->where('effective_date', '<=', date('Y-m-d'))
            ->where ('dissolve_date', '=', '0000-00-00')
            ->orderByDesc('committee_id');

        $result = $obj->get();
        $this->data = $this->format($result->toArray());
    }

    public function concat($a, $b)
    {
        return $a . "";
    }
}
