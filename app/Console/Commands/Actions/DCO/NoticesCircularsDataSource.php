<?php

namespace App\Console\Commands\Actions\DCO;

use Illuminate\Console\Command;
use App\Console\Commands\Action;
//import workflow
use App\Console\Commands\Workflow;

class NoticesCircularsDataSource extends Action
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'datasource:NoticesCircularsDataSource {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Notices and Circulars Data Source';

    protected $formatter = [
        'id' => '',
        'notice_ref_no' => '',
        'subject' => '',
        'type' => '',
        'published_on' => '',
        'effective_from' => '',
    ];

    /**
     * Execute the console command.
     */
    public function apply()
    {
        $result = $this->tenantDB()->table('chsone_notices')
            ->select('notice_id as id', 'notice_ref_no', 'subject', 'type', 'published_on', 'effective_from')
            ->orderBy('notice_id', 'desc')
            ->paginate($this->input['per_page'] ?? 10);
    
        $this->data = $this->format($result->items());
    }
    
}
