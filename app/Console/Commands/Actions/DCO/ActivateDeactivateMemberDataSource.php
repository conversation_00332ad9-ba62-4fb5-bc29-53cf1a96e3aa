<?php

namespace App\Console\Commands\Actions\DCO;

use Illuminate\Console\Command;
use App\Console\Commands\Action;

class ActivateDeactivateMemberDataSource extends Action
{
    protected $signature = 'datasource:activateDeactivateMember {flowId} {parentId} {input}';

    protected $description = 'Activate Deactivate Member DataSource';

    public function apply()
    {
        $type = $this->input['type'];
        $ids = $this->input['ids'];

        $obj = $this->tenantDB()->table('chsone_members_master')
            ->whereIn('id', $ids)
            ->update([
                'status' => $type // 1 for active and 0 for inactive
            ]);

        if($obj){
            $this->status = 'success';
            $this->message = 'Member status updated successfully';
            $this->statusCode = 200;
            $this->data = [];
        }else{
            $this->status = 'error';
            $this->message = 'Member status not updated';
            $this->statusCode = 400;
            $this->data = [];
        }

    }
}