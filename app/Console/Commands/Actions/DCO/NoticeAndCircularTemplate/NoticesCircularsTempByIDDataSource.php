<?php

namespace App\Console\Commands\Actions\DCO\NoticeAndCircularTemplate;

use Illuminate\Console\Command;
use App\Console\Commands\Action;

class NoticesCircularsTempByIDDataSource extends Action
{
    protected $signature = 'datasource:NoticesCircularsTempByID {flowId} {parentId} {input}';

    protected $description = 'Get Notices Circulars template by ID';

    public function apply()
    {
        $id = $this->input['id'];
        $obj = $this->tenantDB()->table('chsone_notice_templates')
            ->select('notice_id as id', 'notice_ref_no', 'subject', 'body', 'type')
            ->where('notice_id', $id)
            ->first();

        $this->data = $obj;
    }
}