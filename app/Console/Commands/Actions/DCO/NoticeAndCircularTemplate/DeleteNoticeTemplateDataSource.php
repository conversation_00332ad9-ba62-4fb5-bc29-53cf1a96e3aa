<?php

namespace App\Console\Commands\Actions\DCO\NoticeAndCircularTemplate;

use Illuminate\Console\Command;
use App\Console\Commands\Action;

class DeleteNoticeTemplateDataSource extends Action
{
    protected $signature = 'datasource:deleteNoticeTemplate {flowId} {parentId} {input}';

    protected $description = 'Delete Notice Template DataSource';

    public function apply()
    {
        $id = $this->input['id'];
        
        $obj = $this->tenantDB()->table('chsone_notice_templates')
            ->where('notice_id', $id)
            ->update([
                'status' => 0,
                'updated_by' => $this->input['updated_by'] ?? 0,
                'updated_date' => date('Y-m-d H:i:s'),
            ]);

        if($obj){
            $this->status = 'success';
            $this->message = 'Notice template deleted successfully';
            $this->statusCode = 200;
        }
        else{
            $this->status = 'error';
            $this->message = 'Failed to delete notice template';
            $this->statusCode = 400;
        }
    }
}