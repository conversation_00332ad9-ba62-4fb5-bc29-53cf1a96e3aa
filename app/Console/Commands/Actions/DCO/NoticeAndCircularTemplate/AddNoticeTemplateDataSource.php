<?php

namespace App\Console\Commands\Actions\DCO\NoticeAndCircularTemplate;

use Illuminate\Console\Command;
use App\Console\Commands\Action;

class AddNoticeTemplateDataSource extends Action
{
    protected $signature = 'datasource:addNoticeTemplate {flowId} {parentId} {input}';

    protected $description = 'Add Notice Template';

    public function apply()
    {
        $companyId = $this->input['company_id'];
        $company = $this->masterDB()->table('chsone_societies_master')
            ->select(
                'soc_id as id',
                'soc_type_id',
                'soc_name',
                'soc_reg_num',
                'soc_address_1',
                'soc_address_2',
                'soc_landmark',
                'soc_city_or_town',
                'soc_state',
                'soc_pincode',
                'status',
                'completed',
            )
            ->where('soc_id', $companyId)
            ->where('status', 1)->first();

        $company_initials = $this->getCompanyInitials($company->soc_name);
        
        $notice_ref_no = $this->tenantDB()->table('chsone_notices')->max('notice_ref_no');
        $ref_no = str_replace($company_initials, '', $notice_ref_no);
        // Commenting it out since string cannot be incremented
        // $ref_no = $ref_no + 1;
        // add the company initials to the ref no and keep the number of digits to 4
        $notice_ref_no = $company_initials . str_pad($ref_no, 4, '0', STR_PAD_LEFT);

        $obj = $this->tenantDB()->table('chsone_notice_templates')
            ->insert([
                'notice_ref_no' => $notice_ref_no,
                'soc_id' => $companyId,
                'subject' => $this->input['subject'],
                'body' => $this->input['body'],
                'type' => $this->input['type'],
                'status' => 1,
                'created_by' => $this->input['created_by'] ?? 0,
                'created_date' => date('Y-m-d H:i:s'),
                'updated_by' => $this->input['created_by'] ?? 0,
                'updated_date' => date('Y-m-d H:i:s'),
            ]);

        if($obj){
            $this->status = 'success';
            $this->message = 'Notice template added successfully';
            $this->statusCode = 200;
            $this->data = [];
        }
        else{
            $this->status = 'error';
            $this->message = 'Failed to add notice template';
            $this->statusCode = 400;
            $this->data = [];
        }
    }
}