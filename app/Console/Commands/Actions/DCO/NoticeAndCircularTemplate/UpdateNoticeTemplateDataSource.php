<?php

namespace App\Console\Commands\Actions\DCO\NoticeAndCircularTemplate;

use Illuminate\Console\Command;
use App\Console\Commands\Action;

class UpdateNoticeTemplateDataSource extends Action
{
    protected $signature = 'datasource:updateNoticeTemplate {flowId} {parentId} {input}';

    protected $description = 'Update Notice Template DataSource';

    public function apply()
    {
        $obj = $this->tenantDB()->table('chsone_notice_templates')
            ->where('notice_id', $this->input['id'])
            ->update([
                'subject' => $this->input['subject'],
                'body' => $this->input['body'],
                'type' => $this->input['type'],
                'updated_by' => $this->input['updated_by'] ?? 0,
                'updated_date' => date('Y-m-d H:i:s'),
            ]);

        if($obj){
            $this->status = 'success';
            $this->message = 'Notice template updated successfully';
            $this->statusCode = 200;
        }
        else{
            $this->status = 'error';
            $this->message = 'Failed to update notice template';
            $this->statusCode = 400;
        }
    }
}