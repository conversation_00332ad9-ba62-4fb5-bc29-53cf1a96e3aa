<?php

namespace App\Console\Commands\Actions\DCO\Amenities;

use Illuminate\Console\Command;
use App\Console\Commands\Action;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\Rule;

class AmenitiesListDataSource extends Action
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'datasource:amenitiesList {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Amenities List';

    /**
     * Execute the console command.
     */
    public function apply()
    {
        try {
            // Pagination parameters
            $perPage = isset($this->input['per_page']) && (int)$this->input['per_page'] > 0 ? (int)$this->input['per_page'] : 10;
            $currentPage = isset($this->input['current_page']) && (int)$this->input['current_page'] > 0 ? (int)$this->input['current_page'] : 1;
            $offset = ($currentPage - 1) * $perPage;

            $query = $this->tenantDB()
                ->table('chsone_amenities_tpl as a')
                ->leftJoin('slot_configurations as s', 'a.amen_tpl_id', '=', 's.amenity_id')
                ->select(
                    'a.amen_tpl_id as id',
                    'a.soc_amen_name',
                    'a.status',
                    'a.bookable',
                    'a.is_multi_subscriber',
                    'a.is_recurring',
                    'a.capacity',
                    'a.charge',
                    'a.deposit_amount as deposit',
                    'a.added_on',
                    'a.modified_on',
                    'a.description',
                    's.id as slot_id',
                    's.slot_duration_mins',
                    's.recurrence_type',
                    's.recurrence_value',
                    's.daily_start_time',
                    's.daily_end_time',
                    's.active_days',
                    's.is_active as slot_is_active',
                    's.created_at',
                    's.updated_at'
                );

            // Get total count for pagination
            $total = (clone $query)->count();

            // Get paginated results
            $results = $query
                ->orderBy('a.added_on', 'desc')
                ->get();

            $lastPage = $perPage > 0 ? (int) ceil($total / $perPage) : 1;

            $this->message    = "Amenities fetched successfully.";
            $this->status     = "success";
            $this->statusCode = 200;
            $this->data       = $results;
        } catch (\Exception $e) {
            dd($e);
        }    

    }

}
