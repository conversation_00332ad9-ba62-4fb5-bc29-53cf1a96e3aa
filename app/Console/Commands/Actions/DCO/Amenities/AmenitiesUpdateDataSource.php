<?php

namespace App\Console\Commands\Actions\DCO\Amenities;

use Illuminate\Console\Command;
use App\Console\Commands\Action;

class AmenitiesUpdateDataSource extends Action
{
    protected $signature = 'datasource:amenitiesUpdate {flowId} {parentId} {input}';
    protected $description = 'Update Amenity';

    public function apply()
    {
        try {
            $id = $this->input['id'] ?? null;
            if (!$id) {
                $this->status = 'error';
                $this->message = 'Amenity ID is required';
                $this->statusCode = 400;
                $this->data = [];
                return;
            }
            $updateData = [
                'soc_amen_name' => $this->input['soc_amen_name'] ?? null,
                'status' => $this->input['status'] ?? 1,
                'bookable' => $this->input['chargeable'] ?? 0,
                'is_multi_subscriber' => $this->input['is_multi_subscriber'] ?? 1,
                'is_recurring' => $this->input['is_recurring'] ?? 1,
                'capacity' => $this->input['capacity'] ?? 2,
                'charge' => $this->input['charge'] ?? 0,
                'deposit_amount' => $this->input['deposit'] ?? 0,
                'image' => $this->input['image'] ?? null,
                'description' => $this->input['message'] ?? null,
                'modified_on' => now(),
            ];

            $affected = $this->tenantDB()->table('chsone_amenities_tpl')->where('amen_tpl_id', $id)->update($updateData);
            
            if ($affected == 0) {
                $this->status = 'error';
                $this->message = 'Amenity not found or nothing changed.';
                $this->statusCode = 404;
                $this->data = [];
            }

            $active_days = json_encode($this->input['active_days'] ?? []);

            $updateData2 = [
                'slot_duration_mins' => $this->input['slot_duration_mins'] ?? 1,
                'recurrence_type' => $this->input['recurrence_type'] ?? null,
                'recurrence_value' => $this->input['recurrence_value'] ?? 1,
                'daily_start_time' => $this->input['daily_start_time'] ?? '00:00:00',
                'daily_end_time' => $this->input['daily_end_time'] ?? '23:59:59',
                'active_days' => $active_days,
                'is_active' => $this->input['is_active'] ?? 1,
            ];

            $this->tenantDB()->table('slot_configurations')->where('amenity_id', $id)->update($updateData2);

            $this->status = 'success';
            $this->message = 'Amenity updated successfully.';
            $this->data = [];
        } catch (\Exception $e) {
            dd($e);
        }
    }
}
