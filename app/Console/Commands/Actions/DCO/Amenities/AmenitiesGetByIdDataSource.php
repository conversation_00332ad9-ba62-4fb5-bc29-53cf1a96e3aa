<?php

namespace App\Console\Commands\Actions\DCO\Amenities;

use Illuminate\Console\Command;
use App\Console\Commands\Action;

class AmenitiesGetByIdDataSource extends Action
{
    protected $signature = 'datasource:amenitiesGetById {flowId} {parentId} {input}';
    protected $description = 'Get Amenity By ID';

    public function apply()
    {
        try {
            $id = $this->input['id'] ?? null;
            if (!$id) {
                $this->status = 'error';
                $this->message = 'Amenity ID is required';
                $this->statusCode = 400;
                $this->data = [];
                return;
            }

            $result = $this->tenantDB()->table('chsone_amenities_tpl as a')
                ->leftJoin('slot_configurations as s', 'a.amen_tpl_id', '=', 's.amenity_id')
                ->select(
                    'a.amen_tpl_id as id',
                    'a.soc_amen_name',
                    'a.status',
                    'a.bookable as chargeable',
                    'a.is_multi_subscriber',
                    'a.is_recurring',
                    'a.capacity',
                    'a.charge',
                    'a.deposit_amount as deposit',
                    'a.description as message',
                    'a.added_on',
                    'a.modified_on',
                    's.id as slot_id',
                    's.slot_duration_mins',
                    's.recurrence_type',
                    's.recurrence_value',
                    's.daily_start_time',
                    's.daily_end_time',
                    's.active_days',
                    's.is_active as slot_is_active',
                    's.created_at',
                    's.updated_at'
                )
                ->where('a.amen_tpl_id', $id)
                ->first();

            if ($result == null) {
                $this->message = 'Amenity not found.';
                $this->status = 'error';
                $this->statusCode = 404;
                $this->data = [];
            } 

            if ($result) {
                $result->chargeable = (bool) $result->chargeable;
                $result->status = (bool) $result->status;
                $result->is_multi_subscriber = (bool) $result->is_multi_subscriber;
                $result->is_recurring = (bool) $result->is_recurring;
                $result->slot_is_active = (bool) $result->slot_is_active;
                $result->active_days = json_decode($result->active_days);
            }            
            $this->data = $result;
            
        } catch (\Exception $e) {
            dd($e);
        }
    }
}
