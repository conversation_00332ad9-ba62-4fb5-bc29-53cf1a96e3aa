<?php

namespace App\Console\Commands\Actions\DCO\Amenities;

use Illuminate\Console\Command;
use App\Console\Commands\Action;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\Rule;

class AmenitiesAddDataSource extends Action
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'datasource:amenitiesAdd {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Amenities Add';

    /**
     * Execute the console command.
     */
    public function apply()
    {
        try {
            $amenityData = [
                'soc_amen_name' => $this->input['soc_amen_name'],
                'status' => $this->input['status'] ?? 1,
                'bookable' => $this->input['chargeable'] ?? 0,
                'is_multi_subscriber' => $this->input['is_multi_subscriber'] ?? 1,
                'is_recurring' => $this->input['is_recurring'] ?? 1,
                'capacity' => $this->input['capacity'] ?? 2,
                'charge' => $this->input['charge'] ?? 0,
                'deposit_amount' => $this->input['deposit'] ?? 0,
                'image' => $this->input['image'] ?? null,
                'description' => $this->input['message'] ?? null,
                'added_on' => now(),
                'modified_on' => now(),
                
            ];
    
            $amenity = $this->tenantDB()->table('chsone_amenities_tpl')->insertGetId($amenityData);

            $active_days = json_encode($this->input['active_days'] ?? []);
            $slotData = [
                'amenity_id' => $amenity,
                'slot_duration_mins' => $this->input['slot_duration_mins'] ?? null,
                'recurrence_type' => $this->input['recurrence_type'] ?? 'monthly',
                'recurrence_value' => $this->input['recurrence_value'] ?? 1,
                'daily_start_time' => $this->input['daily_start_time'] ?? '00:00:00',
                'daily_end_time' => $this->input['daily_end_time'] ?? '23:59:59',
                'active_days' => $active_days,
                'is_active' => $this->input['is_active'] ?? 1,
            ];


            $slot = $this->tenantDB()->table('slot_configurations')->insert($slotData);
            
            if ($slot) {
                $this->message    = "Amenity created successfully.";
                $this->status     = "success";
                $this->statusCode = 200;
                $this->data = [];
            }
        } catch (\Exception $e) {
            dd($e);
        }
    }

}
