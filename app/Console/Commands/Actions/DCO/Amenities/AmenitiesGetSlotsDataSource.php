<?php

namespace App\Console\Commands\Actions\DCO\Amenities;

use App\Console\Commands\Action;


class AmenitiesGetSlotsDataSource extends Action
{
    protected $signature = 'datasource:amenitiesGetSlots {flowId} {parentId} {input}';
    protected $description = 'Action for Get Amenity Slots';
    
    /**
     * Get slot config and bookings for an amenity
     * @param int $amenity_id
     * @return array
     */
    /**
     * Get slot config and bookings for an amenity, with pagination
     * @param int $amenity_id
     * @param int $page
     * @param int $perPage
     * @return array
     */
    public function apply()
    {
        $amenity_id = $this->input['amenity_id'];
        $page = $this->input['current_page'] ?? 1;
        $perPage = $this->input['per_page'] ?? 10;

        $config = $this->tenantDB()->table('slot_configurations')
            ->where('amenity_id', $amenity_id)
            ->first();
    
        $offset = ($page - 1) * $perPage;
    
        $total = $this->tenantDB()->table('amenities_bookings')
            ->where('amenity_id', $amenity_id)
            ->where('status', '!=', 'cancelled')
            ->count();
    
        $bookings = $this->tenantDB()->table('amenities_bookings')
            ->where('amenity_id', $amenity_id)
            ->where('status', '!=', 'cancelled')
            ->offset($offset)
            ->limit($perPage)
            ->get();
    
        $finalData = [
            [$config],
            $bookings,
            // [
            //     'total' => $total,
            //     'page' => $page,
            //     'perPage' => $perPage
            // ]
        ];

        $this->data = $finalData;
    }
    
}
