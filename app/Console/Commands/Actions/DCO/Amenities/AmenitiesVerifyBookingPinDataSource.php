<?php

namespace App\Console\Commands\Actions\DCO\Amenities;

use App\Console\Commands\Action;
use Carbon\Carbon;

class AmenitiesVerifyBookingPinDataSource extends Action
{
    protected $signature = 'datasource:amenitiesVerifyBookingPin {flowId} {parentId} {input}';
    protected $description = 'Action for Verifying Booking Pin';

    public function apply()
    {
        $booking_id = $this->input['booking_id'] ?? null;
        $pin = $this->input['pin'] ?? null;
        $now = Carbon::now();

        if (!$booking_id || !$pin) {
            $this->data = [];
            $this->message = 'booking_id and pin are required.';
            $this->status = 'error';
            $this->statusCode = 400;
            return;
        }

        $booking = $this->tenantDB()->table('amenities_bookings')
            ->where('id', $booking_id)
            ->where('pin', $pin)
            ->where('status', '!=', 'cancelled')
            ->where('start_time', '<=', $now)
            ->where('end_time', '>=', $now)
            ->first();

        if ($booking) {
            $this->data = [];
            $this->message = 'Pin verified successfully.';
            $this->status = 'success';
            $this->statusCode = 200;
        } 

        $this->data = [];
        $this->message = 'Invalid pin or booking is not in a valid time range.';
        $this->status = 'error';
        $this->statusCode = 400;
    }
}
