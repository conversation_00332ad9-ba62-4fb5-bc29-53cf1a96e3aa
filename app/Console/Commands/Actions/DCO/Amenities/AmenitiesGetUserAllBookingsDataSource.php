<?php

namespace App\Console\Commands\Actions\DCO\Amenities;

use App\Console\Commands\Action;

class AmenitiesGetUserAllBookingsDataSource extends Action
{
    protected $signature = 'datasource:amenitiesGetUserAllBookings {flowId} {parentId} {input}';
    protected $description = 'Action for Get All Bookings by User';

    public function apply()
    {
        $user_id = $this->input['user_id'];

        $bookings = $this->tenantDB()->table('amenities_bookings')
            ->where('user_id', $user_id)
            ->get();

        if ($this->input['status'] == 'cancelled') {
            $bookings = $bookings->where('status', 'cancelled');
        }   

        if ($this->input['status'] == 'booked') {
            $bookings = $bookings->where('status', 'booked');
        }   

        if ($this->input['status'] == 'completed') {
            $bookings = $bookings->where('status', 'completed');
        }   

        $this->data = $bookings;
    }
}
