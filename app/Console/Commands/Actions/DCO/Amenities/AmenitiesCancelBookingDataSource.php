<?php

namespace App\Console\Commands\Actions\DCO\Amenities;

use App\Console\Commands\Action;

class AmenitiesCancelBookingDataSource extends Action
{
    protected $signature = 'datasource:amenitiesCancelBooking {flowId} {parentId} {input}';
    protected $description = 'Action for Cancel Booking';

    public function apply()
    {
        $booking_id = $this->input['booking_id'] ?? null;
        if (!$booking_id) {
            $this->data = [];
            $this->message = 'booking_id is required.';
            $this->status = 'error';
            $this->statusCode = 400;
            return;
        }
        $affected = $this->tenantDB()->table('amenities_bookings')
            ->where('id', $booking_id)
            ->update(['status' => $this->input['status']]);
        if ($affected) {
            $this->data = [];
            $this->message = 'Booking cancelled successfully.';
            $this->status = 'success';
            $this->statusCode = 200;
        } else {
            $this->data = [];
            $this->message = 'Booking not found or already cancelled.';
            $this->status = 'error';
            $this->statusCode = 400;
        }
    }
}
