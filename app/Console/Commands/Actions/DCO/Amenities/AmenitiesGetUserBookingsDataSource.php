<?php

namespace App\Console\Commands\Actions\DCO\Amenities;

use App\Console\Commands\Action;

class AmenitiesGetUserBookingsDataSource extends Action
{
    protected $signature = 'datasource:amenitiesGetUserBookings {flowId} {parentId} {input}';
    protected $description = 'Action for Get Amenity Bookings by User';

    public function apply()
    {
        
        $amenity_id = $this->input['amenity_id'];
        $user_id = $this->input['user_id'];

        $bookings = $this->tenantDB()->table('amenities_bookings')
            ->where('amenity_id', $amenity_id)
            ->where('user_id', $user_id)
            ->get();

        if ($this->input['status'] == 'cancelled') {
            $bookings = $bookings->where('status', 'cancelled');
        }   

        if ($this->input['status'] == 'booked') {
            $bookings = $bookings->where('status', 'booked');
        }   

        if ($this->input['status'] == 'completed') {
            $bookings = $bookings->where('status', 'completed');
        }   

        $this->data = $bookings;
    }
}
