<?php

namespace App\Console\Commands\Actions\DCO\Amenities;

use Illuminate\Console\Command;
use App\Console\Commands\Action;

class AmenitiesDeleteDataSource extends Action
{
    protected $signature = 'datasource:amenitiesDelete {flowId} {parentId} {input}';
    protected $description = 'Delete Amenity';

    public function apply()
    {
        try {
            $id = $this->input['id'] ?? null;
            if (!$id) {
                $this->status = 'error';
                $this->message = 'Amenity ID is required';
                $this->statusCode = 400;
                $this->data = [];
                return;
            }
            $deleted = $this->tenantDB()->table('chsone_amenities_tpl')->where('amen_tpl_id', $id)->delete();

            if ($deleted == 0) {
                $this->status = 'error';
                $this->message = 'Amenity not found or cannot be deleted.';
                $this->statusCode = 400;
                $this->data = [];
                return;
            }

            $this->status = 'success';
            $this->message = 'Amenity deleted successfully.';
            $this->statusCode = 200;
        } catch (\Exception $e) {
            dd($e);
        }
    }
}
