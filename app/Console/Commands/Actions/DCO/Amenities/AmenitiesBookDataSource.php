<?php

namespace App\Console\Commands\Actions\DCO\Amenities;

use Illuminate\Console\Command;
use App\Console\Commands\Action;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Aws\S3\S3Client;
use Illuminate\Validation\Rule;
use Endroid\QrCode\QrCode;
use Endroid\QrCode\Writer\PngWriter;

class AmenitiesBookDataSource extends Action
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'datasource:amenitiesBook {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Amenities Book';

    /**
     * Execute the console command.
     */
    public function apply()
    {
        try {
            // generate a random 6 digit 
            $pin = rand(100000, 999999);

            // $qr_image = $this->generateAmenitiesQrToS3($pin);

            // verify if amenity exists
            $amenity = $this->tenantDB()->table('chsone_amenities_tpl')->where('amen_tpl_id', $this->input['amenity_id'])->first();
            if (!$amenity) {
                $this->message    = "Amenity not found.";
                $this->status     = "error";
                $this->statusCode = 400;
                return;
            }
            

            $slot = $this->tenantDB()->table('slot_configurations')->where('amenity_id', $this->input['amenity_id'])->first();
            if (!$slot) {
                $this->message    = "Slot not found.";
                $this->status     = "error";
                $this->statusCode = 400;
                return;
            }

            $bookingDate = $this->input['booking_date'] ?? now();
            $bookingDayOfWeek = $this->input['booking_day_of_week'] ?? \Carbon\Carbon::parse($bookingDate)->dayOfWeek;
            $activeDays = isset($slot->active_days) ? array_map('intval', explode(',', $slot->active_days)) : [];
            if (!in_array((int)$bookingDayOfWeek, $activeDays, true)) {
                $this->message    = "Amenity is not open on the selected day.";
                $this->status     = "error";
                $this->statusCode = 400;
                return;
            }

            // Check if booking time is within allowed slot times
            $startTime = $this->input['start_time'] ?? null;
            $endTime = $this->input['end_time'] ?? null;
            if (!$startTime || !$endTime) {
                $this->message    = "Start time and end time are required.";
                $this->status     = "error";
                $this->statusCode = 400;
                return;
            }
            $slotStart = $slot->daily_start_time ?? '00:00:00';
            $slotEnd = $slot->daily_end_time ?? '23:59:59';
            if ($startTime < $slotStart || $endTime > $slotEnd) {
                $this->message    = "Booking time is outside amenity's open hours.";
                $this->status     = "error";
                $this->statusCode = 400;
                return;
            }

            // Check for slot vacancy (no overlapping booking)
            $overlap = $this->tenantDB()->table('amenities_bookings')
                ->where('amenity_id', $this->input['amenity_id'])
                ->whereDate('booking_date', \Carbon\Carbon::parse($bookingDate)->toDateString())
                ->where(function($query) use ($startTime, $endTime) {
                    $query->where(function($q) use ($startTime, $endTime) {
                        $q->where('start_time', '<', $endTime)
                          ->where('end_time', '>', $startTime);
                    });
                })
                ->exists();
            if ($overlap) {
                $this->message    = "The slot is already booked for the selected time.";
                $this->status     = "error";
                $this->statusCode = 409;
                return;
            }


            $amenityData = [
                'amenity_id' => $this->input['amenity_id'] ?? null,
                'user_id' => $this->input['user_id'] ?? null,
                'booking_date' => $bookingDate,
                'start_time' => $startTime,
                'end_time' => $endTime,
                'status' => 'booked',
                'pin' => $pin
            ];



            $slot = $this->tenantDB()->table('amenities_bookings')->insertGetId($amenityData);

            $qr_data = [
                $slot,
                $pin
            ];

            $qr_image = $this->generateAmenitiesQrToS3($qr_data);

            $this->tenantDB()->table('amenities_bookings')->where('id', $slot)->update(['qr_image' => $qr_image]);
            
            if ($slot) {
                $this->message    = "Booking created successfully.";
                $this->status     = "success";
                $this->statusCode = 200;
                $this->data = [];
            }
        } catch (\Exception $e) {
            dd($e);
        }
    }
    
    public function generateAmenitiesQrToS3($pin): ?string
    {
        try {
            $qrContent = json_encode($pin);
            $qrCode    = new QrCode($qrContent);
            $writer    = new PngWriter();
            $image     = $writer->write($qrCode);

            $fileName  = 'qrcodes/amenities_' . ($this->input['amenity_id'] ?? 'x') . '_' . time() . '.png';
            $bucket    = env('AWS_BUCKET');

            // Instantiate the AWS SDK S3 client
            $s3 = new S3Client([
                'version'     => 'latest',
                'region'      => env('AWS_DEFAULT_REGION'),
                'credentials' => [
                    'key'    => env('AWS_ACCESS_KEY_ID'),
                    'secret' => env('AWS_SECRET_ACCESS_KEY'),
                ],
            ]);

           
            // Upload to S3 directly
            $result = $s3->putObject([
                'Bucket'      => $bucket,
                'Key'         => $fileName,
                'Body'        => $image->getString(),
                'ContentType' => 'image/png',
                'ACL'         => 'public-read', // optional, only if you want public access
            ]);

            return $result['ObjectURL'] ?? null;

        } catch (\Exception $e) {
            \Log::error("QR S3 upload failed: " . $e->getMessage());
            return null;
        }
    }

}
