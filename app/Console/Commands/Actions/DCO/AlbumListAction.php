<?php

namespace App\Console\Commands\Actions\DCO;

use Illuminate\Console\Command;
use App\Console\Commands\Action;

class AlbumListAction extends Action
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'datasource:albumsList {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Albums List';

    /**
     * Execute the console command.
     */
    public function apply()
    {
        $obj = $this->tenantDB()->table("photo_album AS album");
        $results = $obj->get()->toArray();
        $this->data = $results;
    }
}
