<?php

namespace App\Console\Commands\Actions\DCO\CommitteeMember;

use Illuminate\Console\Command;
use App\Console\Commands\Action;

class CommitteeDetailsCommitteeMembersDataSource extends Action
{
    protected $signature = 'datasource:committeeDetailsCommitteeMembers {flowId} {parentId} {input}';

    protected $description = 'Committee Details Office Bearer';

    public function apply()
    {
        $committeeResult = array();
        $committeeId = $this->input['id'];
        $objCommittee = $this->tenantDB()->table("chsone_committees as committee")
                             ->where('committee.committee_id', $committeeId)
                             ->first();
        $committeeResult = (array)$objCommittee;

        $objCommitteeDetails = $this->tenantDB()->table("chsone_committee_members as members")
                    ->selectRaw(
                        'CONCAT(member_master.member_first_name," ",member_master.member_last_name) AS member_name,
                        member_master.user_id,
                        member_master.status,
                        members.committee_member_id,
                        members.soc_id,
                        members.fk_member_id AS member_id,
                        members.fk_unit_id,
                        members.designation_name,
                        members.on_board_from,
                        members.end_date,
                        units.soc_building_name,
                        units.unit_flat_number'
                    )
                    ->leftJoin(
                        'chsone_members_master AS member_master',
                        'member_master.id',
                        '=',
                        'members.fk_member_id'
                    )
                    ->leftJoin(
                        'chsone_units_master AS units',
                        'members.fk_unit_id',
                        '=',
                        'units.unit_id'
                    )
                    ->where('members.fk_committee_id', $committeeId)
                    //->where('member_master.status' , 1)
                    ->get();
        $result = $objCommitteeDetails->toArray();
        $data = array();
        $officeBearer = [];
        $committeeMembers = [];

        foreach ($result as $committeeDetails) {
            $committeeDetails = (array)$committeeDetails;
                array_push($committeeMembers, [
                    "member_name" => $committeeDetails['member_name']."(".$committeeDetails['soc_building_name']."-".$committeeDetails['unit_flat_number'].")",
                    "member_id" => $committeeDetails['member_id'],
                    "unit_id" => $committeeDetails['fk_unit_id'],
                    "user_id" => $committeeDetails['user_id'],
                    "user_account_id" => $committeeDetails['user_id'],
                    "designation_name" => $committeeDetails['designation_name']
                ]);
             if (!empty($committeeDetails['designation_name']) || $committeeDetails['designation_name'] == 'Committee Member'){
                $committeeDetails = (array)$committeeDetails;
                array_push($officeBearer, [
                    "ob_member_name" => $committeeDetails['member_name']."(".$committeeDetails['soc_building_name']."-".$committeeDetails['unit_flat_number'].")",
                    "ob_member_id" => $committeeDetails['member_id'],
                    "ob_unit_id" => $committeeDetails['fk_unit_id'],
                    "ob_user_id" => $committeeDetails['user_id'],
                    "ob_user_account_id" => $committeeDetails['user_id'],
                    "ob_designation_name" => $committeeDetails['designation_name']
                ]);
            }
        }
        $this->data = array(
            $officeBearer,
            $committeeMembers
        );
    }
}