<?php

namespace App\Console\Commands\Actions\DCO\CommitteeMember;

use Illuminate\Console\Command;
use App\Console\Commands\Action;

class DissolveCommitteeDataSource extends Action
{
    protected $signature = 'datasource:dissolveCommittee {flowId} {parentId} {input}';

    protected $description = 'Dissolve Committee';

    public function apply()
    {
        $committeeId = $this->input['id'];
        $file_url = $this->input['file_url'];
        $resolution_date = $this->input['resolution_date'];

        $obj = $this->tenantDB()->table("chsone_committees")
            ->where('committee_id', $committeeId)
            ->update([
                'resolution_to_dissolve' => $file_url,
                'resolution_to_dissolve_date' => $resolution_date,
                'dissolve_date' => $resolution_date,
                'updated_date' => date('Y-m-d H:i:s'),
                'updated_by' => $this->input['user_id'] ?? 0
            ]);

        $dissolveCommittee = $this->tenantDB()->table("chsone_committee_members")
            ->where('committee_member_id', $committeeId)
            ->update([
                'end_date' => $resolution_date,
                'updated_date' => date('Y-m-d H:i:s'),
                'updated_by' => $this->input['user_id'] ?? 0
            ]);

        if($dissolveCommittee){
            $this->status = 'success';
            $this->message = 'Committee dissolved successfully';
            $this->statusCode = 200;
        }
        else{
            $this->status = 'error';
            $this->message = 'Failed to dissolve committee';
            $this->statusCode = 400;
        }
    }
}