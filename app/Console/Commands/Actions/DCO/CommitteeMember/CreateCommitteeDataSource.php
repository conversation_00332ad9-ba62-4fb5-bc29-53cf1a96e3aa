<?php

namespace App\Console\Commands\Actions\DCO\CommitteeMember;

use Illuminate\Console\Command;
use App\Console\Commands\Action;

class CreateCommitteeDataSource extends Action
{
    protected $signature = 'datasource:createCommittee {flowId} {parentId} {input}';

    protected $description = 'Create Committee';

    public function apply()
    {
        $committee_type = $this->input['committee_type'];
        $effective_date = $this->input['effective_date'];
        $committee_members = $this->input['committee_members'];

        $obj = $this->tenantDB()->table("chsone_committees")
        ->insertGetId([
            'soc_id' => $this->input['company_id'],
            'parent_id' => $this->input['parent_id'] ?? 0,
            // make committee_name as $committee_type. date with provisional-Apr-2019 format
            'committee_name' => $committee_type.'-'.date('M-Y'),
            'effective_date' => $effective_date,
            'dissolve_date' => '0000-00-00',
            'resolution_of_form_date' => '0000-00-00',
            'resolution_to_dissolve_date' => '0000-00-00',
            'created_date' => date('Y-m-d H:i:s'),
            'created_by' => $this->input['user_id'] ?? 0,
            'updated_date' => date('Y-m-d H:i:s'),
            'updated_by' => $this->input['user_id'] ?? 0
        ]);
        
        $committeeId = $obj;
        
        $members = [];
        foreach($committee_members as $member){
            $unit_id = $this->tenantDB()->table("chsone_members_master")
                ->where('id', $member)
                ->value('fk_unit_id');

            if ($unit_id) {
                $members[] = [
                    'soc_id' => $this->input['company_id'],
                    'fk_committee_id' => $committeeId,
                    'fk_member_id' => $member,
                    'fk_unit_id' => $unit_id,
                    'designation_name' => $member['designation_name'] ?? '',
                    'on_board_from' => $effective_date,
                    'end_date' => '0000-00-00',
                    'created_date' => date('Y-m-d H:i:s'),
                    'created_by' => $this->input['user_id'] ?? 0,
                    'updated_date' => date('Y-m-d H:i:s'),
                    'updated_by' => $this->input['user_id'] ?? 0
                ];
            }
        }

        $committeeMembers = $this->tenantDB()->table("chsone_committee_members")
            ->insert($members);

        if($committeeMembers){
            $this->status = 'success';
            $this->message = 'Committee created successfully';
            $this->statusCode = 200;
        }
        else{
            $this->status = 'error';
            $this->message = 'Failed to create committee';
            $this->statusCode = 400;
        }
    }
}
