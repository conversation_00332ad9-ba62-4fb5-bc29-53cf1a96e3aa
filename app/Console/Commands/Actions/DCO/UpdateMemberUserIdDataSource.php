<?php

namespace App\Console\Commands\Actions\DCO;

use Illuminate\Console\Command;
use App\Console\Commands\Action;

class UpdateMemberUserIdDataSource extends Action
{
    protected $signature = 'datasource:UpdateMemberUserId {flowId} {parentId} {input}';

    protected $description = 'Update Member UserId';

    public function apply()
    {
        $id = $this->input['id'];
        $user_id = $this->input['user_id'] ?? '';

        $obj = $this->tenantDB()->table('chsone_members_master')
            ->where('id', $id)
            ->update([
                'user_id' => $user_id,
                'updated_date' => date('Y-m-d H:i:s'),
                'updated_by' => $this->input['user_id'] ?? 0
            ]);

        if($obj){
            $this->status = 'success';
            $this->message = 'Allottee updated successfully';
            $this->statusCode = 200;
        }else{
            $this->status = 'error';
            $this->message = 'Allottee not updated';
            $this->statusCode = 400;
        }
    }
}