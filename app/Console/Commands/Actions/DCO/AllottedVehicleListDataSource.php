<?php

namespace App\Console\Commands\Actions\DCO;

use Illuminate\Console\Command;
use App\Console\Commands\Action;

class AllottedVehicleListDataSource extends Action
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'datasource:allottedVehicleList {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Allotted Vehicle List';

    protected $formatter = [
        "id" => "",
        "soc_building_name" => "",
        "unit_flat_number" => "",
        "parking_number" => "",
        "allotment_for" => "",
        "building_unit" => "concat:soc_building_name,unit_flat_number,parking_number",
        "parking_unit" => "concat:parking_number,allotment_for",
        "occupied_by" => "",
        "member_primary_name" => "",
        "vehicle_occupied_by" => "concat:member_primary_name,occupied_by",
        "unit_id" => "",
        "vehicle_registration_number" => "",
        "vehicle_type" => "",
        "vehicle_colour" => "",
        "vehicle_model_number" => "",
        "soc_building_name" => "",
        "unit_flat_number" => "",
        "soc_building_floor" => "",
    ];

    protected $formatterByKeys =  ["id"];

    protected $mapper = [
        "id" => "parking_allotment.parking_allotment_id",
        "parking_number" => "parking_allotment.parking_number",
        "unit_flat_number" => "units.unit_flat_number",
        "allotment_for" => "parking_allotment.allotment_for",
        "parking_type" => "parking_allotment.parking_type",
        "vehicle_owned_by" => "vehicle_detail.vehicle_owned_by",
    ];
    /**
     * Execute the console command.
     */
    public function apply()
    {
        $parking_number = '';
        $unit_flat_number = '';
        $parking_type = '';
        $allotment_for = '';
        $occupied_by = null;

        $page = $this->input['page'] ?? 1;
        $per_page = !empty($this->input['per_page']) ? $this->input['per_page'] : ($this->hugeData ? 100000 : 10);
        $offset = ($page - 1) * $per_page;

        if (!empty($this->input['filters'])) {
            $parking_number =
            !empty($this->input['filters']['parking_number']) ? $this->input['filters']['parking_number'] : '';
            unset($this->input['filters']['parking_number']);

            $unit_flat_number =
            !empty($this->input['filters']['unit_flat_number']) ? $this->input['filters']['unit_flat_number'] : '';
            unset($this->input['filters']['unit_flat_number']);

            $allotment_for =
            !empty($this->input['filters']['allotment_for']) ? $this->input['filters']['allotment_for'] : '';
            unset($this->input['filters']['allotment_for']);

            $parking_type =
            !empty($this->input['filters']['parking_type']) ? $this->input['filters']['parking_type'] : '';
            unset($this->input['filters']['parking_type']);

            $occupied_by = !empty($this->input['filters']['occupied_by']) ? strtolower($this->input['filters']['occupied_by']) : '';
            unset($this->input['filters']['occupied_by']);
        }

        $cancelDate = '0000-00-00';
        $currentDate = $this->getCurrentDate('database');

        $obj = $this->tenantDB()
        ->table('chsone_member_vehicle_detail AS vehicle_detail')
        ->selectRaw("
            vehicle_detail.member_vehicle_detail_id AS id,
            vehicle_detail.fk_member_id AS member_id,
            vehicle_detail.vehicle_registration_number,
            vehicle_detail.vehicle_type,
            vehicle_detail.vehicle_colour,
            vehicle_detail.vehicle_model_number,
            vehicle_detail.vehicle_owned_by,
            parking_allotment.parking_number,
            parking_allotment.parking_type,
            parking_allotment.allotment_for,
            units.unit_id,
            units.soc_building_floor,
            units.unit_flat_number,
            units.occupied_by,
            units.occupancy_type,
            units.is_occupied,
            CONCAT(member_master.member_first_name, ' ', member_master.member_last_name) AS member_primary_name,
            soc_bldg_master.soc_building_name
        ")
        ->leftJoin('chsone_members_master AS member', 'vehicle_detail.fk_member_id', '=', 'member.id')
        ->leftJoin('chsone_units_master AS units', 'member.fk_unit_id', '=', 'units.unit_id')
        ->leftJoin('chsone_parking_allotment_detail AS parking_allotment', 'units.unit_id', '=', 'parking_allotment.fk_unit_id')
        ->leftJoin('chsone_societies_building_master AS soc_bldg_master', 'units.soc_building_id', '=', 'soc_bldg_master.soc_building_id')
        ->leftJoin('chsone_members_master AS member_master', 'vehicle_detail.fk_member_id', '=', 'member_master.id')
        ->where('parking_allotment.status', '=', 1) 
        ->where('vehicle_detail.status', '=', 1)
        ->whereNotNull('parking_allotment.parking_number')
        ->where('parking_allotment.parking_number', '!=', '')
        ->groupBy('vehicle_detail.member_vehicle_detail_id');
        
        $obj = $this->filter($obj);

        if ($parking_number) {
            $obj = $obj->whereRaw("LOWER(parking_allotment.parking_number) LIKE '%{$parking_number}%'");
        }

        if ($parking_type) {
            $obj = $obj->whereRaw("LOWER(parking_allotment.parking_type) LIKE '%{$parking_type}%'");
        }

        if ($unit_flat_number) {
            $obj = $obj->whereRaw("LOWER(units.unit_flat_number) LIKE '%{$unit_flat_number}%'");
        }

        if ($allotment_for) {
            $obj = $obj->whereRaw("LOWER(parking_allotment.allotment_for) LIKE '%{$allotment_for}%'");
        }

        if ($occupied_by) {
            $obj = $obj->whereRaw("LOWER(units.occupied_by) = '{$occupied_by}'");
        }
        
        $obj = $this->filter($obj);
        $count = $obj->count();

        $obj = $obj->offset($offset);
        $obj = $obj->limit($per_page);
        $result = $obj->get();
        $this->data = $this->format($result->toArray());
        $this->meta['pagination']['total'] = $count;
    
    }

    public function concat($a, $b, $c=null)
    {
        if($c) {
            return $a . ' / ' . $b;
        }
         elseif($b) {
            $b = ucfirst(str_replace('owner', ' Owner', $b));
            $b = ucfirst(str_replace('tenant', ' Tenant', $b));
            return $a ." ( ". $b . " ) ";
        }
    }
}
