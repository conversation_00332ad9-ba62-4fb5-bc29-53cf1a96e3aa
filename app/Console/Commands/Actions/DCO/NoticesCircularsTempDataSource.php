<?php

namespace App\Console\Commands\Actions\DCO;

use Illuminate\Console\Command;
use App\Console\Commands\Action;

class NoticesCircularsTempDataSource extends Action
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'datasource:NoticesCircularsTempDataSource {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Notices and Circulars Data Source';

    protected $formatter = [
        'id' => '',
        'notice_ref_no' => '',
        'subject' => '',
        'body' => '',
        'type' => '',
    ];

    protected $formatterByKeys = ['id'];

    protected $mapper = [
        'notice_id' => 'id',
    ];

    /**
     * Execute the console command.
     */
    public function apply()
    {
        $obj = $this->tenantDB()->table('chsone_notice_templates')
            ->select('notice_id as id', 'notice_ref_no', 'subject', 'body', 'type')
            ->where('status', 1);
        $obj = $obj->orderBy('id', 'asc');
        $result = $obj->get();
        $this->data = $this->format($result->toArray());
    }
}
