<?php

namespace App\Console\Commands\Actions\DCO\Inventory;

use Illuminate\Console\Command;
use App\Console\Commands\Action;

class AddInventoryCategoryDataSource extends Action
{
    protected $signature = 'datasource:addInventoryCategory {flowId} {parentId} {input}';

    protected $description = 'Add Inventory Category';

    public function apply()
    {
        $inventory_categories_name = $this->input['inventory_categories_name'];
        $company_id = $this->input['company_id'];
        $data = [];
        foreach ($inventory_categories_name as $name) {
            $data[] = [
                'inventory_categories_name' => $name,
                'soc_id' => $company_id,
                'status' => 1,
                'added_on' => date('Y-m-d H:i:s'),
                'updated_on' => date('Y-m-d H:i:s')
            ];
        }

        $obj = $this->tenantDB()->table('chsone_inventory_categories')->insert($data);

        if($obj){
            $this->status = 'success';
            $this->message = 'Inventory category added successfully';
            $this->statusCode = 200;
        }
        else{
            $this->status = 'error';
            $this->message = 'Failed to add inventory category';
            $this->statusCode = 400;
        }
    }
}