<?php

namespace App\Console\Commands\Actions\DCO\Inventory;

use Illuminate\Console\Command;
use App\Console\Commands\Action;

class UpdateInventoryCategoryDataSource extends Action
{
    protected $signature = 'datasource:updateInventoryCategory {flowId} {parentId} {input}';

    protected $description = 'Update Inventory Category';

    public function apply()
    {
        $category_data = $this->input['category_data'];
        $company_id = $this->input['company_id'];

        $data = [];

        foreach ($category_data as $category) {
            $obj = $this->tenantDB()->table('chsone_inventory_categories')
                ->where('inventory_categories_id', $category['id'])
                ->update([
                    'inventory_categories_name' => $category['inventory_categories_name'],
                    'updated_on' => date('Y-m-d H:i:s'),
                ]);
        }

        if($obj){
            $this->status = 'success';
            $this->message = 'Inventory category updated successfully';
            $this->statusCode = 200;
        }
        else{
            $this->status = 'error';
            $this->message = 'Failed to update inventory category';
            $this->statusCode = 400;
        }

    }

}