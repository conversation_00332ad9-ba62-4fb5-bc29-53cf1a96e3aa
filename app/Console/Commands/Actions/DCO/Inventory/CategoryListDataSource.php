<?php

namespace App\Console\Commands\Actions\DCO\Inventory;

use Illuminate\Console\Command;
use App\Console\Commands\Action;

class CategoryListDataSource extends Action
{
    protected $signature = 'datasource:categoryList {flowId} {parentId} {input}';

    protected $description = 'Category List';

    protected $formatter = [
        "id" => "",
        "inventory_categories_name" => ""
    ];

    protected $formatterByKeys = ["id"];

    public function apply()
    {
        $obj = $this->tenantDB()->table('chsone_inventory_categories')
            ->select('inventory_categories_id as id', 'inventory_categories_name')
            ->where('status', 1)
            ->get();
        $this->data = $this->format($obj->toArray());

    }
}