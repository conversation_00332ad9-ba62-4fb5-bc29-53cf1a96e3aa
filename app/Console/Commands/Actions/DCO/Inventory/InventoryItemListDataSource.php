<?php

namespace App\Console\Commands\Actions\DCO\Inventory;

use Illuminate\Console\Command;
use App\Console\Commands\Action;
use Illuminate\Support\Facades\Route;

class InventoryItemListDataSource extends Action
{
    protected $signature = 'datasource:inventoryItemList {flowId} {parentId} {input}';

    protected $description = 'List the Inventory Items';

    protected $formatter = [
        "id" => "",
        "inven_items_name" => "",
        "inventory_categories_name" => "",
        "inven_items_quantity" => "",
        "added_on" => "",
        "inven_items_cost" => "calculate:inven_items_quantity,inven_items_netcost",
        "status" => ""
    ];

    protected $formatterByKeys = ["id"];

    public function apply()
    {
        $id = $this->input['id'] ?? null;
    
        $query = $this->tenantDB()->table('chsone_inventory_items as items')
            ->select(
                'items.inven_items_id as id',
                'items.inven_items_name',
                'category.inventory_categories_name',
                'items.inven_items_quantity',
                'items.inven_items_netcost',
                'items.added_on',
                'items.status'
            )
            ->leftJoin('chsone_inventory_categories as category', 'items.inven_items_category_id', '=', 'category.inventory_categories_id')
            ->where('items.status', 1);
    
        if (!empty($id)) {
            // When ID is provided, get a single object instead of an array
            $result = $query->where('items.inven_items_id', $id)->first();

            if ($result) {
                // Format a single object
                $this->data = $this->format([$result])[0] ?? null;
            } else {
                $this->data = null;
            }
        } else {
            // For listing all items, keep the original behavior
            $results = $query->get();
            $this->data = $this->format($results);
        }
    }


    public function calculate($quantity, $netcost)
    {
        return (int)$quantity * (int)$netcost;
    }
}