<?php

namespace App\Console\Commands\Actions\DCO\Inventory;

use Illuminate\Console\Command;
use App\Console\Commands\Action;

class InwardInventoryDataSource extends Action
{
    protected $signature = 'datasource:inwardInventory {flowId} {parentId} {input}';

    protected $description = 'Inward Inventory';

    public function apply()
    {
        $inventory_item_id = $this->input['inventory_item_id'];
        $user_id = $this->input['user_id'];
        $inventory_txns_quantity = $this->input['inventory_txns_quantity'];
        $inventory_txns_comments = $this->input['inventory_txns_comments'];
        $inventory_txns_date = $this->input['inventory_txns_date'];
        $inventory_txns_cost = $this->input['inventory_txns_cost'];
        $location = $this->input['location'];
        $transaction_by = $this->input['transaction_by'];
        $ledger_account_id = $this->input['ledger_account_id'];

        $item = $this->tenantDB()->table('chsone_inventory_items')
            ->where('inven_items_id', $inventory_item_id)
            ->first();

        $this->tenantDB()->table('chsone_inventory_items')
            ->where('inven_items_id', $inventory_item_id)
            ->update([
                'inven_items_quantity' => $item->inven_items_quantity + $inventory_txns_quantity,
                'inven_items_netcost' => $item->inven_items_netcost + $inventory_txns_cost,
                'updated_on' => date('Y-m-d')
            ]);

        $ledger_account = $obj = $this->tenantDB()->table('chsone_grp_ledger_tree')
            ->where('ledger_account_id', $ledger_account_id)
            ->first();

        $obj = $this->tenantDB()->table('chsone_inventory_transactions')
            ->insert([
                'inventory_categories_id' => $item->inven_items_category_id,
                'inventory_item_id' => $inventory_item_id,
                'soc_id' => $this->input['company_id'],
                'user_id' => $user_id,
                'user_name' => $ledger_account->ledger_account_name,
                'inventory_txns_quantity' => $inventory_txns_quantity,
                'inventory_txns_comments' => $inventory_txns_comments,
                'inventory_txns_date' => $inventory_txns_date,
                'inventory_txns_mode' => 'add',
                'inventory_txns_cost' => $inventory_txns_cost,
                'location' => $location,
                'transaction_by' => $transaction_by,
                'added_on' => date('Y-m-d H:i:s'),
                'updated_on' => date('Y-m-d H:i:s'),
                'status' => 1
            ]);

        $ledger_first_entry = $this->tenantDB()->table('chsone_ledger_transactions')
            ->insertGetId([
                'soc_id' => $this->input['company_id'],
                'ledger_account_id' => '',
                'ledger_account_name' => '',
                'voucher_type' => '',
                'voucher_reference_number' => '',
                'voucher_reference_id' => 0,
                'transaction_type' => 'dr',
                'payment_mode' => '',
                'payment_reference' => '',
                'transaction_amount' => $inventory_txns_cost,
                'other_reference_id' => '',
                'txn_from_id' => 0,
                'transaction_date' => $inventory_txns_date,
                'memo_desc' => 'Stock purchase of '.$item->inven_items_name.' of '. $inventory_txns_quantity .'('.$item->inven_items_unit.') on '.$inventory_txns_date,
                'is_opening_balance' => 1,
                'is_reconciled' => 0,
                'is_cancelled' => 0,
                'created_by' => $this->input['created_by'] ?? 0,
                'added_on' => date('Y-m-d H:i:s'),
                'value_date' => $inventory_txns_date,
            ]);

        $ledger_second_entry = $this->tenantDB()->table('chsone_ledger_transactions')
            ->insertGetId([
                'soc_id' => $this->input['company_id'],
                'ledger_account_id' => $ledger_account_id,
                'ledger_account_name' => $ledger_account->ledger_account_name,
                'voucher_type' => '',
                'voucher_reference_number' => '',
                'voucher_reference_id' => 0,
                'transaction_type' => 'cr',
                'payment_mode' => '',
                'payment_reference' => '',
                'transaction_amount' => $inventory_txns_cost,
                'other_reference_id' => '',
                'txn_from_id' => $ledger_first_entry,
                'transaction_date' => $inventory_txns_date,
                'memo_desc' => 'Stock purchase of '.$item->inven_items_name.' of '. $inventory_txns_quantity .'('.$item->inven_items_unit.') on '.$inventory_txns_date,
                'is_opening_balance' => 1,
                'is_reconciled' => 0,
                'is_cancelled' => 0,
                'created_by' => $this->input['created_by'] ?? 0,
                'added_on' => date('Y-m-d H:i:s'),
                'value_date' => $inventory_txns_date,
            ]);

        $this->data = [];
        $this->status = 'success';
        $this->message = 'Inward Inventory added successfully';
        $this->statusCode = 200;
    }
}