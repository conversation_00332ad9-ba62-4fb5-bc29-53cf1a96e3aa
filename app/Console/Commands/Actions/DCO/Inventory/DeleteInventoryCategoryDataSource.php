<?php

namespace App\Console\Commands\Actions\DCO\Inventory;

use Illuminate\Console\Command;
use App\Console\Commands\Action;

class DeleteInventoryCategoryDataSource extends Action
{
    protected $signature = "datasource:deleteInventoryCategory {flowId} {parentId} {input}";

    protected $description = "Delete Inventory Category";

    public function apply()
    {
        $id = $this->input['id'];

        $obj = $this->tenantDB()->table('chsone_inventory_categories')->where('inventory_categories_id', $id)->delete();

        if($obj){
            $this->status = 'success';
            $this->message = 'Inventory Category deleted successfully';
            $this->statusCode = 200;
            $this->data = [];
        }
        else{
            $this->status = 'error';
            $this->message = 'Inventory Category not found';
            $this->statusCode = 400;
            $this->data = [];
        }
    }
}