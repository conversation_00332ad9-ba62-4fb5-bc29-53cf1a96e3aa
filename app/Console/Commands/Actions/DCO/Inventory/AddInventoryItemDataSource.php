<?php

namespace App\Console\Commands\Actions\DCO\Inventory;

use Illuminate\Console\Command;
use App\Console\Commands\Action;

class AddInventoryItemDataSource extends Action
{
    protected $signature = 'datasource:addInventoryItem {flowId} {parentId} {input}';

    protected $description = 'Add Inventory Item';

    public function apply()
    {
        $company_id = $this->input['company_id'];
        $inven_items_category_id = $this->input['inven_items_category_id'];
        $inven_items_name = $this->input['inven_items_name'];
        $inven_items_unit = $this->input['inven_items_unit'];
        $inven_led_id = $this->input['inven_led_id'];

        $obj = $this->tenantDB()->table('chsone_inventory_items')
            ->insert([
                'soc_id' => $company_id,
                'inven_items_category_id' => $inven_items_category_id,
                'inven_items_name' => $inven_items_name,
                'inven_items_unit' => $inven_items_unit,
                'inven_items_quantity' => 0,
                'inven_items_netcost' => 0,
                'inven_led_id' => $inven_led_id,
                'added_on' => date('Y-m-d'),
                'updated_on' => date('Y-m-d'),
                'status' => 1
            ]);

            if($obj){
                $this->status = 'success';
                $this->message = 'Inventory item added successfully';
                $this->statusCode = 200;
            }
            else{
                $this->status = 'error';
                $this->message = 'Failed to add inventory item';
                $this->statusCode = 400;
            }
    }
}