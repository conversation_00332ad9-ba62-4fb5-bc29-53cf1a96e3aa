<?php

namespace App\Console\Commands\Actions\DCO\Inventory;

use Illuminate\Console\Command;
use App\Console\Commands\Action;

class InventoryTransactionDataSource extends Action
{
    protected $signature = 'datasource:inventoryTransaction {flowId} {parentId} {input}';

    protected $description = 'Inventory Transaction';

    public function apply()
    {
        $id = $this->input['id'];
        $currentTab = $this->input['current_tab'] ?? 'Inward';

        if ($currentTab == 'Inward') {
            $obj = $this->tenantDB()->table('chsone_inventory_transactions')
                ->where('inventory_item_id', $id)
                ->where('inventory_txns_mode', 'add')
                ->orderBy('inventory_txns_date', 'desc')
                ->get();
        } else {
            $obj = $this->tenantDB()->table('chsone_inventory_transactions')
                ->where('inventory_item_id', $id)
                ->where('inventory_txns_mode', 'remove')
                ->orderBy('inventory_txns_date', 'desc')
                ->get();
        }

        $this->data = $obj;
    }
}