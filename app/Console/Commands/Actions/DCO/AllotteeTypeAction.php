<?php

namespace App\Console\Commands\Actions\DCO;

use Illuminate\Console\Command;
use App\Console\Commands\Action;

class AllotteeTypeAction extends Action
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'datasource:allotteeType {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Allottee Type List';

    /**
     * Execute the console command.
     */
    public function apply()
    {
        // $obj = $this->tenantDB()->table("chsone_member_type_master AS member_type")->where("status", 1)->orderBy('member_type_id');
        // $results = $obj->get()->toArray();
        // $this->data = $results;

        $obj = $this->tenantDB()->table('chsone_member_type_master')
            ->select('member_type_id as id', 'soc_id', 'member_type_name', 'defined_by', 'status')
            ->where('status', 1)
            ->orderBy('member_type_id', 'asc');

        $obj = $this->filter($obj);

        $results = $obj->get();

        $this->data = $results;
    }
}
