<?php

namespace App\Console\Commands\Actions\DCO;

use App\Console\Commands\Action;
use Exception;

class unlinkUserDataSource extends Action
{
    protected $signature = 'datasource:unlinkUser {flowId} {parentId} {input}';
    protected $description = 'Datasource to unlink a user from a member';

    public function apply()
    {
        $memberId = $this->input['id'] ?? null;
        $socId = $this->input['company_id'] ?? null;

        if (!$memberId || !$socId) {
            $this->status = 'error';
            $this->message = 'Member ID and Society ID are required';
            $this->statusCode = 400;
            $this->data = [];
            return;
        }

        try {
            $member = $this->tenantDB()->table('chsone_members_master')
                ->where('soc_id', $socId)
                ->where('id', $memberId)
                ->first();

            if (!$member) {
                $this->status = 'error';
                $this->message = 'Member not found';
                $this->statusCode = 404;
                $this->data = [];
                return;
            }

            $affected = $this->tenantDB()->table('chsone_members_master')
                ->where('soc_id', $socId)
                ->where('id', $memberId)
                ->update([
                    'user_id' => null,
                    'is_user_created' => 0,
                    'user_account_id' => null,
                    'updated_date' => now(),
                ]);

            if ($affected) {
                $this->status = 'success';
                $this->message = 'Member has been unlinked from the user';
                $this->statusCode = 200;
                $this->data = ['member_id' => $memberId];
            } else {
                $this->status = 'error';
                $this->message = 'Failed to unlink member';
                $this->statusCode = 400;
                $this->data = [];
            }
        } catch (Exception $e) {
            $this->status = 'error';
            $this->message = $e->getMessage();
            $this->statusCode = 500;
            $this->data = [];
        }
    }
} 