<?php

namespace App\Console\Commands\Actions\DCO\Member;

use Illuminate\Console\Command;
use App\Console\Commands\Action;
use GuzzleHttp\Client;
use Illuminate\Support\Facades\DB;

class SendInvitationDataSource extends Action
{
    protected $signature = 'datasource:sendInvitation {flowId} {parentId} {input}';

    protected $description = 'Send Invitation to Member Data Source';

    public function apply()
    {
        $id = $this->input['id'];
        $company_id = $this->input['company_id'];
        $user_id = $this->input['user_id'] ?? null;

        // Get member details with unit information
        $memberInfo = $this->tenantDB()->table('chsone_members_master as members')
            ->select(
                'members.id',
                'members.member_first_name',
                'members.member_last_name',
                'members.member_mobile_number',
                'members.member_email_id',
                'members.fk_unit_id as unit_id',
                'units.unit_flat_number',
                'units.soc_building_name',
                'units.soc_building_floor'
            )
            ->leftJoin('chsone_units_master as units', 'units.unit_id', '=', 'members.fk_unit_id')
            ->where('members.id', $id)
            ->where('members.soc_id', $company_id)
            ->first();

        if (!$memberInfo) {
            $this->status = 'error';
            $this->message = 'Member not found';
            $this->statusCode = 404;
            $this->data = [];
            return;
        }

        // Format mobile number
        $mobile_number = $memberInfo->member_mobile_number;
        if (strlen($mobile_number) == 10) {
            $mobile_number = '91' . $mobile_number;
        }

        // Prepare SSO invitation data
        $ssoData = [
            'auth_server_api_key' => config('services.sso.server_api_key'),
            'app_id' => config('services.sso.app_id'),
            'company_id' => $company_id,
            'first_name' => $memberInfo->member_first_name,
            'contact' => $mobile_number ?: $memberInfo->member_email_id,
            'invited_by' => $user_id,
            'invite_role' => 'member',
            'unit_id' => $memberInfo->unit_id,
            'unit_number' => ucfirst($memberInfo->unit_flat_number),
            'building_name' => ucfirst($memberInfo->soc_building_name),
            'floor_no' => $memberInfo->soc_building_floor,
            'member_type' => 'owner',
            'vendor_ref_id' => $memberInfo->id,
        ];

        try {
            // Make HTTP request to SSO service
            $client = new Client();
            $ssoUrl = config('services.sso.url') . '/api/v2/apps/user/invite';
            
            $response = $client->post($ssoUrl, [
                'json' => $ssoData,
                'timeout' => 30,
            ]);

            $responseData = json_decode($response->getBody()->getContents(), true);
            $statusCode = $response->getStatusCode();

            if ($statusCode == 200 || $statusCode == 201) {
                // Update member status to 'invited'
                $this->tenantDB()->table('chsone_members_master')
                    ->where('id', $id)
                    ->where('soc_id', $company_id)
                    ->update(['member_status' => 'invited']);

                $this->status = 'success';
                $this->message = 'User invited successfully';
                $this->statusCode = 200;
                $this->data = [
                    'member_id' => $id,
                    'member_name' => $memberInfo->member_first_name . ' ' . $memberInfo->member_last_name,
                    'invitation_status' => 'sent',
                    'sso_response' => $responseData
                ];
            } else {
                $this->status = 'error';
                $this->message = $responseData['message'] ?? 'Failed to send invitation';
                $this->statusCode = $statusCode;
                $this->data = [];
            }

        } catch (\GuzzleHttp\Exception\RequestException $e) {
            $this->status = 'error';
            $this->message = 'Failed to connect to SSO service: ' . $e->getMessage();
            $this->statusCode = 500;
            $this->data = [];
        } catch (\Exception $e) {
            $this->status = 'error';
            $this->message = 'Something went wrong: ' . $e->getMessage();
            $this->statusCode = 500;
            $this->data = [];
        }
    }
}
