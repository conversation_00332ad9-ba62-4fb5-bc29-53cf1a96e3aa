<?php

namespace App\Console\Commands\Actions\DCO;

use Illuminate\Console\Command;
use App\Console\Commands\Action;

class ParkingListAction extends Action
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'datasource:parkingList {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Get Parking List';
    // protected $formatter = [
    //     "id" => "",
    //     "vehicle_owned_by" => "",
    //     "parking_allotment_id" => "",
    //     "parking_number" => "",
    //     "unit_id" => "",
    //     "member_name" => "",
    //     "badge_number" => "",
    //     "vehicle_registration_number" => "",
    //     "vehicle_type" => "",
    //     "is_alloted" => "",
    //     "member_vehicle_detail_id" => "",
    //     "soc_building_name" => "",
    //     "unit_flat_number" => "",
    //     "vehicle_company" => "",
    //     "vehicle_model_number" => "",
    //     "vehicle_colour" => "",
    // ];
    // protected $formatterByKeys =  ["id"];
    // protected $mapper = [
    //     "id" => "members.id",
    //     "memberName" => "members.member_first_name",
    //     "badgeNumber" => "member_vehicles.badge_number",
    //     "vehicleType" => "member_vehicles.vehicle_type",
    //     "vehicleRegNumber" => "member_vehicles.vehicle_registration_number",
    // ];

    /**
     * Execute the console command.
     */
    public function apply()
    {
        $memberName = '';
        $badgeNumber = '';
        $vehicleType = '';
        $vehicleRegNumber = '';
        
        if (!empty($this->input['filters'])) {
            $memberName =
            !empty($this->input['filters']['memberName']) ? $this->input['filters']['memberName'] : '';
            unset($this->input['filters']['memberName']);

            $badgeNumber =
            !empty($this->input['filters']['badgeNumber']) ? $this->input['filters']['badgeNumber'] : '';
            unset($this->input['filters']['badgeNumber']);

            $vehicleType =
            !empty($this->input['filters']['vehicleType']) ? $this->input['filters']['vehicleType'] : '';
            unset($this->input['filters']['vehicleType']);

            $vehicleRegNumber =
            !empty($this->input['filters']['vehicleRegNumber']) ? $this->input['filters']['vehicleRegNumber'] : '';
            unset($this->input['filters']['vehicleRegNumber']);
        }

        $cancelDate = '0000-00-00';
        $currentDate = $this->getCurrentDate('database');
        $obj = $this->tenantDB()->table("chsone_member_vehicle_detail AS member_vehicles")
        ->selectRaw(
            'member_vehicles.member_vehicle_detail_id,
            member_vehicles.fk_member_id AS member_id,
            CONCAT(members.member_first_name,\' \',members.member_last_name) AS member_name,
            member_vehicles.fk_parking_allotment_id AS parking_allotment_id,
            parking_details.fk_unit_id AS fk_unit_id,
            parking_details.parking_number AS parking_number,
            member_vehicles.fk_parking_unit_id AS alloted_parking_unit_id,
            units.soc_building_name AS soc_building_name,
            units.soc_building_id AS soc_building_id,
            units.unit_id AS unit_id,
            units.unit_category AS unit_category,
            units.unit_type AS unit_type,
            units.unit_flat_number AS unit_flat_number,
            units.is_allotted AS is_alloted,
            IF(units.is_allotted = 1, \'yes\', \'no\') AS is_alloted,
            member_vehicles.badge_number AS badge_number,
            member_vehicles.vehicle_registration_number AS vehicle_registration_number,
            member_vehicles.effective_date AS effective_date,
            member_vehicles.vehicle_type AS vehicle_type,
            member_vehicles.vehicle_company AS vehicle_company,
            member_vehicles.vehicle_model_number AS vehicle_model_number,
            CONCAT(member_vehicles.vehicle_company, \' \', member_vehicles.vehicle_model_number) AS make,
            member_vehicles.vehicle_colour AS vehicle_colour,
            member_vehicles.vehicle_registration_type AS vehicle_registration_type,
            member_vehicles.vehicle_owned_by AS vehicle_owned_by,
            member_vehicles.status AS status'
        )
        ->leftJoin('chsone_members_master AS members', 'members.id', '=', 'member_vehicles.fk_member_id')
        ->leftJoin('chsone_parking_allotment_detail AS parking_details', 'parking_details.parking_allotment_id', '=', 'member_vehicles.fk_parking_allotment_id')
        ->leftJoin('chsone_units_master AS units', 'units.unit_id', '=', 'member_vehicles.fk_parking_unit_id')
        ->whereRaw('units.effective_date <= "'.$currentDate.'" AND ((units.cancel_date = "'.$cancelDate.'") OR (members.cancel_date >= "'.$currentDate.'"))')
        ->where('members.status', '1')
        ->where('member_vehicles.status', '1');

        if ($memberName) {
            $obj = $obj->whereRaw("CONCAT(LOWER(members.member_first_name), ' / ',
                LOWER(members.member_last_name)) LIKE '%{$memberName}%'");
        }

        if ($vehicleType) {
            dd($vehicleType);
            $obj = $obj->whereRaw("LOWER(member_vehicles.vehicle_type) LIKE '%{$vehicleType}%'");
        }

        if ($badgeNumber) {
            $obj = $obj->whereRaw("LOWER(member_vehicles.badge_number) LIKE '%{$badgeNumber}%'");
        }

        if ($vehicleRegNumber) {
            $obj = $obj->whereRaw("LOWER(member_vehicles.vehicle_registration_number) LIKE '%{$vehicleRegNumber}%'");
        }

        if (isset($this->input['filters']['is_alloted']) && $this->input['filters']['is_alloted'] == 'yes') {
            $isAlloted = 1;
            $obj = $obj->whereRaw("LOWER(units.is_allotted) LIKE '%{$isAlloted}%'");
        }

        if (isset($this->input['filters']['is_alloted']) && $this->input['filters']['is_alloted'] == 'no') {
            $isAlloted = 0;
            $obj = $obj->whereRaw("LOWER(units.is_allotted) LIKE '%{$isAlloted}%'");
        }

        $obj = $this->filter($obj);
        $result = $obj->get();
        
        $arrFinalArray = [];
        // Get unique fk_unit_ids
        $uniqueUnitIds = collect($result)->pluck('fk_unit_id')->unique()->filter()->values();

        foreach ($uniqueUnitIds as $unitId) {
            $unit = $this->tenantDB()->table('chsone_units_master')
                ->select('unit_id', 'soc_building_name', 'unit_flat_number')
                ->where('unit_id', $unitId)
                ->first();
            
            if ($unit) {
                // Find the index where this unit should be added
                $unitIndex = count($arrFinalArray);
                
                // Add the unit level data
                $arrFinalArray[$unitIndex] = [
                    'id' => $unit->unit_id,
                    'soc_building_name' => $unit->soc_building_name,
                    'unit_flat_number' => $unit->unit_flat_number,
                    'member_name' => $unit->soc_building_name . '-' . $unit->unit_flat_number,
                    'disable' => 1,
                    'rows' => []
                ];

                // Get all vehicles for this unit
                $unitVehicles = collect($result)->filter(function($item) use ($unitId) {
                    return $item->fk_unit_id == $unitId;
                });

                // Add each vehicle as a row
                foreach ($unitVehicles as $vehicle) {
                    $arrFinalArray[$unitIndex]['rows'][] = [
                        'disable' => 0,
                        'id' => $vehicle->member_vehicle_detail_id+1,
                        'member_name' => $vehicle->member_name,
                        'parking_allotment_id' => $vehicle->parking_allotment_id,
                        'parking_number' => $vehicle->parking_number,
                        'unit_id' => $vehicle->unit_id,
                        'badge_number' => $vehicle->badge_number,
                        'vehicle_registration_number' => $vehicle->vehicle_registration_number,
                        'vehicle_type' => $vehicle->vehicle_type,
                        'is_alloted' => $vehicle->is_alloted,
                        'soc_building_name' => $vehicle->soc_building_name,
                        'unit_flat_number' => $vehicle->unit_flat_number,
                        'vehicle_company' => $vehicle->vehicle_company,
                        'vehicle_model_number' => $vehicle->vehicle_model_number,
                        'make' => $vehicle->make,
                        'vehicle_colour' => $vehicle->vehicle_colour,
                        'vehicle_owned_by' => $vehicle->vehicle_owned_by,
                        'effective_date' => $vehicle->effective_date,
                        'status' => $vehicle->status,
                    ];
                }
            }
        }

        $this->data = $arrFinalArray;
    }

    public function concat($a, $b)
    {
        return $a . ' ' . $b;
    }
}


