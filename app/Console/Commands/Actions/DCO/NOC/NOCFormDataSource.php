<?php

namespace App\Console\Commands\Actions\DCO\NOC;

use Illuminate\Console\Command;
use App\Console\Commands\Action;

class NOCFormDataSource extends Action
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'datasource:NOCForm {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Action for NOC Form Data Source';

    protected $formatter = [
        "id" => "",
        "purpose" => "",
        "status" => "",
        "dues" => "",
        "unit" => "concat:soc_building_name,unit_flat_number"
    ];

    protected $formatterByKeys =  ['id'];

    public function apply()
    {
        
        $requests = $this->tenantDB()->table('chsone_noc_requests as noc')
            ->select('noc.noc_request_id as id', 'noc.*', 'unit.soc_building_name', 'unit.unit_flat_number')
            ->leftJoin('chsone_units_master as unit', 'noc.unit_id', '=', 'unit.unit_id')
            ->orderBy('noc_request_id', 'desc')
            ->get();

            foreach($requests as $request) {
                $income_unit_invoice = $this->tenantDB()->table('income_unit_invoices')
                ->select('*')
                ->where('fk_unit_id', $request->unit_id)
                ->whereIn('payment_status', ['unpaid', 'partialpaid'])
                ->orderBy('unit_invoice_id', 'desc')
                ->first();
                
                $particular_amount = $this->tenantDB()->table('income_invoice_particular')
                ->selectRaw('SUM(amount) as total_amount')
                ->where('fk_unit_invoice_id', $income_unit_invoice->unit_invoice_id ?? null)
                ->pluck('total_amount')
                ->first();
                
                $total_amount = $particular_amount + ($income_unit_invoice->outstanding_principal ?? 0) + ($income_unit_invoice->interest_amount ?? 0) + ($income_unit_invoice->outstanding_interest ?? 0) + ($income_unit_invoice->roundoff_amount ?? 0) - ($income_unit_invoice->advance_amount ?? 0);
                $total_amount = round($total_amount, 2);
                
                $request->dues = $total_amount;
            }

        $this->data = $this->format($requests->toArray());

    }

    public function concat($a, $b)
    {
        return $a . "-" . $b;
    }
}
