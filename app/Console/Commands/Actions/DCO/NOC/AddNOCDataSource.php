<?php

namespace App\Console\Commands\Actions\DCO\NOC;

use Illuminate\Console\Command;
use App\Console\Commands\Action;

class AddNOCDataSource extends Action
{
    protected $signature = 'datasource:addNOC {flowId} {parentId} {input}';

    protected $description = 'Add NOC DataSource';

    public function apply()
    {
        $is_header_attached = $this->input['is_header_attached'];
        $unit_id = $this->input['unit_id'];
        $status = $this->input['status'] ?? 1;
        $purpose = $this->input['purpose'];
        $user_id = $this->input['user_id'];
        $society_name = $this->input['society_name'];
        $society_registration_no = $this->input['society_registration_no'];
        $address_line_1 = $this->input['address_line_1'];
        $address_line_2 = $this->input['address_line_2'];
        $letter_date = $this->input['letter_date'];
        $aaplier_name = $this->input['aaplier_name'] ?? null    ;
        $relation_with_owner = $this->input['relation_with_owner'] ?? null;
        $owner_name = $this->input['owner_name'] ?? null;
        $owner_address = $this->input['owner_address'] ?? null;
        $since_staying_date = $this->input['since_staying_date'] ?? null;
        $HIM_HER = $this->input['HIM_HER'] ?? null;
        $HIS_HER = $this->input['HIS_HER'] ?? null;

        $obj = $this->tenantDB()->table('chsone_noc_requests')
            ->insert([
                'unit_id' => $unit_id,
                'purpose' => $purpose,
                'status' => $status,
                'is_header_attached' => $is_header_attached,
                'user_id' => $user_id,
                'soc_id' => $this->input['company_id']
            ]);

        if($obj) {
            $this->status = 'success';
            $this->message = 'NOC added successfully';
            $this->statusCode = 200;
        }
        else {
            $this->status = 'error';
            $this->message = 'NOC not added';
            $this->statusCode = 400;
        }
    }
}