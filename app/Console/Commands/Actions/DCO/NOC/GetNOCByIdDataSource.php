<?php

namespace App\Console\Commands\Actions\DCO\NOC;

use Illuminate\Console\Command;
use App\Console\Commands\Action;

class GetNOCByIdDataSource extends Action
{
    protected $signature = "datasource:getNOCById {flowId} {parentId} {input}";

    protected $description = "Get NOC By Id DataSource";

    public function apply()
    {
        $id = $this->input['id'];

        $obj = $this->tenantDB()
            ->table('chsone_noc_requests')
            ->where('noc_request_id', $id)
            ->first();

        if($obj) {
            $obj->purpose = (int) $obj->purpose;

            $this->status = 'success';
            $this->message = 'NOC found';
            $this->statusCode = 200;
            $this->data = $obj;
        }
        else {
            $this->status = 'error';
            $this->message = 'NOC not found';
            $this->statusCode = 404;
            $this->data = [];
        }
    }
}