<?php

namespace App\Console\Commands\Actions\DCO\NOC;

use Illuminate\Console\Command;
use App\Console\Commands\Action;

class UpdateNOCDataSource extends Action
{
    protected $signature = "datasource:updateNOC {flowId} {parentId} {input}";

    protected $description = "Action for Update NOC Data Source";

    public function apply()
    {
        $id = $this->input['id'];
        $is_header_attached = $this->input['is_header_attached'];
        $status = $this->input['status'];
        $preview = $this->input['preview'];

        $obj = $this->tenantDB()->table('chsone_noc_requests')
            ->where('noc_request_id', $id)
            ->update([
                'is_header_attached' => $is_header_attached,
                'status' => $status,
                'preview' => $preview
            ]);

        if ($obj) {
            $this->status = 'success';
            $this->message = 'NOC updated successfully';
            $this->statusCode = 200;
            $this->data = [];
        } else {
            $this->status = 'error';
            $this->message = 'NOC not updated';
            $this->statusCode = 400;
            $this->data = [];
        }
    }

}