<?php

namespace App\Console\Commands\Actions\DCO\ParkingAllotment;

use Illuminate\Console\Command;
use App\Console\Commands\Action;

class UpdateAllocateParkingDataSource extends Action
{
    protected $signature = 'datasource:updateAllocateParking {flowId} {parentId} {input}';

    protected $description = 'Update parking allotment';

    public function apply()
    {
        $id = $this->input['id'];

        // check $id is a number or not then return error
        if(!is_numeric($id)) {
            $this->status = 'error';
            $this->message = 'Please provide a valid parking allotment id and it should be a number.';
            $this->statusCode = 400;
            $this->data = [];
            return;
        }

        // check if the id is valid or not and if not then return error
        $parkingDetails = $this->tenantDB()->table('chsone_parking_allotment_detail')
            ->where('parking_allotment_id', $this->input['id'])
            ->first();
        
        if(!$parkingDetails){
            $this->status = 'error';
            $this->message = 'Please provide a valid id OR No record found for this id';
            $this->statusCode = 400;
            $this->data = [];
            return;
        }

        // check fk_unit_id is valid or not and if not then return error
        $unitDetails = $this->tenantDB()->table('chsone_units_master')
            ->where('unit_id', $this->input['fk_unit_id'])
            ->first();

        if(!$unitDetails){
            $this->status = 'error';
            $this->message = 'Please provide a valid unit id OR No record found for this unit id';
            $this->statusCode = 400;
            $this->data = [];
            return;
        }

        // check fk_parking_unit_id is valid or not and if not then return error
        $parkingUnitDetails = $this->tenantDB()->table('chsone_units_master')
            ->where('unit_id', $this->input['fk_parking_unit_id'])
            ->first();

        if(!$parkingUnitDetails){
            $this->status = 'error';
            $this->message = 'Please provide a valid parking unit id OR No record found for this parking unit id';
            $this->statusCode = 400;
            $this->data = [];
            return;
        }

        $id = $this->input['id'];
        $unitId = $this->input['fk_unit_id'];
        $societyId = $this->input['company_id'];
        $parkingUnitId = $this->input['fk_parking_unit_id'];
        $effectiveDate = $this->input['effective_date'];
        $parkingType = $this->input['parking_type'];
        $parkingNumber = $this->input['parking_number'];
        $cancelDate = $this->input['cancel_date'] ?? "0000-00-00";
        $allotmentFor = $this->input['allotment_for'] ?? "";
        $allowedParking = $this->input['allowed_number_of_parkings'] ?? "";
        $updated_date = date('Y-m-d H:i:s');


        $result = $this->tenantDB()->table('chsone_parking_allotment_detail')
            ->where('parking_allotment_id', $id)
            ->update([
                'fk_unit_id' => $unitId,
                'soc_id' => $societyId,
                'fk_parking_unit_id' => $parkingUnitId,
                'effective_date' => $effectiveDate,
                'parking_type' => $parkingType,
                'parking_number' => $parkingNumber,
                'cancel_date' => $cancelDate,
                'allotment_for' => $allotmentFor,
                'allowed_number_of_parkings' => $allowedParking,
                'updated_date' => $updated_date
            ]);

        if($result){
            $this->status = 'success';
            $this->message = 'Parking allotment done successfully w.e.f. ' . $effectiveDate;
            $this->statusCode = 200;
            $this->data = [];
        }
        else{
            $this->status = 'error';
            $this->message = 'Error in updating parking allotment';
            $this->statusCode = 400;
            $this->data = [];
        }

    }
}