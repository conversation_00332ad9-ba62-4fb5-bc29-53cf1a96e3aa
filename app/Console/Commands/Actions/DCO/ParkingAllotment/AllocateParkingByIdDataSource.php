<?php

namespace App\Console\Commands\Actions\DCO\ParkingAllotment;

use Illuminate\Console\Command;
use App\Console\Commands\Action;

class AllocateParkingByIdDataSource extends Action
{
    protected $signature = 'datasource:allocateParkingById {flowId} {parentId} {input}';
    
    protected $description = 'Parking Allotment by id';

    public function apply()
    {
        $id = $this->input['id'];

        // check if id is exists or not from chsone_parking_allotment_detail table
        $CheckExist = $this->tenantDB()->table('chsone_parking_allotment_detail')
            ->where('parking_allotment_id', $id)
            ->exists();

        if(!$CheckExist)
        {
            $this->status = 'error';
            $this->message = 'Please provide a valid id';
            $this->statusCode = 400;
            $this->data = [];
            return;
        }

        $result = $this->tenantDB()->table('chsone_parking_allotment_detail')
            ->select('parking_allotment_id','soc_id','fk_unit_id','fk_parking_unit_id','parking_number','parking_type','allotment_for','allowed_number_of_parkings','actual_number_of_parkings','effective_date','cancel_date','cancellation_reason','status')
            ->where('parking_allotment_id', $id)
            ->first();

        $this->data = $result;
    }
}