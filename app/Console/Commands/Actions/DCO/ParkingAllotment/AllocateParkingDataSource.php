<?php

namespace App\Console\Commands\Actions\DCO\ParkingAllotment;

use Illuminate\Console\Command;
use App\Console\Commands\Action;

class AllocateParkingDataSource extends Action
{
    protected $signature = 'datasource:allocateParking {flowId} {parentId} {input}';

    protected $description = 'Allocate parking to primary member';

    public function apply()
    {
        // check fk_unit_id is valid or not and if not then return error
        $unitDetails = $this->tenantDB()->table('chsone_units_master')
            ->where('unit_id', $this->input['fk_unit_id'])
            ->first();

        if(!$unitDetails){
            $this->status = 'error';
            $this->message = 'Please provide a valid unit id OR No record found for this unit id';
            $this->statusCode = 400;
            $this->data = [];
            return;
        }

        // check fk_parking_unit_id is valid or not and if not then return error
        $parkingUnitDetails = $this->tenantDB()->table('chsone_units_master')
            ->where('unit_id', $this->input['fk_parking_unit_id'])
            ->first();

        if(!$parkingUnitDetails){
            $this->status = 'error';
            $this->message = 'Please provide a valid parking unit id OR No record found for this parking unit id';
            $this->statusCode = 400;
            $this->data = [];
            return;
        }

        // check if the parking unit is already allotted or not
        if($parkingUnitDetails->is_allotted == 1){
            $this->status = 'error';
            $this->message = 'Parking unit is already allotted';
            $this->statusCode = 400;
            $this->data = [];
            return;
        }

        // check parking allotment is already exists or not
        $parkingAllotment = $this->tenantDB()->table('chsone_parking_allotment_detail')
            ->where('fk_unit_id', $this->input['fk_unit_id'])
            ->where('fk_parking_unit_id', $this->input['fk_parking_unit_id'])
            ->where('status', 1)
            ->first();

        if($parkingAllotment){
            $this->status = 'error';
            $this->message = 'Parking Allotment already exists';
            $this->statusCode = 400;
            $this->data = [];
            return;
        }

        $unitId = $this->input['fk_unit_id'];
        $societyId = $this->input['company_id'];
        $parkingUnitId = $this->input['fk_parking_unit_id'];
        $effectiveDate = $this->input['effective_date'];
        $parkingType = $this->input['parking_type'];
        $parkingNumber = $this->input['parking_number'];
        $cancelDate = $this->input['cancel_date'] ?? "0000-00-00";
        $allotmentFor = $this->input['allotment_for'] ?? "";
        $allowedParking = $this->input['allowed_number_of_parkings'] ?? "";
        $status = 1;
        $created_date = date('Y-m-d H:i:s');
        $created_by = $this->input['user_id'];
        $updated_date = date('Y-m-d H:i:s');
        $updated_by = $this->input['user_id'];

        $result = $this->tenantDB()->table('chsone_parking_allotment_detail')
            ->insert([
                'fk_unit_id' => $unitId,
                'soc_id' => $societyId,
                'fk_parking_unit_id' => $parkingUnitId,
                'effective_date' => $effectiveDate,
                'parking_type' => $parkingType,
                'parking_number' => $parkingNumber,
                'cancel_date' => $cancelDate,
                'allotment_for' => $allotmentFor,
                'allowed_number_of_parkings' => $allowedParking,
                'status' => $status,
                'created_date' => $created_date,
                'created_by' => $created_by,
                'updated_date' => $updated_date,
                'updated_by' => $updated_by
            ]);

        if($result){

            // now update is_allotted, is_occupied and occupied_by in chsone_units_master table where unit_id = $parkingUnitId

            $updateParkingUnit = $this->tenantDB()->table('chsone_units_master')
                ->where('unit_id', $parkingUnitId)
                ->update([
                    'is_allotted' => 1,
                    'is_occupied' => 1,
                    'occupied_by' => 'owner'
                ]);

            // if not updated return error
            if(!$updateParkingUnit){
                $this->status = 'error';
                $this->message = 'Parking Allotment added successfully but unable to update parking unit';
                $this->statusCode = 400;
                $this->data = [];
                return;
            }

            $this->status = 'success';
            $this->message = 'Parking Allotment added successfully';
            $this->statusCode = 200;
            $this->data = [];
        }
        else{
            $this->status = 'error';
            $this->message = 'Parking Allotment not added';
            $this->statusCode = 400;
            $this->data = [];
        }

    }

}