<?php

namespace App\Console\Commands\Actions\DCO\ParkingAllotment;

use Illuminate\Console\Command;
use App\Console\Commands\Action;
use App\Models\Tenants\ChsoneMemberVehicleDetail;
use App\Models\Tenants\ChsoneParkingAllotmentDetail;
use App\Models\Tenants\ChsoneUnitsMaster;

class DeleteAllocateParkingDataSource extends Action
{
    protected $signature = 'datasource:deleteAllocateParking {flowId} {parentId} {input}';
    
    protected $description = 'Delete Parking Allotment by id';

    public function apply()
    {
        $id = $this->input['id'];
        $soc_id = $this->input['company_id'];
        $cancelDate = $this->input['cancel_date'];
        $cancellationReason = $this->input['cancellation_reason'];

        // check $id is a number or not then return error
        if(!is_numeric($id)) {
            $this->status = 'error';
            $this->message = 'Please provide a valid parking allotment id and it should be a number.';
            $this->statusCode = 400;
            $this->data = [];
            return;
        }

        $parkingLot = ChsoneParkingAllotmentDetail::where('soc_id', $soc_id)
            ->where('parking_allotment_id', $id)
            ->first();

        // Check if parking allotment is empty then return error
        if(!isset($parkingLot)) {
            $this->status = 'error';
            $this->message = 'No parking allotment found for the given id.';
            $this->statusCode = 400;
            $this->data = [];
            return;
        }

        if(isset($parkingLot->parking_allotment_id) && $parkingLot->parking_allotment_id != $id) {
            $this->status = 'error';
            $this->message = 'No parking allotment found for the given id.';
            $this->statusCode = 400;
            $this->data = [];
            return;
        } 
        
        if(isset($parkingLot->status) && $parkingLot->status == 0) {
            $this->status = 'error';
            $this->message = 'Parking allotment is already revoked.';
            $this->statusCode = 400;
            $this->data = [];
            return;
        }
        
        $parking_number = $parkingLot->parking_number;
        $parking_type = $parkingLot->parking_type;

        $vehicle_status = $this->removeRegisteredVehiclesFromParkingLot(['soc_id' => $soc_id, 'parking_allotment_id' => $parkingLot->parking_allotment_id]);

        $parkingLot->cancel_date = $cancelDate;
        $parkingLot->cancellation_reason = $cancellationReason;
        $parkingLot->status = 0;
        $parkingLot->updated_date = date('Y-m-d H:i:s');
        $parkingLot->updated_by = $this->input['user_id'];

        $strMessage = '';

        if(!$parkingLot->save()) 
        {
            $this->status = 'error';
            $this->statusCode = 400;
            $this->message = 'Parking allocation for '.$parking_number.'('.$parking_type.') is could not be revoked.';
            return;
        }else {
            $parkingUnit = ChsoneUnitsMaster::where('soc_id', $soc_id)
                ->where('unit_id', $parkingLot->fk_parking_unit_id)
                ->first();

            if ($parkingUnit) {
                $parkingUnit->is_allotted = '0';
                $parkingUnit->is_occupied = '0';
                $parkingUnit->occupied_by = 'vacant';

                if (!$parkingUnit->save()) {
                    $this->status = 'error';
                    $this->statusCode = 400;
                    $this->message = 'Parking allocation for ' . $parking_number . ' (' . $parking_type . ') is successfully revoked. <br/>Parking unit occupancy needs to be updated manually.';
                    $this->data = [];
                    return;
                } else {
                    $strMessage = 'Parking allocation for ' . $parking_number . ' (' . $parking_type . ') is successfully revoked.';
                    if ($vehicle_status) {
                        $strMessage .= ' Subsequent vehicle registration also cancelled.';
                    }
                    $this->status = 'success';
                    $this->statusCode = 200;
                    $this->message = $strMessage;
                    $this->data = [];
                    return;
                }
            } else {
                $this->status = 'error';
                $this->statusCode = 400;
                $this->message = 'Parking unit not found. Please check the details.';
                $this->data = [];
                return;
            }
        }
    }


    public function removeRegisteredVehiclesFromParkingLot($params = [])
    {
        // Check if required parameters are missing
        if (empty($params) || !isset($params['soc_id']) || !isset($params['parking_allotment_id'])) {
            return false;
        }

        // Fetch the list of vehicles based on the conditions
        $vehicleList = ChsoneMemberVehicleDetail::where('soc_id', $params['soc_id'])
            ->where('fk_parking_allotment_id', $params['parking_allotment_id'])
            ->where('status', '1')
            ->get();

        // Check if vehicles are found and delete them
        if ($vehicleList->isNotEmpty()) {
            foreach ($vehicleList as $vehicle) {
                $vehicle->delete();
            }
        }

        return true;
    }
}