<?php

namespace App\Console\Commands\Actions\DCO;

use Illuminate\Console\Command;
use App\Console\Commands\Action;
use Illuminate\Support\Facades\DB;

class ParkingUnitsListDataSource extends Action
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'datasource:parkingUnitsList {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Get Parking Units List (includes current parking unit if provided)';

    /**
     * Execute the console command.
     */
    public function apply()
    {
        $vacant_parking_clause = '';
        $currentDate = date('Y-m-d');
        $currentParkingId = $this->input['current_parking_id'] ?? $this->input['id'] ?? $this->input['fk_parking_unit_id'] ?? null;

        $obj = $this->tenantDB()->table('chsone_parking_allotment_detail')
        ->selectRaw('GROUP_CONCAT(DISTINCT fk_parking_unit_id) as already_alloted_parkings')
        ->where('soc_id', $this->input['company_id'])
        ->where('status', 1)
        ->whereDate('effective_date', '<=', $currentDate)
        ->where(function ($query) use ($currentDate) {
            $query->whereDate('cancel_date', '>=', $currentDate)
                  ->orWhere('cancel_date', '0000-00-00');
        })
        ->first();

        if (!empty($obj) && !empty($obj->already_alloted_parkings)) {
            $vacant_parking_clause = 'unit_id NOT IN (' . $obj->already_alloted_parkings . ')';
        }

        $parkingUnitsQuery = $this->tenantDB()->table('chsone_units_master')
            ->select('unit_id as id', DB::raw("CONCAT(soc_building_name, '-', unit_flat_number) as parking_number"))
            ->where('soc_id', $this->input['company_id'])
            ->whereRaw('LOWER(unit_type) = ?', ['parking'])
            ->where('status', 1);

        // Apply the NOT IN clause if set, but exclude current parking ID if provided
        if (!empty($vacant_parking_clause)) {
            // Extract the comma-separated values into an array
            $excludedUnitIds = explode(',', str_replace(['unit_id NOT IN(', ')'], '', $vacant_parking_clause));

            // If current parking ID is provided, remove it from excluded list so it's included in results
            if ($currentParkingId && in_array($currentParkingId, $excludedUnitIds)) {
                $excludedUnitIds = array_diff($excludedUnitIds, [$currentParkingId]);
            }

            // Apply the exclusion only if there are still IDs to exclude
            if (!empty($excludedUnitIds)) {
                $parkingUnitsQuery->whereNotIn('unit_id', $excludedUnitIds);
            }
        }

        $parkingUnits = $parkingUnitsQuery->get();

        $this->data = $parkingUnits;
    }
}
