<?php

namespace App\Console\Commands\Actions\DCO;

use Illuminate\Console\Command;
use App\Console\Commands\Action;
use Illuminate\Support\Facades\DB;

class ParkingAllotmentListDataSource extends Action
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'datasource:parkingAllotmentList {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Get Parking Allotment List';

    protected $formatter =  [
        "id" => "",
        "parking_allotment_id" => "",
        "unit_id" => "",
        "soc_building_floor" => "",
        "soc_building_name" => "",
        "parking_number" => "",
        "parking_type" => "",
        "allotted_parking_type" => "",
        "unit_parking_type" => "",
        "unit_name" => "",
        "parking_unit_name" => "",
        "allotment_to" => "concat:soc_building_name,unit_name",
        "effective_date" => "concat:effective_date,'onwards'",
        "member_primary_name" => "",
        "allowed_number_of_parkings" => "",
        "actual_number_of_parkings" => "",
        "allotment_for_id" => "",
        "allotment_for" => "",
        "status" => "",
        "parking_detail" => "concat:allowed_number_of_parkings,actual_number_of_parkings,allotment_for"

    ];
    protected $formatterByKeys =  ["id"];

    protected $mapper = [
        "id" => "a.parking_allotment_id",
        "parking_number" => "a.parking_number",
        "unit_flat_number" => "units.unit_flat_number",
        "allotment_for" => "a.allotment_for",
        "parking_type" => "a.parking_type",
    ];
    /**
     * Execute the console command.
     */
    public function apply()
    {
        $parking_number = '';
        $unit_flat_number = '';
        $parking_type = '';
        $allotment_for = '';
        $status = null;

        $page = $this->input['page'] ?? 1;
        $per_page = !empty($this->input['per_page']) ? $this->input['per_page'] : ($this->hugeData ? 100000 : 10);
        $offset = ($page - 1) * $per_page;

        if (!empty($this->input['filters'])) {
            $parking_number =
            !empty($this->input['filters']['parking_number']) ? $this->input['filters']['parking_number'] : '';
            unset($this->input['filters']['parking_number']);

            $unit_flat_number =
            !empty($this->input['filters']['unit_flat_number']) ? $this->input['filters']['unit_flat_number'] : '';
            unset($this->input['filters']['unit_flat_number']);

            $allotment_for =
            !empty($this->input['filters']['allotment_for']) ? $this->input['filters']['allotment_for'] : '';
            unset($this->input['filters']['allotment_for']);

            $parking_type =
            !empty($this->input['filters']['parking_type']) ? $this->input['filters']['parking_type'] : '';
            unset($this->input['filters']['parking_type']);

            $status = !empty($this->input['filters']['is_allotted']) ? strtolower($this->input['filters']['is_allotted']) : '';
            unset($this->input['filters']['is_allotted']);
        
        }

        $cancelDate = '0000-00-00';
        $currentDate = $this->getCurrentDate('database');

        // $obj = $this->tenantDB()->table('chsone_parking_allotment_detail AS parking_allotment')
        //             ->selectRaw("
        //                 parking_allotment.parking_allotment_id AS id,
        //                 parking_allotment.parking_allotment_id,
        //                 parking_allotment.parking_number,
        //                 parking_allotment.parking_type,
        //                 parking_allotment.allotment_for,
        //                 parking_allotment.effective_date,
        //                 parking_allotment.cancel_date,
        //                 parking_allotment.allowed_number_of_parkings,
        //                 parking_allotment.actual_number_of_parkings,
        //                 IF(parking_allotment.status=1, 'Unalloted', 'Allotted') AS status,
        //                 units.unit_id,
        //                 units.soc_building_floor,
        //                 units.unit_flat_number,
        //                 units.occupied_by,
        //                 units.occupancy_type,
        //                 units.is_occupied,
        //                 concat(member_master.member_first_name, ' ', member_master.member_last_name) AS member_primary_name,
        //                 soc_bldg_master.soc_building_name
        //             ")
        //             ->where("parking_allotment.status", "1")
        //             ->whereRaw('parking_allotment.effective_date <= "'.$currentDate.'" AND ((parking_allotment.cancel_date = "'.$cancelDate.'") OR (parking_allotment.cancel_date >= "'.$currentDate.'"))')
        //             ->leftJoin(
        //                 'chsone_units_master AS units',
        //                 'parking_allotment.fk_unit_id',
        //                 '=',
        //                 'units.unit_id'
        //             )
        //             ->leftJoin(
        //                 'chsone_units_master AS parkingUnits',
        //                 'parking_allotment.fk_parking_unit_id',
        //                 '=',
        //                 'units.unit_id'
        //             )
        //             ->leftJoin(
        //                 'chsone_societies_building_master AS soc_bldg_master',
        //                 'units.soc_building_id',
        //                 '=',
        //                 'soc_bldg_master.soc_building_id'
        //             )->leftJoin(
        //                 'chsone_members_master AS member_master',
        //                 'parking_allotment.fk_parking_unit_id',
        //                 '=',
        //                 'member_master.id'
        //             );

        $obj = $this->tenantDB()->table('chsone_units_master AS u')
        ->select([
            'a.parking_allotment_id as id',
            'u.unit_id as unit_id',
            'u.unit_flat_number as parking_unit_name',
            'u.unit_category as unit_parking_type',
            'u.soc_building_name',
            'u.soc_building_floor',
            'u.is_allotted as parking_allotted',
            'u.is_occupied as parking_occupied',
            'u.occupancy_type',
            'u.occupied_by',
            "a.parking_type",
            'u.chargeable',
            'u.extra_attributes',
            'a.parking_number',
            'a.parking_type as allotted_parking_type',
            'a.allotment_for as allotment_for_vehicle',
            'a.allotment_for',
            'a.actual_number_of_parkings',
            'a.fk_unit_id',
            'a.effective_date',
            'a.parking_allotment_id',
            'a.cancel_date',
            'units.unit_flat_number as unit_name',
            'member_master.id as allotment_for_id',
            'a.allowed_number_of_parkings',
            // concat building name and unit name
            DB::raw("CONCAT(units.soc_building_name, ' ', units.unit_flat_number) AS allotment_to"),
            DB::raw("CONCAT(member_master.member_first_name, ' ', member_master.member_last_name) AS member_primary_name"),
            
        ])
        ->leftJoin('chsone_parking_allotment_detail AS a', 'u.unit_id', '=', 'a.fk_parking_unit_id')
        ->leftJoin('chsone_units_master AS units', 'a.fk_unit_id', '=', 'units.unit_id')
        ->leftJoin('chsone_members_master AS member_master', 'units.unit_id', '=', 'member_master.fk_unit_id')
        ->where('u.soc_id', $this->input['company_id'])
        // ->where('u.unit_type', 'parking')
        ->where('u.status', 1)
        ->where('a.status', 1)
        ->where('member_master.member_type_id', 1)
        ->when(isset($this->input['arrPostData']['allotted']), function ($query) {
            $val = strtolower($this->input['arrPostData']['allotted']);
            if ($val === 'yes') {
                $query->where('u.is_allotted', 1);
            } elseif ($val === 'no') {
                $query->where('u.is_allotted', 0);
            }
        })
        ->groupBy('a.parking_allotment_id')
        ->orderBy('u.created_date', 'ASC');
        if ($parking_number) {
            $obj = $obj->whereRaw("LOWER(a.parking_number) LIKE '%{$parking_number}%'");
        }

        if ($parking_type) {
            // Split by comma and trim spaces
            $types = array_map('trim', explode(',', strtolower($parking_type)));

            // Apply whereIn condition
            $obj = $obj->whereIn(DB::raw('LOWER(a.parking_type)'), $types);
        }


        if ($unit_flat_number) {
            $obj = $obj->whereRaw("LOWER(units.unit_flat_number) LIKE '%{$unit_flat_number}%'");
        }

        if ($allotment_for) {
            $allotment_for = array_map('trim', explode(',', strtolower($allotment_for)));
            $obj = $obj->whereIn(DB::raw('LOWER(a.allotment_for)'), $allotment_for);
        }

        if ($status) {
            $status = strtolower($status);
            if ($status === 'yes') {
                $obj = $obj->whereRaw("LOWER(u.occupied_by) != 'vacant'");
            } elseif ($status === 'no') {
                $obj = $obj->whereRaw("LOWER(u.occupied_by) = 'vacant'");
            }
        }
        
        $obj = $this->filter($obj);
        $count = $obj->get()->count();

        $obj = $obj->offset($offset);
        $obj = $obj->limit($per_page);
        $result = $obj->get();
        $results = $result->map(function ($item) {
            // Decode the extra_attributes JSON string
            $extra = json_decode($item->extra_attributes, true);
        
            // Add the new 'parking_type' key
            $item->parking_type = $extra['vehicle_type'] ?? null;
        
            // Add the new 'status' key based on occupied_by
            if (strtolower($item->occupied_by) === 'vacant') {
                $item->status = 'unallotted';
            } elseif (strtolower($item->occupied_by) === 'owner') {
                $item->status = 'allotted';
            } else {
                $item->status = 'unknown'; // optional fallback
            }
        
            return $item;
        });
        // $this->data = $result->toArray();
        $this->data = $this->format($results->toArray());
        $this->meta['pagination']['total'] = $count;
    }

    public function concat($a, $b, $c=null)
    {
        if (!is_null($c)) {

            // Default value if $c is null or not one of the expected types
            $spaceFor = 'Not Specified';

            // Normalize to lowercase for lookup
            $key = strtolower($c);

            // Map of valid vehicle types
            $arrVehicleTypes = [
                '2wheeler' => '2 Wheeler',
                '4wheeler' => '4 Wheeler',
            ];

            if (isset($arrVehicleTypes[$key])) {
                $spaceFor = $arrVehicleTypes[$key];
            }

            return "Allowed: " . $a . ", Actual: " . $b . ", Space for: " . $spaceFor;

            // $c = ucfirst(str_replace('wheeler', ' Wheeler', $c));
            // return "Allowed: ".$a . ', Actual: ' . $b. ", Space for: ". $c;
        } elseif (empty($b)) {
            return $a ." onwards";
        } else {
            return $a ." / ".$b;
        }
    }
}
