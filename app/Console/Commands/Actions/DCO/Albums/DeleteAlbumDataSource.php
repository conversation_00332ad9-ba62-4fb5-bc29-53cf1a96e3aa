<?php

namespace App\Console\Commands\Actions\DCO\Albums;

use Illuminate\Console\Command;
use App\Console\Commands\Action;

class DeleteAlbumDataSource extends Action
{
    protected $signature = 'datasource:deleteAlbum {flowId} {parentId} {input}';

    protected $description = 'Delete album';

    public function apply()
    {
        $id = $this->input['id'];
        $obj = $this->tenantDB()->table("photo_album")
            ->where('id', $id)
            ->delete();

        if($obj){
            $this->status = 'success';
            $this->message = 'Album deleted successfully';
            $this->statusCode = 200;
            $this->data = [];
        }
        else{
            $this->status = 'error';
            $this->message = 'Album not found';
            $this->statusCode = 400;
            $this->data = [];
        }
    }
}