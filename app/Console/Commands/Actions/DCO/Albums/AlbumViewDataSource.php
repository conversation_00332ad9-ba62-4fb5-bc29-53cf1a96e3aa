<?php

namespace App\Console\Commands\Actions\DCO\Albums;

use Illuminate\Console\Command;
use App\Console\Commands\Action;

class AlbumViewDataSource extends Action
{
    protected $signature = 'datasource:albumView {flowId} {parentId} {input}';
    protected $description = 'Get album data';

    protected $formatter = [
        "id" => "",
        "name" => "",
        "photo_id" => "",
        "photo_image" => "concat:name,photo_image"
    ];

    protected $formatterByKeys =  ['photo_id'];
    
    public function apply()
    {
        $id = $this->input['id'];
        $obj = $this->tenantDB()->table("photo_album AS album")
            ->where('album.id', $id)
            ->leftJoin('album_photos AS photo', 'album.id', '=', 'photo.album_id')
            ->select('album.id', 'album.name', 'photo.id AS photo_id', 'photo.image AS photo_image')
            ->get();

        $this->data = $this->format($obj->toArray());
    }

    public function concat($a, $b)
    {
        $company_id = $this->input['company_id'];
        return "https://s3.ap-south-1.amazonaws.com/society.chsone.in/albums/" . $company_id . "/" . $a . "/" . $b;
    }
}