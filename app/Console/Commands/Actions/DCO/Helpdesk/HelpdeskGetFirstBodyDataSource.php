<?php

namespace App\Console\Commands\Actions\DCO\Helpdesk;

use App\Console\Commands\Action;

class HelpdeskGetFirstBodyDataSource extends Action
{
    protected $signature = 'datasource:helpdeskGetFirstBody {flowId} {parentId} {input}';
    protected $description = 'Action for Get First Body from Helpdesk Threads1';

    /**
     * Get first 'body' from helpdesk_threads1 by issue_id
     * @param int $issue_id
     * @return array
     */
    public function apply()
    {
        try {
            $issue_id = $this->input['issue_id'];
            $db = $this->tenantDB();
            $schema = $db->getSchemaBuilder();
            $body = null;

            if ($schema->hasTable('helpdesk_threads1')) {
                $row = $db->table('helpdesk_threads1')
                    ->where('fk_issue_id', $issue_id)
                    ->orderBy('thread_id', 'asc')
                    ->first();
                if ($row && isset($row->body)) {
                    $body = $row->body;
                }
            }

            $ticket_number = $this->tenantDB()->table('helpdesk_issues2')->where('issue_id', $issue_id)->first();
            $this->data = $body !== null ? 
            [
                'job_details' => $body,
                'ticket_number' => $ticket_number->ticket_number ?? null
            ] : [];
        } catch (\Exception $e) {
            dd($e);
        }
    }
}
