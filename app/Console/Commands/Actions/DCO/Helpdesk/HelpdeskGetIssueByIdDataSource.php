<?php

namespace App\Console\Commands\Actions\DCO\Helpdesk;

use App\Console\Commands\Action;

class HelpdeskGetIssueByIdDataSource extends Action
{
    protected $signature = 'datasource:helpdeskGetIssueById {flowId} {parentId} {input}';
    protected $description = 'Action for Get Helpdesk Issue By Id';

    /**
     * Get helpdesk issue by id
     * @param int $issue_id
     * @return array
     */
    public function apply()
    {
        try {
            $issue_id = $this->input['issue_id'];
            $db = $this->tenantDB();
            $schema = $db->getSchemaBuilder();
            $issue = null;
        
            // Step 1: Try helpdesk_issues
            if ($schema->hasTable('helpdesk_issues')) {
                $issue = $db->table('helpdesk_issues')
                    ->where('issue_id', $issue_id)
                    ->first();
                $tableName = 'helpdesk_issues';
            }
        
            // Step 2: Try helpdesk_issues1
            if (!$issue && $schema->hasTable('helpdesk_issues1')) {
                $issue = $db->table('helpdesk_issues1')
                    ->where('issue_id', $issue_id)
                    ->first();
                $tableName = 'helpdesk_issues1';
            }
        
            // Step 3: Try helpdesk_issues2
            if (!$issue && $schema->hasTable('helpdesk_issues2')) {
                $issue = $db->table('helpdesk_issues2')
                    ->where('issue_id', $issue_id)
                    ->first();
                $tableName = 'helpdesk_issues2';
            }
        
            if ($issue) {
                // Rename foreign key to user-friendly name
                $issue->help_topic_id = $issue->fk_help_topic_id;
                unset($issue->fk_help_topic_id);
        
                // Step 4: Fetch all attachments for this issue
                if ($schema->hasTable('helpdesk_attachments')) {
                    $attachments = $db->table('helpdesk_attachments')
                        ->where('fk_issue_id', $issue->issue_id)
                        ->pluck('file_path') // get just the column values
                        ->toArray();         // convert to array
                } else {
                    $attachments = [];
                }
        
                // Step 5: Set src as array of file paths
                $issue->src = $attachments;
            }
        
            $this->data = $issue ?: [];
        
        } catch (\Exception $e) {
            dd($e);
        }
    }
}
