<?php

namespace App\Console\Commands\Actions\DCO;

use Illuminate\Console\Command;
use App\Console\Commands\Action;

class NoticesViewDataSource extends Action
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'datasource:NoticesViewDataSource {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Notices View Data Source';

    protected $formatter = [
        'id' => '',
        'notice_ref_no' => '',
        'subject' => '',
        'body' => '',
    ];

    /**
     * Execute the console command.
     */
    public function apply()
    {
        $id = $this->input['id'];
        $obj = $this->tenantDB()->table('chsone_notices')
        ->select('notice_id as id', 'notice_ref_no', 'subject', 'body')
        ->where('notice_id', $id);
        $obj = $obj->orderBy('id', 'asc');
        $result = $obj->first();
        // $this->data = $this->format($result->toArray());
        $this->data = $result;
    }
}
