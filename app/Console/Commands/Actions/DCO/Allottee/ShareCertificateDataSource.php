<?php

namespace App\Console\Commands\Actions\DCO\Allottee;

use Illuminate\Console\Command;
use App\Console\Commands\Action;

class ShareCertificateDataSource extends Action
{
    protected $signature = 'datasource:shareCertificate {flowId} {parentId} {input}';

    protected $description = 'Share Certificate Data Source';

    public function apply()
    {
        $unit_id = $this->input['unit_id'];

        $obj = $this->tenantDB()->table('chsone_member_share_certificate')
            ->insertGetId([
                'certificate_no' => $this->input['certificate_no'],
                'member_reg_no' => $this->input['member_reg_no'],
                'unit_id' => $this->input['unit_id'],
                'soc_id' => $this->input['company_id'],
                'membership_id' => $this->input['membership_id'],
                'admission_date' => $this->input['admission_date'] ?? null,
                'entrance_fee' => $this->input['entrance_fee'] ?? 0,
                'full_name' => $this->input['full_name'] ?? null,
                'address' => $this->input['address'] ?? null,
                'occupation' => $this->input['occupation'] ?? null,
                'age_on_admission' => $this->input['age_on_admission'] ?? null,
                'ceasing_reason' => $this->input['ceasing_reason'] ?? null,
                'no_of_shares' => $this->input['no_of_shares'],
                'share_series_start' => $this->input['share_series_start'],
                'share_series_end' => $this->input['share_series_end'],
                'share_sr_no' => $this->input['share_sr_no'],
                'share_value' => $this->input['share_value'],
                'amount_paid' => $this->input['amount_paid'],
                'status' => 1,
                'created_date' => date('Y-m-d H:i:s'),
                'created_by' => $this->input['created_by'] ?? 0,
                'updated_date' => date('Y-m-d H:i:s'),
                'updated_by' => $this->input['updated_by'] ?? 0,
            ]);

        $nomineeDetails = $this->input['nominee_details'] ?? [];

        foreach($nomineeDetails as $nominee)
        {
            $nominee = $this->tenantDB()->table('chsone_iregister_nominees')
                ->insert([
                    'share_certificate_id' => $obj,
                    'nominee_name' => $nominee['nominee_name'] ?? null,
                    'nominee_address' => $nominee['nominee_address'] ?? null,
                    'percentage' => $nominee['nominee_percentage'] ?? 0,
                    'is_minor' => $nominee['is_minor'] ?? 0,
                    'guardian_name' => $nominee['guardian_name'] ?? "",
                    'guardian_address' => $nominee['guardian_address'] ?? "",
                    'relation_with_minor' => $nominee['relation_with_minor'] ?? "",
                    'status' => 1,
                    'created_date' => date('Y-m-d H:i:s'),
                    'created_by' => $this->input['created_by'] ?? 0,
                    'updated_date' => date('Y-m-d H:i:s'),
                    'updated_by' => $this->input['updated_by'] ?? 0,
                ]);
        }

        if($obj){
            $this->status = 'success';
            $this->message = 'Member status updated successfully';
            $this->statusCode = 200;
            $this->data = [];
        }else{
            $this->status = 'error';
            $this->message = 'Member status not updated';
            $this->statusCode = 400;
            $this->data = [];
        }
    }
}