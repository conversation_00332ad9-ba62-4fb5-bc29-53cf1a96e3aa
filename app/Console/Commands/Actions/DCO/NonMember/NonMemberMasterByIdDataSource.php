<?php

namespace App\Console\Commands\Actions\DCO\NonMember;

use Illuminate\Console\Command;
use App\Console\Commands\Action;

class NonMemberMasterByIdDataSource extends Action
{
    protected $signature = 'datasource:nonMemberMasterById {flowId} {parentId} {input}';

    protected $description = 'Get Non Member Master By Id Data Source';

    public function apply()
    {
        $id = $this->input['id'];
        $obj = $this->tenantDB()->table('chsone_nonmember_master')
            ->where('nonmember_id', $id)
            ->first();

        if (!$obj) {
            $this->status = 'error';
            $this->message = 'No data found';
            $this->statusCode = 400;
            $this->data = [];
        }
        else{
            $this->data = $obj;
        }
    }
}