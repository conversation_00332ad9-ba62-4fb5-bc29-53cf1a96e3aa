<?php

namespace App\Console\Commands\Actions\DCO\NonMember;

use Illuminate\Console\Command;
use App\Console\Commands\Action;

class UpdateNonMemberMasterDataSource extends Action
{
    protected $signature = 'datasource:updateNonMemberMaster {flowId} {parentId} {input}';

    protected $description = 'Update Non Member Master Data Source';

    public function apply()
    {
        $first_name = $this->input['first_name'] ?? '';
        $last_name = $this->input['last_name'] ?? '';
        $email_id = $this->input['email_id'] ?? '';
        $mobile_number = $this->input['mobile_number'] ?? '';
        $gender = $this->input['gender'] ?? '';
        $gstin = $this->input['gstin'] ?? '';
        $place_of_supply = $this->input['place_of_supply'] ?? '';
        $pan_number = $this->input['pan_number'] ?? '';
        $hsn = $this->input['hsn'] ?? '';
        $address = $this->input['address'] ?? '';
        $id = $this->input['id'];

        $obj = $this->tenantDB()->table('chsone_nonmember_master')
            ->update([
                'first_name' => $first_name,
                'last_name' => $last_name,
                'email_id' => $email_id,
                'mobile_number' => $mobile_number,
                'gender' => $gender,
                'gstin' => $gstin,
                'place_of_supply' => $place_of_supply,
                'pan_number' => $pan_number,
                'hsn' => $hsn,
                'address' => $address,
            ]);

        if($obj) {
            $this->status = 'success';
            $this->message = 'Non Member Master updated successfully';
            $this->statusCode = 200;
            $this->data = [];
        }
        else {
            $this->status = 'error';
            $this->message = 'Non Member Master not updated';
            $this->statusCode = 400;
            $this->data = [];
        }
    }
}