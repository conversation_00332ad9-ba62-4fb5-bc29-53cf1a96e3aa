<?php

namespace App\Console\Commands\Actions\DCO\NonMember;

use Illuminate\Console\Command;
use App\Console\Commands\Action;

class AddNonMemberMasterDataSource extends Action
{
    protected $signature = 'datasource:addNonMemberMaster {flowId} {parentId} {input}';

    protected $description = 'Add Non Member Master Data Source';

    public function apply()
    {
        $first_name = $this->input['first_name'] ?? '';
        $last_name = $this->input['last_name'] ?? '';
        $email_id = $this->input['email_id'] ?? '';
        $mobile_number = $this->input['mobile_number'] ?? '';
        $gender = $this->input['gender'] ?? '';
        $gstin = $this->input['gstin'] ?? '';
        $place_of_supply = $this->input['place_of_supply'] ?? '';
        $pan_number = $this->input['pan_number'] ?? '';
        $hsn = $this->input['hsn'] ?? '';
        $address = $this->input['address'] ?? '';

        $is_default_ledger = $this->input['is_default_ledger'] ?? 1;

        $default_ledger = $this->tenantDB()->table('chsone_grp_ledger_tree')
            ->where('soc_id', $this->input['company_id'])
            ->where('ledger_account_name', 'Nonmember Income')
            ->where('context', 'sundrydebtors')
            ->where('status', 1)
            ->first();

        $default_ledger_id = $default_ledger->ledger_account_id ?? 0; 

        if($is_default_ledger == 0) {
            $default_ledger_id = $this->tenantDB()->table('chsone_grp_ledger_tree')
                ->insertGetId([
                    'soc_id' => $this->input['company_id'],
                    'ledger_account_name' => $first_name . ' ' . $last_name,
                    'nature_of_account' => 'dr',
                    'parent_id' => $default_ledger_id,
                    'report_head' => 'balance sheet',
                    'operating_type' => 'indirect',
                    'context' => 'sundrydebtors',
                    'added_on' => date('Y-m-d H:i:s'),
                    'status' => 1,
                    'created_by' => $this->input['created_by'] ?? 0,
                    'entity_type' => 'ledger',
                    'behaviour' => 'asset',
                    'defined_by' => 'system'
                ]);
        }

        $obj = $this->tenantDB()->table('chsone_nonmember_master')
            ->insert([
                'soc_id' => $this->input['company_id'],
                'is_default_ledger' => $is_default_ledger,
                'nonmember_ledger_id' => $default_ledger_id,
                'first_name' => $first_name,
                'last_name' => $last_name,
                'email_id' => $email_id,
                'mobile_number' => $mobile_number,
                'gender' => $gender,
                'gstin' => $gstin,
                'place_of_supply' => $place_of_supply,
                'pan_number' => $pan_number,
                'hsn' => $hsn,
                'address' => $address,
                'created_date' => date('Y-m-d H:i:s'),
                'created_by' => $this->input['created_by'] ?? 0,
                'updated_date' => date('Y-m-d H:i:s'),
                'updated_by' => $this->input['updated_by'] ?? 0,
            ]);

        if($obj) {
            $this->status = 'success';
            $this->message = 'Non Member Master added successfully';
            $this->statusCode = 200;
        } else {
            $this->status = 'error';
            $this->message = 'Non Member Master not added';
            $this->statusCode = 400;
        }

    }
}