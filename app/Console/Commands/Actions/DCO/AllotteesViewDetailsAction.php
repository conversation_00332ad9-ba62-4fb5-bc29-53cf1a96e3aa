<?php

namespace App\Console\Commands\Actions\DCO;

use Illuminate\Console\Command;
use App\Console\Commands\Action;

class AllotteesViewDetailsAction extends Action
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'datasource:allotteesViewDetails {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Allottees Details';

    protected $formatter = [
        "id" => "",
        "fk_unit_id" => "",
        "member_type_id" => "",
        "unit_flat_number" => "",
        "member_type_name" => "",
        "member_first_name" => "",
        "member_last_name" => "",
        "member_name" => "concat:member_first_name,member_last_name",
        "member_email_id" => "",
        "member_mobile_number" => "",
        "effective_date" => "",
        "member_dob" => "",
    ];

    protected $formatterByKeys = [
        "id"
    ];

    protected $mapper = [
        "id" => "cmmh.member_history_id",
    ];

    /**
     * Execute the console command.
     */
    public function apply()
    {
        $id = $this->input['id'];
        $fk_unit_id = $this->tenantDB()->table('chsone_members_master')
            ->where('id', $id)
            ->value('fk_unit_id');

            $history = $this->tenantDB()->table('chsone_members_master_history AS cmmh')
            ->selectRaw('
                cmmh.member_history_id as id,
                cmmh.fk_unit_id,
                cmmh.member_type_id,
                cmmh.member_first_name,
                cmmh.member_last_name,
                CONCAT(cmmh.member_first_name, " ", cmmh.member_last_name) AS full_name, -- Concatenate first and last name
                cmmh.member_email_id,
                cmmh.member_mobile_number,
                CASE 
                    WHEN cmmh.member_gender = "M" THEN "Male"
                    WHEN cmmh.member_gender = "F" THEN "Female"
                    ELSE "Unknown"
                END AS member_gender, -- Transform gender
                COALESCE(cmmh.member_dob, "") AS member_dob, -- Replace NULL dob with empty string
                cum.unit_flat_number,
                cmtm.member_type_name,
                cmmh.effective_date
            ')
            ->leftJoin('chsone_member_type_master AS cmtm', 'cmtm.member_type_id', '=', 'cmmh.member_type_id')
            ->leftJoin('chsone_units_master AS cum', 'cum.unit_id', '=', 'cmmh.fk_unit_id')
            ->where('cmmh.fk_unit_id', $fk_unit_id)
            ->where('cmmh.id', $id)
            ->get();

        $this->data = $history->toArray();
        
    }

    public function concat($a, $b)
    {
        return $a . " " . $b;
    }
}
