<?php

namespace App\Console\Commands\Actions\DCO;

use Illuminate\Console\Command;
use App\Console\Commands\Action;

class UpdateAllotteesDataSource extends Action
{
    protected $signature = 'datasource:updateAllottees {flowId} {parentId} {input}';

    protected $description = 'Update Allottees';

    public function apply()
    {
        $id = $this->input['id'];
        $salute = $this->input['salute'];
        $member_type_id = $this->input['member_type_id'];
        $member_first_name = $this->input['member_first_name'];
        $member_last_name = $this->input['member_last_name'];
        $member_email_id = $this->input['member_email_id'];
        $member_mobile_number = $this->input['member_mobile_number'];
        $member_intercom = $this->input['member_intercom'];
        $member_gender = $this->input['member_gender'];
        $member_dob = $this->input['member_dob'];
        $user_id = $this->input['user_id'] ?? '';
        $gstin = $this->input['gstin'];
        $effective_date = $this->input['effective_date'];
        $is_tenant = $this->input['is_tenant'];

        $unit_id = $this->input['unit_id'];
        // $soc_building_id = $this->input['soc_building_id'];
        // $soc_building_floor = $this->input['soc_building_floor'];
        // $unit_flat_number = $this->input['unit_flat_number'];
        // $unit_category = $this->input['unit_category'];
        $is_occupied = $this->input['is_occupied'];
        $occupied_by = $this->input['occupied_by'];

        if($is_occupied == 1){
            $occupied_data = [
                'is_occupied' => $is_occupied,
                'occupied_by' => $occupied_by
            ];
        }else{
            $occupied_data = [
                'is_occupied' => $is_occupied,
                'occupied_by' => null
            ];
        }


        $obj = $this->tenantDB()->table('chsone_members_master')
            ->where('id', $id)
            ->update([
                'salute' => $salute,
                'member_type_id' => $member_type_id,
                'member_first_name' => $member_first_name,
                'member_last_name' => $member_last_name,
                'member_email_id' => $member_email_id,
                'member_mobile_number' => $member_mobile_number,
                'member_intercom' => $member_intercom,
                'member_gender' => $member_gender,
                'member_dob' => $member_dob,
                'gstin' => $gstin,
                'effective_date' => $effective_date,
                'is_tenant' => $is_tenant,
                'fk_unit_id' => $unit_id,
                'user_id' => $user_id,     // specially added for oneapp in case of blank user id.
                'updated_date' => date('Y-m-d H:i:s'),
                'updated_by' => $this->input['user_id'] ?? 0
            ]);

        $unit = $this->tenantDB()->table('chsone_units_master')
            ->where('unit_id', $unit_id)
            ->update($occupied_data);

        if($obj){
            $this->status = 'success';
            $this->message = 'Allottee updated successfully';
            $this->statusCode = 200;
        }else{
            $this->status = 'error';
            $this->message = 'Allottee not updated';
            $this->statusCode = 400;
        }

    }
}