<?php

namespace App\Console\Commands\Actions\DCO\VehicleRegistration;

use Illuminate\Console\Command;
use App\Console\Commands\Action;

class VehicleRegisterDataSource extends Action
{
    protected $signature = 'datasource:vehicleRegister {flowId} {parentId} {input}';

    protected $description = 'Vehicle Register Data Source';

    public function apply()
    {
        try     {
            $flat_number = $this->input['unit_number'] ?? 1;
    
            $building = $this->input['building'] ?? null;
            $floor = $this->input['floor'] ?? null;
            $flat = $this->input['flat'] ?? null;
    
            $fk_member_id = $this->input['fk_member_id'] ?? 1;
            
            $fk_parking_unit_id = $this->input['fk_parking_unit_id'] ?? 1;
            $badge_number = $this->input['badge_number'] ?? 1;
            $vehicle_registration_number = $this->input['vehicle_registration_number'] ?? null;
            $effective_date = $this->input['effective_date'] ?? date('Y-m-d');
            $vehicle_type = $this->input['vehicle_type'] ?? "2wheeler";
            $vehicle_company = $this->input['vehicle_company'] ?? "";
            $vehicle_model_number = $this->input['vehicle_model_number'] ?? "";
            $vehicle_colour = $this->input['vehicle_colour'] ?? "";
            $vehicle_registration_type = $this->input['vehicle_registration_type'] ?? "primary";
            $vehicle_owned_by = $this->input['vehicle_owned_by'] ?? "member";
            $status = $this->input['status'] ?? 1;
            $created_date = $this->input['created_date'] ?? date('Y-m-d H:i:s');
            $created_by = $this->input['user_id'] ?? 0;
            $updated_date = $this->input['updated_date'] ?? date('Y-m-d H:i:s');
            $updated_by = $this->input['user_id'] ?? 0;
            
    
            if ($flat_number) {
                
                $units = $this->tenantDB()->table("chsone_units_master AS cum")
                    ->select("cum.*")
                    ->where('cum.unit_flat_number', $flat_number)
                    ->get();
    
                if($units->count() > 0) {
                    $unit = $units->first();
                    $unit_id = $unit->unit_id;
    
                    $chsone_parking_allotment_detail = $this->tenantDB()->table("chsone_parking_allotment_detail")
                        ->where('fk_unit_id', $unit_id)
                        ->where('fk_parking_unit_id', $fk_parking_unit_id)
                        ->first();
                    
                    if($chsone_parking_allotment_detail != null) {
                        // get the fk_parking_allotment_id from chsone_parking_allotment_detail
                        $fk_parking_allotment_id = $chsone_parking_allotment_detail->parking_allotment_id;
                    } else {
                        // insert into chsone_parking_allotment_detail and get the fk_parking_allotment_id
                        $obj = $this->tenantDB()->table("chsone_parking_allotment_detail")
                            ->insert([
                                'soc_id' => $this->input['company_id'],
                                "fk_unit_id" => $unit_id,
                                "fk_parking_unit_id" => $fk_parking_unit_id,
                                "parking_number" => $vehicle_type == "2wheeler" ? "parking-02" : "parking-04",
                                "parking_type" => "parking",
                                "allotment_for" => $vehicle_type,
                                "allowed_number_of_parkings" => 1,
                                "actual_number_of_parkings" => 0,
                                "effective_date" => $effective_date ?? date('Y-m-d'),
                                "cancel_date" => "0000-00-00",
                                "status" => 1,
                                "created_date" => $created_date,
                                "created_by" => $created_by,
                                "updated_date" => $updated_date,
                                "updated_by" => $updated_by
                            ]);
    
                        $lastInserted = $this->tenantDB()->table("chsone_parking_allotment_detail")
                            ->where('fk_unit_id', $unit_id)
                            ->first();
    
                        $fk_parking_allotment_id = $lastInserted->parking_allotment_id;
    
                    }
    
                    
                    $obj = $this->tenantDB()->table("chsone_member_vehicle_detail")
                        ->insert([
                            'soc_id' => $this->input['company_id'],
                            'fk_member_id' => $fk_member_id,
                            'fk_parking_allotment_id' => $fk_parking_allotment_id,
                            'fk_parking_unit_id' => $fk_parking_unit_id,
                            'badge_number' => $badge_number,
                            'vehicle_registration_number' => $vehicle_registration_number,
                            'effective_date' => $effective_date,
                            'vehicle_type' => $vehicle_type,
                            'vehicle_company' => $vehicle_company,
                            'vehicle_model_number' => $vehicle_model_number,
                            'vehicle_colour' => $vehicle_colour,
                            'vehicle_registration_type' => $vehicle_registration_type,
                            'vehicle_owned_by' => $vehicle_owned_by,
                            'status' => $status,
                            'created_date' => $created_date,
                            'created_by' => $created_by,
                            'updated_date' => $updated_date,
                            'updated_by' => $updated_by
                        ]);
    
    
                    $this->status = 'success';
                    $this->message = 'Vehicle Registered Successfully';
                    $this->statusCode = 200;
                    $this->data = [];
                    
    
                } else {
                    $this->status = 'error';
                    $this->message = 'Please provide a valid flat number';
                    $this->statusCode = 400;
                    $this->data = [];
                }
    
            } else {
                $this->status = 'error';
                $this->message = 'Please provide a valid flat number';
                $this->statusCode = 400;
                $this->data = [];
            }
        } catch (\Exception $e) {
            dd($e);
        }
    }
}