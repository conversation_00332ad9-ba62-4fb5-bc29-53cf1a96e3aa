<?php

namespace App\Console\Commands\Actions\DCO\VehicleRegistration;

use Illuminate\Console\Command;
use App\Console\Commands\Action;
use Illuminate\Support\Facades\DB;

class VehicleRegisterByIdDataSource extends Action
{
    protected $signature = 'datasource:vehicleRegisterById {flowId} {parentId} {input}';

    protected $description = 'Vehicle Register By Id';

    protected $formatter = [
        "id" => "",
        "unit_id" => "",
        "soc_building_name" => "",
        "unit_flat_number" => "",
        "member_id" => "",
        "member_first_name" => "",
        "member_last_name" => "",
        "member_vehicle_detail_id" => "",
        "soc_id" => "",
        "fk_member_id" => "",
        "fk_parking_allotment_id" => "",
        "fk_parking_unit_id" => "",
        "badge_number" => "",
        "vehicle_registration_number" => "",
        "effective_date" => "",
        "vehicle_type" => "",
        "vehicle_company" => "",
        "vehicle_model_number" => "",
        "vehicle_colour" => "",
        "vehicle_registration_type" => "",
        "vehicle_owned_by" => "",
        "status" => "",
        "created_date" => "",
        "created_by" => "",
        "updated_date" => "",
        "updated_by" => "",
        "member_name" => "concat:member_first_name,member_last_name",
        "parking_soc_building_name" => "",
        "parking_unit_flat_number" => "",
        "parking_soc_building_floor" => "",
        "parking_unit_name" => "",
    ];

    protected $formatterByKeys = ['id'];

    public function apply()
    {
        $id = $this->input['id'];

        $obj = $this->tenantDB()->table("chsone_units_master AS cum")
            ->leftJoin("chsone_members_master AS cmm", "cum.unit_id", "=", "cmm.fk_unit_id")
            ->join("chsone_member_vehicle_detail AS cmvd", "cmm.id", "=", "cmvd.fk_member_id")
            ->where("cum.unit_id", $id)
            ->where("cmvd.status", 1)
            ->select(
                "cum.unit_id", "cum.soc_building_name", "cum.unit_flat_number",
                "cmm.id as member_id", "cmm.member_first_name", "cmm.member_last_name",
                "cmvd.member_vehicle_detail_id as id", "cmvd.*"
            );

        $results = $obj->get();

        // fetch parking unit details from chsone_units_master table according to $result->fk_parking_unit_id and set the values into $results array
        foreach ($results as $result) {
            $parkingUnitDetails = $this->tenantDB()->table("chsone_units_master AS cum")
                ->where("cum.unit_id", $result->fk_parking_unit_id)
                ->select("soc_building_name", DB::raw("IF(unit_flat_number IS NULL, '', unit_flat_number) AS unit_flat_number"), "soc_building_floor")
                ->first();

            $result->parking_soc_building_name = $parkingUnitDetails->soc_building_name;
            $result->parking_unit_flat_number = $parkingUnitDetails->unit_flat_number;
            $result->parking_soc_building_floor = $parkingUnitDetails->soc_building_floor;
            $result->parking_unit_name = $parkingUnitDetails->soc_building_name . "-" . $parkingUnitDetails->unit_flat_number;
        }

        $this->data = $this->format($results);
    }

    public function concat($a, $b)
    {
        return $a . " ". $b;
    }
}