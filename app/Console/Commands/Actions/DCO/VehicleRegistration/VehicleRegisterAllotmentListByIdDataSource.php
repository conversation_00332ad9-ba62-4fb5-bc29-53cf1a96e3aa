<?php

namespace App\Console\Commands\Actions\DCO\VehicleRegistration;

use Illuminate\Console\Command;
use App\Console\Commands\Action;
use App\Models\Tenants\ChsoneParkingAllotmentDetail;
use App\Models\Tenants\ChsoneUnitsMaster;

class VehicleRegisterAllotmentListByIdDataSource extends Action
{
    protected $signature = 'datasource:vehicleRegisterAllotmentListById {flowId} {parentId} {input}';

    protected $description = 'Vehicle Register Allotment List By Id';

    public function apply()
    {
        $id = $this->input['id'];
        $soc_id = $this->input['company_id'];

        $allocated_parking = $this->tenantDB()->table("chsone_parking_allotment_detail AS cpad")
            ->selectRaw("unit_id as id, CONCAT(soc_building_name, '-', COALESCE(unit_flat_number, '')) as parking_unit_name")
            ->leftJoin("chsone_units_master AS cum", "cum.unit_id", "=", "cpad.fk_parking_unit_id")
            ->where('fk_unit_id', $id)
            ->where('cpad.soc_id', $soc_id)
            // ->where('user_id', '!=', null)
            ->where(function ($q) {
                $q->where('cpad.cancel_date', '0000-00-00')
                  ->orWhere('cpad.cancel_date', '>', now());
            });

        $allocated_parking_list = $allocated_parking->get();

        // now fetch unit_id, soc_building_name, unit_flat_number available parking units from chsone_units_master table with unit_id not in results['id']
        $available_parking = $this->tenantDB()->table("chsone_units_master AS cum")
            ->selectRaw("unit_id as id, CONCAT(soc_building_name, '-', COALESCE(unit_flat_number, '')) as parking_unit_name")
            ->where('soc_id', $soc_id)
            ->whereNotIn('unit_id', $allocated_parking_list->pluck('id'))
            ->where('unit_type', 'parking')
            ->where('status', 1);

        $available_parking_list = $available_parking->get();

        $results = [
            [
                "id" => "allocated_parking",
                "parking_unit_name" => "Allocated Parking",
                "rows" => $allocated_parking_list
            ],
            [
                "id" => "available_reserved_parking",
                "parking_unit_name" => "Available Reserved Parking",
                "rows" => $available_parking_list
            ]
        ];
            
        $this->data = $results;
    }
}