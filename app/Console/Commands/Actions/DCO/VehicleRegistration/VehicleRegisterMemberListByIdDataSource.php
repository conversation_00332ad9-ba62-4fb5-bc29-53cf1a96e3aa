<?php

namespace App\Console\Commands\Actions\DCO\VehicleRegistration;

use Illuminate\Console\Command;
use App\Console\Commands\Action;

class VehicleRegisterMemberListByIdDataSource extends Action
{
    protected $signature = 'datasource:vehicleRegisterMemberListById {flowId} {parentId} {input}';

    protected $description = 'Vehicle Register Member List By Id';

    public function apply()
    {
        $id = $this->input['id'];

        $obj = $this->tenantDB()->table("chsone_members_master")
            ->selectRaw("id, 'member_first_name', 'member_last_name', CONCAT(member_first_name, ' ', member_last_name) as member_name")
            ->where('fk_unit_id', $id)
            // ->where('user_id', '!=', null)
            ->where(function ($q) {
                $q->where('cancel_date', '0000-00-00')
                  ->orWhere('cancel_date', '>', now());
            });

        $results = $obj->get();    

        $this->data = $results;
    }
}