<?php

namespace App\Console\Commands\Actions\DCO\VehicleRegistration;

use Illuminate\Console\Command;
use App\Console\Commands\Action;

class AllottedVehicleCountDataSource extends Action
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'datasource:allottedVehicleCount {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Allotted Vehicle Count Data Source';

    protected $formatter = [
        "building_unit" => "",
        "open_parking" => "",
        "shaded_parking" => "",
        "two_wheeler_owner_tenant" => "",
        "four_wheeler_owner_tenant" => "",
    ];

    protected $formatterByKeys =  ["id"];

    protected $mapper = [
        "id" => "parking_allotment.parking_allotment_id",
        "parking_number" => "parking_allotment.parking_number",
        "unit_flat_number" => "units.unit_flat_number",
        "allotment_for" => "parking_allotment.allotment_for",
        "parking_type" => "parking_allotment.parking_type",
        "vehicle_owned_by" => "vehicle_detail.vehicle_owned_by",
    ];
    /**
     * Execute the console command.
     */
    public function apply()
    {
        try    {
            $parking_number = '';
            $unit_flat_number = '';
            $parking_type = '';
            $allotment_for = '';
    
            $page = $this->input['page'] ?? 1;
            $per_page = !empty($this->input['per_page']) ? $this->input['per_page'] : ($this->hugeData ? 100000 : 10);
            $offset = ($page - 1) * $per_page;
    
            if (!empty($this->input['filters'])) {
                $parking_number =
                !empty($this->input['filters']['parking_number']) ? $this->input['filters']['parking_number'] : '';
                unset($this->input['filters']['parking_number']);
    
                $unit_flat_number =
                !empty($this->input['filters']['unit_flat_number']) ? $this->input['filters']['unit_flat_number'] : '';
                unset($this->input['filters']['unit_flat_number']);
    
                $allotment_for =
                !empty($this->input['filters']['allotment_for']) ? $this->input['filters']['allotment_for'] : '';
                unset($this->input['filters']['allotment_for']);
    
                $parking_type =
                !empty($this->input['filters']['parking_type']) ? $this->input['filters']['parking_type'] : '';
                unset($this->input['filters']['parking_type']);
            }
    
            $cancelDate = '0000-00-00';
            $currentDate = $this->getCurrentDate('database');
    
            $obj = $this->tenantDB()
                ->table('chsone_units_master AS u')
                ->selectRaw("
                        CONCAT(u.soc_building_name, '/', u.unit_flat_number) AS building_unit,
                                COALESCE(COUNT(CASE WHEN pa.parking_type = 'open parking' AND pa.status = 1 THEN pa.parking_allotment_id END), 0) AS open_parking,
                                COALESCE(COUNT(CASE WHEN pa.parking_type = 'shaded parking' AND pa.status = 1 THEN pa.parking_allotment_id END), 0) AS shaded_parking,
                        CONCAT(
                            COALESCE(COUNT(DISTINCT CASE 
                                WHEN vd.vehicle_type = '2wheeler' AND m.is_tenant = 0 AND vd.status = 1 THEN vd.member_vehicle_detail_id 
                            END), 0),
                            '|',
                            COALESCE(COUNT(DISTINCT CASE 
                                WHEN vd.vehicle_type = '2wheeler' AND m.is_tenant = 1 AND vd.status = 1 THEN vd.member_vehicle_detail_id 
                            END), 0)
                        ) AS two_wheeler_owner_tenant,
                        CONCAT(
                            COALESCE(COUNT(DISTINCT CASE 
                                WHEN vd.vehicle_type = '4wheeler' AND m.is_tenant = 0 AND vd.status = 1 THEN vd.member_vehicle_detail_id 
                            END), 0),
                            '|',
                            COALESCE(COUNT(DISTINCT CASE 
                                WHEN vd.vehicle_type = '4wheeler' AND m.is_tenant = 1 AND vd.status = 1 THEN vd.member_vehicle_detail_id 
                            END), 0)
                        ) AS four_wheeler_owner_tenant
                ")        
                ->join('chsone_members_master AS m', function ($join) {
                    $join->on('u.unit_id', '=', 'm.fk_unit_id')
                         ->on('u.soc_id', '=', 'm.soc_id');
                })
                ->join('chsone_member_vehicle_detail AS vd', function ($join) {
                    $join->on('m.id', '=', 'vd.fk_member_id')
                         ->on('vd.soc_id', '=', 'u.soc_id');
                })
                ->join('chsone_parking_allotment_detail AS pa', function ($join) {
                    $join->on('u.unit_id', '=', 'pa.fk_unit_id')
                         ->on('u.soc_id', '=', 'pa.soc_id');
                })
                ->where('u.soc_id', $this->input['company_id'])
                ->where('pa.status', 1)
                ->where('vd.status', 1)
                ->groupBy('u.soc_building_name', 'u.unit_flat_number');
    
            $count = $obj->count();
            
            $obj = $this->filter($obj);
    
            if ($parking_number) {
                $obj = $obj->whereRaw("LOWER(parking_allotment.parking_number) LIKE '%{$parking_number}%'");
            }
    
            if ($parking_type) {
                $obj = $obj->whereRaw("LOWER(parking_allotment.parking_type) LIKE '%{$parking_type}%'");
            }
    
            if ($unit_flat_number) {
                $obj = $obj->whereRaw("LOWER(units.unit_flat_number) LIKE '%{$unit_flat_number}%'");
            }
    
            if ($allotment_for) {
                $obj = $obj->whereRaw("LOWER(parking_allotment.allotment_for) LIKE '%{$allotment_for}%'");
            }
            $obj = $this->filter($obj);
            // dd($obj->get());
            $data = $obj->get()->toArray();
            $count = count($data); // or $obj->count() if you want DB count
            
    
            $obj = $obj->limit($per_page);
            $result = $obj->get();
            
            $this->data = $this->format($result->toArray());
            $this->meta['pagination']['total'] = $count;
        
        } catch (\Exception $e) {
            dd($e);
        }
    }

    public function concat($a, $b, $c=null)
    {
        if($c) {
            return $a . ' / ' . $b;
        }
         elseif($b) {
            $b = ucfirst(str_replace('owner', ' Owner', $b));
            $b = ucfirst(str_replace('tenant', ' Tenant', $b));
            return $a ." ( ". $b . " ) ";
        }
    }
}
