<?php

namespace App\Console\Commands\Actions\DCO\VehicleRegistration;

use Illuminate\Console\Command;
use App\Console\Commands\Action;

class DeleteVehicleRegisterByIdDataSource extends Action
{
    protected $signature = 'datasource:deleteVehicleRegisterById {flowId} {parentId} {input}';

    protected $description = 'Delete Vehicle Registration List By Id';

    public function apply()
    {
        $id = $this->input['id'];

        $obj = $this->tenantDB()->table("chsone_member_vehicle_detail")
            ->where('member_vehicle_detail_id', $id)
            ->update(['status' => 0]);

        if($obj){
            $this->status = 'success';
            $this->message = 'Vehicle Registration Deleted Successfully';
            $this->statusCode = 200;
            $this->data = [];
        }
        else{
            $this->status = 'error';
            $this->message = 'Vehicle Registration Not Deleted';
            $this->statusCode = 400;
            $this->data = [];
        }
    }
}