<?php

namespace App\Console\Commands\Actions\DCO\VehicleRegistration;

use Illuminate\Console\Command;
use App\Console\Commands\Action;

class UpdateVehicleRegisterByIdDataSource extends Action
{
    protected $signature = 'datasource:updateVehicleRegisterById {flowId} {parentId} {input}';

    protected $description = 'Update Vehicle Registration List By Id';

    public function apply()
    {
        $unit_id = $this->input['id'];
        $id = $this->input['detail_id'];

        // fetch the details of chsone_parking_allotment_detail according to the fk_unit_id and fk_parking_unit_id
        $parking_allotment_detail = $this->tenantDB()->table("chsone_parking_allotment_detail")
            ->where('fk_unit_id', $unit_id)
            ->first();

        $fk_member_id = $this->input['fk_member_id'];
        $fk_parking_allotment_id = $parking_allotment_detail->parking_allotment_id;
        $fk_parking_unit_id = $this->input['fk_parking_unit_id'];
        $badge_number = $this->input['badge_number'];
        $vehicle_registration_number = $this->input['vehicle_registration_number'];
        $vehicle_registration_type = $this->input['vehicle_registration_type'];
        $vehicle_type = $this->input['vehicle_type'];
        $effective_date = $this->input['effective_date'];
        $vehicle_company = $this->input['vehicle_company'];
        $vehicle_model_number = $this->input['vehicle_model_number'];
        $vehicle_colour = $this->input['vehicle_colour'];

        $obj = $this->tenantDB()->table("chsone_member_vehicle_detail")
            ->where('member_vehicle_detail_id', $id)
            ->update([
                'fk_member_id' => $fk_member_id,
                'fk_parking_allotment_id' => $fk_parking_allotment_id,
                'fk_parking_unit_id' => $fk_parking_unit_id,
                'badge_number' => $badge_number,
                'vehicle_registration_number' => $vehicle_registration_number,
                'vehicle_registration_type' => $vehicle_registration_type,
                'vehicle_type' => $vehicle_type,
                'effective_date' => $effective_date,
                'vehicle_company' => $vehicle_company,
                'vehicle_model_number' => $vehicle_model_number,
                'vehicle_colour' => $vehicle_colour,
                'updated_date' => date('Y-m-d H:i:s'),
                'updated_by' => $this->input['user_id'] ?? 0
            ]);

        if($obj){
            $this->status = 'success';
            $this->message = 'Vehicle Registration Updated Successfully';
            $this->statusCode = 200;
            $this->data = [];
        }
        else{
            $this->status = 'error';
            $this->message = 'Vehicle Registration Not Updated';
            $this->statusCode = 400;
            $this->data = [];
        }
    }
}