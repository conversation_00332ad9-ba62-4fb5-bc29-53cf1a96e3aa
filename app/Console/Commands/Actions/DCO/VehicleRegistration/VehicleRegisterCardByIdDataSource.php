<?php

namespace App\Console\Commands\Actions\DCO\VehicleRegistration;

use Illuminate\Console\Command;
use App\Console\Commands\Action;

class VehicleRegisterCardByIdDataSource extends Action
{
    protected $signature = 'datasource:vehicleRegisterCardById {flowId} {parentId} {input}';

    protected $description = 'Vehicle Register Card By Id';

    public function apply()
    {
        $id = $this->input['id'];

        $obj = $this->tenantDB()->table("chsone_units_master")
            ->where("unit_id", $id)
            ->first();

        $obj->unit_number = $obj->soc_building_name . "-" . $obj->unit_flat_number;

        $this->data = $obj;
    }
}