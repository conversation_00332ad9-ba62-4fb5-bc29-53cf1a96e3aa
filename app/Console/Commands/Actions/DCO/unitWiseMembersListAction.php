<?php

namespace App\Console\Commands\Actions\DCO;

use Illuminate\Console\Command;
use App\Console\Commands\Action;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Route;

class unitWiseMembersListAction extends Action
{
    protected $signature = 'datasource:unitWiseMembersList {flowId} {parentId} {input}';
    protected $description = 'Unit Wise Members List';

    public function apply()
    {
        $currentTab = $this->input['current_tab'] ?? 'approved';
        $currentDate = $this->getCurrentDate('database');
        $cancelDate = "0000-00-00";

        $columns = $this->getMemberColumns();

        $query = $this->buildBaseQuery($columns, $currentDate, $cancelDate);

        // Fetch the results
        // $results = $query->get()->toArray();
        $results = $query->get();

        // Extract all member IDs in bulk
        // $allMemberIds = array_unique(array_merge(...array_map(fn($result) => explode(',', $result->member_id), $results)));
        $allMemberIds = array_unique(
            array_merge(
                ...array_map(fn($result) => explode(',', $result->member_id), $results->toArray())
            )
        );

        // Fetch member details in a single query
        $memberDetails = $this->fetchAllMemberDetails($allMemberIds);

        // Map member details back to results
        // Group member details by their `member_id` for easier mapping
        // $memberDetailsGrouped = collect($memberDetails)->keyBy('member_id')->toArray();
        $memberDetailsGrouped = collect($memberDetails)->keyBy('member_id');

        // Flatten `member_details` into each result
        // add an 'all' option in the result
        $results->push((object) ['id' => 'all', 'member_id' => 'all', 'approved' => 'all', 'fk_unit_id' => 'all', 'building_unit' => 'All', 'user_id' => '', 'status' => 'all', 'soc_building_name' => 'all', 'unit_flat_number' => 'all']);
        // sort results by id
        // $results = $results->sortBy('id');

        // Custom sort: put 'all' row at the top
        $results = $results->sortBy(function ($item) {
            return $item->id === 'all' ? -1 : (int) $item->id;
        });

        foreach ($results as $result) {
            $memberId = $result->member_id; // Assuming `member_id` is a single value
            if ($memberDetailsGrouped->has($memberId)) {
                $memberDetails = $memberDetailsGrouped->get($memberId);
    
                // Convert result object to an array for merging
                $resultArray = (array) $result;
    
                // Merge member details into the result row
                $this->data[] = array_merge($resultArray, (array) $memberDetails);
            } else {
                $this->data[] = (array) $result; // If no member details found, keep the original result
            }
        }
    }

    private function getMemberColumns()
    {
        $columns = 'member_master.fk_unit_id AS id,
                    GROUP_CONCAT(member_master.id) AS member_id,
                    GROUP_CONCAT(member_master.approved) AS approved,
                    member_master.fk_unit_id AS fk_unit_id,
                    CONCAT(units.soc_building_name, "-", units.unit_flat_number) AS building_unit,
                    member_master.fk_unit_id,
                    member_master.user_id AS user_id,
                    member_master.status AS status,
                    units.soc_building_name AS soc_building_name,
                    units.unit_flat_number AS unit_flat_number';

        return $columns;
    }

    private function buildBaseQuery($columns, $currentDate, $cancelDate)
    {
        return $this->tenantDB()->table('chsone_members_master AS member_master')
            ->selectRaw($columns)
            ->join('chsone_units_master AS units', 'units.unit_id', '=', 'member_master.fk_unit_id')
            ->join('chsone_member_type_master AS member_type', 'member_master.member_type_id', '=', 'member_type.member_type_id')
            ->where('member_type.member_type_name', 'Primary')
            ->where('member_master.status', 1)
            ->where('units.is_allotted', '1')
            ->groupBy('member_master.fk_unit_id');
    }

    private function fetchAllMemberDetails(array $memberIds)
    {
        return $this->tenantDB()->table('chsone_members_master AS member_master')
            ->leftJoin('chsone_member_type_master AS member_type', 'member_type.member_type_id', '=', 'member_master.member_type_id')
            ->select(
                'id as member_id',
                'user_id',
                'salute',
                'member_first_name',
                'member_last_name',
                DB::raw("CONCAT(member_first_name, ' ', member_last_name) AS member_display_name"),
                'member_type_name',
            )
            ->whereIn('id', $memberIds)
            ->where('member_type_name', 'Primary')
            ->get()
            ->toArray();
    }

    private function groupBy($array, $key)
    {
        $grouped = [];
        foreach ($array as $item) {
            $grouped[$item->$key] = $item;
        }
        return $grouped;
    }
}
