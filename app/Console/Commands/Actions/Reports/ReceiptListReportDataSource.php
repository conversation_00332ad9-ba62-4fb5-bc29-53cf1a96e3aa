<?php

namespace App\Console\Commands\Actions\Reports;

use Illuminate\Console\Command;
use App\Console\Commands\Action;
use Illuminate\Support\Facades\Route;

class ReceiptListReportDataSource extends Action
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'datasource:ReceiptListReport {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Get the receipts list';

    protected $formatter = [
        'id' => '',
        'payment_date' => '',
        'receipt_number' => '',
        'payment_mode' => '',
        'received_from'   => '',
        'invoice_number' => '',
        'building_unit' => 'concat:soc_building_name,unit_flat_number',
        'payment_amount' => '',
        'transaction_status' => '',
        'payment_reference' => 'concatpaymentref:payment_mode,transaction_reference,payment_instrument'
    ];

    protected $formatterByKeys = ['id'];

    protected $mapper = [
        'id' => 'id',
    ];

    /**
     * Execute the console command.
     */
    public function apply()
    {
        $society_id = $this->input['company_id'];
        $from_date = date('Y-m-01');
        $to_date = date('Y-m-t');
    
        if (!empty($this->input['filters'])) {
            $date = $this->getFromAndToDate($this->input['filters']);
            $from_date = $date[0];
            $to_date = $date[1];
        }
    
        $filters = $this->input['filters'] ?? [];
        $currentRoute = Route::current();

        // Get the route URI pattern (e.g., "member/register/{id}")
        $routeUri = $currentRoute->uri();
        if ($routeUri == 'api/admin/income-details/receiptReport/download/{type}') {
            $this->hugeData = true;
        }
        // Start building query
            $query = "
            SELECT
            ipt.*,
            ipt.status,
            ipt.transaction_status,
            ipt.payment_mode,
            -- if there's no matching unit, this will be '' rather than NULL
            COALESCE(CONCAT(um.soc_building_name, '/', um.unit_flat_number), '') AS society_unit_name
            FROM income_invoice_payment_tracker AS ipt
            LEFT JOIN chsone_units_master AS um
            ON um.unit_id = ipt.unit_id
            WHERE ipt.soc_id = {$society_id}
            AND ipt.payment_date BETWEEN '{$from_date}' AND '{$to_date}'
        ";

            // CASE WHEN ipt.status = 'P' AND ipt.transaction_status = 'complete' THEN 'Submitted' ELSE 'Cleared' END as transaction_status
            //  CASE WHEN ipt.payment_mode = 'cashtransfer' THEN 'Electronic Fund Transfer' ELSE ipt.payment_mode END as payment_mode,

        
        if (isset($filters['unit_id']) && is_array($filters['unit_id']) && count($filters['unit_id']) > 0) {
            $query .= " AND ipt.unit_id IN (" . implode(',', $filters['unit_id']) . ")";
        } elseif (isset($filters['unit_id']) && is_numeric($filters['unit_id'])) {
            $query .= " AND ipt.unit_id = " . $filters['unit_id'];
         } 

        if (!empty($filters['receipt_number'])) {
            $receiptNumber = addslashes($filters['receipt_number']);
            $query .= " AND ipt.receipt_number LIKE '%$receiptNumber%'";
        }
    
        if (!empty($filters['transaction_reference'])) {
            $transactionReference = addslashes($filters['transaction_reference']);
            $query .= " AND ipt.transaction_reference LIKE '%$transactionReference%'";
        }
    
        if (!empty($filters['payment_date'])) {
            $paymentDate = addslashes($filters['payment_date']);
            $query .= " AND DATE(ipt.payment_date) = '$paymentDate'";
        }

        if (!empty($filters['payment_mode'])) {
            $paymentMode = addslashes($filters['payment_mode']);
            $query .= " AND ipt.payment_mode = '$paymentMode'";
        }
    
        if (!empty($filters['invoice_number'])) {
            $invoiceNumber = addslashes($filters['invoice_number']);
            $query .= " AND ipt.invoice_number LIKE '%$invoiceNumber%'";
        }
    
        if (!empty($filters['unit_flat_number'])) {
            $unitFlatNumber = addslashes($filters['unit_flat_number']);
            $query .= " AND um.unit_flat_number LIKE '%$unitFlatNumber%'";
        }

        if (!empty($filters['bill_type'])) {
            $billType = addslashes($filters['bill_type']);
            $query .= " AND ipt.bill_type = '$billType'";
        }

        if (!empty($filters['status'])) {
            $status = addslashes($filters['status']);
            $query .= " AND ipt.status = '$status'";
        }
    
        if (!empty($filters['member_name'])) {
            $memberName = addslashes($filters['member_name']);
            $query .= " AND ipt.received_from LIKE '%$memberName%'";
        }

        if (!empty($filters['unit_flat_number'])) {
            $unitFlatNumber = addslashes($filters['unit_flat_number']);
            $query .= " AND um.unit_flat_number LIKE '%$unitFlatNumber%'";
        }
    
        try {
            $query .= " ORDER BY ipt.payment_date DESC";
            $result = $this->tenantDB()->select($query);
        } catch (\Exception $e) {
            dd($e);
        }
    
        // Execute and format
       

        // check status of the result and set the if N = Bounced, Y = Cleared, P = Submitted, R = Received, reversed = Reversed
        foreach ($result as $key => $value) {
            if ($value->status == 'N') {
                $result[$key]->transaction_status = 'Bounced';
            } elseif ($value->status == 'Y') {
                $result[$key]->transaction_status = 'Cleared';
            } elseif ($value->status == 'P') {
                $result[$key]->transaction_status = 'Submitted';
            } elseif ($value->status == 'R') {
                $result[$key]->transaction_status = 'Received';
            } elseif ($value->status == 'reversed') {
                $result[$key]->transaction_status = 'Reversed';
            }

            if ($value->payment_mode == 'cash') {
                $result[$key]->payment_mode = 'Cash';
            } elseif ($value->payment_mode == 'cheque') {
                $result[$key]->payment_mode = 'Cheque';
            } elseif ($value->payment_mode == 'cashtransfer') {
                $result[$key]->payment_mode = 'Electronic Fund Transfer';
            } elseif ($value->payment_mode == 'YESPG') {
                $result[$key]->payment_mode = 'PG Yes Bank';
            } elseif ($value->payment_mode == 'srvybecollect') {
                $result[$key]->payment_mode = 'Ecollect Yes Bank';
            } elseif ($value->payment_mode == 'srvpaytm') {
                $result[$key]->payment_mode = 'Paytm';
            } elseif ($value->payment_mode == 'srvmobikwik') {
                $result[$key]->payment_mode = 'Mobikwik';
            } elseif($value->payment_mode == 'srvmobikwikpg'){
                $result[$key]->payment_mode = 'PG Mobikwik';
            } elseif($value->payment_mode == 'CASHFREEPG'){
                $result[$key]->payment_mode = 'PG Cashfree';
            } elseif($value->payment_mode == 'HDFCPG'){
                $result[$key]->payment_mode = 'PG HDFC';
            } elseif($value->payment_mode == 'ATOM_PG'){
                $result[$key]->payment_mode = 'PG ATOM';
            }
        }

        $summary = [
            'paid_amount_total' => array_sum(array_column($result, 'payment_amount')),
            'tds_deducted_total' => array_sum(array_column($result, 'tds_deducted')),
            'write_off_total' => array_sum(array_column($result, 'writeoff_amount')),
        ];

        // set the count of the $arrUnitStatementDetail
        $count = count($result);
        $this->meta['pagination']['total'] = $count;

        // set the pagination data
        $page = isset($this->input['page']) ? $this->input['page'] : 1;
        $per_page = !empty($this->input['per_page']) ? $this->input['per_page'] : ($this->hugeData ? 100000 : 10);
        $offset = ($page - 1) * $per_page;

        // need to set the pagination only to the $arrUnitStatementDetail
        $result = array_slice($result, $offset, $per_page);

        $final = [
            $result,
            [$summary]
        ];
        $this->data = $final;
        // $this->summary = $summary;
    }    

    public function concat($soc_building_name, $unit_flat_number)
    {
        return $soc_building_name . ' / ' . $unit_flat_number;
    }

    public function concatpaymentref($payment_mode, $transaction_reference, $payment_intstrument){
        if($payment_mode == 'cheque'){
            return $payment_mode . ':' . $transaction_reference . ' / ' . $payment_intstrument;
        }else{
            return $transaction_reference;
        }
    }

    public function getFromAndToDate($inputs){
        // $date = $inputs['transaction_date'];
        // $dateRange = explode(',', $date);
        $dateRange = $inputs;
        $from_date = $dateRange['startDate'] ?? date("Y-m-01");
        $to_date = $dateRange['endDate'] ?? date("Y-m-t");
        return array($from_date, $to_date);
    }
}
