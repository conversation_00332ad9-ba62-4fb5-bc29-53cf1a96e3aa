<?php

namespace App\Console\Commands\Actions\Reports;

use Illuminate\Support\Facades\DB;
use App\Console\Commands\Action;

class ExpenseReportDataSource extends Action
{
    protected $signature = 'datasource:expenseReport {flowId} {parentId} {input}';
    protected $description = 'Expense Report Data Source';

    protected $formatter = [
        'vendor_id' => '',
        'is_rcm' => '',
        'vendor_name' => '',
        'cp_et_name' => '',
        'vendor_name_report' => '',
        'particular_count' => '',
        'particulars' => '',
        'et_name' => '',
        'gross_bill_amount' => '', // New field
        'net_bill_amount' => '',   // New field
        'payment_count' => '',
        'paid_date' => '',
        'paid_amount' => '',
        'vendor_service_regn' => '',
        'vendor_bill_type_purchase' => '',
        'vendor_bill_num' => '',
        'vendor_bill_date' => '',
        'vendor_bill_amount' => '',
        'vendor_bill_writeoff' => '',
        'vendor_bill_tds' => '',
        'status' => '',
        'payment_status' => '',
        'particulars_igst_rate' => '',
        'particulars_sgst_rate' => '',
        'particulars_cgst_rate' => '',
        'particulars_igst_amount' => '',
        'particulars_sgst_amount' => '',
        'particulars_cgst_amount' => ''
    ];

    public function apply()
    {
        $fromDate = $this->input['filters']['startDate'] ?? date('Y-m-01');
        $toDate = $this->input['filters']['endDate'] ?? date('Y-m-t');
        $vendor = $this->input['filters']['vendor'] ?? '';

        // Subquery for particulars with CASE statements to split amounts
        $particularsSubquery = $this->tenantDB()->table('chsone_vendor_bill_particulars as c')
            ->select([
                'c.bill_id',
                DB::raw('COUNT(*) as particular_count'),
                DB::raw('GROUP_CONCAT(amount ORDER BY particulars_id ASC SEPARATOR ",") as particulars_amount'),
                DB::raw('GROUP_CONCAT(et_id ORDER BY particulars_id ASC SEPARATOR ",") as et_id'),
                DB::raw('GROUP_CONCAT(total_amount ORDER BY particulars_id ASC SEPARATOR ",") as particulars_total_amount'),
                DB::raw('SUBSTRING_INDEX(GROUP_CONCAT(total_amount ORDER BY particulars_id ASC SEPARATOR ","), ",", 1) as gross_bill_amount'),
                DB::raw('SUBSTRING_INDEX(GROUP_CONCAT(total_amount ORDER BY particulars_id ASC SEPARATOR ","), ",", -1) as net_bill_amount'),
                DB::raw('GROUP_CONCAT(tax_igst ORDER BY particulars_id ASC SEPARATOR ",") as particulars_igst_rate'),
                DB::raw('GROUP_CONCAT(tax_sgst ORDER BY particulars_id ASC SEPARATOR ",") as particulars_sgst_rate'),
                DB::raw('GROUP_CONCAT(tax_cgst ORDER BY particulars_id ASC SEPARATOR ",") as particulars_cgst_rate'),
                DB::raw('GROUP_CONCAT(tax_igst_amount ORDER BY particulars_id ASC SEPARATOR ",") as particulars_igst_amount'),
                DB::raw('GROUP_CONCAT(tax_sgst_amount ORDER BY particulars_id ASC SEPARATOR ",") as particulars_sgst_amount'),
                DB::raw('GROUP_CONCAT(tax_cgst_amount ORDER BY particulars_id ASC SEPARATOR ",") as particulars_cgst_amount'),
                DB::raw('GROUP_CONCAT(particular ORDER BY particulars_id ASC SEPARATOR ",") as particulars')
            ])
            ->groupBy('c.bill_id');

        // Subquery for payments
        $paymentsSubquery = $this->tenantDB()->table('chsone_vendor_bill_payment_details as e')
            ->select([
                'e.vendor_bill_id',
                DB::raw('COUNT(*) as payment_count'),
                DB::raw('SUM(e.vendor_bill_payment_amount) as paid_amount'),
                DB::raw('MAX(e.vendor_bill_payment_date) as paid_date')
            ])
            ->where('e.status', 1)
            ->groupBy('e.vendor_bill_id');

        // Main query
        $query = $this->tenantDB()->table('chsone_vendor_bill_master as b')
            ->leftJoin('chsone_expense_tracker as cp_et', 'cp_et.et_id', '=', 'b.et_id')
            ->leftJoin('chsone_grp_ledger_tree as cpledger', 'cp_et.et_ledger_account_id', '=', 'cpledger.ledger_account_id')
            ->leftJoin('chsone_vendors_master as a', function($join) {
                $join->on('a.vendor_id', '=', 'b.vendor_id')
                     ->where('b.status', '=', 1);
            })
            ->leftJoinSub($particularsSubquery, 'd', 'b.vendor_bill_id', '=', 'd.bill_id')
            ->leftJoin('chsone_expense_tracker as et', 'd.et_id', '=', 'et.et_id')
            ->leftJoin('chsone_grp_ledger_tree as ledger', 'et.et_ledger_account_id', '=', 'ledger.ledger_account_id')
            ->leftJoinSub($paymentsSubquery, 'f', 'b.vendor_bill_id', '=', 'f.vendor_bill_id')
            ->select([
                'a.vendor_id',
                'b.is_rcm',
                'a.vendor_name',
                'cpledger.ledger_account_name as cp_et_name',
                DB::raw('CONCAT(
                    COALESCE(a.vendor_name, ""),
                    " ",
                    IF(
                        COALESCE(b.details, "") = "",
                        CONCAT("Expense - Bill No. ", b.vendor_bill_num, " generated"),
                        COALESCE(b.details, "")
                    )
                ) as vendor_name_report'),
                'd.particular_count',
                'd.particulars',
                'ledger.ledger_account_name as et_name',
                'd.gross_bill_amount', // New field
                'd.net_bill_amount',   // New field
                'f.payment_count',
                'f.paid_date',
                'f.paid_amount',
                'a.vendor_service_regn',
                'b.vendor_id',
                'b.vendor_bill_id',
                'b.vendor_bill_type_purchase',
                'b.vendor_bill_num',
                'b.vendor_bill_date',
                'b.vendor_bill_amount',
                'b.vendor_bill_writeoff',
                'b.vendor_bill_tds',
                'b.status',
                'b.payment_status',
                'd.particulars_igst_rate',
                'd.particulars_sgst_rate',
                'd.particulars_cgst_rate',
                'd.particulars_igst_amount',
                'd.particulars_sgst_amount',
                'd.particulars_cgst_amount'
            ])
            ->where('b.status', 1)
            ->whereBetween('b.vendor_bill_date', [$fromDate, $toDate])
            ->orderByDesc('b.vendor_bill_id');

        if ($vendor) {
            $query->where('a.vendor_name', $vendor);
        }

        $result = $query->get();
        
        // Format the amounts to ensure they are proper numbers
        foreach ($result as &$row) {
            $row->gross_bill_amount = floatval($row->gross_bill_amount);
            $row->net_bill_amount = floatval($row->net_bill_amount);
        }
        
        $this->data = $this->format($result->toArray());
    }
}
