<?php

namespace App\Console\Commands\Actions\Reports;

use App\Console\Commands\Action;

class MembersInvoiceDetailReportDownloadDataSource extends Action
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'datasource:membersInvoiceDetailReportDownload {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Get Members Invoice Detail Report Data for Download';

    protected $formatter = [
        'unit_name' => '',
        'bill_to' => '',
        'gstin' => '',
        'invoice_number' => '',
        'maintenance_fee' => '',
        'period' => '',
        'sinking_fund' => '',
        'interest' => '',
        'tax' => '',
        'invoice_amount' => '',
        'p_arrears' => '',
        'i_arrears' => '',
        'payable' => '',
        'receipt' => '',
        'net_due' => '',
        'advance_credit' => ''
    ];

    protected $formatterByKeys = [];

    protected $mapper = [];

    /**
     * Execute the console command.
     */
    public function apply()
    {
        // Call the existing members invoice detail report datasource
        $membersInvoiceDetailData = $this->action('datasource:membersInvoiceDetailReport', $this->pointer, $this->input);
        
        // Format the data for download
        $this->data = $this->format($membersInvoiceDetailData);
    }
}
