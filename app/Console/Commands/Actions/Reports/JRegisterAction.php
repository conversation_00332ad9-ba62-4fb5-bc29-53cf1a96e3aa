<?php

namespace App\Console\Commands\Actions\Reports;

use Illuminate\Console\Command;
use App\Console\Commands\Action;
use Illuminate\Support\Facades\DB;

class JRegisterAction extends Action
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'datasource:jRegisterReportPrint {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'J Register Report List';

    protected $formatter = [
        'id' => '',
        'full_name' => '',
        'address' => '',
        'unit_id' => '',
        'member_type_name' => ''
    ];

    protected $formatterByKeys = ['id'];

    protected $mapper = [
        "id" => "share_cert.id"
    ];

    /**
     * Execute the console command.
     */
    public function apply()
    {
        $obj = $this->tenantDB()->table('chsone_member_share_certificate AS share_cert')
                    ->join(
                        'chsone_members_master AS member_master',
                        'share_cert.membership_id',
                        '=',
                        'member_master.id'
                    )
                    ->join(
                        'chsone_member_type_master AS member_type',
                        'member_master.member_type_id',
                        '=',
                        'member_type.member_type_id'
                    )
                    ->selectRaw("
                        share_cert.id,
                        share_cert.full_name,
                        share_cert.address,
                        share_cert.unit_id,
                        member_type.member_type_name
                    ")
                    ->where('share_cert.status', "1");
        $result = $obj->get();
        $this->data = $this->format($result->toArray());
    }
}
