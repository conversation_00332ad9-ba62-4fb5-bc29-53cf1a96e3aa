<?php

namespace App\Console\Commands\Actions\Reports;

use Illuminate\Console\Command;
use App\Console\Commands\Action;
use Illuminate\Support\Facades\DB;
use App\Models\Tenants\ChsoneAccountsMaster;
use App\Models\Tenants\ChsoneLedgerTransaction as LedgerTxn;
use App\Models\Tenants\ChsoneGrpLedgerTree as GrpLedgTree;
use Carbon\Carbon;

class BankRecoReportDataSource extends Action
{
    protected $signature = 'datasource:bankRecoReport {flowId} {parentId} {input}';

    protected $description = 'Bank Reconciliation Report Data Source';

    protected $formatter = [
        'sr_no' => '',
        'reconciliation_status' => '',
        'from_account' => '',
        'date' => '',
        'particulars_narration' => '',
        'payment_mode' => '',
        'payment_reference' => '',
        'bank_date' => '',
        'deposit' => '',
        'withdrawal' => '',
        'type' => ''
    ];

    protected $filterDropDown = [];

    public function apply()
    {
        try {
            $from_date = date('Y-m-01');
            $to_date = date('Y-m-t');
            $soc_id    = $this->input['company_id'] ?? null;
            $filters = $this->input['filters'] ?? [];
            
            
            // Initialize filters with proper default values
            $typeKey = $filters['type'] ?? null;
            $bank_accounts = $filters['filter_by'] ?? null;

            $filter = array();
            $filter['from_date'] = $from_date;
            $filter['to_date'] = $to_date;
            $filter['filter_by'] = $bank_accounts;
            $filter['type'] = $typeKey;

            $soc_id= $this->input['company_id'] ?? null;

            if (!empty($this->input['filters'])) {
                $date = $this->getFromAndToDate($this->input['filters']);
                $from_date = $date[0];
                $to_date = $date[1];
            }
            
            // Validate essential parameters
            if (empty($soc_id)) {
                $this->status = 'error';
                $this->message = 'Company ID is required';
                $this->statusCode = 400;
                $this->data = [];
                return;
            }

            // Get bank details
            $arrBankDetail = $this->getDefaultBankAccount(['soc_id' => $soc_id]);
            
            // Set default bank if filter_by is not provided
            if (empty($filter['filter_by']) && !empty($arrBankDetail['ledger_account_id'])) {
                $filter['filter_by'] = $arrBankDetail['ledger_account_id'];
            }

            // Initialize data structure
            $data = [
                'filter' => [
                    'from_date' => $from_date,
                    'to_date' => $to_date,
                    'filter_by' => $filter['filter_by'],
                    'type' => $filter['type'],
                    'date_filter_by' => $filter['date_filter_by'] ?? 1
                ],
                'meta' => ['report_header' => 'Bank Reco Statement'],
                'data' => [],
                'summary' => [],
                'soc_id' => $soc_id
            ];

            $flat = array_merge(
                $data,
                $data['filter']
              );
              
            // Get bank transactions
            $dataArr = $this->bankRecoReport($flat);

            if (isset($dataArr['error']) && $dataArr['error']) {
                $this->status = 'error';
                $this->message = $dataArr['message'];
                $this->statusCode = 400;
                $this->data = [];
                return;
            }

            if (empty($dataArr['data']['data']->items)) {
                $this->status = 'error';
                $this->message = 'No records found';
                $this->statusCode = 404;
                $this->data = [];
                return;
            }

            $this->status = 'success';
            $this->message = 'Bank Reconciliation Report retrieved successfully';
            $this->statusCode = 200;
            $this->data = $dataArr['data']['data']->items;

        } catch (\Exception $e) {
            dd($e);
            $this->status = 'error';
            $this->message = 'An error occurred: ' . $e->getMessage();
            $this->statusCode = 500;
            $this->data = [];
        }
    }

    public function getDefaultBankAccount($data = array())
    {
        $bankDetail = $this->tenantDB()
            ->table('chsone_accounts_master')
            ->where('soc_id', $data['soc_id'])
            ->where('default_account', 1)
            ->first();
        
        if ($bankDetail) {
            $bankDetail = json_decode(json_encode($bankDetail), true);
            
            return [
                'ledger_account_id' => $bankDetail['ledger_account_id'] ?? null,
                'ledger_account_name' => $bankDetail['account_name'] ?? '',
                'account_name' => strtoupper(trim($bankDetail['account_name'] ?? '')),
                'account_number' => trim($bankDetail['account_number'] ?? ''),
                'bank_name' => strtoupper(trim($bankDetail['bank_name'] ?? '')),
                'branch' => strtoupper(trim($bankDetail['branch'] ?? '')),
                'bank_ifsc' => strtoupper(trim($bankDetail['bank_ifsc'] ?? ''))
            ];
        }
        return [];
    }

    public function getBankledger($context = 'bank', $soc_id = null)
    {
        if (empty($soc_id)) {
            $soc_id = $this->input['company_id'];
        }

        $bankledger = $this->tenantDB()
            ->table('chsone_grp_ledger_tree')
            ->where('soc_id', $soc_id)
            ->where('context', $context)
            ->where('entity_type', 'ledger')
            ->get();

        if (!$bankledger->isEmpty()) {
            return $bankledger->toArray();
        }

        return false;
    }

    public function getbankTransaction($bank_ledger_id, $end_date, $start_date, $customdatefilter = 0, $type = null, $soc_id = null)
    {
        $final_array = [];
        if (!empty($customdatefilter)) {
            $key1 = ($customdatefilter == 1) ? 'transaction_date' : 'value_date';
        }
        
        if (empty($soc_id)) {
            $soc_id = $this->input['company_id'];
        }

        $query = $this->tenantDB()->table('chsone_ledger_transactions')
            ->where('soc_id', $soc_id)
            ->where('ledger_account_id', $bank_ledger_id)
            ->where('is_opening_balance', 0)
            ->where(function($q) {
                $q->where('is_cancelled', 0)
                  ->orWhereNull('is_cancelled');
            });
        if (isset($type)) {
            if ($type == 2) { //unreconciled
                $query->where($key1, '>=', $start_date)
                      ->where($key1, '<=', $end_date)
                      ->where('is_reconciled', 0)
                      ->orderBy($key1, 'ASC');
                
                $final_array = $query->get()->toArray();
            } 
            else if ($type == 1) { //reconciled
                $key1 = !empty($customdatefilter) ? $key1 : 'value_date';
                
                $query->where($key1, '>=', $start_date)
                      ->where($key1, '<=', $end_date)
                      ->where('is_reconciled', 1)
                      ->orderBy($key1, 'ASC');
                
                $final_array = $query->get()->toArray();
            } 
            else {
                $key1 = !empty($customdatefilter) ? $key1 : 'transaction_date';
                $key2 = !empty($customdatefilter) ? $key1 : 'value_date';

                // Get reconciled transactions
                $reconciledQuery = clone $query;
                $reconciledTransactions = $reconciledQuery->where('transaction_date', '>=', $start_date)
                    ->where('transaction_date', '<=', $end_date)
                    ->where('is_reconciled', 1)
                    ->orderBy('transaction_date', 'ASC')
                    ->get();

                // Get unreconciled transactions
                $unreconciledQuery = clone $query;
                $unreconciledTransactions = $unreconciledQuery->where('transaction_date', '>=', $start_date)
                    ->where('transaction_date', '<=', $end_date)
                    ->where('is_reconciled', 0)
                    ->orderBy('transaction_date', 'ASC')
                    ->get();

                $final_array = array_merge(
                    $reconciledTransactions->toArray(),
                    $unreconciledTransactions->toArray()
                );
            }
        }

        $ledger_names = $this->getCounterEntryLedgerName($soc_id, $final_array);
        $final_array = json_decode(json_encode($final_array), true);
        foreach ($final_array as $k => &$item) {
            $item['from_account'] = $ledger_names[$k]['name'];
        }

        return $final_array;
    }

    /**
     * Get counter entry ledger names for transactions
     *
     * @param int $soc_id Society ID
     * @param array $transactions Array of transactions
     * @return array Array of ledger names with their IDs
     */
    protected function getCounterEntryLedgerName(int $soc_id, array $transactions): array
    {
        if (empty($transactions)) {
            return [];
        }

        $ledgerArray = [];
        $transactionIds = [];
        $ledgerAccountIds = [];
        $transactions = json_decode(json_encode($transactions), true);
        // Collect all transaction IDs and ledger account IDs
        foreach ($transactions as $transaction) {
            $transactionIds[] = $transaction['txn_id'];
            if (!empty($transaction['ledger_account_id'])) {
                $ledgerAccountIds[] = $transaction['ledger_account_id'];
            }
        }

        // Get all related counter entries in one query
        $counterEntries = LedgerTxn::where('soc_id', $soc_id)
            ->whereIn('txn_from_id', $transactionIds)
            ->get()
            ->keyBy('txn_from_id')
            ->toArray();

        // Get all related ledger accounts in one query
        $ledgerAccounts = GrpLedgTree::whereIn('ledger_account_id', $ledgerAccountIds)
            ->get()
            ->keyBy('ledger_account_id')
            ->toArray();

        foreach ($transactions as $transaction) {
            $ledgerAccountName = '';
            
            // Handle transactions without txn_from_id
            if (empty($transaction['txn_from_id'])) {
                if (isset($counterEntries[$transaction['txn_id']])) {
                    $counterEntry = $counterEntries[$transaction['txn_id']];
                    $ledgerAccountName = $this->getLedgerAccountName($counterEntry, $ledgerAccounts);
                    
                    $ledgerArray[] = [
                        'name' => 'To ' . $ledgerAccountName,
                        'ledger_id' => $counterEntry['ledger_account_id']
                    ];
                } else {
                    $ledgerArray[] = [
                        'name' => 'For ' . ($transaction['ledger_account_name'] ?? ''),
                        'ledger_id' => $transaction['ledger_account_id']
                    ];
                }
            } else {
                // Handle transactions with txn_from_id
                $counterEntry = LedgerTxn::where('soc_id', $soc_id)
                    ->where('txn_id', $transaction['txn_from_id'])
                    ->first();

                if ($counterEntry) {
                    $ledgerAccountName = $this->getLedgerAccountName(
                        $counterEntry->toArray(),
                        $ledgerAccounts
                    );
                    
                    $ledgerArray[] = [
                        'name' => 'By ' . $ledgerAccountName,
                        'ledger_id' => $counterEntry['ledger_account_id']
                    ];
                } else {
                    $ledgerArray[] = [
                        'name' => 'For ' . ($transaction['ledger_account_name'] ?? ''),
                        'ledger_id' => $transaction['ledger_account_id']
                    ];
                }
            }
        }

        return $ledgerArray;
    }

    /**
     * Get ledger account name from counter entry and ledger accounts
     *
     * @param array $counterEntry Counter entry record
     * @param array $ledgerAccounts Array of ledger accounts
     * @return string Ledger account name
     */
    protected function getLedgerAccountName(array $counterEntry, array $ledgerAccounts): string
    {
        // If ledger_account_name exists in counter entry, return it
        if (!empty($counterEntry['ledger_account_name'])) {
            return $counterEntry['ledger_account_name'];
        }

        // If ledger account exists in pre-fetched accounts, return its name
        if (isset($ledgerAccounts[$counterEntry['ledger_account_id']])) {
            return $ledgerAccounts[$counterEntry['ledger_account_id']]['ledger_account_name'];
        }

        // If not found in pre-fetched accounts, try to get it directly
        $ledgerDetails = GrpLedgTree::find($counterEntry['ledger_account_id']);
        if ($ledgerDetails) {
            return $ledgerDetails->ledger_account_name;
        }

        return '';
    }

    public function bankRecoReport($data = array())
    {
        try {
           
            if (!is_array($data)) {
                throw new \InvalidArgumentException('Data parameter must be an array');
            }

            $soc_id = $data['soc_id'] ?? $this->input['company_id'] ?? null;
            
            if (!$soc_id) {
                throw new \InvalidArgumentException('Required parameter missing: company_id');
            }

            // Get default bank account if filter_by is not provided
            if (empty($data['filter_by']) && empty($this->input['filter_by'])) {
                $defaultBank = $this->getDefaultBankAccount(['soc_id' => $soc_id]);
                if (!empty($defaultBank) && isset($defaultBank['ledger_account_id'])) {
                    $data['filter_by'] = $defaultBank['ledger_account_id'];
                    $this->input['filter_by'] = $defaultBank['ledger_account_id'];
                } else {
                    throw new \InvalidArgumentException('No default bank account found for this company');
                }
            }

            $filter = array();
            $filter['from_date'] = $from_date = $data['from_date'] ?? $this->input['from_date'] ?? date('Y-m-01');
            $filter['to_date'] = $to_date = $data['to_date'] ?? $this->input['to_date'] ?? date('Y-m-t');
            $filter['filter_by'] = $data['filter_by'] ?? $this->input['filter_by'];
            $filter['type'] = $data['type'] ?? '0';
            $filter['date_filter_by'] = $data['date_filter_by'] ?? $this->input['date_filter_by'] ?? '1';
            
            $number_page = (isset($data['page']) && $data['page'] > 0) ? $data['page'] : 1;
            $is_paging = $data['is_paging'] ?? false;
            $custom_paging = $data['custom_paging'] ?? null;
            $bank = $data['bank'] ?? null;

            $data = array();
            $data['filter'] = array();
            $data['meta'] = array();
            $data['data'] = array();
            $data['summary'] = array();
            $data['soc_id'] = $soc_id;

            // Get bank accounts for dropdown
            $this->filterDropDown = array(null => 'Filter By');
            $bank_accounts = $this->getBankledger('bank', $soc_id);
            $bank_accounts = json_decode(json_encode($bank_accounts), true);
            if (!empty($bank_accounts)) {
                foreach ($bank_accounts as $value) {
                    $this->filterDropDown[$value['ledger_account_id']] = ucfirst($value['ledger_account_name']);

                    if ($value['ledger_account_id'] == $filter['filter_by']) {
                        $bank = $value;
                    }
                }
            }

            $data['filter']['date_filter_by'] = array(
                'type' => 'option',
                'placeholder' => 'Filter By',
                'options' => ['1' => 'Transaction Date', '2' => 'Bank Date'],
                'value' => $filter['date_filter_by']
            );

            $data['filter']['from_date'] = array(
                'type' => 'date',
                'placeholder' => 'From date',
                'value' => $from_date
            );

            $data['filter']['to_date'] = array(
                'type' => 'date',
                'placeholder' => 'To date',
                'value' => $to_date
            );

            $data['filter']['filter_by'] = array(
                'type' => 'option',
                'placeholder' => 'Bank',
                'options' => $this->filterDropDown,
                'value' => $bank['ledger_account_id']
            );

            $data['filter']['type'] = array(
                'type' => 'option',
                'placeholder' => 'Type',
                'options' => ['0' => 'All', '1' => 'Reconciled', '2' => 'Unreconciled'],
                'value' => $filter['type']
            );

            $data['meta']['report_header'] = 'Bank Reco Statement';
            $filter['from_date'] = $from_date;
            $filter['to_date'] = $to_date;
            $arrPost['filter'] = $filter;
            $arrPost['page'] = $number_page;

            
            // dd($filter);
            $transaction_data = $this->getbankTransaction(
                $filter['filter_by'],
                $to_date,
                $from_date,
                $filter['date_filter_by'],
                $filter['type'],
                $data['soc_id']
            );

            if (!$is_paging) {
                $countData = count($transaction_data);
            }

            $voucherData = $this->prepareBankRecoReportData($transaction_data);

            $voucherItems = new \stdClass();
            $voucherItems->items = $voucherData['data'];

            $data['data']['data'] = $voucherItems;
            $data['data']['headers'] = $voucherData['headers'];
            $data['page']['filters'] = array(
                'from_date' => $filter['from_date'],
                'to_date' => $filter['to_date'],
                'date_filter_by' => $filter['date_filter_by'],
                'filter_by' => $filter['filter_by'],
                'type' => $filter['type']
            );

            $filters = $this->voucherReportFilter($filter, $this->filterDropDown);
            $data['meta']['filters'] = $filters;
            $data['summary']['headers'] = array();
            $data['summary']['data'] = array();
            
            return $data;
        } catch (\Exception $e) {
            return [
                'error' => true,
                'message' => $e->getMessage(),
                'data' => [],
                'meta' => [],
                'filter' => [],
                'summary' => []
            ];
        }
    }

    public function prepareBankRecoReportData($data)
    {
        $finalData = array('headers' => null, 'data' => null);

        $header = array(
            'Sr. No' => 'text',
            'Reconciliation Status' => 'text',
            'From Account' => 'text',
            'Date ' => 'date',
            'Particulars/Narration' => 'text',
            'Payment Mode' => 'text',
            'Payment Reference' => 'text',
            'Bank Date' => 'date',
            'Deposit' => 'amount',
            'Withdrawal' => 'amount',
            'Type(Payment / Receipt)' => 'text'
        );

        $finalData['headers'] = $header;
        $startno = 1;
        for ($i = 0; $i < count($data); $i++) {
            $value = $data[$i];
            $finalData['data'][$i]['sr_no'] = $startno + $i;
            $finalData['data'][$i]['reconciliation_status'] = empty($value['is_reconciled']) ? 'Unreconciled' : 'Reconciled';
            $finalData['data'][$i]['from_account'] = $value['from_account'];
            $finalData['data'][$i]['transaction_date'] = $value['transaction_date'];
            $finalData['data'][$i]['memo_desc'] = $value['memo_desc'];
            $finalData['data'][$i]['payment_mode'] = $value['payment_mode'];
            $finalData['data'][$i]['payment_reference'] = $value['payment_reference'];
            $finalData['data'][$i]['bank_date'] = $value['value_date'];
            if ($value['transaction_type'] === 'dr') {
                $finalData['data'][$i]['withdrawal'] = $value['transaction_amount'];
                $finalData['data'][$i]['deposit'] = 0;
            } else {
                $finalData['data'][$i]['deposit'] = $value['transaction_amount'];
                $finalData['data'][$i]['withdrawal'] = 0;
            }
            $finalData['data'][$i]['type'] = $value['voucher_type'];
        }

        return $finalData;
    }

    protected function voucherReportFilter($filters, $filterDropDown)
    {
        $filterData = array();

        if (isset($filters['from_date']) && isset($filters['to_date'])) {
            $filterData['Date Range'] = date('d/m/Y', strtotime($filters['from_date'])) . ' - ' . date('d/m/Y', strtotime($filters['to_date'])) . ' ,';
        }

        if (isset($filters['filter_by'])) {
            $filterData['Bank'] = $filterDropDown[$filters['filter_by']] . ' ,';
        }

        if (isset($filters['type'])) {
            $a = ['0' => 'All', '1' => 'reconciled', '2' => 'unreconciled'];
            $filterData['Type'] = ucfirst($a[$filters['type']]) . ' ,';
        }

        if (!empty($filterData)) {
            $keys = array_keys($filterData);
            $last = end($keys);
            $filterData[$last] = rtrim($filterData[$last], ' , ');
        }

        return $filterData;
    }

    protected function format1($items)
    {
        return array_map(function($item) {
            return [
                'sr_no' => $item['sr_no'] ?? '',
                'reconciliation_status' => $item['reconciliation_status'] ?? '',
                'from_account' => $item['from_account'] ?? '',
                'transaction_date' => $item['transaction_date'] ?? '',
                'memo_desc' => $item['memo_desc'] ?? '',
                'payment_mode' => $item['payment_mode'] ?? '',
                'payment_reference' => $item['payment_reference'] ?? '',
                'bank_date' => $item['bank_date'] ?? '',
                'deposit' => $item['deposit'] ?? 0,
                'withdrawal' => $item['withdrawal'] ?? 0,
                'type' => $item['type'] ?? ''
            ];
        }, $items);
    }

    public function getFromAndToDate($inputs){
        // $date = $inputs['transaction_date'];
        // $dateRange = explode(',', $date);
        $dateRange = $inputs;
        $from_date = $dateRange['startDate'] ?? date("Y-m-01");
        $to_date = $dateRange['endDate'] ?? date("Y-m-t");
        return array($from_date, $to_date);
    }
}
