<?php

namespace App\Console\Commands\Actions\Reports;

use App\Console\Commands\Actions\BaseDataSource;
use Illuminate\Support\Facades\DB;
use App\Console\Commands\Action;

class ExpenseBudgetReportDataSource extends Action
{
    protected $signature = 'datasource:expenseBudgetReport {flowId} {parentId} {input}';
    protected $description = 'Expense Budget Report Data Source';

    protected $formatter = [
        'expense_account' => '',
        'expense_ledger' => '',
        'budget' => '',
        'actual_expense' => '',
        'variance' => ''
    ];

    public function apply()
    {
        // Decode the input JSON if it's a string
        $input = is_string($this->input) ? json_decode($this->input, true) : $this->input;
        
        $filters = $input['filters'] ?? [];
        // Get financial year and month with default values
        $financialYear = $filters['financial_year'] ?? (date('Y') - 1). '-' . (date('Y') );
        $financialMonth = $filters['financial_month'] ?? 'all';
        
        // Get report data using existing logic
        $result = $this->getExpenseBudgetReport($financialYear, $financialMonth);
        $this->data = $result['data'];
    }

    private function getExpenseBudgetReport($financialYear, $financialMonth)
    {
        try {
            $dateRange = $this->getDateRange($financialYear, $financialMonth);
            $startDate = $dateRange['start_date'];
            $endDate = $dateRange['end_date'];

            $expenseBudgets = $this->getExpenseBudgets($startDate, $endDate);
            $actualExpenses = $this->getActualExpenses($startDate, $endDate);

            return $this->prepareReportData($expenseBudgets, $actualExpenses);
        } catch (\Exception $e) {
            \Log::error('Error in getExpenseBudgetReport: ' . $e->getMessage());
            return ['data' => []];
        }
    }

    private function getDateRange($financialYear, $financialMonth)
    {
        try {
            // Validate financial year format
            if (!preg_match('/^\d{4}-\d{4}$/', $financialYear)) {
                throw new \Exception('Invalid financial year format');
            }

            list($startYear, $endYear) = explode('-', $financialYear);
            
            if (!empty($financialMonth) && $financialMonth !== 'all') {
                $month = (int)$financialMonth;
                if ($month < 1 || $month > 12) {
                    throw new \Exception('Invalid month');
                }
                
                $year = $month < 4 ? $endYear : $startYear;
                $startDate = $year . '-' . str_pad($month, 2, '0', STR_PAD_LEFT) . '-01';
                $endDate = date('Y-m-t', strtotime($startDate));
            } else {
                $startDate = $startYear . '-04-01';
                $endDate = $endYear . '-03-31';
            }

            return [
                'start_date' => $startDate,
                'end_date' => $endDate
            ];
        } catch (\Exception $e) {
            \Log::error('Error in getDateRange: ' . $e->getMessage());
            // Return current financial year dates as fallback
            $currentYear = date('Y');
            return [
                'start_date' => $currentYear . '-04-01',
                'end_date' => ($currentYear + 1) . '-03-31'
            ];
        }
    }

    private function getExpenseBudgets($startDate, $endDate)
    {
        $financialYear = $this->input['filters']['financial_year'] ?? (date('Y') - 1) . '-' . (date('Y'));
        list($startYear, $endYear) = explode('-', $financialYear);

        $this->input['filters']['financial_month'] = $this->input['filters']['financial_month'] ?? 'all';
        if ($this->input['filters']['financial_month'] === 'all') {
            $this->input['filters']['financial_month'] = '0';
        }

        $months_array = array_flip(array(
            'All Months' => '0',
            'April '  => '4',
            'May '  => '5',
            'June '  => '6',
            'July '  => '7',
            'August '  => '8',
            'September '  => '9',
            'October '  => '10',
            'November '  => '11',
            'December '  => '12',
            'January '  => '1',
            'February '  => '2',
            'March '  => '3'
        ));
        // Get the month number from the month name
        $monthId = '0'; // default to All Months
        $financialMonth = $this->input['financial_month'] ?? 'All Months';
        if (array_key_exists($financialMonth, $months_array)) {
            $monthId = $months_array[$financialMonth];
        }

        $accountClosingId = $this->tenantDB()
            ->table('soc_account_financial_year_master')
            ->whereYear('fy_start_date', $startYear)
            ->whereYear('fy_end_date', $endYear)
            ->value('account_closing_id');
        return $this->tenantDB()
            ->table('chsone_expense_accounts_budgets as eba')
            ->join('chsone_expense_tracker as et', 'eba.et_id', '=', 'et.et_id')
            ->join('chsone_grp_ledger_tree as l', 'l.ledger_account_id', '=', 'et.et_ledger_account_id')
            ->select([
                'eba.id',
                'et.et_type_name',
                'l.ledger_account_name',
                'l.ledger_account_id',
                'eba.budget'
            ])
            ->where('eba.fy_id', $accountClosingId)
            ->where('eba.month_id', $monthId)
            ->get();
    }

    private function getActualExpenses($startDate, $endDate)
    {
        return $this->tenantDB()
            ->table('chsone_ledger_transactions')
            ->select([
                'ledger_account_id',
                DB::raw('SUM(CASE 
                    WHEN transaction_type = "dr" THEN transaction_amount 
                    WHEN transaction_type = "cr" THEN -transaction_amount 
                END) as actual_amount')
            ])
            ->whereBetween('transaction_date', [$startDate, $endDate])
            ->groupBy('ledger_account_id')
            ->get()
            ->keyBy('ledger_account_id');
    }

    private function prepareReportData($expenseBudgets, $actualExpenses)
    {
        $reportData = [];
        $totalBudget = 0;
        $totalActual = 0;

        foreach ($expenseBudgets as $budget) {
            $actualAmount = isset($actualExpenses[$budget->ledger_account_id]) 
                ? (float)$actualExpenses[$budget->ledger_account_id]->actual_amount 
                : 0;

            $budgetAmount = (float)($budget->budget ?? 0);
            $variance = $budgetAmount - $actualAmount;

            $totalBudget += $budgetAmount;
            $totalActual += $actualAmount;

            $reportData[] = [
                'expense_account' => ucfirst($budget->et_type_name),
                'expense_ledger' => ucfirst($budget->ledger_account_name),
                'budget' => number_format($budgetAmount, 2),
                'actual_expense' => number_format($actualAmount, 2),
                'variance' => number_format($variance, 2)
            ];
        }

        // Add total row
        // $reportData[] = [
        //     'expense_account' => 'Total',
        //     'expense_ledger' => '',
        //     'budget' => number_format($totalBudget, 2),
        //     'actual_expense' => number_format($totalActual, 2),
        //     'variance' => number_format($totalBudget - $totalActual, 2)
        // ];

        return [
            'data' => $reportData
        ];
    }

    public function getDownloadData()
    {
        return [
            'data' => $this->data,
            'headers' => $this->meta['headers'],
            'title' => 'Expense Budget Report',
            'filters' => [
                ['Financial Year', $this->meta['filters']['financial_year']],
                ['Month', $this->meta['filters']['financial_month']]
            ]
        ];
    }
}
