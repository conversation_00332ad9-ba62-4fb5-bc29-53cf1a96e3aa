<?php

namespace App\Console\Commands\Actions\Reports;

use Illuminate\Console\Command;
use App\Console\Commands\Action;

class TdsReceivableReportDataSource extends Action
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'datasource:tdsreceivableReport {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'TDS Receivable Report Data Source';

    protected $formatter = [

    ];

    // protected $formatterByKeys = ['id'];

    protected $mapper = [

    ];

    /**
     * Execute the console command.
     */
    public function apply()
    {
        $request = $this->input;
        $from_date = date('Y-m-01');
        $to_date = date('Y-m-t');
        
        if (!empty($this->input['filters'])) {
            $date = $this->getFromAndToDate($this->input['filters']);
            $from_date = $date[0];
            $to_date = $date[1];
        }
        // c.member_type_id refers to table chsone_member_type_master table column member_type_id having member_type_name = 'Primary'
        $condition = "a.payment_date BETWEEN '". $from_date ."' AND '". $to_date ."' AND a.tds_deducted > 0 AND (c.member_type_id = 1 OR c.id IS NULL)";

        $query = "WITH RECURSIVE InvoiceData AS (
            SELECT 
                CONCAT(b.soc_building_name, ' / ', b.unit_flat_number) AS unit,
                IFNULL(CONCAT(c.member_first_name, ' ', c.member_last_name), a.received_from) AS member_name,
                a.invoice_number AS invoice_number,
                a.payment_amount AS payment_amount,
                a.tds_deducted AS tds_deducted,
                a.receipt_number,
                ROW_NUMBER() OVER (PARTITION BY a.receipt_number ORDER BY a.receipt_number) AS my_number
            FROM
                income_invoice_payment_tracker AS a
            LEFT JOIN
                chsone_units_master AS b ON a.unit_id = b.unit_id
            LEFT JOIN
                chsone_members_master AS c ON a.unit_id = c.fk_unit_id
            WHERE
                ". $condition ."
        )
        SELECT 
            ROW_NUMBER() OVER (ORDER BY receipt_number) AS id,
            unit,
            member_name,
            invoice_number,
            payment_amount,
            tds_deducted,
            receipt_number
        FROM
            InvoiceData
        WHERE
            my_number = 1;
        ";

        $result = $this->tenantDB()->select($query);

        $summaryQuery = "WITH RECURSIVE InvoiceData AS (
            SELECT 
                a.payment_amount AS payment_amount,
                a.tds_deducted AS tds_deducted,
                ROW_NUMBER() OVER (PARTITION BY a.receipt_number ORDER BY a.receipt_number) AS my_number
            FROM
                income_invoice_payment_tracker AS a
            LEFT JOIN
                chsone_members_master AS c ON a.unit_id = c.fk_unit_id
            WHERE
            ". $condition ."
        )
        SELECT 
            ROW_NUMBER() OVER () AS id,  -- Auto-increment like ID
            SUM(payment_amount) AS total_payment_amount,
            SUM(tds_deducted) AS total_tds_deducted
        FROM
            InvoiceData
        WHERE
            my_number = 1
        LIMIT 1;";
    

        
            $summary = $this->tenantDB()->select($summaryQuery);

        $table = [
            $result,
            $summary
        ];

        // $this->meta['summary'] = [
        //     'total_payment_amount' => $summary[0]->total_payment_amount,
        //     'total_tds_deducted' => $summary[0]->total_tds_deducted,
        // ];

        // $this->data = $this->format($result);
        // dd($table);


        
        $this->data = $table;

    }

    public function getFromAndToDate($inputs){
        // $date = $inputs['transaction_date'];
        // $dateRange = explode(',', $date);
        $dateRange = $inputs;
        $from_date = $dateRange['startDate'] ?? date("Y-m-01");
        $to_date = $dateRange['endDate'] ?? date("Y-m-t");
        return array($from_date, $to_date);
    }
}
