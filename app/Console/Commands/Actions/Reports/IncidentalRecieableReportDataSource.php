<?php

namespace App\Console\Commands\Actions\Reports;

use Illuminate\Console\Command;
use App\Console\Commands\Action;
use App\Models\Tenants\ChsoneUnitsMaster;
use App\Models\Tenants\IncomeCommonAreaCharge;
use App\Models\Tenants\IncomeCommonBillingCharge;
use Illuminate\Support\Facades\DB;
use App\Models\Tenants\ChsoneTaxLog;

class IncidentalRecieableReportDataSource extends Action
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'datasource:incidentalRecieableReport {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Incidental Receivable Report Data Source';

    protected $formatter = [
        'id' => '',
        'vendor_name' => '',
        'vendor_address' => '',
        'vendor_bill_num' => '',
        'vendor_pan_num' => '',
        'vendor_is_company' => '',
        'section' => '',
        'vendor_bill_date' => '',
        'vendor_bill_date_of_deduction' => '',
        'taxable_amount' => '',
        'gst_amount' => '',
        'total_bill_amount' => '',
        'rate' => '',
        'vendor_bill_tds' => '',
        'vendor_bill_amount' => ''
    ];

    // protected $formatterByKeys = ['id'];

    // protected $mapper = [
    //     'id' => 'id',
    // ];

    /**
     * Execute the console command.
     */
    public function apply()
    {
        try     {
            $request = $this->input;
            $data['search_by'] = '';
            $data['soc_id'] = $this->input['company_id'];
            $data['filter'] = array();
            $arrPost['due_date'] = $this->input['filters']['due_date'] ?? date('Y-m-d');
    
            $arrPost['fyStartDate'] = $this->getCurrentActiveFinancialYear()->fy_start_date;
    
            $unitMaster = new ChsoneUnitsMaster;
            $units = $unitMaster->memberMaintenanceUnitsQuery($request['company_id']);
            $units = json_decode(json_encode($units), true);
    
            $incidentalDues = $this->memberIncidentalDue($request['company_id'], $units, $arrPost);
            $particulars = $this->getParticulars(['soc_id' => $request['company_id']]);
    
            // --- BEGIN: Prepare summary columns from particulars ---
            $summaryParticulars = [];
            foreach ($particulars as $particular) {
                $key = str_replace(['.', ' ', '-'], ['_', '_', '_'], $particular) . '_summary';
                $summaryParticulars[] = $key;
                // $summaryColumns[$key] = 0;
            }
            // --- END: Prepare summary columns from particulars ---
    
    
            // uncomment if data mismatch
            // $res = $this->array_merge_incidental_custom($units,  $incidentalDues, $particulars);
            // $schema = $this->generateDynamicSchema($res);
            // $this->meta['schema'] = $schema;
            // $this->data = $res;
    
            $res = $this->array_merge_incidental_custom($units,  $incidentalDues, $particulars);
            $schema = $this->generateDynamicSchema($res);
    
            // --- BEGIN: Summary array for dynamic columns ---
            $s = [];
            if (!empty($res)) {
                $fixedStart = ['building_unit_name', 'member_name'];
                $fixedEnd = ['total_due'];
                $firstRow = $res[0];
                $dynamicKeys = array_diff(array_keys($firstRow), array_merge($fixedStart, $fixedEnd));
                foreach ($dynamicKeys as $key) {
                    $sum = 0;
                    foreach ($res as $row) {
                        if (isset($row[$key])) {
                            $sum += (float)$row[$key];
                        }
                    }
                    $s[$key . '_summary'] = $sum;
                }
            }
    
         
    
            $finalData = [
                $res,
                [$s]
            ];
            $this->meta['schema'] = $schema;
            $this->data = $finalData;
            // --- END: Summary array for dynamic columns ---
    
        } catch (\Exception $e) {
            dd($e);
        }
    }

    public function getCurrentActiveFinancialYear()
    {
        return $this->tenantDB()->table("soc_account_financial_year_master AS financial_master")
            ->where("confirmed", "0")
            ->where("closed", "0")
            ->orderBy("account_closing_id")
            ->first();
    }

    protected function generateDynamicSchema(array $data): array
    {
        // Ensure there is at least one row of data to infer keys
        if (empty($data)) {
            return [];
        }

        // Use the first row to extract keys
        $firstRow = $data[0];

        // Fixed fields
        $fixedStart = ['building_unit_name', 'member_name'];
        $fixedEnd = ['total_due'];

        // Identify dynamic keys by excluding fixed ones
        $dynamicKeys = array_diff(array_keys($firstRow), array_merge($fixedStart, $fixedEnd));

        // Build the schema columns
        $dynamicColumns = [];

        foreach ($dynamicKeys as $key) {
            $dynamicColumns[] = [
                'title' => ucwords(str_replace('_', ' ', $key)),
                'key' => $key,
            ];
        }

        $summaryParticulars = [];
        foreach ($dynamicKeys as $key) {
            $summaryParticulars[] = $key . '_summary';
        }

        $summaryColumns = array_map(fn($k) => ['title' => ucwords(str_replace('_', ' ', $k)), 'key' => $k], $summaryParticulars);

        // Build the full column structure
        $columns = [
            array_merge(
                array_map(fn($k) => ['title' => $k === 'member_name' ? 'Primary Member' : ucwords(str_replace('_', ' ', $k)), 'key' => $k], $fixedStart),
                $dynamicColumns,
                array_map(fn($k) => ['title' => ucwords(str_replace('_', ' ', $k)), 'key' => $k], $fixedEnd)
            ),
            $summaryColumns
        ];

        // Return the complete schema
        return [
            'table' => [
                'tableTitle' => [
                    'Members Incidental Receivable',
                    'Summary'
                ],
                'fields' => ['*'],
                "actions" => [
                    [
                        "title" => "Export Report",
                        "icon" => "ri-export-line",
                        "options" => [
                            [
                                "title" => "Print",
                                "icon" => "ri-file-2-line",
                                "api" =>[
                                    "type" => "download",
                                    "url" => "/admin/income-details/incidentalReceivableReport/download/pdf",
                                    "method" => "GET",
                                ]
                            ],
                            [
                                "title" => "PDF",
                                "icon" => "ri-file-pdf-2-line",
                                "api" =>[
                                    "type" => "download",
                                    "url" => "/admin/income-details/incidentalReceivableReport/download/pdf",
                                    "method" => "GET",
                                ]
                            ],
                            [
                                "title" => "Excel",
                                "icon" => "ri-file-excel-2-line",
                                "api" =>[
                                    "type" => "download",
                                    "url" => "/admin/income-details/incidentalReceivableReport/download/excel",
                                    "method" => "GET",
                                ]
                            ]
                        ]
                    ]
                ],
                'extraFilters' => [
                    'due_date' => [
                        'title' => 'Due On Date',
                        'type' => 'date',
                        'default' => '2023-01-01',
                    ]
                ],
                'columns' => $columns
            ]
        ];
    }


    public function memberIncidentalDue($socId, $units, $arrPost)
    {
        $unitIds = $invoiceIds = $unitInvoiceNumbers = $unitInvoiceNumbersWithPipes = null;
        $unitIds = array_column($units, 'unit_id');
        $date = isset($arrPost['due_date']) ? $this->getDatabaseDate($arrPost['due_date']) : '';
        $unit_invoice_details = array();

        /** First Unit All Invoices Exclude Cancelled * */
        $objUnitsInvoice = $this->tenantDB()->table('income_common_billing_charges')
        ->whereIn('fk_unit_id', $unitIds)
        ->where('payment_status', '!=', 'cancelled')
        ->where(function ($query) use ($date) {
            $query->whereDate('bill_date', '<=', $date)
                  ->orWhereNull('bill_date');
        })
        ->where('soc_id', $socId)
        ->get();
    

        $arrUnitsInvoice = array();
        $unitsTaxes = $unitsInterest = $unitsPayments = array();
        if (!empty($objUnitsInvoice)) {
            $arrUnitsInvoice = $objUnitsInvoice->toArray();
            $arrUnitsInvoice = json_decode(json_encode($arrUnitsInvoice), true);
        }

        if (!empty($arrUnitsInvoice)) {

            $invoiceIds = array_column($arrUnitsInvoice, 'id');
            $unitInvoiceNumbers = array_column($arrUnitsInvoice, 'invoice_number');

            /** Tax Fetch By Unit Invoices * */
            $arrUnitsInvoiceTaxes = $this->getSumInvoiceTaxesByInvoiceNumbers(array('invoice_number' => $unitInvoiceNumbers));
            foreach ($arrUnitsInvoiceTaxes as $value) {
                $unitsTaxes[$value['invoice_number']] = $value['tax_amount'];
            }

            /** Interest Amount Fetch By Unit Invoices * */
            $arrUnitsInvoiceInterest = $this->getSumInvoiceInterestAmountByInvoiceNumbers(array('invoice_number' => $unitInvoiceNumbers));
            $arrUnitsInvoiceInterest = json_decode(json_encode($arrUnitsInvoiceInterest), true);
            foreach ($arrUnitsInvoiceInterest as $value) {
                $unitsInterest[$value['invoice_number']] = $value['interest_amount'] + $value['roundoff_amount'];
            }

            /** Payment Fetch By Unit Invoices * */
            $arrUnitsInvoicePayments = $this->getSumIncidentInvoicePaymentByInvoiceNumbers(array('invoice_ids' => $invoiceIds, 'payment_date' => $date));
            $arrUnitsInvoicePayments = json_decode(json_encode($arrUnitsInvoicePayments), true);
            foreach ($arrUnitsInvoicePayments as $value) {
                $unitsPayments[$value['common_bill_id']]['tds_deducted'] = $value['tds_deducted'];
                $unitsPayments[$value['common_bill_id']]['transaction_charges'] = $value['transaction_charges'];
                $unitsPayments[$value['common_bill_id']]['discount_amount'] = $value['discount_amount'];
                $unitsPayments[$value['common_bill_id']]['payment_amount'] = $value['payment_amount'];
            }
        }

        /** Second Get All Particular By Unit Invoices * */
        $round = 4;
        $dueAmount = array();
        foreach ($arrUnitsInvoice as $value) {
            $payment_amount = $transaction_amount = $tds_amount = $discount_amount = 0;
            $invoices = array('invoice_number' => 'NA', 'payment_status' => 'NA', 'tax_applicable' => 0, 'advanced_paid' => 0, 'invoice_amount' => 0, 'paid_amount' => 0, 'due_amount' => 0);
            $invoices['invoice_number'] = $value['invoice_number'];
            $invoices['payment_status'] = $value['payment_status'];
            $invoices['advanced_paid'] = $value['advance_amount'];

            /** Tax Fetch By Unit Invoices * */
            if (array_key_exists($value['invoice_number'], $unitsTaxes)) {
                $invoices['tax_applicable'] = $unitsTaxes[$value['invoice_number']];
            } else {
                $invoices['tax_applicable'] = 0;
            }

            /** Interest Amount Fetch By Unit Invoices * */
            if (array_key_exists($value['invoice_number'], $unitsInterest)) {
                $invoices['interest_amount'] = $unitsInterest[$value['invoice_number']];
            } else {
                $invoices['interest_amount'] = 0;
            }

            /** Payment Fetch By Unit Invoices * */
            if (array_key_exists($value['id'], $unitsPayments)) {
                $payment_amount = $unitsPayments[$value['id']]['payment_amount'];
                $transaction_amount = $unitsPayments[$value['id']]['transaction_charges'];
                $tds_amount = $unitsPayments[$value['id']]['tds_deducted'];
                $discount_amount = $unitsPayments[$value['id']]['discount_amount'];
            }
            $invoices['billing_type'] = $value["billing_type"];
            $invoices['paid_amount'] = round($payment_amount, $round) + round($tds_amount, $round) + round($invoices['advanced_paid'], $round);
            $invoices['invoice_amount'] = round($value['amount'], $round) + round($invoices['tax_applicable'], $round) + round($invoices['interest_amount'], $round);
            $invoices['due_amount'] = (round($invoices['invoice_amount'], $round) - round($invoices['paid_amount'], $round)) < 0 ? 0 : (round($invoices['invoice_amount'], $round) - round($invoices['paid_amount'], $round));
            if (!array_key_exists($value['fk_unit_id'], $dueAmount)) {
                $dueAmount[$value['fk_unit_id']] = $invoices['due_amount'];
                $unit_invoice_details[$value['fk_unit_id']]['total_due'] = (float) $dueAmount[$value['fk_unit_id']];
            } else {
                $dueAmount[$value['fk_unit_id']] = round($dueAmount[$value['fk_unit_id']], $round) + round($invoices['due_amount'], $round);
                $unit_invoice_details[$value['fk_unit_id']]['total_due'] = (float) $dueAmount[$value['fk_unit_id']];
            }
            $unit_invoice_details[$value['fk_unit_id']][] = $invoices;
        }
        return $unit_invoice_details;
    }

    public function getSumInvoiceTaxesByInvoiceNumbers(array $data = [])
    {
        if (empty($data['invoice_number'])) {
            return [];
        }

        $results = ChsoneTaxLog::select(
                'invoice_number',
                DB::raw('SUM(tax_amount) as tax_amount')
            )
            ->whereIn('invoice_number', $data['invoice_number'])
            ->groupBy('invoice_number')
            ->orderBy('tax_log_id')
            ->get()
            ->toArray();

        return $results;
    }

    public function getSumInvoiceInterestAmountByInvoiceNumbers(array $data = [])
    {
        // Ensure invoice numbers are passed as an array
        foreach ($data['invoice_number'] as $key => $value) {
            $invoiceNumbers[] = explode(',', str_replace("'", '', $value));
        }

        // Run the query using Laravel's Query Builder
        $results = $this->tenantDB()->table('income_unit_invoices')
            ->select(
                'invoice_number',
                DB::raw('SUM(interest_amount) as interest_amount'),
                DB::raw('SUM(roundoff_amount) as roundoff_amount')
            )
            ->whereIn('invoice_number', $invoiceNumbers)
            ->groupBy('invoice_number')
            ->orderBy('unit_invoice_id')
            ->get();

        return $results->toArray();
    }

    public function getSumIncidentInvoicePaymentByInvoiceNumbers(array $data = [])
    {
        // Step 1: Parse the invoice IDs (string to array)
        foreach ($data['invoice_ids'] as $key => $value) {
            $invoiceIds[] = explode(',', str_replace("'", '', $value));
        }

        // Step 2: Start building the query
        $query = $this->tenantDB()->table('income_common_billing_payment as invoicePayment')
            ->select(
                'invoicePayment.fk_common_bill_id as common_bill_id',
                DB::raw('SUM(invoicePayment.tds_deducted) as tds_deducted'),
                DB::raw('SUM(invoicePayment.transaction_charges) as transaction_charges'),
                DB::raw('SUM(invoicePayment.discount_amount) as discount_amount'),
                DB::raw('SUM(invoicePayment.payment_amount) as payment_amount')
            )
            ->whereIn('invoicePayment.fk_common_bill_id', $invoiceIds);

        // Step 3: Add optional payment_date condition
        if (!empty($data['payment_date'])) {
            $paymentDate = $this->getDatabaseDate($data['payment_date']);

            $query->where(function ($q) use ($paymentDate) {
                $q->whereDate('invoicePayment.payment_date', '<=', $paymentDate)
                ->orWhereNull('invoicePayment.payment_date');
            });
        }

        // Step 4: Group and order
        $results = $query
            ->groupBy('invoicePayment.fk_common_bill_id')
            ->orderBy('invoicePayment.payment_id')
            ->get();

        return $results->toArray();
    }

    public function getParticulars($data = array())
    {
        try    {
            try {
                $soc_id = $data['soc_id'];
        
                // Get all raw values without grouping (preserve duplicates)
                $particulars = $this->tenantDB()->table('income_common_area_charges')
                    ->select('id', 'particular')
                    ->where('particular', '!=', 'payment reversal correction')
                    ->distinct()
                    ->get();
        
                $formatted = [];
                foreach ($particulars as $item) {
                    $formatted[$item->id] = $item->particular;
                }
        
                return $formatted;
        
            } catch (\Exception $e) {
                return [];
            }
        } catch (\Exception $e) {
            dd($e);
        }
    }
    

    function array_merge_incidental_custom($units,  $incidents, $filters)
    {
        $result = array();
        foreach ($units as $key => $value) {
            $result[$key]['building_unit_name'] = $value['building_unit_name'];
            $result[$key]['member_name'] = $value['member_name'];

            $heads = $filters;
            foreach ($filters as $fk => $fl) {
                if ($fl != 'Filter By') {
                    $result[$key][str_replace(['.', ' ', '-'], ['_', '_', '_'], $heads[$fk])] = 0;
                }
            }
            $result[$key]['total_due']  = 0;

            if (!empty($incidents[$value['unit_id']])) {
                $result[$key]['total_due'] = $incidents[$value['unit_id']]['total_due'];
                unset($incidents[$value['unit_id']]['total_due']);
                foreach ($incidents[$value['unit_id']] as $kn => $ins) {
                    if (array_key_exists($ins['billing_type'], $heads)) {
                        $column = str_replace(['.', ' ', '-'], ['_', '_', '_'], $heads[$ins['billing_type']]);
                        if (!isset($result[$key][$column])) {
                            $result[$key][$column] = 0;
                        }
                        $result[$key][$column] += $ins['due_amount'];
                    }
                }
            }
        }
        return $result;
    }


}
