<?php

namespace App\Console\Commands\Actions\Reports;

use Illuminate\Console\Command;
use App\Console\Commands\Action;
use App\Models\Tenants\ChsoneMembersMaster;
use App\Models\Tenants\ChsoneTaxLog;
use App\Models\Tenants\ChsoneUnitsMaster;
use App\Models\Tenants\IncomeInvoiceParticular;
use App\Models\Tenants\IncomeInvoicePayment;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;

class MembersReceivableReportAction extends Action
{
    protected $signature = 'datasource:membersReceivableReport {flowId} {parentId} {input}';

    protected $description = 'Get Member Receivable Report';

    // protected $formatter = [
    // ];

    protected $formatterByKeys = [
        'id'
    ];

    public function apply()
    {
        $request = $this->input;
        $date = date('Y-m-d');
        if (isset($request['from_date'])) {
            $date = $this->getDatabaseDate($request['from_date']);
        }
        
        $arrPost['maintenanceDuesMore'] = $this->input['filters']['maintainance_due_more'] ?? '';
        $arrPost['maintenanceDuesLess'] = $this->input['filters']['maintainance_due_less'] ?? '';
        $arrPost['incidentalDuesMore'] = $this->input['filters']['incidental_due_more'] ?? '';
        $arrPost['incidentalDuesLess'] = $this->input['filters']['incidental_due_less'] ?? '';
        $arrPost['transaction_date'] = $this->input['filters']['transaction_date'] ?? '';
        $arrPost['soc_building_name'] = $this->input['filters']['soc_building_name'] ?? '';
        
        $arrPost['fyStartDate'] = $this->getCurrentActiveFinancialYear()->fy_start_date;

        $unitMaster = new ChsoneUnitsMaster;
        $units = $unitMaster->memberMaintenanceUnitsQuery($request['company_id'], $arrPost['soc_building_name']);
        $units = json_decode(json_encode($units), true);

        $maintenanceDues = $this->memberMaintenanceDue($request['company_id'], $units, $arrPost);

        $incidentalDues = $this->memberIncidentalDue($request['company_id'], $units, $arrPost);
        
        $advanceBalances = $this->memberAdvancesBalance($request['company_id'], $units, $arrPost);
        
        $ledgerBalances = $this->memberLedgerBalance($request['company_id'], $units, $arrPost);

        // $data = $this->array_merge_custom($units, $maintenanceDues, $incidentalDues, $result);
        $data = $this->array_merge_custom($units, $maintenanceDues, $incidentalDues, $advanceBalances, $ledgerBalances);
        
        if(isset($request['filters']))
        {
            $data = $this->filterData($arrPost, $data);
        }

        $this->data = $data;
    }

    public function array_merge_custom($units, $maintenanceDues, $incidentalDues, $advanceBalances, $ledgerBalances) 
    {
        $finalArr = [];
        $result = $units;
        foreach ($result as $key => $value) {
           $finalArr[$key]['soc_building_id'] = $value['soc_building_id'];
            $finalArr[$key]['building_unit_name'] = $value['building_unit_name'];
            $finalArr[$key]['member_name'] = $value['member_name'];
            $finalArr[$key]['maintainance_due'] = (empty($maintenanceDues[$value['unit_id']]['total_due']) || $maintenanceDues[$value['unit_id']]['total_due'] < 0) ? 0 : $maintenanceDues[$value['unit_id']]['total_due'];
            $finalArr[$key]['indental_due'] = (empty($incidentalDues[$value['unit_id']]['total_due']) || $incidentalDues[$value['unit_id']]['total_due'] < 0) ? 0 : $incidentalDues[$value['unit_id']]['total_due'];
            $finalArr[$key]['cr_bal'] = (!empty($advanceBalances[$value['unit_id']])) ? $advanceBalances[$value['unit_id']] : 0;
            // $finalArr[$key]['ledger_bal'] = !empty($value['ledger_bal']) ? $value['ledger_bal'] : 0;
            $finalArr[$key]['ledger_bal'] = !empty($ledgerBalances[$value['unit_id']]) ? $ledgerBalances[$value['unit_id']] : 0;
        }
        return $finalArr;
    }

    public function filterData($arrPost, $data)
    {
        if(isset($arrPost['maintenanceDuesMore']) && $arrPost['maintenanceDuesMore'] != '')
        {
            $data = array_filter($data, function ($item) use ($arrPost) {
                return $item['maintainance_due'] >= $arrPost['maintenanceDuesMore'];
            });
        }

        if(isset($arrPost['maintenanceDuesLess']) && $arrPost['maintenanceDuesLess'] != '')
        {
            $data = array_filter($data, function ($item) use ($arrPost) {
                return $item['maintainance_due'] <= $arrPost['maintenanceDuesLess'];
            });
        }


        if(isset($arrPost['incidentalDuesMore']) && $arrPost['incidentalDuesMore'] != '')
        {
            $data = array_filter($data, function ($item) use ($arrPost) {
                return $item['indental_due'] >= $arrPost['incidentalDuesMore'];
            });
        }

        if(isset($arrPost['incidentalDuesLess']) && $arrPost['incidentalDuesLess'] != '')
        {
            $data = array_filter($data, function ($item) use ($arrPost) {
                return $item['indental_due'] <= $arrPost['incidentalDuesLess'];
            });
        }

        return $data;
    }

    public function memberMaintenanceDue($socId, $units, $arrPost)
    {
        $unitIds = $unitInvoiceNumbers = $unitInvoiceNumbersWithPipes = null;
        $unitIds = array_column($units, 'unit_id');
        $date = isset($arrPost['transaction_date']) ? $this->getDatabaseDate($arrPost['transaction_date']) : '';

        $unit_invoice_details = array();

        /** First Unit All Invoices Exclude Cancelled * */
        $objUnitsInvoice = $this->tenantDB()->table('income_unit_invoices')
        ->whereIn('fk_unit_id', $unitIds)
        ->where('status', '!=', 'cancelled')
        ->whereDate('created_date', '<=', $date)
        ->where('soc_id', $socId)
        ->get();

        $arrUnitsInvoice = array();
        $unitsParticulars = $unitsTaxes = $unitsPayments = array();
        if (!empty($objUnitsInvoice)) {
            $arrUnitsInvoice = $objUnitsInvoice->toArray();
            $arrUnitsInvoice = json_decode(json_encode($arrUnitsInvoice), true);
        }

        if (!empty($arrUnitsInvoice)) {

            /** Particular Fetch By Unit Invoices * */
            $unitInvoiceNumbers = array_column($arrUnitsInvoice, 'invoice_number');
            $arrUnitsInvoiceParticulars = $this->getSumInvoiceParticularsByInvoiceNumbers(array('invoice_number' => $unitInvoiceNumbers));
            foreach ($arrUnitsInvoiceParticulars as $value) {
                $unitsParticulars[$value['invoice_number']] = $value['amount'];
            }

            /** Tax Fetch By Unit Invoices * */
            $arrUnitsInvoiceTaxes = $this->getSumInvoiceTaxesByInvoiceNumbers(array('invoice_number' => $unitInvoiceNumbers));
            foreach ($arrUnitsInvoiceTaxes as $value) {
                $unitsTaxes[$value['invoice_number']] = $value['tax_amount'];
            }

            /** Payment Fetch By Unit Invoices * */
            $arrUnitsInvoicePayments = $this->getSumInvoicePaymentByInvoiceNumbers(array('invoice_number' => $unitInvoiceNumbers, 'payment_date' => $date));
            foreach ($arrUnitsInvoicePayments as $value) {
                $unitsPayments[$value['invoice_number']]['tds_deducted'] = $value['tds_deducted'];
                $unitsPayments[$value['invoice_number']]['late_payment_charges'] = $value['late_payment_charges'];
                $unitsPayments[$value['invoice_number']]['writeoff_amount'] = $value['writeoff_amount'];
                $unitsPayments[$value['invoice_number']]['payment_amount'] = $value['payment_amount'];
            }
        }

        /** Second Get All Particular By Unit Invoices * */
        $round = 4;
        $dueAmount = array();
        foreach ($arrUnitsInvoice as $value) {
            $payment_amount = $writeoff_amount = $tds_amount = $latecharges_amount = 0;
            $invoices = array('invoice_number' => 'NA', 'payment_status' => 'NA', 'is_first_invoice' => 0, 'sub_total' => 0, 'tax_applicable' => 0, 'late_charges' => 0, 'roundoff_amount' => 0, 'outstanding_dues' => 0, 'advanced_paid' => 0, 'invoice_amount' => 0, 'paid_amount' => 0, 'due_amount' => 0);
            $invoices['invoice_number'] = $value['invoice_number'];
            $invoices['payment_status'] = $value['payment_status'];
            $invoices['advanced_paid'] = $value['advance_amount'];
            $invoices['is_first_invoice'] = 0;
            $invoices['outstanding_dues'] = 0;
            /** For First Invoice * */
            if (!array_key_exists($value['fk_unit_id'], $dueAmount)) {
                $invoices['is_first_invoice'] = 1;
                $invoices['outstanding_dues'] = $value['principal_amount'];
            }

            /** Particular Fetch By Unit Invoices * */
            if (array_key_exists($value['invoice_number'], $unitsParticulars)) {
                $invoices['sub_total'] = $unitsParticulars[$value['invoice_number']];
            } else {
                $invoices['sub_total'] = 0;
            }

            /** Tax Fetch By Unit Invoices * */
            if (array_key_exists($value['invoice_number'], $unitsTaxes)) {
                $invoices['tax_applicable'] = $unitsTaxes[$value['invoice_number']];
            } else {
                $invoices['tax_applicable'] = 0;
            }

            /** Payment Fetch By Unit Invoices * */
            if (array_key_exists($value['invoice_number'], $unitsPayments)) {
                $payment_amount = $unitsPayments[$value['invoice_number']]['payment_amount'];
                $writeoff_amount = $unitsPayments[$value['invoice_number']]['writeoff_amount'];
                $tds_amount = $unitsPayments[$value['invoice_number']]['tds_deducted'];
                $latecharges_amount = $unitsPayments[$value['invoice_number']]['late_payment_charges'];
            }

            $invoices['paid_amount'] = round($payment_amount, $round) + round($writeoff_amount, $round) + round($tds_amount, $round) + round($latecharges_amount, $round);
            $invoices['late_charges'] = $value['interest_amount'];
            $invoices['roundoff_amount'] = $value['roundoff_amount'];
            /** For Seeder roundOff reset to 0 * */
            if ($value['updated_date'] == '2018-10-09 00:00:00' && $invoices['roundoff_amount'] > 0) {
                $invoices['roundoff_amount'] = 0;
            }
            $invoices['invoice_amount'] = (round($invoices['sub_total'], $round) + round($invoices['tax_applicable'], $round) + round($invoices['late_charges'], $round) + round($invoices['outstanding_dues'], $round) + round($invoices['roundoff_amount'], $round)) - round($invoices['advanced_paid'], $round);
            $invoices['due_amount'] = (round($invoices['invoice_amount'], $round) - round($invoices['paid_amount'], $round)) < 0 ? 0 : (round($invoices['invoice_amount'], $round) - round($invoices['paid_amount'], $round));
            if (!array_key_exists($value['fk_unit_id'], $dueAmount)) {
                $dueAmount[$value['fk_unit_id']] = $invoices['due_amount'];
                $unit_invoice_details[$value['fk_unit_id']]['total_due'] = (float) $dueAmount[$value['fk_unit_id']];
            } else {
                $dueAmount[$value['fk_unit_id']] = round($dueAmount[$value['fk_unit_id']], $round) + round($invoices['due_amount'], $round);
                $unit_invoice_details[$value['fk_unit_id']]['total_due'] = (float) $dueAmount[$value['fk_unit_id']];
            }
            $unit_invoice_details[$value['fk_unit_id']][] = $invoices;
        }
        return $unit_invoice_details;
    }

    public function memberIncidentalDue($socId, $units, $arrPost)
    {
        $unitIds = $invoiceIds = $unitInvoiceNumbers = $unitInvoiceNumbersWithPipes = null;
        $unitIds = array_column($units, 'unit_id');
        $date = isset($arrPost['transaction_date']) ? $this->getDatabaseDate($arrPost['transaction_date']) : '';
        $unit_invoice_details = array();

        /** First Unit All Invoices Exclude Cancelled * */
        $objUnitsInvoice = $this->tenantDB()->table('income_common_billing_charges')
        ->whereIn('fk_unit_id', $unitIds)
        ->where('payment_status', '!=', 'cancelled')
        ->whereDate('bill_date', '<=', $date)
        ->orWhereNull('bill_date')
        ->where('soc_id', $socId)
        ->get();

        $arrUnitsInvoice = array();
        $unitsTaxes = $unitsInterest = $unitsPayments = array();
        if (!empty($objUnitsInvoice)) {
            $arrUnitsInvoice = $objUnitsInvoice->toArray();
            $arrUnitsInvoice = json_decode(json_encode($arrUnitsInvoice), true);
        }

        if (!empty($arrUnitsInvoice)) {

            $invoiceIds = array_column($arrUnitsInvoice, 'id');
            $unitInvoiceNumbers = array_column($arrUnitsInvoice, 'invoice_number');

            /** Tax Fetch By Unit Invoices * */
            $arrUnitsInvoiceTaxes = $this->getSumInvoiceTaxesByInvoiceNumbers(array('invoice_number' => $unitInvoiceNumbers));
            foreach ($arrUnitsInvoiceTaxes as $value) {
                $unitsTaxes[$value['invoice_number']] = $value['tax_amount'];
            }

            /** Interest Amount Fetch By Unit Invoices * */
            $arrUnitsInvoiceInterest = $this->getSumInvoiceInterestAmountByInvoiceNumbers(array('invoice_number' => $unitInvoiceNumbers));
            foreach ($arrUnitsInvoiceInterest as $value) {

                $unitsInterest[$value->invoice_number] = $value->interest_amount + $value->roundoff_amount;
            }

            /** Payment Fetch By Unit Invoices * */
            $arrUnitsInvoicePayments = $this->getSumIncidentInvoicePaymentByInvoiceNumbers(array('invoice_ids' => $invoiceIds, 'payment_date' => $date));
            foreach ($arrUnitsInvoicePayments as $value) {
                $unitsPayments[$value->common_bill_id]['tds_deducted'] = $value->tds_deducted;
                $unitsPayments[$value->common_bill_id]['transaction_charges'] = $value->transaction_charges;
                $unitsPayments[$value->common_bill_id]['discount_amount'] = $value->discount_amount;
                $unitsPayments[$value->common_bill_id]['payment_amount'] = $value->payment_amount;
            }
        }

        /** Second Get All Particular By Unit Invoices * */
        $round = 4;
        $dueAmount = array();
        foreach ($arrUnitsInvoice as $value) {
            $payment_amount = $transaction_amount = $tds_amount = $discount_amount = 0;
            $invoices = array('invoice_number' => 'NA', 'payment_status' => 'NA', 'tax_applicable' => 0, 'advanced_paid' => 0, 'invoice_amount' => 0, 'paid_amount' => 0, 'due_amount' => 0);
            $invoices['invoice_number'] = $value['invoice_number'];
            $invoices['payment_status'] = $value['payment_status'];
            $invoices['advanced_paid'] = $value['advance_amount'];

            /** Tax Fetch By Unit Invoices * */
            if (array_key_exists($value['invoice_number'], $unitsTaxes)) {
                $invoices['tax_applicable'] = $unitsTaxes[$value['invoice_number']];
            } else {
                $invoices['tax_applicable'] = 0;
            }

            /** Interest Amount Fetch By Unit Invoices * */
            if (array_key_exists($value['invoice_number'], $unitsInterest)) {
                $invoices['interest_amount'] = $unitsInterest[$value['invoice_number']];
            } else {
                $invoices['interest_amount'] = 0;
            }

            /** Payment Fetch By Unit Invoices * */
            if (array_key_exists($value['id'], $unitsPayments)) {
                $payment_amount = $unitsPayments[$value['id']]['payment_amount'];
                $transaction_amount = $unitsPayments[$value['id']]['transaction_charges'];
                $tds_amount = $unitsPayments[$value['id']]['tds_deducted'];
                $discount_amount = $unitsPayments[$value['id']]['discount_amount'];
            }
            $invoices['billing_type'] = $value["billing_type"];
            $invoices['paid_amount'] = round($payment_amount, $round) + round($tds_amount, $round) + round($invoices['advanced_paid'], $round);
            $invoices['invoice_amount'] = round($value['amount'], $round) + round($invoices['tax_applicable'], $round) + round($invoices['interest_amount'], $round);
            $invoices['due_amount'] = (round($invoices['invoice_amount'], $round) - round($invoices['paid_amount'], $round)) < 0 ? 0 : (round($invoices['invoice_amount'], $round) - round($invoices['paid_amount'], $round));
            if (!array_key_exists($value['fk_unit_id'], $dueAmount)) {
                $dueAmount[$value['fk_unit_id']] = $invoices['due_amount'];
                $unit_invoice_details[$value['fk_unit_id']]['total_due'] = (float) $dueAmount[$value['fk_unit_id']];
            } else {
                $dueAmount[$value['fk_unit_id']] = round($dueAmount[$value['fk_unit_id']], $round) + round($invoices['due_amount'], $round);
                $unit_invoice_details[$value['fk_unit_id']]['total_due'] = (float) $dueAmount[$value['fk_unit_id']];
            }
            $unit_invoice_details[$value['fk_unit_id']][] = $invoices;
        }
        return $unit_invoice_details;
    }

    public function memberAdvancesBalance($socId, $units, $arrPost)
    {
        $unitIds = null;
        foreach ($units as $unit) {
            $unitIds .= $unit['unit_id'] . ',';
        }
        $unitIds = trim($unitIds, ',');
        $date = isset($arrPost['transaction_date']) ? $this->getDatabaseDate($arrPost['transaction_date']) : '';

        $advances = array();
        $round = 4;

        /** Advances By Unit Invoices * */
        $arrAdvances = $this->getUnitAdvances(array('unit_ids' => $units, 'payment_date' => $date));
        $arrAdvances = json_decode(json_encode($arrAdvances), true);
        foreach ($arrAdvances as $value) {
            $advances[$value['account_id']] = round((float) $value['cr_amount'], $round) - round((float) $value['dr_amount'], $round);
        }
        return $advances;
    }
    
    public function memberLedgerBalance($socId, $units, $arrPost)
    {
        $ledger_Ids = null;
        $unitLedgers = array();
        foreach ($units as $unit) {
            if (!empty($unit['ledger_account_id'])) {
                $ledger_Ids .= $unit['ledger_account_id'] . ',';
                $unitLedgers[$unit['ledger_account_id']] = $unit['unit_id'];
            }
        }
        $ledger_Ids = trim($ledger_Ids, ',');
        $date = isset($arrPost['transaction_date']) ? $this->getDatabaseDate($arrPost['transaction_date']) : '';

        $ledgers = array();
        $round = 4;
        /** Advances By Unit Invoices * */
        $memberLedgers = $this->memberLedgersBalanceRawQuery($units, $date, $arrPost['fyStartDate']);
        foreach ($memberLedgers as $value) {
            if (array_key_exists($value['ledger_account_id'], $unitLedgers)) {
                $ledgers[$unitLedgers[$value['ledger_account_id']]] = round((float) $value['ledger_bal'], $round);
            }
        }
        return $ledgers;
    }

    public function getSumInvoiceParticularsByInvoiceNumbers(array $data = [])
    {
        if (empty($data['invoice_number'])) {
            return [];
        }

        $results = IncomeInvoiceParticular::select(
                'invoice_number',
                DB::raw('SUM(amount) as amount')
            )
            ->whereIn('invoice_number', $data['invoice_number'])
            ->groupBy('invoice_number')
            ->orderBy('id')
            ->get()
            ->toArray();

        return $results;
    }

    public function getSumInvoiceTaxesByInvoiceNumbers(array $data = [])
    {
        if (empty($data['invoice_number'])) {
            return [];
        }

        $results = ChsoneTaxLog::select(
                'invoice_number',
                DB::raw('SUM(tax_amount) as tax_amount')
            )
            ->whereIn('invoice_number', $data['invoice_number'])
            ->groupBy('invoice_number')
            ->orderBy('tax_log_id')
            ->get()
            ->toArray();

        return $results;
    }

    public function getSumInvoicePaymentByInvoiceNumbers(array $data = [])
    {
        if (empty($data['invoice_number'])) {
            return [];
        }

        $query = IncomeInvoicePayment::select(
                'invoice_number',
                DB::raw('SUM(tds_deducted) as tds_deducted'),
                DB::raw('SUM(late_payment_charges) as late_payment_charges'),
                DB::raw('SUM(writeoff_amount) as writeoff_amount'),
                DB::raw('SUM(payment_amount) as payment_amount')
            )
            ->whereIn('invoice_number', $data['invoice_number']);

        if (!empty($data['payment_date'])) {
            $query->whereDate('payment_date', '<=', $this->getDatabaseDate($data['payment_date']));
        }

        $results = $query
            ->groupBy('invoice_number')
            ->orderBy('payment_id')
            ->get()
            ->toArray();

        return $results;
    }

    public function getSumInvoiceInterestAmountByInvoiceNumbers(array $data = [])
    {
        // Ensure invoice numbers are passed as an array
        foreach ($data['invoice_number'] as $key => $value) {
            $invoiceNumbers[] = explode(',', str_replace("'", '', $value));
        }

        // Run the query using Laravel's Query Builder
        $results = $this->tenantDB()->table('income_unit_invoices')
            ->select(
                'invoice_number',
                DB::raw('SUM(interest_amount) as interest_amount'),
                DB::raw('SUM(roundoff_amount) as roundoff_amount')
            )
            ->whereIn('invoice_number', $invoiceNumbers)
            ->groupBy('invoice_number')
            ->orderBy('unit_invoice_id')
            ->get();

        return $results->toArray();
    }

    public function getSumIncidentInvoicePaymentByInvoiceNumbers(array $data = [])
    {
        // Step 1: Parse the invoice IDs (string to array)
        foreach ($data['invoice_ids'] as $key => $value) {
            $invoiceIds[] = explode(',', str_replace("'", '', $value));
        }

        // Step 2: Start building the query
        $query = $this->tenantDB()->table('income_common_billing_payment as invoicePayment')
            ->select(
                'invoicePayment.fk_common_bill_id as common_bill_id',
                DB::raw('SUM(invoicePayment.tds_deducted) as tds_deducted'),
                DB::raw('SUM(invoicePayment.transaction_charges) as transaction_charges'),
                DB::raw('SUM(invoicePayment.discount_amount) as discount_amount'),
                DB::raw('SUM(invoicePayment.payment_amount) as payment_amount')
            )
            ->whereIn('invoicePayment.fk_common_bill_id', $invoiceIds);

        // Step 3: Add optional payment_date condition
        if (!empty($data['payment_date'])) {
            $paymentDate = $this->getDatabaseDate($data['payment_date']);

            $query->where(function ($q) use ($paymentDate) {
                $q->whereDate('invoicePayment.payment_date', '<=', $paymentDate)
                ->orWhereNull('invoicePayment.payment_date');
            });
        }

        // Step 4: Group and order
        $results = $query
            ->groupBy('invoicePayment.fk_common_bill_id')
            ->orderBy('invoicePayment.payment_id')
            ->get();

        return $results->toArray();
    }

    public function getUnitAdvances(array $data = [])
    {
        // Step 1: Convert unit_ids string to array
        $unitIds = null;
        $unitIds = array_column($data['unit_ids'], 'unit_id');
        $paymentDate = $this->getDatabaseDate($data['payment_date']);

        // Step 2: Start query
        $query = $this->tenantDB()->table('chsone_credit_accounts')
        ->select(
            'account_id',
            'credit_account_id',
            DB::raw("SUM(IF(transaction_type = 'cr', amount, 0)) AS cr_amount"),
            DB::raw("SUM(IF(transaction_type = 'dr', amount, 0)) AS dr_amount")
        )
        ->where('account_context', 'unit')
        ->where('use_credit', 'adjustable')
        ->where('use_credit_for', 'maintenance')
        ->whereIn('account_id', $unitIds);

        // check payemnt date is set and not empty then ass where date condition to query
        if (!empty($paymentDate)) {
            $query->whereDate('payment_date', '<=', $paymentDate);
        }

        $query = $query->groupBy('account_id')
        ->orderBy('credit_account_id')
        ->get();

        $results = $query->toArray();

        return $results;
    }

    public function memberLedgersBalanceRawQuery($units, $date, $fy_start_date)
    {
        $date = $this->getDatabaseDate($date);

        if (empty($fy_start_date)) {
            return [];
        }

        // Parse ledger IDs (assuming comma-separated string like "'1','2','3'")
        $ledgerIds = array_column($units, 'ledger_account_id');

        $query = $this->tenantDB()->table('chsone_ledger_transactions as m')
        ->selectRaw("
            m.ledger_account_id,
            IF(
                SUM(IF(m.transaction_type = 'cr', m.transaction_amount, 0)) > SUM(IF(m.transaction_type = 'dr', m.transaction_amount, 0)),
                IF(
                    p.nature_of_account != 'cr',
                    -1 * (SUM(IF(m.transaction_type = 'cr', m.transaction_amount, 0)) - SUM(IF(m.transaction_type = 'dr', m.transaction_amount, 0))),
                    SUM(IF(m.transaction_type = 'cr', m.transaction_amount, 0)) - SUM(IF(m.transaction_type = 'dr', m.transaction_amount, 0))
                ),
                IF(
                    p.nature_of_account != 'dr',
                    -1 * (SUM(IF(m.transaction_type = 'cr', m.transaction_amount, 0)) - SUM(IF(m.transaction_type = 'dr', m.transaction_amount, 0))),
                    SUM(IF(m.transaction_type = 'dr', m.transaction_amount, 0)) - SUM(IF(m.transaction_type = 'cr', m.transaction_amount, 0))
                )
            ) AS ledger_bal
        ")
        ->leftJoin(DB::raw('(
            SELECT o.ledger_account_id, o.nature_of_account
            FROM chsone_grp_ledger_tree AS o
        ) AS p'), 'p.ledger_account_id', '=', 'm.ledger_account_id');

        if($fy_start_date >= $date) {
            $query = $query->whereBetween('m.transaction_date', [$date, $fy_start_date]);
        } else {
            $query = $query->whereBetween('m.transaction_date', [$fy_start_date, $date]);
        }

        $query = $query->whereIn('m.ledger_account_id', $ledgerIds)
        ->groupBy('m.ledger_account_id')
        ->get();

        $result = $query->toArray();

        // Execute query
        return json_decode(json_encode($result), true);
    }
}