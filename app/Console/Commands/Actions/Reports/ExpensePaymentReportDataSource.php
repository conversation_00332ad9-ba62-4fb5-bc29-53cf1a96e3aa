<?php

namespace App\Console\Commands\Actions\Reports;

use Illuminate\Support\Facades\DB;
use App\Console\Commands\Action;
use Carbon\Carbon;

class ExpensePaymentReportDataSource extends Action
{
    protected $signature = 'datasource:expensePaymentReport {flowId} {parentId} {input}';
    protected $description = 'Expense Payment Report Data Source';

    protected $formatter = [];

    public function apply()
    {
        $from_date = date('Y-m-01');
        $to_date = date('Y-m-t');

        if (!empty($this->input['filters'])) {
            $date = $this->getFromAndToDate($this->input['filters']);
            $from_date = $date[0];
            $to_date = $date[1];
        }

        $data = [
            'from_date' => $from_date,
            'to_date' => $to_date,
            'soc_id' => $this->input['company_id'] ?? null,
        ];

        if (!empty($this->input['filters'])) {
            $filters = $this->input['filters'];
            
            if (isset($filters['vendor'])) {
                $data['vendor'] = $filters['vendor'];
            }
            
            if (isset($filters['status'])) {
                $data['status'] = $filters['status'];
            }
            
            if (isset($filters['mode'])) {
                $data['mode'] = $filters['mode'];
            }
            
            if (isset($filters['type'])) {
                $data['type'] = $filters['type'];
            }
        }

        $dataArr = $this->expensePaymentReport($data);
        $this->data = $dataArr;
    }

    public function expensePaymentReport($data)
    {
        $expense = $this->getVendorPaymentsReportDetails($data);
        $expenseData = $this->prepareExpensePaymentReportData($expense);

        return $expense;
    }

    public function getVendorPaymentsReportDetails($data)
    {
        $query = $this->tenantDB()
            ->table('expense_invoice_payment_tracker as vendorpayment')
            ->leftJoin('chsone_vendors_master as vendor', 'vendor.vendor_id', '=', 'vendorpayment.vendor_id')
            ->where('vendorpayment.soc_id', $data['soc_id'])
            ->select([
                'vendorpayment.payment_date',
                'vendorpayment.payment_number',
                'vendorpayment.payment_mode',
                'vendorpayment.bill_type',
                'vendorpayment.invoice_number',
                'vendorpayment.transaction_reference',
                'vendor.vendor_name',
                'vendorpayment.payment_amount',
                'vendorpayment.writeoff_amount',
                'vendorpayment.status'
            ])
            ->orderByDesc('vendorpayment.id');

        // Apply basic date filters
        if ($data['from_date']) {
            $query->where('vendorpayment.payment_date', '>=', $data['from_date']);
        }
        
        if ($data['to_date']) {
            $query->where('vendorpayment.payment_date', '<=', $data['to_date']);
        }

        // Apply vendor filter if provided
        if (!empty($data['vendor'])) {
            $query->where('vendorpayment.vendor_id', $data['vendor']);
        }

        // Apply status filter if provided
        if (!empty($data['status'])) {
            $statusArray = [
                'Y' => 'Y',
                'N' => 'N',
                'P' => 'P',
                'reversed' => 'reversed'
            ];
            $query->where('vendorpayment.status', $statusArray[$data['status']]);
        }

        // Apply payment mode filter if provided
        if (!empty($data['mode'])) {
            $query->where('vendorpayment.payment_mode', $data['mode']);
        }

        // Apply bill type filter if provided
        if (!empty($data['type'])) {
            if ($data['type'] == 'Advance') {
                $query->whereNull('vendorpayment.invoice_number');
            } else if ($data['type'] == 'Cash Purchase') {
                $query->where('vendorpayment.invoice_number', '!=', '')
                     ->where('vendorpayment.vendor_id', 0);
            } else {
                $query->where('vendorpayment.invoice_number', '!=', '')
                     ->where('vendorpayment.vendor_id', '!=', 0);
            }
        }

        return $query->get();
    }

    public function prepareExpensePaymentReportData($data)
    {
        $header = [
            'Payment Date' => 'date',
            'Payment Number' => 'text',
            'Mode' => 'text',
            'Type' => 'text',
            'Payment Of' => 'text',
            'Payment Reference' => 'text',
            'Vendor' => 'text',
            'Paid Amount' => 'amount',
            'Write Off' => 'amount',
            'Status' => 'text',
        ];

        $statusMapping = [
            'Y' => 'cleared',
            'N' => 'bounced',
            'P' => 'submitted',
            'reversed' => 'reversed'
        ];
        
        $data = collect($data)->map(function($row) use ($statusMapping) {
            $row = (array)$row;  // Convert stdClass to array
            $row['status'] = ucfirst($statusMapping[$row['status']] ?? $row['status']);
            
            if (empty($row['bill_type'])) {
                if (empty($row['invoice_number'])) {
                    $row['bill_type'] = 'Advance';
                } else {
                    $row['bill_type'] = empty($row['vendor_name']) ? 'Cash Purchase' : 'Vendor Bill';
                }
            } else {
                $row['bill_type'] = ucfirst($row['bill_type']);
            }
            
            $row['payment_mode'] = ($row['payment_mode'] == "cashtransfer") ? 'Cash Transfer' : ucfirst($row['payment_mode']);

            return $row;
        });

        return $data;
    }

    public function getFromAndToDate($inputs)
    {
        $from_date = $inputs['startDate'] ?? date("Y-m-01");
        $to_date = $inputs['endDate'] ?? date("Y-m-t");
        return array($from_date, $to_date);
    }
}
