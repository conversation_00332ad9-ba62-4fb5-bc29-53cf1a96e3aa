<?php

namespace App\Console\Commands\Actions\Reports;

use Illuminate\Console\Command;
use App\Console\Commands\Action;
use Illuminate\Support\Facades\DB;

class TenantSignatureDataSource extends Action
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'datasource:tenantSignatureReport {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Tenant Signature Report Data Source';

    protected $formatterByKeys = ['id'];

    protected $formatter = [
        'id' => '',
        'member_name' => '',
        'unit_name' => '',
        'member_email_id' => '',
        'member_mobile_number' => '',
        'member_type_name' => '',
        'member_signature' => ''
    ];

    protected $mapper = [
    ];

    /**
     * Execute the console command.
     */
    public function apply()
    {
        $where = '';

        if (isset($this->input['filters'])) {
            $email = $this->input['filters']['member_email_id'] ?? null;
            $mobile = $this->input['filters']['member_mobile_number'] ?? null;
            if ($email == 'with' &&  $mobile == 'all') {
                $where = 'member_email_id != ""';
            }
            if ($email == 'all' &&  $mobile == 'with') {
                $where = 'member_mobile_number != ""';
            }
            if ($email == 'without' && $mobile == 'with') {
                $where = 'member_email_id = "" AND member_mobile_number != ""';
            }
            if ($email == 'with' &&  $mobile == 'with') {
                $where = 'member_email_id != "" AND member_mobile_number != ""';
            }
            if ($email == 'without' && $mobile == 'without') {
                $where = 'member_email_id = "" AND member_mobile_number = ""';
            }
            if ($email == 'with' && $mobile == 'without') {
                $where = 'member_email_id != "" AND member_mobile_number = ""';
            }

            if ($email == 'without' && $mobile == null) {
                $where = 'member_email_id = ""';
            }

            if ($email == null && $mobile == 'without') {
                $where = 'member_mobile_number = ""';
            }
            if ($email == null && $mobile == null) {
                $where = '';
            }
            if ($email == 'with' && $mobile == null) {
                $where = 'member_email_id != ""';
            }
            if ($email == null && $mobile == 'with') {
                $where = 'member_mobile_number != ""';
            }
        }

        $current_date = date('Y-m-d');
        $obj = $this->tenantDB()->table('chsone_members_master as r')
            ->join('chsone_units_master as rt', 'rt.unit_id', '=', 'r.fk_unit_id')
            ->join('chsone_member_type_master as rd', 'rd.member_type_id', '=', 'r.member_type_id')
            ->select([
                'id',
                DB::raw('CONCAT(rt.soc_building_name, "/", rt.unit_flat_number) AS unit_name'),
                DB::raw('CONCAT(r.member_first_name, " ", r.member_last_name) AS member_name'),
                'r.member_email_id',
                'r.member_mobile_number',
                'rd.member_type_name',
                DB::raw('IF(r.member_last_name = 0, "", "") AS member_signature')
            ])
            ->where('rt.effective_date', '<=', $current_date)
            ->where(function ($query) use ($current_date) {
                $query->where('rt.cancel_date', '=', '0000-00-00')
                    ->orWhere('r.cancel_date', '>=', $current_date);
            })
            ->where(function ($query) use ($current_date) {
                $query->where('r.cancel_date', '>=', $current_date)
                    ->orWhere('r.cancel_date', '=', '0000-00-00');
            })
            ->where('r.status', '=', 1)
            ->where('r.approved', '=', 1)
            ->where('r.member_type_id', '=', 4)
            ->orderBy('rt.soc_building_name')
            ->orderBy('rt.unit_flat_number')
            ->unless(empty($where), function ($query) use ($where) {
                return $query->whereRaw($where);
            });
        $result = $obj->get();
        $this->data = $this->format($result->toArray());
    }
}
