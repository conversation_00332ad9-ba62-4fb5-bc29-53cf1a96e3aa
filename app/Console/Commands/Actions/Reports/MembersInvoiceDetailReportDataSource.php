<?php

namespace App\Console\Commands\Actions\Reports;

use App\Console\Commands\Action;
use Illuminate\Support\Facades\DB;
use DateTime;

class MembersInvoiceDetailReportDataSource extends Action
{
    protected $signature = 'datasource:membersInvoiceDetailReport  {flowId} {parentId} {input}';
    protected $description = 'Members Invoice Detail Report Data Source';

    public function apply()
    {
        try{
            $companyId = $this->input['company_id'];
            $filters = $this->input['filters'] ?? [];
            $unitId = $filters['sort_by']['unit_id'] ?? '';
            $unitName = $filters['sort_by']['unit_name'] ?? '';
            $orderBy = $filters['order'] ?? '';
            

            $fromDate = $this->input['filters']['from_date'] ?? date('Y-m-d');
            // select last date of current month
            $fromDateObj = new DateTime($fromDate);

            $whereMonth = (new DateTime($fromDate))->format('m');
            $whereYear = (new DateTime($fromDate))->format('Y');

            // Move to the last day of the same month
            $lastDayOfMonth = $fromDateObj->format('Y-m-t'); // 't' gives the number of days in the month
            $toDate = $lastDayOfMonth;


            $result = $this->tenantDB()->table('income_unit_invoices as i')
            ->leftJoin('income_invoice_particular as p', 'i.invoice_number', '=', 'p.invoice_number')
            ->leftJoin('chsone_tax_log as t', 'i.invoice_number', '=', 't.invoice_number')
            ->leftJoin('income_invoice_payment_tracker as pt', 'i.invoice_number', '=', 'pt.invoice_number')
            ->selectRaw('
                i.fk_unit_id,
                i.soc_building_name,
                i.unit_name,
                i.member_gstin as gstin,
                i.bill_to,
                i.invoice_number,
                GROUP_CONCAT(p.particular) as particular,
                SUM(p.amount) as total_particular_amount,
                i.principal_amount,
                i.interest_amount,
                i.advance_amount,
                i.outstanding_principal,
                i.outstanding_interest,
                i.roundoff_amount,
                GROUP_CONCAT(p.tax_applicable) as tax_applicable,
                GROUP_CONCAT(p.tax_exemptions) as tax_exemptions,
                i.from_date,
                i.to_date,
                i.outstanding_principal as p_arrears,
                i.outstanding_interest as i_arrears,
                CONCAT(i.from_date, " to ", i.to_date) as period,
                (
                    IFNULL(SUM(p.amount), 0) + 
                    i.interest_amount + 
                    i.roundoff_amount + 
                    i.outstanding_principal + 
                    i.outstanding_interest
                ) as payable,
                SUM(CASE WHEN p.particular = "MaintenanceFee" THEN p.amount ELSE 0 END) as maintenance_fee,
                SUM(CASE WHEN p.particular = "SinkingFund" THEN p.amount ELSE 0 END) as sinking_fund,
                SUM(CASE WHEN p.particular = "Interest" THEN p.amount ELSE 0 END) as interest,
                SUM(CASE WHEN p.particular = "InvoiceAmount" THEN p.amount ELSE 0 END) as invoice_amount,
                SUM(CASE WHEN p.particular = "Receipt" THEN p.amount ELSE 0 END) as receipt,
                (
                    IFNULL(i.outstanding_principal, 0) + 
                    IFNULL(i.outstanding_interest, 0)
                ) as net_due,
                t.tax_amount as tax,
                pt.payment_amount as receipt,
                i.advance_amount as advance_credit
            ')
    
            ->where('i.soc_id', $companyId)
            ->where('i.status', '!=', 'cancelled')
            ->where('i.fk_unit_id', '!=', 0)
            ->whereRaw('? BETWEEN i.from_date AND i.to_date', [$fromDate])
            ->groupBy('i.invoice_number')
            ->orderBy('i.fk_unit_id', 'asc');
        
            if (!empty($orderBy)) {
                $sortField = 'i.unit_name';
                if (!empty($unitId)) {
                    $sortField = 'i.fk_unit_id';
                } elseif (!empty($unitName)) {
                    $sortField = 'i.unit_name';
                }
                $result = $result->orderBy($sortField, $orderBy);
            }
        
            try {
                $result = $result->get();
            } catch (\Exception $e) {
                dd($e);
            }

            $invoice_period = $result[0]->from_date;
            $formattedData = [];
            $totalInvoices = 0;
            $totalPayable = 0;
            
            foreach ($result as $row) {
                $formattedData[] = [
                    'sr_no' => ++$totalInvoices,
                    'unit_name' => $row->unit_name,
                    'bill_to' => $row->bill_to,
                    'gstin' => $row->gstin,
                    'invoice_number' => $row->invoice_number,
                    'maintenance_fee' => number_format($row->maintenance_fee, 2),
                    'sinking_fund' => number_format($row->sinking_fund, 2),
                    'interest' => number_format($row->interest, 2),
                    'tax' => number_format($row->tax, 2),
                    'invoice_amount' => number_format($row->invoice_amount, 2),
                    'p_arrears' => number_format($row->p_arrears, 2),
                    'i_arrears' => number_format($row->i_arrears, 2),
                    'payable' => number_format($row->payable, 2),
                    'receipt' => number_format($row->receipt, 2),
                    'net_due' => number_format($row->net_due, 2),
                    'advance_credit' => (int) number_format($row->advance_credit, 2),
                    'period' => $row->period,
                    'credit_adjustment' => number_format(0.00, 2),
                    'from_date' => $row->from_date,
                    'to_date' => $row->to_date
                ];
                
                
                $totalPayable += $row->payable;

            }

            $summary = [
                'total_invoices' => count($result),
                'invoice_preiod' => $result[0]->period,
                'total_payable' => number_format($totalPayable, 2),
            ];

            // $summary = json_decode(json_encode($summary), true);


            $this->data = [
                $formattedData,
                [$summary]
            ];

            $this->meta['schema'] = [
                "table" => [
                    "tableTitle" => [
                        "Members Invoice Detail Report",
                        "Summary"
                    ],
                    "extraFilters" => [
                        "from_date" => [
                            "title" => "From Date",
                            "type" => "date"
                        ]
                    ],
                    "filter_by" => [
                       "unit_id" => [
                             "title" => "Unit Creation Date",
                             "options" => [
                                "asc" => "Ascending",
                                "desc" => "Descending"
                             ],
                             "defaultValue" => "asc"
                            ],
                        "unit_name" => [
                            "title" => "Unit Name",
                            "options" => [
                                "asc" => "Ascending",
                                "desc" => "Descending"
                            ],
                            "defaultValue" => "asc"
                        ],
                        "order" => [
                            "title" => "Order",
                            "options" => [
                               "asc" => "Ascending",
                               "desc" => "Descending"
                            ],
                            "defaultValue" => "asc"
                       ],
                    ],
                    "actions" => [
                        [
                            "title" => "Export",
                            "icon" => "ri-file-download-line",
                            "color" => "primary",
                            "variant" => "contained",
                            "redirect" => "/admin/reports/member-invoice-detail-report/export"
                        ]
                    ],
                    "fields" => [
                        "*"
                    ],
                    "columns" => [
                        [
                            [
                                "title" => "Sr No.",
                                "key" => "sr_no"
                            ],
                            [
                                "title" => "Unit Name",
                                "key" => "unit_name"
                            ],
                            [
                                "title" => "Bill To",
                                "key" => "bill_to"
                            ],
                            [
                                "title" => "GSTIN",
                                "key" => "gstin"
                            ],
                            [
                                "title" => "Invoice No.",
                                "key" => "invoice_number"
                            ],
                            [
                                "title" => 'Maintenance Fee',
                                'key' => 'maintenance_fee'
                            ],
                            [
                                'title' => 'Sinking Fund',
                                'key' => 'sinking_fund'
                            ],
                            [
                                'title' => 'Interest',
                                'key' => 'interest'
                            ],
                            [
                                'title' => 'Tax',
                                'key' => 'tax'
                            ],
                            [
                                'title' => 'Invoice Amount',
                                'key' => 'invoice_amount'
                            ],
                            [
                                'title' => 'Principal Arrears',
                                'key' => 'p_arrears'
                            ],
                            [
                                'title' => 'Interest Arrears',
                                'key' => 'i_arrears'
                            ],
                            [
                                'title' => 'Payable',
                                'key' => 'payable'
                            ],
                            [
                                'title' => 'Receipt',
                                'key' => 'receipt'
                            ],
                            [
                                'title' => 'Net Due',
                                'key' => 'net_due'
                            ],
                            [
                                'title' => 'Advance Credit',
                                'key' => 'advance_credit'
                            ],
                            [
                                'title' => '',
                                // Add any additional columns if needed
                                'key' => 'period'
                            ]
                        ],
                        [
                                [
                                'title' => 'Total Invoices',
                                'key' => 'total_invoices'
                                ],
                                [
                                'title' => 'Invoice Period',
                                'key' => 'invoice_preiod'
                                ],
                                [
                                'title' => 'Total Payable',
                                'key' => 'total_payable'
                                ]
                        ]
                    ]
                ]
            ];
        }
        catch (\Exception $e) {
            return [
                'status' => 500,
                'message' => 'Error fetching data: ' . $e->getMessage()."\n".$e->getTraceAsString()."\n".$e->getFile()."\n".$e->getLine(),
            ];
        }
    }
}