<?php

namespace App\Console\Commands\Actions\Reports;

use Illuminate\Support\Facades\DB;
use App\Console\Commands\Action;
use Illuminate\Support\Facades\Route;

class TdspayableReportDataSource extends Action
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'datasource:tdspayableReport {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'TDS Payable Report Data Source';


    /**
     * Execute the console command.
     */
    public function apply()
    {
        $request = $this->input;
        $from_date = date('Y-m-01');
        $to_date = date('Y-m-t');
        $Route = Route::current();
        if ($Route->uri() == 'api/admin/income-details/tdspayableReport/download/{type}') {
            $this->hugeData = true;
        }
        $page = $this->input['page'] ?? 1;
        $per_page = !empty($this->input['per_page']) ? $this->input['per_page']: ($this->hugeData ? 100000 : 10);
        $offset = ($page - 1) * $per_page;
        $this->meta['pagination']['page'] = $page;
        
        if (!empty($this->input['filters'])) {
            $date = $this->getFromAndToDate($this->input['filters']);
            $from_date = $date[0];
            $to_date = $date[1];
        }

            $summary = $this->tenantDB()->table('chsone_vendor_bill_master')
                ->selectRaw('vendor_bill_id as id,SUM(vendor_bill_tds) AS vendor_bill_tds, SUM(vendor_bill_amount) AS vendor_bill_amount')
                ->whereBetween('vendor_bill_date', [$from_date, $to_date])
                // ->where('status', 1)
                ->where('vendor_bill_tds', '>', 0)
                ->whereNotIn('status', [0, 3])
                ->get();



            $result = $this->tenantDB()->table('chsone_vendor_bill_particulars AS d')
            ->leftJoin('chsone_vendor_bill_master AS a', 'd.bill_id', '=', 'a.vendor_bill_id')
            ->leftJoin('chsone_vendors_master AS b', 'a.vendor_id', '=', 'b.vendor_id')
            ->leftJoin('tds_rates_master AS c', 'b.vendor_tds_id', '=', 'c.id')
            ->select(
                'd.particulars_id AS id',
                'b.vendor_name',
                'b.vendor_address',
                'a.vendor_bill_num',
                'b.vendor_pan_num',
                'b.vendor_is_company',
                'c.section',
                'a.vendor_bill_date',
                'a.vendor_bill_date AS vendor_bill_date_of_deduction',
                DB::raw('SUM(d.amount) AS taxable_amount'),
                DB::raw('SUM(d.total_tax) AS gst_amount'),
                DB::raw('ROUND(SUM(d.amount + d.total_tax), 2) AS total_bill_amount'),
                DB::raw('COALESCE(c.rate, 0) AS rate'),
                'a.vendor_bill_tds',
                'a.vendor_bill_amount'
            )
            ->whereBetween('a.vendor_bill_date', [$from_date, $to_date])
            ->where('a.status', '=', 1)
            ->where('a.vendor_bill_tds', '>', 0)
            ->whereNotIn('a.status', [0, 3])
            ->groupBy(
                'd.bill_id',
                'b.vendor_name',
                'b.vendor_address',
                'a.vendor_bill_num',
                'b.vendor_pan_num',
                'b.vendor_is_company',
                'c.section',
                'a.vendor_bill_date',
                'vendor_bill_date_of_deduction',
                DB::raw('COALESCE(c.rate, 0)'),
                'a.vendor_bill_tds',
                'a.vendor_bill_amount'
            );
            
        $count = $result->get()->count();
        $result = $result->offset($offset);
         $result = $result->limit($per_page);
         $result = $result->get();
          // Convert summary to an array (because it's a single object, not a collection)

        // Merge the two arrays
            $this->data = array_merge( [$result],[$summary]);
            $this->meta['pagination']['total'] = $count;



            // dd($this->data);


    }

    public function getFromAndToDate($inputs){
        // $date = $inputs['transaction_date'];
        // $dateRange = explode(',', $date);
        $dateRange = $inputs;
        $from_date = $dateRange['startDate'] ?? date("Y-m-01");
        $to_date = $dateRange['endDate'] ?? date("Y-m-t");
        return array($from_date, $to_date);
    }
    
}
