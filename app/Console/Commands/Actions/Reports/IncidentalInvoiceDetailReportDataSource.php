<?php

namespace App\Console\Commands\Actions\Reports;

use Illuminate\Support\Facades\DB;
use App\Console\Commands\Action;

class IncidentalInvoiceDetailReportDataSource extends Action
{
    protected $signature = 'datasource:incidentalInvoiceDetailReport  {flowId} {parentId} {input}';
    protected $description = 'Incidental Invoice Detail Report Data Source';

    public function apply()
    {
        try {
            $companyId = $this->input['company_id'];
            // $page = $this->input['page'] ?? 1;
            // $per_page = !empty($this->input['per_page']) ? $this->input['per_page'] : 10;

            // $offset = ($page - 1) * $per_page;
            // Pending invoice date filtration
            $invoiceDate = $this->input['filters']['invoice_date'] ?? date('Y-m-d');

            // First, get all charge types
            $chargeTypes = $this->tenantDB()->table('income_common_area_charges')
                ->where('soc_id', $companyId)
                ->select('id', 'particular')
                ->get();

            // Build dynamic select array
            $selectArray = [
                DB::raw('CONCAT(um.soc_building_name, " ", um.unit_flat_number) as unit_name'),
                'icb.fk_unit_id as fk_unit_id',
                DB::raw('CONCAT(mm.member_first_name, " ", mm.member_last_name) as member_name'),
                'icb.invoice_number as invoice_number',
                'icb.fk_member_id as fk_member_id',
            ];

            // Add dynamic CASE statements for each charge type
            foreach ($chargeTypes as $type) {
                // Sanitize column name - remove special characters and spaces
                $columnName = preg_replace('/[^a-zA-Z0-9]/', '_', $type->particular);
                $columnName = strtolower(trim($columnName, '_'));
                
                $selectArray[] = DB::raw("SUM(CASE WHEN icb.billing_type = {$type->id} THEN icb.amount ELSE 0 END) as `{$columnName}`");
            }

            // Add remaining static columns
            $selectArray = array_merge($selectArray, [
                'iui.interest_amount as interest',
                DB::raw('0 as tax'),
                'icb.amount as invoice_amount',
                DB::raw('0 as credit_adjustment'),
                'iui.outstanding_principal as p_arrears',
                'iui.outstanding_interest as i_arrears',
                DB::raw('(icb.amount + COALESCE(iui.outstanding_principal, 0) + COALESCE(iui.outstanding_interest, 0)) as net_due')
            ]);

            $billingTypes = $this->tenantDB()->table('income_common_area_charges')
            ->where('soc_id', $companyId)
            ->pluck('id')
            ->toArray();
        
        // Fetch all raw rows, one per particular per invoice per unit per member
            $rows = $this->tenantDB()->table('income_common_billing_charges as icb')
                ->leftJoin('income_common_area_charges as c', 'icb.billing_type', '=', 'c.id')
                ->leftJoin('chsone_units_master as um', 'icb.fk_unit_id', '=', 'um.unit_id')
                ->leftJoin('chsone_members_master as mm', 'icb.fk_member_id', '=', 'mm.id')
                ->leftJoin('income_unit_invoices as iui', 'icb.invoice_number', '=', 'iui.invoice_number')
                ->select([
                    DB::raw('CONCAT(um.soc_building_name, " ", um.unit_flat_number) as unit_name'),
                    'um.unit_id',
                    DB::raw('CONCAT(mm.member_first_name, " ", mm.member_last_name) as member_name'),
                    'mm.id as member_id',
                    'icb.invoice_number',
                    'iui.outstanding_principal as p_arrears',
                    'iui.outstanding_interest as i_arrears',
                    'icb.fk_unit_id',
                    'icb.fk_member_id',
                    'icb.billing_type',
                    'icb.amount as particular_amount',
                    'c.particular as particular_name',
                    'iui.interest_amount',
                    'icb.created_date',
                    'icb.payment_status',
                ])
                ->where('icb.soc_id', $companyId)
                ->whereIn('icb.billing_type', $billingTypes)
                ->where('icb.from_date', '<=', $invoiceDate)
                ->where('icb.payment_status', '!=', 'cancelled')
                ->orderBy('icb.created_date', 'asc')
                ->groupBy('icb.billing_type', 'icb.fk_unit_id', 'icb.fk_member_id')
                ->selectRaw('MAX(icb.created_date) as latest_date');

            // $count = $rows->get()->count();
            // $rows = $rows->offset($offset);
            // $rows = $rows->limit($per_page);
            $rows = $rows->get();
            
            // Mapping from particular_name to result key
            $particularKeyMap = [];
            foreach ($chargeTypes as $type) {
                $columnName = preg_replace('/[^a-zA-Z0-9]/', '_', $type->particular);
                $columnName = strtolower(trim($columnName, '_'));
                $particularKeyMap[$type->particular] = $columnName;
            }

        // Convert $rows to array if it's a collection
        $invoiceKeys = [
            'move_in', 'move_out', 'renovation', 'non_occupancy_charges___arrears',
            'transfer_charges', 'share_transfer_fee',
            'entrance___membership_fees', 'cheque_return_charges', 'dues_of_use_of_society_premises',
            'reimbursement_of_legal_expenses', 'interest_on_late_payment', 'interest', 'tax'
        ];
        $result = $rows->map(function ($row) use ($particularKeyMap, $invoiceKeys) {
            $rowArr = (array) $row;

            foreach ($particularKeyMap as $key => $field) {
                $rowArr[$field] = 0;
            }            
            $particularName = $rowArr['particular_name'] ?? null;
            $particularAmount = $rowArr['particular_amount'] ?? null;
            if ($particularName && isset($particularKeyMap[$particularName])) {
                $rowArr[$particularKeyMap[$particularName]] = $particularAmount;
            }
            // Calculate invoice_amount as sum of invoiceKeys
            $particularAmount = isset($rowArr['particular_amount']) ? floatval($rowArr['particular_amount']) : 0;
            $interestAmount = isset($rowArr['interest_amount']) ? floatval($rowArr['interest_amount']) : 0;
            $tax = isset($rowArr['tax']) ? floatval($rowArr['tax']) : 0;
            
            $invoiceAmount = $particularAmount + $interestAmount + $tax;
            $rowArr['invoice_amount'] = $invoiceAmount;
            // Calculate net_due = invoice_amount - credit_adjustment + p_arrears + i_arrears
            $creditAdjustment = isset($rowArr['credit_adjustment']) ? floatval($rowArr['credit_adjustment']) : 0;
            $pArrears = isset($rowArr['p_arrears']) ? floatval($rowArr['p_arrears']) : 0;
            $iArrears = isset($rowArr['i_arrears']) ? floatval($rowArr['i_arrears']) : 0;
            $rowArr['net_due'] = $invoiceAmount - $creditAdjustment + $pArrears + $iArrears;
            return $rowArr;
        });
            // Build dynamic headers using original particulars for display
            $headers = ['Unit Name', 'Bill To', 'Invoice Number'];
            foreach ($chargeTypes as $type) {
                $headers[] = $type->particular;
            }
            $headers = array_merge($headers, [
                'Interest', 'Tax', 'Invoice Amount', 'Credit/Adjustment',
                'P Arrears', 'I Arrears', 'Net Due'
            ]);
 

            $summary = [
                'invoice_count' => $result->count() - 1,
                'date' => $invoiceDate,
                'total_payable' => $result->sum('net_due'),
            ];
            
                $this->data = [
                   $result,
                    [$summary]
                ];
        } catch (\Exception $e) {
           dd('IncidentalInvoiceDetailReport Error: ' . $e->getMessage());
        }

    }
}
