<?php

namespace App\Console\Commands\Actions\Reports;

use App\Console\Commands\Action;

class ExpensePaymentReportDownloadDataSource extends Action
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'datasource:expensePaymentReportDownload {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Get Expense Payment Report Data for Download';

    protected $formatter = [
        'payment_date' => '',
        'payment_number' => '',
        'payment_mode' => '',
        'bill_type' => '',
        'invoice_number' => '',
        'transaction_reference' => '',
        'vendor_name' => '',
        'payment_amount' => '',
        'writeoff_amount' => '',
        'status' => ''
    ];

    protected $formatterByKeys = [];

    protected $mapper = [];

    /**
     * Execute the console command.
     */
    public function apply()
    {
        // Call the existing expense payment report datasource
        $expensePaymentData = $this->action('datasource:expensePaymentReport', $this->pointer, $this->input);
        
        // Format the data for download
        $this->data = $this->format($expensePaymentData);
    }
}
