<?php

namespace App\Console\Commands\Actions\Reports;

use Illuminate\Console\Command;
use App\Console\Commands\Action;
use Illuminate\Support\Facades\DB;

class ContactDetailReportDataSource extends Action
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'datasource:contactDetailReport {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Contact Detail Report Data Source';

    protected $formatterByKeys = ['id'];

    protected $formatter = [
        'id' => '',
        'member_name' => '',
        'unit_name' => '',
        'member_email_id' => '',
        'member_mobile_number' => ''
    ];

    protected $mapper = [
    ];

    /**
     * Execute the console command.
     */
    public function apply()
    {
        $where = '';

        if (isset($this->input['filters'])) {
            $email = $this->input['filters']['member_email_id'] ?? null;
            $mobile = $this->input['filters']['member_mobile_number'] ?? null;
            if ($email == 'with' &&  $mobile == 'all') {
                $where = 'member_email_id != ""';
            }
            if ($email == 'all' &&  $mobile == 'with') {
                $where = 'member_mobile_number != ""';
            }
            if ($email == 'without' && $mobile == 'with') {
                $where = 'member_email_id = "" AND member_mobile_number != ""';
            }
            if ($email == 'with' &&  $mobile == 'with') {
                $where = 'member_email_id != "" AND member_mobile_number != ""';
            }
            if ($email == 'without' && $mobile == 'without') {
                $where = 'member_email_id = "" AND member_mobile_number = ""';
            }
            if ($email == 'with' && $mobile == 'without') {
                $where = 'member_email_id != "" AND member_mobile_number = ""';
            }

            if ($email == 'without' && $mobile == null) {
                $where = 'member_email_id = ""';
            }

            if ($email == null && $mobile == 'without') {
                $where = 'member_mobile_number = ""';
            }
            if ($email == null && $mobile == null) {
                $where = '';
            }
            if ($email == 'with' && $mobile == null) {
                $where = 'member_email_id != ""';
            }
            if ($email == null && $mobile == 'with') {
                $where = 'member_mobile_number != ""';
            }
        }

        $obj = $this->tenantDB()->table('chsone_members_master as cmm')
            ->join('chsone_units_master as cum', 'cmm.fk_unit_id', '=', 'cum.unit_id')
            ->select(
                DB::raw("CONCAT(cmm.member_first_name, ' ', cmm.member_last_name) AS member_name"),
                DB::raw("CONCAT(cum.soc_building_name, '/', cum.unit_flat_number) AS unit_name"),
                'cmm.member_email_id',
                'cmm.member_mobile_number',
                'cmm.id as id'
            )
            ->where('cmm.status',1)
            // ->where('cmm.approved',1)
            ->where('cmm.member_type_id',1)
            ->unless(empty($where), function ($query) use ($where) {
                return $query->whereRaw($where);
            });

        $obj = $obj->orderBy('id', 'asc');
        $result = $obj->get();
        $this->data = $this->format($result->toArray());
    }
}
