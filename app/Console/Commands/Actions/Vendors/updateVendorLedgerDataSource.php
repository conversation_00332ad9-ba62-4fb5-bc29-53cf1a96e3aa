<?php

namespace App\Console\Commands\Actions\Vendors;

use Illuminate\Console\Command;
use App\Console\Commands\Action;
use App\Models\Tenants\ChsoneLedgerTransaction;

class updateVendorLedgerDataSource extends Action
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'datasource:updateVendorLedger {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Update Vendor Ledger';

    /**
     * Execute the console command.
     */
    public function apply()
    {
        $data = $this->input;

        // first need to find the existing vendor ledger
        $vendorLedger = ChsoneLedgerTransaction::where('ledger_account_id', $data['ledger_account_id'])->first();

        if(!$vendorLedger) {
            $this->message = "Vendor Ledger not found";
            $this->status = 'error';
            $this->statusCode = 400;
        }

        // update the vendor ledger name
        $vendorLedger->update([
            'ledger_account_name'=>$data['vendor_name'].'-'.$data['vendor_id'] ?? '',
        ]);

        $this->message = "Vendor Ledger updated successfully";
        $this->status = 'success';
        $this->statusCode = 200;
        $this->data = $vendorLedger->toArray();
    }
}
