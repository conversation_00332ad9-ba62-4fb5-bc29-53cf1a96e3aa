<?php

namespace App\Console\Commands\Actions\Vendors;

use Illuminate\Console\Command;
use App\Console\Commands\Action;
use App\Models\Tenants\ChsoneGrpLedgerTree;
use App\Models\Tenants\SocAccountFinancialYearMaster;
use App\Models\Tenants\ChsoneLedgerTransaction;
use Illuminate\Support\Facades\DB;
use Illuminate\Database\Query\Grammars\MySqlGrammar;

class fetchDataAddVendorPaymentDataSource extends Action
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'datasource:fetchDataAddVendorPayment {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */
    public function apply()
    {
        $ledger = $this->input['ledger'];

        // if(isset($this->input['id'])) {
        //     $vendor_id = $this->input['id'];

        //     // fetch vendor advance
        //     $advance = $this->getVendorAdvances($vendor_id);
        //     if(empty($advance)) {
        //         $vendorDetails['total_advances'] = 0.00;
        //     } else {
        //         $vendorDetails['total_advances'] = $advance->total_adjustable_advance;
        //     }
        // }

        // fetch cash in hand balance
        $cashInHand = $this->getCashInHand($ledger);
        if(empty($cashInHand)) {
            $vendorDetails['ledger_amount'] = 0.00;
        } else {
            $vendorDetails['ledger_amount'] = $cashInHand;
        }

        // fetch ledger account name
        if(!is_numeric($ledger)) {
            $vendorDetails['label'] = 'Cash In Hand';
        } else {
            $vendorDetails['label'] = ChsoneGrpLedgerTree::where('ledger_account_id', $ledger)->first()->ledger_account_name;
        }

        $this->data = $vendorDetails;
    }

    // function getVendorAdvances($vendorId)
    // {
    //     $obj = $this->tenantDB()->table('chsone_credit_accounts AS cr_acc')
    //         ->selectRaw('SUM(IF(cr_acc.transaction_type = "dr" AND cr_acc.use_credit = "adjustable", cr_acc.amount, 0)) - SUM(IF(cr_acc.transaction_type = "cr" AND cr_acc.use_credit = "adjustable", cr_acc.amount, 0)) AS total_adjustable_advance')
    //         // ->selectRaw('SUM(IF(cr_acc.transaction_type = "dr", cr_acc.amount, 0)) - SUM(IF(cr_acc.transaction_type = "cr", cr_acc.amount, 0)) AS total_balance')
    //         ->join('chsone_vendors_master AS vendormaster', 'cr_acc.account_id', '=', 'vendormaster.vendor_id')
    //         ->where('cr_acc.account_context', '=', 'vendor')
    //         ->groupBy(
    //             'cr_acc.account_id'
    //         )
    //         ->where('vendormaster.vendor_id', $vendorId);

    //     $obj = $this->filter($obj);
    //     $result = $obj->first();
    //     return $result;
    // }

    function getCashInHand($ledger)
    {
        if (!is_numeric($ledger)) {
            $ledger = str_replace('_', ' ', $ledger);
            $ledger = strtolower($ledger);

            $tree = ChsoneGrpLedgerTree::where('context', $ledger)->where('entity_type', 'ledger')->where('soc_id', $this->input['company_id'])->first();
            // $tree = ChsoneGrpLedgerTree::where('ledger_account_name', $ledger)->where('soc_id', $this->input['company_id'])->first();
            $ledger = $tree->ledger_account_id;
        }

        $result = (float)str_replace(',', '', $this->cashLedgerBal($ledger, $this->input["company_id"]));

        return $result;
    }

    public function cashLedgerBal($ledger_id, $soc_id, $start_date = '', $end_date = '')
    {
        if (!empty($ledger_id)) {
            $postedValues = request()->post();
            $fetch_list = isset($postedValues['fetch_list']) ? $postedValues['fetch_list'] : 'N';
            
            $arrCurrentFYDetailObj = new SocAccountFinancialYearMaster();
            $arrCurrentFYDetail = $arrCurrentFYDetailObj->getCurrentFYDetail($soc_id);
            $arrFYDetail = json_decode(json_encode($arrCurrentFYDetail), true);
            $start_date = $arrFYDetail['fy_start_date'];
            $end_date = $arrFYDetail['fy_end_date'];
        }
        $ledgerTransactionObj = new ChsoneLedgerTransaction();
        $filter_criteria = array();
        if(empty($start_date))
        {
            $translist  = $ledgerTransactionObj->getLedgerTransactions($soc_id, $ledger_id, $filter_criteria, 'ledger');
        }
        else
        {
            $translist  = $ledgerTransactionObj->getLedgerTransactionsByFY(array('soc_id' => $soc_id, 'ledger_id' => $ledger_id, 'criteria' => $filter_criteria, 'ledger' => 'ledger', 'start_date' => $start_date, 'end_date' => $end_date));
        }
        // $translist = $ledgerTransactionObj->getLedgerTransactionsByFY(array('soc_id' => $soc_id, 'ledger_id' => $ledger_id, 'criteria' => $filter_criteria, 'ledger' => 'ledger', 'start_date' => $start_date, 'end_date' => $end_date));
        if (isset($fetch_list) && $fetch_list == 'Y') {
            return response()->json($translist, 200);
        }
        $translist = json_decode(json_encode($translist), true);
        
        $ChsoneGrpLedgerTreeObj = new ChsoneGrpLedgerTree();
        $ledger = $ChsoneGrpLedgerTreeObj->getLedger($ledger_id, 1);
        if (!empty($ledger)) {
            $ledger = json_decode(json_encode($ledger), true);
        }

        $overall_balance = 0.00;
        $overall_nature = $ledger['nature_of_account'];
        if (!empty($translist['transaction_total'])) {
            if ($translist['transaction_total'][0]['credit_amount'] > $translist['transaction_total'][0]['debit_amount']) {
                $overall_nature = 'cr';
                $overall_balance = $translist['transaction_total'][0]['credit_amount'] - $translist['transaction_total'][0]['debit_amount'];
            } elseif ($translist['transaction_total'][0]['credit_amount'] < $translist['transaction_total'][0]['debit_amount']) {
                $overall_nature = 'dr';
                $overall_balance = $translist['transaction_total'][0]['debit_amount'] - $translist['transaction_total'][0]['credit_amount'];
            }
        }
        $overall_balance = ($overall_nature != $ledger['nature_of_account']) ? (-1) * $overall_balance : $overall_balance;
        return (float)number_format($overall_balance, 2, '.', '');
    }
}
