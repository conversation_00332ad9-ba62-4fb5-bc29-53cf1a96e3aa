<?php

namespace App\Console\Commands\Actions\Vendors;

use Illuminate\Console\Command;
use App\Console\Commands\Action;
use App\Models\Tenants\ChsoneVendorBillMaster;
use App\Models\Tenants\ChsoneVendorsMaster;
use App\Models\Tenants\ChsoneGrpLedgerTree;
use App\Models\Tenants\ExpenseInvoicePaymentTracker;
use App\Models\Tenants\ChsoneVendorBillPaymentDetail;
use App\Models\Tenants\ChsoneLedgerTransaction;
use Illuminate\Support\Facades\DB;

class addVendorCashPurchaseDataSource extends Action
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'datasource:addVendorCashPurchase {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Action for addVendorCashPurchaseDataSource';

    /**
     * Execute the console command.
     */
    public function apply()
    {
        $data = $this->input;

        // need to find out first vendor id and vendor name from chsone_vendors_master table
        $vendor = ChsoneVendorsMaster::where('soc_id', $data['company_id'])->first();
        $ledger_account_name = $vendor->vendor_name.'-'.$vendor->vendor_id;

        // need to find out ledger id from chsone_ledger_master table
        $ledgerDetails = ChsoneGrpLedgerTree::where('soc_id', $data['company_id'])->where('ledger_account_name', $ledger_account_name)->first();

        $vendorBill = new ChsoneVendorBillMaster;

        // $vendorBill->vendor_bill_id=$data['vendor_bill_id'] ?? '';
        // $vendorBill->vendor_id=$data['vendor_id'] ?? 1;
        $vendorBill->et_id=$data['et_id'] ?? '';
        $vendorBill->soc_id=$data['company_id'] ?? '';
        $vendorBill->purchase_form_id=$data['purchase_form_id'] ?? '0';
        $vendorBill->vendor_bill_type_purchase=strtolower($data['purchase_type']) ?? 'cash';
        $vendorBill->vendor_bill_num=$data['vendor_bill_number'] ?? '';
        $vendorBill->details=$data['bill_description'] ?? '';
        $vendorBill->vendor_bill_date=$data['vendor_bill_date'] ?? '';
        $vendorBill->vendor_bill_amount=$data['vendor_bill_amount'] ?? '';
        $vendorBill->vendor_bill_tds=$data['vendor_bill_tds'] ?? '0.000';
        $vendorBill->vendor_bill_writeoff=$data['vendor_bill_writeoff'] ?? '0.000';
        $vendorBill->vendor_bill_roundoff=$data['vendor_bill_roundoff'] ?? '0.000';
        $vendorBill->vendor_bill_due_date=$data['vendor_bill_date'] ?? '';
        $vendorBill->added_on=date("Y-m-d h:i:s");
        $vendorBill->is_billable=$data['is_billable'] ?? '0';
        $vendorBill->is_billed=$data['is_billed'] ?? '0';
        $vendorBill->is_rcm=$data['is_rcm'] ?? '0';
        $vendorBill->status=1;
        $vendorBill->payment_status='paid';
        $vendorBill->ledger_to_id=$data['ledger_to_id'] ?? $ledgerDetails->ledger_account_id;
        $vendorBill->advance_consumed=$data['advance_consumed'] ?? '0.000';

        $vendorBill->save();
        if ($vendorBill->save()) {

            // insert the new entry in the expense_invoice_payment_tracker table
            $insertExpensePaymentEntry = $this->insertExpensePaymentEntry($data, $vendorBill->vendor_bill_id);

            if(!$insertExpensePaymentEntry) {
                $this->message = "Error in creating vendor cash purchase entry in expense_invoice_payment_tracker table.";
                $this->status = 'error';
                $this->statusCode = 400;
                $this->data = [];
                return;
            }

            // insert the new entry in the chsone_vendor_bill_payment_details table
            $insertVendorBillPaymentDetails = $this->insertVendorBillPaymentDetails($insertExpensePaymentEntry);

            if(!$insertVendorBillPaymentDetails) {
                $this->message = "Error in creating vendor cash purchase entry in chsone_vendor_bill_payment_details table.";
                $this->status = 'error';
                $this->statusCode = 400;
                $this->data = [];
                return;
            }

            // insert entry in chsone_ledger_transaction table
            $insertLedgerTransaction = $this->insertLedgerTransaction($insertExpensePaymentEntry, $data);

            if(!$insertLedgerTransaction) {
                $this->message = "Error in creating vendor cash purchase entry in chsone_ledger_transaction table.";
                $this->status = 'error';
                $this->statusCode = 400;
                $this->data = [];
                return;
            }

            $this->message = "Vendor Cash Purchase created successfully";
            $this->status = 'success';
            $this->statusCode = 200;
            $this->data = $vendorBill->toArray();
        } else {
            $this->message = "Unable to create vendor cash purchase";
            $this->status = 'error';
            $this->statusCode = 400;
            $this->data = $vendorBill->toArray();
        }
    }

     /**
     * Insert the new entry in the expense_invoice_payment_tracker table
     */
    public function insertExpensePaymentEntry($data, $vendor_bill_id)
    {
        $expensePaymentEntry = new ExpenseInvoicePaymentTracker;

        $expensePaymentEntry->soc_id=$data['company_id'] ?? '';
        $expensePaymentEntry->vendor_id = $data['vendor_id'] ?? '0';
        $expensePaymentEntry->invoice_number = $vendor_bill_id;
        $expensePaymentEntry->payment_number = substr((uniqid(mt_rand())), 0, 6);
        $expensePaymentEntry->bill_type = 'Cash Purchase';
        $expensePaymentEntry->payment_mode = strtolower($data['purchase_type']) ?? 'cash';
        $expensePaymentEntry->paid_by = $data['user_id'] ?? '0';
        $expensePaymentEntry->total_due_amount = '0.000';
        $expensePaymentEntry->writeoff_amount = $data['vendor_bill_writeoff'] ?? '0.000';
        $expensePaymentEntry->payment_amount = $data['vendor_bill_amount'] ?? '0.000';
        $expensePaymentEntry->status = 'Y';
        $expensePaymentEntry->transaction_status = 'complete';
        $expensePaymentEntry->payment_note = 'Vendorbill Payment - Bill No. '.$data['vendor_bill_number'].' on date '.$data['vendor_bill_date'];
        $expensePaymentEntry->payment_date = $data['vendor_bill_date'] ?? '';
        $expensePaymentEntry->cheque_date = $data['vendor_bill_date'] ?? '';
        $expensePaymentEntry->updated_by = $data['user_id'] ?? '0';
        $expensePaymentEntry->updated_date = date("Y-m-d h:i:s");
        $expensePaymentEntry->created_by = $data['user_id'] ?? '0';
        $expensePaymentEntry->created_date = date("Y-m-d h:i:s");

        // create other information
        $arrOtherInfo = array();
        $arrOtherInfo = [
            'bank_ledger' => $data['bank_ledger'] ?? '',
            'cash_ledger' => $data['cash_ledger'] ?? '',
            'card_ledger' => $data['card_ledger'] ?? '',
            'credit_used_type' => $data['credit_used_type'] ?? '',
            'adjustable_date' => $data['adjustable_date'] ?? '',
            'refundable_vendor_bill_payment_amount' => $data['refundable_vendor_bill_payment_amount'] ?? '',
            'adjustable_vendor_bill_payment_amount' => $data['adjustable_vendor_bill_payment_amount'] ?? '',
        ];

        $expensePaymentEntry->other_information = serialize($arrOtherInfo);

        $expensePaymentEntry->save();

        // if save successfully then return the data otherwise false
        if ($expensePaymentEntry->save()) {
            return $expensePaymentEntry;
        } else {
            return false;
        }
    }

    /**
     * Insert the new entry in the chsone_vendor_bill_payment_details table
     */
    public function insertVendorBillPaymentDetails($expensePaymentEntry)
    {
        // find out to_ledger_id from chsone_grp_ledger_tree table whose context is cash and entity_type is ledger
        $ledger_account_id = $this->tenantDB()->table('chsone_grp_ledger_tree')
            ->where('context', 'cash')
            ->where('entity_type', 'ledger')
            ->where('ledger_account_name', 'like', '%Cash%')
            ->orderBy('ledger_account_id', 'desc')
            ->first();
        $to_ledger_id = $ledger_account_id->ledger_account_id ?? 0;

        // find out from_ledger_id from chsone_grp_ledger_tree table whose context is vendor and context_ref_id is 1
        $from_ledger_id = $this->tenantDB()->table('chsone_grp_ledger_tree')
            ->where('context', 'vendor')
            ->where('context_ref_id', '1')
            ->first();
        $from_ledger_id = $from_ledger_id->ledger_account_id ?? 0;

        $vendorBillPaymentDetails = new ChsoneVendorBillPaymentDetail;

        $vendorBillPaymentDetails->vendor_bill_id = $expensePaymentEntry->invoice_number;
        $vendorBillPaymentDetails->soc_id = $expensePaymentEntry->soc_id;
        $vendorBillPaymentDetails->to_ledger_id = $to_ledger_id;
        $vendorBillPaymentDetails->from_ledger_id = $from_ledger_id;
        $vendorBillPaymentDetails->vendor_bill_payment_amount = $expensePaymentEntry->payment_amount;
        $vendorBillPaymentDetails->tds = $expensePaymentEntry->tds ?? '0.000';
        $vendorBillPaymentDetails->write_off = $expensePaymentEntry->writeoff_amount ?? '0.000';
        $vendorBillPaymentDetails->expense_tracker_id = $expensePaymentEntry->id;
        $vendorBillPaymentDetails->vendor_bill_payment_date = $expensePaymentEntry->payment_date;
        $vendorBillPaymentDetails->vendor_bill_payment_mode = $expensePaymentEntry->payment_mode;
        $vendorBillPaymentDetails->vendor_bill_payment_comments = $expensePaymentEntry->payment_note;
        $vendorBillPaymentDetails->added_on = date("Y-m-d h:i:s");
        $vendorBillPaymentDetails->status = '1';

        $vendorBillPaymentDetails->save();

        // if save successfully then return true otherwise false
        if ($vendorBillPaymentDetails->save()) {
            return true;
        } else {
            return false;
        }
    }

    /**
     * Insert entry in chsone_ledger_transaction table
     */
    public function insertLedgerTransaction($expensePaymentEntry, $data)
    {
        try{
        // find out dr_ledger_account_id from chsone_grp_ledger_tree table
        $ledger_account_id = $this->tenantDB()->table('chsone_expense_tracker')
            ->where('et_id', $data['et_id'])
            ->first();
        $dr_ledger_account_id = $ledger_account_id->et_ledger_account_id ?? 0;

        // fetch the dr_ledger_acocunt_name from chsone_grp_ledger_tree table
        $dr_ledger_account_name = $this->tenantDB()->table('chsone_grp_ledger_tree')
            ->where('ledger_account_id', $dr_ledger_account_id)
            ->first();
        $dr_ledger_account_name = $dr_ledger_account_name->ledger_account_name ?? 0;

        // find out cr_ledger_account_id from chsone_grp_ledger_tree table
        $ledger_account_id = $this->tenantDB()->table('chsone_grp_ledger_tree')
            ->where('context', 'cash')
            ->where('entity_type', 'ledger')
            ->first();
        $cr_ledger_account_id = $ledger_account_id->ledger_account_id ?? 0;
        $cr_ledger_account_name = $ledger_account_id->ledger_account_name ?? '';
        $billDescription = $data['bill_description'] ?? '';
        // now insert the reversal entry in chsone_ledger_transaction table
        $lastInsertedId = $this->tenantDB()->table('chsone_ledger_transactions')->insertGetId([
            'soc_id' => $expensePaymentEntry->soc_id,
            'transaction_date' => $expensePaymentEntry->payment_date,
            'ledger_account_id' => $dr_ledger_account_id,
            'ledger_account_name' => $dr_ledger_account_name,
            'voucher_type' => 'expense',
            'voucher_reference_number' => $data['vendor_bill_number'],
            'voucher_reference_id' => $expensePaymentEntry->invoice_number,
            'transaction_type' => 'dr',
            'payment_mode' => $expensePaymentEntry->payment_mode,
            'payment_reference' => $expensePaymentEntry->invoice_number,
            'transaction_amount' => $expensePaymentEntry->payment_amount,
            'other_reference_id' => '',
            'txn_from_id' => 0,
            'memo_desc' => 'Expense - Bill No. '.$data['vendor_bill_number'].'; Bill number '.$data['vendor_bill_number'].' paid on date '.$data['vendor_bill_date'].'['.$billDescription.']',
            'is_opening_balance' => 0,
            'is_reconciled' => 0,
            'is_cancelled' => 0,
            'created_by' => $this->input['user_id'] ?? 0,
            'added_on' => date('Y-m-d H:i:s'),
        ]);


        // insert second ledger entry
        $insertSecondLedger = new ChsoneLedgerTransaction();
        $insertSecondLedger->soc_id = $expensePaymentEntry->soc_id;
        $insertSecondLedger->transaction_date = $expensePaymentEntry->payment_date;
        $insertSecondLedger->ledger_account_id = $cr_ledger_account_id;
        $insertSecondLedger->ledger_account_name = $cr_ledger_account_name;
        $insertSecondLedger->voucher_type = 'expense';
        $insertSecondLedger->voucher_reference_number = $data['vendor_bill_number'];
        $insertSecondLedger->voucher_reference_id = $expensePaymentEntry->invoice_number;
        $insertSecondLedger->transaction_type = 'cr';
        $insertSecondLedger->payment_mode = $expensePaymentEntry->payment_mode;
        $insertSecondLedger->payment_reference = $expensePaymentEntry->invoice_number;
        $insertSecondLedger->transaction_amount = $expensePaymentEntry->payment_amount;
        $insertSecondLedger->other_reference_id = '';
        $insertSecondLedger->txn_from_id = $lastInsertedId;
        $insertSecondLedger->memo_desc = 'Expense - Bill No. '.$data['vendor_bill_number'].'; Bill number '.$data['vendor_bill_number'].' paid on date '.$data['vendor_bill_date']. '['.$billDescription.']';
        $insertSecondLedger->is_opening_balance = 0;
        $insertSecondLedger->is_reconciled = 0;
        $insertSecondLedger->is_cancelled = 0;
        $insertSecondLedger->created_by = $this->input['user_id'] ?? 0;
        $insertSecondLedger->added_on = date('Y-m-d H:i:s');

        $insertSecondLedger->save();

        if($lastInsertedId && $insertSecondLedger){
            return true;
        }else{
            return false;
        }
        }catch(\Exception $e){
            dd($e);
        }
    }
}
