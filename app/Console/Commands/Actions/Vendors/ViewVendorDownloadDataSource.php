<?php

namespace App\Console\Commands\Actions\Vendors;

use App\Console\Commands\Action;

class ViewVendorDownloadDataSource extends Action
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'datasource:DownloadVendor {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Get Vendor Advance List';

    protected $formatter = [
        "id" => "",
        "vendor_id" => "",
        "vendor_name" => "",
        "vendor_email" => "vendor_email",
        "vendor_contact_number" => "",
        "vendor_ledger_id" => "",
        "status" => "",
        "vendor_service_regn" => "",
        "vendor_pan_num" => "",
        "total_dues" => "",
        "total_advances" => "",
        "total_advances_consumed" => "getAmountPaid:total_advances",
        "add_principal_amount" => "",
    ];

    protected $formatterByKeys = ['id'];
    protected $mapper = [
        'id' => "vendor_master.vendor_id",
        'vendor_name' => 'vendor_master.vendor_name',
        'vendor_email' => 'vendor_master.vendor_email',
        'vendor_contact_number' => 'vendor_master.vendor_contact_number'
    ];
    // protected $hugeData = true;

    /**
     * Execute the console command.
     */
    public function apply()
    {


        $vendorDetails = [];


        $obj = $this->tenantDB()->table('chsone_vendors_master AS vendor_master')
            ->selectRaw(
                'vendor_master.vendor_id AS id,
                            vendor_master.vendor_id,
                            vendor_master.vendor_name,
                            vendor_master.vendor_email,
                            vendor_master.vendor_contact_number,
                            vendor_master.vendor_ledger_id,
                            vendor_master.status,
                            vendor_master.vendor_service_regn,
                            vendor_master.vendor_pan_num'
            );


        $vendorDetails = $obj->get()->toArray();

        $vendorDetails = json_decode(json_encode($vendorDetails), true);

        // fetch the total dues of each vendor. Create a function to get the total dues of each vendor and pass the vendor_id to the function then pass the result to the vendorDetails array
        foreach ($vendorDetails as $key => $value) {

            // fetch vendor dues
            $dues = $this->getVendorDues($value['vendor_id']);
            if(empty($dues)) {
                $vendorDetails[$key]['total_dues'] = 0.00;
            } else {
                $vendorDetails[$key]['total_dues'] = $dues;
            }

            // fetch vendor advance
            $advance = $this->getVendorAdvances($value['vendor_id']);
            if(empty($advance)) {
                $vendorDetails[$key]['total_advances'] = 0.00;
            } else {
                $vendorDetails[$key]['total_advances'] = $advance->total_adjustable_advance;
            }
        }


        // set key for add_principal_amount
        foreach ($vendorDetails as $key => $value) {
            $add_principal_amount = $this->tenantDB()->table('chsone_ledger_transactions')->where('ledger_account_id', $value['vendor_ledger_id'])->where('is_opening_balance', '1')->where('transaction_amount', '>', 0)->get();
            $add_principal_amount = json_decode($add_principal_amount, true);
            if (!empty($add_principal_amount)) {
                //add key add_principal_amount as 1 in my vendorDetails array
                $vendorDetails[$key]['add_principal_amount'] = "1";
            } else {
                //add key add_principal_amount as 0 in my vendorDetails array
                $vendorDetails[$key]['add_principal_amount'] = "0";
            }
        }


        $this->data = $vendorDetails;
    }

    function getVendorDues($vendorId)
    {
        $total_due = 0;
        $vendor_bill_amount = 0;
        $total_paid_amount = 0;
        $obj = $this->tenantDB()->table('chsone_vendor_bill_master AS bill_master')
            ->select('bill_master.vendor_bill_id','bill_master.vendor_bill_amount')
            ->where('bill_master.vendor_id', $vendorId)
            ->whereIN('payment_status', ['unpaid', 'partialpaid'])
            ->where('bill_master.status', '1');
        $result = $obj->get();

        // create a foreach loop to get paid amount
        foreach ($result as $key => $value) {
            $vendor_bill_amount += $value->vendor_bill_amount;
            // get paid amount from expense tracker pass the vendor_bill_id to the function
            $paidAmount = $this->GetPaidAmount($vendorId, $value->vendor_bill_id);
            $total_paid_amount += $paidAmount->paid_amount;

            $total_due = $vendor_bill_amount - $total_paid_amount;
        }

        return $total_due;
    }

    function GetPaidAmount($vendorId, $vendorBillId)
    {
        $obj = $this->tenantDB()->table('chsone_vendor_bill_payment_details AS payments')
            ->selectRaw('SUM(IFNULL(vendor_bill_payment_amount, 0)) AS paid_amount')
            ->leftjoin('expense_invoice_payment_tracker AS expense', 'payments.expense_tracker_id', '=', 'expense.id')
            ->where('payments.vendor_id', $vendorId)
            ->where('payments.vendor_bill_id', $vendorBillId)
            ->where('expense.status', 'Y');
        $result = $obj->first();
        return $result;
    }

    function getVendorAdvances($vendorId)
    {
        $obj = $this->tenantDB()->table('chsone_credit_accounts AS cr_acc')
            ->selectRaw('SUM(IF(cr_acc.transaction_type = "dr" AND cr_acc.use_credit = "adjustable", cr_acc.amount, 0)) - SUM(IF(cr_acc.transaction_type = "cr" AND cr_acc.use_credit = "adjustable", cr_acc.amount, 0)) AS total_adjustable_advance')
            // ->selectRaw('SUM(IF(cr_acc.transaction_type = "dr", cr_acc.amount, 0)) - SUM(IF(cr_acc.transaction_type = "cr", cr_acc.amount, 0)) AS total_balance')
            ->join('chsone_vendors_master AS vendormaster', 'cr_acc.account_id', '=', 'vendormaster.vendor_id')
            ->where('cr_acc.account_context', '=', 'vendor')
            ->groupBy(
                'cr_acc.account_id'
            )
            ->where('vendormaster.vendor_id', $vendorId);

        $obj = $this->filter($obj);
        $result = $obj->first();
        return $result;
    }

    public function concat($a, $b)
    {
        return $a . ' ' . $b;
    }

    public function getAmountPaid($payment_amount)
    {

        $payment_amount = (float) $payment_amount;

        return '₹ ' . number_format($payment_amount, 2);
    }

}
