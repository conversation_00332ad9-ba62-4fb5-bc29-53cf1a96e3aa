<?php

namespace App\Console\Commands\Actions\Vendors;

use Illuminate\Console\Command;
use App\Console\Commands\Action;
use App\Models\Tenants\ChsoneGrpLedgerTree;

class updateVendorInGrpLedgerDataSource extends Action
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'datasource:updateVendorInGrpLedger {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Update Vendor In Grp Ledger';

    /**
     * Execute the console command.
     */
    public function apply()
    {
        $data = $this->input;

        // first need to find the existing vendor ledger
        $vendorLedger = ChsoneGrpLedgerTree::where('ledger_account_id', $data['ledger_account_id'])->first();

        if(!$vendorLedger) {
            $this->message = "Vendor Grp Ledger not found";
            $this->status = 'error';
            $this->statusCode = 400;
        }

        // update the vendor ledger name
        $vendorLedger->update([
            'ledger_account_name'=>$data['vendor_name'].'-'.$data['vendor_id'] ?? '',
        ]);

        $this->message = "Vendor Grp Ledger updated successfully";
        $this->status = 'success';
        $this->statusCode = 200;
        $this->data = $vendorLedger->toArray();
    }
}
