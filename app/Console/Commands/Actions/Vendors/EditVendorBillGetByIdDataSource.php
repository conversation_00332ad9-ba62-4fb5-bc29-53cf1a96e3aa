<?php

namespace App\Console\Commands\Actions\Vendors;

use App\Console\Commands\Action;

class EditVendorBillGetByIdDataSource extends Action
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'datasource:editVendorBillGetById {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Update Vendor Ledger';

    /**
     * Execute the console command.
     */
    public function apply()
    {
        $data    = $this->input;
        $bill_id = $data['id'];

        // Retrieve vendor bill master details.
        $billMaster = $this->tenantDB()->table('chsone_vendor_bill_master AS bill_master')
            ->selectRaw(
                'bill_master.vendor_bill_id AS id,
                 bill_master.vendor_id AS vendor_id,
                 CAST(vendor_bill_num AS SIGNED) AS bill_number,
                 vendor_bill_date AS bill_date,
                 vendor_bill_type_purchase AS purchase_mode,
                 vendor_bill_amount AS bill_amount,
                 vendor_bill_tds AS bill_amount_tds,
                 vendor_bill_due_date AS due_date,
                 is_billable,
                 details ,
                 payment_status,
                 IFNULL(vendor_bill_roundoff, 0.0) AS vendor_bill_roundoff,
                 IFNULL(vendor_bill_writeoff, 0.0) AS vendor_bill_writeoff,
                 IFNULL(advance_consumed, 0.0) AS advance,
                 is_rcm,
                 is_billed,
                 payment_status,
                 advance_consumed,
                 vendor_bill_soft_copy_path,
                 vendor_bill_due_date,
                 IFNULL(cvm.vendor_gst,0) AS vendor_rcm,
                 IFNULL(cvm.vendor_service_regn,0) AS vendor_gstin,
                 IFNULL(tr.rate, 0) AS vendor_bill_tds_rate,
                 cvm.vendor_credit_period as credit_period',
                 
            )
            ->leftJoin('chsone_vendors_master as cvm', 'bill_master.vendor_id', '=', 'cvm.vendor_id')
            ->leftJoin('tds_rates_master AS tr', 'cvm.vendor_tds_id', '=', 'tr.id')
            ->where('bill_master.vendor_bill_id', $bill_id)
            ->orderByDesc('vendor_bill_date')
            ->get();

        // Retrieve vendor bill particulars details.
        $billParticulars = $this->tenantDB()->table('chsone_vendor_bill_particulars AS bill_master')
            ->select(
                'bill_master.particulars_id',
                'bill_master.et_id',
                'bill_master.bill_id',
                'bill_master.soc_id',
                'bill_master.amount',
                'bill_master.particular',
                'bill_master.hsn_sac',
                'bill_master.tax_cgst as particular_cgst',
                'bill_master.tax_cgst_amount',
                'bill_master.tax_sgst as particular_sgst',
                'bill_master.tax_sgst_amount',
                'bill_master.tax_igst as particular_igst',
                'bill_master.tax_igst_amount',
                'bill_master.tax_ugst as particular_gst',
                'bill_master.tax_ugst_amount',
                'bill_master.total_tax',
                'bill_master.total_amount',
                'bill_master.created_by',
                'bill_master.created_date',
                'bill_master.updated_by',
                'bill_master.updated_date',
                'gst_rates_master.name as ledger_account_name',
                'chsone_expense_tracker.et_type_name'
            )
            ->leftJoin('gst_rates_master', 'bill_master.tax_ugst', '=', 'gst_rates_master.id')
            ->leftJoin('chsone_expense_tracker', 'bill_master.et_id', '=', 'chsone_expense_tracker.et_id')
            ->where('bill_master.bill_id', $bill_id)
            ->orderByDesc('bill_master.particulars_id')
            ->get();


        // Attach particulars to the master record.
        // Assuming that the master query returns only one record,
        // we attach the particulars to the first record.
        if ($billMaster->isNotEmpty()) {
            // $billMaster[0]->particulars = [$billParticulars];
            $billMaster[0]->particulars = $billParticulars; // Remove the square brackets. as per discussion
            $this->data = $billMaster[0];
        } else {
            $this->data = null;
        }
        //dd($this->data);
    }
}
