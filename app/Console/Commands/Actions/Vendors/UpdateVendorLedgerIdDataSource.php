<?php

namespace App\Console\Commands\Actions\Vendors;

use Illuminate\Console\Command;
use App\Console\Commands\Action;

class UpdateVendorLedgerIdDataSource extends Action
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'datasource:UpdateVendorLedgerId {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Update Vendor Ledger Id Data Source';

    /**
     * Execute the console command.
     */
    public function apply()
    {
        $data = $this->input;

        // create a update query to update the vendor_ledger_id where vendor_id = $data['vendor_id'] in chsone_vendors_master table
        $obj = $this->tenantDB()->table('chsone_vendors_master')
        ->where('vendor_id', $data['vendor_id'])
        ->update([
            'vendor_ledger_id' => $data['vendor_ledger_id'],
        ]);

        // then fetch the vendor details from chsone_vendors_master table whose vendor_id = $data['vendor_id']
        $vendor = $this->tenantDB()->table('chsone_vendors_master')
        ->select('vendor_id', 'vendor_name', 'vendor_contact_number', 'vendor_phone_number', 'vendor_email', 'vendor_notes', 'vendor_expense_group_id', 'vendor_is_company', 'vendor_credit_period', 'vendor_service_regn', 'vendor_pan_num', 'vendor_section_code', 'vendor_payee_name', 'vendor_is_company', 'vendor_address', 'vendor_ledger_id', 'status', 'place_of_supply', 'vendor_tds_id')
        ->where('vendor_id', $data['vendor_id'])->first();
        // return the vendor details
        $this->data = $vendor;
    }
}
