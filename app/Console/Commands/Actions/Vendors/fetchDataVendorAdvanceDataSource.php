<?php

namespace App\Console\Commands\Actions\Vendors;

use Illuminate\Console\Command;
use App\Console\Commands\Action;

class fetchDataVendorAdvanceDataSource extends Action
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'datasource:fetchDataVendorAdvance {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Fetch Data for Vendor Advance Workflow';

    /**
     * Execute the console command.
     */
    public function apply()
    {
        $vendor_id = $this->input['id'];

        // fetch vendor advance
        $advance = $this->getVendorAdvances($vendor_id);
        if(empty($advance)) {
            $vendorDetails['total_advances'] = 0.00;
        } else {
            $vendorDetails['total_advances'] = $advance->total_adjustable_advance;
        }

        $this->data = $vendorDetails;
    }

    function getVendorAdvances($vendorId)
    {
        $obj = $this->tenantDB()->table('chsone_credit_accounts AS cr_acc')
            ->selectRaw('SUM(IF(cr_acc.transaction_type = "dr" AND cr_acc.use_credit = "adjustable", cr_acc.amount, 0)) - SUM(IF(cr_acc.transaction_type = "cr" AND cr_acc.use_credit = "adjustable", cr_acc.amount, 0)) AS total_adjustable_advance')
            // ->selectRaw('SUM(IF(cr_acc.transaction_type = "dr", cr_acc.amount, 0)) - SUM(IF(cr_acc.transaction_type = "cr", cr_acc.amount, 0)) AS total_balance')
            ->join('chsone_vendors_master AS vendormaster', 'cr_acc.account_id', '=', 'vendormaster.vendor_id')
            ->where('cr_acc.account_context', '=', 'vendor')
            ->groupBy(
                'cr_acc.account_id'
            )
            ->where('vendormaster.vendor_id', $vendorId);

        $obj = $this->filter($obj);
        $result = $obj->first();
        return $result;
    }
}
