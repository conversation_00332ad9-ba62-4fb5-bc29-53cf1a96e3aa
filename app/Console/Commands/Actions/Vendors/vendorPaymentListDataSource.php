<?php

namespace App\Console\Commands\Actions\Vendors;

use App\Console\Commands\Action;

class vendorPaymentListDataSource extends Action
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'datasource:vendorPaymentList {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Vendor Payment List';

    /**
     * Execute the console command.
     */
    protected $formatter = [
        "id" => "",
        "soc_id" => "getNa:soc_id",
        "vendor_id" => "getNa:vendor_id",
        "vendor_name" => "getNa:vendor_name",
        "invoice_number" => "getNa:invoice_number",
        "payment_number" => "getNa:payment_number",
        "payment_mode" => "getNa:payment_mode",
        "transaction_reference" => "getNa:transaction_reference",
        "writeoff_amount" => "",
        "status" => "getNa:status",
        "transaction_status" => "getNa:transaction_status",
        "payment_date" => "getNa:payment_date",
        "payment_amount" => "getAmountPaid:payment_amount,writeoff_amount",
    ];


    protected $formatterByKeys = ['id'];

    protected $mapper = [
        'id' => 'id'
    ];

    public function apply()
    {

        $vendorId = $this->input['id'];
        $paymentNumber = '';
        $voucherNumber = '';
        $paymentDate = '';
        $paymentReference = '';
        $vendorStatus = '';
        $paymentMode = '';
        $sortBy = '';
        $sortByKey = '';
        $sortByValue = '';


        if (!empty($this->input['filters'])) {
            $paymentNumber =
                !empty($this->input['filters']['payment_number']) ? $this->input['filters']['payment_number'] : '';
            unset($this->input['filters']['payment_number']);

            $voucherNumber =
                !empty($this->input['filters']['invoice_number']) ? $this->input['filters']['invoice_number'] : '';
            unset($this->input['filters']['invoice_number']);

            $paymentDate =
                !empty($this->input['filters']['payment_date']) ? $this->input['filters']['payment_date'] : '';
            unset($this->input['filters']['payment_date']);

            $paymentReference =
                !empty($this->input['filters']['transaction_reference']) ? $this->input['filters']['transaction_reference'] : '';
            unset($this->input['filters']['transaction_reference']);


            $vendorStatus = isset($this->input['filters']['status']) ? explode(',', $this->input['filters']['status']) : [];

            $paymentMode = isset($this->input['filters']['payment_mode']) ? explode(',', $this->input['filters']['payment_mode']) : [];
        }

        // add pagination
        $page = $this->input['page'] ?? 1;
        $per_page = !empty($this->input['per_page']) ? $this->input['per_page']: ($this->hugeData ? 100000 : 10);
        $offset = ($page - 1) * $per_page;

        $obj = $this->tenantDB()->table('expense_invoice_payment_tracker as payments')
            ->selectRaw(
                'id,
                payments.soc_id,
                payments.vendor_id,
                vendor_name,
                invoice_number,
                payment_number,
                transaction_reference,
                writeoff_amount,
                payment_amount,
                payments.status,
                transaction_status,
                payment_date'
            )
            ->selectRaw('CONCAT(UCASE(LEFT(payment_mode, 1)), LOWER(SUBSTRING(payment_mode, 2))) as payment_mode')
            ->leftJoin('chsone_vendors_master as vendors', 'vendors.vendor_id', '=', 'payments.vendor_id')
            ->where('payments.vendor_id', $vendorId)
            ->orderByDesc('id');


        if (!empty($this->input['sort'])) {
            $sortBy = $this->input['sort'];
            $sortByKey = array_key_first($sortBy);
            $sortByValue = $sortBy[$sortByKey];
            unset($this->input['sort']);
        }

        if ($sortBy) {
            $obj = $obj->orderBy($sortByKey, $sortByValue);
        }

        if ($paymentNumber) {

            $obj = $obj->where('payment_number', 'like', '%' . $paymentNumber . '%');
        }

        if ($voucherNumber) {
            $obj = $obj->where('invoice_number', 'like', '%' . $voucherNumber . '%');
        }

        if ($paymentDate) {
            $obj = $obj->where('payment_date', 'like', '%' . $paymentDate . '%');
        }

        if ($paymentReference) {
            $obj = $obj->where('transaction_reference', 'like', '%' . $paymentReference . '%');
        }

        if ($paymentMode) {
            $obj = $obj->whereIn('payment_mode', $paymentMode);
        }

        if (!empty($vendorStatus)) {
            $obj = $obj->whereIn("payments.status", $vendorStatus);
        } else {
            $obj = $obj->whereIn('payments.status', ['Y', 'P', 'R', 'Paid']);
        }

        // add count and pagination
        $count = $obj->count();

        $obj = $obj->offset($offset);
        $obj = $obj->limit($per_page);

        $vendorPaymentsList = $obj->get();

        $this->data =$this->format($vendorPaymentsList->toArray());
        $this->meta['pagination']['total']=$count;


    }
    public function getAmountPaid($payment_amount, $writeoff_amount)
    {
        $payment_amount = (float) $payment_amount;
        $writeoff_amount = (float) $writeoff_amount;

        // return '₹ ' . number_format($payment_amount, 2) . '(' . number_format($writeoff_amount, 2) . ')';
        // return '₹ ' . number_format($payment_amount, 2);
        return $payment_amount;
    }

    public function getNa($value)
    {

        if($value===null || $value===''){
            return "N/A";
        }else{
            return $value;
        }
    }

}
