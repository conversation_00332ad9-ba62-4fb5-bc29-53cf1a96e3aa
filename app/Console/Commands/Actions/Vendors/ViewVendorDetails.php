<?php

namespace App\Console\Commands\Actions\Vendors;

use App\Models\Tenants\ChsoneVendorsMaster;
use Illuminate\Console\Command;
use App\Console\Commands\Action;

class ViewVendorDetails extends Action
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'datasource:viewVendorDetails {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Get Vendor Details';

    protected $formatter =  [
        "id" => "",
        "vendor_id" => "",
        "vendor_name" => "",
        "vendor_number" => "",
        "vendor_contact_number" => "",
        "vendor_phone_number" => "",
        "vendor_email" => "",
        "vendor_notes" => "",
        "vendor_credit_period" => "",
        "vendor_service_regn" => "",
        "vendor_pan_num" => "",
        "vendor_section_code" => "",
        "vendor_payee_name" => "",
        "vendor_is_company" => "",
        "vendor_address" => "",
        "status" => ""
    ];
    /**
     * Execute the console command.
     */
    public function apply()
    {
        $vendorId = $this->input['vendorid'];
        $obj = $this->tenantDB()->table('chsone_vendors_master AS vendor_master')
                    ->select(
                        "vendor_id AS id",
                        "vendor_id",
                        "vendor_name",
                        "vendor_number",
                        "vendor_contact_number",
                        "vendor_phone_number",
                        "vendor_email",
                        "vendor_notes",
                        "vendor_credit_period",
                        "vendor_service_regn",
                        "vendor_pan_num",
                        "vendor_section_code",
                        "vendor_payee_name",
                        "vendor_is_company",
                        "vendor_address",
                        "status"
                    )
                    ->where('vendor_master.vendor_id', $vendorId);
        $result = $obj->get();
        $this->data = $this->format($result->toArray());
    }

    public function concat($a, $b)
    {
        return $a . ' ' . $b;
    }
}
