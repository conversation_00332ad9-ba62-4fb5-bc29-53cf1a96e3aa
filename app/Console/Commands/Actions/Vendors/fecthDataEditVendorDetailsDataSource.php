<?php

namespace App\Console\Commands\Actions\Vendors;

use Illuminate\Console\Command;
use App\Console\Commands\Action;
use App\Models\Tenants\ChsoneVendorsMaster;
use Illuminate\Support\Facades\DB;

class fecthDataEditVendorDetailsDataSource extends Action
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'datasource:fecthDataEditVendorDetails {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Fetch Data Edit Vendor Details DataSource';

    /**
     * Execute the console command.
     */
    public function apply()
    {
        $vendorId = $this->input['vendor_id'];

        // fetch the vendor details according to vendor id
        $vendor = ChsoneVendorsMaster::where('vendor_id', $vendorId)
            ->where('soc_id', $this->input['company_id'])
            ->first();

        if (!$vendor) {
            $this->message = "Vendor id not exists";
            $this->status = 'error';
            $this->statusCode = 400;
        } else {
            $obj = $this->tenantDB()->table('chsone_vendors_master as vendors')
                ->select(
                    'vendors.vendor_id as id',
                    'vendors.vendor_name',
                    'vendors.vendor_contact_number',
                    'vendors.vendor_phone_number',
                    'vendors.vendor_email',
                    'vendors.vendor_notes',
                    'vendors.vendor_is_company',
                    'vendors.vendor_credit_period',
                    'vendors.vendor_service_regn',
                    'vendors.vendor_pan_num',
                    'vendors.vendor_payee_name',
                    'vendors.vendor_address',
                    'vendors.vendor_gst as vendor_rcm',
                    DB::raw('CAST(vendors.status AS int) as status'),
                    'vendors.vendor_tds_id as tds',
                    DB::raw('COALESCE(tds_rates_master.section, "") as section'), // Return empty string if null
                    DB::raw('COALESCE(tds_rates_master.rate, "") as rate'), // Return empty string if null
                    //DB::raw('COALESCE(CONCAT(tds_rates_master.section, " - ", tds_rates_master.rate, "%"), "") as tds'), // Return empty string if null
                    'vendors.place_of_supply'
                )
                ->leftJoin('tds_rates_master', 'vendors.vendor_tds_id', '=', 'tds_rates_master.id')
                ->where('vendors.vendor_id', $vendorId)
                ->first();

                // here if $obj->vendor_service_regn is not null then set variablr is_gst_applicable to 1 and if null then set to 0 and pass varibale to $obj
                if($obj->vendor_service_regn != null){
                    $obj->isgstapplicable = 1;
                }else{
                    $obj->isgstapplicable = 0;
                }

            $this->data = $obj;
        }
    }
}
