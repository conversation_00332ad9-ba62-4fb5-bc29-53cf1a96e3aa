<?php

namespace App\Console\Commands\Actions\Vendors;

use Illuminate\Console\Command;
use App\Console\Commands\Action;
use Carbon\Carbon;

class VendorBillListAction extends Action
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'datasource:vendorBillList {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Vendor Bills';

    protected $formatter = [
        "id" => "",
        "vendor_id" => "",
        "vendor_name" => "",
        "bill_number" => "",
        "bill_date" => "",
        "purchase_mode" => "",
        "bill_amount" => "",
        "bill_amount_tds" => "",
        "due_date" => "",
        "payment_status" => "",
        "vendor_bill_payment_amount" => "",
        "amount_paid" => "setRupees:vendor_bill_payment_amount",
        "tds" => "",
        "write_off" => "",
        "is_rcm" => "",
        "due_amount" => "getDueAmount:bill_amount,vendor_bill_payment_amount,advance",
        "total_bill_amount" => "getTotalBillAmount:bill_amount,bill_amount_tds",
    ];

    protected $formatterByKeys = ['id'];

    protected $mapper = [
        'id' => 'vendor_bill_id'
    ];
    protected $hugeData = true;

    /**@SWG\Get(
     *     path="/datasource:vendorBillList",
     *     tags={"Vendors"},
     *     summary="Vendor Bills",
     *     operationId="VendorBillListAction",
     *     @SWG\Parameter(
     *         name="flowId",
     *         in="path",
     *         description="Flow ID",
     *         required=true,
     *         type="string"
     *     ),
     *     @SWG\Parameter(
     *         name="parentId",
     *         in="path",
     *         description="Parent ID",
     *         required=true,
     *         type="string"
     *     ),
     *     @SWG\Parameter(
     *         name="input",
     *         in="path",
     *         description="Input",
     *         required=true,
     *         type="string"
     *     ),
     *     @SWG\Response(
     *         response=200,
     *         description="Vendor Bills"
     *     ),
     *     @SWG\Response(
     *         response=400,
     *         description="Invalid input"
     *     )
     * )
     */
    public function apply()
    {
        $vendorId = $this->input['id'];
        $vendorBillNo = '';
        $paymentStatus = '';
        $purchaseMode = '';
        $isRcm = '';
        $sortBy = '';
        $sortByKey = '';
        $sortByValue = '';
        $vendorBillDueDate = '';
        $vendorName = '';

        if (!empty($this->input['filters'])) {

            $vendorBillNo =
                !empty($this->input['filters']['bill_number']) ? $this->input['filters']['bill_number'] : '';
            unset($this->input['filters']['bill_number']);

            $vendorName =
            !empty($this->input['filters']['vendor_name']) ? $this->input['filters']['vendor_name'] : '';
        unset($this->input['filters']['vendor_name']);

            $isRcm = isset($this->input['filters']['is_rcm']) ? explode(',', $this->input['filters']['is_rcm']) : [];

            $paymentStatus = isset($this->input['filters']['payment_status']) ? explode(',', $this->input['filters']['payment_status']) : [];

            $purchaseMode = isset($this->input['filters']['purchase_mode']) ? explode(',', $this->input['filters']['purchase_mode']) : [];

            $vendorBillDueDate = isset($this->input['filters']['vendor_bill_due_date']) ? explode(',', $this->input['filters']['vendor_bill_due_date']) : [];

        }

        // add pagination
        $page = $this->input['page'] ?? 1;
        $per_page = !empty($this->input['per_page']) ? $this->input['per_page']: ($this->hugeData ? 100000 : 10);
        $offset = ($page - 1) * $per_page;

        $obj = $this->tenantDB()->table('chsone_vendor_bill_master AS bill_master')
            ->selectRaw(
                'bill_master.vendor_bill_id AS id,
                                    bill_master.vendor_id AS vendor_id,
                                    vendor_name,
                                    vendor_bill_num AS bill_number,
                                    vendor_bill_date AS bill_date,
                                    vendor_bill_type_purchase AS purchase_mode,
                                    vendor_bill_amount AS bill_amount,
                                    vendor_bill_tds AS bill_amount_tds,
                                    vendor_bill_due_date AS due_date,
                                    payment_status,
                                    is_rcm',
            )
            // ->leftjoin('chsone_vendor_bill_payment_details AS bill_payment', 'bill_payment.vendor_bill_id', '=', 'bill_master.vendor_bill_id')
            ->leftjoin('chsone_vendors_master AS vendors_master', 'vendors_master.vendor_id', '=', 'bill_master.vendor_id')
            ->where('bill_master.vendor_id', $vendorId)
            ->where('bill_master.status', '1')
            ->orderByDesc('vendor_bill_date')
            ->selectRaw(
                "(SELECT IF(SUM(cvbpd.vendor_bill_payment_amount) = 0, 0, SUM(cvbpd.vendor_bill_payment_amount))
                  FROM chsone_vendor_bill_payment_details AS cvbpd
                  LEFT JOIN expense_invoice_payment_tracker AS eipt ON cvbpd.expense_tracker_id = eipt.id
                  WHERE cvbpd.vendor_bill_id = bill_master.vendor_bill_id
                  AND eipt.status != 'reversed') AS vendor_bill_payment_amount"
            )
            ->selectRaw(
                "(SELECT IF(SUM(cvbpd.tds) = 0, 0, SUM(cvbpd.tds))
                  FROM chsone_vendor_bill_payment_details AS cvbpd
                  LEFT JOIN expense_invoice_payment_tracker AS eipt ON cvbpd.expense_tracker_id = eipt.id
                  WHERE cvbpd.vendor_bill_id = bill_master.vendor_bill_id
                  AND eipt.status != 'reversed') AS tds"
            )
            ->selectRaw(
                "(SELECT COALESCE(IF(SUM(cvbpd.write_off) = 0, 0, SUM(cvbpd.write_off)),0)
                  FROM chsone_vendor_bill_payment_details AS cvbpd
                  LEFT JOIN expense_invoice_payment_tracker AS eipt ON cvbpd.expense_tracker_id = eipt.id
                  WHERE cvbpd.vendor_bill_id = bill_master.vendor_bill_id
                  AND eipt.status != 'reversed') AS write_off"
            );

            if ($vendorName) {
                $obj = $obj->where('vendor_name', 'like', '%' . $vendorName . '%');

            }

        if (isset($this->input['sort']) && is_array($this->input['sort'])) {
            $sortBy = $this->input['sort'];
            $sortByKey = array_key_first($sortBy);
            $sortByValue = $sortBy[$sortByKey];
            unset($this->input['sort']);
        }


        if ($sortBy) {
            $obj = $obj->orderBy($sortByKey, $sortByValue);
        }

        if ($vendorBillNo) {
            $obj = $obj->where('bill_master.vendor_bill_num', 'like', '%' . $vendorBillNo . '%');
        }

        if ($isRcm) {
            $obj = $obj->where('bill_master.is_rcm', $isRcm);
        }

        if ($paymentStatus) {
            $obj->whereIn('bill_master.payment_status', $paymentStatus);
        }

        if ($purchaseMode) {
            $obj->whereIn('bill_master.vendor_bill_type_purchase', $purchaseMode);
        }


        if ($vendorBillDueDate) {
            $obj = $obj->where(function($query) use ($vendorBillDueDate) {
                foreach ($vendorBillDueDate as $status) {
                    if ($status === 'overdue') {
                        $query->orWhere(function($subQuery) {
                            $subQuery->where('vendor_bill_due_date', '<', Carbon::now())
                                     ->where(function($subQuery) {
                                         $subQuery->where('payment_status', 'unpaid')
                                                  ->orWhere('payment_status', 'partialpaid');
                                     });
                        });
                    }
                    if ($status === 'due_in_one_month') {
                        $query->orWhere(function($subQuery) {
                            $subQuery->whereRaw("vendor_bill_due_date BETWEEN CURDATE() AND DATE_ADD(CURDATE(), INTERVAL 1 MONTH)")
                                     ->where(function($subQuery) {
                                         $subQuery->where('payment_status', 'unpaid')
                                                  ->orWhere('payment_status', 'partialpaid');
                                     });
                        });
                    }
                    if ($status === 'due_in_one_week') {
                        $query->orWhere(function($subQuery) {
                            $subQuery->whereRaw("vendor_bill_due_date BETWEEN CURDATE() AND DATE_ADD(CURDATE(), INTERVAL 1 WEEK)")
                                     ->where(function($subQuery) {
                                         $subQuery->where('payment_status', 'unpaid')
                                                  ->orWhere('payment_status', 'partialpaid');
                                     });
                        });
                    }

                }
            });
        }

        // add pagination to the query
        $count = $obj->count();

        $obj = $obj->offset($offset);
        $obj = $obj->limit($per_page);

        $result = $obj->get();
        $this->data = $this->format($result->toArray());
        $this->meta['pagination']['total']=$count;
    }

    public function getTotalBillAmount($bill_amount, $bill_amount_tds)
    {
        $bill_amount = (float) $bill_amount;
        $bill_amount_tds = (float) $bill_amount_tds;


        $total_bill_amount = $bill_amount + $bill_amount_tds;
        // return '₹ ' . number_format($total_bill_amount, 2) . '(' . number_format($bill_amount_tds, 2) . ')';
        // return '₹ ' . number_format($total_bill_amount, 2);
        return $total_bill_amount;
    }

    public function getDueAmount($bill_amount, $amount_paid, $advance)
    {
        $bill_amount = (float) $bill_amount;
        $amount_paid = (float) $amount_paid;
        $advance = (float) $advance;

        $amount = $bill_amount - $amount_paid - $advance;
        // return '₹ ' . number_format($amount, 2);
        return $amount;
    }

    public function getAmountPaid($amount_paid, $write_off)
    {
        $amount_paid = (float) $amount_paid;
        $write_off = (float) $write_off;

        // return '₹ ' . number_format($amount_paid, 2) . '(' . number_format($write_off, 2) . ')';
        return $amount_paid. '(' . $write_off . ')';
    }

    public function setRupees($value)
    {
        $value = (float) $value;
        // return '₹ ' . number_format($value, 2);
        return $value;
    }

}
