<?php

namespace App\Console\Commands\Actions\Vendors;

use App\Models\Tenants\ChsoneAccountsMaster;
use App\Models\Tenants\SocAccountFinancialYearMaster;
use Illuminate\Console\Command;
use App\Console\Commands\Action;

class VendorAgingAction extends Action
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'datasource:vendorAgingReport {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Get Vendor Aging Report';

    // protected $hugeData = true;

    protected $formatterByKeys = ['id'];

    protected $mapper = [
        "id" => "vendor_master.vendor_id"
    ];

    public function apply()
    {
        $days = $this->input['filters']['days'] ?? 30;
        $interval = $this->input['filters']['interval'] ?? 3;
        $societyId = $this->input['company_id'];

        // Pagination setup
        $page = $this->input['page'] ?? 1;
        $per_page = !empty($this->input['per_page']) ? $this->input['per_page'] : ($this->hugeData ? 100000 : 10);
        $offset = ($page - 1) * $per_page;

        // Fetch all vendor details for total calculation (without pagination)
        $allVendorDetails = $this->getAllVendorDetails($societyId);
        $intervalCols = $this->generateIntervalCols($days, $interval);

        // Calculate total amounts for all data
        $total = [];
        foreach ($allVendorDetails as $vendor) {
            $intervalColsIndex = 0;
            foreach ($intervalCols as $key => $cols) {
                $breakIntervalKey = explode('to', $key);
                $unpaidBills = $this->getVendorUnpaidBills($vendor->vendor_id, $breakIntervalKey[1], $breakIntervalKey[0]);

                if ($intervalColsIndex == (count($intervalCols) - 1)) {
                    $unpaidBills = $this->getVendorUnpaidBills($vendor->vendor_id, '0000-00-00', $breakIntervalKey[0]);
                }

                // Calculate the bill amount
                $billAmt = $this->calculateBillAmount($unpaidBills, $vendor);

                // Store the bill amount in the total array
                $key = 'col' . $intervalColsIndex;
                $total[$key] = isset($total[$key]) ? $total[$key] + $billAmt : $billAmt;

                $intervalColsIndex++;
            }
        }

        // Prepare formatted total values for output
        $totalFormatted = [];
        foreach ($total as $key => $value) {
            // $totalFormatted[$key] = "₹ " . number_format($value, 2); // Format for display
            $totalFormatted[$key] = $value;
        }

        // Prepare final total array with id as 'Total'
        $totalArray = array_merge(['id' => 'Total'], $totalFormatted);
        $totalArray = array_merge(['vendor_name' => 'Total'], $totalArray);

        // Fetch paginated data
        $vendorDetails = $this->getVendorDetails($societyId, $offset, $per_page);
        $billData = [];

        foreach ($vendorDetails as $vendor) {
            $billItem = [
                'id' => $vendor->vendor_id,
                'vendor_id' => $vendor->vendor_id,
                'vendor_name' => $vendor->vendor_name,
                'totalAmount' => 0,
            ];

            $intervalColsIndex = 0;

            foreach ($intervalCols as $key => $cols) {
                $breakIntervalKey = explode('to', $key);
                $unpaidBills = $this->getVendorUnpaidBills($vendor->vendor_id, $breakIntervalKey[1], $breakIntervalKey[0]);

                if ($intervalColsIndex == (count($intervalCols) - 1)) {
                    $unpaidBills = $this->getVendorUnpaidBills($vendor->vendor_id, '0000-00-00', $breakIntervalKey[0]);
                }

                // Calculate the bill amount
                $billAmt = $this->calculateBillAmount($unpaidBills, $vendor);
                $billIds = $this->getBillIds($unpaidBills);

                // Store bill ID and dates
                $billItem['bill_id' . $intervalColsIndex] = implode(',', $billIds);

                // Store the bill amount as numeric for calculation
                $billItem['col' . $intervalColsIndex] = empty($unpaidBills) ? 0.00 : $billAmt;
                $billItem['colFrom' . $intervalColsIndex] = $breakIntervalKey[1];
                $billItem['colTo' . $intervalColsIndex] = $breakIntervalKey[0];

                // Adjust the column From/To for the last interval
                if ($intervalColsIndex == (count($intervalCols) - 1)) {
                    $billItem['colFrom' . $intervalColsIndex] = "0000-00-00";
                    $billItem['colTo' . $intervalColsIndex] = $breakIntervalKey[0];
                }

                // Total amount calculation as numeric
                $billItem['totalAmount'] += $billItem['col' . $intervalColsIndex];

                $intervalColsIndex++;
            }

            $billData[] = $billItem;
        }

        // Format billData values for display
        // foreach ($billData as &$bill) {
        //     foreach ($bill as $key => &$value) {
        //         if (strpos($key, 'col') !== false && strlen($key) <= 4) {
        //             $value = "₹ " . number_format($value, 2);
        //         }
        //     }
        // }

        // Apply pagination
        $totalItems = $this->getVendorCounts($societyId);
        $lastPage = ceil($totalItems / $per_page);

        // If this is the last page, merge the total array with the bill data
        if ($page == $lastPage) {
            $this->data = array_merge($billData, [$totalArray]);
        } else {
            $this->data = $billData;
        }

        $this->meta['pagination']['total'] = $totalItems;

    }
        // $billData = array_slice($billData, $offset, $per_page);
        // $this->data = array_merge($billData, [$totalArray]);

    public function getVendorUnpaidBills($vendorId, $billFromDate, $billToDate)
    {
        return $this->tenantDB()->table("chsone_vendor_bill_master AS bill_master")
                    ->whereBetween('bill_master.vendor_bill_date', [$billFromDate, $billToDate])
                    ->where('bill_master.vendor_id', $vendorId)
                    ->where('bill_master.status', '0')
                    ->get()->toArray();
    }

    public function getVendorPaidBills($vendorId, $vendorBillId)
    {
        return $this->tenantDB()->table("chsone_vendor_bill_payment_details AS bill_details")
                    ->where('bill_details.vendor_id', $vendorId)
                    ->where('bill_details.vendor_bill_id', $vendorBillId)
                    ->get()->toArray();
    }

    /**
     * Generate the Columns for the table sructure as per Days and Intervals
    **/
    public function generateIntervalCols($days, $interval)
    {
        $intervalCols = [];
        $currentDate = date("Y-m-d");
        $fromDays = 0;
        $today = $days;
        for ($inter = 1; $inter <= $interval; $inter++) {
            $yesterday = new \DateTime($currentDate . ' - 1 day');
            $startDay = $yesterday->format('Y-m-d');

            $dayAgo = new \DateTime($currentDate . ' - ' . $days . ' day');
            $endDay = $dayAgo->format('Y-m-d');

            $intervalCols[$startDay . 'to' . $endDay] = $fromDays . " to " . $today . " days";

            $currentDate = $endDay;
            $fromDays = $today + 1;
            $today = $today + $days;
        }
        return $intervalCols;
    }

    /**
     * Get Basic Vendor Details
    **/
    public function getVendorDetails($societyId, $offset = 0, $per_page = 10)
    {
        return $this->tenantDB()->table("chsone_vendors_master AS vendor_master")
                    ->where('vendor_master.soc_id', $societyId)
                    ->offset($offset)
                    ->limit($per_page)
                    ->get();
    }

    public function getVendorCounts($societyId)
    {
        return $this->tenantDB()->table("chsone_vendors_master AS vendor_master")
                    ->where('vendor_master.soc_id', $societyId)
                    ->count();
    }

    /**
     * Calculating Billing Amount
    **/
    public function calculateBillAmount($unpaidBills, $vendor)
    {
        $billAmt = 0.00;
        $paidAmount = 0.00;
        foreach ($unpaidBills as $unpaidBill) {
            $chkPaymentAmt = $this->getVendorPaidBills($vendor->vendor_id, $unpaidBill->vendor_bill_id);

            if (!empty($chkPaymentAmt)) {
                foreach ($chkPaymentAmt as $paidBill) {
                    $paidAmount += $paidBill->vendor_bill_payment_amount;
                }
            }
            $billAmt += $unpaidBill->vendor_bill_amount - $paidAmount;
        }

        return $billAmt;
    }

    /**
     * Get the Unpaid Bill number
    **/
    public function getBillIds($unpaidBills)
    {
        $billIds = [];
        foreach ($unpaidBills as $unpaidBill) {
            $billIds[] = $unpaidBill->vendor_bill_num;
        }
        return $billIds;
    }

    public function getAllVendorDetails($societyId)
    {
        return $this->tenantDB()->table("chsone_vendors_master AS vendor_master")
                    ->where('vendor_master.soc_id', $societyId)
                    ->get();
    }

}
