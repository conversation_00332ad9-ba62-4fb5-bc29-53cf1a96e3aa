<?php

namespace App\Console\Commands\Actions\Vendors;

use Illuminate\Console\Command;
use App\Console\Commands\Action;

class fetchDataVendorPaymentListDataSource extends Action
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'datasource:fetchDataVendorPaymentList {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Fetch Data Vendor Payment List DataSource';

    protected $formatter = [
        "id" => "",
        "vendor_id" => "",
        "bill_number" => "",
        "bill_date" => "",
        "purchase_mode" => "",
        "bill_amount" => "",
        "bill_amt" => "setRupees:bill_amount",
        "bill_amount_tds" => "",
        "due_date" => "",
        "payment_status" => "",
        "vendor_bill_roundoff" => "",
        "vendor_bill_writeoff" => "",
        "vendor_bill_payment_amount" => "",
        "amt_paid" => "setRupees:vendor_bill_payment_amount",
        "tds" => "",
        "write_off" => "",
        "advance" => "",
        "due_amount" => "getDueAmount:bill_amount,vendor_bill_payment_amount,advance",
    ];

    protected $formatterByKeys = ['id'];

    protected $mapper = [
        'id' => 'vendor_bill_id'
    ];

    /**
     * Execute the console command.
     */
    public function apply()
    {
        $vendorId = $this->input['vendor_id'];
        $obj = $this->tenantDB()->table('chsone_vendor_bill_master AS bill_master')
            ->selectRaw(
                'bill_master.vendor_bill_id AS id,
                bill_master.vendor_id AS vendor_id,
                vendor_bill_num AS bill_number,
                vendor_bill_date AS bill_date,
                vendor_bill_type_purchase AS purchase_mode,
                vendor_bill_amount AS bill_amount,
                vendor_bill_tds AS bill_amount_tds,
                vendor_bill_due_date AS due_date,
                payment_status,
                vendor_bill_roundoff,
                vendor_bill_writeoff,
                advance_consumed as advance
                ',

            )
            ->leftJoin('chsone_vendors_master as cvm', 'bill_master.vendor_id', '=', 'cvm.vendor_id')
            // ->leftjoin('chsone_vendor_bill_payment_details AS bill_payment', 'bill_payment.vendor_bill_id', '=', 'bill_master.vendor_bill_id')
            ->where('bill_master.vendor_id', $vendorId)
            ->where('bill_master.status', '1')
            ->whereIN('payment_status', ['unpaid', 'partialpaid'])
            ->orderByDesc('vendor_bill_date')
            ->selectRaw(
                "(SELECT IF(SUM(cvbpd.vendor_bill_payment_amount) = 0, 0, SUM(cvbpd.vendor_bill_payment_amount))
                  FROM chsone_vendor_bill_payment_details AS cvbpd
                  LEFT JOIN expense_invoice_payment_tracker AS eipt ON cvbpd.expense_tracker_id = eipt.id
                  WHERE cvbpd.vendor_bill_id = bill_master.vendor_bill_id
                  AND eipt.status != 'reversed') AS vendor_bill_payment_amount"
            )
            ->selectRaw(
                "(SELECT IF(SUM(cvbpd.tds) = 0, 0, SUM(cvbpd.tds))
                  FROM chsone_vendor_bill_payment_details AS cvbpd
                  LEFT JOIN expense_invoice_payment_tracker AS eipt ON cvbpd.expense_tracker_id = eipt.id
                  WHERE cvbpd.vendor_bill_id = bill_master.vendor_bill_id
                  AND eipt.status != 'reversed') AS tds"
            )
            ->selectRaw(
                "(SELECT IF(SUM(cvbpd.write_off) = 0, 0, SUM(cvbpd.write_off))
                  FROM chsone_vendor_bill_payment_details AS cvbpd
                  LEFT JOIN expense_invoice_payment_tracker AS eipt ON cvbpd.expense_tracker_id = eipt.id
                  WHERE cvbpd.vendor_bill_id = bill_master.vendor_bill_id
                  AND eipt.status != 'reversed') AS write_off"
            );

        $result = $obj->get();
        $this->data = $this->format($result->toArray());
    }

    public function getDueAmount($bill_amount, $amount_paid, $advance)
    {
        $bill_amount = (float) $bill_amount;
        $amount_paid = (float) $amount_paid;
        $advance = (float) $advance;
        $amount =  $bill_amount - $amount_paid - $advance;
        // return '₹ ' . number_format($amount, 2);
        return $amount;
    }

    public function setRupees($value)
    {
        $value = (float) $value;
        // return '₹ ' . number_format($value, 2);
        return $value;
    }
}
