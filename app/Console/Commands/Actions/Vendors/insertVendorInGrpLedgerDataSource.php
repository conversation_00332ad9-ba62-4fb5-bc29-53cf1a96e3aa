<?php

namespace App\Console\Commands\Actions\Vendors;

use Illuminate\Console\Command;
use App\Console\Commands\Action;
use App\Models\Tenants\ChsoneGrpLedgerTree;

class insertVendorInGrpLedgerDataSource extends Action
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'datasource:insertVendorInGrpLedger {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Insert Vendor Grp Ledger Data Source';

    /**
     * Execute the console command.
     */
    public function apply()
    {
        $data = $this->input;
        // First need to find out ledger_account_id of the vendor from chsone_grp_ledger_tree table whose ledger_account_name is Vendor
        $objvendorsetting = ChsoneGrpLedgerTree::where('ledger_account_name', 'Vendor')->first();
        $vendor_parent_id = $objvendorsetting->ledger_account_id;

        $vendorLedger = new ChsoneGrpLedgerTree;

        $vendorLedger->soc_id=$data['company_id'] ?? '';
        $vendorLedger->ledger_account_name=$data['vendor_name'].'-'.$data['vendor_id'] ?? '';
        $vendorLedger->nature_of_account= 'cr' ?? '';
        $vendorLedger->parent_id= $vendor_parent_id ?? '';
        $vendorLedger->report_head= 'Balance Sheet' ?? '';
        $vendorLedger->operating_type= $data['operating_type'] ?? '';
        $vendorLedger->context_ref_id= $data['vendor_id'] ?? '';
        $vendorLedger->context= 'vendor' ?? '';
        $vendorLedger->ledger_start_date= date("Y-m-d");
        $vendorLedger->added_on=date("Y-m-d h:i:s");
        $vendorLedger->status= 1;
        $vendorLedger->created_by= $data['user_id'] ?? '';
        $vendorLedger->entity_type = "ledger" ?? '';
        $vendorLedger->behaviour = "liability" ?? '';
        $vendorLedger->defined_by = "user" ?? '';

        $vendorLedger->save();
        if ($vendorLedger->save()) {
            $this->message = "Vendor Ledger created successfully";
            $this->status = 'success';
            $this->statusCode = 200;
            $this->data = $vendorLedger->toArray();
        } else {
            $this->message = "Unable to create vendor ledgers";
            $this->status = 'error';
            $this->statusCode = 400;
            $this->data = $vendorLedger->toArray();
        }

    }
}
