<?php

namespace App\Console\Commands\Actions\Vendors;

use Illuminate\Console\Command;
use App\Console\Commands\Action;
use App\Models\Tenants\ChsoneVendorsMaster;

class editVendorDetailsDataSource extends Action
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'datasource:editVendorDetails {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Edit Vendor Details';

    /**
     * Execute the console command.
     */
    public function apply()
    {
        $data = $this->input;

        // fetch the vendor details according to vendor id
        $vendor = ChsoneVendorsMaster::where('vendor_id', $data['vendor_id'])
        ->where('soc_id', $data['company_id'])
        ->first();

        if(!$vendor) {
            $this->message = "Vendor not found";
            $this->status = 'error';
            $this->statusCode = 400;
        }

        // update the vendor details
        $vendor->update([
            'vendor_name'=>$data['vendor_name'] ?? '',
            'vendor_id'=>$data['vendor_id'] ?? '',
            'vendor_contact_number'=>$data['vendor_contact_number'] ?? '',
            'vendor_phone_number'=>$data['vendor_phone_number'] ?? '',
            'vendor_email'=>$data['vendor_email'] ?? '',
            'vendor_notes'=>$data['vendor_notes'] ?? '',
            'vendor_expense_group_id'=>$data['vendor_expense_group_id'] ?? '',
            'vendor_is_company'=>$data['vendor_is_company'] ?? '',
            'vendor_credit_period'=>$data['vendor_credit_period'] ?? '',
            'vendor_service_regn'=>$data['vendor_service_regn'] ?? '',
            'vendor_pan_num'=>$data['vendor_pan_num'] ?? '',
            'vendor_section_code'=>$data['vendor_section_code'] ?? '',
            'vendor_payee_name'=>$data['vendor_payee_name'] ?? '',
            'vendor_address'=>$data['vendor_address'] ?? '',
            'vendor_vat'=>$data['vendor_vat'] ?? '',
            'vendor_cst'=>$data['vendor_cst'] ?? '',
            'vendor_gst'=>$data['vendor_rcm'] ?? '',
            'status'=>1,
            'added_on'=>date("Y-m-d h:i:s"),
            'update_on'=>date("Y-m-d h:i:s"),
            'start_date'=>date("Y-m-d h:i:s"),
            'end_date'=>date("Y-m-d h:i:s"),
            'soc_id'=>$data['company_id'] ?? '',
            'place_of_supply'=>$data['place_of_supply'] ?? '',
            'vendor_tds_id'=>$data['tds'] ?? ''
        ]);

        $this->message = "Vendor updated successfully";
        $this->status = "success";
        $this->statusCode = 200;
        $this->data = $vendor;
    }
}
