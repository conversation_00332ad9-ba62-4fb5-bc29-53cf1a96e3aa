<?php

namespace App\Console\Commands\Actions\Vendors;

use Illuminate\Console\Command;
use App\Console\Commands\Action;
use App\Models\Tenants\ChsoneLedgerTransaction;

class InsertVendorChsoneLedgerTransactionsDataSource extends Action
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'datasource:InsertVendorChsoneLedgerTransactions {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Insert Vendor Chsone Ledger Transactions Data Source';

    /**
     * Execute the console command.
     */
    public function apply()
    {
        $data = $this->input;

        $vendorLedger = new ChsoneLedgerTransaction;

        $vendorLedger->soc_id=$data['company_id'] ?? '';
        $vendorLedger->transaction_date=date("Y-m-d");
        $vendorLedger->ledger_account_id=$data['vendor_ledger_id'] ?? '';
        $vendorLedger->ledger_account_name=$data['vendor_name'].'-'.$data['vendor_id'] ?? '';
        $vendorLedger->voucher_type=$data['voucher_type'] ?? '';
        $vendorLedger->voucher_reference_number=$data['voucher_reference_number'] ?? '';
        $vendorLedger->voucher_reference_id=$data['voucher_reference_id'] ?? '0';
        $vendorLedger->transaction_type = 'cr' ?? '';
        $vendorLedger->payment_mode = $data['payment_mode'] ?? '';
        $vendorLedger->payment_reference=$data['payment_reference'] ?? '';
        $vendorLedger->transaction_amount=$data['transaction_amount'] ?? '0.000';
        $vendorLedger->other_reference_id=$data['other_reference_id'] ?? '';
        $vendorLedger->txn_from_id = $data['txn_from_id'] ?? '0';
        $vendorLedger->memo_desc=$data['memo_desc'] ?? 'entry for opening balance';
        $vendorLedger->is_opening_balance = '1';
        $vendorLedger->is_reconciled = '0';
        $vendorLedger->is_cancelled = '0';
        $vendorLedger->created_by = $data['user_id'] ?? '';
        $vendorLedger->added_on = date("Y-m-d h:i:s");

        $vendorLedger->save();
        if ($vendorLedger->save()) {
            $this->message = "Vendor Ledger created successfully";
            $this->status = 'success';
            $this->statusCode = 200;
            $this->data = $vendorLedger->toArray();
        } else {
            $this->message = "Unable to create vendor ledgers";
            $this->status = 'error';
            $this->statusCode = 400;
            $this->data = $vendorLedger->toArray();
        }

    }
}
