<?php

namespace App\Console\Commands\Actions\Vendors;

use Illuminate\Console\Command;
use App\Console\Commands\Action;
use App\Models\Tenants\ChsoneVendorsMaster;
use App\Models\Tenants\ChsoneVendorSetting;

class addingVendorDataSource extends Action
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'datasource:addingVendor {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Add a vendor to the system.';

    /**
     * Execute the console command.
     */
    /**
    @SWG\Post(
        path="/datasource:addingVendor",
        tags={"Vendors"},
        summary="Add a vendor to the system.",
        operationId="addingVendorDataSource",
        @SWG\Parameter(
            name="flowId",
            in="path",
            description="Flow ID",
            required=true,
            type="string"
        ),
        @SWG\Parameter(
            name="parentId",
            in="path",
            description="Parent ID",
            required=true,
            type="string"
        ),
        @SWG\Parameter(
            name="input",
            in="path",
            description="Input",
            required=true,
            type="string"
        ),
        @SWG\Response(
            response=200,
            description="Vendor created successfully"
        ),
        @SWG\Response(
            response=400,
            description="Unable to create vendor"
        )
    )
     * @return void
     */
    public function apply()
    {
        $data = $this->input;

        $vendor = new ChsoneVendorsMaster;

        $vendor->vendor_name=$data['vendor_name'] ?? '';
        $vendor->vendor_id=$data['vendor_id'] ?? '';
        $vendor->vendor_contact_number=$data['vendor_contact_number'] ?? '';
        $vendor->vendor_phone_number=$data['vendor_phone_number'] ?? '';
        $vendor->vendor_email=$data['vendor_email'] ?? '';
        $vendor->vendor_notes=$data['vendor_notes'] ?? '';
        $vendor->vendor_expense_group_id=$data['vendor_expense_group_id'] ?? '';
        $vendor->vendor_is_company=$data['vendor_is_company'] ?? '';
        $vendor->vendor_credit_period=$data['vendor_credit_period'] ?? '';
        $vendor->vendor_service_regn=$data['vendor_service_regn'] ?? '';
        $vendor->vendor_pan_num=$data['vendor_pan_num'] ?? '';
        $vendor->vendor_section_code=$data['vendor_section_code'] ?? '';
        $vendor->vendor_payee_name=$data['vendor_payee_name'] ?? '';
        $vendor->vendor_address=$data['vendor_address'] ?? '';
        $vendor->vendor_vat=$data['vendor_vat'] ?? '';
        $vendor->vendor_cst=$data['vendor_cst'] ?? '';
        $vendor->vendor_gst=$data['vendor_rcm'] ?? '';
        $vendor->vendor_tds_id=$data['tds'] ?? '';
        $vendor->status= 1;
        $vendor->added_on=date("Y-m-d h:i:s");
        $vendor->update_on=date("Y-m-d h:i:s");
        $vendor->start_date= date("Y-m-d h:i:s");
        $vendor->end_date= date("Y-m-d h:i:s");
        $vendor->soc_id=$data['company_id'] ?? '';
        $vendor->place_of_supply = $data['place_of_supply'] ?? '';
        if (!empty($data['vendor_tds_id'])) {
            $vendor->vendor_tds_id = $data['vendor_tds_id'];
        }

        $vendor->save();
        if ($vendor->save()) {
            $this->message = "Vendor created successfully";
            $this->status = 'success';
            $this->statusCode = 200;
            $this->data = $vendor->toArray();
        } else {
            $this->message = "Unable to create vendor";
            $this->status = 'error';
            $this->statusCode = 400;
            $this->data = $vendor->toArray();
        }
    }
}
