<?php

namespace App\Console\Commands\Actions\Vendors;

use Illuminate\Console\Command;
use App\Console\Commands\Action;
use App\Models\Tenants\ChsoneVendorBillMaster;
use App\Models\Tenants\ChsoneVendorsMaster;
use App\Models\Tenants\ChsoneGrpLedgerTree;
use App\Models\Tenants\ExpenseInvoicePaymentTracker;
use App\Models\Tenants\ChsoneLedgerTransaction;
use App\Models\Tenants\ChsoneVendorBillParticular;
use App\Models\Tenants\ChsoneVendorBillPaymentDetail;
use App\Models\Tenants\ChsoneVendorBillPaymentDetailParticular;

class AddVendorBillDataSource extends Action
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'datasource:addVendorBill {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Action for AddVendorBillDataSource';

    /**
     * Execute the console command.
     */
    public function apply()
    {
        $postedValues = $this->input;
        $soc_id = $postedValues['company_id'];//$request->query('company_id');
        $postedValues['user_id'] = $this->input['user_id'] ?? 0;
        $chsoneVendorBillMaster = new ChsoneVendorBillMaster();
        $chsoneBillParticular = new ChsoneVendorBillParticular();
        $newVendorBill = $chsoneVendorBillMaster->saveVendorBill($postedValues, $soc_id);
        $postedValues['bill_id'] = $newVendorBill;
        $newPartiular = $chsoneBillParticular->saveParticularVendorBill($postedValues, $soc_id);
        $message = "Vendor Bill has been added successfully";
        $this->status = 'success';
        $this->statusCode = 200;
        $this->message = $message;
    }
}
