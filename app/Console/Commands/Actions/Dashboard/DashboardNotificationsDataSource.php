<?php

namespace App\Console\Commands\Actions\Dashboard;

use App\Console\Commands\Action;

class DashboardNotificationsDataSource extends Action
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'datasource:dashboardNotifications {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Dashboard Helpdesk Data Source';

    /**
     * Execute the console command.
     */

    public function apply()
    {

        $response_data = $this->mongodb()
        ->collection('chsone_notifications')
        ->where('soc_id', $this->input['company_id'])
        ->where('scope', '!=', 'public')
        ->where('user_ids', '!=', $this->input['user_id'])
        ->orderBy("_id", -1) // Order by insertion time in descending order
        ->limit(100)
        ->get();
        
        $this->data = $response_data;
    }

}
