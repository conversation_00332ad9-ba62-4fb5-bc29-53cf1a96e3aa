<?php

namespace App\Console\Commands\Actions\Dashboard;

use App\Console\Commands\Action;

class DashboardBalanceDuesDataSource extends Action
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'datasource:dashboardBalanceDues {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Dashboard Balance Dues Data Source';

    /**
     * Execute the console command.
     */

    public function apply()
    {

        $nonMemberBillList = $this->action('datasource:NonMemberBillList', $this->pointer, $this->input);
        $non_member_list_total_due = 0;
        // calculate non_member list total due only for those whose payment status id unapid and partialpaid
        foreach ($nonMemberBillList as $key => $value) {
  
            // $bill_amount = (float) $value['total_due'] ?? 0;
            $non_member_list_total_due += (float) $value['total_due'];
        }

        $incidentalBillList = $this->action('datasource:IncidentalBillListNew', $this->pointer, $this->input);

        $incidental_bill_list_total_due = 0;
        foreach ($incidentalBillList as $key => $value) {

            $total_due_amount = (float) $value['total_due_amount'] ?? 0;

            $incidental_bill_list_total_due += (float) $total_due_amount;
        }

        $invoiceDS = $this->action('service:maintenanceInvoice', $this->pointer, $this->input);

        $response = [
            'non_member_list_total_due' => round((float)$non_member_list_total_due, 2),
            'incidental_bill_list_total_due' => round((float)$incidental_bill_list_total_due, 2),
            'maintenance_invoice_total_due' => round((float)$invoiceDS, 2),
        ];

        $this->data = $response;
    }

}
