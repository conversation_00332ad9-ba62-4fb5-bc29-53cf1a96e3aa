<?php

namespace App\Console\Commands\Actions\Dashboard;

use App\Console\Commands\Action;
use Illuminate\Support\Facades\DB;
use App\Models\Tenants\ChsoneGrpLedgerTree;
use App\Models\Tenants\ChsoneAccountsMaster;
use App\Console\Commands\Actions\Account\viewBankAccountsListDataSource;
use Carbon\Carbon;

class DashboardBankCashLedgerDataSource extends Action
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'datasource:dashboardBankCashLedger {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Dashboard Bank Cash Ledger Data Source';

    /**
     * Execute the console command.
     */
    public function apply()
    {
        $soc_id = $this->input['company_id'];

        $cash_accounts = $this->listAccounts('cash', 0, 0, TRUE);       

        $arrCashBalance = 0;
        // Disable for optimize
        $arrFYDetail['start_date'] = $this->getCurrentFinantialYear(['soc_id' => $this->input['company_id']]);
        $arrFYDetail['end_date'] = $this->getCurrentDate('database');
        foreach ($cash_accounts as $eachItem) {
            $viewBankAccountsListObj = new viewBankAccountsListDataSource();
            $amount = str_replace(',', '', $viewBankAccountsListObj->cashLedgerBal($eachItem->ledger_account_id, $soc_id, $arrFYDetail['start_date'],$arrFYDetail['end_date']));
            $arrCashBalance = (int)$amount;
        }

        //echo "<pre>";print_r($cash_accounts->toArray());

        $bank_accounts = $this->listAccounts('bank', 0, 0, TRUE);      

        $arrBankBalance = [];
        $totalBankBalance = 0;
        
        $total_amount = 0;
        foreach ($bank_accounts as $eachItem) {
            $viewBankAccountsListObj = new viewBankAccountsListDataSource();
            $amount = str_replace(',', '', $viewBankAccountsListObj->cashLedgerBal($eachItem->ledger_account_id, $soc_id, $arrFYDetail['start_date'],$arrFYDetail['end_date']));
            $arrBankBalance[$eachItem->account_id] = (!empty($amount) && $amount > 0) ? (int)$amount : 0;            
            $total_amount += $amount;
        }
        $totalBankBalance = (int)$total_amount;

        $this->data = [
            //'cash_accounts' => $cash_accounts,
            'arrCashBalance' => $arrCashBalance,
            //'bank_accounts' => $bank_accounts,
            //'arrBankBalance' => $arrBankBalance,
            'totalBankBalance' => $totalBankBalance
        ];
    }

    public function listAccounts($acc_type = 'bank', $number = '', $offset = '', $linear_arr = true)
    {
        $soc_id           = $this->input['company_id'];
        $condition        = "a.soc_id = :soc_id: AND b.context=:context:";
        $bind['soc_id']   = $soc_id;
        $bind['context']  = $acc_type;
        if ($acc_type == 'bank') {
            //$condition     .= " AND a.account_number != :acno:";
            //$bind['acno']   = '';
        } else {
            //$condition     .= " AND a.account_number = :acno:";
            //$bind['acno']   = '';
        }

        $builder = $this->tenantDB()
            ->table('chsone_accounts_master as a')
            ->leftJoin('chsone_grp_ledger_tree as b', 'a.ledger_account_id', '=', 'b.ledger_account_id')
            ->where(function($query) use ($condition, $bind) {
                // Convert the condition string and bind parameters to Laravel's where clauses
                foreach ($bind as $key => $value) {
                    $condition = str_replace(":$key:", "?", $condition);
                }
                $query->whereRaw($condition, array_values($bind));
            })
            ->orderByDesc('a.account_id');

        if ($linear_arr) {
            return $builder->get();  // Changed from ->getQuery()->execute() to ->get()
        } else {
            return $builder;
        }
    }

    public function getCurrentFinantialYear($soc_id)
    {

        // Fetch the account start master record
        $objAccountStartMaster = $this->tenantDB()->table('soc_account_start_master')->where('soc_id', $soc_id)->first();

        $objAccountStartMaster = json_decode(json_encode($objAccountStartMaster), true);
        // Convert to array or set as an empty array if null
        $arrAccountStartMaster = $objAccountStartMaster ? $objAccountStartMaster : [];

        if (!empty($arrAccountStartMaster)) {
            $currentMonth = Carbon::now()->month; // Get the current month as a number (1-12)

            // Get fiscal year start month from 'fy_start_from' (assuming it's 'MM-DD' format)
            $fiscalStartMonth = (int) explode("-", $arrAccountStartMaster['fy_start_from'])[0];

            // Set the base year for the fiscal start date
            $currentYear = Carbon::now()->year;

            // Adjust the fiscal year start date based on the current month
            if ($currentMonth < $fiscalStartMonth) {
                $currentYear--; // Set to previous year if before fiscal start month
            }

            $currentFinancialDate = Carbon::create($currentYear, $fiscalStartMonth, 1)->toDateString(); // Create date in 'YYYY-MM-DD' format
        } else {
            $currentFinancialDate = null; // Return null if no data is found
        }

        return $currentFinancialDate;
    }
}
