<?php

namespace App\Console\Commands\Actions\Dashboard;

use App\Console\Commands\Action;

class DashboardHelpdeskDataSource extends Action
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'datasource:dashboardHelpdesk {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Dashboard Helpdesk Data Source';

    /**
     * Execute the console command.
     */

    public function apply()
    {
        // $helpdesk_data=$this->tenantDB()
        //     ->table('helpdesk_issues2')
        //     ->where("is_active",1)
        //     ->get();

        // if(!$helpdesk_data){
        //     $this->status = 'error';
        //     $this->message = 'Unable to Fetch HelpDesk Data';
        //     $this->statusCode = 400;
        //     return;
        // }


        // $response_data=[
        //     "is_overdue"=>0,
        //     "open"=>0,
        //     "closed"=>0,
        //     "resolved"=>0,
        //     "on_hold"=>0,
        //     "new"=>0,
        // ];

        // foreach($helpdesk_data as $issue){
        //     if($issue->status=="open" && $issue->is_answered===1){
        //         $response_data["open"]++;
        //     }
        //     if($issue->status=="closed"){
        //         $response_data["closed"]++;
        //     }
        //     if($issue->status=="resolved"){
        //         $response_data["resolved"]++;
        //     }
        //     if($issue->status=="onhold"){
        //         $response_data["on_hold"]++;
        //     }
        //     if($issue->is_answered===0 && $issue->status=="open"){
        //         $response_data["new"]++;
        //     }
        //     if($issue->is_overdue===1){
        //         $response_data["is_overdue"]++;
        //     }
        // }

        $company_id = $this->input['company_id'];
        // $formatted_company_id = strlen($company_id) === 3 ? $company_id : str_pad($company_id, 3, '0', STR_PAD_LEFT);

        $databaseName = 'soc_db_' . $company_id;
        $query = "
                    SELECT 
                    SUM(CASE 
                        WHEN is_overdue = 1 AND LOWER(status) NOT IN ('closed', 'onhold', 'resolved') THEN 1 
                        ELSE 0 
                    END) AS is_overdue,
                    SUM(CASE 
                        WHEN LOWER(status) = 'open' AND is_answered = 1 THEN 1   -- Assuming is_answered is boolean 1 (true)
                        ELSE 0 
                    END) AS open,
                    SUM(CASE 
                        WHEN LOWER(status) = 'open' AND is_answered = 0 THEN 1   -- Assuming is_answered is boolean 0 (false)
                        ELSE 0 
                    END) AS new,
                    SUM(CASE 
                        WHEN LOWER(status) = 'closed' THEN 1 
                        ELSE 0 
                    END) AS closed,
                    SUM(CASE 
                        WHEN LOWER(status) = 'resolved' THEN 1 
                        ELSE 0 
                    END) AS resolved,
                    SUM(CASE 
                        WHEN LOWER(status) = 'onhold' THEN 1 
                        ELSE 0 
                    END) AS onhold
                FROM helpdesk_issues2
                WHERE is_active = 1";

        $response_data = $this->tenantDB()->select($query);

        if (!$response_data) {
            $this->status = 'error';
            $this->message = 'Unable to Fetch HelpDesk Data';
            $this->statusCode = 400;
            return;
        }

        // Assuming $response_data is an array with one element
        $response_data = (array) $response_data[0];


        $this->data = $response_data;
    }

}
