<?php

namespace App\Console\Commands\Actions\Dashboard;

use App\Console\Commands\Action;
use Illuminate\Support\Facades\DB;

class DashboardExpnesesDataSource extends Action
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'datasource:dashboardExpenses {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Dashboard Expenses Data Source';

    /**
     * Execute the console command.
     */
    protected $hugeData = true;

    public function apply()
    {
        
        $company_id = $this->input['company_id'];
        // $formatted_company_id = strlen($company_id) === 3 ? $company_id : str_pad($company_id, 3, '0', STR_PAD_LEFT);

        $databaseName = 'soc_db_'.$company_id;

        // $query = "
        //         SELECT 
        //         sub.vendor_bill_id,
        //         sub.vendor_id,
        //         sub.vendor_bill_amount,
        //         sub.vendor_bill_date,
        //         sub.total_amount,
        //         sub.month
        //     FROM (
        //         SELECT 
        //             vendor_bill_id,
        //             vendor_id,
        //             vendor_bill_amount,
        //             vendor_bill_date,
        //             total_amount,
        //             month,
        //             ROW_NUMBER() OVER (PARTITION BY month ORDER BY vendor_bill_date DESC) AS rn
        //         FROM (
        //             SELECT 
        //                 vendor_bill_id,
        //                 vendor_id,
        //                 vendor_bill_amount,
        //                 vendor_bill_date,
        //                 SUM(vendor_bill_amount) AS total_amount,
        //                 DATE_FORMAT(vendor_bill_date, '%M') AS month
        //             FROM ${databaseName}.chsone_vendor_bill_master
        //             GROUP BY month, vendor_bill_date
        //         ) AS subquery
        //     ) AS sub
        //     WHERE rn = 1
        //     ORDER BY sub.vendor_bill_date DESC;
        // ";
        // $data = $this->tenantDB()->select($query);

        // foreach ($data as $key => $value) {
        //     if (is_null($value->month)) {
        //         unset($data[$key]);
        //     }
        // }

        // $this->data = $data;

        $query = $data = $this->tenantDB()->table('chsone_vendor_bill_master')
            ->select(
                'vendor_bill_id', 'vendor_id',
            )
            ->selectRaw('SUM(vendor_bill_amount) as vendor_bill_amount')
            ->selectRaw('SUM(vendor_bill_amount) as total_amount')
            ->selectRaw('DATE_FORMAT(vendor_bill_date, "%M") as month')
            ->where('soc_id', $this->input['company_id'])
            ->groupBy('vendor_bill_date')
            ->get();

        $data = $query;

        foreach ($data as $expenses) {
            if (!isset($result[$expenses->month])) {
                $result[$expenses->month] = 0;
            }
            $result[$expenses->month] = $expenses->vendor_bill_amount;
        }

        $final = [];
        foreach ($result as $month => $amount) {
            $final[] = [
                'vendor_bill_amount' => $amount,
                'total_amount' => $amount,
                'month' => $month
            ];
        }
        
        $this->data = $final;
    }
}
