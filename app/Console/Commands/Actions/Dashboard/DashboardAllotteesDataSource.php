<?php

    namespace App\Console\Commands\Actions\Dashboard;
    use Illuminate\Support\Facades\DB;

    use App\Console\Commands\Action;

    class DashboardAllotteesDataSource extends Action
    {
        /**
         * The name and signature of the console command.
         *
         * @var string
         */
        protected $signature = 'datasource:dashboardAllottees {flowId} {parentId} {input}';

        /**
         * The console command description.
         *
         * @var string
         */
        protected $description = 'Dashboard Allottes Data Source';

        /**
         * Execute the console command.
         */

        public function apply()
        {
            // $sql = "SELECT count(*)as member_count
            // FROM chsone_members_master as cmm 
            // JOIN chsone_member_type_master as cmtm ON cmm.member_type_id = cmtm.member_type_id 
            // where cmm.status = 1 AND cmtm.status = 1 AND fk_unit_id != '' AND approved = 1 AND (cancel_date = '0000-00-00') 
            // group by cmm.member_type_id";

            //convert above query into php laravel query and connect db $this->tenantDB()

            $allottees_data = $this->tenantDB()
                ->table('chsone_members_master as cmm')
                ->join('chsone_member_type_master as cmtm', 'cmm.member_type_id', '=', 'cmtm.member_type_id')
                ->where('cmm.status', 1)
                ->where('cmtm.status', 1)
                ->where('fk_unit_id', '!=', '')
                ->where('approved', 1)
                ->where('cancel_date', '0000-00-00')
                ->groupBy('cmm.member_type_id')
                ->select('cmm.member_type_id', DB::raw('count(*) as member_count'))
                ->pluck('member_count', 'member_type_id');


                $members_count = [
                    'primary_members' => 0,
                    'associates' => 0,
                    'nominal' => 0,
                    'tenants' => 0
                ];

                $members_count['primary_members'] = $allottees_data[1] ?? 0;   // Assuming member_type_id 1 = primary
                $members_count['associates'] = $allottees_data[2] ?? 0;          // Assuming member_type_id 2 = associates
                $members_count['nominal'] = $allottees_data[3] ?? 0;             // Assuming member_type_id 3 = nominal
                $members_count['tenants'] = $allottees_data[4] ?? 0;             

            $this->data = $members_count;
        }

    }
