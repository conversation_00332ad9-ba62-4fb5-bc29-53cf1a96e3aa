<?php

namespace App\Console\Commands\Actions\Dashboard;

use App\Console\Commands\Action;

class DashboardSurveysDataSource extends Action
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'datasource:dashboardSurveys {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Dashboard Surveys Data Source';

    /**
     * Execute the console command.
     */

    public function apply()
    {
        $notice_data = $this->tenantDB()->table('chsone_notices')
            ->where('soc_id', $this->input['company_id'])
            ->where('status', 1)
            ->where('type', 'survey')
            ->orderBy('notice_id', 'desc')
            ->limit(2)
            ->get();
            
        $response_data = [];
        if (count($notice_data) > 0) {

            foreach ($notice_data as $notice) {
                $response_data[] = [
                    "soc_id" => $this->input['company_id'],
                    'survey_id' => $notice->notice_id,
                    'notice_name' => $notice->subject,
                    'count' => $this->surveyResponseCount($notice),
                ];

            }

        }
        $this->data = $response_data;

    }

    public function surveyResponseCount($notice)
    {
        $count = 0;

        $notice_survey_data = $this->mongodb()->table('chsone_surveys')
            ->where('survey_id', (string) $notice->notice_id)
            ->where('soc_id', $this->input['company_id'])
            ->limit(1)
            ->orderBy("_id", -1)
            ->get();

        if (count($notice_survey_data) > 0) {
            $notice_survey_response_data = $this->mongodb()->table('chsone_surveys_responses')
                ->where('survey_id', $notice_survey_data[0]["survey_id"])
                ->where('soc_id', $this->input['company_id'])
                ->orderBy("_id", -1)
                ->get();

            if (count($notice_survey_response_data) > 0) {

                foreach ($notice_survey_response_data as $response) {
                    $count += count($response["unit_response"]);
                }
            }


        }

        return $count;
    }

}
