<?php

namespace App\Console\Commands\Actions\Account\Vouchers;
use App\Console\Commands\Action;
use App\Http\Traits\MongoTraits;

class AccountMongoLogDataSource extends Action
{
    use MongoTraits; // Use the MongoTraits trait in this class

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'datasource:accountMongoLog {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Account Mongo Log Data Source';

    /**
     * Execute the console command.
     */
    public function apply()
    {
        $module = $this->input['module'];
        $company_id = $this->input['company_id'];
        $id =  '';
        // $fields = $this->input['fields'] ?? [];
        // if(!empty($fields)){
        //     $fields = json_decode($fields);
        // }
    
        $this->data = $this->showNotification($module, $company_id, $id, );

    }
}
