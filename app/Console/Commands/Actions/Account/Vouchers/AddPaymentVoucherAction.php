<?php

namespace App\Console\Commands\Actions\Account\Vouchers;

use Illuminate\Console\Command;
use App\Console\Commands\Action;

class AddPaymentVoucherAction extends Action
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'action:addPaymentVoucher {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Add Payment Voucher';

    /**
     * Execute the console command.
     */
    public function apply()
    {
        $socId = $this->input['company_id'];
        $voucherType = "payment";
        $fromLedgerId = $this->input['from_ledger'];
        $toLedgerId = $this->input['to_ledger'];
        $amount = $this->input['transaction_amount'];
        $receiptDate = strip_tags($this->input['transaction_date']);
        $txnDate = $this->getDatabaseDate($receiptDate);
        $refNumber = $this->input['payment_reference'];
        $memoDesc = strip_tags($this->input['memo_desc']);

        $fromLedgerName = preg_replace("/^[^0-9a-zA-Z]+/", "", $this->getLedger($fromLedgerId, 0)->ledger_account_name);
        $toLedgerName = preg_replace("/^[^0-9a-zA-Z]+/", "", $this->getLedger($toLedgerId, 0)->ledger_account_name);
        
        $data = [
            "soc_id" => $socId,
            "voucher_type" => $voucherType,
            'from_ledger_account_id' => $fromLedgerId,
            'to_ledger_account_id' => $toLedgerId,
            'transaction_date' => $txnDate,
            'amount' => $amount,
            'reference' => $refNumber,
            'narration' => $memoDesc,
            'from_ledger_account_name' => $fromLedgerName,
            'to_ledger_account_name' => $toLedgerName,
            "sub_type" => ""
        ];
        //dd($data);
        $response = $this->saveVoucherEntry($data);

        if ($response['success'] == true) {
            $data["voucher_id"] = $response['voucher_id'];
            $data["modeOfPayment"] = "";
            $data["otherRecpRef"] = "";
            $data["voucher_reference_id"] = $response['voucher_id'];
            $data["voucher_reference_number"] = "";
            $data["is_cancelled"] = "0";
            $data["created_by"] = $this->input['user_id'] ?? 0;
            $id = $this->executeVoucher($data);
            $this->message = "Payment Entry added successfully";
        } else {
            $this->message = "Payment Entry not created. Please try again";
        }
    }
}
