<?php

namespace App\Console\Commands\Actions\Account\Vouchers;

use Illuminate\Console\Command;
use App\Console\Commands\Action;

class DebitNoteAction extends Action
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'action:addDebitNote {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Add Debit Note';

    /**
     * Execute the console command.
     */
    public function apply()
    {
        $socId = $this->input['company_id'];
        $voucherType = "debit note";
        $fromLedgerId = $this->input['from_ledger'];
        $toLedgerId = $this->input['to_ledger'];
        $toLedgerId1 = $this->input['to_ledger1'] ?? "";
        $salesType = $this->input['salestype'] ?? "";
        $amount = $this->input['transaction_amount'];
        $receiptDate = strip_tags($this->input['transaction_date']);
        $txnDate = $this->getDatabaseDate($receiptDate);
        $refNumber = $this->input['payment_reference'];
        $memoDesc = strip_tags($this->input['memo_desc']);

        $fromLedgerName = preg_replace("/^[^0-9a-zA-Z]+/", "", $this->getLedger($fromLedgerId, 0)->ledger_account_name);
        $toLedgerName = preg_replace("/^[^0-9a-zA-Z]+/", "", $this->getLedger($toLedgerId, 0)->ledger_account_name);
        $toLedgerName1 = preg_replace("/^[^0-9a-zA-Z]+/", "", $this->getLedger($toLedgerId1, 0)->ledger_account_name);

        $data = [
            "soc_id" => $socId,
            "voucher_type" => $voucherType,
            'from_ledger_account_id' => $fromLedgerId,
            'to_ledger_account_id' => $toLedgerId,
            'transaction_date' => $txnDate,
            'amount' => $amount,
            'reference' => $refNumber,
            'narration' => $memoDesc,
            'from_ledger_account_name' => $fromLedgerName,
            'to_ledger_account_name' => $toLedgerName,
            'type' => $salesType
        ];
        $response = $this->saveVoucherEntry($data);

        if ($response['success'] == true) {
            $data["voucher_id"] = $response['voucher_id'];
            $data["modeOfPayment"] = "";
            $data["otherRecpRef"] = "";
            $data["voucher_reference_id"] = $response['voucher_id'];
            $data["voucher_reference_number"] = "";
            $data["created_by"] = $this->input['user_id'] ?? 0;
            $data["is_cancelled"] = "0";
            $data["sub_type"] = "";
            $id = $this->executeVoucher($data);
            if ($id > 0 && $toLedgerId1 != "") {
                $data['from_ledger_account_id'] = $toLedgerId;
                $data['to_ledger_account_id'] = $toLedgerId1;
                $data['from_ledger_account_name'] = $toLedgerName;
                $data['to_ledger_account_name'] = $toLedgerName1;
                $data["voucher_id"] = $response['voucher_id'];
                $id = $this->executeVoucher($data);
            }
            $this->message = "Debit Note Entry added successfully";
        } else {
            $this->message = "Debit Note Entry not created. Please try again";
        }
    }
}
