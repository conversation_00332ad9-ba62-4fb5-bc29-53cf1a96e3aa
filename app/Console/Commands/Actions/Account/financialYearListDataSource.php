<?php

namespace App\Console\Commands\Actions\Account;

use Illuminate\Console\Command;
use App\Console\Commands\Action;
use DateTime;

class financialYearListDataSource extends Action
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'datasource:financialYearList {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Get financial year list data source';

    /**
     * Execute the console command.
     */
    public function apply()
    {
        $result = $this->tenantDB()->table('soc_account_financial_year_master')
            ->select('account_closing_id', 'fy_start_date', 'fy_end_date', 'closed')
            ->where('closed', 0)
            // ->orderBy('account_closing_id', 'asc')
            ->first();

        if ($result) {
            // Parse the financial year dates
            $start = new DateTime($result->fy_start_date);
            $end = new DateTime($result->fy_end_date);
    
            // Ensure the end date includes the whole month
            $end->modify('last day of this month');
    
            $data = [];
    
            // Generate the array with "id" and "name"
            while ($start <= $end) {
                $data[] = [
                    'id' => $start->format('Y-m'), // Example: "2024-04"
                    'name' => $start->format('F Y') // Example: "April 2024"
                ];

                // Move to the next month
                $start->modify('+1 month');
            }
    
            $this->data = $data;
        }
    }
}
