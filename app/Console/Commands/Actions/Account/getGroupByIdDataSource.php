<?php

namespace App\Console\Commands\Actions\Account;

use Illuminate\Console\Command;
use App\Console\Commands\Action;
use App\Models\Tenants\ChsoneGrpLedgerTree;

class getGroupByIdDataSource extends Action
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'datasource:getGroupById {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Fetch group details.';

    /**
     * Execute the console command.
     */
    public function apply()
    {
        $group_account_id = $this->input['group_account_id'];

        $obj = $this->tenantDB()->table('chsone_grp_ledger_tree as accounts')
        ->where('ledger_account_id', $group_account_id);
        $result = $obj->first();

        // According to the $ledger->parent_id fetch ladger_account_name from chsone_grp_ledger_tree and store the value in $result->ledger_account_name
        $parent_ledger_name = ChsoneGrpLedgerTree::select('ledger_account_name')->where('ledger_account_id', $result->parent_id)->first();
        $result->parent_ledger_name = $parent_ledger_name->ledger_account_name;

        $this->data = $result;
    }
}
