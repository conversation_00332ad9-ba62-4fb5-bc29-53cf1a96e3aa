<?php

namespace App\Console\Commands\Actions\Account;

use Illuminate\Console\Command;
use App\Console\Commands\Action;
use App\Models\Tenants\ChsoneGrpLedgerTree;
use App\Models\Tenants\ChsoneLedgerTransaction;
use App\Models\Tenants\SocAccountFinancialYearMaster;
use Illuminate\Support\Facades\Config;

class getLedgerByIdDataSource extends Action
{
    protected $constants;

    public function __construct()
    {
        parent::__construct();
        $this->constants = Config::get("constants");
    }

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'datasource:getLedgerById {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Fetch ledger details.';

    /**
     * Execute the console command.
     */
    public function apply()
    {
        $ledger_account_id = $this->input['ledger_account_id'];
        $soc_id = $this->input['company_id'];

        $transaction_date = '';
        $transaction_amount = 0;
        $transaction_type = '';
        $arrAllPreviousFY = [];
        $arrFYDetail = [];

        $ledger = $this->getLedger($ledger_account_id);
        
        // According to the $ledger->parent_id fetch ladger_account_name from chsone_grp_ledger_tree and store the value in $ledger->ledger_account_name
        $parent_ledger_name = ChsoneGrpLedgerTree::select('ledger_account_name')->where('ledger_account_id', $ledger->parent_id)->first();
        $ledger->parent_ledger_name = $parent_ledger_name->ledger_account_name;
        if($ledger->operating_type == 'direct') {
            $ledger->income_type = 'Direct';
        } elseif($ledger->operating_type == 'indirect') {
            $ledger->income_type = 'Indirect';
        } else {
            $ledger->income_type = 'Direct';
        }

        if($ledger->nature_of_account == 'cr') {
            $ledger->amount_debit_credit = 'Credit';
            $ledger->nature_of_group = 'Credit';
        } elseif($ledger->nature_of_account == 'dr') {
            $ledger->amount_debit_credit = 'Debit';
            $ledger->nature_of_group = 'Debit';
        } else {
            $ledger->nature_of_group = 'Inappropriate selection of Group. Try again.';
        }
        

        $year = SocAccountFinancialYearMaster::where('soc_id', $this->input['company_id'])->orderBy('account_closing_id', 'desc')->get();
        if (!empty($year) && count($year) > 0) {
            $arrYear = $year->toArray();
            $currentYearKey = '';
            // foreach ($arrYear as $key => $eachYear) {
            //     if ($eachYear['confirmed'] == 0) {
            //         $currentYearKey = $key;
            //         $arrFYDetail = $eachYear;
            //     }
            // }
            // if (is_numeric($currentYearKey)) {
            //     unset($arrYear[$currentYearKey]);
            // }
        }

        if (!empty($arrYear) && count($arrYear) > 0) {
            foreach ($arrYear as $key => $eachYear) {
                $objGetFirstTransaction = ChsoneLedgerTransaction::whereIn('is_opening_balance', [1, 2, 3])
                ->where('ledger_account_id', $ledger_account_id)
                ->where('soc_id', $soc_id)
                ->where('transaction_date', '<=', $eachYear['fy_end_date'])
                ->where('transaction_date', '>=', $eachYear['fy_start_date'])->first();

                $arrAllPreviousFY[$key] = $eachYear;
                if($eachYear['confirmed'] == 1) {
                    $arrAllPreviousFY[$key]['is_editable'] = false;
                } else {
                    $arrAllPreviousFY[$key]['is_editable'] = true;
                }
                if ($objGetFirstTransaction) {
                    $arrAllPreviousFY[$key]['transaction_type'] = $objGetFirstTransaction->transaction_type;
                    $arrAllPreviousFY[$key]['txn_id'] = $objGetFirstTransaction->txn_id;
                } else {
                    $arrAllPreviousFY[$key]['transaction_type'] = '';
                    $arrAllPreviousFY[$key]['txn_id'] = '';
                }
                $arrAllPreviousFY[$key]['edit_opening_bal'] = ($eachYear['confirmed'] == 1) ? 0 : 1;
                if (!empty($arrAllPreviousFY[$key]['edit_opening_bal']) && !in_array($this->input['user_id'], $this->constants['support_access']) && !in_array($this->input['user_id'], $this->constants['edit_support_access'])) { //Dev
                    $arrAllPreviousFY[$key]['edit_opening_bal'] = 0;
                }
                $arrAllPreviousFY[$key]['fy_start_date'] = $this->getDisplayDate($eachYear['fy_start_date']);
                $arrAllPreviousFY[$key]['fy_end_date'] = $this->getDisplayDate($eachYear['fy_end_date']);
                $arrAllPreviousFY[$key]['opening_balance'] = (!empty($objGetFirstTransaction)) ? number_format($objGetFirstTransaction->transaction_amount, 2) : 'Not Present';
                $arrAllPreviousFY[$key]['label'] = 'Opening Balance';
            }
            $transaction_type = $arrAllPreviousFY[0]['transaction_type'];
        }

        usort($arrAllPreviousFY, function($a, $b) {
            return strtotime($a['fy_start_date']) <=> strtotime($b['fy_start_date']);
        });

        $objGetFirstTransaction = ChsoneLedgerTransaction::whereIn('is_opening_balance', [1, 2, 3])
        ->where('ledger_account_id', $ledger_account_id)
        ->where('soc_id', $soc_id)
        ->where('transaction_date', '<=', $eachYear['fy_end_date'])
        ->where('transaction_date', '>=', $eachYear['fy_start_date'])->get();

        if (!empty($objGetFirstTransaction)) {
            $arrGetFirstTransaction = $objGetFirstTransaction->toArray();
            foreach ($arrGetFirstTransaction as $arrTransactiondetails) {
                if ($arrTransactiondetails['is_opening_balance'] >= 2) {
                } elseif ($arrTransactiondetails['is_opening_balance'] == 1) {
                    $transaction_amount = $arrTransactiondetails['transaction_amount'];
                    if (empty($transaction_type)) {
                        $transaction_type = $arrTransactiondetails['transaction_type'];
                    }
                    $transaction_date = $arrTransactiondetails['transaction_date'];
                }
            }
        }

        $result = array();
        // $result['ledger'] = $ledger;
        $result['ledger_account_id'] = $ledger->ledger_account_id;
        $result['ledger_account_name'] = $ledger->ledger_account_name;
        $result['behaviour'] = $ledger->behaviour;
        $result['parent_id'] = $ledger->parent_id;
        $result['parent_ledger_name'] = $ledger->parent_ledger_name;
        $result['income_type'] = $ledger->income_type;
        $result['amount_debit_credit'] = $ledger->amount_debit_credit;
        $result['nature_of_group'] = $ledger->nature_of_group;
        $result['transaction_type'] = $transaction_type;
        $result['transaction_date'] = $transaction_date;
        $result['transaction_amount'] = $transaction_amount;
        $result['fy_start_date'] = date('Y-m-d', strtotime($eachYear['fy_start_date']));
        $result['fy_end_date'] =  date('Y-m-d', strtotime($eachYear['fy_end_date']));

        // according to the $arrAllPreviousFY array we have to display only label, opening_balance, fy_start_date, fy_end_date and store into $result
        $result['arrAllPreviousFY'] = array();
        foreach($arrAllPreviousFY as $previousFY) {
            $result['arrAllPreviousFY'][] = array(
                'label' => $previousFY['label'],
                'opening_balance' => $previousFY['opening_balance'],
                'fy_start_date' => date('Y-m-d', strtotime($previousFY['fy_start_date'])),
                'fy_end_date' => date('Y-m-d', strtotime($previousFY['fy_end_date'])),
                'is_editable' => $previousFY['is_editable'],
                // add fy in result array as array of fy_start_date and fy_end_date
                'fy' => array(
                    date('Y-m-d', strtotime($previousFY['fy_start_date'])),
                    date('Y-m-d', strtotime($previousFY['fy_end_date']))
                )
            );
        }

        $this->data = $result;
        return $result;
    }

    public function getLedger($ledger_acct_id, $return_object = 1)
    {
        //fetch ledger details from chsone_grp_ledger_tree whose ledger_account_id = $ledger_acct_id
        $ledger = ChsoneGrpLedgerTree::where("ledger_account_id", $ledger_acct_id)->first();

        if ($ledger) {
            if ($return_object) {
                return $ledger;
            } else {
                return $ledger->toArray();
            }
        } else {
            return false;
        }
    }
}
