<?php

namespace App\Console\Commands\Actions\Account;

use App\Console\Commands\Action;
use App\Console\Commands\Actions\Reports\IRegisterAction;
use App\Models\Tenants\ChsoneIregisterNominee;
use App\Models\Tenants\ChsoneMemberShareCertificate;

class UpdateShareCertificateDataSource extends Action
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'datasource:updateShareCertificate {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Update Share Certificate DataSource';

    /**
     * Execute the datasource logic.
     */
    public function apply()
    {
        try{
            $unit_id = $this->input['unit_id'] ?? null;
            $soc_id = $this->input['company_id'] ?? null;
            $shareId = $this->input['share_id'] ?? null;
            if (!$unit_id) {
                $this->status = 'error';
                $this->message = 'ID is required';
                $this->data = [];
                return;
            }
            $registerShareDetls = $this->getShareDetailsByUnitIdAction($soc_id, $unit_id);
            $unit_info = $this->tenantDB()->table('chsone_units_master')->where('soc_id', $soc_id)
            ->where('unit_id', $unit_id)
            ->where(function ($query) {
                $query->whereNull('cancel_date')
                    ->orWhere('cancel_date', '0000-00-00');
            })
            ->first();
            if (!empty($unit_info)) {
                $unitDetls = json_decode(json_encode($unit_info), true);
                $title = 'Share Certificate - ' . ucwords($unitDetls['soc_building_name']) . ' ' . $unitDetls['unit_flat_number'];
                $memberType =  $this->tenantDB()->table('chsone_member_type_master')->where('member_type_name', 'Primary')->first();
                $memberTypeId = $memberType ? $memberType->member_type_id : null;

                $members = $this->tenantDB()->table('chsone_members_master')->where('soc_id', $soc_id)
                ->where('status', 1)
                ->where('member_type_id', $memberTypeId)
                ->where('fk_unit_id', $unit_id)
                ->get();
                $memberFullName = $members[0]->member_first_name . ' ' . $members[0]->member_last_name;
                $memberId = $members[0]->id;
                if ($shareId == '') {
                    $options['memberFullName'] = ucwords($memberFullName);
                    $options['memberId'] = $memberId;
                }
                
                if (!empty($shareId)) {
                    $i_model = ChsoneMemberShareCertificate::where('id', $shareId)
                        ->where('soc_id', $soc_id)
                        ->where('status', 1)
                        ->first();
                    $process = 'edited';
                } else {
                    $i_model = new ChsoneMemberShareCertificate();
                    $process = 'added';
                }

                $i_model->certificate_no = $this->input['certificate_no'];
                $i_model->member_reg_no = $this->input['member_reg_no'];
                $i_model->unit_id = $unit_id;
                $i_model->soc_id = $soc_id;
                $i_model->membership_id = $memberId;
                $reqAdmissionDate = $this->input['admission_date'];
                $i_model->admission_date = (empty($reqAdmissionDate)) ? NULL : $reqAdmissionDate;
                $i_model->entrance_fee = $this->input['entrance_fee'];
                $i_model->full_name = $this->input['full_name'];
                $i_model->address = $this->input['address'];
                $i_model->occupation = $this->input['occupation'];
                $i_model->age_on_admission = $this->input['age_on_admission'];
                $i_model->ceasing_reason = $this->input['cessation_reason'];
                $i_model->no_of_shares = $this->input['no_of_shares'];
                $i_model->cancel_date = $this->input['cancel_date'];
                $i_model->share_value = $this->input['share_value'];
                $i_model->share_series_start = $this->input['share_series_start'];
                $i_model->share_series_end = $this->input['share_series_end'];
                $i_model->amount_paid = $this->input['no_of_shares'] * $this->input['share_value'];
                $i_model->status = 1;
                $i_model->created_by = $this->input['user_id'] ?? '';
                $i_model->updated_by = $this->input['user_id'] ?? '';
                $i_model->created_date = date('Y-m-d');
                $i_model->updated_date = date('Y-m-d');
                $result = $i_model->save();

                if (!$result) {
                    $this->status = 'error';
                    $this->statusCode = '400';
                    $this->message = 'Share certificate could not be added';
                }else{
                    $this->status = 'success';
                    $this->statusCode = '200';
                    $this->message = 'Share certificate has been successfully ' . $process;
                    if (!empty($this->input['nominee']['id'])) {
                        $nomineeslist = implode(',', $this->input['nominee']['id']);
                        $iregisterNominees = ChsoneIregisterNominee::where('share_certificate_id', $shareId)
                        ->whereNotIn('nominees_id', explode(',', $nomineeslist))
                        ->get();    
                        foreach ($iregisterNominees as $iregisterNominee) {
                            $iregisterNominee->status = 0;
                            if (!$iregisterNominee->save()) {
                                $this->status = 'error';
                                $this->statusCode = '400';
                                $this->message = 'Nominees could not be added';
                            }
                        }
                    }

                    foreach ($this->input['nominee'] as $nominee) {
                        // Try to fetch existing nominee if ID is present
                        $iregisterNominee = null;

                        if (!empty($nominee['id'])) {
                            $iregisterNominee = ChsoneIregisterNominee::where('nominees_id', $nominee['id'])
                                ->where('status', 1)
                                ->first();
                        }

                        // If not found, create new
                        if (!$iregisterNominee) {
                            $iregisterNominee = new ChsoneIregisterNominee();
                        }

                        // Assign values
                        $iregisterNominee->nominees_id = $nominee['id'];
                        $iregisterNominee->nominee_name = $nominee['name'];
                        $iregisterNominee->nominee_address = $nominee['address'];
                        $iregisterNominee->percentage = $nominee['percentage'];
                        $iregisterNominee->is_minor = $nominee['minor'];
                        $iregisterNominee->guardian_name = $nominee['guardian_name'];
                        $iregisterNominee->guardian_address = $nominee['guardian_address'];
                        $iregisterNominee->relation_with_minor = $nominee['relation_with_minor'];
                        $iregisterNominee->share_certificate_id = $i_model->id; // assuming $i_model is already set
                        $iregisterNominee->status = 1;
                        $iregisterNominee->created_by = $this->input['user_id'];
                        $iregisterNominee->updated_by = $this->input['user_id'];
                        $iregisterNominee->created_date = date('Y-m-d');
                        $iregisterNominee->updated_date = date('Y-m-d');

                        // Save and handle error
                        if (!$iregisterNominee->save()) {
                            $this->status = 'error';
                            $this->statusCode = '400';
                            $this->message = 'Nominees could not be added';
                        }
                    }

                    
                }
            } 
            
        }catch(\Exception $e){
            dd($e);
        }
    }

    public function getShareDetailsByUnitIdAction($soc_id, $unit_id)
{
    $arrResponse = [];
    // Fetch the member_share_certificate record
    $result = $this->tenantDB()
    ->table('chsone_member_share_certificate')
        ->where('soc_id', $soc_id)
        ->where('unit_id', $unit_id)
        ->where('status', 1)
        ->first();
    if (!empty($result)) {
        $arrResponse['member_share_certificate'] = (array) $result;
        $arrResponse['iregister'] = (array) $result;
        $shareId = $arrResponse['iregister']['id'];
        // Assuming these methods exist and return arrays
        $arrResponse['nominees'] = $this->getNomineeDetailsByShareIdAction($shareId);
        $arrResponse['cashbookfolios'] = $this->getCashBookFolioDetailsByShareIdAction($shareId);
    }
    // Return the array (or any other format you prefer
    return $arrResponse;
}

public function getNomineeDetailsByShareIdAction($share_certificate_id)
{
    $arrResponse = [];
    $result = $this->tenantDB()
        ->table('chsone_iregister_nominees')
        ->where('share_certificate_id', $share_certificate_id)
        ->where('status', 1)
        ->get();
    if (!$result->isEmpty()) {
        $arrResponse = $result->toArray();
    }
    return $arrResponse;
}

public function getCashBookFolioDetailsByShareIdAction($share_certificate_id)
{
    $arrResponse = [];
    $result = $this->tenantDB()
        ->table('chsone_cashbook_folios as cashbookfolios')
        ->select([
            'cashbookfolios.id',
            'cashbookfolios.cashbook_folio_no',
            'cashbookfolios.source_cashbook_folio_id',
            'cashbookfolios.created_date',
            'sourcecashbookfolios.cashbook_folio_no as source_cashbook_folio_no',
        ])
        ->leftJoin('chsone_cashbook_folios as sourcecashbookfolios', 'sourcecashbookfolios.id', '=', 'cashbookfolios.source_cashbook_folio_id')
        ->where('cashbookfolios.share_certificate_id', $share_certificate_id)
        ->orderByDesc('cashbookfolios.created_date')
        ->get();
        
    if (!$result->isEmpty()) {
        $arrResponse = $result->toArray();
    }
    return $arrResponse;
}
}
