<?php

namespace App\Console\Commands\Actions\Account;

use App\Console\Commands\Action;

class AddShareCertificateDataSource extends Action
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'datasource:addShareCertificate {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Add Share Certificate DataSource';

    /**
     * Execute the datasource logic.
     */
    public function apply()
    {
        // TODO: Implement DB insert logic for share certificate
        $this->data = ['status' => 'success', 'message' => 'Share certificate added (stub)'];
    }
}
