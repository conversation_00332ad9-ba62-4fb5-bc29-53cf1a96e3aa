<?php

namespace App\Console\Commands\Actions\Account;

use Illuminate\Console\Command;
use App\Console\Commands\Action;

class nonMemberIncomeAccountsDataSource extends Action
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'datasource:nonMemberIncomeAccounts {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Fetch non-member income accounts.';

    protected $formatter = [
        'ledger_account_id' => '',
        'ledger_account_name' => '',
        'nature_of_account' => '',
        'report_head' => '',
        'entity_type' => '',
        'behaviour' => '',
        'parent_id' => '',
        'status' => '',
        'context' => '',
    ];

    protected $formatterByKeys = ['ledger_account_id'];

    protected $mapper = [
        'ledger_account_id' => 'ledger_account_id',
    ];

    /**
     * Execute the console command.
     */
    public function apply()
    {
        $obj = $this->tenantDB()->table('chsone_grp_ledger_tree')
        ->select('ledger_account_id', 'ledger_account_name', 'nature_of_account', 'report_head', 'entity_type', 'behaviour', 'parent_id', 'status', 'context')
        ->where('parent_id', '87');

        $result = $obj->get();

        $this->data = $this->format($result->toArray());
    }
}
