<?php

namespace App\Console\Commands\Actions\Account;

use Illuminate\Console\Command;
use App\Console\Commands\Action;
use App\Models\Tenants\ChsoneLedgerTransaction;
use App\Models\Tenants\SocAccountFinancialYearMaster;

class IncorrectLedgerTransactionDataSource extends Action
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'datasource:IncorrectLedgerTransactionDataSource {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Get the list of incorrect ledger transaction';

    protected $formatter = [
        'id' => '',
        'transaction_type' => '',
        'transaction_date' => '',
        'ledger_account_name' => '',
        'voucher_type' => '',
        'voucher_reference_number' => '',
        'transaction_amount' => '',
        'narration' => '',
        'reconciliation_status' => '',
        'cancellation_status' => ''
    ];

    /**
     * Execute the console command.
     */
    public function apply()
    {
        $soc_id = $this->input['company_id'];

        $page = $this->input['page'] ?? 1;
        $per_page = !empty($this->input['per_page']) ? $this->input['per_page']: ($this->hugeData ? 100000 : 10);
        $offset = ($page - 1) * $per_page;

        $arrFYDetail = SocAccountFinancialYearMaster::where('soc_id', $soc_id)
        ->where('confirmed', 0)
        ->selectRaw('MIN(fy_start_date) as min_fy_start_date, MAX(fy_end_date) as max_fy_end_date')
        ->get();

        if (!empty($arrFYDetail)) {
            $arrFYDetail = $arrFYDetail->toArray();
            $arrFYDetail = $arrFYDetail[0];
        }

        // get total count of incorrect ledger transactions
        $totalCount = ChsoneLedgerTransaction::where(function ($query) use ($arrFYDetail) {
            $query->where('transaction_date', '<', $arrFYDetail['min_fy_start_date'])
                  ->orWhere('transaction_date', '>', $arrFYDetail['max_fy_end_date']);
        })
        ->selectRaw("
            txn_id as id,
            transaction_type,
            DATE_FORMAT(transaction_date, '%Y-%m-%d') as transaction_date,
            ledger_account_name,
            voucher_type,
            voucher_reference_number,
            transaction_amount,
            memo_desc as narration,
            CASE WHEN is_reconciled = 1 THEN 'Reconciled' ELSE 'Unreconciled' END as reconciliation_status,
            CASE WHEN is_cancelled = 1 THEN 'Cancelled' ELSE 'Active' END as cancellation_status
        ")
        ->count();

        // Assuming $arrFYDetail is an array with start and end dates
        $ledgerTxns = ChsoneLedgerTransaction::where(function ($query) use ($arrFYDetail) {
            $query->where('transaction_date', '<', $arrFYDetail['min_fy_start_date'])
                  ->orWhere('transaction_date', '>', $arrFYDetail['max_fy_end_date']);
        })
        ->selectRaw("
            txn_id as id,
            transaction_type,
            DATE_FORMAT(transaction_date, '%Y-%m-%d') as transaction_date,
            ledger_account_name,
            voucher_type,
            voucher_reference_number,
            transaction_amount,
            memo_desc as narration,
            CASE WHEN is_reconciled = 1 THEN 'Reconciled' ELSE 'Unreconciled' END as reconciliation_status,
            CASE WHEN is_cancelled = 1 THEN 'Cancelled' ELSE 'Active' END as cancellation_status
        ")
        ->offset($offset)
        ->limit($per_page)
        ->get()
        ->toArray();

        $this->data = $ledgerTxns;
        $this->meta['pagination']['total'] = $totalCount;
    }
}
