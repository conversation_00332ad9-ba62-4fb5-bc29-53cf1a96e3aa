<?php

namespace App\Console\Commands\Actions\Account;

use App\Console\Commands\Action;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class viewBankAccountsListDataSource extends Action
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'datasource:viewBankAccountsList  {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Get Bank Accounts List Data Source';

    protected $mapper = [
        'id' => 'ledger_account_id',
    ];

    /**
     * Execute the console command.
     */
    public function apply()
    {
        $soc_id = $this->input['company_id'];
        $obj = $this->tenantDB()->table('chsone_accounts_master')
            ->select('chsone_accounts_master.ledger_account_id as id', 'account_id', 'bank_name', 'branch', 'account_number', 'bank_address', 'bank_city', 'bank_ifsc', 'default_account', 'chsone_accounts_master.status', 'group_id', 'active_for_payments', 'default_bank_for_incidental', 'default_bank_for_nonmember', 'ledger_account_name', 'account_name')
            ->leftJoin('chsone_grp_ledger_tree', 'chsone_grp_ledger_tree.ledger_account_id', '=', 'chsone_accounts_master.ledger_account_id')
            ->where('group_id', 5)
            ->where('chsone_accounts_master.status', 1)
            ->where('chsone_grp_ledger_tree.status', 1)
            ->orderBy('chsone_accounts_master.ledger_account_id', 'desc');

        $page = $this->input['page'] ?? 1;
        $per_page = !empty($this->input['per_page']) ? $this->input['per_page'] : ($this->hugeData ? 100000 : 10);
        $offset = ($page - 1) * $per_page;

        $count = $obj->count();

        $obj = $obj->offset($offset);
        $obj = $obj->limit($per_page);

        $result = $obj->get();

        $arrFYDetail['start_date'] = $this->getCurrentFinantialYear($soc_id);
        $arrFYDetail['end_date'] = $this->getCurrentDate('database');

        $arrBankBalance  = [];

        foreach ($result as $eachItem) {
            $amount = str_replace(',', '', $this->cashLedgerBal($eachItem->id, $soc_id, $arrFYDetail['start_date'], $arrFYDetail['end_date']));
            $arrBankBalance[$eachItem->account_id] = (!empty($amount) && $amount > 0) ? number_format($amount, 2) : number_format(0, 2);
            $eachItem->balance = $arrBankBalance[$eachItem->account_id];
        }
        $this->meta['pagination']['total'] = $count;
        $response = [
            [
                'id' => 0,
                'ledger_account_name' => 'Bank',
                'rows' => $result,
            ],
        ];
        $this->data = $result;
    }

    public function getCurrentFinantialYear($soc_id)
    {

        // Fetch the account start master record
        $objAccountStartMaster = $this->tenantDB()->table('soc_account_start_master')->where('soc_id', $soc_id)->first();

        $objAccountStartMaster = json_decode(json_encode($objAccountStartMaster), true);
        // Convert to array or set as an empty array if null
        $arrAccountStartMaster = $objAccountStartMaster ? $objAccountStartMaster : [];

        if (!empty($arrAccountStartMaster)) {
            $currentMonth = Carbon::now()->month; // Get the current month as a number (1-12)

            // Get fiscal year start month from 'fy_start_from' (assuming it's 'MM-DD' format)
            $fiscalStartMonth = (int) explode("-", $arrAccountStartMaster['fy_start_from'])[0];

            // Set the base year for the fiscal start date
            $currentYear = Carbon::now()->year;

            // Adjust the fiscal year start date based on the current month
            if ($currentMonth < $fiscalStartMonth) {
                $currentYear--; // Set to previous year if before fiscal start month
            }

            $currentFinancialDate = Carbon::create($currentYear, $fiscalStartMonth, 1)->toDateString(); // Create date in 'YYYY-MM-DD' format
        } else {
            $currentFinancialDate = null; // Return null if no data is found
        }

        return $currentFinancialDate;
    }

    public function cashLedgerBal($ledger_id, $soc_id, $start_date = '', $end_date = '')
    {

        $filter_criteria = array();
        if (empty($start_date)) {
            $translist = $this->getLedgerTransactions($soc_id, $ledger_id, $filter_criteria, 'ledger');
        } else {

            $translist = $this->getLedgerTransactionsByFY(array('soc_id' => $soc_id, 'ledger_id' => $ledger_id, 'criteria' => $filter_criteria, 'ledger' => 'ledger', 'start_date' => $start_date, 'end_date' => $end_date));
        }

        $ledger = $this->getLedger($ledger_id);
        $ledger = json_decode(json_encode($ledger), true);
        if (!empty($ledger)) {
            $ledger = $ledger;
        }

        $overall_balance = 0.00;
        $overall_nature = $ledger['nature_of_account'] ?? '';

        $translist = json_decode(json_encode($translist), true);

        if (!empty($translist['transaction_total'])) {

            if ($translist['transaction_total'][0]['credit_amount'] > $translist['transaction_total'][0]['debit_amount']) {

                $overall_nature = 'cr';
                $overall_balance = $translist['transaction_total'][0]['credit_amount'] - $translist['transaction_total'][0]['debit_amount'];
            } elseif ($translist['transaction_total'][0]['credit_amount'] < $translist['transaction_total'][0]['debit_amount']) {

                $overall_nature = 'dr';
                $overall_balance = $translist['transaction_total'][0]['debit_amount'] - $translist['transaction_total'][0]['credit_amount'];
            }
        }
        if($ledger == false){
            $nature_of_account = $ledger['nature_of_account'] ?? '';
            $overall_balance = ($overall_nature != $nature_of_account) ? (-1) * $overall_balance : $overall_balance;
        } else {
            $overall_balance = ($overall_nature != $ledger['nature_of_account']) ? (-1) * $overall_balance : $overall_balance;
        }
        return number_format($overall_balance, 2);
    }

    public function getLedgerTransactions($soc_id, $ledger_id, $criteria, $entity = 'ledger', $offset = 0, $length =  20)
    {
        $result_array = [];
        $filter_array = [];
        $column_array = ['SQL_CALC_FOUND_ROWS a.*, b.*'];
        $sort_array = [];

        // Build criteria clause
        $criteria_clause = implode(' AND ', array_filter($criteria));
        if (!empty($criteria_clause)) {
            $filter_array[] = $criteria_clause;
        }


        // Entity-specific conditions and sorting
        $filter_array[] = "a.soc_id = $soc_id";
        $filter_array[] = "transaction_amount != 0.00";

        switch ($entity) {
            case 'ledger':
                $sort_array = ['a.transaction_date DESC', 'a.added_on DESC'];
                $filter_array[] = "a.ledger_account_id = $ledger_id";
                break;

            case 'main':
            case 'group':
                $sort_array = ['a.ledger_account_id ASC', 'a.transaction_date DESC', 'a.added_on DESC'];
                $filter_array[] = "a.ledger_account_id IN($ledger_id)";
                break;
        }

        // Execute primary query with SQL_CALC_FOUND_ROWS
        $sql = "
            SELECT " . implode(', ', $column_array) . "
            FROM chsone_ledger_transactions AS a
            LEFT JOIN chsone_grp_ledger_tree AS b ON a.ledger_account_id = b.ledger_account_id
            WHERE " . implode(' AND ', $filter_array) . "
            ORDER BY " . implode(', ', $sort_array) . "
            LIMIT $offset, $length
        ";
        $transaction_array = $this->tenantDB()->select($sql);


        // Get total record count without limit\

        $found_rows_array = $this->tenantDB()->select('SELECT FOUND_ROWS() as total_records');
        $total_records = $found_rows_array[0]->total_records ?? 0;

        // Determine least transaction ID for each ledger account
        $least_ledger_ac_id = [];
        foreach ($transaction_array as $transaction) {
            if (
                !isset($least_ledger_ac_id[$transaction->ledger_account_id]) ||
                ($least_ledger_ac_id[$transaction->ledger_account_id] > $transaction->txn_id)
            ) {
                $least_ledger_ac_id[$transaction->ledger_account_id] = $transaction->txn_id;
            }
        }

        // Calculate total credit and debit amounts
        $column_array = [
            'a.txn_id',
            'a.ledger_account_id',
            'SUM(IF(a.transaction_type = "cr", a.transaction_amount, 0)) AS credit_amount',
            'SUM(IF(a.transaction_type = "dr", a.transaction_amount, 0)) AS debit_amount',
        ];
        $sort_array = ['a.transaction_date DESC', 'a.added_on DESC', 'a.ledger_account_id'];

        $filter_array = array_merge($filter_array, [
            "a.transaction_date <= '" . $this->getCurrentDate('database') . "'",
            "a.ledger_account_id IN($ledger_id)",
        ]);

        $sql = "
            SELECT " . implode(', ', $column_array) . "
            FROM chsone_ledger_transactions AS a
            LEFT JOIN chsone_grp_ledger_tree AS b ON a.ledger_account_id = b.ledger_account_id
            WHERE " . implode(' AND ', $filter_array) . "
            ORDER BY " . implode(', ', $sort_array);
        $transaction_total = $this->tenantDB()->select($sql);
        // Prepare previous transaction totals if needed
        $prev_transaction_total = [];
        if (!empty($transaction_array) && ($offset + $length < $total_records)) {
            foreach ($transaction_total as $total) {
                $x_where_clause = str_replace('a.', 'x.', $criteria_clause);

                // Filter for previous transactions
                $filter_array = [
                    "a.ledger_account_id IN(" . $total->ledger_account_id . ")",
                    "a.soc_id = $soc_id",
                    "transaction_amount != 0.00",
                    "a.txn_id NOT IN (
                        SELECT tmp.txn_id
                        FROM (
                            SELECT x.ledger_account_id, x.txn_id
                            FROM chsone_ledger_transactions AS x
                            WHERE " . (!empty($x_where_clause) ? $x_where_clause . ' AND ' : '') . "
                                  x.ledger_account_id IN(" . $total->ledger_account_id . ")
                                  AND x.soc_id = $soc_id
                                  AND x.transaction_amount != 0
                            ORDER BY x.transaction_date DESC, x.added_on DESC
                            LIMIT " . ($offset + $length) . "
                        ) AS tmp
                    )",
                ];

                $sql = "
                    SELECT " . implode(', ', $column_array) . "
                    FROM chsone_ledger_transactions AS a
                    LEFT JOIN chsone_grp_ledger_tree AS b ON a.ledger_account_id = b.ledger_account_id
                    WHERE " . implode(' AND ', $filter_array) . "
                    ORDER BY " . implode(', ', $sort_array);
                $previous_ledger_transaction_total = $this->tenantDB()->select($sql);

                if (!empty($previous_ledger_transaction_total)) {
                    $prev_transaction_total = array_merge($prev_transaction_total, $previous_ledger_transaction_total);
                }
            }
        }

        // Compile final result array
        $result_array = [
            'transaction_array' => $transaction_array,
            'total_records' => $total_records,
            'transaction_total' => $transaction_total,
            'prev_transaction_total' => $prev_transaction_total,
        ];


        return $result_array;
    }



    public function getLedgerTransactionsByFY($data)
    {

        $soc_id = $data['soc_id'];
        $ledger_id = $data['ledger_id'];
        $criteria = $data['criteria'];
        $entity = !empty($data['entity']) ? $data['entity'] : 'ledger';
        $offset = !empty($data['offset']) ? $data['offset'] : 0;
        $length = !empty($data['length']) ? $data['length'] : 20;
        $start_date = $data['start_date'];
        $end_date = $data['end_date'];

        $result_array = [];
        $filter_array = [];
        $column_array = ['SQL_CALC_FOUND_ROWS a.*, b.*'];
        $sort_array = [];

        // Build criteria clause
        $criteria_clause = implode(' AND ', array_filter($criteria));
        if (!empty($criteria_clause)) {
            $filter_array[] = $criteria_clause;
        }

        // Entity-specific conditions and sorting
        $filter_array[] = "a.soc_id = $soc_id";
        $filter_array[] = "a.transaction_amount != 0.00";
        $filter_array[] = "a.transaction_date BETWEEN '$start_date' AND '$end_date'";

        switch ($entity) {
            case 'ledger':
                $sort_array = ['a.transaction_date DESC', 'a.added_on DESC'];
                $filter_array[] = "a.ledger_account_id = $ledger_id";
                break;

            case 'main':
            case 'group':
                $sort_array = ['a.ledger_account_id ASC', 'a.transaction_date DESC', 'a.added_on DESC'];
                $filter_array[] = "a.ledger_account_id IN($ledger_id)";
                break;
        }

        // Primary SQL query with SQL_CALC_FOUND_ROWS
        $sql = "
        SELECT " . implode(', ', $column_array) . "
        FROM chsone_ledger_transactions AS a
        LEFT JOIN chsone_grp_ledger_tree AS b ON a.ledger_account_id = b.ledger_account_id
        WHERE " . implode(' AND ', $filter_array) . "
        ORDER BY " . implode(', ', $sort_array) . "
        LIMIT $offset, $length
    ";
        $transaction_array = $this->tenantDB()->select($sql);

        // Get total record count without limit
        $found_rows_array = $this->tenantDB()->select('SELECT FOUND_ROWS() as total_records');
        $total_records = $found_rows_array[0]->total_records ?? 0;

        // Determine least transaction ID for each ledger account
        $least_ledger_ac_id = [];
        foreach ($transaction_array as $transaction) {
            if (!isset($least_ledger_ac_id[$transaction->ledger_account_id]) || ($least_ledger_ac_id[$transaction->ledger_account_id] > $transaction->txn_id)) {
                $least_ledger_ac_id[$transaction->ledger_account_id] = $transaction->txn_id;
            }
        }

        // Calculate total credit and debit amounts
        $column_array = [
            'a.txn_id',
            'a.ledger_account_id',
            'SUM(IF(a.transaction_type = "cr", a.transaction_amount, 0)) AS credit_amount',
            'SUM(IF(a.transaction_type = "dr", a.transaction_amount, 0)) AS debit_amount',
        ];
        $sort_array = ['a.transaction_date DESC', 'a.added_on DESC', 'a.ledger_account_id'];

        $filter_array = array_merge($filter_array, [
            "a.ledger_account_id IN($ledger_id)"
        ]);

        $sql = "
        SELECT " . implode(', ', $column_array) . "
        FROM chsone_ledger_transactions AS a
        LEFT JOIN chsone_grp_ledger_tree AS b ON a.ledger_account_id = b.ledger_account_id
        WHERE " . implode(' AND ', $filter_array) . "
        ORDER BY " . implode(', ', $sort_array);

        $transaction_total = $this->tenantDB()->select($sql);

        // Prepare previous transaction totals if needed
        $prev_transaction_total = [];
        if (!empty($transaction_array) && ($offset + $length < $total_records)) {
            foreach ($transaction_total as $total) {
                $x_where_clause = str_replace('a.', 'x.', $criteria_clause);

                // Filter for previous transactions
                $filter_array = [
                    "a.ledger_account_id IN(" . $total->ledger_account_id . ")",
                    "a.soc_id = $soc_id",
                    "a.transaction_amount != 0.00",
                    "a.transaction_date BETWEEN '$start_date' AND '$end_date'",
                    "a.txn_id NOT IN (
                    SELECT tmp.txn_id
                    FROM (
                        SELECT x.ledger_account_id, x.txn_id
                        FROM chsone_ledger_transactions AS x
                        WHERE " . (!empty($x_where_clause) ? $x_where_clause . ' AND ' : '') . "
                              x.ledger_account_id IN(" . $total->ledger_account_id . ")
                              AND x.soc_id = $soc_id
                              AND x.transaction_amount != 0
                        ORDER BY x.transaction_date DESC, x.added_on DESC
                        LIMIT " . ($offset + $length) . "
                    ) AS tmp
                )",
                ];

                $sql = "
                SELECT " . implode(', ', $column_array) . "
                FROM chsone_ledger_transactions AS a
                LEFT JOIN chsone_grp_ledger_tree AS b ON a.ledger_account_id = b.ledger_account_id
                WHERE " . implode(' AND ', $filter_array) . "
                ORDER BY " . implode(', ', $sort_array);
                $previous_ledger_transaction_total = $this->tenantDB()->select($sql);

                if (!empty($previous_ledger_transaction_total)) {
                    $prev_transaction_total = array_merge($prev_transaction_total, $previous_ledger_transaction_total);
                }
            }
        }


        // Compile final result array
        $result_array = [
            'transaction_array' => $transaction_array,
            'total_records' => $total_records,
            'transaction_total' => $transaction_total,
            'prev_transaction_total' => $prev_transaction_total,
        ];

        return $result_array;
    }
}
