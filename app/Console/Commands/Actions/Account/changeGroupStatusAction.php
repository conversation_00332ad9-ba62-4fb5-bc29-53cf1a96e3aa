<?php

namespace App\Console\Commands\Actions\Account;

use Illuminate\Console\Command;
use App\Console\Commands\Action;
use App\Models\Tenants\ChsoneAccountsMaster;
use App\Models\Tenants\ChsoneGrpLedgerTree;
use App\Models\Tenants\ChsoneLedgerTransaction;
use Carbon\Carbon;

class changeGroupStatusAction extends Action
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'datasource:changeGroupStatus {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Change Group Status Action';

    /**
     * Execute the console command.
     */

    public function apply()
    {
        $id = $this->input['id'];
        $status = $this->input['status'];

        $obj = $this->tenantDB()->table('chsone_grp_ledger_tree')->where('ledger_account_id', $id)->update([
            'status' => $status
        ]);

        if($obj) {
            $this->status = 'success';
            $this->message = 'Group status updated successfully';
            $this->statusCode = 200;
        } else {
            $this->status = 'error';
            $this->message = 'Group status not updated';
            $this->statusCode = 400;
        }
    }
}
