<?php

namespace App\Console\Commands\Actions\Account;

use Illuminate\Console\Command;
use App\Console\Commands\Action;

class GetAllLedgersAction extends Action
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'datasource:getAllLedgers {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Get All Ledgers List';



    protected $formatterKey = ['id'];

    protected $mapper = [
        'id' => 'grp_ledger.ledger_account_id',
        'ledger_account_name' => "grp_ledger.ledger_account_name",
        'behaviour' => "grp_ledger.behaviour",
    ];

    /**
     * Execute the console command.
     */
    public function apply()
    {
        $page = $this->input['page'] ?? 1;
        $per_page = !empty($this->input['per_page']) ? $this->input['per_page'] : ($this->hugeData ? 100000 : 10);
        $offset = ($page - 1) * $per_page;
        $name = '';
        $behaviour = '';
        $nature = '';
        $status = '';
        $searchTerm = $this->input['filters']['search'] ?? '';

        if (isset($this->input['filters'])) {
            $name =
            !empty($this->input['filters']['name']) ? $this->input['filters']['name'] : '';
        unset($this->input['filters']['name']);
            
        $behaviour =
            !empty($this->input['filters']['behaviour']) ? $this->input['filters']['behaviour'] : '';
        unset($this->input['filters']['behaviour']);


        $nature = isset($this->input['filters']['nature']) ? explode(',', $this->input['filters']['nature']) : [];
        $status = isset($this->input['filters']['status']) ? explode(',', $this->input['filters']['status']) : [];

        }

        // Start building the query
        $obj = $this->tenantDB()->table('chsone_grp_ledger_tree AS grp_ledger')
            ->selectRaw('
                ledger_account_id AS id,
                ledger_account_id,
                ledger_account_name AS name,
                (CASE
                    WHEN nature_of_account = "cr" THEN "Credit"
                    WHEN nature_of_account = "dr" THEN "Debit"
                END) AS nature,
                behaviour,
                entity_type,
                parent_id AS parent,
                status,
                context,
                operating_type
            ')
            ->where('entity_type', 'ledger')
            ->where('grp_ledger.soc_id', $this->input['company_id']); // Ensure soc_id filter is applied

        // Apply filters
        if ($name) {
            $obj->where('grp_ledger.ledger_account_name', 'LIKE', '%' . $name . '%');
        }
        

        if ($behaviour) {
            $obj->whereRaw('LOWER(grp_ledger.behaviour) LIKE ?', ['%' . strtolower($behaviour) . '%']);
        }

        if($nature) {
            $obj->whereIn('nature_of_account', $nature);
        }
        
        if (!empty($status)) {
            $obj->whereIn('status', $status);
        }
        
        if ($searchTerm) {
            $columns = ['ledger_account_id', 'ledger_account_name', 'behaviour', 'nature_of_account', 'status','parent_id','operating_type','context'];

            $obj->where(function ($q) use ($columns, $searchTerm) {
                foreach ($columns as $column) {
                    $q->orWhere($column, 'LIKE', '%' . $searchTerm . '%');
                }
            });
        }

        // Get total count after applying all filters
        $count = $obj->count();

        // Apply pagination
        $obj = $obj->offset($offset)->limit($per_page);
        // Fetch results
        $result = $obj->get();
        // Set response data
        $this->data = $result;
        $this->meta['pagination']['total'] = $count;
    }

}
