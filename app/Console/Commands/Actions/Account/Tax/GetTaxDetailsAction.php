<?php

namespace App\Console\Commands\Actions\Account\Tax;

use App\Console\Commands\Action;
use Illuminate\Support\Facades\DB;

class GetTaxDetailsAction extends Action
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'datasource:GetTaxDetails {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Get Tax Class details with associated Tax Categories';

    /**
     * Execute the console command.
     */
    public function apply()
    {
        try {
            $tax_class_id = $this->input['tax_class_id'];
            $soc_id = $this->input['company_id'] ?? null;

            if (!$soc_id) {
                return [
                    'error' => 'Company ID is required'
                ];
            }

            // Get tax class details
            $taxClass = $this->tenantDB()->table('chsone_tax_classes')
                ->where('tax_class_id', $tax_class_id)
                ->select(
                    'tax_class_id',
                    'tax_class_name',
                    'tax_class_description',
                    'tax_categories_footer'
                )
                ->first();

            if (!$taxClass) {
                return [
                    'error' => 'Tax class not found'
                ];
            }

            // Get associated tax categories
            $taxCategories = $this->tenantDB()->table('chsone_tax_categories')
                ->where('fk_tax_class_id', $tax_class_id)
                ->select(
                    'tax_categories_id',
                    'tax_categories_name',
                    'tax_categories_amount',
                    'tax_categories_type',
                    'tax_categories_from_date',
                    'tax_categories_to_date',
                    'tax_code as tax_categories_code',
                    'status'
                )
                ->selectRaw("
                    CONCAT(tax_categories_amount,
                        CASE WHEN tax_categories_type = 'percentage'
                            THEN '%'
                            ELSE ''
                        END
                    ) AS formatted_rate
                ")
                ->orderBy('tax_categories_id')
                ->get();

            // Convert to array and format dates
            $taxCategoriesArray = [];
            foreach ($taxCategories as $category) {
                $categoryArray = (array)$category;

                // Format dates and combine into a single array
                $dateArray = [];

                if (!empty($categoryArray['tax_categories_from_date'])) {
                    $dateArray[] = date('Y-m-d', strtotime($categoryArray['tax_categories_from_date']));
                }

                if (!empty($categoryArray['tax_categories_to_date'])) {
                    $dateArray[] = date('Y-m-d', strtotime($categoryArray['tax_categories_to_date']));
                    // Remove the separate to_date field as it's now included in the array
                    unset($categoryArray['tax_categories_to_date']);
                }

                // Set the combined date array
                $categoryArray['tax_categories_from_date'] = $dateArray;

                $taxCategoriesArray[] = $categoryArray;
            }

            // Combine tax class and categories
            $result = (array)$taxClass;
            $result['taxclassruledetails'] = $taxCategoriesArray;
           $this->data = $result;
        } catch (\Exception $e) {
            return [
                'error' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine()
            ];
        }
    }
}
