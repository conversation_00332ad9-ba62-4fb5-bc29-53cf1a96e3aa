<?php

namespace App\Console\Commands\Actions\Account\Tax;

use Illuminate\Console\Command;
use App\Console\Commands\Action;

class ViewTaxRuleInfo extends Action
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'datasource:viewTaxRuleInfo {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'View Tax Rules';

    protected $formatter = [
        "id" => "",
        "tax_class_id" => "",
        "tax_class_name" => "",
        "tax_class_description" => "",
        "tax_categories_footer" => ""
    ];

    protected $formatterByKeys = ['id'];

    protected $mapper = [
        "id" => "chsone_tax_classes.tax_class_id"
    ];

    /**
     * Execute the console command.
     */
    public function apply()
    {
        $taxClassId = $this->input['id'];
        $arrayMaster = [];
        $taxClasses = $this->tenantDB()->table('chsone_tax_classes')
                        ->where('tax_class_id', $taxClassId)
                        ->select(
                            "tax_class_id AS id",
                            "tax_class_id",
                            "tax_class_name",
                            "tax_class_description",
                            "tax_categories_footer"
                        )
                        ->orderByDesc('tax_class_id')
                        ->first();
        $arrayMaster = $taxClasses;
        $result = (array)$arrayMaster;
        $this->data = $result;
        $this->meta['schema'] = $this->schema();
    }

    public function schema()
    {
        return $meta = [
            "table" => [
                "tableTitle" => "Tax Class Details",
                "actions" => [
                    [
                        "title" => "Back",
                        "icon" => "ri-arrow-go-back-line",
                        "color" => "primary",
                        "variant" => "contained",
                        "href" => "/admin/tax/viewTaxRule"
                    ]
                ],
                "fields" => [
                    "*"
                ],
                "columns" => [
                    [
                        "title" => "Class Name",
                        "key" => "tax_class_name",
                        "icon" => "ri:money-dollar-box-line",
                        "color" => "#ffa400"
                    ],
                    [
                        "title" => "Description",
                        "key" => "tax_class_description"
                    ],
                    [
                        "title" => "Tax Footer",
                        "key" => "tax_categories_footer"
                    ]
                ]
            ]
        ];
    }
}
