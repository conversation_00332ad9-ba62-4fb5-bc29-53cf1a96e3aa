<?php

namespace App\Console\Commands\Actions\Account\Tax;

use Illuminate\Console\Command;
use App\Console\Commands\Action;

class ViewTaxExemptionAction extends Action
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'datasource:viewTaxExemption {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'View Tax Exemption';

    protected $formatter = [
        "id" => "",
        "lower_bound" => "",
        "upper_bound" => "",
        "gender" => "",
        "description" => "",
        "effective_date" => "",
        "tax_code" => "",
        "rate_limit" => ""
    ];

    protected $formatterByKeys = ['id'];

    /**
     * Execute the console command.
     */
    public function apply()
    {
        $page = $this->input['page'] ?? 1;
        $per_page = !empty($this->input['per_page']) ? $this->input['per_page'] : ($this->hugeData ? 100000 : 10);
        $offset = ($page - 1) * $per_page;
        $currentFYDate = $this->getCurrentDate('database');
        $opEffectiveDate = isset($this->input['operation_effective_date'])
            ? $this->input['operation_effective_date'] : '>';
        $obj = $this->tenantDB()->table('tax_exemptions')
            ->selectRaw('
                        id,
                        lower_bound,
                        upper_bound,
                        gender,
                        description,
                        effective_date,
                        chsone_tax_categories.tax_code AS tax_code,
                        CONCAT(rate,
                                CASE 
                                    WHEN type = "percentage" THEN " %"
                                    WHEN type = "fixed" THEN ""
                                    ELSE ""
                                END
                        ) AS rate_limit
                    ')
            ->join(
                "chsone_tax_categories",
                "chsone_tax_categories.fk_tax_class_id",
                "=",
                "tax_exemptions.tax_class_id"
            )
            // ->whereRaw('effective_date'. ' '. $opEffectiveDate. ' '. '"'.$currentFYDate.'"')
            ->orderBy('effective_date');
            $count = $obj->distinct()->count('tax_exemptions.id');



        $obj = $obj->offset($offset);
        $obj = $obj->limit($per_page);

        $result = $obj->get();
        $this->data = $result;
        // $this->meta['schema'] = $this->schema();
        $this->meta['pagination']['total'] = $count;
    }
}
