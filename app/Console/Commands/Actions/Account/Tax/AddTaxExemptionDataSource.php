<?php

namespace App\Console\Commands\Actions\Account\Tax;

use Illuminate\Console\Command;
use App\Console\Commands\Action;
use Illuminate\Support\Facades\Log;

class AddTaxExemptionDataSource extends Action
{
    protected $signature = 'datasource:addTaxExemption {flowId} {parentId} {input}';

    protected $description = 'Add Tax Exemption Data Source';

    public function apply()
    {
        try {
            // Grab the ID (if any) from your input
            $taxExemptionId = $this->input['taxExemptionId'] ?? null;
            // Build the common data array
            $data = [
                'lower_bound'     => $this->input['lower_limit']   ?? '0.000',
                'upper_bound'     => $this->input['upper_limit']   ?? '0.000',
                'gender'          => $this->input['gender'],
                'description'     => $this->input['tax_exemption_description'],
                'rate'            => $this->input['rate'],
                'type'            => $this->input['type'],
                'effective_date'  => $this->input['effective_date'] ?? date('Y-m-d'),
                'tax_class_id'    => $this->input['tax_class'],
                'soc_id'          => $this->input['company_id'],
                'updated_by'      => $this->input['user_id']        ?? 0,
                'updated_date'    => date('Y-m-d H:i:s'),
            ];

            if ($taxExemptionId) {
                // Update existing record
                $obj = $this->tenantDB()
                    ->table('tax_exemptions')
                    ->where('id', $taxExemptionId)
                    ->update($data);
                $this->message = 'Tax Exemption updated successfully';
            } else {
                // Add creation timestamps and user
                $data['created_by']   = $this->input['user_id'] ?? 0;
                $data['created_date'] = date('Y-m-d H:i:s');

                // Insert new record
                $obj = $this->tenantDB()
                    ->table('tax_exemptions')
                    ->insert($data);
                
                $this->message = 'Tax Exemption added successfully';
            }
            if ($obj) {
                $this->status = 'success';
                
                $this->statusCode = 200;
            } else {
                $this->status = 'error';
                $this->message = 'Tax Exemption not added';
                $this->statusCode = 400;
            }
        } catch (\Illuminate\Database\QueryException $e) {
            Log::error('SQL Error: ' . $e->getMessage());
            Log::error('SQL Query: ' . $e->getSql());
            Log::error('SQL Bindings: ' . json_encode($e->getBindings()));
        
            $this->status = 'error';
            $this->message = 'Database error: ' . $e->getMessage();
            $this->statusCode = 500;
        }
        
    }
}
