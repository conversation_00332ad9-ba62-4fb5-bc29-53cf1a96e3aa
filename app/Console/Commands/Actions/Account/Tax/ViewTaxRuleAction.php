<?php

namespace App\Console\Commands\Actions\Account\Tax;

use Illuminate\Console\Command;
use App\Console\Commands\Action;

class ViewTaxRuleAction extends Action
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'datasource:viewTaxRule {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'View Tax Rules';

    protected $formatterByKeys = ['id'];

    protected $mapper = [
        "id" => "chsone_tax_categories.tax_categories_id"
    ];

    /**
     * Execute the console command.
     */
    public function apply()
    {

        $page = $this->input['page'] ?? 1;
        $per_page = !empty($this->input['per_page']) ? $this->input['per_page'] : ($this->hugeData ? 100000 : 10);
        $offset = ($page - 1) * $per_page;
        $taxClassId = $this->input['id'];
        $obj = $this->tenantDB()->table('chsone_tax_categories')
            ->where('fk_tax_class_id', $taxClassId)
            ->selectRaw("
                tax_categories_id AS id,
                fk_tax_class_id AS tax_class_id,
                tax_categories_name,
                tax_categories_amount,
                tax_categories_type,
                tax_categories_from_date AS from_date,
                tax_categories_to_date AS to_date,
                tax_code,
                status,
                CONCAT(tax_categories_amount, 
                    CASE WHEN tax_categories_type = 'percentage' THEN '%' ELSE '' END
                ) AS rate
            ")
            ->orderBy('tax_categories_id');
        $count = $obj->count();

        $obj = $obj->offset($offset);
        $obj = $obj->limit($per_page);

        $result = $obj->get();
        $this->data = $result;
        $this->meta['schema'] = $this->schema();
        $this->meta['pagination']['total'] = $count;
    }

    public function schema()
    {
        return $meta = [
            "table" => [
                "tableTitle" => "Tax Rules",
                "fields" => [
                    "*"
                ],
                "columns" => [
                    [
                        "title" => "Sr No.",
                        "key" => "id"
                    ],
                    [
                        "title" => "Rule Name",
                        "key" => "tax_categories_name",
                    ],
                    [
                        "title" => "Rate",
                        "key" => "rate"
                    ],
                    [
                        "title" => "From Date",
                        "key" => "from_date"
                    ],
                    [
                        "title" => "To Date",
                        "key" => "to_date"
                    ],
                    [
                        "title" => "Code ",
                        "key" => "tax_code"
                    ],
                    [
                        "title" => "Status",
                        "key" => "status",
                        "type" => "chip",
                        "options" => [
                            "1" => [
                                "title" => "Active",
                                "color" => "success"
                            ],
                            "0" => [
                                "title" => "Past",
                                "color" => "error"
                            ],
                        ]
                    ]
                ]
            ]
        ];
    }
}
