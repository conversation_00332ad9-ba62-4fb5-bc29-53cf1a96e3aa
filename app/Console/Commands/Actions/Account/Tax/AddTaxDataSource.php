<?php

namespace App\Console\Commands\Actions\Account\Tax;

use Illuminate\Console\Command;
use App\Console\Commands\Action;
use App\Models\Tenants\SocAccountFinancialYearMaster;
use App\Models\Tenants\IncomeInvoiceSetting;
use App\Models\Tenants\ChsoneGrpLedgerTree;
use App\Models\Tenants\ChsoneLedgerTransaction;
use Carbon\Carbon;
use App\Models\Tenants\ChsoneInvestmentsMaster;
use App\Models\Tenants\ChsoneAssetsCategory;
use App\Models\Tenants\ChsoneTaxCategory;
use App\Models\Tenants\ChsoneTaxClass;
use App\Models\Tenants\ChsoneVendorsMaster;
use App\Models\Tenants\TaxExemption;
use App\Models\Tenants\ChsoneBankRecoMaster;
use App\Models\Tenants\ChsoneAccountsMaster;
use App\Models\Tenants\ChsoneAsset;
use DateTime;

class AddTaxDataSource extends Action
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'datasource:AddTax {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Add Tax Data Source';

    /**
     * Execute the console command.
     */

    public function apply()
    {
        $postedValues = request()->post();
        $soc_id = $this->input['company_id'];
        $jsonRuleData = $postedValues['taxclassruledetails'];
        $arrRuleData = $postedValues['taxclassruledetails'];
        if (isset($postedValues['tax_class_id']) && $postedValues['tax_class_id'] != "" && $postedValues['tax_class_id'] != "0") {
            $objTaxClass = ChsoneTaxClass::where('soc_id', $soc_id)
                ->where('tax_class_id', $postedValues['tax_class_id'])
                ->first();
        } else {
            $objTaxClass = new ChsoneTaxClass();
            $objTaxClass->created_date = now()->toDateTimeString(); // or use Carbon\Carbon::now()->toDateTimeString() if Carbon is not imported
            $objTaxClass->created_by = $this->input['user_id'] ?? 0;
        }
        $objTaxClass->soc_id = $soc_id;
        $objTaxClass->tax_class_name = $postedValues['tax_class_name'];
        $objTaxClass->tax_class_description = $postedValues['tax_class_description'];
        $objTaxClass->tax_categories_footer = $postedValues['tax_categories_footer'];
        $objTaxClass->status = 1;
        $objTaxClass->updated_date = date("H:i:s");
        $objTaxClass->updated_by = $this->input['user_id'] ?? 0;
        $objTaxClass->save();
        $inttax_class_id = $objTaxClass->tax_class_id;

        if (!empty($arrRuleData) && !empty($inttax_class_id)) {
            $flagset = 0;
            foreach ($arrRuleData as $key => $arrRuleDetails) {
                    if (isset($arrRuleDetails['tax_categories_id']) && $arrRuleDetails['tax_categories_id'] != "" && $arrRuleDetails['tax_categories_id'] != "0") {
                        $objTaxCategories = ChsoneTaxCategory::where('soc_id', $soc_id)
                            ->where('tax_categories_id', $arrRuleDetails['tax_categories_id'])
                            ->first();
                            $this->message = 'Tax Details has been updated successfully';
                    } else {
                        $objTaxCategories = new ChsoneTaxCategory();
                        $objTaxCategories->created_date = now()->toDateTimeString(); // or use Carbon\Carbon::now()->toDateTimeString() if Carbon is not imported
                        $objTaxCategories->created_by = $this->input['user_id'] ?? 0;
                        $this->message = 'Tax Details has been added successfully';
                    }
                    $objTaxCategories->soc_id = $soc_id;
                    $objTaxCategories->fk_tax_class_id = $inttax_class_id;
                    $objTaxCategories->tax_categories_name = $arrRuleDetails['tax_categories_name'];
                    $objTaxCategories->tax_categories_amount = $arrRuleDetails['tax_categories_amount'];
                    $objTaxCategories->tax_categories_type = ($arrRuleDetails['tax_categories_type']) ? $arrRuleDetails['tax_categories_type'] : 'percentage';
                    $objTaxCategories->status = $arrRuleDetails['status'] ?? 1;
                    $objTaxCategories->tax_code = $arrRuleDetails['tax_categories_code'];
                    $objTaxCategories->tax_categories_from_date = $arrRuleDetails['tax_categories_from_date'][0];
                    $objTaxCategories->tax_categories_to_date = $arrRuleDetails['tax_categories_from_date'][1];
                    $objTaxCategories->updated_date = now()->toDateTimeString(); // or use Carbon\Carbon::now()->toDateTimeString() if Carbon is not imported
                    $objTaxCategories->updated_by = $this->input['user_id'] ?? 0;


                    if (!$objTaxCategories->save()) {
                        $finaldata['error'] = $objTaxCategories->getMessages();
                        $finaldata['result'] = false;
                        $flagset == 1;
                        return $finaldata;
                    }
            }
            if ($flagset == 0) {
                $finaldata['result'] = true;
            }
        } else {
            $finaldata['result'] = true;
        }
        $this->status = 'success';
        $this->statusCode = 200;
        
    }
}
