<?php

namespace App\Console\Commands\Actions\Account\Tax;

use App\Console\Commands\Action;
use Illuminate\Console\Command;

class TaxClassesAction extends Action
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'datasource:taxClasses {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Get Tax Classes';

    protected $mapper = [
        "id" => "chsone_tax_classes.tax_class_id",
    ];

    protected $formatterByKeys = ['id'];

    /**
     * Execute the console command.
     */
    public function apply()
    {
        $page = $this->input['page'] ?? 1;
        $per_page = !empty($this->input['per_page']) ? $this->input['per_page'] : ($this->hugeData ? 100000 : 10);
        $offset = ($page - 1) * $per_page;

        $obj = $this->tenantDB()->table('chsone_tax_classes')
            ->select(
                "tax_class_id AS id",
                "tax_class_name",
                "tax_class_description",
                "tax_categories_footer"
            );
        $count = $obj->count();

        $obj = $obj->offset($offset);
        $obj = $obj->limit($per_page);

        $result = $obj->get();

        // according to the tax class id need to fecth sum of tax_categories_amount and tax_categories_type from chsone_tax_categories and pass to the details to the result
        foreach($result as $item) {
            $taxClassId = $item->id;
            $taxCategories = $this->tenantDB()->table('chsone_tax_categories')
                ->where('fk_tax_class_id', $taxClassId)
                ->select(
                    "tax_categories_type"
                )
                ->selectRaw("SUM(tax_categories_amount) AS tax_categories_amount")
                ->groupBy('fk_tax_class_id')
                ->get();

            $item->tax_categories_amount = $taxCategories->sum('tax_categories_amount');
            $item->tax_categories_type = $taxCategories->first()->tax_categories_type ?? null;
        }

        $this->data = $result;
        $this->meta['pagination']['total'] = $count;
    }
}
