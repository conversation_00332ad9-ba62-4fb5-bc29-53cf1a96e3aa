<?php

namespace App\Console\Commands\Actions\Account;

use Illuminate\Console\Command;
use App\Console\Commands\Action;

class assetsListDataSource extends Action
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'datasource:assetsList {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    protected $formatterByKeys = ['id'];

    protected $mapper = [
        'id' => 'assets_id',
    ];


    /**
     * Execute the console command.
     */
    public function apply()
    {

        $page = $this->input['page'] ?? 1;
        $per_page = !empty($this->input['per_page']) ? $this->input['per_page'] : ($this->hugeData ? 100000 : 10);
        $offset = ($page - 1) * $per_page;

        $obj = $this->tenantDB()->table('chsone_assets as assets')
            ->select('assets_id as id', 'assets_name', 'assets_tag_number', 'assets_vendor_id', 'vendor_name', 'assets.assets_categories_id', 'assets_categories_name', 'assets_location', 'vendor_bill_type_purchase', 'assets_purchase_date', 'assets_cost', 'assets.status')
            ->leftJoin('chsone_vendors_master as vendor', 'vendor.vendor_id', '=', 'assets.assets_vendor_id')
            ->leftJoin('chsone_assets_categories as categories', 'categories.assets_categories_id', '=', 'assets.assets_categories_id');

        $count = $obj->count();

        $obj = $obj->offset($offset);
        $obj = $obj->limit($per_page);

        $obj=$obj->orderBy('assets_id', 'desc');

        $result = $obj->get();
        $this->data = $result;
        $this->meta['pagination']['total'] = $count;
    }
}
