<?php

namespace App\Console\Commands\Actions\Account;

use Illuminate\Console\Command;
use App\Console\Commands\Action;

class investmentsListDataSource extends Action
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'datasource:investmentsList {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Get Investments List Data Source';

    protected $formatter = [
        'id' => '',
        'account_number' => '',
        'type' => '',
        'bank_name' => '',
        'branch' => '',
        'bank_address' => '',
        'bank_city' => '',
        'bank_ifsc' => '',
        'start_date' => '',
        'maturity_date' => '',
        'status' => '',
        'ledger_account_id' => '',
    ];

    protected $formatterByKeys = ['id'];

    protected $mapper = [
        'id' => 'id',
    ];

    /**
     * Execute the console command.
     */
    public function apply()
    {
        $page = $this->input['page'] ?? 1;
        $per_page = !empty($this->input['per_page']) ? $this->input['per_page']: ($this->hugeData ? 100000 : 10);
        $offset = ($page - 1) * $per_page;

        $totalCount = $this->TenantDB()->table('chsone_investments_master as investments')
            ->count();

        $obj = $this->tenantDB()->table('chsone_investments_master')
        ->select('id','account_number','type','bank_name','branch','bank_address','bank_city','bank_ifsc','start_date','maturity_date','status','ledger_account_id');
        $result = $obj->get();
        $this->data = $this->format($result->toArray());
        $this->meta['pagination']['total'] = $totalCount;
    }
}
