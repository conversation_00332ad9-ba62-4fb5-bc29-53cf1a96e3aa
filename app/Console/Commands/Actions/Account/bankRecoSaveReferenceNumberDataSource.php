<?php

namespace App\Console\Commands\Actions\Account;

use Illuminate\Console\Command;
use App\Console\Commands\Action;
use App\Models\Tenants\ChsoneLedgerTransaction;

class bankRecoSaveReferenceNumberDataSource extends Action
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'datasource:bankRecoSaveReferenceNumber {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Bank Reconciliation Save Reference Number Data Source';

    /**
     * Execute the console command.
     */
    public function apply()
    {
        $soc_id = $this->input['company_id'];
        $requestData = $this->input;

        $txn_id = $requestData['txn_id'] ?? null;
        $ref_no = $requestData['ref_no'] ?? 0;

        $getReference = ChsoneLedgerTransaction::where('soc_id', $soc_id)
            ->where('txn_id', $txn_id)
            ->first();

        if ($getReference) {
            $getReference->payment_reference = !empty($ref_no) ? $ref_no : '';
            $updateRef = $getReference->save();

            if ($updateRef) {
                $this->message = "Reference number updated successfully.";
                $this->status = "success";
                $this->statusCode = 200;
            } else {
                $this->message = "Failed to update reference number.";
                $this->status = "error";
                $this->statusCode = 400;
            }
        } else {
            $this->message = "No transaction found with the given txn_id.";
            $this->status = "error";
            $this->statusCode = 400;
        }
    }
}
