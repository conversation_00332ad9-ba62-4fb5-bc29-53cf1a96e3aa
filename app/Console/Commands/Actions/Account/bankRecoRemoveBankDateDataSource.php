<?php

namespace App\Console\Commands\Actions\Account;

use Illuminate\Console\Command;
use App\Console\Commands\Action;
use App\Models\Tenants\ChsoneLedgerTransaction;

class bankRecoRemoveBankDateDataSource extends Action
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'datasource:bankRecoRemoveBankDate {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Bank Reconciliation Remove Bank Date Data Source';

    /**
     * Execute the console command.
     */
    public function apply()
    {
        $soc_id = $this->input['company_id'];
        $requestData = $this->input;

        $txn_id = $requestData['txn_id'] ?? null;

        $getReference = ChsoneLedgerTransaction::where('soc_id', $soc_id)
            ->where('txn_id', $txn_id)
            ->first();

        if ($getReference) {
            $getReference->value_date = '';
            $updateRef = $getReference->save();

            if ($updateRef) {
                $this->message = "Bank date removed successfully.";
                $this->status = "success";
                $this->statusCode = 200;
            } else {
                $this->message = "Failed to remove bank date.";
                $this->status = "error";
                $this->statusCode = 400;
            }
        } else {
            $this->message = "No transaction found with the given txn_id.";
            $this->status = "error";
            $this->statusCode = 400;
        }
    }
}
