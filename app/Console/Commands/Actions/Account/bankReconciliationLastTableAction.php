<?php

namespace App\Console\Commands\Actions\Account;

use Illuminate\Console\Command;
use App\Console\Commands\Action;
use App\Models\Tenants\ChsoneAccountsMaster;
use App\Models\Tenants\ChsoneGrpLedgerTree;
use App\Models\Tenants\ChsoneLedgerTransaction;
use Carbon\Carbon;

class bankReconciliationLastTableAction extends Action
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'datasource:bankReconciliationLastTable {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Bank Reconciliation';

    protected $formatterByKeys = ['id'];

    protected $mapper = [
        'id' => 'chsone_ledger_transactions.ledger_account_id',
    ];

    /**
     * Execute the console command.
     */

    public function apply()
    {
        // create a final_data array to store the final data
        $final_data = [];

        $soc_id = $this->input['company_id'];
        $requestData = $this->input;

        $seletedFinYear = $requestData['filters']['year'];
        $getFinYearSplit = explode('-', $seletedFinYear);

        $firstYear = $getFinYearSplit[0];
        $secondYear = $getFinYearSplit[1];

        /**************** Reconciled amount of current Month Start ******************/
        $month = $requestData['filters']['month'];
        $getMonthSplit = explode('-', $month);
        $getMonth = $getMonthSplit[0];
        $year = $getMonthSplit[1];
        $month = (int)$month; // Cast to integer

        $monthName = date('F', mktime(0, 0, 0, $month, 10)) . ' ' . $year;

        $bank_ledger_id = $requestData['filters']['bank_ledger_id'];

        $add_amount = number_format($requestData['filters']['bankClosingAmount'], 2, '.', '');

        $currentMonth = date('m');
        // $daysInMonth = cal_days_in_month(CAL_GREGORIAN, $month, $year);
        // use Carbon to get the last day of the month
        $daysInMonth = Carbon::createFromDate($year, $month)->daysInMonth;

        if ($getMonth == $currentMonth) {
            $end_date = date("Y-m-d", mktime(0, 0, 0, $month, date('d'), $year));
        } else {
            $end_date = date("Y-m-d", mktime(0, 0, 0, $month, $daysInMonth, $year));
        }

        $pre_date = date("Y-m-d", mktime(0, 0, 0, $month - 1, date("t", mktime(0, 0, 0, date("n") - 1)), $year));

        $start_date = date("Y-m-d", mktime(0, 0, 0, $month, 1, $year));

        $end_date = Date("Y-m-d", strtotime($monthName . " last day of this month"));

        // echo $end_date; exit;

        $translist = $this->getbankTransaction($bank_ledger_id, $end_date, $start_date,0,null,$soc_id);
        // dd($translist);
        $transaction_data = $translist;
        $reco_bal = $this->getReconciledbalance($bank_ledger_id, $end_date, $pre_date, $start_date,$soc_id);
        $reco_bal_data = $reco_bal;
        // echo '<pre>'; print_r($reco_bal_data); exit;

        $confirmedTranslist = $this->getConfirmedTransaction($bank_ledger_id, $end_date, $start_date,$soc_id);
        // echo count($transaction_data);
        // echo count($confirmedTranslist); exit;
        if (count($confirmedTranslist) >= 1) {
            $confirmReconciled = 'True';
        } else {
            $confirmReconciled = 'False';
        }

        $curr_debit_sum = $reco_bal_data['curr_debit_sum'];
        $curr_cerdit_sum = $reco_bal_data['curr_credit_sum'];
        $prev_debit_sum = $reco_bal_data['prev_debit_sum'];
        $prev_cerdit_sum = $reco_bal_data['prev_credit_sum'];
        // $reco_opning_bal = ($curr_debit_sum - $curr_cerdit_sum) + ($prev_debit_sum - $prev_cerdit_sum);

        /***************Reconciled amount of current Month End******************/

        /***************Reconciled amount of Previuos Month Start****************** */

        $first_date_current_month = date("Y-m-1", strtotime($end_date));
        $first_date_of_last_month = Date("Y-m-d", strtotime($monthName . " first day of last month"));
        // echo $last_date_last_month = date("Y-m-t",strtotime($pre_date));exit;
        $last_date_last_month = Date("Y-m-d", strtotime($monthName . " last day of last month"));
        // exit;
        
        $reconciled_bal_prev_month = $this->getReconciledbalanceOfPrevMonth($bank_ledger_id, $first_date_of_last_month, $last_date_last_month, $year,$soc_id);
        $last_curr_debit_sum = $reconciled_bal_prev_month['curr_debit_sum'];
        $last_curr_cerdit_sum = $reconciled_bal_prev_month['curr_credit_sum'];
        $last_prev_debit_sum = $reconciled_bal_prev_month['prev_debit_sum'];
        $last_prev_cerdit_sum = $reconciled_bal_prev_month['prev_credit_sum'];
        //        $prev_reco_opning_bal = ($last_curr_debit_sum - $last_curr_cerdit_sum) + ($last_prev_debit_sum - $last_prev_cerdit_sum);
        //*****************************Reconciled amount of Previuos Month End*************************************************/

        $lastMonthFirstDay = Date("Y-m-d", strtotime($monthName . " first day of last month"));
        $lastMonthEndDay = Date("Y-m-d", strtotime($monthName . " last day of last month"));

        $prevMonthConfirmedTransList = $this->getConfirmedTransaction($bank_ledger_id, $lastMonthEndDay, $lastMonthFirstDay, $soc_id);

        if (count($prevMonthConfirmedTransList) >= 1) {
            $isPrevMonthConfirmed = 1;
        } else {
            $isPrevMonthConfirmed = 0;
        }

        $closure_master_details = $this->getFyStartDate( array('soc_id' => $soc_id));
        $closing_details = json_decode(json_encode($closure_master_details), true);

        $arrClosingDetails = explode('-', $closing_details[0]['fy_start_date']);
        $firstFinancialYear = $arrClosingDetails[0];

        $arrCheckEntries = array('soc_id' => $soc_id, 'bank_ledger_id' => $bank_ledger_id, 'firstYear' => $firstYear, 'lastMonthFirstDay' => $lastMonthFirstDay, 'lastMonthEndDay' => $lastMonthEndDay, 'firstFinancialYear' => $firstFinancialYear);
        $arrOpeningEntries = array('soc_id' => $soc_id, 'bank_ledger_id' => $bank_ledger_id, 'currentYear' => $firstYear, 'lastMonthFirstDay' => $lastMonthFirstDay, 'lastMonthEndDay' => $lastMonthEndDay, 'firstFinancialYear' => $firstFinancialYear);
        // echo '<pre>'; print_r($arrOpeningEntries); exit;
        $yearsArr = array('soc_id' => $soc_id, 'firstYear' => $firstYear, 'secondYear' => $secondYear, 'firstFinancialYear' => $firstFinancialYear);
        $isFinYearClosed = $this->checkFinancialYearClosed( $yearsArr);

        $closingBalOfLastMonthLedger = $this->calculateLedgerAmount($arrCheckEntries);

        $openingBalOfYear = $this->getOpeningBalanceOfYear($arrOpeningEntries);

        $previousYearOpeningBal = $this->getPrevYearOpeningBalance($arrOpeningEntries);
        // echo '<pre>'; print_r($openingBalOfYear);
        // exit;
        $bank_accounts = $this->getBankledger('bank');

        // echo $openingBalOfYear['transaction_amount']; exit;
        $reco_opning_bal = ($curr_debit_sum - $curr_cerdit_sum) + ($prev_debit_sum - $prev_cerdit_sum) + $openingBalOfYear['transaction_amount'];
        // echo "d ".$reco_opning_bal." dd";
        // exit;

        $prev_reco_opning_bal = ($last_curr_debit_sum - $last_curr_cerdit_sum) + ($last_prev_debit_sum - $last_prev_cerdit_sum);

        //Ledger opening balance of previous year
        $ledger_bal_prev_month = $this->getLedgerBalanceOfPrevMonth($bank_ledger_id, $first_date_of_last_month, $last_date_last_month, $firstYear, $soc_id);
        $ledger_last_curr_debit_sum = $ledger_bal_prev_month['curr_debit_sum'];
        $ledger_last_curr_credit_sum = $ledger_bal_prev_month['curr_credit_sum'];
        $ledger_last_prev_debit_sum = $ledger_bal_prev_month['prev_debit_sum'];
        $ledger_last_prev_credit_sum = $ledger_bal_prev_month['prev_credit_sum'];

        $prev_ledger_bal = ($ledger_last_curr_debit_sum - $ledger_last_curr_credit_sum) + ($ledger_last_prev_debit_sum - $ledger_last_prev_credit_sum);

        //Closing balance of last month by ledger
        $closingBalOfLastMonth = $closingBalOfLastMonthLedger;
        // echo $openingBalOfYear['transaction_amount'];
        // echo $prev_reco_opning_bal; exit;
        
        //Reconciled opening balance
        if ($firstFinancialYear == $firstYear) {
            if ($getMonth == 4) {
                $prev_reco_opning_bal = $openingBalOfYear['transaction_amount'];
            } else {
                $prev_reco_opning_bal = $prev_reco_opning_bal;
            }
        } elseif ($firstFinancialYear != $firstYear && ($isFinYearClosed == '' || $isFinYearClosed == 0) && $getMonth == 4) {
            // echo "fldsjf";exit;
            $prev_reco_opning_bal = $prev_reco_opning_bal;
        } elseif ($firstFinancialYear != $firstYear && ($isFinYearClosed == '' || $isFinYearClosed == 0) && $getMonth != 4) {
            // echo "fldsccfjf";exit;
            $prev_reco_opning_bal = $prev_reco_opning_bal - $openingBalOfYear['transaction_amount'];
        } else {
            $prev_reco_opning_bal = $openingBalOfYear['transaction_amount'];
        }

        // Opening balance of year
        if ($firstFinancialYear == $firstYear) {
            $openingBalOfYear['transaction_amount'] = $openingBalOfYear['transaction_amount'];
        } elseif ($firstFinancialYear != $firstYear && ($isFinYearClosed == '' || $isFinYearClosed == 0)) {
            $openingBalOfYear['transaction_amount'] = $closingBalOfLastMonthLedger;
        } else {
            $openingBalOfYear['transaction_amount'] = $openingBalOfYear['transaction_amount'];
        }

        // fetch the bank ledger name from the bank ledger id from the chsone_grp_ledger_tree table
        $ledger_account_name = ChsoneGrpLedgerTree::where('ledger_account_id', $bank_ledger_id)->value('ledger_account_name');

        // $final_data['bank_accounts'] = $bank_accounts;
        $final_data['bank_ledger_id'] = $bank_ledger_id;
        $final_data['bank_ledger_name'] = $ledger_account_name;
        $final_data['closingBalOfLastMonth'] = $closingBalOfLastMonth;
        $final_data['bank_opening_balance'] = $reco_opning_bal;
        $final_data['prev_reco_opning_bal'] = $prev_reco_opning_bal;
        // $final_data['prev_reco_opning_bal_for_april'] = $prev_reco_opning_bal_for_april;
        $final_data['curr_debit_sum'] = $curr_debit_sum;
        $final_data['curr_cerdit_sum'] = $curr_cerdit_sum;
        $final_data['year'] = $year;
        $final_data['seletedFinYear'] = $seletedFinYear;
        $final_data['month'] = $monthName;
        // $final_data['bank_account_name'] = $bank_account_name;
        $final_data['confirmReconciled'] = $confirmReconciled;
        $final_data['openingBalOfYear'] = $openingBalOfYear['transaction_amount'];
        $final_data['firstFinancialYear'] = $firstFinancialYear;
        $final_data['isPrevMonthConfirmed'] = $isPrevMonthConfirmed;

        if(strpos($month, 'April '.$firstFinancialYear) !== false) {
            $final_data['reconciled_opening_balance_of'] = abs($openingBalOfYear['transaction_amount']) ?? 0.00;
        } else {
            $final_data['reconciled_opening_balance_of'] =  abs($prev_reco_opning_bal) ?? 0.00;
        }

        if(strpos($month, 'April') !== false) {
            $final_data['opening_balance_of'] = abs($openingBalOfYear['transaction_amount']) ?? 0.00;
        } else {
            $final_data['opening_balance_of'] =  abs($closingBalOfLastMonth) ?? 0.00;
        }

        $final_data['reconciled_receipts'] = $curr_debit_sum;
        $final_data['reconciled_payments'] = $curr_cerdit_sum;

        if(strpos($month, 'April') !== false) {
            // $diff_amount = $curr_debit_sum-$curr_cerdit_sum;
            $final_data['reconciled_closing_balance'] = $final_data['opening_balance_of'] + $final_data['curr_debit_sum'] - $final_data['curr_cerdit_sum'];
        } else {
            $final_data['reconciled_closing_balance'] = $final_data['reconciled_opening_balance_of'] + $final_data['curr_debit_sum'] - $final_data['curr_cerdit_sum'];
        }

        $final_data['closing_balance_as_per_bank_statement'] = 0.00;
        $final_data['amount_not_reflected_in_bank'] = $final_data['reconciled_closing_balance'] -  $final_data['closing_balance_as_per_bank_statement'];

        // if (isset($transaction_data) && $transaction_data != '') {
        //     // $final_data['transaction_data'] = $translist_final;
        //     $final_data['transaction_data'] = $transaction_data;
        //     $final_data['flag'] = 1;
        //     $final_data['add_amount'] = $add_amount;
        //     // create a foreach loop to check transaction_type = dr then set voucher_type = 'Receipt' else set voucher_type = 'Payment'
        //     foreach ($transaction_data as $key => $value) {
        //         if ($value->transaction_type == 'dr') {
        //             $final_data['transaction_data'][$key]->voucher_type = 'Receipt';
        //             $final_data['transaction_data'][$key]->deposit = $value->transaction_amount;
        //             $final_data['transaction_data'][$key]->withdrawal = '';
        //         } else {
        //             $final_data['transaction_data'][$key]->voucher_type = 'Payment';
        //             $final_data['transaction_data'][$key]->withdrawal = $value->transaction_amount;
        //             $final_data['transaction_data'][$key]->deposit = '';
        //         }
        //     }
        // }

        $this->data = $final_data;
        // add pagination according to the $final_data['transaction_data'] and then set to meta['pagination]['total'] = count($final_data['transaction_data'])
        // $this->meta['pagination']['total'] = count($final_data['transaction_data']);

    }
    

    public function getbankTransaction($bank_ledger_id, $end_date, $start_date, $customdatefilter = 0, $type = null, $soc_id = null)
    {
        $final_array = [];

        if (!empty($customdatefilter)) {
            $key1 = ($customdatefilter == 1) ? 'transaction_date' : 'value_date';
        }

        if (isset($type)) {
            if ($type == 2) { // Unreconciled
                $not_recon_translist = $this->tenantDB()->table('chsone_ledger_transactions')
                    ->where('soc_id', $soc_id)
                    ->where('ledger_account_id', $bank_ledger_id)
                    ->where($key1, '>=', $start_date)
                    ->where($key1, '<=', $end_date)
                    ->where('is_reconciled', 0)
                    ->where('is_opening_balance', 0)
                    ->where(function ($query) {
                        $query->where('is_cancelled', 0)
                            ->orWhereNull('is_cancelled');
                    })
                    ->orderBy($key1, 'asc')
                    ->get()
                    ->toArray();

                $final_array = array_merge($not_recon_translist);
            } elseif ($type == 1) { // Reconciled
                $key1 = !empty($customdatefilter) ? $key1 : 'value_date';
                $recon_bank_date_list = $this->tenantDB()->table('chsone_ledger_transactions')
                    ->where('soc_id', $soc_id)
                    ->where('ledger_account_id', $bank_ledger_id)
                    ->where($key1, '>=', $start_date)
                    ->where($key1, '<=', $end_date)
                    ->where('is_reconciled', 1)
                    ->where('is_opening_balance', 0)
                    ->where(function ($query) {
                        $query->where('is_cancelled', 0)
                            ->orWhereNull('is_cancelled');
                    })
                    ->orderBy($key1, 'asc')
                    ->get()
                    ->toArray();

                $final_array = $recon_bank_date_list;
            } else {
                $key1 = !empty($customdatefilter) ? $key1 : 'transaction_date';
                $key2 = !empty($customdatefilter) ? $key1 : 'value_date';

                $recon_bank_date_list = $this->tenantDB()->table('chsone_ledger_transactions')
                    ->where('soc_id', $soc_id)
                    ->where('ledger_account_id', $bank_ledger_id)
                    ->where($key2, '>=', $start_date)
                    ->where($key2, '<=', $end_date)
                    ->where('is_reconciled', 1)
                    ->where('is_opening_balance', 0)
                    ->where(function ($query) {
                        $query->where('is_cancelled', 0)
                            ->orWhereNull('is_cancelled');
                    })
                    ->orderBy($key2, 'asc')
                    ->get()
                    ->toArray();

                $not_recon_translist = $this->tenantDB()->table('chsone_ledger_transactions')
                    ->where('soc_id', $soc_id)
                    ->where('ledger_account_id', $bank_ledger_id)
                    ->where($key1, '>=', $start_date)
                    ->where($key1, '<=', $end_date)
                    ->where('is_reconciled', 0)
                    ->where('is_opening_balance', 0)
                    ->where(function ($query) {
                        $query->where('is_cancelled', 0)
                            ->orWhereNull('is_cancelled');
                    })
                    ->orderBy($key1, 'asc')
                    ->get()
                    ->toArray();

                $final_array = array_merge($recon_bank_date_list, $not_recon_translist);
            }
        } else {

            $prev_non_recon_list = $this->tenantDB()->table('chsone_ledger_transactions')
                ->where('soc_id', $soc_id)
                ->where('ledger_account_id', $bank_ledger_id)
                ->where('transaction_date', '<', $start_date)
                ->where('is_reconciled', 0)
                ->where('is_opening_balance', 0)
                ->where(function ($query) {
                    $query->where('is_cancelled', 0)
                        ->orWhereNull('is_cancelled');
                })
                ->orderBy('transaction_date', 'asc')
                ->get()
                ->toArray();

            $recon_bank_date_list = $this->tenantDB()->table('chsone_ledger_transactions')
                ->where('soc_id', $soc_id)
                ->where('ledger_account_id', $bank_ledger_id)
                ->where('value_date', '>=', $start_date)
                ->where('value_date', '<=', $end_date)
                ->where('is_reconciled', 1)
                ->where('is_opening_balance', 0)
                ->where(function ($query) {
                    $query->where('is_cancelled', 0)
                        ->orWhereNull('is_cancelled');
                })
                ->orderBy('transaction_date', 'asc')
                ->get()
                ->toArray();

            $not_recon_translist = $this->tenantDB()->table('chsone_ledger_transactions')
                ->where('soc_id', $soc_id)
                ->where('ledger_account_id', $bank_ledger_id)
                ->where('transaction_date', '>=', $start_date)
                ->where('transaction_date', '<=', $end_date)
                ->where('is_reconciled', 0)
                ->where('is_opening_balance', 0)
                ->where(function ($query) {
                    $query->where('is_cancelled', 0)
                        ->orWhereNull('is_cancelled');
                })
                ->orderBy('transaction_date', 'asc')
                ->get()
                ->toArray();

            $final_array = array_merge($recon_bank_date_list, $prev_non_recon_list, $not_recon_translist);
        }

        $ledger_names = $this->getCounterEntryLedgerName($soc_id, $final_array);

        foreach ($final_array as $k => &$item) {
            $item->from_account = $ledger_names[$k]['name'];
        }
        return $final_array;
    }

    public function getCounterEntryLedgerName($soc_id, $all_ledger = [])
    {
        $all_ledger = json_decode(json_encode($all_ledger), true);
        // dd($all_ledger);
        $ledger_array = [];

        foreach ($all_ledger as $t) {
            $ledger_account_name = '';

            if (empty($t['txn_from_id'])) {
                // Find the counter entry ledger name
                $counter_entry = ChsoneLedgerTransaction::where('soc_id', $soc_id)
                    ->where('txn_from_id', $t['txn_id'])
                    ->get()->toArray();

                if (count($counter_entry) > 0) {
                    $ledger_account_name = $counter_entry[0]['ledger_account_name'];

                    if (empty($counter_entry[0]['ledger_account_name'])) {
                        $counter_ledger_details = ChsoneGrpLedgerTree::where('ledger_account_id', $counter_entry[0]['ledger_account_id'])
                            ->get()->toArray();

                        if ($counter_ledger_details) {
                            $ledger_account_name = $counter_ledger_details[0]['ledger_account_name'];
                        }
                    }

                    $ledger_array[] = [
                        'name' => 'To ' . $ledger_account_name,
                        'ledger_id' => $counter_entry[0]['ledger_account_id'],
                    ];
                } else {
                    $ledger_array[] = [
                        'name' => 'For ' . $t['ledger_account_name'],
                        'ledger_id' => $t['ledger_account_id'],
                    ];
                }
            } else {
                // Find the entry ledger name for this counter entry
                $counter_entry = ChsoneLedgerTransaction::where('soc_id', $soc_id)
                    ->where('txn_id', $t['txn_from_id'])
                    ->get()->toArray();

                if (count($counter_entry) > 0) {
                    $ledger_account_name = $counter_entry[0]['ledger_account_name'];

                    if (empty($ledger_account_name)) {
                        $counter_ledger_details = ChsoneGrpLedgerTree::where('ledger_account_id', $counter_entry[0]['ledger_account_id'])
                            ->get()->toArray();

                        if ($counter_ledger_details) {
                            $ledger_account_name = $counter_ledger_details[0]['ledger_account_name'];
                        }
                    }

                    $ledger_array[] = [
                        'name' => 'By ' . $ledger_account_name,
                        'ledger_id' => $counter_entry[0]['ledger_account_id'],
                    ];
                } else {
                    $ledger_array[] = [
                        'name' => 'For ' . $t['ledger_account_name'],
                        'ledger_id' => $t['ledger_account_id'],
                    ];
                }
            }
        }
        return $ledger_array;
    }


    public function getReconciledbalance($bank_ledger_id, $date, $pre_date, $start_date,$soc_id)
    {
        // Fetch current reconciled balance
        $current_recon_balance = $this->tenantDB()->table('chsone_ledger_transactions as a')
            ->selectRaw("
                SUM(IF(a.transaction_type = 'dr', a.transaction_amount, 0)) AS curr_debit_sum,
                SUM(IF(a.transaction_type = 'cr', a.transaction_amount, 0)) AS curr_credit_sum
            ")
            ->where('a.soc_id', $soc_id)
            ->where('a.ledger_account_id', $bank_ledger_id)
            ->where('a.is_reconciled', 1)
            ->whereBetween('a.value_date', [$start_date, $date])
            ->where(function ($query) {
                $query->where('a.is_cancelled', 0)
                    ->orWhereNull('a.is_cancelled');
            })
            ->where('a.is_opening_balance', 0)
            ->first();

        // Fetch previous reconciled balance
        $prev_recon_balance = $this->tenantDB()->table('chsone_ledger_transactions as a')
            ->selectRaw("
                SUM(IF(a.transaction_type = 'dr', a.transaction_amount, 0)) AS prev_debit_sum,
                SUM(IF(a.transaction_type = 'cr', a.transaction_amount, 0)) AS prev_credit_sum
            ")
            ->where('a.soc_id', $soc_id)
            ->where('a.ledger_account_id', $bank_ledger_id)
            ->where('a.is_reconciled', 1)
            ->where('a.value_date', '<=', $pre_date)
            ->where(function ($query) {
                $query->where('a.is_cancelled', 0)
                    ->orWhereNull('a.is_cancelled');
            })
            ->where('a.is_opening_balance', 0)
            ->first();

        // Combine and return the results
        return array_merge((array)$prev_recon_balance, (array)$current_recon_balance);
    }


    public function getConfirmedTransaction($bank_ledger_id, $end_date, $start_date, $soc_id)
    {
        $confirm_recon_list = $this->tenantDB()->table('chsone_bank_reco_master')
            ->where('soc_id', $soc_id)
            ->where('ledger_account_id', $bank_ledger_id)
            ->whereBetween('value_date', [$start_date, $end_date])
            ->where('is_reconcile_confirmed', 1)
            ->orderBy('bank_date', 'asc')
            ->get()
            ->toArray();

        return $confirm_recon_list;
    }


    public function getReconciledbalanceOfPrevMonth($bank_ledger_id, $start_date_prev_month, $end_date_prev_month, $year,$soc_id)
    {
        // Current reconciled balance query
        $current_recon_balance = $this->tenantDB()->table('chsone_ledger_transactions')
            ->selectRaw("
                SUM(CASE WHEN transaction_type = 'dr' THEN transaction_amount ELSE 0 END) AS curr_debit_sum,
                SUM(CASE WHEN transaction_type = 'cr' THEN transaction_amount ELSE 0 END) AS curr_credit_sum
            ")
            ->where('soc_id', $soc_id)
            ->where('ledger_account_id', $bank_ledger_id)
            ->where('is_reconciled', 1)
            ->whereBetween('value_date', [$start_date_prev_month, $end_date_prev_month])
            ->where(function ($query) {
                $query->where('is_cancelled', 0)
                    ->orWhereNull('is_cancelled');
            })
            ->first();

        // Previous reconciled balance query
        $prev_recon_balance = $this->tenantDB()->table('chsone_ledger_transactions')
            ->selectRaw("
                SUM(CASE WHEN transaction_type = 'dr' THEN transaction_amount ELSE 0 END) AS prev_debit_sum,
                SUM(CASE WHEN transaction_type = 'cr' THEN transaction_amount ELSE 0 END) AS prev_credit_sum
            ")
            ->where('soc_id', $soc_id)
            ->where('ledger_account_id', $bank_ledger_id)
            ->where('is_reconciled', 1)
            ->where('value_date', '<', $start_date_prev_month)
            ->where(function ($query) {
                $query->where('is_cancelled', 0)
                    ->orWhereNull('is_cancelled');
            })
            ->first();

        // Combine results into a single array
        $result = array_merge(
            json_decode(json_encode($prev_recon_balance), true),
            json_decode(json_encode($current_recon_balance), true)
        );

        return $result;
    }

    
    public function checkFinancialYearClosed($data)
    {
        $socId = $data['soc_id'];
        $prevFirstYear = $data['firstYear'] - 1;
        $prevSecondYear = $data['secondYear'] - 1;

        // Query to check if the financial year is closed
        $result = $this->tenantDB()->table('soc_account_financial_year_master')
            ->where('fy_start_date', "{$prevFirstYear}-04-01")
            ->where('fy_end_date', "{$prevSecondYear}-03-31")
            ->where('soc_id', $socId)
            ->first();

        // return $result ? $result->closed : 0; // purposfully set to 0 as per the old society
        return 0; 
    }


    public function getFyStartDate($socId, $limit = null)
    {
        // Define the conditions and bindings for the query
        $condition = [
            ['soc_id', '=', $socId],
            ['confirmed', '=', 0],
        ];

        // Build the query using Laravel's Query Builder
        $query = $this->tenantDB()->table('soc_account_financial_year_master')
            ->where($condition)
            ->orderBy('account_closing_id', 'asc');

        // Check if a limit is set, if not, limit to 1 record
        if (is_null($limit)) {
            $query->limit(1);
        }

        // Execute the query and get the result
        return $query->get()->toArray();
    }


    public function calculateLedgerAmount(array $data)
    {
        $socId = $data['soc_id'];
        $bankLedgerId = $data['bank_ledger_id'];
        $firstYear = $data['firstYear'];
        $firstFinancialYear = $data['firstFinancialYear'];
        $lastMonthFirstDay = $data['lastMonthFirstDay'];
        $lastMonthEndDay = $data['lastMonthEndDay'];
        $firstYearStartDate = $firstYear . "-04-01";

        // Check if the year is reconciled in the MasterLedgerTxn table
        $isYearReconciled = $this->tenantDB()->table('chsone_bank_reco_master')
            ->where('soc_id', $socId)
            ->where('ledger_account_id', $bankLedgerId)
            ->where('year', $firstYear)
            ->where('month', 3) // Assuming March (month = 3)
            ->exists();

        // Check if the last month is March
        if (strpos($lastMonthFirstDay, "$firstYear-03-") !== false) {
            // Fetch the transaction date of the previous year opening balance
            $prevYearStartDate = $this->tenantDB()->table('chsone_ledger_transactions')
                ->select('transaction_date')
                ->where('ledger_account_id', $bankLedgerId)
                ->where('is_opening_balance', 1)
                ->where('transaction_date', '<', $firstYearStartDate)
                ->orderBy('transaction_date', 'desc')
                ->value('transaction_date');

            // Fetch the debit and credit sum for transactions from previous year's opening date to last month's end date
            $result = $this->tenantDB()->table('chsone_ledger_transactions')
                ->selectRaw("
                SUM(CASE WHEN transaction_type = 'dr' THEN transaction_amount ELSE 0 END) as debit,
                SUM(CASE WHEN transaction_type = 'cr' THEN transaction_amount ELSE 0 END) as credit
            ")
                ->where('ledger_account_id', $bankLedgerId)
                ->where('soc_id', $socId)
                ->whereBetween('transaction_date', [$prevYearStartDate, $lastMonthEndDay])
                ->where('is_cancelled', 0)
                ->first();
        } else {
            // Fetch the debit and credit sum for transactions for the current financial year
            $result = $this->tenantDB()->table('chsone_ledger_transactions')
                ->selectRaw("
                SUM(CASE WHEN transaction_type = 'dr' THEN transaction_amount ELSE 0 END) as debit,
                SUM(CASE WHEN transaction_type = 'cr' THEN transaction_amount ELSE 0 END) as credit
            ")
                ->where('ledger_account_id', $bankLedgerId)
                ->where('soc_id', $socId)
                ->whereBetween('transaction_date', [$firstYearStartDate, $lastMonthEndDay])
                ->where('is_cancelled', 0)
                ->first();
        }

        // Calculate the total amount
        if ($result) {
            $debitSum = $result->debit ?? 0;
            $creditSum = $result->credit ?? 0;
            $totalAmount = $creditSum - $debitSum;
        } else {
            $totalAmount = 0;
        }

        return $totalAmount;
    }


    public function getOpeningBalanceOfYear($data)
    {
        $socId = $data['soc_id'];
        $bankLedgerId = $data['bank_ledger_id'];
        $currentYear = $data['currentYear'];

        // Check if the year is reconciled
        $ifYearReconciled = $this->tenantDB()->table('chsone_bank_reco_master')
            ->where('soc_id', $socId)
            ->where('ledger_account_id', $bankLedgerId)
            ->where('year', $currentYear)
            ->where('month', '3')
            ->get();

        // Fetch the opening balance for the current financial year on April 1st
        $openingBalance = $this->tenantDB()->table('chsone_ledger_transactions')
            ->select('transaction_type', 'transaction_amount')
            ->where('ledger_account_id', $bankLedgerId)
            ->where('soc_id', $socId)
            ->where('transaction_date', "{$currentYear}-04-01")
            ->where('is_opening_balance', 1)
            ->where('is_reconciled', 1)
            ->where(function ($query) {
                $query->where('is_cancelled', 0)
                    ->orWhereNull('is_cancelled');
            })
            ->first();

        // If no opening balance is found, return an empty array
        if (!$openingBalance) {
            return ['transaction_amount' => 0];
        }

        // Adjust the amount based on the transaction type (debit/credit)
        $transactionAmount = $openingBalance->transaction_amount ?? 0;
        if ($openingBalance->transaction_type === 'cr') {
            $transactionAmount = -$transactionAmount;
        }

        return [
            'transaction_amount' => $transactionAmount,
        ];
    }


    public function getPrevYearOpeningBalance($data = array())
    {
        $soc_id = $data['soc_id'];
        $bank_ledger_id = $data['bank_ledger_id'];

        $lastMonthFirstDay = $data['lastMonthFirstDay'];
        $lastMonthEndDay = $data['lastMonthEndDay'];
        $currentYear = $data['currentYear'];
        $firstFinancialYear = $data['firstFinancialYear'];

        // Check if the year is reconciled
        $ifYearReconciled = $this->tenantDB()->table('chsone_bank_reco_master')
            ->where('soc_id', $soc_id)
            ->where('ledger_account_id', $bank_ledger_id)
            ->where('year', $currentYear)
            ->where('month', '3')
            ->get();

        // Query for opening balance
        $openingBalQry = $this->tenantDB()->table('chsone_ledger_transactions')
            ->select('*')
            ->where('ledger_account_id', $bank_ledger_id)
            ->where('soc_id', $soc_id)
            ->where('transaction_date', '<', "{$currentYear}-04-01")
            ->where('is_opening_balance', 1)
            // Uncomment the next line if you need to check for reconciled entries
            // ->where('is_reconciled', 1)
            ->where(function ($query) {
                $query->where('is_cancelled', 0)
                      ->orWhereNull('is_cancelled');
            })
            ->get();

        $getResult = $openingBalQry->toArray();

        if (!empty($getResult) && isset($getResult[0]->transaction_type)) {
            if ($getResult[0]->transaction_type == 'cr') {
                $getResult[0]->transaction_amount = $getResult[0]->transaction_amount * -1;
            } else {
                $getResult[0]->transaction_amount = $getResult[0]->transaction_amount;
            }
        }

        return $getResult;
    }

    
    public function getBankledger($context = 'bank', $soc_id = null)
    {
        if (empty($soc_id)) {
            $soc_id = $this->input['company_id'];
        }

        $bankledger = $this->tenantDB()->table('chsone_grp_ledger_tree')
            ->where('soc_id', $soc_id)
            ->where('context', $context)
            ->where('entity_type', 'ledger')
            ->get();

        if (!empty($bankledger)) {
            return $bankledger->toArray();
        } else {
            return false;
        }
    }


    public function getLedgerBalanceOfPrevMonth($bankLedgerId, $startDatePrevMonth, $endDatePrevMonth, $firstYear, $socId)
    {
        // Query for current month's balance in March of the given year
        $currentReconBalance = $this->tenantDB()->table('chsone_ledger_transactions')
            ->selectRaw("SUM(CASE WHEN transaction_type = 'dr' THEN transaction_amount ELSE 0 END) AS curr_debit_sum,
                    SUM(CASE WHEN transaction_type = 'cr' THEN transaction_amount ELSE 0 END) AS curr_credit_sum")
            ->where('soc_id', $socId)
            ->where('ledger_account_id', $bankLedgerId)
            ->whereBetween('transaction_date', ["$firstYear-03-01", "$firstYear-03-31"])
            ->where(function ($query) {
                $query->where('is_cancelled', 0)
                    ->orWhereNull('is_cancelled');
            })
            ->first();

        // Query for previous year's balance before March
        $prevReconBalance = $this->tenantDB()->table('chsone_ledger_transactions')
            ->selectRaw("SUM(CASE WHEN transaction_type = 'dr' THEN transaction_amount ELSE 0 END) AS prev_debit_sum,
                    SUM(CASE WHEN transaction_type = 'cr' THEN transaction_amount ELSE 0 END) AS prev_credit_sum")
            ->where('soc_id', $socId)
            ->where('ledger_account_id', $bankLedgerId)
            ->where('transaction_date', '<', "$firstYear-03-01")
            ->where(function ($query) {
                $query->where('is_cancelled', 0)
                    ->orWhereNull('is_cancelled');
            })
            ->first();

        // Merge the results and return
        return [
            'curr_debit_sum' => $currentReconBalance->curr_debit_sum ?? 0,
            'curr_credit_sum' => $currentReconBalance->curr_credit_sum ?? 0,
            'prev_debit_sum' => $prevReconBalance->prev_debit_sum ?? 0,
            'prev_credit_sum' => $prevReconBalance->prev_credit_sum ?? 0,
        ];
    }
}
