<?php

namespace App\Console\Commands\Actions\Account;

use Illuminate\Console\Command;
use App\Console\Commands\Action;

class fetchDataEditCashAccountDataSource extends Action
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'datasource:fetchDataEditCashAccount {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Fetch data for editing cash account';

    /**
     * Execute the console command.
     */
    public function apply()
    {
        $id = $this->input['id'];

        $obj = $this->tenantDB()->table('chsone_accounts_master as accounts')
        ->select('account_id','accounts.ledger_account_id','ledger_account_name','bank_name','accounts.status')
        ->leftJoin('chsone_grp_ledger_tree as ledger', 'accounts.ledger_account_id', '=', 'ledger.ledger_account_id')
        ->where('account_id', $id);
        $result = $obj->first();

        if(!$result) {
            $this->status = 'error';
            $this->statusCode = '404';
            $this->message = 'Data not found or invalid id';
        }

        $this->data = $result;
    }
}
