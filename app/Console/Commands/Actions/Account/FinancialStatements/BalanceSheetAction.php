<?php

namespace App\Console\Commands\Actions\Account\FinancialStatements;

use Illuminate\Console\Command;
use App\Console\Commands\Action;

class BalanceSheetAction extends Action
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'action:balanceSheet {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Balance Sheet T and Tree view';

    /**
     * Execute the console command.
     */
    public function apply()
    {
        $financial_year = $this->input['filters']['financial_year'] ?? date('Y').'-'.(date('Y')+1);
        $years = explode('-',$financial_year);
        $type = $this->input['filters']['periodType'] ?? 'yearly';
        $financial_month = $this->input['filters']['financial_month'] ?? '3';
        $month = explode('-', $financial_month)[0];
        $asondateval = $this->input['as_on_date'] ?? date('Y-m-d');
        $soc_id = $this->input['company_id'];
        $inputValidation = $this->validateInput(array(
            "financial_year"=>$financial_year,
            "type"=>$type,
            "month"=>$month,
            "asondateval"=>$asondateval,
            "soc_id"=>$soc_id
        ));

        if ($inputValidation["status"] == "error") {
            $this->message = $inputValidation["message"];
            $this->status = $inputValidation["status"];
            $this->statusCode = 400;
            return;
        }
        $prev_financial_year = ($years[0]-1).'_'.$years[0];
        $financial_year = $years[0].'_'.$years[1];

        $fy_start_date = $years[0].'-04-01';
        $fy_end_date = $years[1].'-03-31';
        $fy_end_month = "";

        if($type == "asonmonth" || $type == "monthly"){
            if($month > 3){
                $fy_end_date = $years[0].'-'.sprintf("%02d", $month).'-'.date('t', strtotime($years[0].'-'.sprintf("%02d", $month).'-01'));
            }else{
                $fy_end_date = $years[1].'-'.sprintf("%02d", $month).'-'.date('t', strtotime($years[1].'-'.sprintf("%02d", $month).'-01'));
            }
        }else if($type == "asondate"){
            $fy_end_date = $asondateval;
        }
        if($type == "monthly"){
            if($month > 3){
                $fy_end_month = $years[0].'-'.sprintf("%02d", $month);
            }else{
                $fy_end_month = $years[1].'-0'.$month;
            }
        }

        // check in soc_account_financial_year_master start date
        $start_date_check = $this->tenantDB()->select('SELECT fy_start_date FROM soc_account_financial_year_master LIMIT 1');
        if(!empty($start_date_check)){
            $start_date_check = $start_date_check[0]->fy_start_date;
            if($start_date_check == $fy_start_date){
                if($type == "monthly"){
                    if($month > 3){
                        $fy_start_date = $years[0].'-'.sprintf("%02d", $month).'-01';

                    }else{
                        $fy_start_date = $years[1].'-'.sprintf("%02d", $month).'-01';
                    }
                }
            }
        }
        $condition = 'WHERE TR.transaction_date BETWEEN "'.$fy_start_date.'" AND "'.$fy_end_date.'"';

        $query = "WITH RECURSIVE LedgerTree AS (
            SELECT ledger_account_id, ledger_account_name, nature_of_account, entity_type, behaviour, parent_id, 0 AS level, status, context, report_head
            FROM chsone_grp_ledger_tree
            WHERE parent_id = 0
      
            UNION ALL
      
            SELECT child.ledger_account_id, child.ledger_account_name, child.nature_of_account, child.entity_type, child.behaviour, child.parent_id, parent.level + 1, child.status, child.context, child.report_head
            FROM chsone_grp_ledger_tree AS child
            INNER JOIN LedgerTree AS parent ON child.parent_id = parent.ledger_account_id
        )
        SELECT *
        FROM LedgerTree
        WHERE report_head LIKE '%balance sheet%' 
        ORDER BY level, ledger_account_id;";

        $query = $this->filter($query);

        $ledgerData = $this->tenantDB()->select($query);

        // $transactionData = $this->tenantDB()->table('chsone_grp_ledger_tree as LR')
        // ->select(
        //     'LR.ledger_account_id',
        //     'LR.ledger_account_name',
        //     'LR.nature_of_account',
        //     'LR.entity_type',
        //     'LR.behaviour',
        //     'LR.parent_id'
        // )
        // ->selectRaw('SUM(IF(TR.is_opening_balance =  1, IF(TR.transaction_type = "dr", TR.transaction_amount, (-1 * TR.transaction_amount)), 0)) AS opening_balance')
        // ->selectRaw('SUM(IF(TR.transaction_type = "dr", TR.transaction_amount, 0)) - SUM(IF(TR.transaction_type = "cr", TR.transaction_amount, 0)) as transaction_amount')
        // ->leftJoin('chsone_ledger_transactions as TR', 'LR.ledger_account_id', '=', 'TR.ledger_account_id')
        // ->where('TR.ledger_account_id', 108)
        // ->whereBetween('TR.transaction_date', [$fy_start_date, $fy_end_date])
        // ->groupBy('LR.ledger_account_id')
        // ->get();

        // $query_string = " (SELECT *,CASE WHEN ledger_account_id IN ($condition) THEN @idlist := CONCAT(ledger_account_id) "
        // . "WHEN FIND_IN_SET(parent_id,@idlist) THEN @idlist := CONCAT(@idlist,',',ledger_account_id) END as checkId "
        // . "FROM chsone_grp_ledger_tree ORDER BY parent_id, ledger_account_name, ledger_account_id ASC) as T ";

        // $transactionData = json_decode($transactionData, true);

        // $transactionQuery = 'SELECT  
        // LR.ledger_account_id, LR.ledger_account_name, LR.nature_of_account, LR.entity_type, LR.behaviour, LR.parent_id,
        // SUM(IF(TR.is_opening_balance =  1, IF(TR.transaction_type = "dr", TR.transaction_amount, (-1 * TR.transaction_amount)), 0)) AS opening_balance,
        // SUM(IF(TR.transaction_type = "dr", TR.transaction_amount, 0)) - SUM(IF(TR.transaction_type = "cr", TR.transaction_amount, 0)) as transaction_amount
        // FROM chsone_grp_ledger_tree AS LR
        // LEFT  JOIN chsone_ledger_transactions AS TR on LR.ledger_account_id = TR.ledger_account_id
        // WHERE 
        //     TR.transaction_date BETWEEN "2020-04-01" AND "2021-03-31"
        // GROUP BY LR.ledger_account_id;';

        $transactionQuery = 'WITH LedgerSummary AS (
            SELECT  
                LR.ledger_account_id, LR.ledger_account_name, LR.nature_of_account, LR.entity_type, LR.behaviour, LR.parent_id,
                SUM(IF(TR.is_opening_balance = 1, IF(TR.transaction_type = "dr", TR.transaction_amount, -TR.transaction_amount), 0)) AS '.$prev_financial_year.',
                SUM(IF(TR.transaction_type = "dr", TR.transaction_amount, 0)) AS sum1,
                SUM(IF(TR.transaction_type = "cr", TR.transaction_amount, 0)) AS sum2
            FROM chsone_grp_ledger_tree AS LR
            LEFT JOIN chsone_ledger_transactions AS TR ON LR.ledger_account_id = TR.ledger_account_id
            ' . $condition . '
            GROUP BY LR.ledger_account_id
        ),
        MonthlySummary AS (
            SELECT  
                TR.ledger_account_id,
                SUM(IF(TR.transaction_type = "dr", TR.transaction_amount, 0)) AS transaction_total_debit,
                SUM(IF(TR.transaction_type = "cr", TR.transaction_amount, 0)) AS transaction_total_credit
            FROM chsone_ledger_transactions AS TR
            WHERE TR.transaction_date LIKE "'.$fy_end_month.'%"
            GROUP BY TR.ledger_account_id
        )
        
        SELECT  
            LS.ledger_account_id, LS.ledger_account_name, LS.nature_of_account, LS.entity_type, LS.behaviour, LS.parent_id, LS.sum1, LS.sum2, MS.transaction_total_debit, MS.transaction_total_credit, 
            CASE 
                WHEN LS.nature_of_account = "dr" THEN '.$prev_financial_year.'
                ELSE 
                    CASE 
                        WHEN ( LS.sum2 = 0 AND "' . $type . '" = "monthly" ) THEN LS.sum2
                        WHEN LS.sum2 = 0 THEN LS.'.$prev_financial_year.' + LS.sum1
                        ELSE -LS.'.$prev_financial_year.'
                    END
            END AS '.$prev_financial_year.',
            CASE 
                WHEN (LS.nature_of_account = "dr" AND "' . $type . '" != "monthly") THEN 
                    CASE 
                        WHEN LS.sum1 > LS.sum2 THEN LS.sum1 - LS.sum2
                        ELSE -1 * (LS.sum2 - LS.sum1)
                    END
                WHEN (LS.nature_of_account = "dr" AND "' . $type . '" = "monthly") THEN 
                CASE
                    WHEN ((MS.transaction_total_debit IS NOT NULL OR MS.transaction_total_credit IS NOT NULL) AND MS.transaction_total_debit != LS.sum1) THEN LS.'.$prev_financial_year.' + COALESCE(MS.transaction_total_debit, 0) - COALESCE(MS.transaction_total_credit, 0)
                    WHEN MS.transaction_total_debit IS NOT NULL OR MS.transaction_total_credit IS NOT NULL THEN COALESCE(MS.transaction_total_debit, 0) - COALESCE(MS.transaction_total_credit, 0)
                    ELSE LS.'.$prev_financial_year.'
                END
                WHEN LS.nature_of_account = "cr" THEN
                    CASE
                        WHEN ("' . $type . '" = "monthly" AND LS.sum1 = LS.sum2 AND MS.transaction_total_credit IS NULL) THEN LS.sum2 
                        WHEN ("' . $type . '" = "monthly" AND LS.sum1 = LS.sum2 AND MS.transaction_total_credit = 0) THEN MS.transaction_total_credit 
                        WHEN ("' . $type . '" = "monthly" AND LS.sum1 = LS.sum2 AND MS.transaction_total_credit IS NOT NULL) THEN MS.transaction_total_credit - LS.sum1
                        WHEN ("' . $type . '" = "monthly" AND LS.sum1 != LS.sum2 AND MS.transaction_total_credit IS NOT NULL) THEN LS.sum2 - LS.sum1
                        WHEN ("' . $type . '" = "monthly" AND LS.sum1 != LS.sum2 AND MS.transaction_total_credit IS NULL) THEN -1 * LS.'.$prev_financial_year.'

                        ELSE LS.sum2 - LS.sum1
                    END
                ELSE 
                    LS.sum2 - LS.sum1
            END AS '.$financial_year.'
        FROM LedgerSummary LS
        LEFT JOIN MonthlySummary MS ON LS.ledger_account_id = MS.ledger_account_id';

        $transactionData = $this->tenantDB()->select($transactionQuery);

        $transactionFormat = [
            'ledger_account_id' => [
                'ledger_account_id' => 'casting:int,0',
                'ledger_account_name' => '',
                'nature_of_account' => '',
                'entity_type' => '',
                'behaviour' => '',
                'context' => '',
                'status' => '',
                $financial_year => 'casting:double,0.0',
                'during_this_year' => 'casting:double,0.0',
                $prev_financial_year => 'casting:double,0.0'
            ]

        ];

        $transactionData = $this->smartFormat($transactionData, $transactionFormat);

        // build the tree stucture
        $tree = $this->buildTree($ledgerData, $transactionData, 0, $financial_year, $prev_financial_year);

        // Filter data to get only asset and liability
        $asset = collect($tree)->firstWhere('ledger_account_name', 'asset');
        $liability = collect($tree)->firstWhere('ledger_account_name', 'liability');

        // // Calculate the differences
        $sdoc = $asset[$prev_financial_year] - $liability[$prev_financial_year];
        $sdcc = $asset[$financial_year] - $liability[$financial_year];

        $lastId = end($tree)['id'];
        
        $profit = [
            'id'                    => ++$lastId,
            'ledger_account_name'   => 'Profit',
            'nature_of_account'     => 'cr',
            'context'               => 'profit',
            $prev_financial_year    => number_format($sdoc, 2, '.', ''),
            $financial_year         => number_format($sdcc, 2, '.', '')
        ];
        if($sdcc > $sdoc){
            $profit['during_this_year'] = '(+) '. number_format(($sdcc - $sdoc), 2, '.', '');
        }
        else{
            $profit['during_this_year'] = '(-) '. number_format((($sdcc - $sdoc) * -1), 2, '.', '');
        }

        // $total = [
        //     'id'                    => ++$lastId,
        //     'ledger_account_name'   => 'Total',
        //     'context'               => 'total',
        //     $prev_financial_year    => $asset[$prev_financial_year],
        //     'during_this_year'      => null,
        //     $financial_year         => $asset[$financial_year]
        // ];

        $tree[] = $profit;
        // $tree[] = $total;

        $table = [ 
            [$liability, $profit],
            [$asset]
        ];

        $this->data = $table;
    }

    public function buildTree($data, $transactionData, $parentId, $financial_year, $prev_financial_year)
    {
        $tree = [];

        foreach ($data as $row) {
            if ($row->parent_id == $parentId) {
                // Create an array for the current node
                $node = [
                    'id' => $row->ledger_account_id,
                    'ledger_account_name' => $row->ledger_account_name,
                    'nature_of_account' => $row->nature_of_account,
                    'entity_type' => $row->entity_type,
                    'behaviour' => $row->behaviour,
                    'context' => $row->context,
                    'status' => $row->status,
                   // 'rows' => []
                ];

                if(!empty($transactionData[$row->ledger_account_id])) {
                    
                    $node[$prev_financial_year] = number_format($transactionData[$row->ledger_account_id][$prev_financial_year], 2, '.', '');
                    $node['during_this_year'] = number_format($transactionData[$row->ledger_account_id][$financial_year] - $transactionData[$row->ledger_account_id][$prev_financial_year], 2, '.', '');

                    if($transactionData[$row->ledger_account_id][$financial_year] > $transactionData[$row->ledger_account_id][$prev_financial_year]){
                        $node['during_this_year'] = '(+) '. number_format($node['during_this_year'], 2, '.', '');
                    }
                    else{
                        $node['during_this_year'] = '(-) '. number_format(($node['during_this_year'] * -1), 2, '.', '');
                    }
                    $node[$financial_year] = number_format($transactionData[$row->ledger_account_id][$financial_year], 2, '.', '');
                } 
                else {
                    
                    $node[$prev_financial_year] = 0.0;
                    $node['during_this_year'] = null;
                    $node[$financial_year] = 0.0;
                }
                $node['rows'] = [];

                // Recursively build the child nodes
                $children = $this->buildTree($data, $transactionData, $row->ledger_account_id, $financial_year, $prev_financial_year);
                if (!empty($children)) {
                    $node[$prev_financial_year] = number_format(array_sum(array_column($children, $prev_financial_year)), 2, '.', '');
                    $node[$financial_year] = number_format(array_sum(array_column($children, $financial_year)), 2, '.', '');
                    $node['rows'] = $children;
                }

                // Add the node to the tree
                // if(isset($node[$prev_financial_year]) || isset($node[$financial_year])){
                //     if((int)$node[$prev_financial_year] != 0 || (int)$node[$financial_year] != 0){
                        $tree[] = $node;
                //     }
                // }
            }
        }

        return $tree;
    }
}