<?php

namespace App\Console\Commands\Actions\Account\FinancialStatements;

use Illuminate\Console\Command;
use App\Console\Commands\Action;

class CashflowAction extends Action
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'action:cashFlow {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Cash Flow';

    /**
     * Execute the console command.
     */
    public function apply()
    {
        $financial_year = $this->input['filters']['financial_year'] ?? date('Y').'-'.(date('Y')+1);
        $years = explode('-',$financial_year);
        $type = $this->input['filters']['periodType'] ?? 'yearly';
        $financial_month = $this->input['filters']['financial_month'] ?? '3';
        $month = explode('-', $financial_month)[0];
        $asondateval = $this->input['filters']['as_on_date'] ?? date('Y-m-d');
        $soc_id = $this->input['company_id'];
        $inputValidation = $this->validateInput(array(
            "financial_year"=>$financial_year,
            "type"=>$type,
            "month"=>$month,
            "asondateval"=>$asondateval,
            "soc_id"=>$soc_id
        ));

        if ($inputValidation["status"] == "error") {
            $this->message = $inputValidation["message"];
            $this->status = $inputValidation["status"];
            $this->statusCode = 400;
            return;
        }
        $fy_start_date = $years[0].'-04-01';
        $fy_end_date = $years[1].'-03-31';
        $fy_end_month = "";

        if($type == "asonmonth" || $type == "monthly"){
            if($month > 3){
                $fy_end_date = $years[0].'-'.sprintf("%02d", $month).'-'.date('t', strtotime($years[0].'-'.sprintf("%02d", $month).'-01'));
            }else{
                $fy_end_date = $years[1].'-'.sprintf("%02d", $month).'-'.date('t', strtotime($years[1].'-'.sprintf("%02d", $month).'-01'));
            }
        }else if($type == "asondate"){
            $fy_end_date = $asondateval;
        }
        if($type == "monthly"){
            if($month > 3){
                $fy_end_month = $years[0].'-'.sprintf("%02d", $month);
            }else{
                $fy_end_month = $years[1].'-0'.$month;
            }
        }

        // check in soc_account_financial_year_master start date
        $start_date_check = $this->tenantDB()->select('SELECT fy_start_date FROM soc_account_financial_year_master LIMIT 1');
        if(!empty($start_date_check)){
            $start_date_check = $start_date_check[0]->fy_start_date;
            if($start_date_check == $fy_start_date){
                if($type == "monthly"){
                    if($month > 3){
                        $fy_start_date = $years[0].'-'.sprintf("%02d", $month).'-01';

                    }else{
                        $fy_start_date = $years[1].'-'.sprintf("%02d", $month).'-01';
                    }
                }
            }
        }
        $condition = ' A.transaction_date BETWEEN "'.$fy_start_date.'" AND "'.$fy_end_date.'"';

        $query = "SELECT
                A.ledger_account_name,
                CASE
                    WHEN (A.voucher_type IN ('receipt') AND B.context NOT IN ('sundrydebtors') AND A.payment_reference != '') THEN 'Account Receivable'
                    WHEN (A.voucher_type IN ('receipt','payment', 'expense', 'purchase','journal') AND B.context NOT IN ('sundrydebtors')) THEN 'Others'
                    WHEN (A.voucher_type IN ('receipt') AND B.context = 'sundrydebtors') THEN 'Sundry Debtors'
                    WHEN A.voucher_type IN ('contra') THEN 'Contra'
                    ELSE A.voucher_type
                END AS voucher_type_group,
                SUM(IF(A.transaction_type = 'dr', A.transaction_amount, 0)) AS amount,
                B.context
            FROM
                chsone_ledger_transactions AS A
            INNER JOIN
                chsone_grp_ledger_tree AS B
                ON B.ledger_account_id = A.ledger_account_id
            WHERE
            ".$condition."
                AND A.is_opening_balance = 0
                AND A.is_cancelled = 0
                AND A.voucher_type != ''
                AND B.context IN ('cash', 'bank', 'vendor', 'sundrydebtors')
            GROUP BY
                A.ledger_account_id, voucher_type_group, A.ledger_account_name
            HAVING
                amount > 0";

        $query = $this->filter($query);
        $ledgerData = $this->tenantDB()->select($query);

        $InFlow = [];
        $OutFlow = [];
        $totalInflow = 0;
        $totalOutflow = 0;

        foreach ($ledgerData as $value) {
            $accountName = $value->ledger_account_name;
            $voucherType = $value->voucher_type_group;
            $amount = $value->amount;
            $context = $value->context;

            if ($context === 'bank' || $context === 'cash') {
                // Check if account already exists in InFlow
                $existingAccountKey = array_search($accountName, array_column($InFlow, 'ledger_account_name'));

                if ($existingAccountKey === false) {
                    // Add new account entry
                    $InFlow[] = [
                        'ledger_account_name' => $accountName,
                        'amount' => $amount,
                        'rows' => [
                            ['ledger_account_name' => $voucherType, 'amount' => $amount]
                        ]
                    ];
                } else {
                    // Update existing account entry
                    $InFlow[$existingAccountKey]['amount'] += $amount;

                    // Update rows for voucher type
                    $existingDetailKey = array_search($voucherType, array_column($InFlow[$existingAccountKey]['rows'], 'ledger_account_name'));

                    if ($existingDetailKey === false) {
                        // Add new voucher type entry
                        $InFlow[$existingAccountKey]['rows'][] = [
                            'ledger_account_name' => $voucherType,
                            'amount' => $amount
                        ];
                    } else {
                        // Update existing voucher type amount
                        $InFlow[$existingAccountKey]['rows'][$existingDetailKey]['amount'] += $amount;
                    }
                }

                // Increment total inflow
                $totalInflow += $amount;
            } else {
                // Check if account already exists in OutFlow
                $existingAccountKey = array_search($accountName, array_column($OutFlow, 'ledger_account_name'));

                if ($existingAccountKey === false) {
                    // Add new account entry
                    $OutFlow[] = [
                        'ledger_account_name' => $accountName,
                        'amount' => $amount
                    ];
                } else {
                    // Update existing account entry
                    $OutFlow[$existingAccountKey]['amount'] += $amount;
                }

                // Increment total outflow
                $totalOutflow += $amount;
            }
        }

        // Add 'Other Expenses' to OutFlow
        $otherExpenseQuery = "
            SELECT SUM(IF(A.transaction_type = 'dr', A.transaction_amount, 0)) AS amount
            FROM chsone_ledger_transactions AS A
            INNER JOIN chsone_grp_ledger_tree AS B
            ON B.ledger_account_id = A.ledger_account_id
            WHERE $condition AND B.report_head IN ('profit and loss', 'Profit & Loss') AND B.behaviour = 'expense'
        ";

        $OtherExpenses = $this->tenantDB()->select($otherExpenseQuery);
        if (!empty($OtherExpenses) && isset($OtherExpenses[0]->amount)) {
            $OutFlow[] = [
                'ledger_account_name' => 'Other Expenses',
                'amount' => $OtherExpenses[0]->amount
            ];
            $totalOutflow += $OtherExpenses[0]->amount;
        }

        // Calculate net inflow
        $netInflow = $totalInflow - $totalOutflow; // Ensure this line is before any usage of $netInflow

        // Add total_inflow as a ledger account
        // $InFlow[] = [
        //     'ledger_account_name' => 'total_inflow',
        //     'amount' => $totalInflow
        // ];

        // Add total_outflow and net_inflow as ledger accounts
        // $OutFlow[] = [
        //     'ledger_account_name' => 'total_outflow',
        //     'amount' => $totalOutflow
        // ];
        // $OutFlow[] = [
        //     'ledger_account_name' => 'net_inflow',
        //     'amount' => abs($netInflow)
        // ];

        // Prepare the final table structure
        $table = [

                $InFlow,


                 $OutFlow

        ];

        $this->data = $table;

    }
}
