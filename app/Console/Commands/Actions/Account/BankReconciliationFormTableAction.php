<?php

namespace App\Console\Commands\Actions\Account;

use App\Console\Commands\Action;
use Illuminate\Console\Command;

class BankReconciliationFormTableAction extends Action
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'datasource:bankReconciliationFormTable {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Bank Reconcilation Form Table list';

    protected $formatterByKeys = ['id'];

    protected $mapper = [
        'id' => 'chsone_ledger_transactions.ledger_account_id',
    ];

    /**
     * Execute the console command.
     */
    public function apply()
    {
        // $yearSelected = $this->input['yearSelected'] ?? null;

        // if($yearSelected){
        //     $this->data['reconcilled_amount'] = $this->getReconciledMonthsAction($this->input);
        // }

        // $this->data['reconcilled_month'] = $this->getReconciledMonthsAction($this->input);

        $this->data = $this->getReconciledMonthsAction($this->input);
    }

    public function getReconciledMonthsAction($data)
    {
        $fin_year = $data['yearSelected'] ?? null;
        $soc_id = $data['company_id'];

        $finalOutput = [];

        // Define month number to name mapping
        $monthMap = [
            4 => 'Apr', 5 => 'May', 6 => 'Jun', 7 => 'Jul',
            8 => 'Aug', 9 => 'Sep', 10 => 'Oct', 11 => 'Nov',
            12 => 'Dec', 1 => 'Jan', 2 => 'Feb', 3 => 'Mar'
        ];

        // Fetch bank accounts
        $bank_accounts = $this->getBankLedger('bank', $soc_id);

        foreach ($bank_accounts as $bank) {
            $bank = json_decode(json_encode($bank), true);
            $ledger_account_id = $bank['ledger_account_id'];

            // Initialize all months to false
            $monthsStatus = [];
            foreach ($monthMap as $num => $name) {
                $monthsStatus[$name] = false;
            }

            // Get reconciled months
            $reconciledMonths = $this->tenantDB()
                ->table('chsone_bank_reco_master')
                ->selectRaw('DISTINCT month')
                ->where('soc_id', $soc_id)
                ->where('financial_year', $fin_year)
                ->where('ledger_account_id', $ledger_account_id)
                ->pluck('month')
                ->toArray();

            // Set reconciled months to true
            foreach ($reconciledMonths as $monthNum) {
                if (isset($monthMap[$monthNum])) {
                    $monthsStatus[$monthMap[$monthNum]] = true;
                }
            }

            // Build final entry
            $entry = array_merge(['bank_account' => $bank['ledger_account_name']], $monthsStatus);
            $finalOutput[] = $entry;
        }

        return $finalOutput;
    }

    public function getBankLedger($context = 'bank', $soc_id = null)
    {
        // Fetch bank ledger using Eloquent with the given conditions
        $bankLedger = $this->tenantDB()->table('chsone_grp_ledger_tree')->where('soc_id', $soc_id)
            ->where('context', $context)
            ->where('entity_type', 'ledger')
            ->get();

        // Check if records were found
        if ($bankLedger->isNotEmpty()) {
            return $bankLedger->toArray();
        } else {
            return false;
        }
    }
}
