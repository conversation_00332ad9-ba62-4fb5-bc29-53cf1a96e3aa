<?php

namespace App\Console\Commands\Actions\Account;

use Illuminate\Console\Command;
use App\Console\Commands\Action;
use App\Models\Tenants\ChsoneAccountsMaster;
use App\Models\Tenants\ChsoneGrpLedgerTree;
use App\Models\Tenants\ChsoneLedgerTransaction;
use App\Models\Tenants\FsadminSocietyBankActivation;
use App\Models\Tenants\SocAccountFinancialYearMaster;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\DB;

class editCashAccountDataSource extends Action
{
    protected $constants;

    public function __construct() {
        parent::__construct();
        $this->constants = Config::get("constants");
    }

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'datasource:editCashAccount {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Edit a cash account';

    /**
     * Execute the console command.
     */
    public function apply()
    {
        $id = $this->input['id'];

        // check bankn_name is already exist or not in chsone_grp_ledger_tree table
        $check = $this->tenantDB()->table('chsone_grp_ledger_tree')
        ->where('ledger_account_name', $this->input['bank_name'])
        ->where('soc_id', $this->input['company_id'])
        ->first();

        if($check) {
            $this->status = 'error';
            $this->statusCode = '400';
            $this->message = 'Ledger account name already exist';
            return;
        }

        $obj = $this->tenantDB()->table('chsone_accounts_master as accounts')
        ->select('account_id','accounts.ledger_account_id','ledger_account_name','bank_name','accounts.status')
        ->leftJoin('chsone_grp_ledger_tree as ledger', 'accounts.ledger_account_id', '=', 'ledger.ledger_account_id')
        ->where('account_id', $id);
        $result = $obj->first();

        if(!$result) {
            $this->status = 'error';
            $this->statusCode = '404';
            $this->message = 'Data not found or invalid id';
        }

        $name = $this->input['bank_name'];
        $acc_type = "cash";
        $parent_group = $result->ledger_account_id;
        $opening_bal = 0;
        $acc_id = $result->account_id;

        $id = $this->manipulateAccount($name, $acc_type, $parent_group, "", "", "", $opening_bal, $acc_id);

        if ($id) {
            //$this->flashSession->success("Your information was stored correctly!");
            $successmsg = ($acc_id > 0) ? CASH_EDIT_SUCC : CASH_ADD_SUCC;
            $this->status = 'success';
            $this->statusCode = '200';
            $this->message = $successmsg;
        } else {
            $this->status = 'error';
            $this->statusCode = '400';
            $this->message = 'Error in saving data';
        }
    }

    public function manipulateAccount(
        /*01*/
        $name,
        /*02*/
        $acc_type,
        /*03*/
        $parent_group,
        /*04*/
        $acc_num = "",
        /*05*/
        $acc_addr = "",
        /*06*/
        $acc_city = "",
        /*07*/
        $opening_bal = '',
        /*08*/
        $acc_id = '',
        /*09*/
        $acc_ifsc = '',
        /*10*/
        $acc_default = 0,
        /*11*/
        $account_name = '',
        /*12*/
        $branch = '',
        /*13*/
        $is_reco = 0,
        /*14*/
        $acc_default_incidental = 0,
        /*15*/
        $acc_default_nonmember = 0
    ) {

        $soc_id = $this->input['company_id'];
        $is_benecode_generated = false;

        if ($acc_id) {
            $account = ChsoneAccountsMaster::where('account_id', $acc_id)->first();

            $ba = $this->getBankSettlementAccountDetail(array('soc_id' => $soc_id, 'bank_id' => $acc_id));
            if (!empty($ba['bene_code'])) {
                $is_benecode_generated = true;
            }

            $account->modified_on = date('Y-m-d h:i:s');
            if (!$is_benecode_generated) {
                $ledger_name = $name . (!empty($acc_num) ? '-' . $acc_num : '');
                $ledger_id = $this->manipulate($ledger_name, ENTITY_TYPE_LEDGER, "", $parent_group, $behaviour = "", '', $opening_bal, $account->ledger_account_id, '', '', '', '', '', $is_reco);
                $dup_check = substr($ledger_id, 0, 3);

                if (!$ledger_id || $dup_check == "DUP") {
                    return false;
                } else {
                    $account->ledger_account_id = $ledger_id;
                }
            }
        } else {
            $account = new ChsoneAccountsMaster();
            $ledger_name = $name . (!empty($acc_num) ? '-' . $acc_num : '');
            $ledger_id = $this->manipulate($ledger_name, ENTITY_TYPE_LEDGER, "", $parent_group, $behaviour = "", $ledg_id = '', $opening_bal, '', '', '', '', '', '', $is_reco);
            $dup_check = substr($ledger_id, 0, 3);
            if (!$ledger_id || $dup_check == "DUP") {
                return false;
            } else {
                $account->ledger_account_id = $ledger_id;
            }
        }
        
        $account->added_on = date('Y-m-d h:i:s');
        $account->created_by = $this->input['user_id'] ?? 0;

        // Check if the account is default
        if ($acc_default) {
            $acc_id = ($acc_id) ? $acc_id : 0;
            // Count accounts with the given conditions
            $count = ChsoneAccountsMaster::where('soc_id', $soc_id)
            ->where('default_account', 1)
            ->where('account_id', '<>', $acc_id)
            ->count();

            if ($count) {
                // Find the first account that meets the conditions
                $other_accounts = ChsoneAccountsMaster::where('soc_id', $soc_id)
                ->where('default_account', 1)
                ->where('account_id', '<>', $acc_id)
                ->first();

                if ($other_accounts) {
                    // Update the default_account field and save the record
                    $other_accounts->default_account = 0;
                    $other_accounts->save();
                }
            }
        }

        // Check if the account is default for incidental
        if ($acc_default_incidental) {
            $acc_id = ($acc_id) ? $acc_id : 0;
            // Count accounts with the given conditions
            $count = ChsoneAccountsMaster::where('soc_id', $soc_id)
            ->where('default_account', 1)
            ->where('account_id', '<>', $acc_id)
            ->count();

            if ($count) {
                // Find the first account that meets the conditions
                $other_accounts = ChsoneAccountsMaster::where('soc_id', $soc_id)
                ->where('default_account', 1)
                ->where('account_id', '<>', $acc_id)
                ->first();
                if ($other_accounts) {
                    // Update the default_bank_for_incidental field and save the record
                    $other_accounts->default_bank_for_incidental = 0;
                    $other_accounts->save();
                }
            }
        }

        // Check if the account is default for non-member
        if ($acc_default_nonmember) {
            $acc_id = ($acc_id) ? $acc_id : 0;
            // Count accounts with the given conditions
            $count = ChsoneAccountsMaster::where('soc_id', $soc_id)
            ->where('default_account', 1)
            ->where('account_id', '<>', $acc_id)
            ->count();

            if ($count) {
                // Find the first account that meets the conditions
                $other_accounts = ChsoneAccountsMaster::where('soc_id', $soc_id)
                ->where('default_account', 1)
                ->where('account_id', '<>', $acc_id)
                ->first();
                if ($other_accounts) {
                    // Update the default_bank_for_nonmember field and save the record
                    $other_accounts->default_bank_for_nonmember = 0;
                    $other_accounts->save();
                }
            }
        }

        $account->group_id = $parent_group;

        if (!$is_benecode_generated) {
            $account->bank_name       = $name;
            $account->account_number  = $acc_num;
            $account->bank_address    = $acc_addr;
            $account->bank_city       = $acc_city;
            $account->bank_ifsc       = $acc_ifsc;
        }

        $account->default_account = $acc_default;
        $account->default_bank_for_incidental = $acc_default_incidental;
        $account->default_bank_for_nonmember = $acc_default_nonmember;
        $account->status          = ACTIVE;
        $account->soc_id          = $soc_id;
        $account->account_name    = $account_name;
        $account->branch          = $branch;
        if ($account->save()) {
            $res = $this->update_context_ref($account->ledger_account_id, $account->account_id);
            if ($res) {
                return $account->ledger_account_id;
            } else {
                return false;
            }
        } else {
            return false;
        }
    }

    public function getBankSettlementAccountDetail($data = [])
    {
        // Start building the query
        $query = FsadminSocietyBankActivation::query();

        // Add conditions dynamically
        if (!empty($data['soc_id'])) {
            $query->where('soc_id', $data['soc_id']);
        }

        if (!empty($data['bank_id'])) {
            $query->where('bank_id', $data['bank_id']);
        }

        if (!empty($data['pg_code'])) {
            $query->where('pg_code', $data['pg_code']);
        }

        if (!empty($data['status'])) {
            $query->where('status', $data['status']);
        }

        if (isset($data['bene_code_not_empty']) && !empty($data['bene_code_not_empty'])) {
            $query->where('bene_code', '!=', '');
        }

        // Execute the query and get the first record
        $objAccountDetail = $query->first();

        // Convert the result to an array or return an empty array
        $arrAccountDetail = $objAccountDetail ? $objAccountDetail->toArray() : [];

        return $arrAccountDetail;
    }

    public function manipulate($name, $entity_type = ENTITY_TYPE_GROUP, $grp_ledg_id = "", $parent_group = 0, $behaviour = "", $ledg_id = '', $opening_balance = 0, $update_led_id = '', $ledger_start_date = '', $ledger_type = '', $context = '', $txn_id = 0, $soc_id = 0, $is_reco = 0)
    {
        $config = Config::get("constants");
        if(empty($soc_id)){
            $soc_id = $this->input['company_id'];
        }
        $dup = $this->_checkGroupNameDuplication($parent_group, $name, $ledg_id, $update_led_id);
        
        $arrFYDetail = $this->getCurrentFinancialYear(array('soc_id' => $soc_id));
        $arrFYDetail = json_decode(json_encode($arrFYDetail), true);
        $ledger_start_date = $arrFYDetail['fy_start_date'];
        if ($dup == 0) {
            $behaviour = trim(strtolower($behaviour));

            $grp_ledg_tree = new ChsoneGrpLedgerTree();

            if ($ledg_id) {
                $grp_ledg_tree = $this->getLedger($ledg_id);
            }

            $grp_ledg_tree->entity_type = $entity_type;
            $grp_ledg_tree->soc_id = $soc_id;
            $grp_ledg_tree->ledger_account_name = $name;
            $grp_ledg_tree->ledger_start_date = $ledger_start_date;
            $grp_ledg_tree->context_ref_id = 0;

            if (!empty($ledger_type) && $ledger_type != "NULL" && $ledger_type != '') {
                $grp_ledg_tree->operating_type = $ledger_type;
            } else {
                $grp_ledg_tree->operating_type = '';
            }

            if (!empty($grp_ledg_id)) {
                $grp_ledg_tree->ledger_account_id = $grp_ledg_id;
            }

            if ($update_led_id != '') {
                $grp_ledg_tree->ledger_account_id = $update_led_id;
            }
            if (!empty($parent_group)) {
                $grp_ledg_tree->parent_id = $parent_group;
                $ledger_props = $this->_getLedgerProps($parent_group);
            } else {
                $grp_ledg_tree->parent_id = 0;
            }

            if (!empty($behaviour)) {
                $grp_ledg_tree->behaviour = $behaviour;
                $grp_ledg_tree->nature_of_account = $config->nature_account->$behaviour;
                $grp_ledg_tree->report_head = $config->report_head->$behaviour;
            } else {
                $grp_ledg_tree->behaviour = $ledger_props["behaviour"];
                $grp_ledg_tree->nature_of_account = $ledger_props["nature_of_account"];
                $grp_ledg_tree->report_head = $ledger_props["report_head"];
            }

            if (!empty($context) && $context != '') {
                $grp_ledg_tree->context = $context;
            } else {
                $grp_ledg_tree->context = $ledger_props["context"];
            }
            $grp_ledg_tree->defined_by = "user";
            $grp_ledg_tree->status = 1;
            $grp_ledg_tree->added_on = date("Y-m-d H:i:s");
            $grp_ledg_tree->created_by = $this->input['user_id'] ?? 0;

            unset($grp_ledg_tree->ledger_account_id);

            if ($grp_ledg_tree->save()) {
                //Opening balance should not go in case on Income Expense ledger
                if (!in_array(strtolower($grp_ledg_tree->behaviour), array("income", "expense"))) {

                    $txn_date = $ledger_start_date;
                    $narration = 'entry for opening balance';
                    $txn_id = $this->addTxn($grp_ledg_tree->ledger_account_id, $opening_balance, $narration, $txn_date, "", $grp_ledg_tree->nature_of_account, "", "", "", "", $name, $is_opning = 1, $is_reco);
                }

                return $grp_ledg_tree->ledger_account_id;
            } else {
                
                return false;
            }
        } else {
            return "DUP" . $dup;
        }
    }

    private function _checkGroupNameDuplication($parent, $name, $ledg_id = "", $update_led_id = "")
    {
        $soc_id = $this->input['company_id'];

        $bind[1] = strtolower(trim($name));
        $ledgers_count = 0;
        if ($ledg_id) {
            $ledgers_count = ChsoneGrpLedgerTree::where("soc_id", $soc_id)->where('ledger_account_name', strtolower(trim($name)))->where('ledger_account_id', '!=', $ledg_id)->count();
        }
        if ($update_led_id) {
            $ledgers_count = ChsoneGrpLedgerTree::where("soc_id", $soc_id)->where('ledger_account_name', strtolower(trim($name)))->where('ledger_account_id', '!=', $update_led_id)->count();
        }
        
        return $ledgers_count;
    }

    public function getCurrentFinancialYear($data=array())
    {
        $arrFYDetail = array();
        $arrFYDetail = SocAccountFinancialYearMaster::where("soc_id", $data['soc_id'])->where("confirmed", 0)->orderBy("account_closing_id", "asc")->first();
        
        if(!empty($arrFYDetail))
        {
            $arrFYDetail = $arrFYDetail->toArray();
        }
        return $arrFYDetail;
    }

    public function getLedger($ledger_acct_id, $return_object = 1)
    {
        //fetch ledger details from chsone_grp_ledger_tree whose ledger_account_id = $ledger_acct_id
        $ledger = ChsoneGrpLedgerTree::where("ledger_account_id", $ledger_acct_id)->first();

        if ($ledger) {
            if ($return_object) {
                return $ledger;
            } else {
                return $ledger->toArray();
            }
        } else {
            return false;
        }
    }

    public function _getLedgerProps($parent_group)
    {
        $array = array();

        if (is_numeric($parent_group)) {
            $ledger_props = ChsoneGrpLedgerTree::select('ledger_account_name', 'nature_of_account', 'behaviour', 'report_head', 'context')
            ->where('ledger_account_id', $parent_group)
            ->first();
        }

        $array = $ledger_props;
        return $array;
    }

    public function addTxn($ledger_id, $txn_amt, $narration, $txn_date, $voucher_type = VOUCHER_JOURNAL, $txn_type = "", $from_txn_id = "", $pay_mode = "", $pay_ref = "", $opening_balance = "", $ledger_name = "", $is_opning = 1, $is_reco = 0)
    {
        return $this->_addTransaction($is_reco, $ledger_id, $txn_amt, $narration, $txn_date, $voucher_type, $txn_type, $from_txn_id, $pay_mode, $pay_ref, $ledger_name, $is_opning = 1);
    }

    private function _addTransaction($is_reco = 0, $ledger_id, $txn_amt, $narration, $txn_date, $voucher_type = VOUCHER_JOURNAL, $txn_type = "", $from_txn_id = "", $pay_mode = "", $pay_ref = "", $name = "", $is_opning = "", $other_recp_ref = "", $soc_id = "", $created_by = "", $voucher_reference_id = "", $voucher_reference_number = "", $is_cancelled = 0)
    {
        $txn = new ChsoneLedgerTransaction();
        if (empty($soc_id)) {
            $soc_id = $this->input['company_id'];
        }

        if (!empty($txn_type) || $txn_type != "") {
            $type_txn = $txn_type;
        } else {
            if ($from_txn_id) {
                $mode     = "to";
                $type_txn = "cr";
            } else {
                $mode     = "from";
                $type_txn = "dr";
            }
        }

        //echo "s_opning".$is_opning;exit;
        if ($is_opning == 1) {

            $txn_entrt = ChsoneLedgerTransaction::where('soc_id', $soc_id)
            ->where('is_opening_balance', 1)
            ->where('ledger_account_id', $ledger_id)
            ->where('transaction_date', $txn_date)
            ->first();

            $txn->value_date = $txn_date;
            $txn->txn_id = (isset($txn_entrt->txn_id)  && !empty($txn_entrt->txn_id)) ? $txn_entrt->txn_id : '';
            if (!empty($txn->txn_id)) {
                $txn = ChsoneLedgerTransaction::findFirst("txn_id = " . $txn->txn_id);
            }
        }

        $txn->soc_id = $soc_id;
        $txn->transaction_date = $txn_date;
        $txn->ledger_account_id = $ledger_id;
        $txn->ledger_account_name = $name;
        $txn->voucher_type = $voucher_type;
        $txn->transaction_type = $type_txn; //$ledger_name_obj->nature_of_account;//$txn_type;
        $txn->payment_mode = $pay_mode;
        $txn->payment_reference = $pay_ref;
        $txn->transaction_amount = (is_null($txn_amt)) ? $txn->transaction_amount : $txn_amt;
        $txn->other_reference_id = $other_recp_ref;
        $txn->txn_from_id = $from_txn_id;
        $txn->memo_desc = $narration;
        $txn->is_opening_balance = $is_opning;
        $txn->is_reconciled = $is_reco;
        /*Added New Column in transaction*/
        $txn->voucher_reference_number = $voucher_reference_number;
        $txn->voucher_reference_id = $voucher_reference_id;
        $txn->is_cancelled = $is_cancelled;
        /*End New Column in transaction*/
        $txn->created_by = $this->input['user_id'] ?? 0;
        if (isset($created_by) && !empty($created_by)) {
            $txn->created_by = $created_by;
        }
        $txn->added_on = date("Y-m-d H:i:s");

        if ($txn->save()) {
            return $txn->txn_id;
        } else {
            print_r($txn->getMessages);
        }
        //echo "<pre>";print_r($a->getMessages);exit;
        return false;
    }

    public function update_context_ref($led_id, $context_ref_id)
    {
        if ($led_id != '' && $context_ref_id != '') {

            // Extract numeric part of the ledger ID
            $ledger_id = preg_replace("/[^0-9]/", "", $led_id);
            $grp_ledg_tree = $this->getLedger($ledger_id);
            $grp_ledg_tree->context_ref_id = $context_ref_id;

            if ($grp_ledg_tree->save()) {
                return true;
            } else {
                return false;
            }
        }
    }
}
