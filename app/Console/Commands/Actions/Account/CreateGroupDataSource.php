<?php

namespace App\Console\Commands\Actions\Account;

use Illuminate\Console\Command;
use App\Console\Commands\Action;
use App\Models\Tenants\ChsoneGrpLedgerTree;
use App\Models\Tenants\ChsoneLedgerTransaction;
use App\Models\Tenants\IncomeInvoiceSetting;
use App\Models\Tenants\SocAccountFinancialYearMaster;
use Illuminate\Support\Facades\Config;

class CreateGroupDataSource extends Action
{
    protected $constants;

    public function __construct()
    {
        parent::__construct();
        $this->constants = Config::get("constants");
    }
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'datasource:CreateGroup {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Create Group Data Source';

    protected $formatter = [];

    protected $formatterKey = 'id';

    protected $mapper = [];

    /**
     * Execute the console command.
     */
    public function apply()
    {
        $soc_id = $this->input['company_id'] ?? '';
        $user_id = $this->input['user_id'] ?? '';
        $mode = $this->input['mode'] ?? 'add';
        $behaviour = $this->input['behaviour'] ?? '';
        $nature = $this->input['nature'] ?? 'dr';
        $entity_type = $this->input['entity_type'] ?? 'group';
        $ledger_account_name = $this->input['ledger_account_name'] ?? '';
        $opening_balance = $this->input['opening_balance'] ?? '';
        $ledger_type = $this->input['ledger_type'] ?? '';
        $ledg_acc_id = $this->input["ledger_account_id"] ?? '';
        $parent = $this->input["lstParentAccount"] = $this->input["lstParentAccount"] ?? $this->input["id"] ?? '';

        $ledgerDetails = $this->tenantDB()->table('soc_account_financial_year_master')
            ->select('account_closing_id as id', 'fy_start_date', 'fy_end_date', 'closed')
            ->orderBy('account_closing_id', 'asc')
            ->get();

        $arrFYDetail = SocAccountFinancialYearMaster::where('soc_id', $soc_id)
            ->where('confirmed', 0)
            ->orderBy('account_closing_id', 'desc')
            ->first();

        $transaction_date = $arrFYDetail->fy_start_date->timezone('Asia/Kolkata')->format('Y-m-d');
        $addLedger = new ChsoneGrpLedgerTree();

        $ledger_start_date = $ledgerDetails['fy_start_date'] ?? '';
        $parentLedger = $addLedger->_getLedgerProps($this->input["lstParentAccount"] ?? '');

        if (strtolower($parentLedger['ledger_account_name']) == 'bank' && empty($this->input['ledger_account_id'])) {
            $this->status = 'error';
            $this->message = 'Please add bank ledgers from Bank Acounts Form.';
            $this->statusCode = 400;
            return;
        }

        $invoice_settings = IncomeInvoiceSetting::where('soc_id', $soc_id)->first();
        $ledger_start_date = ($ledger_start_date) ? $ledger_start_date : $invoice_settings->effective_date;

        $group_id = $this->getLedgers($ledg_acc_id, '', '');

        if (is_numeric($group_id) && $group_id > 0) {
            $this->status = 'success';
            $this->message = 'Ledger added successfully.';
            $this->statusCode = 200;
        } else {
            $this->status = 'error';
            $this->message = 'Please enter a valid name.';
            $this->statusCode = 400;
        }
    }
}
