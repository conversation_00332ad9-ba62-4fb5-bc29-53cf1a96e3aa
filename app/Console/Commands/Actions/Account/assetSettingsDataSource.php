<?php

namespace App\Console\Commands\Actions\Account;

use Illuminate\Console\Command;
use App\Console\Commands\Action;

class assetSettingsDataSource extends Action
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'datasource:assetSettings {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    protected $formatterByKeys = ['id'];

    protected $mapper = [
        'id' => 'assets_categories_id',
    ];


    /**
     * Execute the console command.
     */
    public function apply()
    {
        $page = $this->input['page'] ?? 1;
        $per_page = !empty($this->input['per_page']) ? $this->input['per_page'] : ($this->hugeData ? 100000 : 10);
        $offset = ($page - 1) * $per_page;

        $obj = $this->tenantDB()->table('chsone_assets_categories as assets')
            ->select('assets.*', 'assets.assets_categories_id as id');

        $count = $obj->count();

        $obj = $obj->offset($offset);
        $obj = $obj->limit($per_page);

        $obj=$obj->orderBy( 'assets_categories_id', 'desc');

        $result = $obj->get();

        $result = $result->map(function ($item, $index) use ($offset) {
            $item->number = $offset + $index + 1;
            return $item;
        });

        $this->data = $result;
        $this->meta['pagination']['total'] = $count;
    }
}
