<?php

namespace App\Console\Commands\Actions\Account\Transaction;

use Illuminate\Console\Command;
use App\Console\Commands\Action;
use App\Models\Tenants\SocAccountFinancialYearMaster;
use App\Models\Tenants\IncomeInvoiceSetting;
use App\Models\Tenants\ChsoneGrpLedgerTree;
use App\Models\Tenants\ChsoneLedgerTransaction;
use Carbon\Carbon;
use App\Models\Tenants\ChsoneInvestmentsMaster;
use App\Models\Tenants\ChsoneAssetsCategory;
use App\Models\Tenants\ChsoneTaxCategory;
use App\Models\Tenants\ChsoneTaxClass;
use App\Models\Tenants\ChsoneVendorsMaster;
use App\Models\Tenants\TaxExemption;
use App\Models\Tenants\ChsoneBankRecoMaster;


class PostNewLedgerDataSource extends Action
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'datasource:PostNewLedger {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Post New Ledger Data Source';

    /**
     * Execute the console command.
     */
    public function apply()
    {
        $soc_id = $this->input['company_id'];
    	$postedValues = request()->post();
        $arrCurrentFYDetailObj = new SocAccountFinancialYearMaster();
        
        $chsoneGrpLedgerTreeObj = new ChsoneGrpLedgerTree();
        $chsoneGrpLedgerTreeObj->added_on = date('Y-m-d H:i:s');
        $arrCurrentFYDetail = $arrCurrentFYDetailObj->getCurrentFYDetail($soc_id);
        $arrCurrentFYDetail = json_decode(json_encode($arrCurrentFYDetail), true);
        $chsoneGrpLedgerTreeObj->soc_id = $soc_id ?? null;
        $chsoneGrpLedgerTreeObj->ledger_start_date = date('Y-m-d');
        $chsoneGrpLedgerTreeObj->ledger_account_name = isset($postedValues['ledger_account_name']) ? $postedValues['ledger_account_name'] : (isset($postedValues['group_account_name']) ? $postedValues['group_account_name'] : null);
        $chsoneGrpLedgerTreeObj->nature_of_account = $postedValues['nature'] ?? null;
        $chsoneGrpLedgerTreeObj->behaviour = $postedValues['behaviour'] ?? null;
        $chsoneGrpLedgerTreeObj->parent_id = $postedValues['lstParentAccount'] ?? null;
        $chsoneGrpLedgerTreeObj->entity_type = 'ledger';
        $chsoneGrpLedgerTreeObj->status = 1;
        $chsoneGrpLedgerTreeObj->created_by = '2894' ?? null;
        $chsoneGrpLedgerTreeObj->save();
        $this->message = 'Ledger Added Successfully';
    }

    
}
