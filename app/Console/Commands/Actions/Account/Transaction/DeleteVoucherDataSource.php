<?php

namespace App\Console\Commands\Actions\Account\Transaction;

use App\Console\Commands\Action;
use App\Http\Traits\MongoTraits;
use Illuminate\Support\Facades\Config;
use App\Models\Tenants\ChsoneLedgerTransaction;

class DeleteVoucherDataSource extends Action {
    /**
    * The name and signature of the console command.
    *
    * @var string
    */

    protected $constants;

    public function __construct() {
        parent::__construct();
        $this->constants = Config::get( 'constants' );
    }
    protected $signature = 'datasource:deleteVoucher {flowId} {parentId} {input}';

    /**
    * The console command description.
    *
    * @var string
    */
    protected $description = 'Get the voucher reciept';
    use MongoTraits;
    // Use the MongoTraits trait in this class

    protected $formatterByKeys = [
        'voucher_id',
    ];

    protected $mapper = [];

    /**
    * Execute the console command.
    */

    public function apply() {
        $id = $this->input[ 'id' ];
        $soc_id = $this->input[ 'company_id' ];
        $user_id = $this->input[ 'user_id' ] ?? 2894;

        $arrVoucherDetail = $this->getVoucherDetail( array( 'soc_id' => $soc_id, 'voucher_id' => array( $id ), 'status' => 1 ) );
        if ( empty( $arrVoucherDetail ) ) {
            $this->message = 'No active record found';
            $this->status = 'error';
            $this->statusCode = 400;
            return;
        }

        $arrVoucherDetail = json_decode( json_encode( $arrVoucherDetail ), true );
        $this->tenantDB()->beginTransaction();
        $arrVoucherResponse = $this->saveVoucherEntry( array( 'soc_id' => $soc_id, 'user_id' => $user_id, 'arrPostData' => array( 'voucher_id' => $id, 'status' => 0 ) ) );

        if ( !empty( $arrVoucherResponse[ 'success' ] ) && $arrVoucherResponse[ 'success' ] == true ) {
            $arrUpdateResponse = $this->updateVoucherTransaction( array( 'soc_id' => $soc_id,  'arrPostData' => $arrVoucherDetail ) );

            $arrUpdateResponse = json_decode( json_encode( $arrUpdateResponse ), true );

            if ( !empty( $arrUpdateResponse ) && strtolower( $arrUpdateResponse[ 'status' ] ) == 'success' ) {
                $id = $this->executeVouchers( $arrVoucherDetail[ 'type' ], $arrVoucherDetail[ 'to_ledger_account_id' ], $arrVoucherDetail[ 'from_ledger_account_id' ], $arrVoucherDetail[ 'transaction_date' ], $arrVoucherDetail[ 'amount' ], $arrVoucherDetail[ 'narration' ], $arrVoucherDetail[ 'to_ledger_account_name' ], $arrVoucherDetail[ 'from_ledger_account_name' ], $arrVoucherDetail[ 'reference' ], '', '', '', $soc_id, $user_id, $arrVoucherDetail[ 'voucher_id' ], '', 1 );

                if ( $id > 0 ) {
                    if ( strtolower( $arrVoucherDetail[ 'type' ] ) == 'debit' && in_array( $arrVoucherDetail[ 'sub_type' ], array( 'income', 'expense' ) ) ) {

                        $arrVoucherTransactionDetail = $this->getVoucherTransaction( array( 'soc_id' => $soc_id, 'arrPostData' => $arrVoucherDetail ) );
                        $arrDebitLedgerDetail = $this->getDebitVoucherThirdLedger( array( 'arrLedgerDetail' => $arrVoucherTransactionDetail, 'arrVoucherDetail' => $arrVoucherDetail ) );
                        if ( !empty( $arrDebitLedgerDetail[ 'ledger_account_id' ] ) ) {
                            $id2 = $this->executeVouchers( array( $arrVoucherDetail[ 'type' ], $arrDebitLedgerDetail[ 'ledger_account_id' ], $arrVoucherDetail[ 'to_ledger_account_id' ], $arrVoucherDetail[ 'transaction_date' ], $arrVoucherDetail[ 'amount' ], $arrVoucherDetail[ 'narration' ], $arrDebitLedgerDetail[ 'ledger_account_name' ], $arrVoucherDetail[ 'to_ledger_account_name' ], $arrVoucherDetail[ 'reference' ], '', '', '',  $soc_id, $user_id, $arrVoucherDetail[ 'voucher_id' ], '', 1 ) );
                            if ( $id2 < 1 ) {
                                $this->tenantDB()->rollBack();

                                $this->message = 'Voucher not deleted, Please try again.';
                                $this->status = 'error';
                                $this->statusCode = 400;
                                return;
                            }
                        }
                    }

                    $this->tenantDB()->commit();

                    $arrResponse = array( 'status' => 'success', 'message' => 'Voucher #' . $arrVoucherDetail[ 'voucher_id' ] . ' has been deleted.' );
                    if ( $arrVoucherDetail[ 'type' ] == 'debit' ) {
                        $type = 'Debit';
                    } elseif ( $arrVoucherDetail[ 'type' ] == 'contra' ) {
                        $type = 'Contra';
                    } elseif ( $arrVoucherDetail[ 'type' ] == 'payment' ) {
                        $type = 'Payment';
                    } elseif ( $arrVoucherDetail[ 'type' ] == 'journal' ) {
                        $type = 'Journal';
                    }
                    $notifyData[ 'voucher_id' ] = $arrVoucherDetail[ 'voucher_id' ];
                    $this->message = 'Voucher #' . $arrVoucherDetail[ 'voucher_id' ] . ' has been deleted.';
                    $this->data = [];
                    $this->status = 'success';

                    // $this->sendMongoNotificationDeleted( $notifyData, $soc_id, $type );
                    return ;
                } else {
                    $this->tenantDB()->rollBack();

                    $this->message = 'Voucher not deleted, Please try again.';
                    $this->status = 'error';
                    $this->statusCode = 400;
                    return;
                }
            } else {

                $this->tenantDB()->rollBack();

                $this->message = 'Voucher not deleted, Please try again.';
                $this->status = 'error';
                $this->statusCode = 400;
                return;
            }
        } else {

            dd ( 'dzf' );
            $this->tenantDB()->rollBack();

            $this->message = 'Voucher not deleted, Please try again.';
            $this->status = 'error';
            $this->statusCode = 400;
            return;
        }
    }

    public function getVoucherDetail( $data = [] ) {
        $arrVoucherDetail = $this->tenantDB()->table( 'chsone_voucher_master AS V' )
        ->select(
            'V.voucher_id',
            'V.type',
            'V.sub_type',
            'V.transaction_date',
            'V.from_ledger_account_id',
            'V.from_ledger_account_name',
            'V.to_ledger_account_id',
            'V.to_ledger_account_name',
            'V.amount',
            'V.reference',
            'V.narration',
            'V.status',
            'V.created_by',
            'V.created_date',
            'V.updated_by',
            'V.updated_date'
        )
        ->where( 'V.status', 1 )
        ->where( 'V.soc_id', $data[ 'soc_id' ] )
        ->where( 'V.voucher_id', $data[ 'voucher_id' ] )
        ->orderBy( 'V.voucher_id', 'ASC' )
        ->first();

        return $arrVoucherDetail ?? [];
    }

    public function saveVoucherEntry( $data = [] ) {
        $data = json_decode( json_encode( $data ), true );
        $soc_id = $data[ 'soc_id' ];
        $arrPostData = $data[ 'arrPostData' ];
        $currentDate = now()->format( 'Y-m-d H:i:s' );

        try {
            // Check if voucher_id is provided for update
            $objVoucher = null;
            if ( !empty( $arrPostData[ 'voucher_id' ] ) ) {
                $objVoucher = $this->tenantDB()->table( 'chsone_voucher_master' )
                ->where( 'voucher_id', $arrPostData[ 'voucher_id' ] )
                ->where( 'soc_id', $soc_id )
                ->first();

                if ( $objVoucher ) {
                    // Update the existing voucher
                    $this->tenantDB()->table( 'chsone_voucher_master' )
                    ->where( 'voucher_id', $arrPostData[ 'voucher_id' ] )
                    ->where( 'soc_id', $soc_id )
                    ->update( [
                        'status' => $arrPostData[ 'status' ] ?? $objVoucher->status,
                        'updated_date' => $currentDate,
                        'updated_by' => $data[ 'user_id' ] ?? 0,
                        'transaction_date' => $arrPostData[ 'transaction_date' ] ?? null,
                        'type' => $arrPostData[ 'voucher_type' ] ?? null,
                        'sub_type' => $arrPostData[ 'sub_type' ] ?? null,
                        'from_ledger_account_id' => $arrPostData[ 'from_ledger_account_id' ] ?? null,
                        'to_ledger_account_id' => $arrPostData[ 'to_ledger_account_id' ] ?? null,
                        'from_ledger_account_name' => $arrPostData[ 'from_ledger_account_name' ] ?? null,
                        'to_ledger_account_name' => $arrPostData[ 'to_ledger_account_name' ] ?? null,
                        'amount' => $arrPostData[ 'amount' ] ?? 0,
                        'reference' => $arrPostData[ 'reference' ] ?? null,
                        'narration' => $arrPostData[ 'narration' ] ?? null,
                    ] );

                    return [
                        'success' => true,
                        'voucher_id' => $arrPostData[ 'voucher_id' ],
                    ];
                }
            }

            // If no existing voucher is found, create a new one
            $newVoucherId = $this->tenantDB()->table( 'chsone_voucher_master' )->insertGetId( [
                'created_date' => $currentDate,
                'created_by' => $data[ 'user_id' ] ?? 0,
                'soc_id' => $soc_id,
                'transaction_date' => $arrPostData[ 'transaction_date' ] ?? null,
                'type' => $arrPostData[ 'voucher_type' ] ?? null,
                'sub_type' => $arrPostData[ 'sub_type' ] ?? null,
                'from_ledger_account_id' => $arrPostData[ 'from_ledger_account_id' ] ?? null,
                'to_ledger_account_id' => $arrPostData[ 'to_ledger_account_id' ] ?? null,
                'from_ledger_account_name' => $arrPostData[ 'from_ledger_account_name' ] ?? null,
                'to_ledger_account_name' => $arrPostData[ 'to_ledger_account_name' ] ?? null,
                'amount' => $arrPostData[ 'amount' ] ?? 0,
                'reference' => $arrPostData[ 'reference' ] ?? null,
                'narration' => $arrPostData[ 'narration' ] ?? null,
                'status' => $arrPostData[ 'status' ] ?? 1,
                'updated_date' => $currentDate,
                'updated_by' => $data[ 'user_id' ] ?? 0,
            ] );

            return [
                'success' => true,
                'voucher_id' => $newVoucherId,
            ];
        } catch ( \Exception $e ) {
            return [
                'success' => false,
                'arrMessage' => [ $e->getMessage() ],
            ];
        }
    }

    public function updateVoucherTransaction( $data = [] ) {
        $data = json_decode( json_encode( $data ), true );
        $arrPostData = $data[ 'arrPostData' ];
        $voucherId = $arrPostData[ 'voucher_id' ] ?? null;

        // Ensure the voucher ID is provided
        if ( empty( $voucherId ) ) {
            return [
                'status' => 'error',
                'message' => 'Voucher ID is required',
            ];
        }

        // Fetch existing transactions for the given voucher
        $resultset = $this->getVoucherTransaction( [
            'soc_id' => $data[ 'soc_id' ],
            'arrPostData' => $arrPostData,
        ] );

        // Check if transactions were found
        if ( empty( $resultset ) ) {
            return [
                'status' => 'error',
                'message' => 'No transactions found for the given voucher',
            ];
        }

        // Extract transaction IDs to be cancelled
        $arrTxnId = array_column( $resultset, 'txn_id' );

        // Update the `is_cancelled` status for these transactions

        try {

            // Perform the update using Eloquent
            $result = $this->tenantDB()->table( 'chsone_ledger_transactions' )
            ->where( 'txn_id', $arrTxnId )
            ->first();

            $result = json_encode( $result );
            $result = json_decode( $result, true );
            $result[ 'is_cancelled' ] = 1;

            $this->tenantDB()->table( 'chsone_ledger_transactions' )
            ->where( 'txn_id', $arrTxnId )
            ->where( 'soc_id', $data[ 'soc_id' ] )
            ->update( $result );

            return [
                'status' => 'success',
                'message' => 'Ledger transactions cancelled successfully',
            ];
        } catch ( \Exception $e ) {
            return [
                'status' => 'error',
                'message' => 'Unable to cancel ledger entry: ' . $e->getMessage(),
            ];
        }
    }

    public function getVoucherTransaction( $data = [] ) {
        $data = json_decode( json_encode( $data ), true );
        $arrPostData = $data[ 'arrPostData' ];

        // Check if voucher_id is provided
        if ( !empty( $arrPostData[ 'voucher_id' ] ) ) {
            // Build the query using Laravel's query builder
            $query = $this->tenantDB()->table('chsone_ledger_transactions AS me')
                ->select('*')
                ->where('me.voucher_reference_id', $arrPostData['voucher_id'])
                ->where('me.soc_id', $data['soc_id'])
                ->where('me.transaction_date', $arrPostData['transaction_date'])
                ->where('me.voucher_type', $arrPostData['type'])
                ->where('me.transaction_amount', $arrPostData['amount'])
                ->get();

            // Convert the result to an array if not empty
            return $query->isNotEmpty() ? $query->toArray() : [];
        }

        // Return false if no voucher_id is provided
        return false;
    }

    public function getDebitVoucherThirdLedger($data = [])
    {
        if (!empty($data['arrLedgerDetail'])) {
            return collect($data['arrLedgerDetail'])->first(function ($eachTransactionDetail) use ($data) {
                return !in_array($eachTransactionDetail['ledger_account_id'], [
                    $data['arrVoucherDetail']['from_ledger_account_id'],
                    $data['arrVoucherDetail']['to_ledger_account_id'],
                ]);
            });
        }
        return false;
    }

    public function sendMongoNotificationDeleted($data, $soc_id, $type)
    {
        /*Add Notification on mongo server*/
        $des_text = "Voucher no #" . $data['voucher_id'] . " has been deleted on " . date('d-M-Y');

        $notification_title = $type . " Entry Deleted";

        $document = [
            'title' => $notification_title,
            'desc' => $des_text,
            'scope' => 'group',
            'module' => $type,
            'company_id' => $this->input['company_id'],
            'user_ids' => $this->input['user_id'] ?? 0,
            'created_by' => $this->input['user_id'] ?? 0,
            'username' => $this->input['user']['first_name']  ?? ''. ' ' . $this->input['user']['last_name'] ?? '',
            'ip_address' => $ip ?? '',
            'role' => 'admin',
            'approve_link' => $this->input['approve_link'] ?? '',
            'action_url' => $this->input['action_url'] ?? '',
            'deny_link' => $this->input['deny_link'] ?? '',
            'id' => $this->input['id'] ?? '',
            'notified_status' => $this->input['notified_status'] ?? 0,
            'to_be_notified' => $this->input['to_be_notified'] ?? 0,
            'member_id' => $this->input['member_id'] ?? [],
            'all_admin' => $this->input['all_admin'] ?? [],
            'notification_id' => $this->input['notification_id'] ?? '',
            'date_time' => date(format: "d-M-Y H:i:s"),

        ];

        $this->addNotification($document);
    }
    public function executeVouchers(
        $voucher_type="",
        $from_ledg_id="",
        $to_ledg_id="",
        $txn_date,
        $txn_amt,
        $narration,
        $from_ledger_name,
        $to_ledger_name,
        $recp_ref = "",
        $type = "",
        $mode_of_payment = "",
        $other_recp_ref = "",
        $soc_id = "",
        $created_by = "",
        $voucher_reference_id = "",
        $voucher_reference_number = "",
        $is_cancelled = 0
    ) {
        $vouchers_array = [];
        // Check if the voucher type contains an underscore and split it if needed
        if (strpos($voucher_type, '_') !== false) {
            $vouchers_array = explode('_', $voucher_type);
            $voucher_type = $vouchers_array[0];
        }


        // Start a database transaction
        $this->tenantDB()->beginTransaction();

        try {
            // Add the first transaction entry (debit)
            $txn_from_id = $this->_addTransaction(
                0,
                $from_ledg_id,
                $txn_amt,
                $narration,
                $txn_date,
                $voucher_type,
                $type,
                "",
                $mode_of_payment,
                $recp_ref,
                $from_ledger_name,
                0,
                $other_recp_ref,
                $soc_id,
                $created_by,
                $voucher_reference_id,
                $voucher_reference_number,
                $is_cancelled
            );

            // Check if the transaction was successful
            if ($txn_from_id) {
                // Handle the case where the voucher type has a secondary part
                if (count($vouchers_array) >= 2) {
                    $voucher_type = $vouchers_array[1];
                }

                // Add the second transaction entry (credit)
                $txn_to_id = $this->_addTransaction(
                    0,
                    $to_ledg_id,
                    $txn_amt,
                    $narration,
                    $txn_date,
                    $voucher_type,
                    $type,
                    $txn_from_id,
                    $mode_of_payment,
                    $recp_ref,
                    $to_ledger_name,
                    0,
                    $other_recp_ref,
                    $soc_id,
                    $created_by,
                    $voucher_reference_id,
                    $voucher_reference_number,
                    $is_cancelled
                );

                if ($txn_to_id) {
                    // Commit the transaction if both entries are successful
                    $this->tenantDB()->commit();
                    return $txn_from_id;
                } else {

                    // Rollback if the second transaction fails
                    $this->tenantDB()->rollBack();
                    return false;
                }
            } else {

                // Rollback if the first transaction fails
                $this->tenantDB()->rollBack();
                return false;
            }
        } catch (\Exception $e) {
            // Rollback in case of any exception
            $this->tenantDB()->rollBack();
            return false;
        }
    }

    public function _addTransactionEntry($data)
    {

        // Set transaction type and ledger details based on the mode
        if (!empty($data['transaction_from_id'])) {
            $data['transaction_type'] = "cr";
            $ledger_id = $data['to_ledger_id'];
            $ledger_name = $data['to_ledger_name'];
        } else {
            $data['transaction_type'] = "dr";
            $ledger_id = $data['from_ledger_id'];
            $ledger_name = $data['from_ledger_name'];
        }

        // Ensure data is an array
        $data = json_decode(json_encode($data), true);
        $soc_id = $data['soc_id'];

        // Check for opening balance transaction
        if (!empty($data['is_opening']) && $data['is_opening'] == 1) {
            $conditions = [
                ['soc_id', ' = ', $soc_id],
                ['is_opening_balance', ' = ', 1],
                ['ledger_account_id', ' = ', $ledger_id],
            ];

            $txn_entry = $this->tenantDB()->table('chsone_ledger_transactions')
                ->where($conditions)
                ->first();

            $data['txn_id'] = $txn_entry->txn_id ?? '';
        }

        // Prepare the data for insertion
        $transactionData = [
            'soc_id' => (string) $soc_id,
            'voucher_type' => (string) $data['voucher_type'],
            'voucher_reference_number' => (int) ($data['voucher_reference_number'] ?? 0),
            'voucher_reference_id' => (int) ($data['voucher_reference_id'] ?? 0),
            'transaction_date' => (string) $data['transaction_date'],
            'ledger_account_id' => (int) $ledger_id,
            'ledger_account_name' => (string) $ledger_name,
            'transaction_type' => (string) $data['transaction_type'],
            'payment_mode' => isset($data['payment_mode']) ? (string) $data['payment_mode'] : null,
            'payment_reference' => (string) $data['payment_reference'],
            'transaction_amount' => (float) ($data['transaction_amount'] ?? 0),
            'other_reference_id' => (string) ($data['other_payment_ref'] ?? ''),
            'txn_from_id' => (string) ($data['transaction_from_id'] ?? ''),
            'memo_desc' => (string) $data['narration'],
            'is_opening_balance' => (int) ($data['is_opening'] ?? 0),
            'is_reconciled' => (int) ($data['is_reconciled'] ?? 0),
            'created_by' => (int) ($data['user_id'] ?? 0),
            'added_on' => date('Y-m-d H:i:s'),
            'is_cancelled' => !empty($data['is_cancelled']) ? 1 : 0,
        ];

        // Log the transaction data to ensure it's correct before insertion
            // Log::info( 'Attempting transaction entry', [ 'transactionData' => $transactionData ] );

            // Attempt to insert the transaction data
            $txn = $this->tenantDB()->table( 'chsone_ledger_transactions' )->insertGetId( $transactionData );

            // Return the transaction ID if successful, otherwise false
            return $txn ? $txn : false;
        }

        private function _addTransaction( $is_reco = 0, $ledger_id, $txn_amt, $narration, $txn_date, $voucher_type = VOUCHER_JOURNAL, $txn_type = '', $from_txn_id = '', $pay_mode = '', $pay_ref = '', $name = '', $is_opning = '', $other_recp_ref = '', $soc_id = '', $created_by = '', $voucher_reference_id = '', $voucher_reference_number = '', $is_cancelled = 0 ) {
            $txn = new ChsoneLedgerTransaction();
            if ( empty( $soc_id ) ) {
                $soc_id = $this->input[ 'company_id' ];
            }

            if ( !empty( $txn_type ) || $txn_type != '' ) {
                $type_txn = $txn_type;
            } else {
                if ( $from_txn_id ) {
                    $mode     = 'to';
                    $type_txn = 'cr';
                } else {
                    $mode     = 'from';
                    $type_txn = 'dr';
                }
            }

            if ( $is_opning == 1 ) {

                $txn_entrt = ChsoneLedgerTransaction::where( 'soc_id', $soc_id )
                ->where( 'is_opening_balance', 1 )
                ->where( 'ledger_account_id', $ledger_id )
                ->where( 'transaction_date', $txn_date )
                ->first();

                $txn->value_date = $txn_date;
                $txn->txn_id = ( isset( $txn_entrt->txn_id )  && !empty( $txn_entrt->txn_id ) ) ? $txn_entrt->txn_id : '';
                if ( !empty( $txn->txn_id ) ) {
                    $txn = ChsoneLedgerTransaction::findFirst( 'txn_id = ' . $txn->txn_id );
                }
            }

            $txn->soc_id = $soc_id;
            $txn->transaction_date = $txn_date;
            $txn->ledger_account_id = $ledger_id;
            $txn->ledger_account_name = $name;
            $txn->voucher_type = $voucher_type;
            $txn->transaction_type = $type_txn;
            //$ledger_name_obj->nature_of_account;
            //$txn_type;
            $txn->payment_mode = $pay_mode;
            $txn->payment_reference = $pay_ref;
            $txn->transaction_amount = ( is_null( $txn_amt ) ) ? $txn->transaction_amount : $txn_amt;
            $txn->other_reference_id = $other_recp_ref;
            $txn->txn_from_id = $from_txn_id;
            $txn->memo_desc = $narration;
            $txn->is_opening_balance = $is_opning;
            $txn->is_reconciled = $is_reco;
            /*Added New Column in transaction*/
            $txn->voucher_reference_number = $voucher_reference_number;
            $txn->voucher_reference_id = $voucher_reference_id;
            $txn->is_cancelled = $is_cancelled;
            /*End New Column in transaction*/
            $txn->created_by = $this->input[ 'user_id' ] ?? 0;
            if ( isset( $created_by ) && !empty( $created_by ) ) {
                $txn->created_by = $created_by;
            }
            $txn->added_on = date( 'Y-m-d H:i:s' );

            if ( $txn->save() ) {
                return $txn->txn_id;
            } else {
                return false;
            }
            return false;
        }

    }
