<?php

namespace App\Console\Commands\Actions\Account\Transaction;

use Illuminate\Console\Command;
use App\Console\Commands\Action;
use App\Models\Tenants\SocAccountFinancialYearMaster;
use App\Models\Tenants\IncomeInvoiceSetting;
use App\Models\Tenants\ChsoneGrpLedgerTree;
use App\Models\Tenants\ChsoneLedgerTransaction;
use Carbon\Carbon;
use App\Models\Tenants\ChsoneInvestmentsMaster;
use App\Models\Tenants\ChsoneAssetsCategory;
use App\Models\Tenants\ChsoneTaxCategory;
use App\Models\Tenants\ChsoneTaxClass;
use App\Models\Tenants\ChsoneVendorsMaster;
use App\Models\Tenants\TaxExemption;
use App\Models\Tenants\ChsoneBankRecoMaster;
use App\Models\Tenants\ChsoneAccountsMaster;
use App\Models\Tenants\ChsoneAsset;
use Illuminate\Support\Facades\Config;


class AddInvestMentDataSource extends Action
{
    protected $constants;

    public function __construct() {
        parent::__construct();
        $this->constants = Config::get("constants");
    }

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'datasource:AddInvestMent {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Post New Ledger Data Source';

    /**
     * Execute the console command.
     */
    public function apply()
    {
        $arrPostData = $this->input;

        $type = $arrPostData['type'];
        $year = $arrPostData['year'];
        $soc_id = $arrPostData['company_id'];
        $account_number = $arrPostData['investment_account_number'];
        $bank_name = $arrPostData['investment_bank_name'];
        $bank_branch = $arrPostData['investment_bank_branch'];
        $bank_address = $arrPostData['investment_bank_address'];
        $bank_city = $arrPostData['investment_bank_city'];
        $bank_ifsc = $arrPostData['investment_bank_ifsc'];
        $amount = $arrPostData['amount'];
        $payment_mode = $arrPostData['payment_mode'];
        $cash_ledger = ChsoneGrpLedgerTree::where('ledger_account_name', 'Cash in Hand')->first()->ledger_account_id;
        if($payment_mode == 'cash') {
            $bank_ledger = '0';
        } else {
            $bank_ledger = $arrPostData['bank_ledger'];
        }
        $start_date = $arrPostData['start_date'];
        $maturity_date = $arrPostData['maturity_date'];

        if ($amount > 0) {
            $behaviour = 'asset';
            $nature = 'dr';
            $entity_type = 'ledger';
            $ledger_account_name = strtoupper("$type-$bank_name-$account_number");
            $parent = ($type == 'fd') ? 7 : 7;
            $mode = 'add';
            $ledger_start_date = Carbon::parse($start_date)->format('Y-m-d'); // Assuming getDatabaseDate also returns a formatted date
            $ledger_type = 'direct';

            // add investment entry
            $investment = new ChsoneInvestmentsMaster();
            $investment->account_number = $account_number;
            $investment->type = $type;
            $investment->bank_name = $bank_name;
            $investment->branch = $bank_branch;
            $investment->bank_address = $bank_address;
            $investment->bank_city = $bank_city;
            $investment->bank_ifsc = $bank_ifsc;
            $investment->ledger_account_id = 0;
            $investment->status = 1;
            $investment->start_date = $ledger_start_date;
            $investment->maturity_date = Carbon::parse($maturity_date)->format('Y-m-d');
            $investment->created_on = $ledger_start_date;
            $investment->created_by = $arrPostData['user_id'] ?? 0;
            $investment->updated_on = $ledger_start_date;
            $investment->updated_by = $investment->created_by;

            // Save the investment
            if($investment->save()) {
                if($year == 'current') {
                    // Bank/Cash to investment ledger entry
                    $opening_balance     = '0.00';
                    $id = $this->manipulate($ledger_account_name, $entity_type, '', 
                                    $parent, $behaviour, '', $opening_balance, '', $ledger_start_date, 
                                    $ledger_type);
                    $from_ledger = ($payment_mode == 'cash') ? $cash_ledger : $bank_ledger;
                    $to_ledger_name       = $this->getLedger($id, 0);
                    $from_ledger_name     = $this->getLedger($from_ledger, 0); 

                    // add transaction entry
                    $this->executeVouchers(VOUCHER_JOURNAL, $id, $from_ledger, $ledger_start_date, $amount, 
                                                               "Deposite Investment for account number $account_number started $start_date",
                                                               $ledger_account_name, $from_ledger_name['ledger_account_name'], $investment->id, 
                                                               $payment_mode,"",$soc_id); 
                } else {
                    // Opening balance of investment ledger entry
                    $opening_balance     = $amount;
                    $id = $this->manipulate($ledger_account_name, $entity_type, '', $parent, $behaviour, '', 
                                    $opening_balance, '', $ledger_start_date, $ledger_type);
                }

                // update investment ledger account id
                $investment->ledger_account_id = $id;

                if ($investment->update()) {
                    $this->status = 'success';
                    $this->statusCode = 200;
                    $this->message = 'Investment Added Successfully';
                } else {
                    $this->status = 'error';
                    $this->statusCode = 400;
                    $this->message = 'Error while saving investment';
                }

            } else {
                $this->status = 'error';
                $this->statusCode = 400;
                $this->message = 'Error while saving investment';
            }
        } else {
            $this->status = 'error';
            $this->statusCode = 400;
            $this->message = 'Amount should be greater than 0';
        }
    }

    public function manipulate($name, $entity_type = ENTITY_TYPE_GROUP, $grp_ledg_id = "", $parent_group = 0, $behaviour = "", $ledg_id = '', $opening_balance = 0, $update_led_id = '', $ledger_start_date = '', $ledger_type = '', $context = '', $txn_id = 0, $soc_id = 0, $is_reco = 0)
    {
        $config = Config::get("constants");
        if(empty($soc_id)){
            $soc_id = $this->input['company_id'];
        }
        $dup = $this->_checkGroupNameDuplication($parent_group, $name, $ledg_id, $update_led_id);
        
        $arrFYDetail = $this->getCurrentFinancialYear(array('soc_id' => $soc_id));
        $arrFYDetail = json_decode(json_encode($arrFYDetail), true);
        $ledger_start_date = $arrFYDetail['fy_start_date'];
        if ($dup == 0) {
            $behaviour = trim(strtolower($behaviour));

            $grp_ledg_tree = new ChsoneGrpLedgerTree();

            if ($ledg_id) {
                $grp_ledg_tree = $this->getLedger($ledg_id);
            }

            $grp_ledg_tree->entity_type = $entity_type;
            $grp_ledg_tree->soc_id = $soc_id;
            $grp_ledg_tree->ledger_account_name = $name;
            $grp_ledg_tree->ledger_start_date = $ledger_start_date;
            $grp_ledg_tree->context_ref_id = 0;

            if (!empty($ledger_type) && $ledger_type != "NULL" && $ledger_type != '') {
                $grp_ledg_tree->operating_type = $ledger_type;
            } else {
                $grp_ledg_tree->operating_type = '';
            }

            if (!empty($grp_ledg_id)) {
                $grp_ledg_tree->ledger_account_id = $grp_ledg_id;
            }

            if ($update_led_id != '') {
                $grp_ledg_tree->ledger_account_id = $update_led_id;
            }
            if (!empty($parent_group)) {
                $grp_ledg_tree->parent_id = $parent_group;
                $ledger_props = $this->_getLedgerProps($parent_group);
            } else {
                $grp_ledg_tree->parent_id = 0;
            }

            if (!empty($behaviour)) {
                $grp_ledg_tree->behaviour = $behaviour;
                $grp_ledg_tree->nature_of_account = $config['nature_account'][$behaviour];
                $grp_ledg_tree->report_head = $config['report_head'][$behaviour];
            } else {
                $grp_ledg_tree->behaviour = $ledger_props["behaviour"];
                $grp_ledg_tree->nature_of_account = $ledger_props["nature_of_account"];
                $grp_ledg_tree->report_head = $ledger_props["report_head"];
            }

            if (!empty($context) && $context != '') {
                $grp_ledg_tree->context = $context;
            } else {
                $grp_ledg_tree->context = $ledger_props["context"];
            }
            $grp_ledg_tree->defined_by = "user";
            $grp_ledg_tree->status = 1;
            $grp_ledg_tree->added_on = date("Y-m-d H:i:s");
            $grp_ledg_tree->created_by = $this->input['user_id'] ?? 0;

            unset($grp_ledg_tree->ledger_account_id);

            if ($grp_ledg_tree->save()) {
                //Opening balance should not go in case on Income Expense ledger
                if (!in_array(strtolower($grp_ledg_tree->behaviour), array("income", "expense"))) {

                    $txn_date = $ledger_start_date;
                    $narration = 'entry for opening balance';
                    $txn_id = $this->addTxn($grp_ledg_tree->ledger_account_id, $opening_balance, $narration, $txn_date, "", $grp_ledg_tree->nature_of_account, "", "", "", "", $name, $is_opning = 1, $is_reco);
                }

                return $grp_ledg_tree->ledger_account_id;
            } else {
                
                return false;
            }
        } else {
            return "DUP" . $dup;
        }
    }

    private function _checkGroupNameDuplication($parent, $name, $ledg_id = "", $update_led_id = "")
    {
        $soc_id = $this->input['company_id'];

        $bind[1] = strtolower(trim($name));
        $ledgers_count = 0;
        if ($ledg_id) {
            $ledgers_count = ChsoneGrpLedgerTree::where("soc_id", $soc_id)->where('ledger_account_name', strtolower(trim($name)))->where('ledger_account_id', '!=', $ledg_id)->count();
        }
        if ($update_led_id) {
            $ledgers_count = ChsoneGrpLedgerTree::where("soc_id", $soc_id)->where('ledger_account_name', strtolower(trim($name)))->where('ledger_account_id', '!=', $update_led_id)->count();
        }
        
        return $ledgers_count;
    }

    public function getCurrentFinancialYear($data=array())
    {
        $arrFYDetail = array();
        $arrFYDetail = SocAccountFinancialYearMaster::where("soc_id", $data['soc_id'])->where("confirmed", 0)->orderBy("account_closing_id", "asc")->first();
        
        if(!empty($arrFYDetail))
        {
            $arrFYDetail = $arrFYDetail->toArray();
        }
        return $arrFYDetail;
    }

    public function getLedger($ledger_acct_id, $return_object = 1)
    {
        //fetch ledger details from chsone_grp_ledger_tree whose ledger_account_id = $ledger_acct_id
        $ledger = ChsoneGrpLedgerTree::where("ledger_account_id", $ledger_acct_id)->first();

        if ($ledger) {
            if ($return_object) {
                return $ledger;
            } else {
                return $ledger->toArray();
            }
        } else {
            return false;
        }
    }

    public function _getLedgerProps($parent_group)
    {
        $array = array();

        if (is_numeric($parent_group)) {
            $ledger_props = ChsoneGrpLedgerTree::select('ledger_account_name', 'nature_of_account', 'behaviour', 'report_head', 'context')
            ->where('ledger_account_id', $parent_group)
            ->first();
        }

        $array = $ledger_props;
        return $array;
    }

    public function addTxn($ledger_id, $txn_amt, $narration, $txn_date, $voucher_type = VOUCHER_JOURNAL, $txn_type = "", $from_txn_id = "", $pay_mode = "", $pay_ref = "", $opening_balance = "", $ledger_name = "", $is_opning = 1, $is_reco = 0)
    {
        return $this->_addTransaction($is_reco, $ledger_id, $txn_amt, $narration, $txn_date, $voucher_type, $txn_type, $from_txn_id, $pay_mode, $pay_ref, $ledger_name, $is_opning = 1);
    }

    private function _addTransaction($is_reco = 0, $ledger_id, $txn_amt, $narration, $txn_date, $voucher_type = VOUCHER_JOURNAL, $txn_type = "", $from_txn_id = "", $pay_mode = "", $pay_ref = "", $name = "", $is_opning = "", $other_recp_ref = "", $soc_id = "", $created_by = "", $voucher_reference_id = "", $voucher_reference_number = "", $is_cancelled = 0)
    {
        $txn = new ChsoneLedgerTransaction();
        if (empty($soc_id)) {
            $soc_id = $this->input['company_id'];
        }

        if (!empty($txn_type) || $txn_type != "") {
            $type_txn = $txn_type;
        } else {
            if ($from_txn_id) {
                $mode     = "to";
                $type_txn = "cr";
            } else {
                $mode     = "from";
                $type_txn = "dr";
            }
        }

        //echo "s_opning".$is_opning;exit;
        if ($is_opning == 1) {

            $txn_entrt = ChsoneLedgerTransaction::where('soc_id', $soc_id)
            ->where('is_opening_balance', 1)
            ->where('ledger_account_id', $ledger_id)
            ->where('transaction_date', $txn_date)
            ->first();

            $txn->value_date = $txn_date;
            $txn->txn_id = (isset($txn_entrt->txn_id)  && !empty($txn_entrt->txn_id)) ? $txn_entrt->txn_id : '';
            if (!empty($txn->txn_id)) {
                $txn = ChsoneLedgerTransaction::findFirst("txn_id = " . $txn->txn_id);
            }
        }

        $txn->soc_id = $soc_id;
        $txn->transaction_date = $txn_date;
        $txn->ledger_account_id = $ledger_id;
        $txn->ledger_account_name = $name;
        $txn->voucher_type = $voucher_type;
        $txn->transaction_type = $type_txn; //$ledger_name_obj->nature_of_account;//$txn_type;
        $txn->payment_mode = $pay_mode;
        $txn->payment_reference = $pay_ref;
        $txn->transaction_amount = (is_null($txn_amt)) ? $txn->transaction_amount : $txn_amt;
        $txn->other_reference_id = $other_recp_ref;
        $txn->txn_from_id = $from_txn_id;
        $txn->memo_desc = $narration;
        $txn->is_opening_balance = $is_opning;
        $txn->is_reconciled = $is_reco;
        /*Added New Column in transaction*/
        $txn->voucher_reference_number = $voucher_reference_number;
        $txn->voucher_reference_id = $voucher_reference_id;
        $txn->is_cancelled = $is_cancelled;
        /*End New Column in transaction*/
        $txn->created_by = $this->input['user_id'] ?? 0;
        if (isset($created_by) && !empty($created_by)) {
            $txn->created_by = $created_by;
        }
        $txn->added_on = date("Y-m-d H:i:s");

        if ($txn->save()) {
            return $txn->txn_id;
        } else {
            print_r($txn->getMessages);
        }
        //echo "<pre>";print_r($a->getMessages);exit;
        return false;
    }

    public function executeVouchers(
        $voucher_type,
        $from_ledg_id,
        $to_ledg_id,
        $txn_date,
        $txn_amt,
        $narration,
        $from_ledger_name,
        $to_ledger_name,
        $recp_ref = "",
        $type = "",
        $mode_of_payment = "",
        $other_recp_ref = "",
        $soc_id = "",
        $created_by = "",
        $voucher_reference_id = "",
        $voucher_reference_number = "",
        $is_cancelled = 0
    ) {
        // Split the voucher_type if it contains an underscore
        $vouchers_array = [];
        if (strpos($voucher_type, '_') !== false && strpos($voucher_type, '_') > 0) {
            $vouchers_array = explode('_', $voucher_type);
            $voucher_type = $vouchers_array[0];
        }

        $this->tenantDB()->beginTransaction();

        try {
            // Add the first transaction
            $txn_from_id = $this->_addTransaction(
                0,
                $from_ledg_id,
                $txn_amt,
                $narration,
                $txn_date,
                $voucher_type,
                $type,
                "",
                $mode_of_payment,
                $recp_ref,
                $from_ledger_name,
                0, // is_opning
                $other_recp_ref,
                $soc_id,
                $created_by,
                $voucher_reference_id,
                $voucher_reference_number,
                $is_cancelled
            );

            if ($txn_from_id) {
                // If there are multiple voucher types, handle the second part
                if (count($vouchers_array) >= 2) {
                    $voucher_type = $vouchers_array[1];
                }

                // Add the second transaction
                $txn_to_id = $this->_addTransaction(
                    0,
                    $to_ledg_id,
                    $txn_amt,
                    $narration,
                    $txn_date,
                    $voucher_type,
                    $type,
                    $txn_from_id,
                    $mode_of_payment,
                    $recp_ref,
                    $to_ledger_name,
                    0, // is_opning
                    $other_recp_ref,
                    $soc_id,
                    $created_by,
                    $voucher_reference_id,
                    $voucher_reference_number,
                    $is_cancelled
                );

                if ($txn_to_id) {
                    // Commit the transaction if both operations succeeded
                    $this->tenantDB()->commit();
                    return $txn_from_id;
                } else {
                    // Rollback if the second transaction fails
                    $this->tenantDB()->rollBack();
                    return false;
                }
            } else {
                // Rollback if the first transaction fails
                $this->tenantDB()->rollBack();
                return false;
            }
        } catch (\Exception $e) {
            // Handle any exceptions and rollback the transaction
            $this->tenantDB()->rollBack();
            throw $e;
        }
    }
}
