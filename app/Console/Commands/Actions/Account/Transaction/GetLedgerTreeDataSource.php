<?php

namespace App\Console\Commands\Actions\Account\Transaction;

use Illuminate\Console\Command;
use App\Console\Commands\Action;
use App\Models\Tenants\ChsoneGrpLedgerTree;

class GetLedgerTreeDataSource extends Action
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'datasource:GetLedgerTree {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Get Ledger Tree Data Source';

    /**
     * Execute the console command.
     */


     public function apply()
     {
         $currentTab = strtolower(trim($this->input['current_tab'] ?? 'asset'));
         $soc_id = $this->input['company_id'];

         // Fetch root-level ledger accounts
        $query = $this->tenantDB()
        ->table('chsone_grp_ledger_tree')
        ->whereIn('nature_of_account', ['dr', 'cr', 'Credit'])
        ->where('soc_id', $soc_id);

        // Conditionally add the parent_id condition based on the currentTab value
        if ($currentTab !== 'cash') {
        $query->where(function ($query) {
            $query->where('parent_id', 0)
                ->orWhereNull('parent_id');
        });
        }

        $result = $query->get();
         // Create a mapping of ledger_account_name to ledger_account_id
         $parentIdMap = [];
         foreach ($result as $row) {
             $parentIdMap[strtolower($row->ledger_account_name)] = $row->ledger_account_id;
         }

         // Normalize the currentTab value and fetch the parentId
         $parentId = $parentIdMap[$currentTab] ?? 1;


         // Prepare the condition based on entity_type
         $condition = isset($_REQUEST['entity_type']) && $_REQUEST['entity_type'] == 'group'
             ? "entity_type = 'group'"
             : "parent_id = $parentId";

         // Recursive query for other cases
         $query = "WITH RECURSIVE LedgerTree AS (
             SELECT ledger_account_id, ledger_account_name, ledger_account_name as title,
                    nature_of_account, entity_type, behaviour, parent_id, 0 AS level, status, context
             FROM chsone_grp_ledger_tree
             WHERE $condition
             UNION ALL
             SELECT child.ledger_account_id, child.ledger_account_name, child.ledger_account_name as title,
                    child.nature_of_account, child.entity_type, child.behaviour, child.parent_id,
                    parent.level + 1, child.status, child.context
             FROM chsone_grp_ledger_tree AS child
             INNER JOIN LedgerTree AS parent ON child.parent_id = parent.ledger_account_id
         )
         SELECT * FROM LedgerTree ORDER BY level, ledger_account_name;";

         $query = $this->filter($query);
         $result = $this->tenantDB()->select($query);

         if(empty($result)){
            $directResult2 = $this->tenantDB()
            ->table('chsone_grp_ledger_tree')
            ->where('soc_id', $soc_id)
            ->where('ledger_account_id', $parentId)
            ->select(
                'ledger_account_id',
                'ledger_account_name',
                'nature_of_account',
                'entity_type',
                'behaviour',
                'status',
                'context',
                'parent_id'
            )
            ->first();

            $directResult2 =  json_decode(json_encode($directResult2), true);

            if($directResult2['parent_id']==0){
               $this->data = [$directResult2];
               return;
            }
         }

         // Build the tree
         $tree = [];
         if (isset($_REQUEST['entity_type']) && $_REQUEST['entity_type'] == 'group') {
             foreach ($parentIdMap as $id) {
                 $tree[] = $this->buildTree($result, $id);
             }
         } else {
             $tree = $this->buildTree($result, $parentId);
         }

         $this->data = $tree;
     }

    public function buildTree($data, $parentId)
    {
        $tree = [];

        foreach ($data as $row) {
            if ($row->parent_id == $parentId && (!isset($_REQUEST['entity_type']) || ($_REQUEST['entity_type'] == 'group' && $row->entity_type == 'group'))) {
                $node = [
                    'id' => $row->ledger_account_id,
                    'ledger_account_name' => $row->ledger_account_name,
                    'title' => $row->ledger_account_name,
                    'nature_of_account' => $row->nature_of_account,
                    'entity_type' => $row->entity_type,
                    'behaviour' => $row->behaviour,
                    'context' => $row->context,
                    'status' => $row->status,
                    'rows' => $this->buildTree($data, $row->ledger_account_id)
                ];

                $tree[] = $node;
            }
        }

        return $tree;
    }
}
