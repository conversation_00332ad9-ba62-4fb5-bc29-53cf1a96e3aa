<?php

namespace App\Console\Commands\Actions\Account\Transaction;

use Illuminate\Console\Command;
use App\Console\Commands\Action;
use Illuminate\Support\Facades\Route;

class ListVoucherTrackerDataSource extends Action
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'datasource:ListVoucherTracker {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Get the list of voucher tracker';


    protected $formatterByKeys = ['voucher_id'];


    /**
     * Execute the console command.
     */
    public function apply()
    {
        $filters = $this->input['filters'] ?? [];

        $from_date = date('Y-m-01');
        $to_date = date('Y-m-t');

        $currentRoute = Route::current();

        // Get the route URI pattern (e.g., "member/register/{id}")
        $routeUri = $currentRoute->uri();
        if ($routeUri == 'api/admin/transaction/voucherReport/download/{type}') {
            $this->hugeData = true;
        }

        if (!empty($filters['startDate']) || !empty($filters['endDate'])) {
            [$from_date, $to_date] = $this->getFromAndToDate($filters);
        }
        

        $obj = $this->tenantDB()->table('chsone_voucher_master AS V')
            ->select(
                'V.voucher_id AS id',
                'V.voucher_id',
                'V.type',
                'V.transaction_date',
                'V.from_ledger_account_id',
                'V.from_ledger_account_name',
                'V.to_ledger_account_id',
                'V.to_ledger_account_name',
                'V.amount',
                'V.reference',
                'V.narration',
                'V.status',
                'V.created_by',
                'V.created_date',
                'V.updated_by',
                'V.updated_date'
            );

        // Apply filters
        foreach (['voucher_id', 'type', 'transaction_date', 'from_ledger_account_name', 'to_ledger_account_name', 'amount', 'narration'] as $filter) {
            if (!empty($filters[$filter])) {
                $obj->where("V.$filter", 'like', '%' . $filters[$filter] . '%');
            }
        }

        if (isset($filters['status']) && $filters['status'] !== '') {
            $status = array_map('trim', explode(',', $filters['status']));
            $obj->whereIn('V.status', $status);
        }
         // get route to switch query
         $currentRoute = Route::current();

         // Get the route URI pattern (e.g., "member/register/{id}")
         $routeUri = $currentRoute->uri();

         if ($routeUri != 'api/admin/transaction/listVoucherTracker') {
            $obj->whereBetween('V.transaction_date', [$from_date, $to_date]);
         } else {
            $this->hugeData = true;
         }


        $obj->orderBy('V.voucher_id', 'asc');
        

        $count = $obj->count();
        $page = $this->input['page'] ?? 1;
        $per_page = $this->input['per_page'] ?? ($this->hugeData ? 100000 : 10);
        $offset = ($page - 1) * $per_page;
        $result = $obj->offset($offset)->limit($per_page)->get();
        // Add sr_no
        $srNoStart = $offset + 1;
        foreach ($result as $key => $row) {
            $row->sr_no = $srNoStart + $key;
        }

        $this->data = $result;
        $this->meta['pagination']['total'] = $count;
    }

    public function getFromAndToDate($inputs)
    {
        $from_date = $inputs['startDate'] ?? date("Y-m-01");
        $to_date = $inputs['endDate'] ?? date("Y-m-t");
        return [$from_date, $to_date];
    }

}
