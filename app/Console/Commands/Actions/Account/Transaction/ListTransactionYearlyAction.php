<?php

namespace App\Console\Commands\Actions\Account\Transaction;

use Illuminate\Console\Command;
use App\Console\Commands\Action;

class ListTransactionYearlyAction extends Action
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'datasource:listTransactionYearly {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'List Yearly Transaction';

    protected $formatterKey = ['id'];



    protected $mapper = [
        'id' => 'txn_id',
    ];

    public function apply()
    {
        $ledgerId = $this->input['id'];
        $month = "";
        $year = "";

        //conditions added for monthly transaction on month and year
        //if month and year selected from dropdown
        if (isset($this->input['filters']['month'])) {
            $month = $this->input['filters']['month'];
        }
        if (isset($this->input['filters']['year'])) {
            $year = $this->input['filters']['year'];
        }
        if (isset($this->input['month']) && $this->input['month'] != 0) {
            $month = $this->input['month'];
            $month = $this->input['filters']['month'] = $month;
        } else {
            $month = date("m");
        }

        if (isset($this->input['year']) && $this->input['year'] != 0) {
            $year = $this->input['year'];
            $year = $this->input['filters']['year'] = $year;
        } else {
            $year = date("Y");
        }

        //condition added in case transaction list is yearly
        if (isset($this->input['yearly'])) {
            $year = explode("-", $this->input['yearly'])[0];
            $month = 13;
        }


        $arrayMaster = [];
        $arrayMaster['accountMaster'] = $this->tenantDB()->table('soc_account_start_master')->first();

        $arrayMaster['financialMaster'] = $this->tenantDB()->table('soc_account_financial_year_master')->get()->toArray();

        $unitStartDateYear = [];

        $fyStartFrom = $arrayMaster['accountMaster']->fy_start_from;
        $transactionList = $this->getMonthlyTransaction($ledgerId, $month, $year);

        if ($fyStartFrom == "01-01") {
            for ($i = 0; $i < count($arrayMaster['financialMaster']); $i++) {
                $currentFyStart = [];
                $currentFyStart = explode("-", $arrayMaster['financialMaster'][$i]['fy_start_date']);
                $unitStartDateYear = $currentFyStart[0];
            }
        } else {
            for ($i = 0; $i < count($arrayMaster['financialMaster']); $i++) {
                $currentFyStart = [];
                $currentFyEnd = [];
                $currentFyStart = explode("-", $arrayMaster['financialMaster'][$i]->fy_start_date);
                $currentFyEnd = explode("-", $arrayMaster['financialMaster'][$i]->fy_end_date);

                if ($currentFyStart[0] == $currentFyEnd[0]) {
                    $currentFyStart[0] = $currentFyStart[0] - 1;
                }
                $unitStartDateYear = $currentFyStart[0] . "-". $currentFyEnd[0];
            }
        }

        // Get the Transaction list yearly
        $transactionList = $this->getMonthlyTransaction($ledgerId, $month, $year);

        $firstrow = array(
            'txn_id' => 0,
            'transaction_date' => ($transactionList['opening'] !== 0) ? $this->getDisplayDate($transactionList['opening_date']) : '',
            'name' => '',
            'memo_desc' => ($transactionList['opening'] !== 0) ? 'Opening Balance' : 'Brought forward',
            'transaction_amount_debit' => ($transactionList['opening'] !== 0 && $transactionList['opening_type'] != 'cr') ? abs($transactionList['opening']) : '',
            'transaction_amount_credit' => ($transactionList['opening'] !== 0 && $transactionList['opening_type'] != 'dr') ? abs($transactionList['opening']) : '',
            'balance' => ($transactionList['opening'] !== 0) ? $transactionList['opening'] : $transactionList['forwarded']
        );

        // Get the Ledger names
        $transactionList['ledgerNames'] = $this->getCounterEntryLedgerName($transactionList['transactions']);
        $transactionList['transactions'] = $transactionList['transactions']->prepend($firstrow);

        $this->data = $transactionList;
    }

    public function getYearlyTransaction($ledgerId, $year)
    {
        $parent = 'Top';
        $openingDate = '';
        $totalCR = 0;
        $totalDR = 0;

        $groupLedgerTree = $this->tenantDB()
            ->table("chsone_grp_ledger_tree")
            ->where('ledger_account_id', $ledgerId)
            ->first();

        if ($groupLedgerTree->parent_id != 0) {
            $parent = $this->getParentLedgerAccount($groupLedgerTree->parent_id);
        }

        $startDate = $year . "-04-01";
        $endDate = ($year + 1) . "-03-31";

        $ledgerTxn = $this->getOpeningBalanceTransaction($ledgerId, $startDate, $endDate);

        if (empty($ledgerTxn)) {
            $forward = $this->calculateForwardBalance($ledgerId, $startDate, $year, $groupLedgerTree);
            $ledgerTxn = 0;
        } else {
            $forward = 0;
            $openingDate = $ledgerTxn->transaction_date;
            $openingType = $ledgerTxn->transaction_type;
            $ledgerTxn = ($ledgerTxn->transaction_type == $groupLedgerTree->nature_of_account) ?
                $ledgerTxn->transaction_amount : ($ledgerTxn->transaction_amount * -1);
        }
        $transactionResult = $this->getTransactions($ledgerId, $startDate, $endDate, $groupLedgerTree);
        $balance = $ledgerTxn + $forward;

        foreach ($transactionResult as &$transaction) {
            $balance = $transaction->balance = ($groupLedgerTree->nature_of_account == $transaction->transaction_type)
                ? $balance + $transaction->transaction_amount : $balance - $transaction->transaction_amount;

            $totalCR += ($transaction->transaction_type == "cr") ? $transaction->transaction_amount : 0;
            $totalDR += ($transaction->transaction_type == "dr") ? $transaction->transaction_amount : 0;
        }

        $total = $this->calculateTotalBalance($ledgerId, $year, $groupLedgerTree);

        return [
            "ledger"       => $groupLedgerTree->ledger_account_name,
            "opening"      => $ledgerTxn,
            'opening_type' => isset($openingType) ? $openingType : '',
            'opening_date' => $openingDate ? $openingDate : '',
            'forwarded'    => $forward,
            'transactions' => $transactionResult,
            'parent'       => $parent,
            'totalcr'      => $totalCR,
            'totaldr'      => $totalDR,
            'balance'      => $balance,
            "total"        => $total
        ];
    }

}
