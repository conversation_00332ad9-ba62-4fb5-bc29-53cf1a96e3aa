<?php

namespace App\Console\Commands\Actions\Account\Transaction;

use Illuminate\Console\Command;
use App\Console\Commands\Action;
use App\Models\Tenants\ChsoneAssetsCategory;
use App\Models\Tenants\ChsoneGrpLedgerTree;
use App\Models\Tenants\ChsoneLedgerTransaction;
use App\Models\Tenants\SocAccountFinancialYearMaster;

class addAssetSettingsDataSource extends Action
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'datasource:addAssetSettings {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Add Asset Settings Data Source';

    /**
     * Execute the console command.
     */
    public function apply()
    {
        $soc_id = $this->input['company_id'];
        $user_id = $this->input['user_id'] ?? 0;

        // check if array is empty or not
        if (empty($this->input['assetCategoryArray'])) {
            $this->status = 'error';
            $this->message = 'Asset category array is required.';
            $this->statusCode = 400;
            return;
        } else {
            $assetCategoryArray = $this->input['assetCategoryArray']; // json_decode($this->input['assetCategoryArray'], true);
        }

        $addAssetCategory = null;
        // if array is not empty then loop through each asset category and add it to the database and also check if the asset category already exists or not
        foreach ($assetCategoryArray as $assetCategory) {
            $asset_categories_name = $assetCategory['assets_categories_name'];
            $asset_categories_type = $assetCategory['assets_categories_type'];
            $opening_balance = $assetCategory['opening_balance'] ?? 0;

            $assetCategoryExists = ChsoneAssetsCategory::where('assets_categories_name', $asset_categories_name)->first();
            if ($assetCategoryExists) {
                $this->status = 'error';
                $this->message = "Asset category '{$asset_categories_name}' already exists.";
                $this->statusCode = 400;
                return;
            }

            // add asset category
            $addAssetCategory = $this->assetSetUp($asset_categories_name, $asset_categories_type, $opening_balance, $soc_id, $user_id);
        }

        if ($addAssetCategory) {
            $this->status = 'success';
            $this->message = 'Asset category added successfully.';
            $this->statusCode = 200;
            return;
        } else {
            $this->status = 'error';
            $this->message = 'Failed to add asset category.';
            $this->statusCode = 400;
            return;
        }
    }

    /**
     * Asset category setup
     *
     * @param string $asset_categories_name
     * @param string $asset_categories_type
     * @param float $opening_balance
     * @return bool
     */
    private function assetSetUp($asset_categories_name, $asset_categories_type, $opening_balance, $soc_id, $user_id = null)
    {
        $assetCategory = new ChsoneAssetsCategory();
        $assetCategory->assets_categories_name = $asset_categories_name;
        $assetCategory->assets_categories_type = $asset_categories_type;
        $assetCategory->asset_opening_balance = $opening_balance;
        $assetCategory->added_on = date('Y-m-d H:i:s');
        $assetCategory->updated_on = date('Y-m-d H:i:s');
        $assetCategory->status = 1;
        $assetCategory->soc_id = $soc_id;
    
        if($assetCategory->save()) {

            // add asset category to the chsone_grp_ledger_tree table
            $addAssetCategoryToLedgerTree = $this->addAssetCategoryToLedgerTree($assetCategory->assets_categories_id, $asset_categories_name, $asset_categories_type, $soc_id, $user_id);
            
            if ($addAssetCategoryToLedgerTree) {
                $ledger_account_id = $addAssetCategoryToLedgerTree;

                // update the ledgercreated flag in the chsone_assets_category table where id = $assetCategory->assets_categories_id to 1
                $assetCategory->ledgercreated = 1;
                $assetCategory->save();

                // add ledger transaction entry for the asset category in the chsone_ledger_transactions table
                $addLedgerTransaction = $this->addLedgerTransaction($ledger_account_id, $assetCategory->assets_categories_id, $asset_categories_name, $asset_categories_type, $opening_balance, $soc_id, $user_id);
            }

            return true;
        } else {
            return false;
        }
    }

    /**
     * Add asset category to the chsone_grp_ledger_tree table
     *
     * @param int $assetCategoryId
     * @param string $assetCategoryName
     * @param string $assetCategoryType
     * @param int $socId
     * @return bool
     */
    private function addAssetCategoryToLedgerTree($assetCategoryId, $assetCategoryName, $assetCategoryType, $socId, $userId)
    {
        $ledgerTree = new ChsoneGrpLedgerTree();

        // fetch nature of account from chsone_grp_ledger_tree table where ledger_account_name = $assetCategoryType. "Assets" and limit 1
        $categoryNatureOfAccount = ChsoneGrpLedgerTree::where('ledger_account_name', $assetCategoryType. " Assets")->first();
        $nature_of_account = $categoryNatureOfAccount->nature_of_account ?? '';
        $parent_id = $categoryNatureOfAccount->ledger_account_id ?? 0;
        $report_head = $categoryNatureOfAccount->report_head ?? '';
        $context = $categoryNatureOfAccount->context ?? '';
        $behaviour = $categoryNatureOfAccount->behaviour ?? '';

        // fetch fy_start_date from soc_account_financial_year_master table where soc_id = $socId and limit 1
        $socAccountFinancialYearMaster = SocAccountFinancialYearMaster::where('soc_id', $socId)->first();
        $fy_start_date = $socAccountFinancialYearMaster->fy_start_date ?? '';

        $ledgerTree->soc_id = $socId;
        $ledgerTree->ledger_account_name = $assetCategoryName.'-'.$assetCategoryId;
        $ledgerTree->nature_of_account = $nature_of_account;
        $ledgerTree->parent_id = $parent_id;
        $ledgerTree->report_head = $report_head;
        $ledgerTree->context_ref_id = $assetCategoryId;
        $ledgerTree->context = $context;
        $ledgerTree->ledger_start_date = $fy_start_date;
        $ledgerTree->added_on = date('Y-m-d H:i:s');
        $ledgerTree->status = 1;
        $ledgerTree->created_by = $userId;
        $ledgerTree->entity_type = 'ledger';
        $ledgerTree->behaviour = $behaviour;
        $ledgerTree->defined_by = 'user';

        if($ledgerTree->save()) {
            return $ledgerTree->ledger_account_id;
        } else {
            return false;
        }
    }

    /**
     * Add ledger transaction entry for the asset category in the chsone_ledger_transactions table
     *
     * @param int $assetCategoryId
     * @param string $assetCategoryName
     * @param string $assetCategoryType
     * @param float $openingBalance
     * @param int $socId
     * @param int $userId
     * @return bool
     */
    private function addLedgerTransaction($ledgerAccountId, $assetCategoryId, $assetCategoryName, $assetCategoryType, $openingBalance, $socId, $userId)
    {
        // fetch fy_start_date from soc_account_financial_year_master table where soc_id = $socId and limit 1
        $socAccountFinancialYearMaster = SocAccountFinancialYearMaster::where('soc_id', $socId)->first();
        $fy_start_date = $socAccountFinancialYearMaster->fy_start_date ?? '';


        $ledgerTransaction = new ChsoneLedgerTransaction();

        $ledgerTransaction->soc_id = $socId;
        $ledgerTransaction->transaction_date = $fy_start_date;
        $ledgerTransaction->ledger_account_id = $ledgerAccountId;
        $ledgerTransaction->ledger_account_name = $assetCategoryName.'-'.$assetCategoryId;
        $ledgerTransaction->voucher_reference_id = 0;
        $ledgerTransaction->transaction_type = 'dr';
        $ledgerTransaction->transaction_amount = $openingBalance;
        $ledgerTransaction->txn_from_id = 0;
        $ledgerTransaction->memo_desc = 'entry for opening balance';
        $ledgerTransaction->is_opening_balance = 1;
        $ledgerTransaction->is_reconciled = 0;
        $ledgerTransaction->is_cancelled = 0;
        $ledgerTransaction->created_by = $userId;
        $ledgerTransaction->added_on = date('Y-m-d H:i:s');

        if($ledgerTransaction->save()) {
            return true;
        } else {
            return false;
        }
    }
}
