<?php

namespace App\Console\Commands\Actions\Account\Transaction;

use Illuminate\Console\Command;
use App\Console\Commands\Action;
use App\Models\Tenants\ChsoneAsset;

class fetchEditAssetDataDataSource extends Action
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'datasource:fetchEditAssetData {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Fetch Edit Asset Data Data Source';

    /**
     * Execute the console command.
     */
    public function apply()
    {
        $soc_id = $this->input['company_id'];
        $assets_id = $this->input['assets_id'];

        // check asset id is exists
        $assetCheck = ChsoneAsset::where('soc_id', $soc_id)
                            ->where('assets_id', $assets_id)
                            ->first();
        
        if (!$assetCheck) {
            $this->status = false;
            $this->statusCode = 400;
            $this->message = "No details found for this asset id / asset id is invalid";
            return;
        }

        $this->data = $assetCheck;
    }
}
