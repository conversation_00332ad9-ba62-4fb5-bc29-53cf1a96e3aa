<?php

namespace App\Console\Commands\Actions\Account\Transaction;

use App\Models\Tenants\ChsoneGrpLedgerTree;
use App\Models\Tenants\ChsoneLedgerTransaction;
use Illuminate\Console\Command;
use App\Console\Commands\Action;

class ListTransactionMonthlyAction extends Action
{
    /**
     * The name and signature of the console command.
     *
     * @var string

     */
    protected $signature = 'datasource:listTransactionMonthly {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'List Monthly Transaction';

    protected $formatterKey = ['id'];



    protected $mapper = [
        'txn_id' => 'id',
    ];
    public function apply()
    {
        $ledgerId = $this->input['id'];
        $month = "";
        $year = "";

        //condition added in case transaction list is yearly
        if (isset($this->input['filters']['financial_year']) && !empty($this->input['filters']['financial_year'])) {
            $monthlyTxn = false;

            // Set month and year to empty if financial_year is set
            $this->input['filters']['month'] = '';
            $this->input['filters']['year'] = '';
            $year = explode("-", $this->input['filters']['financial_year'])[0];
            $month = 13;
        } else {
            $monthlyTxn = true;

            //conditions added for monthly transaction on month and year
            //if month and year selected from dropdown
            if (isset($this->input['filters']['month'])) {
                $month = $this->input['filters']['month'];
            }
            elseif (isset($this->input['month']) && $this->input['month'] != 0) {
                $month = $this->input['month'];
                $this->input['filters']['month'] = $month;
            } else {
                $month = date("m");
            }

            if (isset($this->input['filters']['year'])) {
                $year = $this->input['filters']['year'];
            }
            elseif (isset($this->input['year']) && $this->input['year'] != 0) {
                $year = $this->input['year'];
                $this->input['filters']['year'] = $year;
            } else {
                $year = date("Y");
            }
        }

        $arrayMaster = [];
        $arrayMaster['accountMaster'] = $this->tenantDB()->table('soc_account_start_master')
            ->first();

        $arrayMaster['financialMaster'] = $this->tenantDB()->table('soc_account_financial_year_master')
            ->get()->toArray();

        $unitStartDateYear = [];

        $fyStartFrom = $arrayMaster['accountMaster']->fy_start_from;

        if ($monthlyTxn) {
            if ($fyStartFrom == "01-01") {
                for ($i = 0; $i < count($arrayMaster['financialMaster']); $i++) {
                    $currentFyStart = [];
                    $currentFyStart = explode("-", $arrayMaster['financialMaster'][$i]['fy_start_date']);
                    $unitStartDateYear = $currentFyStart[0];
                }
            } else {
                for ($i = 0; $i < count($arrayMaster['financialMaster']); $i++) {
                    $currentFyStart = [];
                    $currentFyEnd = [];
                    $currentFyStart = explode("-", $arrayMaster['financialMaster'][$i]->fy_start_date);
                    $currentFyEnd = explode("-", $arrayMaster['financialMaster'][$i]->fy_end_date);

                    if ($currentFyStart[0] == $currentFyEnd[0]) {
                        $currentFyStart[0] = $currentFyStart[0] - 1;
                    }
                    $unitStartDateYear = $currentFyStart[0] . "-" . $currentFyEnd[0];
                }
            }
        }

        //Get the Transaction list monthly/yearly
        if ($monthlyTxn) {
            $transactionList = $this->getMonthlyTransaction($ledgerId, $month,  $year);
        } else {
            $transactionList = $this->getMonthlyTransaction($ledgerId, 13,  $year);
        }

        $firstrow = array(
            'id' => 0,
            'transaction_date' => ($transactionList['opening'] !== 0) ? $this->getDisplayDate($transactionList['opening_date']) : '',
            'month' => '',
            'year' => '',
            'name' => '',
            'counter_ledger_account_name' => '',
            'counter_ledger_account_id' => '',
            'is_opening_balance' => 0,
            'transaction_amount' => 0,
            'transaction_type' => '',
            'memo_desc' => ($transactionList['opening'] !== 0) ? 'Opening Balance' : 'Brought forward',
            'transaction_amount_debit' => ($transactionList['opening'] !== 0 && $transactionList['opening_type'] != 'cr') ? abs($transactionList['opening']) : 0,
            'transaction_amount_credit' => ($transactionList['opening'] !== 0 && $transactionList['opening_type'] != 'dr') ? abs($transactionList['opening']) : 0,
            'balance' => ($transactionList['opening'] !== 0) ? $transactionList['opening'] : $transactionList['forwarded']
        );


        if (count($transactionList['transactions']) > 0) {
            //Get the Ledger names
            $transactionList['ledgerNames'] = $this->getCounterEntryLedgerName($this->input['company_id'], $transactionList['transactions']);
            // $transactionList['transactions'] = array_merge($firstrow, $transactionList['transactions']);
            $transactionList['transactions'] = $transactionList['transactions']->prepend($firstrow);
            $final = $transactionList['transactions']->toArray();
            $final = json_decode(json_encode($final), true);
            // dd($final);
            $summary = array(
                'id' => 0,
                'transaction_date' => '',
                'month' => '',
                'year' => '',
                'brought_forward' => round($transactionList['balance'] + ($transactionList['totalcr'] ?? 0) - ($transactionList['totaldr'] ?? 0), 2),
                'name' => '',
                'counter_ledger_account_name' => '',
                'counter_ledger_account_id' => '',
                'is_opening_balance' => 0,
                'transaction_amount' => 0,
                'memo_desc' => 'Total',
                'total_transaction_amount_debit'  => $transactionList['totalcr'] !== 0 ? round(abs($transactionList['totalcr']), 2) : 0,
                'total_ransaction_amount_credit' => $transactionList['totaldr'] !== 0 ? round(abs($transactionList['totaldr']), 2) : 0,
                'total_balance' => round($transactionList['balance'], 2),
            );

            $result = [
                [$summary],
                $final
            ];

            $this->data = $result;
        }else{
            $this->data  =  [];
        }



        // $this->data=$this->format($this->data);
    }

    public function getCounterEntryLedgerName($soc_id, $all_ledger = [])
    {
        $all_ledger = json_decode(json_encode($all_ledger), true);
        // dd($all_ledger);
        $ledger_array = [];

        foreach ($all_ledger as $t) {
            $ledger_account_name = '';

            if (empty($t['txn_from_id'])) {
                // Find the counter entry ledger name
                $counter_entry = ChsoneLedgerTransaction::where('soc_id', $soc_id)
                    ->where('txn_from_id', $t['id'])
                    ->get()->toArray();

                if (count($counter_entry) > 0) {
                    $ledger_account_name = $counter_entry[0]['ledger_account_name'];

                    if (empty($counter_entry[0]['ledger_account_name'])) {
                        $counter_ledger_details = ChsoneGrpLedgerTree::where('ledger_account_id', $counter_entry[0]['ledger_account_id'])
                            ->get()->toArray();

                        if ($counter_ledger_details) {
                            $ledger_account_name = $counter_ledger_details[0]['ledger_account_name'];
                        }
                    }

                    $ledger_array[] = [
                        'name' => 'To ' . $ledger_account_name,
                        'ledger_id' => $counter_entry[0]['ledger_account_id'],
                    ];
                } else {
                    $ledger_array[] = [
                        'name' => 'For ' . $t['ledger_account_name'],
                        'ledger_id' => $t['ledger_account_id'],
                    ];
                }
            } else {
                // Find the entry ledger name for this counter entry
                $counter_entry = ChsoneLedgerTransaction::where('soc_id', $soc_id)
                    ->where('txn_id', $t['txn_from_id'])
                    ->get()->toArray();

                if (count($counter_entry) > 0) {
                    $ledger_account_name = $counter_entry[0]['ledger_account_name'];

                    if (empty($ledger_account_name)) {
                        $counter_ledger_details = ChsoneGrpLedgerTree::where('ledger_account_id', $counter_entry[0]['ledger_account_id'])
                            ->get()->toArray();

                        if ($counter_ledger_details) {
                            $ledger_account_name = $counter_ledger_details[0]['ledger_account_name'];
                        }
                    }

                    $ledger_array[] = [
                        'name' => 'By ' . $ledger_account_name,
                        'ledger_id' => $counter_entry[0]['ledger_account_id'],
                    ];
                } else {
                    $ledger_array[] = [
                        'name' => 'For ' . $t['ledger_account_name'],
                        'ledger_id' => $t['ledger_account_id'],
                    ];
                }
            }
        }
        return $ledger_array;
    }

    public function getMonthlyTransaction($ledgerId, $month, $year)
    {
        $parent = 'Top';
        $openingDate = '';
        $totalCR = 0;
        $totalDR = 0;

        $groupLedgerTree = $this->tenantDB()
            ->table("chsone_grp_ledger_tree")
            ->where('ledger_account_id', $ledgerId)
            ->first();

        if ($groupLedgerTree->parent_id != 0) {
            $parent = $this->getParentLedgerAccount($groupLedgerTree->parent_id);
        }

        // $startDate = $this->calculateStartDate($month, $year);
        // $endDate = $this->calculateEndDate($month, $year);

        $startyear = $year;
        if ($month < 4) {
            $startyear = $year - 1;
        }
        $isthirteen = 0;
        if ($month == 13) {
            $month = 4;
            $isthirteen = 1;
        }

        $startDate = date("Y-m-01", strtotime("$year-$month-01"));
        if ($isthirteen) {
            $month = 3;
            $year++;
        }
        $endDate = date("Y-m-t", strtotime("$year-$month-01"));

        $ledgerTxn = $this->getOpeningBalanceTransaction($ledgerId, $startDate, $endDate);

        if (empty($ledgerTxn)) {
            $forward = $this->calculateForwardBalance($ledgerId, $startDate, $year, $groupLedgerTree);
            $ledgerTxn = 0;
        } else {
            $forward = 0;
            $openingDate = $ledgerTxn->transaction_date;
            $openingType = $ledgerTxn->transaction_type;
            $ledgerTxn = ($ledgerTxn->transaction_type == $groupLedgerTree->nature_of_account) ?
                $ledgerTxn->transaction_amount : ($ledgerTxn->transaction_amount * -1);
        }
        $transactionResult = $this->getTransactions($ledgerId, $startDate, $endDate, $groupLedgerTree);
        $balance = $ledgerTxn + $forward;

        foreach ($transactionResult as &$transaction) {

            // check if transaction_type type is cr or dr according to the transaction_type set transaction_amount
            if ($transaction->transaction_type == 'dr') {
                $transaction->transaction_amount_credit = 0;
                $transaction->transaction_amount_debit = $transaction->transaction_amount;
            } elseif ($transaction->transaction_type == 'cr') {
                $transaction->transaction_amount_credit = $transaction->transaction_amount;
                $transaction->transaction_amount_debit = 0;
            }

            $balance = $transaction->balance = ($groupLedgerTree->nature_of_account == $transaction->transaction_type)
                ? $balance + $transaction->transaction_amount : $balance - $transaction->transaction_amount;

            $totalCR += ($transaction->transaction_type == "cr") ? $transaction->transaction_amount : 0;
            $totalDR += ($transaction->transaction_type == "dr") ? $transaction->transaction_amount : 0;
        }

        $total = $this->calculateTotalBalance($ledgerId, $year, $groupLedgerTree);

        return [
            "ledger"       => $groupLedgerTree->ledger_account_name,
            "opening"      => $ledgerTxn,
            'opening_type' => isset($openingType) ? $openingType : '',
            'opening_date' => $openingDate ? $openingDate : '',
            'forwarded'    => $forward,
            'transactions' => $transactionResult,
            'parent'       => $parent,
            'totalcr'      => $totalCR,
            'totaldr'      => $totalDR,
            'balance'      => $balance,
            "total"        => $total
        ];
    }

    private function getParentLedgerAccount($parentId)
    {
        return $this->tenantDB()
            ->table("chsone_grp_ledger_tree")
            ->where('ledger_account_id', $parentId)
            ->first()->ledger_account_name;
    }

    // private function calculateStartDate($month, $year)
    // {
    //     if ($month < 4) {
    //         $year--;
    //     }

    //     return date("Y-m-01", strtotime("$year-$month-01"));
    // }

    // private function calculateEndDate($month, $year)
    // {
    //     if ($month == 13) {
    //         $month = 4;
    //         $year++;
    //     }

    //     return date("Y-m-t", strtotime("$year-$month-01"));
    // }

    private function getOpeningBalanceTransaction($ledgerId, $startDate, $endDate)
    {
        $ledgerTxn = $this->tenantDB()
            ->table("chsone_ledger_transactions")
            ->where("ledger_account_id", $ledgerId)
            ->where("transaction_date", ">=", $startDate)
            ->where("transaction_date", "<=", $endDate)
            ->where("is_opening_balance", "!=", 0)
            ->first();
        return $ledgerTxn;
    }

    private function calculateForwardBalance($ledgerId, $startDate, $year, $groupLedgerTree)
    {
        $ledgerTxnCR = $this->tenantDB()
            ->table("chsone_ledger_transactions")
            ->where("ledger_account_id", $ledgerId)
            ->where("transaction_date", "<", $startDate)
            ->where("transaction_date", ">=", "$year-04-01")
            ->where("transaction_type", "cr")
            ->sum('transaction_amount');

        $ledgerTxnDR = $this->tenantDB()
            ->table("chsone_ledger_transactions")
            ->where("ledger_account_id", $ledgerId)
            ->where("transaction_date", "<", $startDate)
            ->where("transaction_date", ">=", "$year-04-01")
            ->where("transaction_type", "dr")
            ->sum('transaction_amount');

        if ($groupLedgerTree->nature_of_account == "cr") {
            $forward = $ledgerTxnCR - $ledgerTxnDR;
        } else {
            $forward = $ledgerTxnDR - $ledgerTxnCR;
        }
        return $forward;
    }

   
    private function getTransactions($ledgerId, $startDate, $endDate, $groupLedgerTree)
    {
        $transactions = $this->tenantDB()->table('chsone_ledger_transactions as lTxn')
            ->selectRaw('
                CASE WHEN lTxn.ledger_account_id = ? THEN mTxn.ledger_account_name ELSE lTxn.ledger_account_name END AS counter_ledger_account_name,
                CASE WHEN lTxn.ledger_account_id = ? THEN mTxn.ledger_account_id ELSE lTxn.ledger_account_id END AS counter_ledger_account_id,
                CASE WHEN lTxn.ledger_account_id = ? THEN lTxn.txn_id ELSE mTxn.txn_id END AS id,
                CASE WHEN lTxn.ledger_account_id = ? THEN lTxn.txn_from_id ELSE mTxn.txn_from_id END AS txn_from_id,
                CASE WHEN lTxn.ledger_account_id = ? THEN lTxn.ledger_account_id ELSE mTxn.ledger_account_id END AS ledger_account_name,
                CASE WHEN lTxn.ledger_account_id = ? THEN lTxn.transaction_type ELSE mTxn.transaction_type END AS transaction_type,
                lTxn.transaction_amount,
                lTxn.memo_desc,
                lTxn.transaction_date,
                MONTH(lTxn.transaction_date) AS month,
                YEAR(lTxn.transaction_date) AS year,
                lTxn.soc_id,
                lTxn.voucher_reference_id,
                lTxn.voucher_reference_number,
                lTxn.voucher_type,
                lTxn.is_cancelled,
                lTxn.is_opening_balance,
                lTxn.is_reconciled,
                lTxn.payment_mode,
                lTxn.payment_reference,
                lTxn.other_reference_id,
                lTxn.created_by,
                lTxn.added_on,
                lTxn.value_date
            ', [$ledgerId, $ledgerId, $ledgerId, $ledgerId, $ledgerId, $ledgerId])
            ->join('chsone_ledger_transactions as mTxn', 'lTxn.txn_id', '=', 'mTxn.txn_from_id')
            ->where(function ($query) use ($ledgerId) {
                $query->where('lTxn.ledger_account_id', $ledgerId)
                    ->orWhere('mTxn.ledger_account_id', $ledgerId);
            })
            ->whereBetween('lTxn.transaction_date', [$startDate, $endDate])
            ->where('lTxn.is_opening_balance', 0)
            ->where('lTxn.transaction_amount', '!=', 0)
            // ->where('lTxn.is_cancelled', '=', 0)
            ->orderBy('lTxn.transaction_date', 'asc')
            ->orderBy('counter_ledger_account_name', 'asc')
            ->get();

        return $transactions;
    }

    private function calculateTotalBalance($ledgerId, $year, $groupLedgerTree)
    {
        $totalCR = $this->tenantDB()
            ->table("chsone_ledger_transactions")
            ->where("ledger_account_id", $ledgerId)
            ->where("transaction_date", ">=", "$year-04-01")
            ->where("transaction_type", "cr")
            ->sum('transaction_amount');

        $totalDR = $this->tenantDB()
            ->table("chsone_ledger_transactions")
            ->where("ledger_account_id", $ledgerId)
            ->where("transaction_date", ">=", "$year-04-01")
            ->where("transaction_type", "dr")
            ->sum('transaction_amount');

        if ($groupLedgerTree->nature_of_account == "cr") {
            $total = $totalCR - $totalDR;
        } else {
            $total = $totalDR - $totalCR;
        }
        return $total;
    }
}
