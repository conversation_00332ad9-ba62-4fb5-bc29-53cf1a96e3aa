<?php

namespace App\Console\Commands\Actions\Account\Transaction;

use Illuminate\Console\Command;
use App\Console\Commands\Action;
use App\Models\Tenants\ChsoneAssetsCategory;

class deleteAssetSettingsDataSource extends Action
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'datasource:deleteAssetSettings {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Delete Asset Settings Data Source';

    /**
     * Execute the console command.
     */
    public function apply()
    {
        $soc_id = $this->input['company_id'];
        $assets_category_id = $this->input['assets_category_id'];

        // check asset category exists or not
        $assetCategoryExists = ChsoneAssetsCategory::where('assets_categories_id', $assets_category_id)->where('soc_id', $soc_id)->first();

        if (empty($assetCategoryExists)) {
            $this->status = 'error';
            $this->message = "No data found for asset category id or invalid asset category id.";
            $this->statusCode = 400;
            return;
        }

        // check if asset category is already deleted or not
        if ($assetCategoryExists->status == 0) {
            $this->status = 'error';
            $this->message = "Asset category is already deleted.";
            $this->statusCode = 400;
            return;
        }

        // delete asset category :- set status to 0 in ChsoneAssetsCategory table for the given asset category id
        $assetCategoryExists->status = 0;
        $assetCategoryExists->save();

        $this->status = 'success';
        $this->message = "Asset category deleted successfully.";
        $this->statusCode = 200;
    }
}
