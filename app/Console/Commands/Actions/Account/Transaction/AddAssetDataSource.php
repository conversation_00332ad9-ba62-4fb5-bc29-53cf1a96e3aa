<?php

namespace App\Console\Commands\Actions\Account\Transaction;

use Illuminate\Console\Command;
use App\Console\Commands\Action;
use App\Models\Tenants\SocAccountFinancialYearMaster;
use App\Models\Tenants\IncomeInvoiceSetting;
use App\Models\Tenants\ChsoneGrpLedgerTree;
use App\Models\Tenants\ChsoneLedgerTransaction;
use Carbon\Carbon;
use App\Models\Tenants\ChsoneInvestmentsMaster;
use App\Models\Tenants\ChsoneAssetsCategory;
use App\Models\Tenants\ChsoneTaxCategory;
use App\Models\Tenants\ChsoneTaxClass;
use App\Models\Tenants\ChsoneVendorsMaster;
use App\Models\Tenants\TaxExemption;
use App\Models\Tenants\ChsoneBankRecoMaster;
use App\Models\Tenants\ChsoneAccountsMaster;
use App\Models\Tenants\ChsoneAsset;
use Illuminate\Support\Facades\Config;

class AddAssetDataSource extends Action
{
    protected $constants;

    public function __construct() {
        parent::__construct();
        $this->constants = Config::get("constants");
    }

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'datasource:AddAsset {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Add Asset Data Source';

    /**
     * Execute the console command.
     */
    public function apply()
    {
        $request = $this->input;
        $soc_id = $this->input['company_id'];
        $user_id = $this->input['user_id'] ?? 0;
        $txn_type = $request['vendor_bill_type_purchase'];
        $assets_name = $request["assets_name"];
        $assets_tag_number = $request["assets_tag_number"];
        $assets_vendor_id = $request["assets_vendor_id"];
        $assets_categories_id = $request["assets_categories_id"] ?? $request["id"];
        $assets_location = $request["assets_location"];
        $assets_purchase_date = Carbon::parse($request["assets_purchase_date"])->format('Y-m-d');
        $assets_cost = $request["assets_cost"];
        $assets_cost_currency = "Rs";
        $purchase_type = $request["vendor_bill_type_purchase"];

        // first check asset name is already exists for this vendor
        $asset = ChsoneAsset::where('soc_id', $soc_id)
                            ->where('assets_name', $assets_name)
                            ->where('assets_vendor_id', $assets_vendor_id)
                            ->first();
        if ($asset) {
            $this->status = false;
            $this->statusCode = 400;
            $this->message = "Asset name is already exists for this vendor";
            return;
        }

        // check vendor ledger id is exists or not
        $vendor = ChsoneVendorsMaster::where('soc_id', $soc_id)
                    ->where('vendor_id', $assets_vendor_id)
                    ->first();
        if (!$vendor) {
            $this->status = false;
            $this->statusCode = 400;
            $this->message = "Vendor id is invalid";
            return;
        }

        if ($txn_type == 'bank') {
            $to_name = $request["card_ledger"];
        } elseif ($txn_type == 'cash') {
            $to_name = $request["cash_ledger"];
        } else {
            $to_name = $this->getVendorLedgerId($request["assets_vendor_id"]);
        }

        $asset_category_ledger_info = array('soc_id' => $soc_id, 'ledger_id' => $assets_categories_id);
        $asset_category_ledger = $this->checkledgerExistNew($asset_category_ledger_info);

        $vendor_ledger_info = array('soc_id' => $soc_id, 'ledger_id' => $to_name);
        $vendor_ledger = $this->checkledgerExistNew($vendor_ledger_info);

        $id = $this->manipulateAsset($assets_name, $assets_tag_number, $assets_vendor_id, $assets_categories_id, $assets_location, $assets_purchase_date, $assets_cost, $assets_cost_currency, '', '', $purchase_type, $to_name);
        if ($id) {
            $arrLedgerTransactionData = array();
            $arrLedgerTransactionData['soc_id'] = $soc_id;
            $arrLedgerTransactionData['user_id'] = $user_id;
            $arrLedgerTransactionData['voucher_type'] = '';
            $arrLedgerTransactionData['from_ledger_id'] = $asset_category_ledger['recieving_ledger_id'];
            $arrLedgerTransactionData['to_ledger_id'] = $vendor_ledger['recieving_ledger_id'];
            $arrLedgerTransactionData['transaction_date'] = date('Y-m-d');
            $arrLedgerTransactionData['transaction_amount'] = $assets_cost;
            $arrLedgerTransactionData['narration'] = 'Asset';
            $arrLedgerTransactionData['from_ledger_name'] = $asset_category_ledger['receiver_name'];
            $arrLedgerTransactionData['to_ledger_name'] = $vendor_ledger['receiver_name'];
            $arrLedgerTransactionData['payment_reference'] = '';
            $arrLedgerTransactionData['transaction_type'] = '';
            $arrLedgerTransactionData['mode_of_payment'] = $purchase_type;
            $arrLedgerTransactionData['other_payment_ref'] = '';
            $arrLedgerEntry = $this->transactionLedgerEntry($arrLedgerTransactionData);
        }
        
        if ($arrLedgerEntry['success'] == true && isset($arrLedgerEntry['transaction_id'])) {
            $this->status = true;
            $this->statusCode = 200;
            $this->message = "Asset is added successfully";

            // Activity Log Addition (Asset purchased)
            // $notification_title = 'New asset purchased';
            // $socUsers = $this->session->get("socUsers");
            // $notification_data = ['title' => $notification_title,
            //     'desc' => "New asset " . $this->request->getPost('assets_name') . " is purchased for the " . ucwords($this->session->get('soc_name')),
            //     'module' => 'expense_tracker',
            //     'scope' => 'group',
            //     'role' => 'admin',
            //     'to_be_notified' => 1,
            //     'pk_id' => $id,
            //     'date_time' => date("d-M-Y H:i"),
            //     'soc_id' => $this->session->get('auth')['soc_id'],
            //     'all_staff' => [],
            //     'all_admin' => $socUsers['socAdminUsers']];
            // \ChsOne\Helper\CommonHelper::addNotification($notification_data);
            // End activity Log Addition (Asset purchased)
        } else {
            $this->status = false;
            $this->statusCode = 400;
            $this->message = "Error in adding asset";
        }
    }

    public function getVendorLedgerId($id)
    {
        $socId = $this->input['company_id'];

        $vendor = ChsoneVendorsMaster::where('soc_id', $socId)
                    ->where('vendor_id', $id)
                    ->select('vendor_ledger_id')
                    ->first();

        return $vendor->vendor_ledger_id;
    }

    public function checkledgerExistNew($data=array())
    {
        $arrClinetLedgerDetails = array();
        $query = ChsoneGrpLedgerTree::where('soc_id', $data['soc_id']);
        
        if (isset($data['ledger_name']) && !empty($data['ledger_name'])) {
            $query->where('context', $data['context'])
                  ->where('entity_type', "ledger")
                  ->where('ledger_account_name', $data['ledger_name']);
    
            if (isset($data['behaviour']) && !empty($data['behaviour'])) {
                $query->where('behaviour', $data['behaviour']);
            }
        } elseif (isset($data['ledger_id']) && !empty($data['ledger_id'])) {
            $query->where('ledger_account_id', $data['ledger_id']);
        }

        $objBookerLedger = $query->first();

        //an outsider has booked for the society. Check if exists by name; otherwise, create new.
        if(!empty($objBookerLedger)) {
            $arrClinetLedgerDetails['recieving_ledger_id'] = $objBookerLedger->ledger_account_id;
            $arrClinetLedgerDetails['receiver_name'] = $objBookerLedger->ledger_account_name;
        }
        elseif(!empty($data['group_name']))
        {
            $name         = $data['ledger_name'];
            $entity_type  = "ledger";
            $grp_ledg_id  = "";
            $parent       = $this->getParentGroupId($data, TRUE);
            
            if(empty($parent->ledger_account_id))
            {
                return $arrClinetLedgerDetails;
            }
            $parent_group = $parent->ledger_account_id;
            
            $ledger_id    = $this->manipulate($name, $entity_type, $grp_ledg_id , $parent_group, "", '', 0, '', '', '', '', '', 0, $data['soc_id']);
            if ($ledger_id) {
                $arrClinetLedgerDetails['recieving_ledger_id'] = $ledger_id;
                $arrClinetLedgerDetails['receiver_name'] = $data['ledger_name'];
            }
        }
        unset($objBookerLedger);
        return $arrClinetLedgerDetails;
    }

    public function getParentGroupId($arrData)
    {
        $parent = ChsoneGrpLedgerTree::where('soc_id', $arrData['soc_id'])
            ->where('ledger_account_name', $arrData['group_name'])
            ->where('entity_type', "group")
            ->where('behaviour', $arrData['behaviour'])
            ->where('context', $arrData['context'])
            ->first();
        if ($parent) {
            return $parent;
        } else {
            return false;
        }
    }

    public function manipulate($name, $entity_type = ENTITY_TYPE_GROUP, $grp_ledg_id = "", $parent_group = 0, $behaviour = "", $ledg_id = '', $opening_balance = 0, $update_led_id = '', $ledger_start_date = '', $ledger_type = '', $context = '', $txn_id = 0, $soc_id = 0, $is_reco = 0)
    {
        $config = Config::get("constants");
        if(empty($soc_id)){
            $soc_id = $this->input['company_id'];
        }
        $dup = $this->_checkGroupNameDuplication($parent_group, $name, $ledg_id, $update_led_id);
        
        $arrFYDetail = $this->getCurrentFinancialYear(array('soc_id' => $soc_id));
        $arrFYDetail = json_decode(json_encode($arrFYDetail), true);
        $ledger_start_date = $arrFYDetail['fy_start_date'];
        if ($dup == 0) {
            $behaviour = trim(strtolower($behaviour));

            $grp_ledg_tree = new ChsoneGrpLedgerTree();

            if ($ledg_id) {
                $grp_ledg_tree = $this->getLedger($ledg_id);
            }

            $grp_ledg_tree->entity_type = $entity_type;
            $grp_ledg_tree->soc_id = $soc_id;
            $grp_ledg_tree->ledger_account_name = $name;
            $grp_ledg_tree->ledger_start_date = $ledger_start_date;
            $grp_ledg_tree->context_ref_id = 0;

            if (!empty($ledger_type) && $ledger_type != "NULL" && $ledger_type != '') {
                $grp_ledg_tree->operating_type = $ledger_type;
            } else {
                $grp_ledg_tree->operating_type = '';
            }

            if (!empty($grp_ledg_id)) {
                $grp_ledg_tree->ledger_account_id = $grp_ledg_id;
            }

            if ($update_led_id != '') {
                $grp_ledg_tree->ledger_account_id = $update_led_id;
            }
            if (!empty($parent_group)) {
                $grp_ledg_tree->parent_id = $parent_group;
                $ledger_props = $this->_getLedgerProps($parent_group);
            } else {
                $grp_ledg_tree->parent_id = 0;
            }

            if (!empty($behaviour)) {
                $grp_ledg_tree->behaviour = $behaviour;
                $grp_ledg_tree->nature_of_account = $config['nature_account'][$behaviour];
                $grp_ledg_tree->report_head = $config['report_head'][$behaviour];
            } else {
                $grp_ledg_tree->behaviour = $ledger_props["behaviour"];
                $grp_ledg_tree->nature_of_account = $ledger_props["nature_of_account"];
                $grp_ledg_tree->report_head = $ledger_props["report_head"];
            }

            if (!empty($context) && $context != '') {
                $grp_ledg_tree->context = $context;
            } else {
                $grp_ledg_tree->context = $ledger_props["context"];
            }
            $grp_ledg_tree->defined_by = "user";
            $grp_ledg_tree->status = 1;
            $grp_ledg_tree->added_on = date("Y-m-d H:i:s");
            $grp_ledg_tree->created_by = $this->input['user_id'] ?? 0;

            unset($grp_ledg_tree->ledger_account_id);

            if ($grp_ledg_tree->save()) {
                //Opening balance should not go in case on Income Expense ledger
                if (!in_array(strtolower($grp_ledg_tree->behaviour), array("income", "expense"))) {

                    $txn_date = $ledger_start_date;
                    $narration = 'entry for opening balance';
                    $txn_id = $this->addTxn($grp_ledg_tree->ledger_account_id, $opening_balance, $narration, $txn_date, "", $grp_ledg_tree->nature_of_account, "", "", "", "", $name, $is_opning = 1, $is_reco);
                }

                return $grp_ledg_tree->ledger_account_id;
            } else {
                
                return false;
            }
        } else {
            return "DUP" . $dup;
        }
    }

    private function _checkGroupNameDuplication($parent, $name, $ledg_id = "", $update_led_id = "")
    {
        $soc_id = $this->input['company_id'];

        $bind[1] = strtolower(trim($name));
        $ledgers_count = 0;
        if ($ledg_id) {
            $ledgers_count = ChsoneGrpLedgerTree::where("soc_id", $soc_id)->where('ledger_account_name', strtolower(trim($name)))->where('ledger_account_id', '!=', $ledg_id)->count();
        }
        if ($update_led_id) {
            $ledgers_count = ChsoneGrpLedgerTree::where("soc_id", $soc_id)->where('ledger_account_name', strtolower(trim($name)))->where('ledger_account_id', '!=', $update_led_id)->count();
        }
        
        return $ledgers_count;
    }

    public function getCurrentFinancialYear($data=array())
    {
        $arrFYDetail = array();
        $arrFYDetail = SocAccountFinancialYearMaster::where("soc_id", $data['soc_id'])->where("confirmed", 0)->orderBy("account_closing_id", "asc")->first();
        
        if(!empty($arrFYDetail))
        {
            $arrFYDetail = $arrFYDetail->toArray();
        }
        return $arrFYDetail;
    }

    public function getLedger($ledger_acct_id, $return_object = 1)
    {
        //fetch ledger details from chsone_grp_ledger_tree whose ledger_account_id = $ledger_acct_id
        $ledger = ChsoneGrpLedgerTree::where("ledger_account_id", $ledger_acct_id)->first();

        if ($ledger) {
            if ($return_object) {
                return $ledger;
            } else {
                return $ledger->toArray();
            }
        } else {
            return false;
        }
    }

    public function _getLedgerProps($parent_group)
    {
        $array = array();

        if (is_numeric($parent_group)) {
            $ledger_props = ChsoneGrpLedgerTree::select('ledger_account_name', 'nature_of_account', 'behaviour', 'report_head', 'context')
            ->where('ledger_account_id', $parent_group)
            ->first();
        }

        $array = $ledger_props;
        return $array;
    }

    public function addTxn($ledger_id, $txn_amt, $narration, $txn_date, $voucher_type = VOUCHER_JOURNAL, $txn_type = "", $from_txn_id = "", $pay_mode = "", $pay_ref = "", $opening_balance = "", $ledger_name = "", $is_opning = 1, $is_reco = 0)
    {
        return $this->_addTransaction($is_reco, $ledger_id, $txn_amt, $narration, $txn_date, $voucher_type, $txn_type, $from_txn_id, $pay_mode, $pay_ref, $ledger_name, $is_opning = 1);
    }

    private function _addTransaction($is_reco = 0, $ledger_id, $txn_amt, $narration, $txn_date, $voucher_type = VOUCHER_JOURNAL, $txn_type = "", $from_txn_id = "", $pay_mode = "", $pay_ref = "", $name = "", $is_opning = "", $other_recp_ref = "", $soc_id = "", $created_by = "", $voucher_reference_id = "", $voucher_reference_number = "", $is_cancelled = 0)
    {
        $txn = new ChsoneLedgerTransaction();
        if (empty($soc_id)) {
            $soc_id = $this->input['company_id'];
        }

        if (!empty($txn_type) || $txn_type != "") {
            $type_txn = $txn_type;
        } else {
            if ($from_txn_id) {
                $mode     = "to";
                $type_txn = "cr";
            } else {
                $mode     = "from";
                $type_txn = "dr";
            }
        }

        //echo "s_opning".$is_opning;exit;
        if ($is_opning == 1) {

            $txn_entrt = ChsoneLedgerTransaction::where('soc_id', $soc_id)
            ->where('is_opening_balance', 1)
            ->where('ledger_account_id', $ledger_id)
            ->where('transaction_date', $txn_date)
            ->first();

            $txn->value_date = $txn_date;
            $txn->txn_id = (isset($txn_entrt->txn_id)  && !empty($txn_entrt->txn_id)) ? $txn_entrt->txn_id : '';
            if (!empty($txn->txn_id)) {
                $txn = ChsoneLedgerTransaction::findFirst("txn_id = " . $txn->txn_id);
            }
        }

        $txn->soc_id = $soc_id;
        $txn->transaction_date = $txn_date;
        $txn->ledger_account_id = $ledger_id;
        $txn->ledger_account_name = $name;
        $txn->voucher_type = $voucher_type;
        $txn->transaction_type = $type_txn; //$ledger_name_obj->nature_of_account;//$txn_type;
        $txn->payment_mode = $pay_mode;
        $txn->payment_reference = $pay_ref;
        $txn->transaction_amount = (is_null($txn_amt)) ? $txn->transaction_amount : $txn_amt;
        $txn->other_reference_id = $other_recp_ref;
        $txn->txn_from_id = $from_txn_id;
        $txn->memo_desc = $narration;
        $txn->is_opening_balance = $is_opning;
        $txn->is_reconciled = $is_reco;
        /*Added New Column in transaction*/
        $txn->voucher_reference_number = $voucher_reference_number;
        $txn->voucher_reference_id = $voucher_reference_id;
        $txn->is_cancelled = $is_cancelled;
        /*End New Column in transaction*/
        $txn->created_by = $this->input['user_id'] ?? 0;
        if (isset($created_by) && !empty($created_by)) {
            $txn->created_by = $created_by;
        }
        $txn->added_on = date("Y-m-d H:i:s");

        if ($txn->save()) {
            return $txn->txn_id;
        } else {
            print_r($txn->getMessages);
        }
        //echo "<pre>";print_r($a->getMessages);exit;
        return false;
    }

    public function manipulateAsset($name, $tag_number, $vendor_id, $category, $location, $purchase_date, $cost, $currency ="", $id = "", $status, $purchase_type, $to_name)
    {
        $asset = new ChsoneAsset();
        $soc_id = $this->input['company_id'];

        // Check for duplicate assets
        $dupli  = $this->_checkDuplicateAsset($name, $vendor_id, $id);
        
        if ($dupli == 0) {
             // Find or create the asset
            $asset = $id ? ChsoneAsset::where('soc_id', $soc_id)
                ->where('assets_id', $id)
                ->first() : new ChsoneAsset();

            if ($id) {
                $asset->updated_on = now();
            } else {
                $asset->added_on = now();
            }   
            
            // Get vendor information
            $vendor = $this->getSingleVendor($vendor_id);
            $now    = date("Y-m-d"); 
            
            // Update asset status based on vendor's start and end date
            if ($now >= $vendor['start_date'] && $now <= $vendor['end_date']) {
                $asset->status = 1; // Active
            } else {
                $asset->status = 3; // Inactive
            }
            
            $asset->assets_name          = $name;
            $asset->assets_tag_number    = $tag_number;   
            $asset->assets_vendor_id     = $vendor_id; 
            $asset->assets_categories_id = $category; 
            $asset->assets_location      = $location; 
            $asset->assets_purchase_date = $purchase_date; 
            $asset->assets_cost          = $cost; 
            $asset->assets_cost_currency = $currency;
            $asset->vendor_bill_type_purchase = $purchase_type;
            $asset->purchase_ledger_id   = $to_name;
            $asset->soc_id               = $soc_id;       

            // Save the asset and return the result
            if ($asset->save()) {
                return $asset->assets_id;
            } else {
                return false;
            }
        } else {
            return "Exists";
        }
    }

    public function getSingleVendor($vendor_id, $linear = true, $soc_id = null)
    {
        // If the authenticated user has a `soc_id`, use it
        if (!empty($this->input['company_id'])) {
            $soc_id = $this->input['company_id'];
        }

        // Build the query
        $query = ChsoneVendorsMaster::where('soc_id', $soc_id)
                                    ->where('vendor_id', $vendor_id);

        // Execute the query and get the result
        $result = $query->first();

        if ($linear) {
            return $result ? $result->toArray() : [];
        }
        return $result;
    }

    public function _checkDuplicateAsset($name, $vendor, $id = null)
    {
        $soc_id = $this->input['company_id'];

        // Build the query
        $query = ChsoneAsset::where('soc_id', $soc_id)
                                ->whereRaw('TRIM(assets_name) = ?', [trim($name)])
                                ->where('assets_vendor_id', $vendor);

        // Add condition to exclude a specific asset ID, if provided
        if ($id) {
            $query->where('assets_id', '!=', $id);
        }

        // Count the results
        $count = $query->count();

        return $count;
    }

    public function transactionLedgerEntry($data=array())
    {
        $arrResponse = array();
        if(empty(trim($data['from_ledger_id'])) || empty(trim($data['to_ledger_id'])))
        {
            $arrResponse = array('error'=>true);
            return $arrResponse;
        }
        if(empty($data['transaction_date']))
        {
            $data['transaction_date'] = date('Y-m-d');
        }

        $transaction_id = $this->executeTransactionEntry($data); 
        if($transaction_id){
            $arrResponse = array('success'=>true, 'transaction_id'=>$transaction_id);
        }else{
            $arrResponse = array('error'=>true);
        }
        
        return $arrResponse;
    }

    public function executeTransactionEntry($data)
    {
        $vouchers_array = [];
        if ((strpos($data['voucher_type'], '_') !== false) && (strpos($data['voucher_type'], '_') > 0)) {
            $vouchers_array = explode('_', $data['voucher_type']);
            $data['voucher_type'] = $vouchers_array[0];
        }

        $data['is_opening'] = 0;
        $data['transaction_from_id'] = '';
        $data['transaction_from_id'] = $this->_addTransactionEntry($data);

        if (!empty($data['transaction_from_id'])) {
            if (count($vouchers_array) >= 2) {
                $data['voucher_type'] = $vouchers_array[1];
            }
            if ($this->_addTransactionEntry($data)) {
                return $data['transaction_from_id'];
            } else {
                return false;
            }
        } else {
            return false;
        }
    }

    private function _addTransactionEntry($data)
    {
        $txn = new ChsoneLedgerTransaction();
        if (!empty($data['transaction_from_id'])) {
            $mode = "to";
            $data['transaction_type'] = "cr";
            $ledger_id = $data['to_ledger_id'];
            $ledger_name = $data['to_ledger_name'];
        } else {
            $mode = "from";
            $data['transaction_type'] = "dr";
            $ledger_id = $data['from_ledger_id'];
            $ledger_name = $data['from_ledger_name'];
        }
        //echo "s_opning".$is_opning;exit;
        if (!empty($data['is_opening']) && $data['is_opening'] == 1) {
            $txn_entrt = ChsoneLedgerTransaction::where('soc_id', $data['soc_id'])
                ->where('is_opening_balance', 1)
                ->where('ledger_account_id', $ledger_id)
                ->first();

            $txn->txn_id = (isset($txn_entrt->txn_id)  && !empty($txn_entrt->txn_id)) ? $txn_entrt->txn_id : '';
        }
        $txn->soc_id = $data['soc_id'];
        $txn->transaction_date = $data['transaction_date'];
        $txn->ledger_account_id = $ledger_id;
        $txn->ledger_account_name = $ledger_name;
        $txn->voucher_type = $data['voucher_type'];
        $txn->transaction_type = $data['transaction_type']; //$ledger_name_obj->nature_of_account;//$txn_type;
        $txn->payment_mode = $data['mode_of_payment'];
        $txn->payment_reference = $data['payment_reference'];
        $txn->transaction_amount = $data['transaction_amount'];
        $txn->other_reference_id = $data['other_payment_ref'];
        $txn->txn_from_id = $data['transaction_from_id'];
        $txn->memo_desc = $data['narration'];
        $txn->is_opening_balance = $data['is_opening'];
        $txn->is_reconciled = (!empty($data['is_reconciled'])) ? $data['is_reconciled'] : 0;;
        $txn->created_by = (!empty($data['user_id'])) ? $data['user_id'] : 0;
        $txn->added_on = date("Y-m-d H:i:s");
        $txn->voucher_reference_number = (!empty($data['voucher_reference_number'])) ? $data['voucher_reference_number'] : '';
        $txn->voucher_reference_id = (!empty($data['voucher_reference_id'])) ? $data['voucher_reference_id'] : '';
        $txn->is_cancelled = (!empty($data['is_cancelled'])) ? 1 : 0;

        if ($txn->save()) {
            return $txn->txn_id;
        } else {
            return false;
        }
    }
}
