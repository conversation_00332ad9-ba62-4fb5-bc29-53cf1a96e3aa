<?php

namespace App\Console\Commands\Actions\Account\Transaction;

use App\Console\Commands\Action;
use Illuminate\Console\Command;

class ViewVoucherRecieptDataSource extends Action
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'datasource:ViewVoucherReciept {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Get the voucher reciept';

    protected $formatter = [
        'soc_formatted_address' => '',
        'rupeesInWord' => '',
        'voucher_id' => '',
        'voucher_type' => '',
        'voucher_sub_type' => '',
        'voucher_transaction_date' => '',
        'narration' => '',
        'reference' => '',
        'status' => '',
        'perticulars' => [],
        'totalAmount' => '',
    ];

    protected $formatterByKeys = [
        'voucher_id',
    ];

    protected $mapper = [];

    /**
     * Execute the console command.
     */
    public function apply()
    {
        $voucher_id = $this->input['id'];
        $arrVoucherDetail = $this->tenantDB()->table('chsone_voucher_master AS V')
            ->select(
                'V.voucher_id',
                'V.type',
                'V.sub_type',
                'V.transaction_date',
                'V.from_ledger_account_id',
                'V.from_ledger_account_name',
                'V.to_ledger_account_id',
                'V.to_ledger_account_name',
                'V.amount',
                'V.reference',
                'V.narration',
                'V.status',
                'V.created_by',
                'V.created_date',
                'V.updated_by',
                'V.updated_date'
            )
            ->where('V.status', 1)
            ->where('V.voucher_id', $voucher_id)
            ->orderBy('V.voucher_id', 'ASC')
            ->first();

        $arrVoucherDetail = json_decode(json_encode($arrVoucherDetail), true);
        $totalamount = $arrVoucherDetail['amount'];

        // // if the voucher type is debit and sub type is income or expense
        if ($arrVoucherDetail['type'] == 'debit' && in_array($arrVoucherDetail['sub_type'], ['income', 'expense'])) {
            $arrVoucherTransactionDetail = $this->getVoucherTransaction(array('soc_id' => $this->input['company_id'], 'arrPostData' => $arrVoucherDetail[0]));
            $arrDebitLedgerDetail = $this->getDebitVoucherThirdLedger(array('arrLedgerDetail' => $arrVoucherTransactionDetail, 'arrVoucherDetail' => $arrVoucherDetail));
            $totalamount = $arrVoucherDetail[0]['amount'] + $arrDebitLedgerDetail['transaction_amount'];
        }

        if (is_array($arrVoucherDetail)) {
            $arrVoucherDetail['amount'] = number_format($arrVoucherDetail['amount'] ?? 0, 2, '.', '');
        }
                $arrVoucherDetail['totalAmountInWords'] = $this->convertNumberToWords($totalamount) . ' ' . 'rupees';

        $this->data = $arrVoucherDetail;
    }

    public function getVoucherTransaction($soc_id, $data = [])
    {
        $arrPostData = $data['arrPostData'];

        // Check if voucher_id is provided
        if (!empty($arrPostData['voucher_id'])) {
            // Build the query using Laravel's query builder
            $query = $this->tenantDB()->table('chsone_ledger_transactions AS me')
                ->select('*')
                ->where('me.voucher_reference_id', $arrPostData['voucher_id'])
                ->where('me.soc_id', $soc_id)
                ->where('me.transaction_date', $arrPostData['transaction_date'])
                ->where('me.voucher_type', $arrPostData['type'])
                ->where('me.transaction_amount', $arrPostData['amount'])
                ->get();

            // Convert the result to an array if not empty
            return $query->isNotEmpty() ? $query->toArray() : [];
        }

        // Return false if no voucher_id is provided
        return false;
    }

    public function getDebitVoucherThirdLedger($data = [])
    {
        if (!empty($data['arrLedgerDetail'])) {
            return collect($data['arrLedgerDetail'])->first(function ($eachTransactionDetail) use ($data) {
                return !in_array($eachTransactionDetail['ledger_account_id'], [
                    $data['arrVoucherDetail']['from_ledger_account_id'],
                    $data['arrVoucherDetail']['to_ledger_account_id'],
                ]);
            });
        }
        return false;
    }
}
