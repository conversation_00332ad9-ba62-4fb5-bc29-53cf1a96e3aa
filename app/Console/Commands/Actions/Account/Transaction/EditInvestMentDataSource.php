<?php

namespace App\Console\Commands\Actions\Account\Transaction;

use Illuminate\Console\Command;
use App\Console\Commands\Action;
use App\Models\Tenants\SocAccountFinancialYearMaster;
use App\Models\Tenants\IncomeInvoiceSetting;
use App\Models\Tenants\ChsoneGrpLedgerTree;
use App\Models\Tenants\ChsoneLedgerTransaction;
use Carbon\Carbon;
use App\Models\Tenants\ChsoneInvestmentsMaster;
use App\Models\Tenants\ChsoneAssetsCategory;
use App\Models\Tenants\ChsoneTaxCategory;
use App\Models\Tenants\ChsoneTaxClass;
use App\Models\Tenants\ChsoneVendorsMaster;
use App\Models\Tenants\TaxExemption;
use App\Models\Tenants\ChsoneBankRecoMaster;
use App\Models\Tenants\ChsoneAccountsMaster;
use App\Models\Tenants\ChsoneAsset;


class EditInvestMentDataSource extends Action
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'datasource:EditInvestMent {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Post New Ledger Data Source';

    /**
     * Execute the console command.
     */
    public function apply()
    {
        $arrPostData = request()->all();
        $type = request()->input('type');
        $year = request()->input('year');
        $soc_id = request()->input('company_id', '');
        $account_number = request()->input('investment_account_number');
        $bank_name = request()->input('investment_bank_name');
        $bank_branch = request()->input('investment_bank_branch');
        $bank_address = request()->input('investment_bank_address');
        $bank_city = request()->input('investment_bank_city');
        $bank_ifsc = request()->input('investment_bank_ifsc');
        $amount = request()->input('amount');
        $payment_mode = request()->input('payment_mode');
        $cash_ledger = request()->input('cash_ledger');
        $bank_ledger = request()->input('bank_ledger');
        $start_date = request()->input('start_date');
        $maturity_date = request()->input('maturity_date');

        if ($amount > 0) {
            $behaviour = 'asset';
            $nature = 'dr';
            $entity_type = 'ledger';
            $ledger_account_name = strtoupper("$type-$bank_name-$account_number");
            $parent = ($type == 'fd') ? 7 : 7;
            $mode = 'add';
            $ledger_start_date = Carbon::parse($start_date)->format('Y-m-d'); // Assuming getDatabaseDate also returns a formatted date
            $ledger_type = 'direct';

            $investment = ChsoneInvestmentsMaster::where('account_number', $account_number)
            ->first();
            $investment->account_number = $account_number;
            $investment->type = $type;
            $investment->bank_name = $bank_name;
            $investment->branch = $bank_branch;
            $investment->bank_address = $bank_address;
            $investment->bank_city = $bank_city;
            $investment->bank_ifsc = $bank_ifsc;
            $investment->ledger_account_id = 0;
            $investment->status = 1;
            $investment->start_date = $ledger_start_date;
            $investment->maturity_date = Carbon::parse($maturity_date)->format('Y-m-d');
            $investment->created_on = $ledger_start_date;
            $investment->created_by = '2894'; //auth()->user()->id; // Assuming you're using Laravel's authentication
            $investment->updated_on = $ledger_start_date;
            $investment->updated_by = $investment->created_by;
            // Save the investment
            if($investment->save()) {
                $this->message = 'Investment has been upated successfully';
            } else {
                $this->message = 'Failed to add investment';
            }
        }
    }
}
