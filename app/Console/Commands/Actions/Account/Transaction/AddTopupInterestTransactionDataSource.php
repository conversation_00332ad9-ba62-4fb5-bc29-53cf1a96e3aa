<?php

namespace App\Console\Commands\Actions\Account\Transaction;

use Illuminate\Console\Command;
use App\Console\Commands\Action;
use App\Console\Commands\Actions\Account\viewBankAccountsListDataSource;
use App\Console\Commands\Actions\Income\creditNoteAddDataSource;
use App\Console\Commands\Actions\Income\CreditNoteDataSource;
use App\Models\Tenants\SocAccountFinancialYearMaster;
use App\Models\Tenants\IncomeInvoiceSetting;
use App\Models\Tenants\ChsoneGrpLedgerTree;
use App\Models\Tenants\ChsoneLedgerTransaction;
use Carbon\Carbon;
use App\Models\Tenants\ChsoneInvestmentsMaster;
use App\Models\Tenants\ChsoneAssetsCategory;
use App\Models\Tenants\ChsoneTaxCategory;
use App\Models\Tenants\ChsoneTaxClass;
use App\Models\Tenants\ChsoneVendorsMaster;
use App\Models\Tenants\TaxExemption;
use App\Models\Tenants\ChsoneBankRecoMaster;
use App\Models\Tenants\ChsoneAccountsMaster;
use Illuminate\Support\Facades\Route;

class AddTopupInterestTransactionDataSource extends Action
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'datasource:AddTopupInterestTransaction {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Add Interest And Topup Data Source';

    /**
     * Execute the console command.
     */
    public function apply()
    {
        try {
            $postedValues = request()->post();
            $type = $postedValues["type"] ?? null;
            $to_ledger = $postedValues["to_ledger"] ?? $postedValues["ledger_account_id"] ?? 0;
            $transaction_date = $postedValues["transaction_date"] ?? null;
            $memo_desc = $postedValues["memo_desc"] ?? $postedValues["memo_description"] ?? '';
            $amount = $postedValues["amount"] ?? null;
            $soc_id = $this->input["company_id"] ?? null;
            if ($type == 'interest') {
                $from_ledger = 0;
                $arrFromLedgerDetails = $this->checkledgerExistNew(array('ledger_name' => 'Investments Interest', 'context' => 'income', 'group_name' => 'Indirect Income', 'behaviour' => 'income'));
            } elseif ($type == 'topup') { // From bank/cash to deposit
                $payment_mode = $postedValues["payment_mode"] ?? null;
                if($payment_mode == 'cash') {
                    $ledgerId = ChsoneGrpLedgerTree::where('ledger_account_name', 'Cash In Hand')->where('entity_type', 'ledger')->first();
                    $from_ledger = $ledgerId->ledger_account_id;
                } else {
                    $from_ledger = $postedValues["bank_account"] ?? 0;
                }
                if ($from_ledger == '') {
                    $res = [
                        'status' => 'error',
                        'status_code' => 400,
                        'message' => '',
                        'data' => [],
                        'meta' => ['errors' => ['payment_type' => 'From account not selected']],
                        'pointer' => []
                    ];
                }
                $arrFromLedgerDetails = $this->checkledgerExistNew(array('ledger_id' => $from_ledger));
            }
            if (isset($arrFromLedgerDetails) && $arrFromLedgerDetails['receiver_name'] == 'Cash in Hand') {
                $addBankTransactionObj = new AddBankCashTransactionDataSource();
                $init_from_ledger_bal = str_replace(',', '', $addBankTransactionObj->cashLedgerBal($from_ledger, $soc_id));

                $to_ledger_name = $this->checkledgerExistNew(array('ledger_id' => $to_ledger));
                $init_to_ledger_bal = str_replace(',', '', $addBankTransactionObj->cashLedgerBal($to_ledger, $soc_id));
                if (
                    strtolower($to_ledger_name['receiver_name']) == 'cash in hand' && $init_to_ledger_bal < 1 ||
                    strtolower($to_ledger_name['receiver_name']) == 'cash in hand' && $init_to_ledger_bal < $amount
                ) {
                    $response = array(
                        'error' => 1,
                        'msg' => 'Your Cash in Hand amount is: ' . $init_to_ledger_bal
                    );
                    echo json_encode($response);
                    exit;
                }
            }

            $arrToLedgerDetails = $this->checkledgerExistNew(array('ledger_id' => $to_ledger));
            // Fix the typo in the key name - using 'recieving_ledger_id' instead of 'receiving_ledger_id'
            $arrLedgerTransactionData['from_ledger_id'] = $arrToLedgerDetails['recieving_ledger_id'];
            //receiving_ledger_id
            $arrLedgerTransactionData['to_ledger_id'] = $arrFromLedgerDetails['recieving_ledger_id'] ?? '';
            $arrLedgerTransactionData['from_ledger_name'] = $arrToLedgerDetails['receiver_name'] ?? '';
            $arrLedgerTransactionData['to_ledger_name'] = $arrFromLedgerDetails['receiver_name'] ?? '';

            $arrLedgerTransactionData['transaction_date'] = $transaction_date;
            $arrLedgerTransactionData['transaction_amount'] = $amount;
            $arrLedgerTransactionData['narration'] = ($memo_desc != '') ? $memo_desc : 'Interest amount added on date ' . $transaction_date;

            $arrLedgerTransactionData['payment_reference'] = '';
            $arrLedgerTransactionData['transaction_type'] = '';
            $arrLedgerTransactionData['mode_of_payment'] = '';
            $arrLedgerTransactionData['other_payment_ref'] = '';
            $arrLedgerTransactionData['soc_id'] = $soc_id;
            $creditNoteAddDataSourceObj = new creditNoteAddDataSource();
            $arrLedgerEntry = $creditNoteAddDataSourceObj->transactionLedgerEntry($arrLedgerTransactionData);
            $addBankTransactionObj = new viewBankAccountsListDataSource();

            $this->status = 'success';
            $this->statusCode = 200;
            // if ($this->input['type'] == 'topup') {
            //     $this->message = "Your latest balance is " . $addBankTransactionObj->cashLedgerBal($to_ledger, $soc_id);
            // } else {
            //     $this->message = "Transaction Successful";
            // }
            $this->message = "Transaction Successful";
            $this->message .= "Your latest balance is " . $addBankTransactionObj->cashLedgerBal($to_ledger, $soc_id);
            //dd($arrLedgerTransactionData);
        } catch (\Exception $e) {
            $this->status = 'error';
            $this->statusCode = 500;
            $this->message = 'Error in apply() method at line ' . $e->getLine() . ' in file ' . basename($e->getFile()) . ': ' . $e->getMessage();
        }
    }

    public function checkLedgerExistNew($data = [])
    {
        try {
            $arrClientLedgerDetails = [];
            $condition = [];
            $data['soc_id'] = $this->input['company_id'];
            if (isset($data['ledger_name']) && !empty($data['ledger_name'])) {
                $condition['soc_id'] = $this->input['company_id'];
                $condition['context'] = $data['context'];
                $condition['entity_type'] = 'ledger';
                $condition['ledger_account_name'] = $data['ledger_name'];

                if (isset($data['behaviour']) && !empty($data['behaviour'])) {
                    $condition['behaviour'] = $data['behaviour'];
                }
            } elseif (isset($data['ledger_id']) && !empty($data['ledger_id'])) {
                $condition['soc_id'] = $this->input['company_id'];
                $condition['ledger_account_id'] = $data['ledger_id'];
            }

        // Find the ledger using the conditions
        $objBookerLedger = ChsoneGrpLedgerTree::where($condition)->first();

        if ($objBookerLedger) {
            $arrClientLedgerDetails['recieving_ledger_id'] = $objBookerLedger->ledger_account_id;
            $arrClientLedgerDetails['receiver_name'] = $objBookerLedger->ledger_account_name;
        } elseif (!empty($data['group_name'])) {
            $name = $data['ledger_name'];
            $entity_type = 'ledger';
            $grp_ledg_id = "";

            $objGroupLedg = new ChsoneGrpLedgerTree();
            $parent = $objGroupLedg->getParentGroupId($data, true);

            if (empty($parent->ledger_account_id)) {
                return $arrClientLedgerDetails;
            }

            $parent_group = $parent->ledger_account_id;

            $ledger_id = $objGroupLedg->manipulate(
                $name,
                $entity_type,
                $grp_ledg_id,
                $parent_group,
                "",
                '',
                0,
                '',
                '',
                '',
                '',
                '',
                0,
                $this->input['company_id']
            );

            if ($ledger_id) {
                $arrClientLedgerDetails['recieving_ledger_id'] = $ledger_id;
                $arrClientLedgerDetails['receiver_name'] = $data['ledger_name'];
            }
        }
        return $arrClientLedgerDetails;
        } catch (\Throwable $e) {
            dd([
                'message' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine()
            ]);
        }
    }

    public function checkledgerExistNewVery($ledgerName, $parentId, $context, $behaviour)
    {
        try {
            $grpLedgerTree = new ChsoneGrpLedgerTree();
            $grpLedgerTreeData = $grpLedgerTree->where("ledger_account_name", $ledgerName)
                                                ->where("behaviour", $behaviour)
                                                ->where("parent_id", $parentId)
                                                ->where("entity_type", "ledger")
                                                ->first();

            if (!empty($grpLedgerTreeData)) {
                return $grpLedgerTreeData['ledger_account_id'];
            } else {
                $ledgerId = $this->dataManipulation($ledgerName, "ledger", "", $parentId, $behaviour, "", 0, "", "", null, $context);
                if ((gettype($ledgerId) != 'boolean') && (strpos('DUP', $ledgerId) === false)) {
                    return $ledgerId;
                }
            }
            return false;
        } catch (\Exception $e) {
            throw new \Exception('Error in checkledgerExistNewVery() method at line ' . $e->getLine() . ' in file ' . basename($e->getFile()) . ': ' . $e->getMessage(), $e->getCode());
        }
    }

    public function cashLedgerBal($ledger_id, $soc_id, $start_date = '', $end_date = '')
    {
        try {
            if (empty($ledger_id)) {
                $postedValues = request()->post();
                $ledger_id = $postedValues["from_ledger"];
                $to_ledger = $postedValues["to_ledger"];
                $transaction_date = $postedValues["transaction_date"];
                $fetch_list = isset($postedValues['fetch_list']) ? $postedValues['fetch_list'] : 'N';

                $arrCurrentFYDetailObj = new SocAccountFinancialYearMaster();
                $soc_id = $postedValues['company_id'];
                $arrCurrentFYDetail = $arrCurrentFYDetailObj->getCurrentFYDetail($postedValues['company_id']);
                $arrFYDetail = json_decode(json_encode($arrCurrentFYDetail), true);
                $start_date = $arrFYDetail['fy_start_date'];
                $end_date = $arrFYDetail['fy_end_date'];
            }
            $ledgerTransactionObj = new ChsoneLedgerTransaction();

            $filter_criteria = array();
            if (empty($start_date)) {
                $translist = $ledgerTransactionObj->getLedgerTransactions($soc_id, $ledger_id, $filter_criteria, 'ledger');
            } else {
                $translist = $ledgerTransactionObj->getLedgerTransactionsByFY(array('soc_id' => $soc_id, 'ledger_id' => $ledger_id, 'criteria' => $filter_criteria, 'ledger' => 'ledger', 'start_date' => $start_date, 'end_date' => $end_date));
            }
            if (isset($fetch_list) && $fetch_list == 'Y') {
                return response()->json($translist, 200);
            }
            $translist = json_decode(json_encode($translist), true);

            $ChsoneGrpLedgerTreeObj = new ChsoneGrpLedgerTree();
            $ledger = $ChsoneGrpLedgerTreeObj->getLedger($ledger_id, 1);
            if (!empty($ledger)) {
                $ledger = json_decode(json_encode($ledger), true);
            }

            $overall_balance = 0.00;
            $overall_nature = $ledger['nature_of_account'];
            if (!empty($translist['transaction_total'])) {
                if ($translist['transaction_total'][0]['credit_amount'] > $translist['transaction_total'][0]['debit_amount']) {
                    $overall_nature = 'cr';
                    $overall_balance = $translist['transaction_total'][0]['credit_amount'] - $translist['transaction_total'][0]['debit_amount'];
                } elseif ($translist['transaction_total'][0]['credit_amount'] < $translist['transaction_total'][0]['debit_amount']) {
                    $overall_nature = 'dr';
                    $overall_balance = $translist['transaction_total'][0]['debit_amount'] - $translist['transaction_total'][0]['credit_amount'];
                }
            }
            $overall_balance = ($overall_nature != $ledger['nature_of_account']) ? (-1) * $overall_balance : $overall_balance;
            return number_format($overall_balance, 2);
        } catch (\Exception $e) {
            throw new \Exception('Error in cashLedgerBal() method at line ' . $e->getLine() . ' in file ' . basename($e->getFile()) . ': ' . $e->getMessage(), $e->getCode());
        }
    }

    public function addTransactionEntry($data)
    {
        try {
            $txn = new ChsoneLedgerTransaction();
            if (!empty($data['transaction_from_id'])) {
                $mode = MODE_TO;
                $data['transaction_type'] = "cr";
                $ledgerId = $data['to_ledger_id'];
                $ledgerName = $data['to_ledger_name'];
            } else {
                $mode = MODE_FROM;
                $data['transaction_type'] = "dr";
                $ledgerId = $data['from_ledger_id'];
                $ledgerName = $data['from_ledger_name'];
            }

            if (!empty($data['is_opening']) && $data['is_opening'] == 1) {
                $txnEntry = ChsoneLedgerTransaction::where('soc_id', $data['soc_id'])
                    ->where('is_opening_balance', 1)
                    ->where('ledger_account_id', $ledgerId)
                    ->first();

                $txn->txn_id = $txnEntry ? $txnEntry->txn_id : '';
            }

            $txn->soc_id = $data['soc_id'];
            $txn->transaction_date = $data['transaction_date'];
            $txn->ledger_account_id = $ledgerId;
            $txn->ledger_account_name = $ledgerName;
            $txn->voucher_type = $data['voucher_type'];
            $txn->transaction_type = $data['transaction_type'];
            $txn->payment_mode = $data['mode_of_payment'];
            $txn->payment_reference = $data['payment_reference'];
            $txn->transaction_amount = $data['transaction_amount'];
            $txn->other_reference_id = $data['other_payment_ref'];
            $txn->txn_from_id = isset($data['transaction_from_id']) ? $data['transaction_from_id'] : null;
            $txn->memo_desc = $data['narration'];
            $txn->is_opening_balance = isset($data['is_opening']) ? $data['is_opening'] : 0;
            $txn->is_reconciled = !empty($data['is_reconciled']) ? $data['is_reconciled'] : 0;
            $txn->created_by = $this->input['user_id'] ?? 0;
            $txn->added_on = now();
            $txn->voucher_reference_number = !empty($data['voucher_reference_number']) ? $data['voucher_reference_number'] : '';
            $txn->voucher_reference_id = !empty($data['voucher_reference_id']) ? $data['voucher_reference_id'] : '';
            $txn->is_cancelled = !empty($data['is_cancelled']) ? 1 : 0;

            if ($txn->save()) {
                return $txn->txn_id;
            } else {
                return false;
            }
        } catch (\Exception $e) {
            throw new \Exception('Error in addTransactionEntry() method at line ' . $e->getLine() . ' in file ' . basename($e->getFile()) . ': ' . $e->getMessage(), $e->getCode());
        }
    }
}
