<?php

namespace App\Console\Commands\Actions\Account\Transaction;

use App\Console\Commands\Action;
use App\Http\Traits\MongoTraits;
use Illuminate\Support\Facades\Config;

class ListTransactionDetailDataSource extends Action
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */


    protected $constants;

    public function __construct()
    {
        parent::__construct();
        $this->constants = Config::get("constants");
    }
    protected $signature = 'datasource:listTransactionDetail {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Get the voucher reciept';
    use MongoTraits; // Use the MongoTraits trait in this class



    protected $formatterByKeys = [
        'voucher_id',
    ];

    protected $mapper = [];

    /**
     * Execute the console command.
     */

    public function apply()
    {
        // actually id is txn_id and ledger_id is ledger_id
        $txn_id = $this->input['id'];

        // first fetch the details from chsone_ledger_transactions table where txn_id = $txn_id
        $from_transactions = $this->tenantDB()->table('chsone_ledger_transactions')
        ->selectRaw('
            txn_id as from_txn_id,
            txn_from_id,
            transaction_date,
            ledger_account_id as from_ledger_id,
            ledger_account_name as from_ledger_account_name,
            transaction_amount as from_transaction_amount,
            transaction_type as from_transaction_type,
            memo_desc as from_memo_desc')
        ->where('txn_id', $txn_id)
        ->where('is_opening_balance', 0)
        ->where('transaction_amount', '!=', 0)
        ->first();
        
        if (!$from_transactions) {
            $this->message = 'No transactions found';
            $this->status = "error";
            $this->statusCode = 400;
            return;
        }

        if ($from_transactions->txn_from_id == 0) {
            $txn_from_id = $txn_id;
            // Now fetch the details from chsone_ledger_transactions table where txn_from_id = $txn_from_id
            $to_transactions = $this->tenantDB()->table('chsone_ledger_transactions')
            ->selectRaw('
                txn_id as to_txn_id,
                txn_from_id as to_txn_from_id,
                transaction_date,
                ledger_account_id as to_ledger_id,
                ledger_account_name as to_ledger_account_name,
                transaction_amount as to_transaction_amount,
                transaction_type as to_transaction_type,
                memo_desc as to_memo_desc')
            ->where('txn_from_id', $txn_from_id)
            ->where('is_opening_balance', 0)
            ->where('transaction_amount', '!=', 0)
            ->first();
        } else {
            $txn_from_id = $from_transactions->txn_from_id;
            // Now fetch the details from chsone_ledger_transactions table where txn_from_id = $txn_from_id
            $to_transactions = $this->tenantDB()->table('chsone_ledger_transactions')
            ->selectRaw('
                txn_id as to_txn_id,
                txn_from_id as to_txn_from_id,
                transaction_date,
                ledger_account_id as to_ledger_id,
                ledger_account_name as to_ledger_account_name,
                transaction_amount as to_transaction_amount,
                transaction_type as to_transaction_type,
                memo_desc as to_memo_desc')
            ->where('txn_id', $txn_from_id)
            ->where('is_opening_balance', 0)
            ->where('transaction_amount', '!=', 0)
            ->first();
        }

        $transactions = array_merge((array) $from_transactions, (array) $to_transactions);

        // need to add only month and year to the transactions
        if ($transactions) {
            $transactions['month'] = date('m', strtotime($transactions['transaction_date']));
            $transactions['month'] = number_format($transactions['month'], 0, '', ',');
            $transactions['year'] = date('Y', strtotime($transactions['transaction_date']));
        }

        if (!$transactions) {
            $this->message = 'No transactions found';
            $this->status = "error";
            $this->statusCode = 400;
            return;
        }

        $this->data = $transactions;
    }
}
