<?php

namespace App\Console\Commands\Actions\Account\Transaction;

use Illuminate\Console\Command;
use App\Console\Commands\Action;
use App\Models\Tenants\ChsoneAssetsCategory;
use App\Models\Tenants\ChsoneGrpLedgerTree;
use App\Models\Tenants\ChsoneLedgerTransaction;
use App\Models\Tenants\SocAccountFinancialYearMaster;

class UpdateAssetSettingDataSource extends Action
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'datasource:updateAssetSetting {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Update a single asset setting by ID';

    /**
     * Execute the console command.
     */
    public function apply()
    {
        try {
            $soc_id = $this->input['company_id'];
            $user_id = $this->input['user_id'] ?? 0;
            $assets_category_id = $this->input['assets_category_id'] ?? null;
            $assets_category_type = $this->input['assets_categories_type'] ?? null;
            
            if (empty($assets_category_id)) {
                $this->status = 'error';
                $this->message = 'Asset category ID is required.';
                $this->statusCode = 400;
                return;
            }

            // Find the asset category by ID
            $assetCategory = ChsoneAssetsCategory::where('assets_categories_id', $assets_category_id)
                ->where('soc_id', $soc_id)
                ->first();

            if (!$assetCategory) {
                $this->status = 'error';
                $this->message = 'Asset category not found.';
                $this->statusCode = 404;
                return;
            }

            // Update the asset category with new values if provided
            if (isset($this->input['assets_categories_name'])) {
                $assetCategory->assets_categories_name = $this->input['assets_categories_name'];
            }
            
            if (isset($this->input['opening_balance'])) {
                $assetCategory->asset_opening_balance = $this->input['opening_balance'];
            }
            
            if ($assets_category_type) {
                $assetCategory->assets_categories_type = $assets_category_type;
            }
            
            $assetCategory->updated_on = now();
            $assetCategory->save();

            // Get the updated record
            $updatedCategory = ChsoneAssetsCategory::find($assets_category_id);

            
            $this->data = [];
            $this->status = 'success';
            $this->statusCode = 200;
            
        } catch (\Exception $e) {
            $this->status = 'error';
            $this->message = 'Failed to update asset category: ' . $e->getMessage();
            $this->statusCode = 500;
        }
    }
}
