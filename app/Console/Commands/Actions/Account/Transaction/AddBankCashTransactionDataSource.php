<?php

namespace App\Console\Commands\Actions\Account\Transaction;

use Illuminate\Console\Command;
use App\Console\Commands\Action;
use App\Models\Tenants\SocAccountFinancialYearMaster;
use App\Models\Tenants\IncomeInvoiceSetting;
use App\Models\Tenants\ChsoneGrpLedgerTree;
use App\Models\Tenants\ChsoneLedgerTransaction;
use Carbon\Carbon;
use App\Models\Tenants\ChsoneInvestmentsMaster;
use App\Models\Tenants\ChsoneAssetsCategory;
use App\Models\Tenants\ChsoneTaxCategory;
use App\Models\Tenants\ChsoneTaxClass;
use App\Models\Tenants\ChsoneVendorsMaster;
use App\Models\Tenants\TaxExemption;
use App\Models\Tenants\ChsoneBankRecoMaster;
use App\Models\Tenants\ChsoneAccountsMaster;
use App\Models\Tenants\ChsoneVoucherMaster;

class AddBankCashTransactionDataSource extends Action
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'datasource:AddBankCashTransaction {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Post New Bank Cash Transaction Data Source';

    /**
     * Execute the console command.
     */
    public function apply()
    {
        $postedValues = request()->post();
        $from_ledger = $this->input['from_ledger'] ?? '';
        $soc_id = $this->input['company_id'] ?? '';
        $to_ledger = $this->input["to_ledger"] ?? '';
        $bank_name = $this->input["bank_name"] ?? '';
        $transaction_date = $this->input["transaction_date"] ?? '';
        $memo_desc = $this->input["memo_desc"] ?? '';
        $amount = $this->input["amount"] ?? '';
        $arrCurrentFYDetailObj = new SocAccountFinancialYearMaster();
        $ledgerObj = new ChsoneGrpLedgerTree();
        $arrCurrentFYDetail = $arrCurrentFYDetailObj->getCurrentFYDetail($soc_id);
        $type = isset($this->input['type']) ? $this->input['type'] : 'N';
        $arrFYDetail = json_decode(json_encode($arrCurrentFYDetail), true);

        /*if ($transaction_date <= $arrFYDetail['fy_start_date'] || $transaction_date >= $arrFYDetail['fy_end_date']) {
            return response()->json('Transaction date should be within Financial Year', 400);
        }*/
        $from_ledger = $this->tenantDB()->table('chsone_grp_ledger_tree')
            ->where('soc_id', $soc_id)
            ->where('ledger_account_name', 'like', '%' . $from_ledger . '%')
            // ->where('status', 1)
            ->first()
            ->ledger_account_id;

        $init_from_ledger_bal = str_replace(',', '', $this->cashLedgerBal($from_ledger, $soc_id, $arrFYDetail['fy_start_date'], $arrFYDetail['fy_end_date']));
        $init_to_ledger_bal = str_replace(',', '', $this->cashLedgerBal($to_ledger, $soc_id, $arrFYDetail['fy_start_date'], $arrFYDetail['fy_end_date']));
        $from_ledger_name = json_decode(json_encode($ledgerObj->getLedger($from_ledger)), true);
        $to_ledger_name = json_decode(json_encode($ledgerObj->getLedger($to_ledger)), true);

        // For withdrawals ('w'), check if the bank account (to_ledger in user input) has sufficient balance
        // For deposits ('d'), check if Cash in Hand (from_ledger in user input) has sufficient balance
        if ($type == 'd') {
            // Withdrawal: money comes FROM bank (to_ledger) TO cash (from_ledger), so check bank balance
            if ($init_to_ledger_bal < $amount) {
                $response = array(
                    'status' => 'error',
                    'statusCode' => 400,
                    'message' => 'Insufficient balance in ' . $to_ledger_name['ledger_account_name'] . '. Available: ' . $init_to_ledger_bal
                );
                echo json_encode($response);
                exit;
            }
        } else {
            // Deposit: money comes FROM cash (from_ledger) TO bank (to_ledger), so check cash balance
            if (strtolower($from_ledger_name['ledger_account_name']) == 'cash in hand' && ($init_from_ledger_bal < 1 || $init_from_ledger_bal < $amount)) {
                $response = array(
                    'status' => 'error',
                    'statusCode' => 400,
                    'message' => 'Your Cash in Hand amount is: ' . $init_from_ledger_bal
                );
                echo json_encode($response);
                exit;
            }
        }

        // For deposits ('d'), money flows FROM cash TO bank (as sent by user)
        // For withdrawals ('w'), money flows FROM bank TO cash (need to swap)
        if ($type == 'd') {
            // Withdrawal: swap from and to ledgers
            $arrLedgerTransactionData['from_ledger_id'] = $to_ledger;
            $arrLedgerTransactionData['to_ledger_id'] = $from_ledger;
            $arrLedgerTransactionData['from_ledger_name'] = $to_ledger_name['ledger_account_name'];
            $arrLedgerTransactionData['to_ledger_name'] = $from_ledger_name['ledger_account_name'];
        } else {
            // Deposit: use as sent by user
            $arrLedgerTransactionData['from_ledger_id'] = $from_ledger;
            $arrLedgerTransactionData['to_ledger_id'] = $to_ledger;
            $arrLedgerTransactionData['from_ledger_name'] = $from_ledger_name['ledger_account_name'];
            $arrLedgerTransactionData['to_ledger_name'] = $to_ledger_name['ledger_account_name'];
        }

        $arrLedgerTransactionData['transaction_date'] = $transaction_date;
        $arrLedgerTransactionData['transaction_amount'] = $amount;
        $arrLedgerTransactionData['narration'] = ($memo_desc != '') ? $memo_desc : (($type == 'w') ? 'Amount withdraw' : 'Amount deposited');

        $arrLedgerTransactionData['payment_reference'] = '';
        $arrLedgerTransactionData['transaction_type'] = '';
        $arrLedgerTransactionData['mode_of_payment'] = '';
        $arrLedgerTransactionData['other_payment_ref'] = '';
        $arrLedgerTransactionData['transaction_readable_date'] = $transaction_date;
        $arrLedgerTransactionData['voucher_type'] = VOUCHER_CONTRA;
        $arrLedgerTransactionData['soc_id'] = $soc_id;

        $resMasterVoucher = $this->saveInVoucherMaster($arrLedgerTransactionData);
        if (!$resMasterVoucher['error']) {
            $arrLedgerTransactionData['voucher_reference_number'] = $resMasterVoucher['voucher_id'];
            $arrLedgerTransactionData['voucher_reference_id'] = $resMasterVoucher['voucher_id'];
        } else {
            $arrLedgerTransactionData['voucher_reference_number'] = '';
        }

        $txn_id = $this->addTransactionEntry($arrLedgerTransactionData);
        $arrLedgerTransactionData['transaction_from_id'] = $txn_id;

        $txn_id = $this->addTransactionEntry($arrLedgerTransactionData);
        if ($type == 'a') {
            return response()->json($txn_id, 200);
        }
        // Calculate final balances using the actual ledger IDs used in the transaction
        $actual_from_ledger = $arrLedgerTransactionData['from_ledger_id'];
        $actual_to_ledger = $arrLedgerTransactionData['to_ledger_id'];
        $final_from_ledger_bal = $this->cashLedgerBal($actual_from_ledger, $soc_id, $arrFYDetail['fy_start_date'], $arrFYDetail['fy_end_date']);
        $final_to_ledger_bal = $this->cashLedgerBal($actual_to_ledger, $soc_id, $arrFYDetail['fy_start_date'], $arrFYDetail['fy_end_date']);
        if ($type == 'w') {
            $message = 'Cash Withdrawn Successfully';
        } else {
            $message = 'Cash Deposited Successfully';
        }
        // $response = array(
        //     'balance_old_from' => "Balance of $from_ledger_name[ledger_account_name] was $init_from_ledger_bal",
        //     'balance_old_to' => "Balance of $to_ledger_name[ledger_account_name] was $init_to_ledger_bal",
        //     'balance_new_from' => "Balance of $from_ledger_name[ledger_account_name] is $final_from_ledger_bal",
        //     'balance_new_to' => "Balance of $to_ledger_name[ledger_account_name] is $final_to_ledger_bal"
        // );

        $response = "Transaction successful, " . $arrLedgerTransactionData['from_ledger_name'] . " balance is $final_from_ledger_bal, Balance of " . $arrLedgerTransactionData['to_ledger_name'] . " is $final_to_ledger_bal.";
        $this->statusCode = 200;
        $this->message = $response;
        $this->status = 'success';
        //$this->session->set("succ_msg_bank", "Cash Deposited Successfully");
        //echo json_encode($response);
        //exit;
    }

    public function cashLedgerBal($ledger_id, $soc_id, $start_date = '', $end_date = '')
    {
        if (empty($ledger_id)) {
            $postedValues = request()->post();
            $ledger_id = $postedValues["from_ledger"] ?? '';
            $to_ledger = $postedValues["to_ledger"] ?? '';
            $transaction_date = $postedValues["transaction_date"] ?? '';
            $fetch_list = isset($postedValues['fetch_list']) ? $postedValues['fetch_list'] : 'N';

            $arrCurrentFYDetailObj = new SocAccountFinancialYearMaster();
            $soc_id = $soc_id;
            $arrCurrentFYDetail = $arrCurrentFYDetailObj->getCurrentFYDetail($soc_id);
            $arrFYDetail = json_decode(json_encode($arrCurrentFYDetail), true);
            $start_date = $arrFYDetail['fy_start_date'];
            $end_date = $arrFYDetail['fy_end_date'];
        }
        $ledgerTransactionObj = new ChsoneLedgerTransaction();
        
        $filter_criteria = array();
        if (empty($start_date)) {
            $translist = $ledgerTransactionObj->getLedgerTransactions($soc_id, $ledger_id, $filter_criteria, 'ledger');
        } else {
            $translist = $ledgerTransactionObj->getLedgerTransactionsByFY(array('soc_id' => $soc_id, 'ledger_id' => $ledger_id, 'criteria' => $filter_criteria, 'ledger' => 'ledger', 'start_date' => $start_date, 'end_date' => $end_date));
        }
        if (isset($fetch_list) && $fetch_list == 'Y') {
            return response()->json($translist, 200);
        }
        $translist = json_decode(json_encode($translist), true);

        $ChsoneGrpLedgerTreeObj = new ChsoneGrpLedgerTree();
        $ledger = $ChsoneGrpLedgerTreeObj->getLedger($ledger_id, 1);
        if (!empty($ledger)) {
            $ledger = json_decode(json_encode($ledger), true);
        }
        
        $overall_balance = 0.00;
        $overall_nature = $ledger['nature_of_account'];
        if (!empty($translist['transaction_total'])) {
            if ($translist['transaction_total'][0]['credit_amount'] > $translist['transaction_total'][0]['debit_amount']) {
                $overall_nature = 'cr';
                $overall_balance = $translist['transaction_total'][0]['credit_amount'] - $translist['transaction_total'][0]['debit_amount'];
            } elseif ($translist['transaction_total'][0]['credit_amount'] < $translist['transaction_total'][0]['debit_amount']) {
                $overall_nature = 'dr';
                $overall_balance = $translist['transaction_total'][0]['debit_amount'] - $translist['transaction_total'][0]['credit_amount'];
            }
        }
        $overall_balance = ($overall_nature != $ledger['nature_of_account']) ? (-1) * $overall_balance : $overall_balance;
        return number_format($overall_balance, 2);
    }

    public function addTransactionEntry($data)
    {
        $txn = new ChsoneLedgerTransaction();
        if (!empty($data['transaction_from_id'])) {
            $mode = MODE_TO;
            $data['transaction_type'] = "cr";
            $ledgerId = $data['to_ledger_id'];
            $ledgerName = $data['to_ledger_name'];
        } else {
            $mode = MODE_FROM;
            $data['transaction_type'] = "dr";
            $ledgerId = $data['from_ledger_id'];
            $ledgerName = $data['from_ledger_name'];
        }

        if (!empty($data['is_opening']) && $data['is_opening'] == 1) {
            $txnEntry = ChsoneLedgerTransaction::where('soc_id', $data['soc_id'])
                ->where('is_opening_balance', 1)
                ->where('ledger_account_id', $ledgerId)
                ->first();

            $txn->txn_id = $txnEntry ? $txnEntry->txn_id : '';
        }

        $txn->soc_id = $data['soc_id'];
        $txn->transaction_date = $data['transaction_date'];
        $txn->ledger_account_id = $ledgerId;
        $txn->ledger_account_name = $ledgerName;
        $txn->voucher_type = $data['voucher_type'];
        $txn->transaction_type = $data['transaction_type'];
        $txn->payment_mode = $data['mode_of_payment'];
        $txn->payment_reference = $data['payment_reference'];
        $txn->transaction_amount = $data['transaction_amount'];
        $txn->other_reference_id = $data['other_payment_ref'];
        $txn->txn_from_id = isset($data['transaction_from_id']) ? $data['transaction_from_id'] : null;
        $txn->memo_desc = $data['narration'];
        $txn->is_opening_balance = isset($data['is_opening']) ? $data['is_opening'] : 0;
        $txn->is_reconciled = !empty($data['is_reconciled']) ? $data['is_reconciled'] : 0;
        $txn->created_by = $this->input['user_id'] ?? '';
        $txn->added_on = now();
        $txn->voucher_reference_number = !empty($data['voucher_reference_number']) ? $data['voucher_reference_number'] : '';
        $txn->voucher_reference_id = !empty($data['voucher_reference_id']) ? $data['voucher_reference_id'] : '';
        $txn->is_cancelled = !empty($data['is_cancelled']) ? 1 : 0;

        if ($txn->save()) {
            return $txn->txn_id;
        } else {
            return false;
        }
    }

    public function saveInVoucherMaster($voucher_data)
    {
        $voucher_type = $voucher_data['voucher_type'];
        $from_ledg_id = $voucher_data['from_ledger_id'];
        $to_ledg_id = $voucher_data['to_ledger_id'];
        $txn_date = $voucher_data['transaction_date'];

        $txn_amt = $voucher_data['transaction_amount'];
        $narration = $voucher_data['narration'];
        $recp_ref = $voucher_data['payment_reference'];

        $from_name = $voucher_data['from_ledger_name']; //;
        $to_name = $voucher_data['to_ledger_name']; //;

        //$auth = $voucher_data['auth'];

        $from_ledger_name = preg_replace('/^[^0-9a-zA-Z]+/', "", trim($from_name));
        $to_ledger_name = preg_replace('/^[^0-9a-zA-Z]+/', "", trim($to_name));

        $arrVoucherResponse = $this->saveVoucherEntry(array('arrPostData' => array('voucher_type' => $voucher_type, 'from_ledger_account_id' => $from_ledg_id, 'to_ledger_account_id' => $to_ledg_id, 'transaction_date' => $txn_date, 'amount' => $txn_amt, 'reference' => $recp_ref, 'narration' => $narration, 'from_ledger_account_name' => $from_ledger_name, 'to_ledger_account_name' => $to_ledger_name, 'action' => 'add')));
        if (!empty($arrVoucherResponse['success']) && $arrVoucherResponse['success'] == true) {
            $notifyData['voucher_id'] = $arrVoucherResponse['voucher_id'];
            $notifyData['from_ledger_account_name'] = $from_ledger_name;
            $notifyData['to_ledger_account_name'] = $to_ledger_name;
            $notifyData['amount'] = $txn_amt;
            $notifyData['transaction_date'] = $voucher_data['transaction_readable_date'];
            // $this->sendMongoNotification($notifyData,$auth,'Payment');
            return array('error' => false, 'voucher_id' => $arrVoucherResponse['voucher_id']);
        } else {
            return array('error' => true, 'error_msg' => 'Voucher not created, Please try again.');
        }
    }

    public function saveVoucherEntry($data = array())
    {
        //echo '<pre>';print_r($data);exit;
        //$auth = $data['auth'];
        $soc_id = $this->input['company_id'] ?? '';
        $arrPostData = $data['arrPostData'];

        if (isset($arrPostData['voucher_id']) && !empty($arrPostData['voucher_id'])) {
            $objVoucher = ChsoneVoucherMaster
                ::where('voucher_id', $arrPostData['voucher_id'])
                ->where('soc_id', $soc_id)
                ->first();
            $objVoucher->status = (isset($arrPostData['status'])) ? $arrPostData['status'] : $objVoucher->status;
        } else {
            $objVoucher = new ChsoneVoucherMaster();
            $objVoucher->created_date = date('Y-m-d');
            $objVoucher->created_by = $this->input['user_id'] ?? '';
            $objVoucher->soc_id = $soc_id;
            $objVoucher->transaction_date = (isset($arrPostData['transaction_date']) && !empty($arrPostData['transaction_date'])) ? $arrPostData['transaction_date'] : '';
            $objVoucher->type = (isset($arrPostData['voucher_type']) && !empty($arrPostData['voucher_type'])) ? $arrPostData['voucher_type'] : '';

            $objVoucher->sub_type = (isset($arrPostData['sub_type']) && !empty($arrPostData['sub_type'])) ? $arrPostData['sub_type'] : '';


            $objVoucher->from_ledger_account_id = (isset($arrPostData['from_ledger_account_id']) && !empty($arrPostData['from_ledger_account_id'])) ? $arrPostData['from_ledger_account_id'] : '';
            $objVoucher->to_ledger_account_id = (isset($arrPostData['to_ledger_account_id']) && !empty($arrPostData['to_ledger_account_id'])) ? $arrPostData['to_ledger_account_id'] : '';
            $objVoucher->from_ledger_account_name = (isset($arrPostData['from_ledger_account_name']) && !empty($arrPostData['from_ledger_account_name'])) ? $arrPostData['from_ledger_account_name'] : '';
            $objVoucher->to_ledger_account_name = (isset($arrPostData['to_ledger_account_name']) && !empty($arrPostData['to_ledger_account_name'])) ? $arrPostData['to_ledger_account_name'] : '';
            $objVoucher->amount = $arrPostData['amount'];
            $objVoucher->reference = (isset($arrPostData['reference']) && !empty($arrPostData['reference'])) ? $arrPostData['reference'] : '';
            $objVoucher->narration = (isset($arrPostData['narration']) && !empty($arrPostData['narration'])) ? $arrPostData['narration'] : '';
            $objVoucher->status = (isset($arrPostData['status'])) ? $arrPostData['status'] : 1;
        }
        $objVoucher->updated_date = date('Y-m-d');
        $objVoucher->updated_by = $this->input['user_id'] ?? '';
        //print_r($objVoucher->toArray());exit;
        $finalArray = array();
        if (!$objVoucher->save()) {
            $arrMessage = [];
            foreach ($objVoucher->getMessages() as $messages) {
                $arrMessage[] = (string) $messages;
            }
            $finalArray['success'] = false;
            $finalArray['arrMessage'] = $arrMessage;
        } else {
            $finalArray['success'] = true;
            $finalArray['voucher_id'] = $objVoucher->voucher_id;
        }
        return $finalArray;
    }
}
