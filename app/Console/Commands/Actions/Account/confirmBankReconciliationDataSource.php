<?php

namespace App\Console\Commands\Actions\Account;

use Illuminate\Console\Command;
use App\Console\Commands\Action;
use App\Models\Tenants\ChsoneBankRecoMaster;
use App\Models\Tenants\ChsoneGrpLedgerTree;
use App\Models\Tenants\ChsoneLedgerTransaction;
use Carbon\Carbon;

class confirmBankReconciliationDataSource extends Action
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'datasource:confirmBankReconciliation {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Confirm Bank Reconciliation Data Source';

    /**
     * Execute the console command.
     */
    public function apply()
    {
        $soc_id = $this->input['company_id'];
        $requestData = $this->input;

        if (!empty($requestData)) {

            $seletedFinYear = $requestData['year'];
            $getFinYearSplit = explode('-', $seletedFinYear);

            $firstYear = $getFinYearSplit[0];
            $secondYear = $getFinYearSplit[1];

            /**************** Reconciled amount of current Month Start ******************/
            $month = $requestData['month'];
            $getMonthSplit = explode('-', $month);
            $getMonth = $getMonthSplit[0];
            $year = $getMonthSplit[1];
            $month = (int)$month; // Cast to integer

            $bank_ledger_id = $requestData['bank_ledger_id'];
            $bankClosingAmount = $requestData['bankClosingAmount']; // number_format($requestData['bankClosingAmount'], 2, '.', '');

            $monthName = date('F', mktime(0, 0, 0, $month, 10)) . ' ' . $year;
            $currentMonth = date('m');

            // $daysInMonth = cal_days_in_month(CAL_GREGORIAN, $month, $year);
            // use Carbon to get the last day of the month
            $daysInMonth = Carbon::createFromDate($year, $month)->daysInMonth;

            if ($getMonth == $currentMonth) {
                $end_date = date("Y-m-d", mktime(0, 0, 0, $month, date('d'), $year));
            } else {
                $end_date = date("Y-m-d", mktime(0, 0, 0, $month, $daysInMonth, $year));
            }

            $start_date = date("Y-m-d", mktime(0, 0, 0, $month, 1, $year));

            // Get the bank transactions of the current month
            $translist = $this->getbankTransactionOfCurentMonth($bank_ledger_id, $end_date, $start_date, $soc_id);

            if(!empty($translist)) {
                
                // Insert data in chsone_bank_reco_master table
                $insertDataInMaster = $this->insertMasterRecon($translist, $year, $getMonth, $seletedFinYear, $bankClosingAmount, $bank_ledger_id, $soc_id);

                if ($insertDataInMaster == 1) {
                    $this->message = "Entry's Reconciled Successfully";
                    $this->status = "success";
                    $this->statusCode = 200;
                    return;
                } else {
                    $this->message = "Entry already reconciled";
                    $this->status = "error";
                    $this->statusCode = 400;
                    return;
                }
            } else {
                $this->message = "No transactions found for the selected month";
                $this->status = "error";
                $this->statusCode = 400;
                return;
            }

        } else {
            $this->message = "No input provided";
            $this->status = "error";
            $this->statusCode = 400;
            return;
        }
    }

    public function getbankTransactionOfCurentMonth($bank_ledger_id, $end_date, $start_date, $soc_id)
    {
        // Query to fetch the ledger transactions
        $notReconTranslist = ChsoneLedgerTransaction::where('soc_id', $soc_id)
        ->where('ledger_account_id', $bank_ledger_id)
        ->where(function ($query) use ($start_date, $end_date) {
            $query->where(function ($subQuery) use ($start_date, $end_date) {
                $subQuery->whereBetween('value_date', [$start_date, $end_date])
                        ->whereIn('is_opening_balance', [0, 2]);
            })
            ->orWhere('is_opening_balance', 1);
        })
        ->orderBy('transaction_date', 'ASC')
        ->get()
        ->toArray();

        return $notReconTranslist;
    }

    public function insertMasterRecon($translist, $year, $getMonth, $seletedFinYear, $bankClosingAmount, $bank_ledger_id, $soc_id)
    {
        $flag = 'fail'; // Initialize the flag variable
        if (empty($soc_id)) {
            $soc_id = $this->input['company_id'];
        }

        if (isset($translist) && !empty($translist)) {
            foreach ($translist as $translistVal) {
                // need to check if the transaction is already reconciled with $translistVal['txn_id'] and $soc_id
                $txnExists = ChsoneBankRecoMaster::where('txn_id', $translistVal['txn_id'])
                ->where('soc_id', $soc_id)
                ->first();

                if ($txnExists) {
                    continue;
                } else {
                    $txn = new ChsoneBankRecoMaster();
                    $txn->txn_id = $translistVal['txn_id'];
                    $txn->soc_id = $soc_id;
                    $txn->txn_from_id = $translistVal['txn_from_id'];
                    $txn->ledger_account_id = $translistVal['ledger_account_id'];
                    $txn->ledger_account_name = $translistVal['ledger_account_name'];
                    $txn->financial_year = $seletedFinYear;
                    $txn->year = $year;
                    $txn->month = $getMonth;
                    $txn->is_reconcile_confirmed = $translistVal['is_reconciled'];
                    $txn->bank_date = $translistVal['transaction_date'];
                    $txn->value_date = $translistVal['value_date'];
                    if ($txn->txn_from_id == 0) {
                        $txn->bank_date = date("Y-m-d", mktime(0, 0, 0, $getMonth, 1, $year));
                        $txn->value_date = date("Y-m-d", mktime(0, 0, 0, $getMonth, 1, $year));
                    }

                    if ($txn->save()) {
                        $flag = 'success';
                        // return true;
                    } else {
                        $flag = 'fail';
                        return false;
                    }
                }



                // $txn = new ChsoneBankRecoMaster();
                // $txn->txn_id = $translistVal['txn_id'];
                // $txn->soc_id = $soc_id;
                // $txn->txn_from_id = $translistVal['txn_from_id'];
                // $txn->ledger_account_id = $translistVal['ledger_account_id'];
                // $txn->ledger_account_name = $translistVal['ledger_account_name'];
                // $txn->financial_year = $seletedFinYear;
                // $txn->year = $year;
                // $txn->month = $getMonth;
                // $txn->is_reconcile_confirmed = $translistVal['is_reconciled'];
                // $txn->bank_date = $translistVal['transaction_date'];
                // $txn->value_date = $translistVal['value_date'];
                // if ($txn->txn_from_id == 0) {
                //     $txn->bank_date = date("Y-m-d", mktime(0, 0, 0, $getMonth, 1, $year));
                //     $txn->value_date = date("Y-m-d", mktime(0, 0, 0, $getMonth, 1, $year));
                // }

                // if ($txn->save()) {
                //     $flag = 'success';
                //     // return true;
                // } else {
                //     $flag = 'fail';
                //     return false;
                // }
            }
        } else {
            $ledger = $this->getLedger($bank_ledger_id)->toarray();
            $ledger_account_name = $ledger['ledger_account_name'] ?? '';

            $txnNew = new ChsoneBankRecoMaster();
            $txnNew->txn_id = md5(time());
            $txnNew->soc_id = $soc_id;
            $txnNew->ledger_account_id = $bank_ledger_id;
            $txnNew->ledger_account_name = $ledger_account_name;
            $txnNew->financial_year = $seletedFinYear;
            $txnNew->year = $year;
            $txnNew->month = $getMonth;
            $txnNew->is_reconcile_confirmed = 1;
            $txnNew->bank_date = "$year-$getMonth-01";
            $txnNew->value_date = "$year-$getMonth-01";

            if ($txnNew->save()) {
                $flag = 'success';
                // return true;
            } else {
                $flag = 'fail';
                return false;
            }
        }

        if ($flag == 'success') {
            return $this->enterOpeningBalanceOfNextFinYear($translist ?? [], $year, $getMonth, $bankClosingAmount, $bank_ledger_id);
        }

        return false;
    }

    public function getLedger($ledger_acct_id, $return_object = 1)
    {
        //fetch ledger details from chsone_grp_ledger_tree whose ledger_account_id = $ledger_acct_id
        $ledger = ChsoneGrpLedgerTree::where("ledger_account_id", $ledger_acct_id)->first();

        if ($ledger) {
            if ($return_object) {
                return $ledger;
            } else {
                return $ledger->toArray();
            }
        } else {
            return false;
        }
    }

    public function enterOpeningBalanceOfNextFinYear($translist, $year, $getMonth, $bankClosingAmount, $bank_ledger_id)
    {
        if (empty($soc_id)) {
            $soc_id = $this->input['company_id'];
        }

        // Check if the year is already reconciled and the current month is March
        $ifYearReconciled = ChsoneBankRecoMaster::where('soc_id', $soc_id)
        ->where('ledger_account_id', $bank_ledger_id)
        ->where('year', $year)
        ->where('month', 3)
        ->get();

        if (!empty($ifYearReconciled) && $getMonth == 3) {

            $typeTxn = 'dr';

            // Fetch ledger details
            $ledger = $this->getLedger($bank_ledger_id)->toArray();
            $ledgerAccountName = $ledger['ledger_account_name'] ?? null;

            // Check if an opening balance already exists for the next financial year
            $txnEntry = ChsoneLedgerTransaction::where('soc_id', $soc_id)
            ->where('is_opening_balance', 1)
            ->where('ledger_account_id', $bank_ledger_id)
            ->where('transaction_date', "$year-04-01")
            ->first();
            
            // If an opening balance already exists, update the amount
            $txn = $txnEntry ?? new ChsoneLedgerTransaction();

            $txn->soc_id = $soc_id;
            $txn->transaction_date = "$year-04-01";
            $txn->ledger_account_id = $bank_ledger_id;
            $txn->ledger_account_name = $ledgerAccountName;
            $txn->transaction_type = $typeTxn; // Debit transaction
            $txn->transaction_amount = $bankClosingAmount;
            $txn->txn_from_id = 0;
            $txn->memo_desc = 'Opening Balance';
            $txn->is_opening_balance = 1;
            $txn->is_reconciled = 1;
            $txn->is_cancelled = 0;
            $txn->created_by = $this->input['user_id'] ?? 0;
            $txn->added_on = date("Y-m-d H:i:s");
            $txn->value_date = "$year-04-01";

            if ($txn->save()) {
                return true;
            } else {
                return false;
            }   
        }

        return true;
    }
}
