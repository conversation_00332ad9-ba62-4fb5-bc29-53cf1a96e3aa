<?php

namespace App\Console\Commands\Actions\Account;

use Illuminate\Console\Command;
use App\Console\Commands\Action;
use App\Models\Tenants\ChsoneGrpLedgerTree;
use App\Models\Tenants\ChsoneLedgerTransaction;
use App\Models\Tenants\ChsoneUnitsMaster;
use App\Models\Tenants\ChsoneVendorsMaster;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\Route;

class MembersUnitLedgerReportDataSource extends Action
{
    protected $constants;

    public function __construct()
    {
        parent::__construct();
        $this->constants = Config::get("constants");
    }

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'datasource:MembersUnitLedgerReport {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Member Unit Ledger Report Data Source';

    protected $formatter = [
        'id' => '',
        'transaction_date' => '',
        'particulars' => '',
        'voucher_type' => '',
        'voucher_reference_number' => '',
        'debit' => '',
        'credit' => '',
    ];

    protected $formatterByKeys = ['id'];

    /**
     * Execute the console command.
     */

    public function apply()
    {
        $request = $this->input;
        $soc_id = $request['company_id'] ?? null;
        $from_date = date('Y-m-01');
        $to_date = date('Y-m-t');
        $unit = $this->input['filters']['unit_id'] ?? null;
        $Route = Route::current();
        if ($Route->uri() == 'api/admin/accountsreporting/membersUnitAccountStatementReport/download/{type}') {
            $this->hugeData = true;
        }
        if(empty($unit)) {
            $result = [];
            $this->data = $result;
            return;
        }

        if (isset($request['filters']['startDate'])) {
            $from_date = $request['filters']['startDate'];
            $internal_data['start_date'] = $from_date;
         } else {
            $internal_data['start_date'] = date('Y-m-01');
         }

        if (isset($request['filters']['endDate'])) {
            $to_date = $request['filters']['endDate'];
            $internal_data['end_date'] = $to_date;
        } else {
            $internal_data['end_date'] = date('Y-m-t');
        }

        if (isset($unit) && $unit != '') {
            $unit_details = ChsoneUnitsMaster::where("unit_id", $unit)->where('soc_id', $soc_id)->first();
            $internal_data['ledgers'] = $unit_details->ledger_account_id;
        }

        $internal_data['year'] = 'irrelevent_feild_just_bypas';

        // fetch ledger data
        $ledData = $this->leadgerDownloadAction('pdf', true, $internal_data, $soc_id);
        if(empty($ledData)) {
            $result = [];
            $this->data = $result;
            return;
        }
        $arrData['internal_data'] = $internal_data;
        $arrData['arrReportResult'] = $this->formatLedgerData($ledData);
        $arrData['pages'] = isset($this->input['page']) ? $this->input['page'] : 1;
        $arrData['per_page'] = !empty($this->input['per_page']) ? $this->input['per_page'] : ($this->hugeData ? 100000 : 10);
        $arrData['offset'] = ($arrData['pages'] - 1) * $arrData['per_page'];

        $result = $arrData['arrReportResult']['arrUnitStatementDetail'];

        $summary = [
            'total_debit' => $arrData['arrReportResult']['arrUnitStatementSummary']['debit'],
            'total_credit' => $arrData['arrReportResult']['arrUnitStatementSummary']['credit'],
            'total_amount' => $arrData['arrReportResult']['arrUnitStatementSummary']['total'],
        ];

        // set the count of the $arrUnitStatementDetail
        $count = count($result);
        $this->meta['pagination']['total'] = $count;

        // set the pagination data
        $page = isset($this->input['page']) ? $this->input['page'] : 1;
        $per_page = !empty($this->input['per_page']) ? $this->input['per_page'] : ($this->hugeData ? 100000 : 10);
        $offset = ($page - 1) * $per_page;

        // // need to set the pagination only to the $arrUnitStatementDetail
        $result = array_slice($result, $offset, $per_page);

        $final = [
            $result,
           [$summary]
        ];

        $this->data = $final;
    }

    public function leadgerDownloadAction($downloadformat = 'pdf', $internal = false, $internal_data = [], $soc_id = null)
    {
        $midReturn = false;
        if ($internal) {
            $midReturn = true;
            $arrGet['ledgers'] = $internal_data['ledgers'];
            $arrGet['year'] = $internal_data['year'];
        }

        $ledgerArr = [];

        if (!empty($arrGet['ledgers']) && !empty($arrGet['year'])) {
            $page = !empty($arrGet['page']) ? $arrGet['page'] : 1;
            $report = !empty($arrGet['report']) ? $arrGet['report'] : 'ledger';
            $ledgers_input = explode(',', $arrGet['ledgers']);
            $year = explode('-', $arrGet['year'])[0];
            // $ledgers = $this->getLedgGroupTree(VOUCHER_JOURNAL, MODE_FROM, ENTITY_TYPE_LEDGER, '', $soc_id); as per old code
            $ledgers = $this->getLedgGroupTree(ACCOUNTSETTING, MODE_TO, ENTITY_TYPE_GROUP, '', $soc_id); // as per data get in old code
            $ledgers = $this->getLedgerChildrens($ledgers, $arrGet['ledgers']);

            $ledgerArr = $this->ledgerTreeToArray($ledgers);
            $pagesize = 2;
            $lcount = $page * $pagesize - $pagesize;
            $inr = 0;
            
            $unitsInfo = $this->getUnitsDetailsByledger($soc_id);
            $vendorInfo = $this->getVendorsDetailsByledger($soc_id);
            foreach ($ledgerArr as $legId => $legVal) {
                //echo $legId;
                if ((($inr >= $lcount && $inr < ($page * $pagesize)) || $downloadformat == 'excel') || $internal == true) {
                    $translist = array();
                    $translist = $this->getTransactionsMonthly($soc_id, $legId, 13, $year, $internal, $internal_data);
                    $translist = json_decode(json_encode($translist), true);
                    
                    $ledger_names = $this->getCounterEntryLedgerName($soc_id, $translist['transactions']);
                    if (strpos($legVal['ledger_account_name'], 'BLDG#') !== false) {

                        $unit_details = !empty($unitsInfo[$legVal['ledger_account_id']]) ? $unitsInfo[$legVal['ledger_account_id']] : null;
                    }
                    $vendor_details = [];
                    if ($legVal['context'] == 'vendor') {
                        $vendor_details = !empty($vendorInfo[$legVal['ledger_account_id']]) ? $vendorInfo[$legVal['ledger_account_id']] : null;
                    }

                    $ledgerArr[$legId] = array_merge($ledgerArr[$legId], $translist);
                    $ledgerArr[$legId]['others'] =  $ledger_names;
                    $ledgerArr[$legId]['unit_details'] =  $unit_details;
                    $ledgerArr[$legId]['vendor_details'] =  $vendor_details;
                }
                $inr++;
            }
            if ($midReturn) {
                return $ledgerArr;
            }

            // return $ledgerArr;
        }
    }

    public function getLedgGroupTree($voucher_type, $mode, $entity_type = "", $ledger_id = '', $soc_id = '', $behaviour = '')
    {
        $fin_cnd = "";
        $conditions = "";
        $conditionsnt = "";
        $conditionsgrp = "";
        $cnd_arr = $this->_contextVoucherType($voucher_type, $mode, $soc_id);
        // $cnd_arr = $this->_contextVoucherType(ACCOUNTSETTING, MODE_TO, $soc_id);
        
        if ($entity_type == 'group') {
            unset($cnd_arr['group']);
        }

        // Context conditions
        if (!empty($cnd_arr['context'])) {
            $conditions .= "context IN ('" . implode("','", $cnd_arr['context']) . "')";
        }
        if (!empty($cnd_arr['context_not'])) {
            $conditionsnt .= "context NOT IN ('" . implode("','", $cnd_arr['context_not']) . "')";
        }

        // Ledger account conditions
        if (!empty($ledger_id)) {
            $conditionsgrp .= "ledger_account_id IN (" . implode(",", (array) $ledger_id) . ")";
        } elseif (!empty($cnd_arr['group'])) {
            $conditionsgrp .= "ledger_account_id IN (" . implode(",", $cnd_arr['group']) . ")";
        }

        if (!empty($conditions) && !empty($conditionsnt)) {
            $fin_cnd .= "((" . $conditions . ") AND (" . $conditionsnt . "))";
        } elseif (!empty($conditions)) {
            $fin_cnd .= "(" . $conditions . ") ";
        } elseif (!empty($conditionsnt)) {
            $fin_cnd .= "(" . $conditionsnt . ") ";
        }

        if (!empty($conditionsgrp) && !empty($fin_cnd)) {
            $fin_cnd .= " OR (" . str_replace('AND', '', $conditionsgrp) . ")";
        } elseif (empty($fin_cnd) && !empty($conditionsgrp)) {
            $fin_cnd .= " (" . $conditionsgrp . ") ";
        }

        if (empty($fin_cnd)) {
            $fin_cnd .= " status = " . ACTIVE . " ";
        } else {
            $fin_cnd .= " AND status = " . ACTIVE . " ";
        }
        $fin_cnd .= " AND soc_id = $soc_id ";
        
        // Entity type conditions
        if ($entity_type == ENTITY_TYPE_LEDGER) {
            $fin_cnd .= "AND entity_type != '" . strtolower(ENTITY_TYPE_LEDGER ?? 'ledger') . "'";
        } elseif ($entity_type == ACC_TYPE_BANK || $entity_type == ACC_TYPE_CASH) {
            $fin_cnd .= "(behaviour = 'asset' OR behaviour = 'liability')";
            $fin_cnd .= "AND entity_type != '" . strtolower(ENTITY_TYPE_LEDGER ?? 'ledger') . "'";
        } elseif ($entity_type == ENTITY_TYPE_GROUP) {
            // $fin_cnd .= "AND entity_type != '" . strtolower(ENTITY_TYPE_LEDGER ?? 'ledger') . "'";
        }

        if (!empty($ledger_id)) {
            $groups_ldgs = $this->getLedgerTreeByIds($ledger_id);
        }

        // Handle voucher type 'contra'
        if ($voucher_type == 'contra') {
            $fin_cnd = "parent_id IN (" . implode(',', $cnd_arr['group']) . ") OR $fin_cnd";
        }

        if (!empty($cnd_arr["group"]) && empty($cnd_arr["context"]) && empty($cnd_arr["context_not"])) {

            if (count($cnd_arr["group"]) == 1) {
                $groups_ldgs = $this->getLedgerTreeById($cnd_arr["group"][0]);
            } else {
                $groups_ldgs = $this->getLedgerTreeByIds($cnd_arr["group"]);
            }
        } else {
            $fin_cnd = ($behaviour == '') ? $fin_cnd : 'behaviour = "' . $behaviour . '" AND status = 1 AND soc_id = ' . $soc_id;
            $groups_ldgs = $this->getLedgerTreeByCondition($fin_cnd);
        }
        $ret_arr = $this->prepareTree($groups_ldgs);
        return $ret_arr;
    }

    public function _contextVoucherType($voucherType, $mode, $soc_id)
    {
        $context = "";
        $contextNot = "";
        $grpId = [];
        $strings = [];
        $soc_id = $soc_id ? $soc_id : $this->input['company_id'];

        if ($mode == MODE_FROM) {
            // $voucherType = "payment";
            if (isset($this->constants['context_from_conf_arr'][$voucherType])) {
                $context = $this->constants['context_from_conf_arr'][$voucherType];
            } else {
                $context = "";
            }
            if (isset($this->constants['context_from_not_in_conf_arr'][$voucherType])) {
                $contextNot = $this->constants['context_from_not_in_conf_arr'][$voucherType];
            } else {
                $contextNot = "";
            }
            if (isset($this->constants['context_from_grp_array'][$voucherType])) {
                $strings = $this->constants['context_from_grp_array'][$voucherType];
            }
        }

        if ($mode == MODE_TO) {
            if (isset($this->constants['context_to_conf_arr'][$voucherType])) {
                $context = $this->constants['context_to_conf_arr'][$voucherType];
            } else {
                $context = "";
            }
            if (isset($this->constants['context_to_not_in_conf_arr'][$voucherType])) {
                $contextNot = $this->constants['context_to_not_in_conf_arr'][$voucherType];
            } else {
                $contextNot = "";
            }
            if (isset($this->constants['context_to_grp_array'][$voucherType])) {
                $strings = $this->constants['context_to_grp_array'][$voucherType];
            }
        }

        $groups = $this->tenantDB()->table("chsone_grp_ledger_tree")
            ->whereIn("ledger_account_name", $strings)
            ->where("soc_id", $soc_id)
            ->select("ledger_account_id")
            ->get();

        if (!empty($groups)) {
            foreach ($groups as $group) {
                $grpId[] = $group->ledger_account_id;
            }
        } else {
            $grpId = [];
        }
        return [
            "context" => $context,
            "context_not" => $contextNot,
            "group" => $grpId
        ];
    }

    public function getLedgerTreeByIds($ids = [])
    {
        if (empty($ids) || !is_array($ids)) {
            return [];
        }

        // Clean and sanitize IDs
        $ids = array_filter(array_map('intval', $ids));

        if (empty($ids)) {
            return [];
        }

        // Prepare bindings for IN clause
        $bindings = implode(',', $ids);

        $query = "
            WITH RECURSIVE ledger_tree AS (
                SELECT
                    ledger_account_id,
                    parent_id,
                    ledger_account_name,
                    nature_of_account,
                    behaviour,
                    entity_type,
                    status,
                    context,
                    operating_type
                FROM chsone_grp_ledger_tree
                WHERE ledger_account_id IN ($bindings)
                
                UNION ALL
                
                SELECT
                    c.ledger_account_id,
                    c.parent_id,
                    c.ledger_account_name,
                    c.nature_of_account,
                    c.behaviour,
                    c.entity_type,
                    c.status,
                    c.context,
                    c.operating_type
                FROM chsone_grp_ledger_tree c
                INNER JOIN ledger_tree lt ON c.parent_id = lt.ledger_account_id
            )
            SELECT * FROM ledger_tree;
        ";

        // Execute the query
        $results = $this->tenantDB()->select($query);
        return $results ? json_decode(json_encode($results), true) : [];
    }

    public function getLedgerTreeById($id = '')
    {
        // Validate and cast ID
        $id = intval($id);
        if ($id <= 0) {
            return [];
        }

        // Set @idlist using a raw statement
        $this->tenantDB()->statement("SET @idlist = ''");

        // Build the query string without bound parameters
        $query_string = "
            SELECT
                ledger_account_name,
                nature_of_account,
                behaviour,
                entity_type,
                ledger_account_id,
                parent_id as parent,
                status,
                context,
                operating_type
            FROM (
                SELECT *,
                    CASE
                        WHEN ledger_account_id = $id THEN @idlist := CONCAT(ledger_account_id)
                        WHEN FIND_IN_SET(parent_id, @idlist) THEN @idlist := CONCAT(@idlist, ',', ledger_account_id)
                    END as checkId
                FROM chsone_grp_ledger_tree
                ORDER BY ledger_account_id ASC
            ) as T
            WHERE checkId IS NOT NULL
        ";

        // Execute the query directly (no bindings)
        $results = $this->tenantDB()->select($query_string);

        return $results ? json_decode(json_encode($results), true) : [];
    }

    public function getLedgerTreeByCondition($fin_cnd)
    {
        // Validate the input condition to prevent potential SQL injection
        if (empty($fin_cnd)) {
            return [];
        }

        try {
            $this->tenantDB()->statement("SET @idlist = ''");

            $query_string = "
            SELECT ledger_account_name, nature_of_account, behaviour, entity_type,
            ledger_account_id, parent_id as parent, status, context, operating_type
            FROM (
                SELECT *, CASE
                    WHEN $fin_cnd THEN @idlist := CONCAT(ledger_account_id)
                    WHEN FIND_IN_SET(parent_id, @idlist) THEN @idlist := CONCAT(@idlist, ',', ledger_account_id)
                END as checkId
                FROM chsone_grp_ledger_tree
                ORDER BY parent_id, ledger_account_name ASC
            ) as T
            WHERE checkId IS NOT NULL
        ";

            $results = $this->tenantDB()->select($query_string);

            return !empty($results) ? json_decode(json_encode($results), true) : [];
        } catch (\Exception $e) {
            dd($e->getMessage());
            return [];
        }
    }

    function prepareTree($tree, $root = 0)
    {
        $return = [];
        foreach ($tree as $key => $parent) {

            # A direct child is found
            if ($parent['parent'] == $root) {
                # Remove item from tree (we don't need to traverse this again)
                unset($tree[$key]);

                # Append the child into result array and parse its children
                $modified_array = array();
                $modified_array["name"] = $parent["ledger_account_name"];
                $modified_array["nature"] = $parent["nature_of_account"];
                $modified_array["behaviour"] = $parent["behaviour"];
                $modified_array["entity_type"] = $parent["entity_type"];
                $modified_array["ledger_account_id"] = $parent["ledger_account_id"];
                $modified_array["parent"] = $parent["parent"];
                $modified_array["status"] = $parent["status"];
                $modified_array["context"] = preg_replace('/[0-9]+/', '', $parent["context"]);
                $modified_array["is_parent"] = "yes";
                $modified_array["operating_type"] = $parent["operating_type"];

                $return[$parent['ledger_account_id']] = $modified_array;
                $child = $this->prepareTree($tree, $parent['ledger_account_id']);
                if (empty($child)) {
                    $return[$parent['ledger_account_id']]['is_parent'] = 'no';
                }
                $return[$parent['ledger_account_id']]['children'] = $child;
            } else {
                continue;
            }
        }
        return empty($return) ? null : $return;
    }

    private function getLedgerChildrens($ledgers, $childId)
    {
        foreach ($ledgers as $lid => $lval) {
            if ($childId == $lid) {
                if ($lval['entity_type'] == 'ledger') {
                    return array($lval);
                } else {
                    return $lval['children'];
                }
            }
            if ($lval['entity_type'] == 'group' || $lval['entity_type'] == 'main') {
                if (is_array($lval['children']) && count($lval['children'])) {
                    $value = $this->getLedgerChildrens($lval['children'], $childId);
                    if (!is_null($value)) {
                        return $value;
                    }
                }
            }
        }
        return null;
    }

    private function ledgerTreeToArray($ledgers)
    {
        $ledgerArr = array();
        if (isset($ledgers) && is_array($ledgers)) {

            foreach ($ledgers as $val) {
                if ($val['entity_type'] == 'ledger') {
                    if (array_key_exists($val["ledger_account_id"], $ledgerArr)) {
                    } else {
                        $val['ledger_account_name'] = $val['name'];
                        $val['nature_of_account'] = $val['nature'];
                        $ledgerArr[(string)$val["ledger_account_id"]] = $val;
                    }
                } else {
                    if ($val['children'] && is_array($val['children'])) {
                        //print_r($ledgerArr);
                        $newArr = $this->ledgerTreeToArray($val['children']);
                        //print_r($newArr);
                        $ledgerArr = $newArr + $ledgerArr;
                        //print_r($ledgerArr);
                        //exit();
                    }
                }
            }
        }

        return $ledgerArr;
    }

    private function getUnitsDetailsByledger($soc_id)
    {
        $primaryUnits = $this->getUnitByAllLedger($soc_id);
        $primaryUnits = json_decode(json_encode($primaryUnits), true);
        $unitsDetails = [];
        foreach ($primaryUnits as $unit) {
            $unitsDetails[$unit['ledger_account_id']] = $unit;
        }
        return $unitsDetails;
    }

    private function getVendorsDetailsByledger($soc_id)
    {
        $vendors = ChsoneVendorsMaster::where('soc_id', $soc_id)->get()->toArray();
        $vendorData = [];
        foreach ($vendors as $vendor) {
            $vendorData[$vendor['vendor_ledger_id']] = $vendor;
        }
        return $vendorData;
    }

    public function getTransactionsMonthly($soc_id, $ledgerId, $month, $year, $internal = false, $internal_data = [])
    {
        $parent = 'Top';
        $openingDate = '';
        $totalCR = 0;
        $totalDR = 0;

        $groupLedgerTree = $this->tenantDB()
            ->table("chsone_grp_ledger_tree")
            ->where('ledger_account_id', $ledgerId)
            ->first();

        if ($groupLedgerTree->parent_id != 0) {
            $parent = $this->getParentLedgerAccount($groupLedgerTree->parent_id);
        }

        // $startDate = $this->calculateStartDate($month, $year);
        // $endDate = $this->calculateEndDate($month, $year);

        $startyear = $year;
        if ($month < 4) {
            $startyear = $year - 1;
        }
        $isthirteen = 0;
        if ($month == 13) {
            $month = 4;
            $isthirteen = 1;
        }

        if ($isthirteen) {
            $month = 3;
            $year++;
        }

        // $startDate = date("Y-m-01", strtotime("$year-$month-01"));
        // $endDate = date("Y-m-t", strtotime("$year-$month-01"));
        $startDate = date("Y-m-01", strtotime($internal_data['start_date'] ?? "$startyear-$month-01"));
        $endDate = date("Y-m-t", strtotime($internal_data['end_date'] ?? "$startyear-$month-01"));

        $ledgerTxn = $this->getOpeningBalanceTransaction($ledgerId, $startDate, $endDate);
        if (empty($ledgerTxn)) {
            $forward = $this->calculateForwardBalance($ledgerId, $startDate, $year, $groupLedgerTree);
            $ledgerTxn = 0;
        } else {
            $forward = 0;
            $openingDate = $ledgerTxn->transaction_date;
            $openingType = $ledgerTxn->transaction_type;
            $ledgerTxn = ($ledgerTxn->transaction_type == $groupLedgerTree->nature_of_account) ?
                $ledgerTxn->transaction_amount : ($ledgerTxn->transaction_amount * -1);
        }
        $transactionResult = $this->getTransactions($ledgerId, $startDate, $endDate, $groupLedgerTree);
        $transactionResult = json_decode(json_encode($transactionResult), false);
        $balance = $ledgerTxn + $forward;

        foreach ($transactionResult as &$transaction) {

            // check if transaction_type type is cr or dr according to the transaction_type set transaction_amount
            if ($transaction->transaction_type == 'dr') {
                $transaction->transaction_amount_credit = 0;
                $transaction->transaction_amount_debit = $transaction->transaction_amount;
            } elseif ($transaction->transaction_type == 'cr') {
                $transaction->transaction_amount_credit = $transaction->transaction_amount;
                $transaction->transaction_amount_debit = 0;
            }

            $balance = $transaction->balance = ($groupLedgerTree->nature_of_account == $transaction->transaction_type)
                ? $balance + $transaction->transaction_amount : $balance - $transaction->transaction_amount;

            $totalCR += ($transaction->transaction_type == "cr") ? $transaction->transaction_amount : 0;
            $totalDR += ($transaction->transaction_type == "dr") ? $transaction->transaction_amount : 0;
        }

        $total = $this->calculateTotalBalance($ledgerId, $year, $groupLedgerTree);

        return [
            "ledger"       => $groupLedgerTree->ledger_account_name,
            "opening"      => $ledgerTxn,
            'opening_type' => isset($openingType) ? $openingType : '',
            'opening_date' => $openingDate ? $openingDate : '',
            'forwarded'    => $forward,
            'transactions' => $transactionResult,
            'parent'       => $parent,
            'totalcr'      => $totalCR,
            'totaldr'      => $totalDR,
            'balance'      => $balance,
            "total"        => $total
        ];
    }

    private function getParentLedgerAccount($parentId)
    {
        return $this->tenantDB()
            ->table("chsone_grp_ledger_tree")
            ->where('ledger_account_id', $parentId)
            ->first()->ledger_account_name;
    }

    private function getOpeningBalanceTransaction($ledgerId, $startDate, $endDate)
    {
        $ledgerTxn = $this->tenantDB()
            ->table("chsone_ledger_transactions")
            ->where("ledger_account_id", $ledgerId)
            ->where("transaction_date", ">=", $startDate)
            ->where("transaction_date", "<=", $endDate)
            ->where("is_opening_balance", "!=", 0)
            ->first();
        return $ledgerTxn;
    }

    private function calculateForwardBalance($ledgerId, $startDate, $year, $groupLedgerTree)
    {
        $ledgerTxnCR = $this->tenantDB()
            ->table("chsone_ledger_transactions")
            ->where("ledger_account_id", $ledgerId)
            ->where("transaction_date", "<", $startDate)
            ->where("transaction_date", ">=", "$year-04-01")
            ->where("transaction_type", "cr")
            ->sum('transaction_amount');

        $ledgerTxnDR = $this->tenantDB()
            ->table("chsone_ledger_transactions")
            ->where("ledger_account_id", $ledgerId)
            ->where("transaction_date", "<", $startDate)
            ->where("transaction_date", ">=", "$year-04-01")
            ->where("transaction_type", "dr")
            ->sum('transaction_amount');

        if ($groupLedgerTree->nature_of_account == "cr") {
            $forward = $ledgerTxnCR - $ledgerTxnDR;
        } else {
            $forward = $ledgerTxnDR - $ledgerTxnCR;
        }
        return $forward;
    }

    private function getTransactions($ledgerId, $startDate, $endDate, $groupLedgerTree)
    {
        $transactions = $this->tenantDB()->table('chsone_ledger_transactions as lTxn')
            ->selectRaw('
                CASE WHEN lTxn.ledger_account_id = ? THEN mTxn.ledger_account_name ELSE lTxn.ledger_account_name END AS counter_ledger_account_name,
                CASE WHEN lTxn.ledger_account_id = ? THEN lTxn.txn_id ELSE mTxn.txn_id END AS id,
                CASE WHEN lTxn.ledger_account_id = ? THEN lTxn.txn_from_id ELSE mTxn.txn_from_id END AS txn_from_id,
                CASE WHEN lTxn.ledger_account_id = ? THEN lTxn.ledger_account_id ELSE mTxn.ledger_account_id END AS ledger_account_name,
                CASE WHEN lTxn.ledger_account_id = ? THEN lTxn.transaction_type ELSE mTxn.transaction_type END AS transaction_type,
                lTxn.transaction_amount,
                lTxn.memo_desc,
                lTxn.transaction_date,
                lTxn.soc_id,
                lTxn.voucher_reference_id,
                lTxn.voucher_reference_number,
                lTxn.voucher_type,
                lTxn.is_cancelled,
                lTxn.is_opening_balance,
                lTxn.is_reconciled,
                lTxn.payment_mode,
                lTxn.payment_reference,
                lTxn.other_reference_id,
                lTxn.created_by,
                lTxn.added_on,
                lTxn.value_date
            ', [$ledgerId, $ledgerId, $ledgerId, $ledgerId, $ledgerId])
            ->join('chsone_ledger_transactions as mTxn', 'lTxn.txn_id', '=', 'mTxn.txn_from_id')
            ->where(function ($query) use ($ledgerId) {
                $query->where('lTxn.ledger_account_id', $ledgerId)
                    ->orWhere('mTxn.ledger_account_id', $ledgerId);
            })
            ->whereBetween('lTxn.transaction_date', [$startDate, $endDate])
            ->where('lTxn.is_opening_balance', 0)
            ->where('lTxn.transaction_amount', '!=', 0)
            ->where('lTxn.is_cancelled', '=', 0)
            ->orderBy('lTxn.transaction_date', 'asc')
            ->orderBy('counter_ledger_account_name', 'asc')
            ->get();

        return $transactions;
    }

    private function calculateTotalBalance($ledgerId, $year, $groupLedgerTree)
    {
        $totalCR = $this->tenantDB()
            ->table("chsone_ledger_transactions")
            ->where("ledger_account_id", $ledgerId)
            ->where("transaction_date", ">=", "$year-04-01")
            ->where("transaction_type", "cr")
            ->sum('transaction_amount');

        $totalDR = $this->tenantDB()
            ->table("chsone_ledger_transactions")
            ->where("ledger_account_id", $ledgerId)
            ->where("transaction_date", ">=", "$year-04-01")
            ->where("transaction_type", "dr")
            ->sum('transaction_amount');

        if ($groupLedgerTree->nature_of_account == "cr") {
            $total = $totalCR - $totalDR;
        } else {
            $total = $totalDR - $totalCR;
        }
        return $total;
    }

    public function getCounterEntryLedgerName($soc_id, $all_ledger = [])
    {
        $all_ledger = json_decode(json_encode($all_ledger), true);
        // dd($all_ledger);
        $ledger_array = [];

        foreach ($all_ledger as $t) {
            $ledger_account_name = '';

            if (empty($t['txn_from_id'])) {
                // Find the counter entry ledger name
                $counter_entry = ChsoneLedgerTransaction::where('soc_id', $soc_id)
                    ->where('txn_from_id', $t['id'])
                    ->get()->toArray();

                if (count($counter_entry) > 0) {
                    $ledger_account_name = $counter_entry[0]['ledger_account_name'];

                    if (empty($counter_entry[0]['ledger_account_name'])) {
                        $counter_ledger_details = ChsoneGrpLedgerTree::where('ledger_account_id', $counter_entry[0]['ledger_account_id'])
                            ->get()->toArray();

                        if ($counter_ledger_details) {
                            $ledger_account_name = $counter_ledger_details[0]['ledger_account_name'];
                        }
                    }

                    $ledger_array[] = [
                        'name' => 'To ' . $ledger_account_name,
                        'ledger_id' => $counter_entry[0]['ledger_account_id'],
                    ];
                } else {
                    $ledger_array[] = [
                        'name' => 'For ' . $t['ledger_account_name'],
                        'ledger_id' => $t['ledger_account_id'],
                    ];
                }
            } else {
                // Find the entry ledger name for this counter entry
                $counter_entry = ChsoneLedgerTransaction::where('soc_id', $soc_id)
                    ->where('txn_id', $t['txn_from_id'])
                    ->get()->toArray();

                if (count($counter_entry) > 0) {
                    $ledger_account_name = $counter_entry[0]['ledger_account_name'];

                    if (empty($ledger_account_name)) {
                        $counter_ledger_details = ChsoneGrpLedgerTree::where('ledger_account_id', $counter_entry[0]['ledger_account_id'])
                            ->get()->toArray();

                        if ($counter_ledger_details) {
                            $ledger_account_name = $counter_ledger_details[0]['ledger_account_name'];
                        }
                    }

                    $ledger_array[] = [
                        'name' => 'By ' . $ledger_account_name,
                        'ledger_id' => $counter_entry[0]['ledger_account_id'],
                    ];
                } else {
                    $ledger_array[] = [
                        'name' => 'For ' . $t['ledger_account_name'],
                        'ledger_id' => $t['ledger_account_id'],
                    ];
                }
            }
        }
        return $ledger_array;
    }

    public function getUnitByAllLedger($soc_id)
    {
        $obj = $this->tenantDB()->table('chsone_units_master as a')
            ->leftJoin('chsone_members_master as b', 'a.unit_id', '=', 'b.fk_unit_id')
            ->select([
                'a.ledger_account_id',
                DB::raw("CONCAT(a.soc_building_name, '/', a.unit_flat_number) AS building_unit_name"),
                'a.unit_id',
                DB::raw("CONCAT(b.member_first_name, ' ', b.member_last_name) AS member_name"),
            ])
            ->where('a.soc_id', $soc_id)
            ->where('a.status', 1)
            ->where('b.member_type_id', 1)
            ->where('b.status', 1)
            ->groupBy('a.unit_id')
            ->orderBy('a.soc_building_name')
            ->orderBy('a.soc_building_floor')
            ->orderBy('a.unit_flat_number')
            ->get()
            ->toArray();
        return $obj;
    }

    private function formatLedgerData($ledData)
    {

        foreach ($ledData as $key => $value) {
            if ($value['opening_type'] == 'dr') {
                $op_tran_type = 'cr';
            } elseif ($value['opening_type'] == 'cr') {
                $op_tran_type = 'dr';
            }
            $reData['arrUnitStatementDetail'][] = array(
                'transaction_date' => $value['opening_date'],
                'particulars' => ucfirst($op_tran_type) . ' Opening Balance',
                'voucher_type' => '',
                'voucher_reference_number' => '',
                'debit' => ($value['opening_type'] == 'dr') ? (float)abs($value['opening']) : 0.00,
                'credit' => ($value['opening_type'] == 'cr') ? (float)abs($value['opening']) : 0.00
            );
            $i = 0;
            foreach ($value['transactions'] as $transactions) {
                if ($transactions['voucher_type'] == 'purchase') {
                    $transactions['voucher_type'] = 'payment';
                } elseif ($transactions['voucher_type'] == 'sales') {
                    $transactions['voucher_type'] = 'income';
                } elseif ($transactions['voucher_type'] == 'credit') {
                    $transactions['voucher_type'] = 'credit note';
                } elseif ($transactions['voucher_type'] == 'debit') {
                    $transactions['voucher_type'] = 'debit note';
                }

                if ($transactions['transaction_type'] == 'dr') {
                    $tran_type = 'cr';
                } elseif ($transactions['transaction_type'] == 'cr') {
                    $tran_type = 'dr';
                }
                $reData['arrUnitStatementDetail'][] = array(
                    'transaction_date' => $transactions['transaction_date'],
                    'particulars' => ucfirst($tran_type) . ' ' . str_replace(['To', 'By'], ' ', $value['others'][$i]['name']),
                    'voucher_type' => ucfirst($transactions['voucher_type']),
                    'voucher_reference_number' => $transactions['voucher_reference_number'],
                    'debit' => ($transactions['transaction_type'] == 'dr') ? (float)abs($transactions['transaction_amount']) : 0.00,
                    'credit' => ($transactions['transaction_type'] == 'cr') ? (float)abs($transactions['transaction_amount']) : 0.00
                );
                $i++;
            }
            $debit = ($value['opening_type'] == 'dr') ? (float)abs($value['totaldr']) + (float) abs($value['opening']) : (float)abs($value['totaldr']);
            $credit = ($value['opening_type'] == 'cr') ? (float)abs($value['totalcr']) + (float) abs($value['opening']) : (float)abs($value['totalcr']);
            $total = ($debit > $credit) ? $debit - $credit : $credit - $debit;
            $reData['arrUnitStatementSummary'] = array(
                'debit' => $debit,
                'credit' => $credit,
                'total' => $total
            );
        }
        return $reData;
    }
}
