<?php

namespace App\Console\Commands\Actions\Account;

use App\Console\Commands\Action;
use Illuminate\Console\Command;
use Carbon\Carbon;

class BankReconciliationFormAction extends Action
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'datasource:bankReconciliationForm {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Bank Reconcilation Form';

    protected $formatterByKeys = ['id'];

    protected $mapper = [
        'id' => 'chsone_ledger_transactions.ledger_account_id',
    ];

    /**
     * Execute the console command.
     */
    public function apply()
    {
        $yearSelected = $this->input['yearSelected'] ?? null;

        $this->data = $this->getRecoDetailsOfMonthAction($this->input);

        // if($yearSelected){
        //     $this->data['reconcilled_amount'] = $this->getReconciledMonthsAction($this->input);
        // }

        // $this->data['reconcilled_month'] = $this->getReconciledMonthsAction($this->input);
    }

    public function getRecoDetailsOfMonthAction($data)
    {
        $soc_id = $data["company_id"];
        $monthNo = $data['month_no'];
        $bank_ledger_id = $data['bank_ledger_id'];
        $seletedFinYear = $data['yearSelected'];

        $getFinYearSplit = explode('-', $seletedFinYear);

        $firstYear = $getFinYearSplit[0];
        $secondYear = $getFinYearSplit[1];

        $getMonthSplit = explode('-', $data['month_no']);
        $monthNumber = intval($getMonthSplit[0]); // Extracting the month number correctly
        $year = $getMonthSplit[1];

        // Convert the month name to a month number

        // Get the full month name and year
        $monthFullName = date('F', mktime(0, 0, 0, $monthNumber, 1)) . ' ' . $year;
        /*****************************Reconciled amount of current Month Start******************************************** */

        /* *************************** Reconciled amount of current Month Start **************************** */
        $currentMonth = date('m');
        // $daysInMonth = cal_days_in_month(CAL_GREGORIAN, $monthNumber, $year);
        $daysInMonth = Carbon::createFromDate($year, $monthNumber)->daysInMonth;
        if ($monthNumber == $currentMonth) {
            $end_date = date("Y-m-d", mktime(0, 0, 0, $monthNumber, date('d'), $year));
        } else {
            $end_date = date("Y-m-d", mktime(0, 0, 0, $monthNumber, $daysInMonth, $year));
        }

        // Calculate the previous month date
        $end_date = date("Y-m-t", strtotime("$year-$monthNumber-01"));
        $pre_date = date("Y-m-t", strtotime("-1 month", strtotime("$year-$monthNumber-01")));
        $start_date = date("Y-m-01", strtotime("$year-$monthNumber-01"));

        $reco_bal = $this->getReconciledbalance($bank_ledger_id, $end_date, $pre_date, $start_date, $soc_id);

        $reco_bal_data = $reco_bal;

        $curr_debit_sum = $reco_bal_data['curr_debit_sum'];
        $curr_cerdit_sum = $reco_bal_data['curr_credit_sum'];
        $prev_debit_sum = $reco_bal_data['prev_debit_sum'];
        $prev_cerdit_sum = $reco_bal_data['prev_credit_sum'];
        /*****************************Reconciled amount of current Month End*************************************************/

        /*****************************Reconciled amount of Previuos Month Start**********************************************/

        $first_date_current_month = date("Y-m-1", strtotime($end_date));

        $monthName = date('F', mktime(0, 0, 0, $monthNumber, 1));

        // Calculate the first and last day of the previous month using the correct monthName
        $first_date_of_last_month = date("Y-m-d", strtotime("first day of last month", strtotime("$monthName 1 $year")));
        $last_date_last_month = date("Y-m-d", strtotime("last day of last month", strtotime("$monthName 1 $year")));

        $closure_master_details = $this->getFyStartDate($soc_id);
        $closure_master_details = json_decode(json_encode($closure_master_details), true);

        $arrClosingDetails = explode('-', $closure_master_details[0]['fy_start_date']);
        $firstFinancialYear = $arrClosingDetails[0];

        $reconciled_bal_prev_month = $this->getReconciledBalanceOfPrevMonth($bank_ledger_id, $first_date_of_last_month, $last_date_last_month, $year, $soc_id);

        $last_curr_debit_sum = $reconciled_bal_prev_month['curr_debit_sum'];
        $last_curr_cerdit_sum = $reconciled_bal_prev_month['curr_credit_sum'];
        $last_prev_debit_sum = $reconciled_bal_prev_month['prev_debit_sum'];
        $last_prev_cerdit_sum = $reconciled_bal_prev_month['prev_credit_sum'];

        //*****************************Reconciled amount of Previuos Month End*************************************************/

        $lastMonthFirstDay = date("Y-m-01", strtotime("-1 month", strtotime("$year-$monthNumber-01")));
        $lastMonthEndDay = date("Y-m-t", strtotime("-1 month", strtotime("$year-$monthNumber-01")));

        // Define current month date range
        $arrCurrentMonthDates = [
            'soc_id' => $soc_id,
            'bank_ledger_id' => $bank_ledger_id,
            'currentMonthFirstDay' => $first_date_current_month,
            'currentMonthEndDay' => $end_date,
            'firstYear' => $firstYear,
            'firstFinancialYear' => $firstFinancialYear,
        ];

        // Define last month's date range for checking entries
        $arrCheckEntries = [
            'soc_id' => $soc_id,
            'bank_ledger_id' => $bank_ledger_id,
            'lastMonthFirstDay' => $lastMonthFirstDay,
            'lastMonthEndDay' => $lastMonthEndDay,
            'firstYear' => $firstYear,
            'firstFinancialYear' => $firstFinancialYear,
        ];

        // Define opening entries for the financial year
        $arrOpeningEntries = [
            'soc_id' => $soc_id,
            'bank_ledger_id' => $bank_ledger_id,
            'currentYear' => $firstYear,
            'lastMonthFirstDay' => $lastMonthFirstDay,
            'lastMonthEndDay' => $lastMonthEndDay,
            'firstFinancialYear' => $firstFinancialYear,
        ];

        $closingBalOfCurrentMonthLedger = $this->calculateClosingLedgerAmountOfCurrentMonth($arrCurrentMonthDates);

        $closingBalOfLastMonthLedger = $this->calculateLedgerAmount($arrCheckEntries);
        $closingBalOfLastMonthLedger = abs($closingBalOfLastMonthLedger);

        $openingBalOfYear = $this->getOpeningBalanceOfYear($arrOpeningEntries);
        $openingBalOfYear = json_decode(json_encode($openingBalOfYear), true);

        $yearsArr = [
            'soc_id' => $soc_id,
            'firstYear' => $firstYear,
            'secondYear' => $secondYear,
            'firstFinancialYear' => $firstFinancialYear,
        ];
        $isFinYearClosed = $this->checkFinancialYearClosed($yearsArr);
        $isFinYearClosed = json_decode(json_encode($isFinYearClosed), true);

        $reco_opning_bal = ($curr_debit_sum - $curr_cerdit_sum) + ($prev_debit_sum - $prev_cerdit_sum) + $openingBalOfYear['transaction_amount'];
        $reco_opning_bal = json_decode(json_encode($reco_opning_bal), true);

        $prev_reco_opning_bal = ($last_curr_debit_sum - $last_curr_cerdit_sum) + ($last_prev_debit_sum - $last_prev_cerdit_sum);

        $ledger_bal_prev_month = $this->getLedgerBalanceOfPrevMonth($bank_ledger_id, $first_date_of_last_month, $last_date_last_month, $firstYear, $soc_id);

        $ledger_last_curr_debit_sum = $ledger_bal_prev_month['curr_debit_sum'];
        $ledger_last_curr_credit_sum = $ledger_bal_prev_month['curr_credit_sum'];
        $ledger_last_prev_debit_sum = $ledger_bal_prev_month['prev_debit_sum'];
        $ledger_last_prev_credit_sum = $ledger_bal_prev_month['prev_credit_sum'];

        $prev_ledger_bal = ($ledger_last_curr_debit_sum - $ledger_last_curr_credit_sum) + ($ledger_last_prev_debit_sum - $ledger_last_prev_credit_sum);

        $recoDetails = array();

        $recoDetails['closingBalOfLastMonthByLedger'] = $closingBalOfLastMonthLedger;
        $recoDetails['prev_reco_opning_bal'] = $prev_reco_opning_bal;
        $recoDetails['firstFinancialYear'] = $firstFinancialYear;

        $recoDetails['closingBalOfCurrentMonthLedger'] = $closingBalOfCurrentMonthLedger;
        $recoDetails['closingBalOfLastMonthByLedger'] = $recoDetails['closingBalOfLastMonthByLedger'];
        $recoDetails['closingBalOfCurrentMonthLedger'] = $recoDetails['closingBalOfCurrentMonthLedger'];
        $recoDetails['closingBalOfLastMonthByLedger'] = $recoDetails['closingBalOfLastMonthByLedger'];
        // echo $recoDetails['prev_reco_opning_bal']; exit;
        //Reconciled opening balance
        if ($firstFinancialYear == $firstYear) {
            if ($monthNumber == 4) {
                $prev_reco_opning_bal = $openingBalOfYear['transaction_amount'];
            }  else {
                $prev_reco_opning_bal = $recoDetails['prev_reco_opning_bal'];
            }
        } elseif ($firstFinancialYear != $firstYear && (!$isFinYearClosed || $isFinYearClosed == 0) && $monthNumber == 4) {

            $prev_reco_opning_bal = $recoDetails['prev_reco_opning_bal'];

        } elseif ($firstFinancialYear != $firstYear && ($isFinYearClosed == '' || $isFinYearClosed == 0) && $monthNo != 4) {

            $prev_reco_opning_bal = $recoDetails['prev_reco_opning_bal'] - $openingBalOfYear['transaction_amount'];

        } else {
            $prev_reco_opning_bal = $openingBalOfYear['transaction_amount'];
        }

        // Opening balance of year
        if ($firstFinancialYear == $firstYear) {
            $recoDetails['openingBalOfYearByLedger'] = $openingBalOfYear['transaction_amount'];
        } elseif ($firstFinancialYear != $firstYear && ($isFinYearClosed == '' || $isFinYearClosed == 0)) {
            $recoDetails['openingBalOfYearByLedger'] = $closingBalOfLastMonthLedger;
        } else {
            $recoDetails['openingBalOfYearByLedger'] = $openingBalOfYear['transaction_amount'];
        }

        if (strpos($monthNo, '4-') !== false) {
            $recoDetails['openingBalanceAsPerLedger'] = $recoDetails['openingBalOfYearByLedger'];
        } else {
            $recoDetails['openingBalanceAsPerLedger'] = $recoDetails['closingBalOfLastMonthByLedger'];
        }
        
        if(strpos($monthNo, '4-'.$firstFinancialYear) !== false)
        {
            $recoDetails['reconciledOpeningBalance'] = $recoDetails['openingBalOfYearByLedger'];
        } else {
            $recoDetails['reconciledOpeningBalance'] = $recoDetails['prev_reco_opning_bal'];
        }

        $recoDetails['closingBalanceAsPerLedger'] = $recoDetails['closingBalOfCurrentMonthLedger'];

        return $recoDetails;
    }

    public function getReconciledBalance($bankLedgerId, $date, $preDate, $startDate, $socId)
    {
        // Fetch the current reconciled balance within the specified date range
        $currentReconBalance = $this->tenantDB()->table('chsone_ledger_transactions')
            ->selectRaw("
                SUM(CASE WHEN transaction_type = 'dr' THEN transaction_amount ELSE 0 END) as curr_debit_sum,
                SUM(CASE WHEN transaction_type = 'cr' THEN transaction_amount ELSE 0 END) as curr_credit_sum
            ")
            ->where('soc_id', $socId)
            ->where('ledger_account_id', $bankLedgerId)
            ->where('is_reconciled', 1)
            ->whereBetween('value_date', [$startDate, $date])
            ->where(function ($query) {
                $query->where('is_cancelled', 0)->orWhereNull('is_cancelled');
            })
            ->where('is_opening_balance', 0)
            ->first();

        // Fetch the previous reconciled balance before the given date
        $prevReconBalance = $this->tenantDB()->table('chsone_ledger_transactions')
            ->selectRaw("
                SUM(CASE WHEN transaction_type = 'dr' THEN transaction_amount ELSE 0 END) as prev_debit_sum,
                SUM(CASE WHEN transaction_type = 'cr' THEN transaction_amount ELSE 0 END) as prev_credit_sum
            ")
            ->where('soc_id', $socId)
            ->where('ledger_account_id', $bankLedgerId)
            ->where('is_reconciled', 1)
            ->where('value_date', '<=', $preDate)
            ->where(function ($query) {
                $query->where('is_cancelled', 0)->orWhereNull('is_cancelled');
            })
            ->where('is_opening_balance', 0)
            ->first();

        return [
            'curr_debit_sum' => $currentReconBalance->curr_debit_sum ?? 0,
            'curr_credit_sum' => $currentReconBalance->curr_credit_sum ?? 0,
            'prev_debit_sum' => $prevReconBalance->prev_debit_sum ?? 0,
            'prev_credit_sum' => $prevReconBalance->prev_credit_sum ?? 0,
        ];
    }
    public function getFyStartDate($socId, $limit = null)
    {
        // Define the conditions and bindings for the query
        $condition = [
            ['soc_id', '=', $socId],
            ['confirmed', '=', 0],
        ];

        // Build the query using Laravel's Query Builder
        $query = $this->tenantDB()->table('soc_account_financial_year_master')
            ->where($condition)
            ->orderBy('account_closing_id', 'asc');

        // Check if a limit is set, if not, limit to 1 record
        if (is_null($limit)) {
            $query->limit(1);
        }

        // Execute the query and get the result
        return $query->get();
    }
    public function getReconciledBalanceOfPrevMonth($bankLedgerId, $startDatePrevMonth, $endDatePrevMonth, $year, $socId)
    {
        // Query to get current month's reconciled debit and credit sums
        $currentReconBalance = $this->tenantDB()->table('chsone_ledger_transactions')
            ->selectRaw("SUM(CASE WHEN transaction_type = 'dr' THEN transaction_amount ELSE 0 END) as curr_debit_sum,
                     SUM(CASE WHEN transaction_type = 'cr' THEN transaction_amount ELSE 0 END) as curr_credit_sum")
            ->where('soc_id', $socId)
            ->where('ledger_account_id', $bankLedgerId)
            ->where('is_reconciled', 1)
            ->whereBetween('value_date', [$startDatePrevMonth, $endDatePrevMonth])
            ->where(function ($query) {
                $query->where('is_cancelled', 0)
                    ->orWhereNull('is_cancelled');
            })
            ->first();

        // Query to get previous month's reconciled debit and credit sums
        $prevReconBalance = $this->tenantDB()->table('chsone_ledger_transactions')
            ->selectRaw("SUM(CASE WHEN transaction_type = 'dr' THEN transaction_amount ELSE 0 END) as prev_debit_sum,
                     SUM(CASE WHEN transaction_type = 'cr' THEN transaction_amount ELSE 0 END) as prev_credit_sum")
            ->where('soc_id', $socId)
            ->where('ledger_account_id', $bankLedgerId)
            ->where('is_reconciled', 1)
            ->where('value_date', '<', $startDatePrevMonth)
            ->where(function ($query) {
                $query->where('is_cancelled', 0)
                    ->orWhereNull('is_cancelled');
            });

        // Adjust the query if it's the start of the financial year
        if ($startDatePrevMonth == "$year-04-01") {
            $prevReconBalance->where('value_date', '<', $startDatePrevMonth);
        }

        $prevReconBalance = $prevReconBalance->first();

        // Merge the results from current and previous month
        $result = [
            'prev_debit_sum' => $prevReconBalance->prev_debit_sum ?? 0,
            'prev_credit_sum' => $prevReconBalance->prev_credit_sum ?? 0,
            'curr_debit_sum' => $currentReconBalance->curr_debit_sum ?? 0,
            'curr_credit_sum' => $currentReconBalance->curr_credit_sum ?? 0,
        ];

        return $result;
    }
    public function calculateClosingLedgerAmountOfCurrentMonth(array $data)
    {
        $socId = $data['soc_id'];
        $bankLedgerId = $data['bank_ledger_id'];
        $firstYear = $data['firstYear'];
        $currentMonthFirstDay = $data['currentMonthFirstDay'];
        $currentMonthEndDay = $data['currentMonthEndDay'];

        $result = $this->tenantDB()->table('chsone_ledger_transactions')
            ->selectRaw("
                SUM(CASE WHEN transaction_type = 'dr' THEN transaction_amount ELSE 0 END) as debit,
                SUM(CASE WHEN transaction_type = 'cr' THEN transaction_amount ELSE 0 END) as credit
            ")
            ->where('ledger_account_id', $bankLedgerId)
            ->where('soc_id', $socId)
            ->whereBetween('transaction_date', ["$firstYear-04-01", $currentMonthEndDay])
            ->where('is_cancelled', 0)
            ->first();

        $debitSum = $result->debit ?? 0;
        $creditSum = $result->credit ?? 0;
        return $debitSum - $creditSum;
    }
    public function calculateLedgerAmount(array $data)
    {
        $socId = $data['soc_id'];
        $bankLedgerId = $data['bank_ledger_id'];
        $firstYear = $data['firstYear'];
        $firstFinancialYear = $data['firstFinancialYear'];
        $lastMonthFirstDay = $data['lastMonthFirstDay'];
        $lastMonthEndDay = $data['lastMonthEndDay'];
        $firstYearStartDate = $firstYear . "-04-01";

        // Check if the year is reconciled in the MasterLedgerTxn table
        $isYearReconciled = $this->tenantDB()->table('chsone_bank_reco_master')
            ->where('soc_id', $socId)
            ->where('ledger_account_id', $bankLedgerId)
            ->where('year', $firstYear)
            ->where('month', 3) // Assuming March (month = 3)
            ->exists();

        // Check if the last month is March
        if (strpos($lastMonthFirstDay, "$firstYear-03-") !== false) {
            // Fetch the transaction date of the previous year opening balance
            $prevYearStartDate = $this->tenantDB()->table('chsone_ledger_transactions')
                ->select('transaction_date')
                ->where('ledger_account_id', $bankLedgerId)
                ->where('is_opening_balance', 1)
                ->where('transaction_date', '<', $firstYearStartDate)
                ->orderBy('transaction_date', 'desc')
                ->value('transaction_date');

            // Fetch the debit and credit sum for transactions from previous year's opening date to last month's end date
            $result = $this->tenantDB()->table('chsone_ledger_transactions')
                ->selectRaw("
                SUM(CASE WHEN transaction_type = 'dr' THEN transaction_amount ELSE 0 END) as debit,
                SUM(CASE WHEN transaction_type = 'cr' THEN transaction_amount ELSE 0 END) as credit
            ")
                ->where('ledger_account_id', $bankLedgerId)
                ->where('soc_id', $socId)
                ->whereBetween('transaction_date', [$prevYearStartDate, $lastMonthEndDay])
                ->where('is_cancelled', 0)
                ->first();
        } else {
            // Fetch the debit and credit sum for transactions for the current financial year
            $result = $this->tenantDB()->table('chsone_ledger_transactions')
                ->selectRaw("
                SUM(CASE WHEN transaction_type = 'dr' THEN transaction_amount ELSE 0 END) as debit,
                SUM(CASE WHEN transaction_type = 'cr' THEN transaction_amount ELSE 0 END) as credit
            ")
                ->where('ledger_account_id', $bankLedgerId)
                ->where('soc_id', $socId)
                ->whereBetween('transaction_date', [$firstYearStartDate, $lastMonthEndDay])
                ->where('is_cancelled', 0)
                ->first();
        }

        // Calculate the total amount
        if ($result) {
            $debitSum = $result->debit ?? 0;
            $creditSum = $result->credit ?? 0;
            $totalAmount = $creditSum - $debitSum;
        } else {
            $totalAmount = 0;
        }

        return $totalAmount;
    }
    public function getOpeningBalanceOfYear($data)
    {
        $socId = $data['soc_id'];
        $bankLedgerId = $data['bank_ledger_id'];
        $currentYear = $data['currentYear'];

        // Fetch the opening balance for the current financial year on April 1st
        $openingBalance = $this->tenantDB()->table('chsone_ledger_transactions')
            ->select('transaction_type', 'transaction_amount')
            ->where('ledger_account_id', $bankLedgerId)
            ->where('soc_id', $socId)
            ->where('transaction_date', "{$currentYear}-04-01")
            ->where('is_opening_balance', 1)
            ->where('is_reconciled', 1)
            ->where(function ($query) {
                $query->where('is_cancelled', 0)
                    ->orWhereNull('is_cancelled');
            })
            ->first();

        // If no opening balance is found, return an empty array
        if (!$openingBalance) {
            return ['transaction_amount' => 0];
        }

        // Adjust the amount based on the transaction type (debit/credit)
        $transactionAmount = $openingBalance->transaction_amount ?? 0;
        if ($openingBalance->transaction_type === 'cr') {
            $transactionAmount = -$transactionAmount;
        }

        return [
            'transaction_amount' => $transactionAmount,
        ];
    }
    public function checkFinancialYearClosed($data)
    {
        $socId = $data['soc_id'];
        $prevFirstYear = $data['firstYear'] - 1;
        $prevSecondYear = $data['secondYear'] - 1;

        // Query to check if the financial year is closed
        $result = $this->tenantDB()->table('soc_account_financial_year_master')
            ->where('fy_start_date', "{$prevFirstYear}-04-01")
            ->where('fy_end_date', "{$prevSecondYear}-03-31")
            ->where('soc_id', $socId)
            ->first();

            if (!empty($result)) {
                $result->closed = 0;
                return $result->closed;
            } else {
                return 0;
            }

        // return $result ? $result->closed : 0;
        return $result->closed = 0;
    }
    public function getLedgerBalanceOfPrevMonth($bankLedgerId, $startDatePrevMonth, $endDatePrevMonth, $firstYear, $socId)
    {
        // Query for current month's balance in March of the given year
        $currentReconBalance = $this->tenantDB()->table('chsone_ledger_transactions')
            ->selectRaw("SUM(CASE WHEN transaction_type = 'dr' THEN transaction_amount ELSE 0 END) AS curr_debit_sum,
                     SUM(CASE WHEN transaction_type = 'cr' THEN transaction_amount ELSE 0 END) AS curr_credit_sum")
            ->where('soc_id', $socId)
            ->where('ledger_account_id', $bankLedgerId)
            ->whereBetween('transaction_date', ["$firstYear-03-01", "$firstYear-03-31"])
            ->where(function ($query) {
                $query->where('is_cancelled', 0)
                    ->orWhereNull('is_cancelled');
            })
            ->first();

        // Query for previous year's balance before March
        $prevReconBalance = $this->tenantDB()->table('chsone_ledger_transactions')
            ->selectRaw("SUM(CASE WHEN transaction_type = 'dr' THEN transaction_amount ELSE 0 END) AS prev_debit_sum,
                     SUM(CASE WHEN transaction_type = 'cr' THEN transaction_amount ELSE 0 END) AS prev_credit_sum")
            ->where('soc_id', $socId)
            ->where('ledger_account_id', $bankLedgerId)
            ->where('transaction_date', '<', "$firstYear-03-01")
            ->where(function ($query) {
                $query->where('is_cancelled', 0)
                    ->orWhereNull('is_cancelled');
            })
            ->first();

        // Merge the results and return
        return [
            'curr_debit_sum' => $currentReconBalance->curr_debit_sum ?? 0,
            'curr_credit_sum' => $currentReconBalance->curr_credit_sum ?? 0,
            'prev_debit_sum' => $prevReconBalance->prev_debit_sum ?? 0,
            'prev_credit_sum' => $prevReconBalance->prev_credit_sum ?? 0,
        ];
    }
    public function getBankLedger($context = 'bank', $soc_id = null)
    {
        // Fetch bank ledger using Eloquent with the given conditions
        $bankLedger = $this->tenantDB()->table('chsone_grp_ledger_tree')->where('soc_id', $soc_id)
            ->where('context', $context)
            ->where('entity_type', 'ledger')
            ->get();

        // Check if records were found
        if ($bankLedger->isNotEmpty()) {
            return $bankLedger->toArray();
        } else {
            return false;
        }
    }

    public function getReconciledMonthsAction($data)
    {
        // Retrieve the financial year and soc_id from the request
        $fin_year = $data['yearSelected'] ?? null;
        $soc_id = $data['company_id'];

        $bankMonthArr = [];

        // Fetch bank accounts using Eloquent
        $bank_accounts = $this->getBankLedger('bank', $soc_id);

        // Iterate through each bank account
        foreach ($bank_accounts as $bank) {
            $bank = json_decode(json_encode($bank), true);
            $ledger_account_id = $bank['ledger_account_id'];

            // Initialize the months array with all months set to false
            $monthsStatus = [];
            for ($month = 4; $month <= 12; $month++) {
                $monthsStatus[$month] = false;
            }
            for ($month = 1; $month <= 3; $month++) {
                $monthsStatus[$month] = false;

            }

        if($fin_year){
            // Fetch distinct reconciled months for the current bank account
            $reconciledMonths = $this->tenantDB()->table('chsone_bank_reco_master')->selectRaw('DISTINCT month')
                ->where('soc_id', $soc_id)
                ->where('financial_year', $fin_year)
                ->where('ledger_account_id', $ledger_account_id)
                ->pluck('month')
                ->toArray();

            // Mark the reconciled months as true
            foreach ($reconciledMonths as $month) {
                $monthsStatus[$month] = true;
            }
        }

            // Store the result in the final array
            $bankMonthArr[$bank['ledger_account_name']] = $monthsStatus;
        }

        // Return the formatted result
        return $bankMonthArr;
    }

}
