<?php

namespace App\Console\Commands\Actions\Account;

use Illuminate\Console\Command;
use App\Console\Commands\Action;
use App\Models\Tenants\ChsoneGrpLedgerTree;
use App\Models\Tenants\ChsoneLedgerTransaction;
use App\Models\Tenants\IncomeInvoiceSetting;
use App\Models\Tenants\SocAccountFinancialYearMaster;
use Illuminate\Support\Facades\Config;

class CreateLedgerDataSource extends Action
{
    protected $constants;

    public function __construct()
    {
        parent::__construct();
        $this->constants = Config::get("constants");
    }
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'datasource:CreateLedger {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Create Ledger Data Source';

    protected $formatter = [];

    protected $formatterKey = 'id';

    protected $mapper = [];

    /**
     * Execute the console command.
     */
    public function apply()
    {
        if(isset($this->input['nature_of_group']) && strtolower(($this->input['nature_of_group']) == 'debit')){
            $this->input['nature_of_group'] = 'dr';
        } else {
            $this->input['nature_of_group'] = 'cr';
        }

        $addLedger = new ChsoneGrpLedgerTree();
        $parentLedger = $addLedger->_getLedgerProps($this->input["lstParentAccount"] ?? $this->input["parent_id"] ?? '');

        if (strtolower($parentLedger['ledger_account_name']) == 'bank' && empty($this->input['ledger_account_id'])) {
            $this->status = 'error';
            $this->message = 'Please add bank ledgers from Bank Acounts Form.';
            $this->statusCode = 400;
            return;
        }

        $soc_id = $this->input['company_id'] ?? '';
        $user_id = $this->input['user_id'] ?? '';
        $mode = $this->input['mode'] ?? 'add';
        $behaviour = $this->input['behaviour'] ?? (is_array($parentLedger) ? ($parentLedger['behaviour'] ?? '') : '');
        $nature = $this->input['nature'] ?? $this->input['nature_of_account'] ?? $this->input['nature_of_group'] ?? '';
        $entity_type = $this->input['entity_type'] ?? 'ledger';
        $ledger_account_name = $this->input['ledger_account_name'] ?? '';
        $opening_balance = $this->input['opening_balance'] ?? '0';
        $ledger_type = isset($this->input['ledger_type']) ? $this->input['ledger_type']
            : (isset($this->input['income_type']) ? strtolower($this->input['income_type'])
            : (isset($this->input['amount_debit_credit']) ? strtolower($this->input['amount_debit_credit'])
            : 'direct'));
        $ledg_acc_id = $this->input["ledger_account_id"] ?? '';
        $parent = $this->input["lstParentAccount"] ?? $this->input["parent_id"] ?? '';

        $ledgerDetails = $this->tenantDB()->table('soc_account_financial_year_master')
            ->select('account_closing_id as id', 'fy_start_date', 'fy_end_date', 'closed')
            ->orderBy('account_closing_id', 'asc')
            ->get();

        $arrFYDetail = SocAccountFinancialYearMaster::where('soc_id', $soc_id)
            ->where('confirmed', 0)
            ->orderBy('account_closing_id', 'desc')
            ->first();

        $transaction_date = $arrFYDetail->fy_start_date->timezone('Asia/Kolkata')->format('Y-m-d');

        $ledger_start_date = $ledgerDetails['fy_start_date'] ?? '';
        $invoice_settings = IncomeInvoiceSetting::where('soc_id', $soc_id)->first();
        $ledger_start_date = ($ledger_start_date) ? $ledger_start_date : $invoice_settings->effective_date;

        if (!empty($ledg_acc_id)) {

            $constants['edit_support_access'] = array(59919, 52925);
            $constants['support_access'] = array(635, 831, 780, 791, 1108);

            /*if (!in_array($user_id, $constants['support_access']) && !in_array($user_id, $constants['edit_support_access'])) {     //Dev
                $this->status = 'error';
                $this->message = 'You dont have access to update ledger.';
                $this->statusCode = 400;
                return;
            }*/

            $arrPrevYearBalance = array();
            $firstYearTransactionDate = '';

            foreach (request()->input() as $key => $value) {
                $arrTxnDetail = array();
                if (strstr($key, 'opening_balance_')) {
                    $arrTxnDetail = explode('opening_balance_', $key); //exit;
                    $arrPrevYearBalance[$arrTxnDetail[1]] = $value;
                }
                if (strstr($key, 'transaction_date_')) {
                    $arrTxnDateDetail = explode('transaction_date_', $key); //exit;
                    if (empty($arrTxnDateDetail[1])) {
                        $firstYearTransactionDate = $value;
                    }
                }
            }

            $objCurrentOpeningBalance = ChsoneLedgerTransaction::where('soc_id', $soc_id)
                ->where('ledger_account_id', $ledg_acc_id)
                ->where('is_opening_balance', 1)
                ->where('transaction_date', $transaction_date)
                ->first();

            if (!empty($objCurrentOpeningBalance->txn_id)) {
                $arrPrevYearBalance[$objCurrentOpeningBalance->txn_id] = $opening_balance;
            }

            $i = 0;

            if (!empty($arrPrevYearBalance) && count($arrPrevYearBalance) > 0) {
                foreach ($arrPrevYearBalance as $key => $value) {
                    if (!empty($key)) {
                        $transactionUpdate = ChsoneLedgerTransaction::where('soc_id', $soc_id)
                            ->where('txn_id', $key)
                            ->first();

                        if ($value != $transactionUpdate->transaction_amount || $transactionUpdate->transaction_type != $nature) {
                            $transactionUpdate->transaction_amount = $value;
                            if ($i == 0) {
                                $transactionUpdate->transaction_type = $nature;
                            }
                            if (!$transactionUpdate->save()) {
                                $this->status = 'error';
                                $this->message = 'Unable to update previous year opening balance.';
                                $this->statusCode = 400;
                                //$this->session->set("err_msg_ledg", "");
                                //return $this->response->redirect($this->config->system->full_base_url . "accounts/editLedger/" . $ledg_acc_id);
                            }
                        }
                    } elseif (empty($key) && !empty($firstYearTransactionDate)) {
                        $value = (!is_numeric($value)) ? 0 : $value;
                        if (!in_array(strtolower($behaviour), array(INCOME, EXPENSE))) {
                            $txn = new ChsoneLedgerTransaction();
                            //$txn->initialize($di);
                            $txn_date = $firstYearTransactionDate; //date("Y-m-d H:i:s");
                            $from_txn_id = (!empty($nature) && strtolower($nature) == 'cr') ? $nature : 'dr';
                            $txn->addTxn($ledg_acc_id, $value, 'entry for opening balance', $firstYearTransactionDate, "", "", $from_txn_id, "", "", "", $ledger_account_name, $is_opning = 1, $is_reco = 0);
                        } else {
                            $this->status = 'error';
                            $this->message = 'You cannot set opening balance for Income and Expense ledger.';
                            $this->statusCode = 400;
                            //$this->session->set("err_msg_ledg", "");
                            //return $this->response->redirect($this->config->system->full_base_url . "accounts/editLedger/" . $ledg_acc_id);
                        }
                    }
                    $id = $ledg_acc_id;
                    $i++;
                }
                $this->status = 'success';
                $this->message = 'Ledger edited successfully.';
                $this->statusCode = 200;
            }
        } else {
            $id = $addLedger->manipulateByArray(array(
                'soc_id' => $this->input['company_id'],
                'ledger_account_name' => $ledger_account_name,
                'entity_type' => $entity_type,
                'parent_group' => $parent,
                'behaviour' => $behaviour,
                'ledger_account_id' => $ledg_acc_id,
                'opening_balance' => $opening_balance,
                'ledger_start_date' => $ledger_start_date,
                'ledger_type' => $ledger_type,
                'nature' => $nature,
                'input' => $this->input,
            ));

            if (!empty($txn_id)) {
                $transaction = ChsoneLedgerTransaction::where('soc_id', $soc_id)->where('txn_id', $txn_id)->first();
                if(!empty($transaction)) {
                    $transaction->transaction_date = $ledger_start_date;
                    $transaction->transaction_type = $nature;
                    $transaction->save();
                }
            }

            if (is_numeric($id) && $id > 0) {
                $this->status = 'success';
                $this->message = 'Ledger added successfully.';
                $this->statusCode = 200;
            } else {
                $this->status = 'error';
                $this->message = $ledger_account_name.' ledger name already exists.';
                $this->statusCode = 400;
            }
        }
    }
}
