<?php

namespace App\Console\Commands\Actions\Account\Challans;

use Illuminate\Console\Command;
use App\Console\Commands\Action;
use App\Models\Tenants\ChsoneTdsChallan;
use App\Models\Tenants\ChsoneLedgerTransaction;

class CancelChallansAction extends Action
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'action:cancelChallans {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Cancel TDS Challans';

    /**
     * Execute the console command.
     */
    public function apply()
    {
        $id = $this->input['id'];
        $soc_id = $this->input['company_id'];
        $data['cancel_reason'] = $this->input['cancel_reason'];
        $user_id = $this->input['user_id'] ?? 0;


        $tdsData = $this->tenantDB()->table('chsone_tds_challans')
            ->select('tds_challan_id', 'soc_id', 'challan_serial_no', 'tender_date', 'payment_mode', 'bank_account', 'income_tax', 'surcharges', 'education_cess', 'interest', 'penalty_code', 'penalty', 'others', 'fee_under_sec_234e')
            ->where('tds_challan_id', $id)->first();

        if (empty($tdsData)) {
            $this->message = 'TDS Challan not found.';
            $this->status = "error";
            $this->statusCode = 400;
            return;
        } else {
            $tdsData = json_decode(json_encode($tdsData), true);


            /** Transaction Started * */
            $this->tenantDB()->beginTransaction();

            /** Get TDS Ledger Account ID* */
            $tdsLedger = $this->tenantDB()->table('chsone_challan_accounts')->where('context', 'tds')->first();
            /** Cancel TDS Challan * */


            if (empty($tdsLedger)) {
                // Setup Tax Ledger Code
                $this->tenantDB()->rollBack();
                $this->message = 'Setup TDS Challan Account Ledger First.';
                $this->status = "error";
                $this->statusCode = 400;
                return;
                // $this->session->set("err_msg", 'Setup TDS Challan Account Ledger First.');
            }
            $tdsLedger = json_decode(json_encode($tdsLedger), true);

            $tdsData['total'] = $tdsData['income_tax'] + $tdsData['surcharges'] + $tdsData['education_cess'] + $tdsData['interest'] + $tdsData['penalty_code'] + $tdsData['penalty'] + $tdsData['others'] + $tdsData['fee_under_sec_234e'];

            $arrToLedgerDetails = $this->checkledgerExistNew(array("soc_id" => $soc_id, 'ledger_id' => $tdsLedger['ledger_id']));

            /** If Mode is Cash then Get Cash in Hand Ledger Id and Ignore bank Account ID * */
            if ($tdsData['payment_mode'] == 'cash') {
                $cashAccount = $this->tenantDB()->table('chsone_accounts_master')
                    ->where('soc_id', $tdsData['soc_id'])
                    ->where('bank_name', 'Cash in Hand')
                    ->first();

                $cashAccount = json_decode(json_encode($cashAccount), true);
                $tdsData['bank_account'] = $cashAccount['ledger_account_id'];
            }
            /** Ledger Transactions * */
            $arrFromLedgerDetails = $this->checkledgerExistNew(array("soc_id" => $soc_id, 'ledger_id' => $tdsData['bank_account']));

            $narration = 'TDS Challan Sr. No ' . $tdsData['challan_serial_no'] . ' cancelled due to ' . $data['cancel_reason'];
            // Assuming $tdsData['transaction_reference'] is optional
            $transactionReference = !empty($tdsData['transaction_reference']) ? $tdsData['transaction_reference'] : null;

            // Call executeVoucher, passing null for transaction_reference if it's not available
            $this->executeVouchers(
                "",
                $arrFromLedgerDetails['recieving_ledger_id'],
                $arrToLedgerDetails['recieving_ledger_id'],
                $tdsData['tender_date'],
                $tdsData['total'],
                $narration,
                $arrFromLedgerDetails['receiver_name'],
                $arrToLedgerDetails['receiver_name'],
                $transactionReference,  // Pass null or empty string if not available
                "",
                $tdsData['payment_mode'],
                "",
                $soc_id
            );


            $response = $this->cancelTdsChallanResponse(array('id' => $id, 'data' => $data, 'user' => $user_id ?? 0, 'soc_id' => $soc_id));

            $response = json_decode(json_encode($response), true);

            if ($response && $response['error'] == true) {
                $this->tenantDB()->rollBack();
                $this->message = 'Unable to cancel TDS Challan.';
                $this->status = "error";
                $this->statusCode = 400;
                return;
            } else {
                /** Commit Record * */
                $this->tenantDB()->commit();
                $this->message = 'TDS Challan has been cancelled successfully.';
                $this->status = "success";
                $this->statusCode = 200;
                return;
            }
        }
    }

    public function cancelTdsChallanResponse($data = [])
    {
        try {

            $user = $data['user'];
            $id = $data['id'];
            $soc_id = $data['soc_id'];

            // Find the TDS Challan by its ID and associated soc_id
            $challan = $this->tenantDB()->table('chsone_tds_challans')->where('tds_challan_id', $id)
                ->where('soc_id', $soc_id)
                ->first();

            if (!$challan) {
                $challan2 = $this->tenantDB()->table('chsone_tds_challans')->where('tds_challan_id', $id)
                    ->where('soc_id', intval($soc_id))
                    ->first();


                if (!$challan2) { // Fixed missing `$` before challan2
                    $this->message = 'TDS Challan not found.';
                    $this->status = "error";
                    $this->statusCode = 400;
                    return;
                }

                $soc_id = intval($soc_id);
                $challan = $challan2; // Assign the value of challan2 to challan if found
            }
            // Perform the update
            $result = $this->tenantDB()->table('chsone_tds_challans')
                ->where('tds_challan_id', $id)
                ->where('soc_id', $soc_id)
                ->update([
                    'updated_by' => $user ?? null,
                    'updated_date' => $user ? now() : null,
                    'status' => 0,
                    'cancel_reason' => $data['cancel_reason'] ?? null,
                ]);

            if ($result) {

                // Success response
                return [
                    'error' => false,
                    'data' => $challan->tds_challan_id
                ];
            } else {
                // Validation failed, return the errors
                return
                    [
                        'error' => true,
                        'data' => ['message' =>
                        'Unable to cancel TDS Challan.']
                    ];
            }
        } catch (\Exception $e) {
            // Catch exceptions and log the error if needed

            return [
                'error' => true,
                'data' => ['message' =>
                'Unable to cancel TDS Challan.']
            ];
        }
    }

    public function checkledgerExistNew($data = [])
    {
        $data = json_decode(json_encode($data), true);
        $arrNonmemberAccounts = [];
        $soc_id = $data['soc_id'];

        $condition = '';
        // Building condition based on ledger name or ledger ID
        if (isset($data['ledger_name']) && !empty($data['ledger_name'])) {
            $condition = 'soc_id="' . $soc_id . '" AND context="' . $data['context'] . '" AND entity_type="' . ENTITY_TYPE_LEDGER . '" AND ledger_account_name = "' . $data['ledger_name'] . '"';
            if (isset($data['behaviour']) && !empty($data['behaviour']) && !empty($condition)) {
                $condition .= ' AND behaviour="' . $data['behaviour'] . '"';
            }
        } elseif (isset($data['ledger_id']) && !empty($data['ledger_id'])) {

            $condition = 'soc_id="' . $soc_id . '" AND ledger_account_id="' . $data['ledger_id'] . '"';
        }

        $arrClinetLedgerDetails = [];

        // Check if the ledger exists
        $objBookerLedger = $this->tenantDB()->table('chsone_grp_ledger_tree')->whereRaw($condition)->first();

        if (!empty($objBookerLedger)) {

            $arrClinetLedgerDetails['recieving_ledger_id'] = $objBookerLedger->ledger_account_id;
            $arrClinetLedgerDetails['receiver_name'] = $objBookerLedger->ledger_account_name;
        } elseif (!empty($data['group_name'])) {

            // Handling the creation of new ledger if not found
            // $data ['group_name'] = 'Bank';//need to remove this line testing purpose only
            // $data ['behaviour'] = 'asset';//need to remove this line testing purpose only\
            // $data ['context'] = 'bank';//need to remove this line testing purpose only
            $name = $data['ledger_name'] ?? '';
            $entity_type = ENTITY_TYPE_LEDGER;
            $grp_ledg_id = "";

            // Fetch parent group ID
            $parent = $this->getParentGroupId($data);
            if (empty($parent->ledger_account_id)) {
                return $arrClinetLedgerDetails;
            }

            $parent_group = $parent->ledger_account_id;

            // Create a new ledger
            $ledger_id = $this->manipulate(
                $name,
                $entity_type,
                $grp_ledg_id,
                $parent_group,
                "",
                '',
                0,
                '',
                '',
                '',
                '',
                '',
                $soc_id,
                0
            );

            if ($ledger_id) {
                $arrClinetLedgerDetails['recieving_ledger_id'] = $ledger_id;
                $arrClinetLedgerDetails['receiver_name'] = $data['ledger_name'];
            }
        }

        return $arrClinetLedgerDetails;
    }

    public function getParentGroupId($arrData)
    {
        // Fetch parent group details based on provided criteria
        $parent = $this->tenantDB()->table('chsone_grp_ledger_tree')
            ->where('soc_id', $arrData['soc_id'])
            ->where('ledger_account_name', $arrData['group_name'])
            ->where('entity_type', ENTITY_TYPE_GROUP)
            ->where('behaviour', $arrData['behaviour'])
            ->where('context', $arrData['context'])
            ->first();

        return $parent;
    }


    public function manipulate($name, $entity_type = ENTITY_TYPE_GROUP, $grp_ledg_id = "", $parent_group = 0, $behaviour = "", $ledg_id = '', $opening_balance = 0, $update_led_id = '', $ledger_start_date = '', $ledger_type = '', $context = '', $txn_id = 0, $soc_id = 0, $is_reco = 0)
    {
        global $config, $di;

        $dup = $this->_checkGroupNameDuplication($parent_group, $name, $ledg_id, $update_led_id, $soc_id);

        $arrFYDetail = $this->getCurrentFinancialYear(['soc_id' => $soc_id]);
        $ledger_start_date = $arrFYDetail['fy_start_date'];

        if ($dup == 0) {
            $behaviour = trim(strtolower($behaviour));

            // Check if we need to update or create a new ledger
            $grp_ledg_tree = $this->tenantDB()->table('chsone_grp_ledger_tree');
            if ($ledg_id) {
                $grp_ledg_tree = $this->getLedger($ledg_id);
            }

            // Prepare data for insertion or update
            $grpLedgData = [
                'entity_type' => $entity_type,
                'soc_id' => $soc_id,
                'ledger_account_name' => $name,
                'ledger_start_date' => $ledger_start_date,
                'context_ref_id' => 0,
                'operating_type' => (!empty($ledger_type) && $ledger_type != "NULL" && $ledger_type != '') ? $ledger_type : '',
                'parent_id' => !empty($parent_group) ? $parent_group : HEAD_GROUP_VAL,
                'behaviour' => !empty($behaviour) ? $behaviour : $this->_getLedgerProps($parent_group)['behaviour'],
                'nature_of_account' => $config->nature_account->$behaviour ?? $this->_getLedgerProps($parent_group)['nature_account'],
                'report_head' => $config->report_head->$behaviour ?? $this->_getLedgerProps($parent_group)['report_head'],
                'context' => !empty($context) ? $context : $this->_getLedgerProps($parent_group)['context'],
                'defined_by' => USER,
                'status' => ACTIVE,
                'added_on' => date("Y-m-d H:i:s"),
                'created_by' => 0,
            ];

            // Set ledger_account_id if provided
            if (!empty($grp_ledg_id)) {
                $grpLedgData['ledger_account_id'] = $grp_ledg_id;
            }
            if ($update_led_id != '') {
                $grpLedgData['ledger_account_id'] = $update_led_id;
            }

            // Insert or update ledger in the database
            $result = $grp_ledg_tree->insertOrIgnore($grpLedgData);

            if ($result) {
                // Handle transaction for opening balance if applicable
                if (!in_array(strtolower($grpLedgData['behaviour']), [INCOME, EXPENSE])) {
                    $txn_date = $ledger_start_date;
                    $narration = 'entry for opening balance';
                    if ($txn_id = $this->addTxn($grpLedgData['ledger_account_id'], $opening_balance, $narration, $txn_date, "", $grpLedgData['nature_of_account'], "", "", "", "", $name, $is_opning = 1, $is_reco, $soc_id)) {
                        // Transaction successful
                    } else {
                        // Handle failure
                    }
                }
                return $grpLedgData['ledger_account_id'];
            } else {
                // Handle save failure
                return false;
            }
        } else {
            return "DUP" . $dup;
        }
    }

    public function _checkGroupNameDuplication($parent, $name, $ledg_id = "", $update_led_id = "", $soc_id)
    {

        // Initialize the query to check for group name duplication
        $query = $this->tenantDB()->table('chsone_grp_ledger_tree')
            ->where('soc_id', $soc_id)
            ->whereRaw('LOWER(ledger_account_name) = ?', [strtolower(trim($name))]);

        // Exclude the current ledger account if a ledger ID is provided
        if ($ledg_id) {
            $query->where('ledger_account_id', '!=', $ledg_id);
        }

        // Exclude the ledger account based on the update ledger ID, if provided
        if ($update_led_id) {
            $query->where('ledger_account_id', '!=', $update_led_id);
        }

        // Execute the query and count the number of matching records
        $ledgers_count = $query->count();

        return $ledgers_count;
    }

    public function getCurrentFinancialYear(array $data = [])
    {
        // Fetch the current financial year details based on the provided conditions
        $arrFYDetail = $this->tenantDB()->table('soc_account_financial_year_master')
            ->where('soc_id', $data['soc_id'])
            ->where('confirmed', 0)
            ->orderBy('account_closing_id', direction: 'asc')
            ->first();

        // Convert the result to an array if found, otherwise return an empty array
        return $arrFYDetail ? (array) $arrFYDetail : [];
    }

    public function getLedger($ledgerId, $returnObj = 1)
    {
        $ledger = $this->tenantDB()->table("chsone_grp_ledger_tree")
            ->where("ledger_account_id", $ledgerId)
            ->first();
        if ($ledger) {
            if ($returnObj) {
                return $ledger;
            } else {
                return $ledger;
            }
        } else {
            return false;
        }
    }

    public function executeVouchers(
        $voucher_type,
        $from_ledg_id,
        $to_ledg_id,
        $txn_date,
        $txn_amt,
        $narration,
        $from_ledger_name,
        $to_ledger_name,
        $recp_ref = "",
        $type = "",
        $mode_of_payment = "",
        $other_recp_ref = "",
        $soc_id = "",
        $created_by = "",
        $voucher_reference_id = "",
        $voucher_reference_number = "",
        $is_cancelled = 0
    ) {
        // Split the voucher_type if it contains an underscore
        $vouchers_array = [];
        if (strpos($voucher_type, '_') !== false && strpos($voucher_type, '_') > 0) {
            $vouchers_array = explode('_', $voucher_type);
            $voucher_type = $vouchers_array[0];
        }

        $this->tenantDB()->beginTransaction();

        try {
            // Add the first transaction
            $txn_from_id = $this->_addTransaction(
                0,
                $from_ledg_id,
                $txn_amt,
                $narration,
                $txn_date,
                $voucher_type,
                $type,
                "",
                $mode_of_payment,
                $recp_ref,
                $from_ledger_name,
                0, // is_opning
                $other_recp_ref,
                $soc_id,
                $created_by,
                $voucher_reference_id,
                $voucher_reference_number,
                $is_cancelled
            );

            if ($txn_from_id) {
                // If there are multiple voucher types, handle the second part
                if (count($vouchers_array) >= 2) {
                    $voucher_type = $vouchers_array[1];
                }

                // Add the second transaction
                $txn_to_id = $this->_addTransaction(
                    0,
                    $to_ledg_id,
                    $txn_amt,
                    $narration,
                    $txn_date,
                    $voucher_type,
                    $type,
                    $txn_from_id,
                    $mode_of_payment,
                    $recp_ref,
                    $to_ledger_name,
                    0, // is_opning
                    $other_recp_ref,
                    $soc_id,
                    $created_by,
                    $voucher_reference_id,
                    $voucher_reference_number,
                    $is_cancelled
                );

                if ($txn_to_id) {
                    // Commit the transaction if both operations succeeded
                    $this->tenantDB()->commit();
                    return $txn_from_id;
                } else {
                    // Rollback if the second transaction fails
                    $this->tenantDB()->rollBack();
                    return false;
                }
            } else {
                // Rollback if the first transaction fails
                $this->tenantDB()->rollBack();
                return false;
            }
        } catch (\Exception $e) {
            // Handle any exceptions and rollback the transaction
            $this->tenantDB()->rollBack();
            throw $e;
        }
    }



    private function _addTransaction($is_reco = 0, $ledger_id, $txn_amt, $narration, $txn_date, $voucher_type = VOUCHER_JOURNAL, $txn_type = "", $from_txn_id = "", $pay_mode = "", $pay_ref = "", $name = "", $is_opning = "", $other_recp_ref = "", $soc_id = "", $created_by = "", $voucher_reference_id = "", $voucher_reference_number = "", $is_cancelled = 0)
    {
        $txn = new ChsoneLedgerTransaction();
        if (empty($soc_id)) {
            $soc_id = $this->input['company_id'];
        }

        if (!empty($txn_type) || $txn_type != "") {
            $type_txn = $txn_type;
        } else {
            if ($from_txn_id) {
                $mode     = "to";
                $type_txn = "cr";
            } else {
                $mode     = "from";
                $type_txn = "dr";
            }
        }

        //echo "s_opning".$is_opning;exit;
        if ($is_opning == 1) {

            $txn_entrt = ChsoneLedgerTransaction::where('soc_id', $soc_id)
                ->where('is_opening_balance', 1)
                ->where('ledger_account_id', $ledger_id)
                ->where('transaction_date', $txn_date)
                ->first();

            $txn->value_date = $txn_date;
            $txn->txn_id = (isset($txn_entrt->txn_id)  && !empty($txn_entrt->txn_id)) ? $txn_entrt->txn_id : '';
            if (!empty($txn->txn_id)) {
                $txn = ChsoneLedgerTransaction::findFirst("txn_id = " . $txn->txn_id);
            }
        }

        $txn->soc_id = $soc_id;
        $txn->transaction_date = $txn_date;
        $txn->ledger_account_id = $ledger_id;
        $txn->ledger_account_name = $name;
        $txn->voucher_type = $voucher_type;
        $txn->transaction_type = $type_txn; //$ledger_name_obj->nature_of_account;//$txn_type;
        $txn->payment_mode = $pay_mode;
        $txn->payment_reference = $pay_ref;
        $txn->transaction_amount = (is_null($txn_amt)) ? $txn->transaction_amount : $txn_amt;
        $txn->other_reference_id = $other_recp_ref;
        $txn->txn_from_id = $from_txn_id;
        $txn->memo_desc = $narration;
        $txn->is_opening_balance = $is_opning;
        $txn->is_reconciled = $is_reco;
        /*Added New Column in transaction*/
        $txn->voucher_reference_number = $voucher_reference_number;
        $txn->voucher_reference_id = $voucher_reference_id;
        $txn->is_cancelled = $is_cancelled;
        /*End New Column in transaction*/
        $txn->created_by = $this->input['user_id'] ?? 0;
        if (isset($created_by) && !empty($created_by)) {
            $txn->created_by = $created_by;
        }
        $txn->added_on = date("Y-m-d H:i:s");

        if ($txn->save()) {
            return $txn->txn_id;
        } else {
            print_r($txn->getMessages);
        }
        //echo "<pre>";print_r($a->getMessages);exit;
        return false;
    }
}
