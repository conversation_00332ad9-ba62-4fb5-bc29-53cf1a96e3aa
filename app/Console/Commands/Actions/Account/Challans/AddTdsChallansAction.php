<?php

namespace App\Console\Commands\Actions\Account\Challans;

use Illuminate\Console\Command;
use App\Console\Commands\Action;
use App\Models\Tenants\ChsoneTdsChallan;
use App\Models\Tenants\ChsoneLedgerTransaction;

class AddTdsChallansAction extends Action
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'action:addTdsChallans {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Add TDS Challans';

    /**
     * Execute the console command.
     */
    public function apply()
    {
        $id = $this->input['id'] ?? null;
        $soc_id = $this->input['company_id'];
        $user_id = $this->input['user_id'] ?? 0;
        $data = $this->input;



        $challan = $this->tenantDB()->table('chsone_tds_challans')
        ->where('challan_no', $this->input['challan_no'])
        ->where('soc_id', $soc_id)
        ->first();

        if($challan) {
            $this->message = 'TDS Challan already exists.';
            $this->status = "error";
            $this->statusCode = 400;
            return;
        }


        /** Transaction Started * */
        $this->tenantDB()->beginTransaction();

        /** Get TDS Ledger Account ID* */
        $tdsLedger = $this->tenantDB()->table('chsone_challan_accounts')->where('context', 'tds')->first();
        /** Cancel TDS Challan * */

        if (empty($tdsLedger)) {
            // Setup Tax Ledger Code
            $this->tenantDB()->rollBack();
            $this->message = 'Setup TDS Challan Account Ledger First.';
            $this->status = "error";
            $this->statusCode = 400;
            return;
            // $this->session->set("err_msg", 'Setup TDS Challan Account Ledger First.');
        }
        $tdsLedger = json_decode(json_encode($tdsLedger), true);


        $arrFromLedgerDetails = $this->checkledgerExistNew(array("soc_id" => $soc_id, 'ledger_id' => $tdsLedger['ledger_id']));

        /** If Mode is Cash then Get Cash in Hand Ledger Id and Ignore bank Account ID * */
        if ($data['payment_mode'] == 'cash') {
            $cashAccount = $this->tenantDB()->table('chsone_accounts_master')
                ->where('soc_id', $this->input['company_id'])
                ->where('bank_name', 'Cash in Hand')
                ->first();

            if (empty($cashAccount) || $cashAccount == null) {
                $this->tenantDB()->rollBack();
                $this->message = 'Cash in Hand Account not found.';
                $this->status = "error";
                $this->statusCode = 400;
                return;
            }
            $cashAccount = json_decode(json_encode($cashAccount), true);
            $data['bank_account'] = $cashAccount['ledger_account_id'];
        }
        /** Ledger Transactions * */

        $arrToLedgerDetails = $this->checkledgerExistNew(array("soc_id" => $soc_id, 'ledger_id' => $data['bank_account']));
        // dd($arrToLedgerDetails);
        $transactionReference = $data['transaction_reference'] ?? '';

        $narration = 'TDS Challan paid on ' . $this->getDisplayDate($data['payment_date']) . ' via ' . $data['payment_mode'] . ' with SR. No ' . $data['challan_serial_no'] . ' and BSR Code ' . $data['bsr_code'];
        $fromLedgerData = $this->executeVouchers("", $arrFromLedgerDetails['receiving_ledger_id'], $arrToLedgerDetails['receiving_ledger_id'], $data['payment_date'], $data['total'], $narration, $arrFromLedgerDetails['receiver_name'], $arrToLedgerDetails['receiver_name'], $transactionReference, "", $data['payment_mode'], "", $soc_id);
        $data['from_ledger_id'] = $fromLedgerData;

        /** Save TDS Challan * */
        $response = $this->saveTdsChallanResponse(array('id' => $id, 'data' => $data, 'user' => $user_id, 'soc_id' => $soc_id));


        if ($response['error'] == true) {
            /** Rollback Record * */
            $this->tenantDB()->rollBack();
            $this->message = 'Unable to save TDS Challan.';
            $this->status = "error";
            $this->statusCode = 400;
            return;
        } else {
            /** Commit Record * */
            $this->tenantDB()->commit();
            $this->message = 'TDS Challan saved successfully.';
            $this->status = "success";
            $this->statusCode = 200;
            return;
        }
    }

    public function saveTdsChallanResponse($data = [])
    {

        // try {
        $user = $data['user'];
        $id = $data['id'] ?? null;
        $formData = $data['data'];
        $soc_id = $this->input['company_id'];

        // Check if the TDS Challan already exists
        if ($id) {

            $challan = $this->tenantDB()->table('chsone_tds_challans')
                ->where('tds_challan_id', $id)
                ->where('soc_id', $soc_id)
                ->first();


            if (!$challan) {
                return response()->json([
                    'error' => true,
                    'message' => 'TDS Challan not found.',
                ], 400);
            }

            // Update existing challan
            $updated = $this->tenantDB()->table('chsone_tds_challans')
                ->where('tds_challan_id', $id)
                ->update([
                    'updated_by' => $user,
                    'updated_date' => now(),
                ]);

            if (!$updated) {
                return response()->json([
                    'error' => true,
                    'message' => 'Failed to update TDS Challan.',
                ], 500);
            }
        } else {

            // Prepare data for new TDS Challan
            $challanData = [
                'created_by' => $user,
                'created_date' => now(),
                'soc_id' => $this->input['company_id'],
                'challan_no' => $formData['challan_no'],
                'submitted_bank' => $formData['submitted_bank'],
                'payment_mode' => $formData['payment_mode'],
                'assessment_year' => $formData['assessment_year'],
                'tan_no' => $formData['tan_no'] ?? null,
                'tax_applicable' => $formData['tax_applicable'],
                'type_of_payment' => $formData['type_of_payment'],
                'income_tax' => $formData['income_tax'],
                'surcharges' => $formData['surcharges'],
                'education_cess' => $formData['education_cess'],
                'interest' => $formData['interest'],
                'penalty_code' => $formData['penalty_code'],
                'penalty' => $formData['penalty'],
                'others' => $formData['others'],
                'fee_under_sec_234e' => $formData['fee_under_sec_234e'],
                'bsr_code' => $formData['bsr_code'],
                'tender_date' => $formData['payment_date'],
                'challan_serial_no' => $formData['challan_serial_no'],
                'on_account_of' => $formData['on_account_of'],
                'status' => 1,
                'bank_account' => $formData['payment_mode'] === 'cash' ? 0 : $formData['bank_account'],
                'from_ledger_id' => $formData['from_ledger_id'] ?? null,
                'to_ledger_id' => $formData['to_ledger_id'] ?? null,
            ];

            // Insert new challan
            $newChallanId = $this->tenantDB()->table('chsone_tds_challans')->insertGetId($challanData);


            if (!$newChallanId) {
                return [
                    'error' => true,
                    'message' => 'Unable to save TDS Challan.',
                ];
            }
        }


        return [
            'error' => false,
            'data' => $id ?? $newChallanId,
        ];
        // } catch (\Exception $e) {
        //     return [
        //         'error' => true,
        //         'message' => 'Unable to save TDS Challan.',
        //     ];
        // }
    }


    public function executeVouchers(
        $voucher_type,
        $from_ledg_id,
        $to_ledg_id,
        $txn_date,
        $txn_amt,
        $narration,
        $from_ledger_name,
        $to_ledger_name,
        $recp_ref = "",
        $type = "",
        $mode_of_payment = "",
        $other_recp_ref = "",
        $soc_id = "",
        $created_by = "",
        $voucher_reference_id = "",
        $voucher_reference_number = "",
        $is_cancelled = 0
    ) {
        // Split the voucher_type if it contains an underscore
        $vouchers_array = [];
        if (strpos($voucher_type, '_') !== false && strpos($voucher_type, '_') > 0) {
            $vouchers_array = explode('_', $voucher_type);
            $voucher_type = $vouchers_array[0];
        }

        $this->tenantDB()->beginTransaction();

        try {
            // Add the first transaction
            $txn_from_id = $this->_addTransaction(
                0,
                $from_ledg_id,
                $txn_amt,
                $narration,
                $txn_date,
                $voucher_type,
                $type,
                "",
                $mode_of_payment,
                $recp_ref,
                $from_ledger_name,
                0, // is_opning
                $other_recp_ref,
                $soc_id,
                $created_by,
                $voucher_reference_id,
                $voucher_reference_number,
                $is_cancelled
            );

            if ($txn_from_id) {
                // If there are multiple voucher types, handle the second part
                if (count($vouchers_array) >= 2) {
                    $voucher_type = $vouchers_array[1];
                }

                // Add the second transaction
                $txn_to_id = $this->_addTransaction(
                    0,
                    $to_ledg_id,
                    $txn_amt,
                    $narration,
                    $txn_date,
                    $voucher_type,
                    $type,
                    $txn_from_id,
                    $mode_of_payment,
                    $recp_ref,
                    $to_ledger_name,
                    0, // is_opning
                    $other_recp_ref,
                    $soc_id,
                    $created_by,
                    $voucher_reference_id,
                    $voucher_reference_number,
                    $is_cancelled
                );

                if ($txn_to_id) {
                    // Commit the transaction if both operations succeeded
                    $this->tenantDB()->commit();
                    return $txn_from_id;
                } else {
                    // Rollback if the second transaction fails
                    $this->tenantDB()->rollBack();
                    return false;
                }
            } else {
                // Rollback if the first transaction fails
                $this->tenantDB()->rollBack();
                return false;
            }
        } catch (\Exception $e) {
            // Handle any exceptions and rollback the transaction
            $this->tenantDB()->rollBack();
            throw $e;
        }
    }

    private function _addTransaction($is_reco = 0, $ledger_id, $txn_amt, $narration, $txn_date, $voucher_type = VOUCHER_JOURNAL, $txn_type = "", $from_txn_id = "", $pay_mode = "", $pay_ref = "", $name = "", $is_opning = "", $other_recp_ref = "", $soc_id = "", $created_by = "", $voucher_reference_id = "", $voucher_reference_number = "", $is_cancelled = 0)
    {
        $txn = new ChsoneLedgerTransaction();
        if (empty($soc_id)) {
            $soc_id = $this->input['company_id'];
        }

        if (!empty($txn_type) || $txn_type != "") {
            $type_txn = $txn_type;
        } else {
            if ($from_txn_id) {
                $mode     = "to";
                $type_txn = "cr";
            } else {
                $mode     = "from";
                $type_txn = "dr";
            }
        }

        //echo "s_opning".$is_opning;exit;
        if ($is_opning == 1) {

            $txn_entrt = ChsoneLedgerTransaction::where('soc_id', $soc_id)
                ->where('is_opening_balance', 1)
                ->where('ledger_account_id', $ledger_id)
                ->where('transaction_date', $txn_date)
                ->first();

            $txn->value_date = $txn_date;
            $txn->txn_id = (isset($txn_entrt->txn_id)  && !empty($txn_entrt->txn_id)) ? $txn_entrt->txn_id : '';
            if (!empty($txn->txn_id)) {
                $txn = ChsoneLedgerTransaction::findFirst("txn_id = " . $txn->txn_id);
            }
        }

        $txn->soc_id = $soc_id;
        $txn->transaction_date = $txn_date;
        $txn->ledger_account_id = $ledger_id;
        $txn->ledger_account_name = $name;
        $txn->voucher_type = $voucher_type;
        $txn->transaction_type = $type_txn; //$ledger_name_obj->nature_of_account;//$txn_type;
        $txn->payment_mode = $pay_mode;
        $txn->payment_reference = $pay_ref;
        $txn->transaction_amount = (is_null($txn_amt)) ? $txn->transaction_amount : $txn_amt;
        $txn->other_reference_id = $other_recp_ref;
        $txn->txn_from_id = $from_txn_id;
        $txn->memo_desc = $narration;
        $txn->is_opening_balance = $is_opning;
        $txn->is_reconciled = $is_reco;
        /*Added New Column in transaction*/
        $txn->voucher_reference_number = $voucher_reference_number;
        $txn->voucher_reference_id = $voucher_reference_id;
        $txn->is_cancelled = $is_cancelled;
        /*End New Column in transaction*/
        $txn->created_by = $user_id ?? 0;
        if (isset($created_by) && !empty($created_by)) {
            $txn->created_by = $created_by;
        }
        $txn->added_on = date("Y-m-d H:i:s");

        if ($txn->save()) {
            return $txn->txn_id;
        } else {
            print_r($txn->getMessages);
        }
        //echo "<pre>";print_r($a->getMessages);exit;
        return false;
    }
}
