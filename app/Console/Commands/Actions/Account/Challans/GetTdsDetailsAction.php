<?php

namespace App\Console\Commands\Actions\Account\Challans;

use Illuminate\Console\Command;
use App\Console\Commands\Action;

class GetTdsDetailsAction extends Action
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'action:getTdsDetails {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Get TDS Details';

    /**
     * Execute the console command.
     */
    public function apply()
    {
        $obj = $this->tenantDB()->table('chsone_tds_challans')
        ->selectRaw(
            "tds_challan_id AS id,
            challan_no,
            assessment_year,
            bsr_code,
            tender_date,
            income_tax,
            surcharges,
            education_cess,
            penalty_code,
            interest,
            penalty,
            others,
            fee_under_sec_234e,
            tan_no,
            payment_mode,
            tax_applicable,
            submitted_bank,
            type_of_payment,
            challan_serial_no,
            on_account_of,
            tender_date,
            (COALESCE(CAST(income_tax AS FLOAT), 0) +
            COALESCE(CAST(surcharges AS FLOAT), 0) +
            COALESCE(CAST(education_cess AS FLOAT), 0) +
            COALESCE(CAST(penalty_code AS FLOAT), 0) +
            COALESCE(CAST(interest AS FLOAT), 0) +
            COALESCE(CAST(penalty AS FLOAT), 0) +
            COALESCE(CAST(others AS FLOAT), 0) +
            COALESCE(CAST(fee_under_sec_234e AS FLOAT), 0)) AS total_sum"
        )
        ->where('status', 1)
        ->where('tds_challan_id', $this->input['id']); // Assuming $this->input['id'] is set

    $result = $obj->first();

    if ($result == null) {
        // If no result is found, set appropriate message and status
        $this->message = "Challan not found";
        $this->status = "error";
        $this->statusCode = 400;
    } else {
        // If result is found, assign data and success message
        $this->data = $result;
        $this->message = "Challan details fetched successfully";
    }

    }

    public function add($it, $srchrg, $edCess, $penaltyCode, $int, $penalty, $others, $fee)
    {
        return (float)$it + (float)$srchrg + (float)$edCess + (float)$penaltyCode + (float)$int + (float)$penalty + (float)$others + (float)$fee;
    }
}
