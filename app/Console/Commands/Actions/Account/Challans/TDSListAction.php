<?php

namespace App\Console\Commands\Actions\Account\Challans;

use Illuminate\Console\Command;
use App\Console\Commands\Action;

class TDSListAction extends Action
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'datasource:tdsList {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Get TDS List';


    protected $mapper = [
        "id" => "chsone_tds_challans.tds_challan_id"
    ];

    protected $formatterByKeys = ['id'];

    /**
     * Execute the console command.
     */
    public function apply()
    {

        $page = $this->input['page'] ?? 1;
        $per_page = !empty($this->input['per_page']) ? $this->input['per_page'] : ($this->hugeData ? 100000 : 10);
        $offset = ($page - 1) * $per_page;

        $obj =$this->tenantDB()->table('chsone_tds_challans')
        ->selectRaw(
            "tds_challan_id AS id,
            challan_no,
            assessment_year,
            bsr_code,
            tender_date,
            income_tax,
            surcharges,
            education_cess,
            penalty_code,
            interest,
            penalty,
            others,
            fee_under_sec_234e,
            (CAST(income_tax AS FLOAT) + CAST(surcharges AS FLOAT) +
            CAST(education_cess AS FLOAT) + CAST(penalty_code AS FLOAT) +
            CAST(interest AS FLOAT) + CAST(penalty AS FLOAT) +
            CAST(others AS FLOAT) + CAST(fee_under_sec_234e AS FLOAT)) AS total_sum"
        )
        ->where('status', 1)
        ->orderByDesc('tds_challan_id');

        $count = $obj->count();
        $obj = $obj->offset($offset);
        $obj = $obj->limit($per_page);

        $result = $obj->get();

        $this->data =$result;
        $this->meta['pagination']['total'] = $count;
    }

    public function add($it, $srchrg, $edCess, $penaltyCode, $int, $penalty, $others, $fee)
    {
        return (float)$it + (float)$srchrg + (float)$edCess + (float)$penaltyCode + (float)$int + (float)$penalty + (float)$others + (float)$fee;
    }
}
