<?php

namespace App\Console\Commands\Actions\Account;

use Illuminate\Console\Command;
use App\Console\Commands\Action;
use App\Models\Tenants\SocAccountFinancialYearMaster;
use App\Models\Tenants\IncomeInvoiceSetting;
use App\Models\Tenants\ChsoneGrpLedgerTree;
use App\Models\Tenants\ChsoneLedgerTransaction;
use Carbon\Carbon;
use App\Models\Tenants\ChsoneInvestmentsMaster;
use App\Models\Tenants\ChsoneAssetsCategory;
use App\Models\Tenants\ChsoneTaxCategory;
use App\Models\Tenants\ChsoneTaxClass;
use App\Models\Tenants\ChsoneVendorsMaster;
use App\Models\Tenants\TaxExemption;
use App\Models\Tenants\ChsoneBankRecoMaster;


class EditLedgerGetDataSource extends Action
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'datasource:EditLedgerGet {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Ledger Details Data Source';

    /**
     * Execute the console command.
     */


    public function apply()
    {
        $soc_id = $this->input['company_id'];
        $ledger_account_id = $this->input['id'];

        // Fetch the financial year data for the given soc_id, ordered by account_closing_id
        $yearMaster = $this->tenantDB()
            ->table('soc_account_financial_year_master')
            ->where('soc_id', $soc_id)
            ->orderBy('account_closing_id', 'asc')
            ->get();

        // Fetch the main ledger data
        $ledger_data = $this->tenantDB()
            ->table('chsone_grp_ledger_tree')
            ->where('ledger_account_id', $ledger_account_id)
            ->first();

        $ledger_data = json_decode(json_encode($ledger_data), true);

        // Check if 'parent_id' exists before querying for ledger group
        $ledger_group = null;
        if (!empty($ledger_data['parent_id'])) {
            $ledger_group = $this->tenantDB()
                ->table('chsone_grp_ledger_tree')
                ->where('ledger_account_id', $ledger_data['parent_id'])
                ->first();

            $ledger_group = json_decode(json_encode($ledger_group), true);
        }

        $arrYear = [];
        foreach ($yearMaster as $key => $eachYear) {
            $arrYear[$key]['fy_start_date'] = Carbon::parse($eachYear->fy_start_date)->format('Y-m-d');
            $arrYear[$key]['fy_end_date'] = Carbon::parse($eachYear->fy_end_date)->format('Y-m-d');

            // Fetch the first matching transaction within the financial year date range
            $objGetFirstTransaction = $this->tenantDB()
                ->table('chsone_ledger_transactions')
                ->whereIn('is_opening_balance', [1, 2, 3])
                ->where('ledger_account_id', $ledger_account_id)
                ->where('soc_id', $soc_id)
                ->whereBetween('transaction_date', [$eachYear->fy_start_date, $eachYear->fy_end_date])
                ->orderBy('transaction_date', 'asc')
                ->first();

            $arrYear[$key]['transaction_amount'] = $objGetFirstTransaction
                ? number_format($objGetFirstTransaction->transaction_amount, 2)
                : 'Not Present';
        }

        $result = [
            'ledger_name' => $ledger_data['ledger_account_name'],
            'ledger_group' => $ledger_group['ledger_account_name'] ?? $ledger_data['behaviour'],
            'nature_of_account' => $ledger_data['nature_of_account'],
            'year_data' => $arrYear,
        ];
        $this->data = $result;
    }


}
