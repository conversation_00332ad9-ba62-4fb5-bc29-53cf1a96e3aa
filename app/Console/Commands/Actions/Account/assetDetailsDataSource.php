<?php

namespace App\Console\Commands\Actions\Account;

use Illuminate\Console\Command;
use App\Console\Commands\Action;

class assetDetailsDataSource extends Action
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'datasource:assetDetails {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Fetch asset details.';

    /**
     * Execute the console command.
     */
    public function apply()
    {
        $assets_id = $this->input['assets_id'];

        $obj = $this->tenantDB()->table('chsone_assets as assets')
        ->select('assets_id as id','assets_name','assets_tag_number','assets_vendor_id','vendor_name','assets.assets_categories_id','assets_categories_name','assets_location','vendor_bill_type_purchase','purchase_ledger_id','assets_purchase_date','assets_cost','assets.status')
        ->leftJoin('chsone_vendors_master as vendor', 'vendor.vendor_id', '=', 'assets.assets_vendor_id')
        ->leftJoin('chsone_assets_categories as categories', 'categories.assets_categories_id', '=', 'assets.assets_categories_id')
        ->where('assets_id', $assets_id);
        $result = $obj->first();
        $this->data = $result;
    }
}
