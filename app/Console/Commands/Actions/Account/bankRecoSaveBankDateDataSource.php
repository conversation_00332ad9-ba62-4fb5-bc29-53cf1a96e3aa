<?php

namespace App\Console\Commands\Actions\Account;

use Illuminate\Console\Command;
use App\Console\Commands\Action;
use App\Models\Tenants\ChsoneLedgerTransaction;

class bankRecoSaveBankDateDataSource extends Action
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'datasource:bankRecoSaveBankDate {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Bank Reconciliation Save Bank Date Data Source';

    /**
     * Execute the console command.
     */
    public function apply()
    {
        $soc_id = $this->input['company_id'];
        $requestData = $this->input;

        $txn_id = $requestData['txn_id'] ?? null;
        $bank_date = $requestData['bank_date'] ?? null;

        $getReference = ChsoneLedgerTransaction::where('soc_id', $soc_id)
            ->where('txn_id', $txn_id)
            ->first();

        if ($getReference) {
            $getReference->value_date = !empty($bank_date) ? $bank_date : null;
            $updateRef = $getReference->save();

            if ($updateRef) {
                $this->message = "Bank date updated successfully.";
                $this->status = "success";
                $this->statusCode = 200;
            } else {
                $this->message = "Failed to update bank date.";
                $this->status = "error";
                $this->statusCode = 400;
            }
        } else {
            $this->message = "No transaction found with the given txn_id.";
            $this->status = "error";
            $this->statusCode = 400;
        }
    }
}
