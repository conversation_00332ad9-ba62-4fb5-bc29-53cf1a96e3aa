<?php

namespace App\Console\Commands\Actions\Account;

use App\Console\Commands\Action;
use Illuminate\Support\Facades\Config;

class closeAccountsDataSource extends Action
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */

    protected $constants;

    public function __construct()
    {
        parent::__construct();
        $this->constants = Config::get("constants");
    }
    protected $signature = 'datasource:closeAccounts {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Get Investments List Data Source';

    protected $formatter = [
        'id' => '',
        'account_number' => '',
        'type' => '',
        'bank_name' => '',
        'branch' => '',
        'bank_address' => '',
        'bank_city' => '',
        'bank_ifsc' => '',
        'start_date' => '',
        'maturity_date' => '',
        'status' => '',
        'ledger_account_id' => '',
    ];

    protected $formatterByKeys = ['id'];

    protected $mapper = [
        'id' => 'id',
    ];

    /**
     * Execute the console command.
     */
  
    public function apply()
    {
        $soc_id = $this->input["company_id"] ?? null;
        $account_closing_id = (int) $this->input["account_closing_id"] ?? null;
        $confirmflag = $this->input["confirm_flag"] ?? 0; //previously we accept 1 or 0 but for now we accept 0 only confirmed with mahesh chauresiya

            // Cast soc_id to integer to ensure correct type
        $soc_id = (int)$soc_id;

        // Get financial year details
        $financial_year_details = $this->getFyDetails([
            'soc_id' => $soc_id,
            'accountclosingid' => $account_closing_id,
            'process' => 'fetch',
        ]);

        if (empty($financial_year_details)) {
            $this->message = 'Unable to find financial year.';
            $this->status = 'error';
            $this->statusCode = 400;
            return;
        }

        $financial_year_details = json_decode(json_encode($financial_year_details), true);

        // Ensure bank reconciliation is complete before closing account
        if ($confirmflag == 1) {
            $arrReconciledFY = [
                'soc_id' => $soc_id,
                'financial_year' => [
                    $financial_year_details[0]['account_closing_id'] => date('Y', strtotime($financial_year_details[0]['fy_end_date'])),
                ],
                'arrBankLedger' => $this->getLedgerByType([
                    'soc_id' => $soc_id,
                    'context' => 'bank',
                    'entity_type' => $this->constants['ENTITY_TYPE_LEDGER'] ?? 'ledger',
                ]),
            ];

            $arrReconciledYear = $this->getReconciledFinancialYear($arrReconciledFY);

            if (empty($arrReconciledYear)) {
                $this->message = 'Please complete bank reconciliation before closing the account.';
                $this->status = 'error';
                $this->statusCode = 400;
                return;
            }
        }

        // Add Profit and Loss Ledger Opening Transaction
        $next_year_start_date = date('Y-m-d', strtotime('+1 year', strtotime($financial_year_details[0]['fy_start_date'])));
        $this->_addProfitLossLedgerOpeningTxn($soc_id, $next_year_start_date);

        // Execute stored procedure
        $closed_by = $this->input['user_id'] ?? 0;

        // Format dates correctly
        $fy_start_date = date('Y-m-d', strtotime($financial_year_details[0]['fy_start_date']));
        $fy_end_date = date('Y-m-d', strtotime($financial_year_details[0]['fy_end_date']));

        try {
            // Log the parameters for debugging
            // \Log::info('Calling stored procedure accountclosure with parameters:', [
            //     'soc_id' => $soc_id,
            //     'fy_start_date' => $fy_start_date,
            //     'fy_end_date' => $fy_end_date,
            //     'closed_by' => $closed_by,
            //     'confirmflag' => $confirmflag,
            // ]);

            // dd($soc_id, $fy_start_date, $fy_end_date, $closed_by, $confirmflag);
            $this->tenantDB()->statement('CALL accountclosure(?, ?, ?, ?, ?)', [
                $soc_id,
                $fy_start_date,
                $fy_end_date,
                $closed_by,
                $confirmflag,
            ]);

        } catch (\Exception $e) {
            $this->message = 'An error occurred while closing the account: ' . $e->getMessage();
            $this->status = 'error';
            $this->statusCode = 500;
            return;
        }

        // Add notification
        $start_year = date('Y', strtotime($financial_year_details[0]['fy_start_date']));
        $end_year = date('Y', strtotime($financial_year_details[0]['fy_end_date']));
        $notification_data = [
            'title' => 'Books of account closed',
            'desc' => "Books of account for financial year $start_year-$end_year have been closed.",
            'module' => 'accounts',
            'scope' => 'group',
            'role' => 'admin',
            'pk_id' => $financial_year_details[0]['account_closing_id'],
            'date_time' => date("d-M-Y H:i"),
            'to_be_notified' => 1,
            'soc_id' => $soc_id,
            'all_staff' => [],
        ];

        // Send notification
        // \ChsOne\Helper\CommonHelper::addNotification($notification_data);

        // Update FY date range for the society
        $arrFYDetail = $this->tenantDB()->table('soc_account_financial_year_master')
            ->selectRaw('MIN(fy_start_date) as min_fy_start_date, MAX(fy_end_date) as max_fy_end_date')
            ->where('soc_id', $soc_id)
            ->where('confirmed', 0)
            ->first();

        if (!empty($arrFYDetail)) {
            // Update session or other necessary variables with $arrFYDetail
            // For example:
            // $this->session->set('socFYDateRange', [
            //     'start_date' => $this->getDisplayDate($arrFYDetail->min_fy_start_date),
            //     'end_date' => $this->getDisplayDate($arrFYDetail->max_fy_end_date),
            //     'db_start_date' => $arrFYDetail->min_fy_start_date,
            //     'db_end_date' => $arrFYDetail->max_fy_end_date,
            // ]);
        }

        $this->message = $confirmflag == 1
            ? 'Financial year account has been successfully closed.'
            : 'Financial year trial account has been successfully closed.';
        $this->status = 'success';
        $this->statusCode = 200;
    }


    public function getFyDetails($data = array())
    {
        $accountclosingid = $data["accountclosingid"];

        $closingyeardetails = $this->tenantDB()->table('soc_account_financial_year_master')
            ->where('account_closing_id', $accountclosingid)
            ->where('soc_id', $data['soc_id'])
            ->get()
            ->toArray();

        return $closingyeardetails;
    }

    public function getLedgerByType($data = array())
    {
        $soc_id = $data['soc_id'];
        $context = $data['context'];
        $entity_type = $data['entity_type'];

        $bankledger = $this->tenantDB()->table('chsone_grp_ledger_tree')->where('soc_id', $soc_id)
            ->where('context', $context)
            ->where('entity_type', $entity_type)
            ->get();

        if (!$bankledger->isEmpty()) {
            return $bankledger->toArray();
        } else {
            return false;
        }
    }

    public function getReconciledFinancialYear($data = array())
    {
        $arrReconciledFYDetail = array();

        if (!empty($data['financial_year'])) {
            $financialYears = array_values($data['financial_year']);
            // $year = $financialYears[0];

            $arrFYDetail = $this->tenantDB()->table('chsone_bank_reco_master')
                ->select('year', 'ledger_account_id', 'month')
                ->where('soc_id', $data['soc_id'])
            // ->where('month', 3)
                ->where('is_reconcile_confirmed', 1)
                ->whereIn('year', $financialYears)
                ->groupBy('year', 'ledger_account_id')
                ->get();

            dd($arrFYDetail);
            if (!$arrFYDetail->isEmpty()) {
                $arrFYDetail = $arrFYDetail->toArray();

                foreach ($data['financial_year'] as $eachYear) {
                    $arrReconciledFYDetail[$eachYear] = count($data['arrBankLedger']) + 1;
                    foreach ($data['arrBankLedger'] as $eachLedger) {
                        foreach ($arrFYDetail as $eachFYDetail) {

                            $eachFYDetail = json_decode(json_encode($eachFYDetail), true);
                            $eachLedger = json_decode(json_encode($eachLedger), true);

                            if ($eachFYDetail['year'] == $eachYear && $eachFYDetail['ledger_account_id'] == $eachLedger['ledger_account_id']) {
                                $arrReconciledFYDetail[$eachYear]--;
                            }

                            if ($arrReconciledFYDetail[$eachYear] == 1) {
                                $month = $eachFYDetail->month;
                                $month = ($month < 10) ? '0' . $month : $month;
                                $arrReconciledFYDetail[$eachFYDetail->year . '-' . $month . '-31'] = 1;
                            }

                        }
                    }
                    // unset($arrReconciledFYDetail[$eachYear]);
                }
            }
        }


        return $arrReconciledFYDetail;
    }

    public function checkpreviousyear($data = array())
    {
        $soc_id = $data["soc_id"];
        $fy_start_date = $data["fy_start_date"];

        $closedyeardetails = $this->tenantDB()->table('account_financial_master')
            ->where('fy_end_date', '<', $fy_start_date)
            ->where('soc_id', $soc_id)
            ->get()
            ->toArray();

        return $closedyeardetails;
    }
    public function getledgerss($data = array())
    {
        $soc_id = $data["soc_id"];

        $legerdetails = $this->tenantDB()->table('chsone_grp_ledger_tree')->where('entity_type', 'ledger')
            ->where('soc_id', $soc_id)
            ->get()
            ->toArray();

        return $legerdetails;
    }

    public function _addProfitLossLedgerOpeningTxn($soc_id, $start_date)
    {
        // Fetch the Profit and Loss ledger
        $pnlledger = $this->tenantDB()->table('chsone_grp_ledger_tree')
            ->where('soc_id', $soc_id)
            ->where('entity_type', 'special')
            ->get()
            ->toArray();

        if (empty($pnlledger)) {
            // Create a new Profit and Loss ledger
            $objgrpLedgTreeData = [
                'soc_id' => $soc_id,
                'ledger_account_name' => '##ProfitLoss',
                'nature_of_account' => 'dr',
                'parent_id' => 0,
                'report_head' => '',
                'context_ref_id' => '0',
                'context' => '',
                'added_on' => date("Y-m-d H:i:s"),
                'status' => 1,
                'created_by' => 0,
                'entity_type' => 'special',
                'behaviour' => '',
                'defined_by' => 'system',
            ];

            // Insert the new ledger and get its ID
            $newLedgerId = $this->tenantDB()->table('chsone_grp_ledger_tree')->insertGetId($objgrpLedgTreeData);

            // Update the parent_id to the new ledger ID
            $this->tenantDB()->table('chsone_grp_ledger_tree')
                ->where('ledger_account_id', $newLedgerId)
                ->update(['parent_id' => $newLedgerId]);

            // Fetch the updated Profit and Loss ledger
            $pnlledger = $this->tenantDB()->table('chsone_grp_ledger_tree')
                ->where('soc_id', $soc_id)
                ->where('entity_type', 'special')
                ->get()
                ->toArray();
        }

        if (!empty($pnlledger)) {
            $income_total = 0;
            $expense_total = 0;

            $pnlledger = json_decode(json_encode($pnlledger), true);

            // Fetch income ledgers (assuming parent_id = 3)
            $income_ledgers = $this->tenantDB()->table('chsone_grp_ledger_tree')
                ->where('soc_id', $soc_id)
                ->where('parent_id', 3)
                ->where('entity_type', 'ledger')
                ->get()
                ->toArray();

            // Fetch expense ledgers (assuming parent_id = 4)
            $expense_ledgers = $this->tenantDB()->table('chsone_grp_ledger_tree')
                ->where('soc_id', $soc_id)
                ->where('parent_id', 4)
                ->where('entity_type', 'ledger')
                ->get()
                ->toArray();

            // Calculate total income
            foreach ($income_ledgers as $income_ledger) {

                $income_ledger = json_decode(json_encode($income_ledger), true);
                $ledgerDrSum = $this->tenantDB()->table('chsone_ledger_transactions')
                    ->where('soc_id', $soc_id)
                    ->where('ledger_account_id', $income_ledger['ledger_account_id'])
                    ->where('transaction_type', 'dr')
                    ->where('transaction_date', '<', $start_date)
                    ->sum('transaction_amount');

                $ledgerCrSum = $this->tenantDB()->table('chsone_ledger_transactions')
                    ->where('soc_id', $soc_id)
                    ->where('ledger_account_id', $income_ledger['ledger_account_id'])
                    ->where('transaction_type', 'cr')
                    ->where('transaction_date', '<', $start_date)
                    ->sum('transaction_amount');

                $income_total += $ledgerCrSum - $ledgerDrSum;
            }

            // Calculate total expenses
            foreach ($expense_ledgers as $expense_ledger) {
                $ledgerDrSum = $this->tenantDB()->table('chsone_ledger_transactions')
                    ->where('soc_id', $soc_id)
                    ->where('ledger_account_id', $expense_ledger['ledger_account_id'])
                    ->where('transaction_type', 'dr')
                    ->where('transaction_date', '<', $start_date)
                    ->sum('transaction_amount');

                $ledgerCrSum = $this->tenantDB()->table('chsone_ledger_transactions')
                    ->where('soc_id', $soc_id)
                    ->where('ledger_account_id', $expense_ledger['ledger_account_id'])
                    ->where('transaction_type', 'cr')
                    ->where('transaction_date', '<', $start_date)
                    ->sum('transaction_amount');

                $expense_total += $ledgerDrSum - $ledgerCrSum;
            }

            // Calculate opening balance for Profit and Loss
            $pnlopening = $income_total - $expense_total;

            $date = \DateTime::createFromFormat('Y-m-d H:i:s', $start_date . " 00:00:00");

            // Check if a transaction already exists for the given date
            $txn = $this->tenantDB()->table('chsone_ledger_transactions')
                ->where('added_on', $date->format('Y-m-d H:i:s'))
                ->first();

            $txnData = [
                'soc_id' => $soc_id,
                'transaction_date' => null,
                'ledger_account_id' => $pnlledger[0]['ledger_account_id'],
                'ledger_account_name' => '##Profit Loss',
                'voucher_type' => '',
                'transaction_type' => 'cr',
                'payment_mode' => '',
                'payment_reference' => '',
                'transaction_amount' => $pnlopening,
                'txn_from_id' => 0,
                'memo_desc' => 'entry for opening Balance',
                'is_opening_balance' => 1,
                'is_reconciled' => 0,
                'created_by' => 0,
                'added_on' => $date->format('Y-m-d H:i:s'),
            ];

            if (empty($txn)) {
                // Insert a new transaction
                $this->tenantDB()->table('chsone_ledger_transactions')->insert($txnData);
            } else {
                // Update the existing transaction
                $this->tenantDB()->table('chsone_ledger_transactions')
                    ->where('added_on', $date->format('Y-m-d H:i:s'))
                    ->update($txnData);
            }
        }
    }

    public function getChildLedgers($soc_id, $parent_id)
    {
        $childLedgers = $this->tenantDB()->table('chsone_grp_ledger_tree')
            ->where('soc_id', $soc_id)
            ->where('parent_id', $parent_id)
            ->where('entity_type', 'ledger')
            ->get()
            ->toArray();

        $allLedgers = $childLedgers;

        foreach ($childLedgers as $ledger) {
            $subChildLedgers = $this->getChildLedgers($soc_id, $ledger['ledger_account_id']);
            $allLedgers = array_merge($allLedgers, $subChildLedgers);
        }

        return $allLedgers;
    }
}
