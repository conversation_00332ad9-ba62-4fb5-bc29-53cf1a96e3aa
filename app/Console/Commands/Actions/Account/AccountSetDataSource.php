<?php

namespace App\Console\Commands\Actions\Account;

use Illuminate\Console\Command;
use App\Console\Commands\Action;

class AccountSetDataSource extends Action
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'datasource:AccountSet {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Get Ledger Group Tree Data Source';

    protected $formatter = [
        'account_closing_id' => '',
        'fy_start_date' => '',
        'fy_end_date' => '',
        'closed' => '',
        'title' => '',
        'financial_year' => 'add:fy_start_date,fy_end_date',
        'id' => '',
        'year_financial' => ''
    ];

    protected $formatterKey = 'id';

    protected $mapper = [
        'id' => 'soc_account_financial_year_master.account_closing_id',
    ];

    /**
     * Execute the console command.
     */
    public function apply()
    {
        $page = $this->input['page'] ?? 1;
        $per_page = !empty($this->input['per_page']) ? $this->input['per_page']: ($this->hugeData ? 100000 : 10);
        $offset = ($page - 1) * $per_page;

        $totalCount = $this->TenantDB()->table('soc_account_financial_year_master as financial_year')
            ->count();

        $result = $this->tenantDB()->table('soc_account_financial_year_master')
            ->select('account_closing_id', 'fy_start_date', 'fy_end_date', 'closed')
            ->orderBy('account_closing_id', 'asc')
            ->offset($offset)
            ->limit($per_page)
            ->get();

        // check if closed is 1 then set title is Re-calculate Next FY Opening Balance and is 0 then set title is Trial A/c Closure & Create Next FY and pass to the result
        foreach ($result as $key => $value) {
            $startYear = date('Y', strtotime($value->fy_start_date));
            $endYear = date('Y', strtotime($value->fy_end_date));

            // Format financial year as "YYYY-YYYY"
            $yearFinancial = "{$startYear}-{$endYear}";

            // Add the year_financial and title fields to each item
            $result[$key]->id = $yearFinancial;
            $result[$key]->year_financial = $yearFinancial;
            $result[$key]->title = $value->closed == 1
                ? 'Re-calculate Next FY Opening Balance'
                : 'Trial A/c Closure & Create Next FY';
        }

        $this->data = $this->format($result->toArray());
        $this->meta['pagination']['total'] = $totalCount;
    }

    public function add($a, $b)
    {
        return $a . ' - ' . $b;
    }
}
