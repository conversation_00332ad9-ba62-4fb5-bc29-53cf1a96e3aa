<?php

namespace App\Console\Commands\Actions\Validators;

use Illuminate\Console\Command;
use App\Console\Commands\Action;

class ValidateAction extends Action
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'action:validate {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Validate action';

    /**
     * Execute the console command.
     */
    public function apply()
    {
        $rules = $this->input['rules'];
        $rulesMessage = $this->input['rulesMessage'];
        $data = $this->input['data'];
        $validator = \Validator::make($data, $rules, $rulesMessage);
        if ($validator->fails()) {
            // dd($validator->errors()->toArray());
            $this->status = 'error';
            $this->message = 'Validation failed';
            $this->statusCode = 400;
            $this->meta['errors'] = $validator->errors()->toArray();
            //$this->log($this->flowId, $this->actionId, $this->getNode(), $this->getConnection());
            $error = \Illuminate\Validation\ValidationException::withMessages([
                'errors' => $this->message,
            ]);
            //$error->status = $this->statusCode;
            throw $error;
        } else {
            $this->message = 'Validation Success';
            $this->statusCode = 200;
            //$this->log($this->flowId, $this->actionId, $this->getNode(), $this->getConnection());
        }

    }
}
