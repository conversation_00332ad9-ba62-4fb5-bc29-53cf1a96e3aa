<?php

namespace App\Console\Commands\Actions\Users;

use Illuminate\Console\Command;
use App\Console\Commands\Action;
use GuzzleHttp\Client;
use App\Models\Masters\ChsoneSocietiesUser;

class GetSocietyAccess extends Action
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'action:get-society-access {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Get the access to a society';

    /**
     * Execute the console command.
     */
    public function apply()
    {
        $uuid = $this->input['uuid'];
        $url = env('GATEWAY_URL').'/saas/user/society/access/'.$uuid ;
        $client = new Client();
        $response = $client->get($url);
        $response = json_decode($response->getBody(), true);

        $response['data']['uuid'] = $uuid;
        $roles = [];
        if(!empty($response['data']['user_id'])) {
            $societyUserRoles = ChsoneSocietiesUser::where(['user_id' => $response['data']['user_id'], 'user_status' => '1'])->get();
            if(!empty($societyUserRoles)) {
                foreach($societyUserRoles->toArray() as $ele) {
                    $roles[$ele['soc_id']] = $ele['user_role'];
                }
            }
        }

        if(!empty($response['data']['companies'])) {
            foreach($response['data']['companies'] as $key => $value) {
                foreach($response['data']['companies'][$key] as $iKey => $iValue) {

                    foreach($iValue['apps'] as $jkey => $app){
                        if($app['app_id'] == 2){
                            if(!empty($roles[$iValue['company_id']])) {
                                // $response['data']['companies'][$key][$iKey]['apps'][$jkey]['is_admin'] = (str_contains(strtolower($roles[$iValue['company_id']]), 'admin')) ? TRUE : FALSE; 
                                $user_roles = explode(",",$roles[$iValue['company_id']]);
                                $response['data']['companies'][$key][$iKey]['apps'][$jkey]['roles']  = array_unique($user_roles);
                            } else {
                                // $response['data']['companies'][$key][$iKey]['apps'][$jkey]['is_admin'] = FALSE;
                                $response['data']['companies'][$key][$iKey]['apps'][$jkey]['roles'] = [];
                            }
                        }
                        else{
                            $response['data']['companies'][$key][$iKey]['apps'][$jkey]['roles'] = [];
                        }
                    }

                    // if(!empty($roles[$iValue['company_id']])) {
                    //     $response['data']['companies'][$key][$iKey]['is_admin'] = (str_contains(strtolower($roles[$iValue['company_id']]), 'admin')) ? TRUE : FALSE; 
                    //     $response['data']['companies'][$key][$iKey]['roles']  = $roles[$iValue['company_id']];
                    // } else {
                    //     $response['data']['companies'][$key][$iKey]['is_admin'] = FALSE;
                    //     $response['data']['companies'][$key][$iKey]['roles'] = '';
                    // }
                }
            }
        }

        $this->status = $response['status'];
        $this->message = $response['message'];
        $this->data = $response['data'];

    }
}
