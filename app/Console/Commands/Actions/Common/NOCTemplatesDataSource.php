<?php

namespace App\Console\Commands\Actions\Common;

use App\Console\Commands\Action;
use Illuminate\Console\Command;

class NOCTemplatesDataSource extends Action
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'datasource:NOCTemplate {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Get Data of NOC Templates';

    protected $formatter = [
        'id' => '',
        'purpose' => '',
        'description' => '',
    ];

    protected $formatterByKeys = ['id'];

    /**
     * Execute the console command.
     */
    public function apply()
    {
        try{
            $obj = $this->tenantDB()->table('chsone_email_template')
            ->select('id', 'purpose')
            ->selectRaw("REPLACE(body, '\\n', '') as description");
            // ->select('noc_request_id as id', 'purpose', 'preview as description');
            $obj = $obj->where('type', 'noc');
            $obj = $obj->orderBy('id', 'asc');
            $result = $obj->get();
            $this->data = $this->format($result->toArray());
        }catch(\Exception $e) {
            dd($e);
        }
    }
}
