<?php

namespace App\Console\Commands\Actions\Common;
use App\Console\Commands\Action;
use App\Http\Traits\MongoTraits;

class MongoNotificationLogDataSource extends Action
{

    use MongoTraits; // Use the MongoTraits trait in this class

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'datasource:mongoNotificationLog {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Mongo Notification Log Data Source';

    /**
     * Execute the console command.
     */
    public function apply()
    {

        $ip = $this->input['additional_data']['ip'];
        $document = [
            'title' => $this->input['title'],
            'desc' => $this->input['desc'],
            'scope' => $this->input['scope'],
            'module' => $this->input['module'],
            'company_id' => $this->input['company_id'],
            'user_ids' => $this->input['user_id'],
            'created_by' => $this->input['user_id'],
            'username' => $this->input['user']['user_info']['first_name'] . ' ' . $this->input['user']['user_info']['last_name'],
            'ip_address' => $ip,
            'role' => $this->input['role'] ?? '',
            'approve_link' => $this->input['approve_link'] ?? '',
            'action_url' => $this->input['action_url'] ?? '',
            'deny_link' => $this->input['deny_link'] ?? '',
            'id' => $this->input['id'] ?? '',
            'notified_status' => $this->input['notified_status'] ?? 0,
            'to_be_notified' => $this->input['to_be_notified'] ?? 0,
            'member_id' => $this->input['member_id'] ?? '',
            'all_admin' => $this->input['all_admin'] ?? '',
            'notification_id' => $this->input['notification_id'] ?? ''
        ];

        $this->data = $this->addNotification($document);
    }
}
