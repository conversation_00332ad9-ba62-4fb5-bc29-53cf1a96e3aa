<?php

namespace App\Console\Commands\Actions\Common;

use App\Console\Commands\Action;
use Illuminate\Console\Command;

class UnitTypesDataSource extends Action
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'datasource:UnitTypes {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Get Data of All Unit Types';

    protected $formatter = [
        'flat' => '',
        'duplex' => '',
        'bedsit' => '',
        'pent_house' => '',
        'swimming_pool' => '',
        'hall' => '',
        'garden' => '',
        'clubhouse' => '',
        'shop' => '',
        'office' => '',
        'Parking' => '',
        'gala' => ''
    ];

    /**
     * Execute the console command.
     */
    public function apply()
    {
        $constants = array(
            'data' => array(
                "flat" => "Flat", "duplex" => "Duplex", "bedsit" => "Bedsit", "pent_house" => "Pent House",
                "swimming_pool" => "Swimming pool", "hall" => "Hall", "garden" => "Garden", "clubhouse" => "Clubhouse",
                "shop" => "Shop", "office" => "Office", "Parking" => "Parking", "gala" => "Gala"
            ),
        );

        $result = collect($constants);
        $this->data = $this->format($result->toArray());
    }
}
