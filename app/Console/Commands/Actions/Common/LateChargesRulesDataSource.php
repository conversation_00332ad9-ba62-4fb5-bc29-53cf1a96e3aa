<?php

namespace App\Console\Commands\Actions\Common;

use App\Console\Commands\Action;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class LateChargesRulesDataSource extends Action
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'datasource:LateChargesRules {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Get Data of Late Charges Rules';

    protected $formatter = [
        'id' => '',
        'ledger_account_id' => '',
        'account_number' => '',
        'account_name' => '',
        'bank_ifsc' => '',
        'bank_address' => '',
        'bank_city' => '',
        'ledger_name' => ''
    ];

    protected $formatterByKeys = ['id'];

    /**
     * Execute the console command.
     */
    public function apply()
    {
        $obj = $this->tenantDB()->table('chsone_accounts_master')
        ->select('account_id as id', 'ledger_account_id', 'account_number', 'account_name', 'bank_ifsc', 'bank_address', 'bank_city', DB::raw("CONCAT(bank_name, ' - ',account_number) AS ledger_name"));
        $obj = $obj->orderBy('id', 'asc');
        $result = $obj->get();
        $this->data = $this->format($result->toArray());
    }
}
