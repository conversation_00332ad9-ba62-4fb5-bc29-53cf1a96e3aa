<?php

namespace App\Console\Commands\Actions\Common;

use App\Console\Commands\Action;
use App\Http\Traits\MongoTraits;

class UnreadNotificationDataSource extends Action
{

    use MongoTraits; // Use the MongoTraits trait in this class

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'datasource:unreadNotification {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Un Read Notification Log Data Source';

    /**
     * Execute the console command.
     */
    public function apply()
    {

        $count= $this->getUnreadNotificationCount($this->input['company_id'], $this->input['user_id']);
        $this->data['count']=$count;
    }
}
