<?php

namespace App\Console\Commands\Actions\Common;

use Illuminate\Console\Command;
use App\Console\Commands\Action;
use Illuminate\Support\Facades\DB;

class GetInvoiceParticularsDataSource extends Action
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'datasource:GetInvoiceParticularsDataSource {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Get list of member/non-member invoice particulars';


    protected $formatterByKeys = ['id'];

    /**
     * Execute the console command.
     */
    public function apply()
    {
        // $keyword = $this->input['keyword'];
        $invoice_id = $this->input['invoice_id'];

        if($this->input['account_type'] == 'member') {

            $bill_type = $this->input['bill_type'] ?? 'maintenance';
            $result = $this->getMemberInvoiceParticulars($invoice_id, $bill_type);

        } elseif($this->input['account_type'] == 'nonmember') {
            $result = $this->getNonMemberInvoiceParticulars($invoice_id);
        }

        $this->data = $result;
    }

    private function getMemberInvoiceParticulars($invoice_id, $bill_type) {
        if(strtolower($bill_type) == 'maintenance') {
            $obj = $this->tenantDB()->table('income_invoice_particular as particulars')
            ->select('particulars.id', 'particulars.particular', 'particulars.amount','income_accounts.fk_income_ledger_id AS et_ledger_id')
            ->leftJoin('income_unit_invoices as unit_invoices', 'unit_invoices.unit_invoice_id', '=', 'particulars.fk_unit_invoice_id')
            ->leftJoin('income_accounts', 'income_accounts.account_name', '=', 'particulars.particular'); // Corrected field name

            // if (!empty($keyword)) {
            //     $newKeyword = explode('(', $keyword);
            //     $obj->where(DB::raw("particulars.invoice_number"), '=', trim($newKeyword[0]));
            // }

            if (!empty($invoice_id)) {
                $obj->where(DB::raw("particulars.fk_unit_invoice_id"), '=', $invoice_id);
            }

            $result = $obj->get();

        } elseif(strtolower($bill_type) == 'incidental') {
            $obj = $this->tenantDB()->table('income_common_billing_charges as incidental_invoice')
            ->select('incidental_invoice.id', 'particular', 'amount','incidental_particular.ledger_id AS et_ledger_id')
            ->leftJoin('income_common_area_charges as incidental_particular', 'incidental_invoice.billing_type', '=', 'incidental_particular.id')
            ;
            // if (!empty($keyword)) {
            //     $newKeyword = explode('(', $keyword);
            //     $obj->where(DB::raw("incidental_invoice.invoice_number"), '=', trim($newKeyword[0]));
            // }

            if (!empty($invoice_id)) {
                $obj->where(DB::raw("incidental_invoice.id"), '=', $invoice_id);
            }

            $result = $obj->get();

        }

        return $result;
    }

    private function getNonMemberInvoiceParticulars($invoice_id) {
        $obj = $this->tenantDB()->table('income_nonmember_bills')
        ->select('nonmember_bill_id as id', 'bill_for as particular', 'bill_amount as amount','income_accounts.fk_income_ledger_id AS et_ledger_id')
        ->leftJoin('income_accounts', 'income_accounts.account_id', '=', 'income_nonmember_bills.fk_income_account_id'); // Corrected field name

        // if (!empty($keyword)) {
        //     $newKeyword = explode('(', $keyword);
        //     $obj->where(DB::raw("bill_number"), '=', trim($newKeyword[0]));
        // }

        if (!empty($invoice_id)) {
            $obj->where(DB::raw("nonmember_bill_id"), '=', $invoice_id);
        }

        $result = $obj->get();

        return $result;
    }
}
