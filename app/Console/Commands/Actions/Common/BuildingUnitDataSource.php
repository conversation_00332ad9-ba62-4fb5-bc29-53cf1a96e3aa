<?php

namespace App\Console\Commands\Actions\Common;

use App\Console\Commands\Action;
use Illuminate\Console\Command;

class BuildingUnitDataSource extends Action
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'datasource:BuildingUnitData {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Get Data of Building, Floor, Unit';

    protected $formatter = [
        'id' => '',
        'fk_unit_category_id' => '',
        'unit_category' => '',
    ];

    protected $formatterByKeys = ['id'];

    /**
     * Execute the console command.
     */
    public function apply()
    {
        $obj = $this->tenantDB()->table('chsone_units_master')
        ->select('unit_id as id', 'fk_unit_category_id', 'unit_category');
        $obj = $obj->where('soc_id', $this->input['company_id']);
        $obj = $obj->orderBy('unit_id', 'asc');
        $result = $obj->get();
        $this->data = $this->format($result->toArray());
    }
}
