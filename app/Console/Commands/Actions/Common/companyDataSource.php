<?php

namespace App\Console\Commands\Actions\Common;

use Illuminate\Console\Command;
use App\Console\Commands\Action;

class companyDataSource extends Action
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'datasource:company {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Company Data Source';

    protected $formatter = [
        'id' => '',
        'soc_type_id' => '',
        'soc_name' => '',
        'soc_reg_num' => '',
        'soc_address_1' => '',
        'soc_address_2' => '',
        'soc_landmark' => '',
        'soc_city_or_town' => '',
        'soc_state' => '',
        'soc_pincode' => '',
        'status' => '',
        'completed' => '',
        'soc_gst_number' => '',
        'soc_office_email' => '',
        'soc_office_mobile' => ''
    ];

    protected $formatterByKeys = ['id'];

    /**
     * Execute the console command.
     */
    public function apply()
    {
        $companyId = $this->input['company_id'];
        $obj = $this->masterDB()->table('chsone_societies_master')
            ->select(
                'soc_id as id',
                'soc_type_id',
                'soc_name',
                'soc_reg_num',
                'soc_address_1',
                'soc_address_2',
                'soc_landmark',
                'soc_city_or_town',
                'soc_state',
                'soc_pincode',
                'status',
                'completed',
                'soc_gst_number',
                'soc_office_email',
                'soc_office_mobile'
            )
            ->where('soc_id', $companyId)
            ->where('status', 1);

        $result = $obj->get();

        $this->data = $this->format($result->toArray());
    }
}
