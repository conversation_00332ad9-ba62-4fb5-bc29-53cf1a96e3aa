<?php

namespace App\Console\Commands\Actions\Common;

use App\Console\Commands\Action;
use Illuminate\Console\Command;
use App\Models\Tenants\ChsoneTaxCategory as TaxCategory;
use App\Models\Tenants\ChsoneTaxClass as TaxClass;
use App\Models\Tenants\IncomeCommonAreaCharge as IncomeCommonBilling;
use Illuminate\Support\Facades\DB;

class IncidentBillParticularDataSource extends Action
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'datasource:IncidentBillParticular {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Get the all the particulars of the incident bill';

    /**
     * Execute the console command.
     */
    public function apply()
    {
        $soc_id = $this->input['company_id'];

        $records = [];
        $value = '';

        // Step 1: Get the currently applicable records
        $currentlyApplicableRecords = $this->tenantDB()->table('income_common_area_charges')
        ->selectRaw('MAX(effective_date) as effective_date, particular')
        ->where('soc_id', $soc_id)
        ->groupBy('particular')
        ->orderBy('id', 'asc')
        ->get();

        $selectedRecords = [];

        // Step 2: Prepare conditions for the final query
        if ($currentlyApplicableRecords->count() > 0) {
            foreach ($currentlyApplicableRecords as $record) {
                $selectedRecords[] = [
                    'effective_date' => $record->effective_date,
                    'particular' => $record->particular
                ];
            }

            // Prepare subquery for tax data
            $taxSubquery = $this->tenantDB()->table('chsone_tax_categories')
            ->selectRaw('fk_tax_class_id, SUM(tax_categories_amount) as tax')
            ->groupBy('fk_tax_class_id');

            // Step 3: Fetch matching records
            $records = $this->tenantDB()->table('income_common_area_charges')
            ->select('id', 'particular as title', 'rate as amount')
            ->leftJoin('chsone_tax_classes', 'chsone_tax_classes.tax_class_id', '=', 'income_common_area_charges.tax_class_id')
            ->leftJoinSub($taxSubquery, 'tax_data', function ($join) {
                $join->on('tax_data.fk_tax_class_id', '=', 'chsone_tax_classes.tax_class_id');
            })
            ->selectRaw('IFNULL(tax_data.tax, 0) as tax')
            ->selectRaw('(income_common_area_charges.rate * IFNULL(tax_data.tax, 0) / 100) as tax_amount') // Calculate tax_amount
            ->where('income_common_area_charges.soc_id', $soc_id)
            ->where(function ($query) use ($selectedRecords) {
                foreach ($selectedRecords as $condition) {
                    $query->orWhere(function ($subQuery) use ($condition) {
                        $subQuery->where('effective_date', $condition['effective_date'])
                            ->where('particular', $condition['particular']);
                    });
                }
            })
            ->orderBy('id', 'asc')
            ->get();
        }

        $rate = IncomeCommonBilling::where('soc_id', $soc_id)
            //->where('id', $id)
            ->first();

        $today = now()->toDateString();

        $taxes = TaxCategory::where('soc_id', $soc_id)
            ->where('fk_tax_class_id', $rate->tax_class_id)
            ->where('status', 1)
            ->whereDate('tax_categories_from_date', '<=', $today)
            ->whereDate('tax_categories_to_date', '>=', $today)
            ->get();

// Retrieve a single tax class instance instead of a collection
        $taxClass = TaxClass::where('soc_id', $soc_id)
            ->where('tax_class_id', $rate->tax_class_id)
            ->first();

        $usableRate = $value === '' ? $rate->rate : $value;
        $taxAmount = $taxes->reduce(function ($carry, $tax) use ($usableRate) {
            if ($tax->tax_categories_type === 'fixed' && $usableRate != 0) {
                return $carry + $tax->tax_categories_amount;
            }

            return $carry + ($usableRate * ($tax->tax_categories_amount / 100));
        }, 0);

        foreach ($records as $record) {
            $record->tax = $taxAmount;
            $record->tax_amount = $record->amount * $taxAmount / 100;
        }
        $this->data = $records->toArray();
    }
}
