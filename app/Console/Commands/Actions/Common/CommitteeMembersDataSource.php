<?php

namespace App\Console\Commands\Actions\Common;

use Illuminate\Console\Command;
use App\Console\Commands\Action;
use Illuminate\Support\Facades\DB;

class CommitteeMembersDataSource extends Action
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'datasource:CommitteeMembers {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Get the all list of committee members';

    protected $formatter = [
        'id' => '',
        'title' => '',
        'fk_committee_id' => '',
        'designation_name' => '',
    ];

    /**
     * Execute the console command.
     */
    public function apply()
    {
        $committeeId = $this->input['id'];
        $obj = $this->tenantDB()->table('chsone_committee_members as committee_members')
        ->select(
            'committee_members.committee_member_id',
            'committee_members.fk_committee_id',
            'committee_members.fk_member_id',
            'committee_members.fk_unit_id',
            'committee_members.designation_name',
            'members.id',
            DB::raw("CONCAT(members.member_first_name, ' ', members.member_last_name, '(', units.soc_building_name, '-', units.unit_flat_number, ')') AS title")
        )
        ->join('chsone_members_master as members', 'committee_members.fk_member_id', '=', 'members.id')
        ->join('chsone_units_master as units', 'members.fk_unit_id', '=', 'units.unit_id')

        ->orderBy('committee_members.committee_member_id', 'asc')
        ->where('committee_members.fk_committee_id', $committeeId);
        $result = $obj->get();
        $this->data = $this->format($result->toArray());
    }
}
