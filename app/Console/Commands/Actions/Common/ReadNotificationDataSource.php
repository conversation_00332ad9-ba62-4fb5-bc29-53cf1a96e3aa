<?php

namespace App\Console\Commands\Actions\Common;

use App\Console\Commands\Action;
use App\Http\Traits\MongoTraits;

class ReadNotificationDataSource extends Action
{

    use MongoTraits; // Use the MongoTraits trait in this class

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'datasource:readNotification {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Mongo Notification Log Data Source';

    /**
     * Execute the console command.
     */
    public function apply()
    {
        $data = [
            'soc_id' => $this->input['company_id'],
            'user_id' => $this->input['user_id']
        ];

        if (!empty($this->input['notification_id'])) {
            $data['notification_id'] = $this->input['notification_id'];
        }
        $this->data = $this->readNotification($data);
    }
}
