<?php

namespace App\Console\Commands\Actions\Common;

use Illuminate\Console\Command;
use App\Console\Commands\Action;

class ActiveVendorsDataSource extends Action
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'datasource:ActiveVendors {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    protected $formatter = [
        'id' => '',
        'vendor_name' => '',
        'vendor_number' => '',
        'vendor_contact_number' => '',
        'vendor_phone_number' => '',
        'vendor_email' => '',
        'vendor_notes' => '',
        'vendor_expense_group_id' => '',
        'soc_id' => '',
        'status' => '',
    ];

    protected $formatterByKeys = ['id'];

    /**
     * Execute the console command.
     */
    public function apply()
    {
        $obj = $this->tenantDB()->table('chsone_vendors_master')
        ->select('vendor_id as id', 'vendor_name', 'vendor_number', 'vendor_contact_number', 'vendor_phone_number', 'vendor_email', 'vendor_notes', 'vendor_expense_group_id', 'soc_id', 'status')
        ->where('status', '1');

        $obj = $obj->orderBy('vendor_id', 'asc');
        $result = $obj->get();
        $this->data = $this->format($result->toArray());
    }
}
