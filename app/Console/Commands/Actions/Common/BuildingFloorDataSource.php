<?php

namespace App\Console\Commands\Actions\Common;

use App\Console\Commands\Action;
use Illuminate\Console\Command;

class BuildingFloorDataSource extends Action
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'datasource:BuildingFloorData {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Get Data of Building, Floor, Unit';

    protected $formatter = [
        'id' => '',
        'unit_flat_number' => '',
        'soc_building_floor' => '',
        'unit_category' => '',
        'unit_type' => '',
        'soc_building_name' => '',
    ];

    protected $formatterByKeys = ['id'];

    protected $schema = [

    ];

    /**
     * Execute the console command.
     */
    public function apply()
    {
        $obj = $this->tenantDB()->table('chsone_units_master')
        ->select('unit_id as id', 'unit_category', 'unit_type', 'soc_building_name', 'soc_building_floor', 'unit_flat_number');
        $obj = $obj->where('soc_id', $this->input['company_id']);
        $obj = $obj->orderBy('unit_id', 'asc');
        $result = $obj->get();
        $this->data = $this->format($result->toArray());
    }
}
