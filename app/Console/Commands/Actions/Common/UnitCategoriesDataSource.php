<?php

namespace App\Console\Commands\Actions\Common;

use App\Console\Commands\Action;
use Illuminate\Console\Command;

class UnitCategoriesDataSource extends Action
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'datasource:UnitCategories {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Get Data of All Unit Categories';

    protected $formatter = [
        'id' => '',
        'title' => ''
    ];

    protected $formatterByKeys = ['id'];

    /**
     * Execute the console command.
     */
    public function apply()
    {
        $obj = $this->tenantDB()->table('chsone_society_units_tpl')
        ->select('soc_units_type_id as id', 'type as title');
        $obj = $obj->orderBy('id', 'asc');
        $result = $obj->get();
        $this->data = $this->format($result->toArray());
    }
}
