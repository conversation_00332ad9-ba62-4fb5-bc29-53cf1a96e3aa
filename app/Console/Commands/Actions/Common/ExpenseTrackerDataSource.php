<?php

namespace App\Console\Commands\Actions\Common;

use App\Console\Commands\Action;
use Illuminate\Console\Command;

class ExpenseTrackerDataSource extends Action
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'datasource:ExpenseTracker {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Get the all list of expense tracker';

    protected $formatter = [
        'id' => '',
        'title' => '',
        // 'soc_id' => '',
        // 'status' => ''
    ];

    protected $formatterByKeys = ['id'];

    /**
     * Execute the console command.
     */

     protected $hugeData = true;

    public function apply()
    {
        $obj = $this->tenantDB()->table('chsone_expense_tracker')
        ->select('et_id as id', 'et_type_name as title');
        $obj = $obj->orderBy('id', 'desc');
        $result = $obj->get();
        $this->data = $this->format($result->toArray());
    }
}
