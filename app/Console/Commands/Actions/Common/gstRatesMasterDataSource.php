<?php

namespace App\Console\Commands\Actions\Common;

use Illuminate\Console\Command;
use App\Console\Commands\Action;
use Illuminate\Support\Facades\DB;

class gstRatesMasterDataSource extends Action
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'datasource:gstRatesMaster {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Get Data List of All Gst Rates Master';

    protected $formatter = [
        'id' => '',
        'type' => '',
        'name' => '',
        'rate' => '',
    ];

    protected $formatterByKeys = ['id'];

    /**
     * Execute the console command.
     */
    public function apply()
    {
        $obj = $this->tenantDB()->table('gst_rates_master')
        ->select('id', 'type', 'name', DB::raw('CASE WHEN type = "gst" THEN rate * 2 ELSE rate END as rate'))
        ->where('status', '1');
        $result = $obj->get();
        $this->data = $this->format($result->toArray());
    }
}
