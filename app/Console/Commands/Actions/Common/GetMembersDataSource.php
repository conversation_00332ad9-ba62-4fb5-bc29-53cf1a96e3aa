<?php

namespace App\Console\Commands\Actions\Common;

use App\Console\Commands\Action;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class GetMembersDataSource extends Action
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'datasource:GetMembersDataSource {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Get list of members/non-members';

    protected $formatter = [
        'id' => '',
        'title' => '',
    ];

    protected $formatterByKeys = ['id'];

    /**
     * Execute the console command.
     */
    public function apply()
    {
        $keyword = $this->input['keyword'];
        // check keyword length is greater than 45 otherwise return error
        if (strlen($keyword) > 45) {
            $this->status = 'error';
            $this->message = 'Keyword length should be less than 45 characters';
            $this->statusCode = 400;
            $this->data = [];
            return;
        }

        // check account type is only member or nonmember otherwise return error
        if (!in_array($this->input['account_type'], ['member', 'nonmember'])) {
            $this->status = 'error';
            $this->message = 'Invalid account type';
            $this->statusCode = 400;
            $this->data = [];
            return;
        }

        if($this->input['account_type'] == 'member') {
            $result = $this->getMember($keyword);
        } else if($this->input['account_type'] == 'nonmember') {
            $result = $this->getNonMember($keyword);
        }

        $this->data = $result;

    }

    public function getMember($keyword)
    {
        try {
            // Step 1: Query members with their unit details
            $obj = $this->tenantDB()->table('chsone_members_master as members_master')
                ->select(
                    'members_master.id',
                    'units_master.unit_id as unit_id',
                    'units_master.soc_building_name AS building',
                    'units_master.unit_flat_number',
                    DB::raw("CONCAT(members_master.member_first_name, ' ', members_master.member_last_name, '(', units_master.soc_building_name, '-', units_master.unit_flat_number, ')') AS title"),
                    DB::raw("'member' as account_type"),
                    DB::raw("CONCAT(soc_building_name , '-', unit_flat_number) AS unit_name"),
                    'salute',
                    'member_gender',
                    'members_master.created_date as since_staying_date',
                    'noc.created_date as letter_date',
                    DB::raw("CONCAT(members_master.member_first_name, ' ', members_master.member_last_name) AS owner_name"),
                )
                ->leftJoin('chsone_units_master as units_master', 'units_master.unit_id', '=', 'members_master.fk_unit_id')
                ->leftJoin('chsone_member_type_master as mtype', 'mtype.member_type_id', '=', 'members_master.member_type_id')
                ->leftJoin('chsone_noc_requests as noc', 'noc.unit_id', '=', 'members_master.fk_unit_id');
    
            if (!empty($keyword)) {
                $obj->where('members_master.status', 1);
                $obj->where(DB::raw("CONCAT(members_master.member_first_name, ' ', members_master.member_last_name, '(', units_master.soc_building_name, '-', units_master.unit_flat_number, ')')"), 'LIKE', '%' . $keyword . '%');
            }
    
            $members = $obj->get();
    
            // Step 2: Get society details
            $society = $this->masterDB()->table('chsone_societies_master')
                ->select(
                    'soc_id', 'soc_name', 'soc_reg_num', 'soc_address_1', 'soc_address_2', 
                    'soc_landmark', 'soc_city_or_town', 'soc_state', 'soc_pincode',
                    'soc_gst_number', 'soc_office_mobile', 'soc_office_email'
                )
                ->where('soc_id', $this->input['company_id'])
                ->first();
    
            // Step 3: Attach society info and build owner_address
            foreach ($members as $item) {
                $item->society_id = $society->soc_id ?? null;
                $item->society_name = $society->soc_name ?? null;
                $item->society_reg_num = $society->soc_reg_num ?? null;
                $item->society_address_1 = $society->soc_address_1 ?? null;
                $item->society_address_2 = $society->soc_address_2 ?? null;
                $item->society_landmark = $society->soc_landmark ?? null;
                $item->society_city_or_town = $society->soc_city_or_town ?? null;
                $item->society_state = $society->soc_state ?? null;
                $item->society_pincode = $society->soc_pincode ?? null;
                $item->society_gst_num = $society->soc_gst_number ?? null;
                $item->society_office_mobile = $society->soc_office_mobile ?? null;
                $item->society_office_mail = $society->soc_office_email ?? null;
    
                // Construct owner_address
                $building = $item->building ?? '';
                $flat = $item->unit_flat_number ?? '';
                $addr1 = $society->soc_address_1 ?? '';
                $addr2 = $society->soc_address_2 ?? '';
    
                $parts = array_filter([$building . '-' . $flat, $addr1, $addr2]);
                $item->owner_address = implode(', ', $parts);
            }
    
            return $members;
        } catch (\Exception $e) {
            $this->status = 'error';
            $this->message = $e->getMessage();
            $this->statusCode = 500;
            $this->data = [];
            return;
        }
    }

    public function getNonMember($keyword)
    {
        $obj = $this->tenantDB()->table('chsone_nonmember_master')
        ->select(
            'nonmember_id AS id',
            DB::raw("CONCAT(first_name, ' ', last_name) AS title"),
            DB::raw("'nonmember' as account_type")
        );
        if (!empty($keyword)) {
            $obj->where("first_name", 'LIKE', '%' . $keyword . '%');
        }

        $result = $obj->get();

        return $result;
    }
}
