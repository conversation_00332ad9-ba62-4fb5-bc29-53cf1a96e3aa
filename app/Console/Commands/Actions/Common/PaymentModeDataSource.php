<?php

namespace App\Console\Commands\Actions\Common;

use App\Console\Commands\Action;
use Illuminate\Console\Command;

class PaymentModeDataSource extends Action
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'datasource:PaymentMode {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Get Data of Payment Modes';

    protected $formatter = [
        'id' => '',
        'title' => '',
    ];

    protected $formatterByKeys = ['id'];

    /**
     * Execute the console command.
     */
    public function apply()
    {
        // dd('wip');
        $obj = $this->tenantDB()->table('income_invoice_payment')
        ->select('payment_id as id', 'payment_mode as title')
        ->distinct('payment_mode');
        $obj = $obj->orderBy('id', 'asc');
        $result = $obj->get();
        $this->data = $this->format($result->toArray());
    }
}
