<?php

namespace App\Console\Commands\Actions\Common;

use App\Console\Commands\Action;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class GetInvoicesDataSource extends Action
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'datasource:GetInvoicesDataSource {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Get list of member/non-member invoices';

    protected $formatter = [
        'id' => '',
        'invoice_number' => '',
        'date_range' => '',
        'combined_range' => '',
    ];

    protected $formatterByKeys = ['id'];

    /**
     * Execute the console command.
     */
    public function apply()
    {
        // $keyword = $this->input['keyword'];
        $account_id = $this->input['account_id'];

        if ($this->input['account_type'] == 'member') {

            $bill_type = $this->input['bill_type'] ?? 'maintenance';

            $result = $this->getMemberInvoices($account_id, $bill_type);

        } elseif ($this->input['account_type'] == 'non_member') {

            $result = $this->getNonMemberInvoices($account_id);
        }

        $this->data = $this->format($result->toArray());
    }

    private function getMemberInvoices($account_id, $bill_type)
    {
        if (strtolower($bill_type) == 'maintenance') {

            $obj = $this->tenantDB()->table('income_unit_invoices as unit_invoices')
                ->select(
                    DB::raw('DISTINCT(invoice_number) as invoice_number'),
                    'unit_invoice_id as id',
                    DB::raw("CONCAT(
                    DATE_FORMAT(from_date, '%b %d, %Y'),
                    ' - ',
                    DATE_FORMAT(to_date, '%b %d, %Y')
                ) as date_range"),
                    DB::raw("CASE
                            WHEN from_date IS NULL OR to_date IS NULL THEN invoice_number
                            WHEN TRIM(CONCAT(
                                DATE_FORMAT(from_date, '%b %d, %Y'),
                                ' - ',
                                DATE_FORMAT(to_date, '%b %d, %Y')
                            )) = '-' THEN invoice_number
                            WHEN TRIM(CONCAT(
                                DATE_FORMAT(from_date, '%b %d, %Y'),
                                ' - ',
                                DATE_FORMAT(to_date, '%b %d, %Y')
                            )) = '' THEN invoice_number
                            ELSE IFNULL(
                                CONCAT(
                                    invoice_number, ' (',
                                    DATE_FORMAT(from_date, '%b %d, %Y'),
                                    ' - ',
                                    DATE_FORMAT(to_date, '%b %d, %Y'),
                                    ')'
                                ), invoice_number
                            )
                        END as combined_range")
                )
                ->leftJoin('chsone_units_master as unit_master', 'unit_invoices.fk_unit_id', '=', 'unit_master.unit_id')

                ->whereIn('unit_invoices.payment_status', ['paid', 'partialpaid'])
                ->where('unit_invoices.status', '!=', 'cancelled')
                ->where('unit_master.unit_id', $account_id);

                // if (!empty($keyword)) {
                //     if (preg_match('/\(([^)]+)\)/', $keyword, $matches)) {
                //         $newKeyword = $matches[1]; // Extracting content inside parentheses
                //     } else {
                //         $newKeyword = $keyword; // If no parentheses are found, use the original keyword
                //     }
                //     $obj->where(DB::raw("CONCAT(unit_master.soc_building_name, '-', unit_master.unit_flat_number)"), 'like', '%' . $newKeyword . '%');
                // }

            $result = $obj->get();
        } elseif (strtolower($bill_type) == 'incidental') {
            $obj = $this->tenantDB()->table('income_common_billing_charges as common_billing')

                ->select(
                    'common_billing.invoice_number as invoice_number',
                    'id',
                    DB::raw("CONCAT(
                    DATE_FORMAT(from_date, '%b %d, %Y'),
                    ' - ',
                    DATE_FORMAT(to_date, '%b %d, %Y')
                ) as date_range"),
                    DB::raw("CASE
                            WHEN from_date IS NULL OR to_date IS NULL THEN common_billing.invoice_number
                            WHEN TRIM(CONCAT(
                                DATE_FORMAT(from_date, '%b %d, %Y'),
                                ' - ',
                                DATE_FORMAT(to_date, '%b %d, %Y')
                            )) = '-' THEN common_billing.invoice_number
                            WHEN TRIM(CONCAT(
                                DATE_FORMAT(from_date, '%b %d, %Y'),
                                ' - ',
                                DATE_FORMAT(to_date, '%b %d, %Y')
                            )) = '' THEN common_billing.invoice_number
                            ELSE CONCAT(
                                common_billing.invoice_number, ' (',
                                DATE_FORMAT(from_date, '%b %d, %Y'),
                                ' - ',
                                DATE_FORMAT(to_date, '%b %d, %Y'),
                                ')'
                            )
                        END as combined_range")
                )

                ->leftJoin('chsone_units_master as unit_master', 'common_billing.fk_unit_id', '=', 'unit_master.unit_id')
                ->whereIn('common_billing.payment_status', ['paid', 'partialpaid'])
                ->where('common_billing.payment_status', '!=', 'cancelled')
                ->where('unit_master.unit_id', $account_id);
                // if (!empty($keyword)) {
                //     if (preg_match('/\(([^)]+)\)/', $keyword, $matches)) {
                //         $newKeyword = $matches[1]; // Extracting content inside parentheses
                //     } else {
                //         $newKeyword = $keyword; // If no parentheses are found, use the original keyword
                //     }
                //     $obj->where(DB::raw("CONCAT(unit_master.soc_building_name, '-', unit_master.unit_flat_number)"), 'like', '%' . $newKeyword . '%');
                // }

            $result = $obj->get();
        }

        return $result;
    }

    private function getNonMemberInvoices($account_id)
    {
        // $id = $this->input['id'];
        $obj = $this->tenantDB()->table('income_nonmember_bills as non_member_bills')
            ->select('bill_number as invoice_number', 'nonmember_bill_id as id')
            ->selectRaw("CONCAT(DATE_FORMAT(from_date, '%b %d, %Y'), ' - ', DATE_FORMAT(end_date, '%b %d, %Y')) as date_range")
            ->selectRaw("
                CASE
                    WHEN from_date IS NULL OR end_date IS NULL THEN bill_number
                    WHEN TRIM(CONCAT(
                        DATE_FORMAT(from_date, '%b %d, %Y'),
                        ' - ',
                        DATE_FORMAT(end_date, '%b %d, %Y')
                    )) = '-' THEN bill_number
                    WHEN TRIM(CONCAT(
                        DATE_FORMAT(from_date, '%b %d, %Y'),
                        ' - ',
                        DATE_FORMAT(end_date, '%b %d, %Y')
                    )) = '' THEN bill_number
                    ELSE CONCAT(
                        bill_number, ' (',
                        DATE_FORMAT(from_date, '%b %d, %Y'),
                        ' - ',
                        DATE_FORMAT(end_date, '%b %d, %Y'),
                        ')'
                    )
                END as combined_range
            ")
            ->whereIn('payment_status', ['paid', 'partialpaid'])
            ->where('payment_status', '!=', 'cancelled')
            ->where('nonmember_id','=', $account_id);
            // ->where('billed_name', 'like', '%' . $keyword . '%');

        $result = $obj->get();

        return $result;
    }
}
