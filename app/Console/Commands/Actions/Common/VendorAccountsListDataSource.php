<?php

namespace App\Console\Commands\Actions\Common;

use Illuminate\Console\Command;
use App\Console\Commands\Action;

class VendorAccountsListDataSource extends Action
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'datasource:VendorAccountsList {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Get Data List of All Income Accounts';

    protected $formatter = [
        'id' => '',
        'title' => ''
    ];

    protected $formatterByKeys = ['id'];

    /**
     * Execute the console command.
     */
    public function apply()
    {
        $obj = $this->tenantDB()->table('chsone_expense_tracker')
        ->select('et_id as id', 'et_type_name as title')
        ->where('status', '1');
        $obj = $obj->orderBy('et_id', 'asc');
        $result = $obj->get();
        $this->data = $this->format($result->toArray());
    }
}
