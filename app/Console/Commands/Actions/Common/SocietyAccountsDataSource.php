<?php

namespace App\Console\Commands\Actions\Common;

use Illuminate\Console\Command;
use App\Console\Commands\Action;

class SocietyAccountsDataSource extends Action
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'datasource:SocietyAccounts {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    protected $formatter = [
        'account_id' => '',
        'bank_name' => '',
        'account_name' => '',
        'ledger_account_id' => '',
        'branch' => '',
        'account_number' => '',
        'bank_address' => '',
        'bank_city' => '',
        'bank_ifsc' => '',
        'default_account' => '',
        'status' => '',
        'group_id' => '',
        'active_for_payments' => '',
        'default_bank_for_incidental' => '',
        'default_bank_for_nonmember' => '',
    ];

    protected $formatterByKeys = ['account_id'];

    /**
     * Execute the console command.
     */
    public function apply()
    {
        $companyId = $this->input['company_id'];
        $obj = $this->tenantDB()->table('chsone_accounts_master as accounts')
        ->select(
            'accounts.account_id',
            'accounts.bank_name',
            'accounts.account_name',
            'accounts.ledger_account_id',
            'accounts.branch',
            'accounts.account_number',
            'accounts.bank_address',
            'accounts.bank_city',
            'accounts.bank_ifsc',
            'accounts.default_account',
            'accounts.status',
            'accounts.group_id',
            'accounts.active_for_payments',
            'accounts.default_bank_for_incidental',
            'accounts.default_bank_for_nonmember'
        )
        ->where('accounts.soc_id', $companyId)
        ->where('accounts.default_account', 1)
        ->where('accounts.status', 1);
        $result = $obj->get();
        dd($result->toArray());
    }
}
