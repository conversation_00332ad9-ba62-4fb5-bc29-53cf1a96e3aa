<?php

namespace App\Console\Commands\Actions\Income;

use App\Console\Commands\Action;

class UpdateBillableNoteDataSource extends Action
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'datasource:updateBillableNote {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Delete the billable note details';
    /**
     * Execute the console command.
     */
    public function apply()
    {
        $id=$this->input['id'];
        $company_id =$this->input['company_id'];
            // Find the billable item by ID and society ID
        $billableItem = $this->tenantDB()->table('income_billable_item')->where('income_billable_item_id', $id)
            ->where('soc_id', $company_id) // Replace with actual way to get soc_id if different
            ->first();


        if(empty($billableItem)){
            $this->status = 'error';
            $this->message = 'No Billable Item found for this id';
            $this->statusCode = 400;
            $this->data = [];
            return;
        }

        $result = $this->tenantDB()->table("income_billable_item")
        ->where('income_billable_item_id', $id)
        ->update([
            'amount' => $this->input['amount'],
            'income_account_id' => $this->input['income_account_id'],
            "apply_late_payment_interest"=>$this->input['apply_late_payment_interest'] ?? 0,
            'due_date' =>  $this->input['due_date'] ?? null,
            'note' => $this->input['note'],
            'updated_date' =>date('Y-m-d'),
            'updated_by' => $this->input['user_id'],
        ]);

        if($result){
            $this->status = 'success';
            $this->message = 'Billable Item Update Successfully';
            $this->statusCode = 200;
            $this->data = [];
            return;
        }else{
            $this->status = 'error';
            $this->message = 'Failed To Update Billable Items';
            $this->statusCode = 403;
            $this->data = [];
        }

     }


}
