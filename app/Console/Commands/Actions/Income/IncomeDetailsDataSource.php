<?php

namespace App\Console\Commands\Actions\Income;

use Illuminate\Console\Command;
use App\Console\Commands\Action;
use App\Models\Tenants\ChsoneCreditNote;
use App\Models\Tenants\ChsoneGrpLedgerTree;
use App\Models\Tenants\IncomeInvoiceGeneralSetting;
use App\Models\Tenants\IncomeUnitInvoice;
use Illuminate\Support\Facades\DB;

class IncomeDetailsDataSource extends Action
{
    protected $signature = 'datasource:IncomeDetails {flowId} {parentId} {input}';

    protected $description = 'Get the incidental bill details';

    public function apply()
    {
        try {
            // $id is a unit id
            $id = $this->input['id'];

            // Check if unit id is valid or not from chsone_units_master table
            $unit = $this->tenantDB()->table('chsone_units_master')
                ->where('unit_id', $id)
                ->get();
            if (count($unit) == 0) {
                $this->status = 'error';
                $this->message = 'Please provide a valid unit id';
                $this->statusCode = 404;
                $this->data = [];
                return;
            }

            $invoice_number = $this->input['invoice_number'];
            $invoice_number = explode('-', $invoice_number);
            $invoice_number = $invoice_number[1];
            // According to this invoice number need to find out the id from income_common_billing_charges table
            if ($invoice_number) {
                $obj = $this->tenantDB()->table('income_invoice_particular as ivp')
                    ->select('*')
                    ->where('ivp.invoice_number', $invoice_number)
                    // ->where('ivp.fk_unit_id', $id)
                    ->first();
                    
                if ($obj) {
                    $obj = $this->tenantDB()->table('income_invoice_particular as ivp')
                        ->select(
                            'ivp.id',
                            'ivp.fk_unit_id AS unit_id',
                            'ivp.invoice_number',
                            'ivp.bill_type',
                            'ivp.particular',
                            DB::raw('FORMAT(ivp.amount, 2) AS amount'),
                            'ivp.tax_applicable',
                            'ivp.tax_exemptions',
                            DB::raw('FORMAT(ivp.is_particular_paid, 2) AS is_particular_paid'),
                            DB::raw('FORMAT(ivp.particular_paid_amount, 2) AS particular_paid_amount'),
                            'unit.unit_id',
                            'unit.unit_flat_number',
                            'unit.soc_building_name',
                            'unit.unit_area',
                            DB::raw('FORMAT(unitInvoice.principal_amount, 2) AS principal_amount'),
                            DB::raw('FORMAT(unitInvoice.interest_amount, 2) AS interest_amount'),
                            DB::raw('FORMAT(unitInvoice.outstanding_principal, 2) AS outstanding_principal'),
                            DB::raw('FORMAT(unitInvoice.outstanding_interest, 2) AS outstanding_interest'),
                            DB::raw('FORMAT(unitInvoice.roundoff_amount, 2) AS roundoff_amount')
                        )
                        ->leftJoin('chsone_units_master as unit', 'ivp.fk_unit_id', '=', 'unit.unit_id')
                        ->leftJoin('income_unit_invoices as unitInvoice', 'ivp.invoice_number', '=', 'unitInvoice.invoice_number')
                        ->where('ivp.invoice_number', $invoice_number)
                        ->orderBy('ivp.id', 'DESC');
                    $result['invoice_particular'] = $obj->get();


                    // Need to fetch the data from chsone_taxlog table according to $result->invoice_number
                    /*$taxLog = $this->tenantDB()->table('chsone_tax_log')
                    ->select('tax_amount','tax_class_id','tax_rule','tax_rule_id','tax_rate','tax_rate_type')
                    ->where('invoice_number', $result->invoice_number)
                    ->get();

                $taxLog = $taxLog->toArray();
                $result->taxLog = $taxLog;

                // First check if the taxLog is empty or not
                if (!empty($taxLog)) {
                    // Need to fetch gst number from chsone_tax_classesm table according to $result->tax_class_id
                    $gstNumber = $this->tenantDB()->table('chsone_tax_classes')
                    ->select('tax_categories_footer')
                    ->where('tax_class_id', $taxLog[0]->tax_class_id)
                    ->first();

                    if($gstNumber) {
                        $result->gstNumber = $gstNumber->tax_categories_footer;
                    } else {
                        $result->gstNumber = '';
                    }
                } else {
                    $result->gstNumber = '';
                }*/

                    // Credit Note Adjustment Data
                    $objCreditNote = IncomeUnitInvoice::where('invoice_number', $invoice_number)
                    ->join('chsone_units_master', 'income_unit_invoices.fk_unit_id', '=', 'chsone_units_master.unit_id')
                    ->select('income_unit_invoices.*', 'chsone_units_master.unit_area')
                    ->first();
                    if ($objCreditNote) {
                        // Keep values as numeric for calculations - formatting will be done in workflow after calculations
                        $objCreditNote->principal_amount = (float)$objCreditNote->principal_amount;
                        $objCreditNote->interest_amount = (float)$objCreditNote->interest_amount;
                        $objCreditNote->advance_amount = (float)$objCreditNote->advance_amount;
                        $objCreditNote->outstanding_principal = (float)$objCreditNote->outstanding_principal;
                        $objCreditNote->outstanding_interest = (float)$objCreditNote->outstanding_interest;
                        $objCreditNote->roundoff_amount = (float)$objCreditNote->roundoff_amount;

                        // Convert to array for consistent access pattern in workflow
                        $result['creditNoteAdjustment'] = $objCreditNote->toArray();
                    } else {
                        $result['creditNoteAdjustment'] = null;
                    }

                    // Add general settings data
                    $arrGeneralSettingData = [
                        'soc_id' => $this->input['company_id'],
                        'setting_key' => [
                            'INCOME_PAYMENT_INSTRUCTION',
                            'INCOME_SHOW_INTEREST_BREAKUP',
                            'INVOICE_LOGO',
                            'PAYMENT_TRANSACTION_RECEIPT_LAST_PERIOD',
                            'SHOW_SOCIETY_SIGNATURE',
                            'SHOW_CHSONE_FOOTER',
                            'SHOW_BANK_DETAIL'
                        ]
                    ];
                    $arrInvoiceGeneralSetting = $this->getInvoiceGeneralSetting($arrGeneralSettingData);

                    if (!empty($arrInvoiceGeneralSetting)) {
                        foreach ($arrInvoiceGeneralSetting as $eachGeneralSetting) {
                            if ($eachGeneralSetting['setting_key'] == 'INCOME_PAYMENT_INSTRUCTION') {
                                $result['arrInvoiceGeneralNote'] = explode(PHP_EOL, $eachGeneralSetting['setting_value']);
                            }

                            if (strtolower($eachGeneralSetting['setting_key']) == 'income_show_interest_breakup' && $eachGeneralSetting['setting_value'] == 1) {
                                $result['showInterestBreakup'] = 1;
                            } else {
                                $result['showInterestBreakup'] = 0;
                            }

                            if (strtolower($eachGeneralSetting['setting_key']) == 'show_society_signature' && strtolower($eachGeneralSetting['setting_value']) == 'yes') {
                                $result['showSocietySignature'] = true;
                            } else {
                                $result['showSocietySignature'] = false;
                            }

                            if (strtolower($eachGeneralSetting['setting_key']) == 'show_chsone_footer' && strtolower($eachGeneralSetting['setting_value']) == 'yes') {
                                $result['showChsoneFooter'] = true;
                            } else {
                                $result['showChsoneFooter'] = false;
                            }

                            if (strtolower($eachGeneralSetting['setting_key']) == 'show_bank_detail' && strtolower($eachGeneralSetting['setting_value']) == 'yes') {
                                $result['showBankDetail'] = 1;
                            } else {
                                $result['showBankDetail'] = 0;
                            }
                        }
                    }

                    // Get bank account details
                    $arrAccountDetail = $this->getDefaultBankAccountDetailIncidental(["soc_id" => $this->input["company_id"]]);

                    if (empty($arrAccountDetail)) {
                        $arrAccountDetail = $this->getDefaultBankAccountDetail(["soc_id" => $this->input["company_id"]]);
                    }
                    $result['arrBankDetail'] = $arrAccountDetail;
                    $this->data = $result;
                } else {
                    $this->status = false;
                    $this->statusCode = 400;
                    $this->message = "No data found for this invoice number";
                    $this->data = [];
                    return;
                }
            } else {
                $this->status = false;
                $this->statusCode = 400;
                $this->message = "Please provide an invoice number";
                $this->data = [];
                return;
            }
        } catch (\Exception $e) {
            dd($e);
        }
    }

    public function getInvoiceGeneralSetting($data = [])
    {
        $arrInvoiceGeneralSetting = [];

        // Ensure the required keys are present in the input data
        if (!empty($data['soc_id']) && !empty($data['setting_key']) && is_array($data['setting_key'])) {
            $objInvoiceGeneralSetting = IncomeInvoiceGeneralSetting::where('soc_id', $data['soc_id'])
                ->whereIn('setting_key', $data['setting_key'])
                ->get();

            if (!empty($objInvoiceGeneralSetting)) {
                $arrInvoiceGeneralSetting = $objInvoiceGeneralSetting->toArray();
            }
        }

        return $arrInvoiceGeneralSetting;
    }

    public function getDefaultBankAccountDetailIncidental($data = [])
    {
        try {
            $arrAccountDetail = [];

            if (!empty($data['soc_id'])) {
                $resultset = $this->tenantDB()->table('chsone_grp_ledger_tree as grpLedger')
                    ->select([
                        'grpLedger.ledger_account_id',
                        'grpLedger.ledger_account_name',
                        'grpLedger.context',
                        'account.account_name',
                        'account.default_account',
                        'account.bank_name',
                        'account.account_number',
                        'account.bank_address',
                        'account.branch',
                        'account.bank_city',
                        'account.bank_ifsc',
                        'account.account_id'
                    ])
                    ->where('grpLedger.soc_id', $data['soc_id'])
                    ->where('grpLedger.entity_type', 'ledger')
                    ->where('grpLedger.status', 1)
                    ->where('account.default_bank_for_incidental', 1)
                    ->whereIn('grpLedger.context', ['bank'])
                    ->leftJoin('chsone_accounts_master as account', 'grpLedger.ledger_account_id', '=', 'account.ledger_account_id')
                    ->where('account.default_account', 1)
                    ->first();

                if ($resultset) {
                    $arrAccountDetail = (array) $resultset; // Convert the object to an associative array
                }
            }

            return $arrAccountDetail;
        } catch (\Exception $e) {
            dd($e);
        }
    }

    public function getDefaultBankAccountDetail($data = [])
    {
        $arrAccountDetail = [];

        // Build the query using Laravel's Query Builder
        try {
            $query = $this->tenantDB()->table('chsone_grp_ledger_tree as grpLedger')
                ->select([
                    'grpLedger.ledger_account_id',
                    'grpLedger.ledger_account_name',
                    'grpLedger.context',
                    'account.account_name',
                    'account.default_account',
                    'account.bank_name',
                    'account.account_number',
                    'account.bank_address',
                    'account.branch',
                    'account.bank_city',
                    'account.bank_ifsc',
                    'account.account_id'
                ])
                ->where('grpLedger.soc_id', $data['soc_id'])
                ->where('grpLedger.entity_type', 'ledger')
                ->where('grpLedger.status', 1)
                ->where('account.default_account', 1)
                ->whereIn('grpLedger.context', ['bank'])
                ->leftJoin('chsone_accounts_master as account', 'grpLedger.ledger_account_id', '=', 'account.ledger_account_id')
                ->where('account.default_account', 1);

            // Execute the query and get the first result
            $resultset = $query->first();

            if (!empty($resultset)) {
                $arrAccountDetail = (array) $resultset; // Convert the object to an associative array
            }

            return $arrAccountDetail;
        } catch (\Exception $e) {
            // Log the error
            throw new \Exception("Error in getDefaultBankAccountDetail: " . $e->getMessage());
        }
    }
}
