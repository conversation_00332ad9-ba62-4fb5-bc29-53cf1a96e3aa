<?php

namespace App\Console\Commands\Actions\Income;

use App\Console\Commands\Action;
use Illuminate\Console\Command;

class PayNonMemberBillDataSource extends Action
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'datasource:payNonMemberBill {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */
    public function apply()
    {
        $id = $this->input['id'] ?? '';
        $data = $this->input;
        $data = json_decode(json_encode($data), true);

        $data['nonmember_bill_id'] = $id;
        $data['soc_id'] = $data['company_id'];
        // Fetch unpaid income details
        $arrNonMemberIncome = $this->getNonmemberUnpaidIncomeDetail($data);

        if (empty($arrNonMemberIncome)) {
            // Session::flash('err_msg', 'No record found.');
            $this->message = 'Bill ID not found';
            $this->status = 'error';
            $this->statusCode = 404;
            $this->data = [];
            return;
            // return redirect()->route('income.nonmember');
        }


        // Fetch payment details

        if (!empty($arrNonMemberIncome)) {
            $arrPaymentDetail = $this->getNonmemberPaymentDetail($arrNonMemberIncome);
            if (!empty($arrPaymentDetail)) {
                $totalPaidAmount = (float) $arrPaymentDetail['payment_amount'] + $arrPaymentDetail['tds_deducted'];
            }
            if (!empty($totalPaidAmount) && $totalPaidAmount > 0) {
                $totalPaidAmount = (float) round($totalPaidAmount - $arrNonMemberIncome['advance_amount'], 2);
            }

            // Prepare options for form view
            $arroptions = $this->preparePaymentOptions($id, $arrNonMemberIncome, $totalPaidAmount);

        }

        $obj = $this->tenantDB()->table('income_nonmember_bills AS nonmemberincome')
            ->select(
                'nonmemberincome.booker_email_address',
                'nonmemberincome.hsn',
                'nonmemberincome.nonmember_bill_id as id',
                'nonmemberincome.soc_id',
                'nonmemberincome.billed_name',
                'nonmemberincome.billed_name as received_from',
                'nonmemberincome.bill_for',
                'nonmemberincome.tax_class_id',
                'nonmemberincome.bill_number as invoice_number',
                'nonmemberincome.bill_amount',
                'nonmemberincome.advance_amount',
                'nonmemberincome.credit_amount',
                'nonmemberincome.discount_amount',
                'nonmemberincome.total_taxes',
                'nonmemberincome.payment_status',
                'nonmemberincome.from_date',
                'nonmemberincome.end_date',
                'nonmemberincome.bill_from',
                'nonmemberincome.booker_mobile_number',
                'nonmemberincome.nonmember_id',
                'nonmemberincome.bill_date'
            )
            ->whereNot('nonmemberincome.payment_status', 'cancelled')
            ->where('nonmemberincome.nonmember_bill_id', $id)
            ->first();


        $obj = json_decode(json_encode($obj), true);

        $total_taxes = empty($obj['total_taxes']) ? 0 : $obj['total_taxes'];
        $advance_amount = empty($obj['advance_amount']) ? 0 : $obj['advance_amount'];

        $total_due = $obj['bill_amount'] + $total_taxes - $advance_amount;

        if (!empty($arroptions)) {
            $obj['total_due'] = $total_due;

            $obj['bill_number'] = $arroptions['bill_number'];

            $obj['booker_name'] = $arroptions['booker_name'];

            $obj['bill_total_amount'] = $arroptions['bill_total_amount'];

            $obj['discount_amount'] = $arroptions['discount_amount'];

            $obj['advance_amount'] = $arroptions['advance_amount'];

            $obj['partial_paid_amount'] = $arroptions['partial_paid_amount'];

            $obj['tax_deduction'] = $arroptions['tax_deduction'];

            $obj['due_amount'] = $arroptions['due_amount'];

            $obj['payment_amount'] = $arroptions['due_amount'];
        }

        $this->data = $obj;
    }

    public function getNonmemberUnpaidIncomeDetail(array $data): array
    {
        // Fetch the unpaid or partially paid income details based on the provided criteria
        $nonmemberIncome = $this->tenantDB()->table('income_nonmember_bills')
            ->where('soc_id', $data['soc_id'])
            ->whereIn('payment_status', ['unpaid', 'partialpaid'])
            ->where(function ($query) use ($data) {
                // Checking for bill_number or nonmember_bill_id
                $query->where('bill_number', $data['bill_number'] ?? '')
                    ->orWhere('nonmember_bill_id', $data['nonmember_bill_id'] ?? '');
            })
            ->first();

        // Convert the result to an array if a record is found, otherwise return an empty array
        return $nonmemberIncome ? (array) $nonmemberIncome : [];
    }

    public function getNonmemberPaymentDetail($data)
    {
        // Initialize the payment details array with default values
        $arrPaymentDetail = [
            'payment_amount' => 0,
            'tax_deducted' => 0,
            'tds_deducted' => 0,
            'discount_amount' => 0,
        ];

        // Fetch all payment records based on the provided soc_id and nonmember_bill_id
        $nonmemberIncomePayments = $this->tenantDB()->table('income_nonmember_bill_payments')->where('soc_id', $data['soc_id'])
            ->where('fk_nonmember_bill_id', $data['nonmember_bill_id'])
            ->get();

        // Iterate through each payment record and accumulate the payment details
        foreach ($nonmemberIncomePayments as $payment) {
            $arrPaymentDetail['bill_number'] = $payment->bill_number;
            $arrPaymentDetail['payment_amount'] += round((float) $payment->payment_amount, 2);
            $arrPaymentDetail['tax_deducted'] += round((float) $payment->tax_deducted, 2);
            $arrPaymentDetail['tds_deducted'] += round((float) $payment->tds_deducted, 2);
            $arrPaymentDetail['discount_amount'] += round((float) $payment->discount_amount, 2);
        }

        return $arrPaymentDetail;
    }

    public function preparePaymentOptions($billNumber, $nonMemberIncome, $totalPaidAmount)
    {
        return [
            'bill_number' => $billNumber,
            'booker_name' => $nonMemberIncome['billed_name'],
            'bill_total_amount' => round($nonMemberIncome['bill_amount'], 2),
            'discount_amount' => round($nonMemberIncome['discount_amount'], 2),
            'advance_amount' => round($nonMemberIncome['advance_amount'] + $nonMemberIncome['credit_amount'], 2),
            'partial_paid_amount' => $totalPaidAmount,
            'tax_deduction' => round($nonMemberIncome['total_taxes'], 2),
            'due_amount' => round($nonMemberIncome['bill_amount'] - $nonMemberIncome['total_deduction'] + $nonMemberIncome['total_taxes'] - $totalPaidAmount - $nonMemberIncome['advance_amount'] - $nonMemberIncome['credit_amount'], 2),
        ];
    }
}
