<?php

namespace App\Console\Commands\Actions\Income;

use App\Console\Commands\Action;
use Carbon\Carbon;
use Carbon\CarbonPeriod;

class AddBillableNoteDataSource extends Action
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'datasource:addBillableNote {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'add the billable note ';
    /**
     * Execute the console command.
     */


    public function apply()
    {

        $data = $this->input;
        $billingPeriodsResult = $this->action('datasource:getBillingPeriods', $this->pointer, $this->input);
        $saveData = $this->addBillableItems($data, $billingPeriodsResult);
        $this->data = $saveData;
    }

    public function getAllTaxClasses($data)
    {
        // Fetch the authenticated user's society ID
        $socId = $data['company_id'] ?? null;
        $status = $data['status'] ?? null;

        // Validate required fields
        if (!$socId) {
            // <PERSON>le missing required data scenario
            return [];
        }

        // Build the query using Eloquent
        $query = $this->tenantDB()->table('chsone_tax_classes')->where('soc_id', $socId);

        // Add status condition if provided
        if (!is_null($status)) {
            $query->where('status', $status);
        }

        // Fetch results ordered by tax_class_id descending
        $taxClasses = $query->orderBy('tax_class_id', 'desc')->get();

        // Convert results to array
        return $taxClasses->toArray();
    }

    public function getTaxClassSelect(array $data = [])
    {
        // Initialize the array with a default option
        $arrTaxClass = ['' => 'Select Tax Class'];

        // Check if 'arrTaxClass' is not empty and iterate over the classes
        if (!empty($data['arrTaxClass'])) {
            // Use Laravel's collection map function if you're working with collections
            foreach ($data['arrTaxClass'] as $eachTaxClass) {
                // Format the tax class name using ucwords and add it to the array
                $arrTaxClass[$eachTaxClass->tax_class_id] = ucwords($eachTaxClass->tax_class_name);
            }
        }

        return $arrTaxClass;
    }

    public function getOnetimeParticulars(array $data = [])
    {
        // Extract society ID from the provided data
        $socId = $data['auth']['soc_id'];
        $effectiveDate = now()->format('Y-m-d'); // Get the current date in Y-m-d format
        // Fetch the common area rent details for onetime particulars
        $recordsOnetime = $this->getCommonAreaRent($socId, $effectiveDate, 'onetime');

        return $recordsOnetime;
    }

    public function getCommonAreaRent($socId, $effectiveDate, $duration = 'onetime')
    {
        // Define the query for retrieving currently applicable records
        $currentlyApplicableRecords = $this->tenantDB()->table('income_common_area_charges')
            ->selectRaw('MAX(effective_date) as effective_date, particular')
            ->where('soc_id', $socId)
            ->where('effective_date', '<=', $effectiveDate)
            ->groupBy('particular')
            ->orderBy('updated_date', 'DESC')
            ->get();

        $selectedRecords = [];

        // Prepare selected records conditions
        foreach ($currentlyApplicableRecords as $record) {
            $selectedRecords[] = "(effective_date = '{$record->effective_date}' AND particular = '{$record->particular}')";
        }

        $records = [];

        // If there are selected records, query to fetch the final records based on conditions
        if (!empty($selectedRecords)) {
            $conditions = implode(' OR ', $selectedRecords);

            $records = $this->tenantDB()->table('income_common_area_charges')
                ->where('soc_id', $socId)
                ->whereRaw($conditions)
                ->orderBy('updated_date', 'DESC')
                ->get()
                ->toArray();
        }

        return $records;
    }

    public function generateInvoiceFrequency($incomeInvoiceSetting, $start, $end)
    {
        $invoiceFreqArr = [];

        // Convert $start and $end to Carbon instances if they are strings
        if (is_string($start)) {
            $start = Carbon::parse($start); // Converts string to Carbon instance
        } else {
            $start = Carbon::instance($start);
        }

        if (is_string($end)) {
            $end = Carbon::parse($end); // Converts string to Carbon instance
        } else {
            $end = Carbon::instance($end);
        }

        switch ($incomeInvoiceSetting->invoicing_frequency) {
            case 'Monthly':
                $interval = new \DateInterval('P1M'); // 1 month interval
                $period = new \DatePeriod($start, $interval, $end);

                foreach ($period as $dt) {
                    $carbonDate = Carbon::instance($dt);
                    $invoiceFreqArr[$carbonDate->format("F-Y")] = $carbonDate->format("F Y");
                }
                break;

            case 'Quarterly':
                $interval = new \DateInterval('P3M'); // 3 months interval
                $period = new \DatePeriod($start, $interval, $end);

                foreach ($period as $dt) {
                    $carbonDate = Carbon::instance($dt);
                    $invoiceFreqArr[$carbonDate->format("F-Y")] = $carbonDate->format("F Y") . " - " . $carbonDate->copy()->addMonths(2)->format("F Y");
                }
                break;

            case 'Half_yearly':
                $interval = new \DateInterval('P6M'); // 6 months interval
                $period = new \DatePeriod($start, $interval, $end);

                foreach ($period as $dt) {
                    $carbonDate = Carbon::instance($dt);
                    $invoiceFreqArr[$carbonDate->format("F-Y")] = $carbonDate->format("F Y") . " - " . $carbonDate->copy()->addMonths(5)->format("F Y");
                }
                break;

            case 'Yearly':
                $interval = new \DateInterval('P1Y'); // 1 year interval
                $period = new \DatePeriod($start, $interval, $end);

                foreach ($period as $dt) {
                    $carbonDate = Carbon::instance($dt);
                    $startYear = $carbonDate->format("Y");
                    $endYear = $carbonDate->copy()->addMonths(11)->format("Y");
                    $key = $startYear . "-" . $endYear;
                    $value = $startYear . " - " . $endYear;
                    $invoiceFreqArr[$key] = $value;
                }
                break;
        }

        return $invoiceFreqArr;
    }

    public function addBillableItems($data, $billingPeriodsResult)
    {
        $arrPost = $data;

        // $postedIds = $data['billing_period'];              // e.g. [1]
        // $firstId   = $postedIds[0] ?? null;                // 1

        // // find the matching entry in $this->data
        // $billing_period = null;
        // foreach ($billingPeriodsResult as $item) {
        //     if ($item['id'] === (int) $firstId) {
        //         $billing_period = $item;
        //         break;
        //     }
        // }

        $postedIds = $data['billing_period'] ?? [];  // Example: [94] or [94, 95]

        $billing_period = [];

        if (!empty($postedIds) && is_array($postedIds)) {
            foreach ($billingPeriodsResult as $item) {
                if (in_array((int) $item['id'], $postedIds, true)) {
                    // Assuming 'name' holds the label like 'Feb-2027'
                    $billing_period[] = $item;
                }
            }
        }

        if (! $billing_period) {
            throw new \Exception("Unknown billing period IDs: " . implode(',', $postedIds));
        }

        // $dates = explode('-', $billing_period['period']);
        $dates = array_column($billing_period, 'period');

        // Determine income account ID based on debit note type
        if ($arrPost['debit_note_type'] == 'dpc_debit_note') {
            $conditionDate = now()->format('Y-m-d');
            $objLatePaymentDetails = $this->tenantDB()->table('income_late_payment_charges')->where('type', 'maintenance')
                ->where('soc_id', $data['company_id'])
                ->where('effective_date', '<=', $conditionDate)
                ->orderBy('effective_date', 'desc')
                ->first();

            $objLatePaymentDetails = json_decode(json_encode($objLatePaymentDetails), true);

            // Check if the object is not empty, then convert it to an array; otherwise, set it to an empty array
            $arrLatePaymentdetails = $objLatePaymentDetails ? $objLatePaymentDetails->toArray() : [];


            $arrPost['income_account_id'] = $arrLatePaymentdetails->id ?? null;
        }

        if (isset($arrPost['note']) && !empty($arrPost['note'])) {
            $note = $arrPost['note'];
        } else {
            $note = ($arrPost['debit_note_type'] == 'debit_note') ? (ucfirst(str_replace("_", " ", $arrPost['debit_note_type']))) . ' for ' . $arrPost['particular_name'] :
                ucfirst(str_replace("_", " ", $arrPost['debit_note_type']));
        }

        if ($arrPost['billable_item_for'] == 'all') {
            $arrUnitMemberDetails = $this->getUnitMemberDetails($data['company_id'], $searchstring = '', $building_id = '', $floor_id = '', $is_allotted = 1, $unitwise = '', $unittype = '', $memberType = 'Primary');

            $arrUnitMemberDetails = json_decode(json_encode($arrUnitMemberDetails), true);
            $unitMemberId = [];
            foreach ($arrUnitMemberDetails as $key => $value) {
                $unitMemberId[$value['unit_id']] = $value['unit_id'];
            }
            $unitMemArr = $unitMemberId;
        } else {
            if (isset($arrPost['unit_id'])) {
                $unitIds = is_array($arrPost['unit_id'])
                    ? $arrPost['unit_id']
                    : explode(',', (string) $arrPost['unit_id']);
            } else {
                $unitIds = [];
            }

            // Normalize member IDs into an array
            if (isset($arrPost['member_id'])) {
                $memberIds = is_array($arrPost['member_id'])
                    ? $arrPost['member_id']
                    : explode(',', (string) $arrPost['member_id']);
            } else {
                $memberIds = [];
            }

            $unitIds = array_filter($unitIds, fn($v) => $v !== '');
            $memberIds = array_filter($memberIds, fn($v) => $v !== '');

            if (count($unitIds) === count($memberIds)) {
                $unitMemArr = array_combine($unitIds, $memberIds);
            } else {
                // Handle mismatch: choose fallback behavior
                $unitMemArr = [];
                trigger_error('unit_id and member_id count mismatch', E_USER_WARNING);
            }

            // $unitMemArr = array_combine(explode(",", $arrPost['unit_id']), explode(",", $arrPost['member_id']));
        }
        $arrMessage = [];
        $savedUnitId = [];
               $this->tenantDB()->beginTransaction();

        try {
            $savedUnitId = [];
            $arrMessage = [];
            foreach ($unitMemArr as $unitId => $memberId) {
                foreach ($dates as $dt) {
                    // Get the start and end dates based on the billing period
                    $billingDates = $this->getBillingPeriodDates($dt);
                    $startDt = $billingDates['start_date'];
                    $endDt = $billingDates['end_date'];

                    // Create the debit note data array
                    $debitNote = [
                        'soc_id' => $data['company_id'],
                        'unit_id' => $unitId,
                        'member_id' => $memberId,
                        'income_account_id' => $data['income_account_id'],
                        'debit_note_type' => $data['debit_note_type'] ?? "debit_note",
                        'note' => $note,
                        'amount' => $data['amount'],
                        'from_date' => $startDt,
                        'to_date' => $endDt,
                        'expense_id' => $data['expense_id'] ?? null,
                        'status' => 1,
                        'is_reversal' => 0,
                        "invoice_number" => $data['invoice_number'] ?? null,
                        'created_date' => date('Y-m-d H:i:s'),
                        'created_by' => $this->input['user_id'] ?? 0,
                        'updated_date' => date('Y-m-d H:i:s'),
                        'updated_by' => $this->input['user_id'] ?? 0,
                        'due_date' =>  $data['due_date'] ?? null,
                        'apply_late_payment_interest' => $data['apply_late_payment_interest'] ?? 0,
                    ];

                    // Insert the debit note into the database and get the ID
                    $result = $this->tenantDB()->table('income_billable_item')->insertGetId($debitNote);

                    // Check if the insert was successful
                    if ($result) {
                        $savedUnitId[] = $unitId;
                        $savedDrItem[] = $result;
                    } else {
                        $failedUnitId[] = $unitId;
                        // Capture the error message for debugging
                        $arrMessage[] = "Failed to insert debit note for unit ID: $unitId.";
                    }
                }
            }

            // Commit or rollback the transaction based on the results
            if (empty($savedUnitId)) {
                // Roll back the transaction if no units were saved
                $this->tenantDB()->rollBack();
                $arrMessage[] = "No debit notes were saved, transaction rolled back.";
            } else {
                // Update vendor bills and commit the transaction
                // $this->updateVendorBills($arrPost, $arrMessage);
                $this->tenantDB()->commit();
            }
        } catch (\Exception $e) {
            // Roll back the transaction and log the error message
            $this->tenantDB()->rollBack();
            $arrMessage[] = "Exception occurred: " . $e->getMessage();
            // Optionally log the exception stack trace for debugging
            // Log::error($e->getTraceAsString());
        }

        // Check the result of the operation and log or handle messages
        if (!empty($arrMessage)) {
            foreach ($arrMessage as $message) {
                // Log each message or handle it as needed
                // Log::error($message);
                echo $message; // This is just for demonstration; adjust based on your actual logging mechanism.
            }
        }


        if (!empty($arrPost['amountAct']) && !empty($arrPost['expense_id'])) {
            // Explode the expense_id string into an array of IDs
            $expenseIds = explode(',', $arrPost['expense_id']);

            foreach ($expenseIds as $expenseId) {
                // Check if the expense_id is not empty
                if (!empty($expenseId)) {
                    // Find the Vendor Bill by its ID
                    $vendorBill = $this->tenantDB()->table('chsone_vendor_bill_master')->where('vendor_bill_id', $expenseId);

                    // If the Vendor Bill is found, update its status
                    if ($vendorBill) {

                        $this->tenantDB()->table('chsone_vendor_bill_master')->where('vendor_bill_id', $expenseId)->update(['is_billed' => 1]);
                        // Save the changes and commit if successful

                    }
                }
            }
        }


        // Prepare final response array
        return $result;
    }

    public function getUnitMemberArray($arrPost, $auth)
    {
        if ($arrPost['billable_item_for'] == 'all') {
            $arrUnitMemberDetails = $this->getUnitMemberDetails($auth, '', '', '', 1, '', '', 'Primary');

            return collect($arrUnitMemberDetails)->mapWithKeys(function ($value) {
                return [$value['unit_id'] => $value['unit_id']];
            })->toArray();
        } else {
            return array_combine(explode(",", $arrPost['unit_id']), explode(",", $arrPost['member_id']));
        }
    }

    public function updateVendorBills($arrPost, &$arrMessage)
    {
        if ($arrPost['amountAct']) {
            foreach (explode(',', string: $arrPost['expense_id']) as $expense_id) {
                if ($expense_id != '') {
                    $vendorBill = $this->tenantDB()->table('chsone_vendor_bill_master')->where('vendor_bill_id', $expense_id);
                    if ($vendorBill) {
                        $this->tenantDB()->table('chsone_vendor_bill_master')->where('vendor_bill_id', $expense_id)->update(['is_billed' => 1]);
                        // if (!$vendorBill->update()) {
                        //     foreach ($vendorBill->getMessages() as $messages) {
                        //         $arrMessage['expense_bill_update_error'] = (string) $messages;
                        //     }
                        // }
                    }
                }
            }
        }
    }

    public function getUnitMemberDetails($company_id, $searchString = '', $buildingId = '', $floorId = '', $isAllotted = '', $unitwise = '', $unitType = '', $memberType = '', $unitId = '')
    {
        // Initialize the query builder with the necessary joins and conditions
        $queryBuilder = $this->tenantDB()->table('chsone_units_master as units')
            ->select([
                'units.soc_id',
                'units.unit_id',
                'units.unit_flat_number',
                'units.soc_building_floor',
                'units.soc_building_id',
                'units.soc_building_name',
                'memmaster.id',
                'memmaster.member_type_id',
                'memmaster.member_first_name',
                'memmaster.member_last_name',
            ])
            ->leftJoin('chsone_members_master as memmaster', 'units.unit_id', '=', 'memmaster.fk_unit_id')
            ->where('units.soc_id', $company_id)
            ->where('units.status', 1)
            ->where(function ($query) {
                $query->where('units.cancel_date', '0000-00-00')
                    ->orWhere('units.cancel_date', '>', now());
            });

        // Additional conditions based on parameters
        if (!empty($isAllotted)) {
            $queryBuilder->where('units.is_allotted', $isAllotted);
        }

        // Handling search string with building and flat number
        if (strpos($searchString, '-') !== false) {
            [$buildingName, $flatNumber] = explode('-', $searchString);
            if (!empty($flatNumber)) {
                $queryBuilder->where('units.unit_flat_number', 'LIKE', '%' . $flatNumber . '%');
            }
            if (!empty($buildingName)) {
                $queryBuilder->where('units.soc_building_name', 'LIKE', '%' . $buildingName . '%');
            }
        } else {
            $queryBuilder->where('units.unit_flat_number', 'LIKE', '%' . $searchString . '%');
        }

        // Filter by unit type if specified
        if ($unitType === 'Parking') {
            $queryBuilder->where('units.unit_type', '!=', $unitType);
        }

        // Filter by building and floor if specified
        if (!empty($buildingId)) {
            $queryBuilder->where('units.soc_building_id', $buildingId);
        }
        if (!empty($floorId)) {
            $queryBuilder->where('units.soc_building_floor', 'LIKE', $floorId);
        }

        // Filter by member status and type
        $queryBuilder->where('memmaster.status', 1);

        if (!empty($memberType)) {
            $queryBuilder->leftJoin('chsone_member_type_master as memType', 'memmaster.member_type_id', '=', 'memType.member_type_id')
                ->where('memType.member_type_name', 'primary');
        }

        // Group results if unitwise flag is set
        if ($unitwise == 1) {
            $queryBuilder->groupBy('units.unit_id');
        }

        // Filter by specific unit ID if provided
        if (!empty($unitId)) {
            $queryBuilder->where('units.unit_id', $unitId);
        }

        // Order by building, floor, and flat number
        $queryBuilder->orderBy('units.soc_building_id')
            ->orderBy('units.soc_building_floor')
            ->orderBy('units.unit_flat_number');

        // Execute the query and return results as an array
        return $queryBuilder->get()->toArray();
    }

    public function getDatesForBillable($billableData)
    {
        $invoiceFreqArr = [];
        $dt = [];

        switch ($billableData['selected_inv_freq']) {
            case 'Monthly':
                foreach ($billableData['invoice_frequency'] as $v) {
                    $monthYear = explode("-", $v);
                    $dtim = Carbon::createFromFormat('F-Y', $monthYear[0] . '-' . $monthYear[1])->day(21);
                    $start = $dtim->copy()->startOfMonth()->format('Y-m-d');
                    $end = $dtim->copy()->endOfMonth()->format('Y-m-d');
                    $dt[] = $start . ' ' . $end;
                    $invoiceFreqArr['date'] = $dt;
                }
                break;

            case 'Quarterly':
                foreach ($billableData['invoice_frequency'] as $v) {
                    $monthYear = explode("-", $v);
                    $dtim = Carbon::createFromFormat('F-Y', $monthYear[0] . '-' . $monthYear[1])->day(21);
                    $start = $dtim->copy()->startOfMonth()->format('Y-m-d');
                    $end = $dtim->copy()->addMonths(2)->endOfMonth()->format('Y-m-d');
                    $dt[] = $start . ' ' . $end;
                    $invoiceFreqArr['date'] = $dt;
                }
                break;

            case 'Half_yearly':
                foreach ($billableData['invoice_frequency'] as $v) {
                    $monthYear = explode("-", $v);
                    $dtim = Carbon::createFromFormat('F-Y', $monthYear[0] . '-' . $monthYear[1])->day(21);
                    $start = $dtim->copy()->startOfMonth()->format('Y-m-d');
                    $end = $dtim->copy()->addMonths(5)->endOfMonth()->format('Y-m-d');
                    $dt[] = $start . ' ' . $end;
                    $invoiceFreqArr['date'] = $dt;
                }
                break;

            case 'Yearly':
                foreach ($billableData['invoice_frequency'] as $v) {
                    $yearRange = explode("-", $v);
                    $start = Carbon::createFromFormat('Y', $yearRange[0])->month(4)->startOfMonth()->format('Y-m-d'); // April of start year
                    $end = Carbon::createFromFormat('Y', $yearRange[1])->month(3)->endOfMonth()->format('Y-m-d'); // March of end year
                    $dt[] = $start . ' ' . $end;
                    $invoiceFreqArr['date'] = $dt;
                }
                break;
        }

        return $invoiceFreqArr['date'];
    }

    function getBillingPeriodDates($billingPeriod)
    {
        // Start date remains the same
        $startDate = date("Y-m-01", strtotime($billingPeriod));

        // End date is the last day of the month of the given date
        $endDate = date("Y-m-t", strtotime($billingPeriod)); // 't' gives the last day of the month

        return [
            'start_date' => $startDate,
            'end_date' => $endDate,
        ];
    }
}
