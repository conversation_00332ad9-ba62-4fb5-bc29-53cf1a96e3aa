<?php

namespace App\Console\Commands\Actions\Income;

use Illuminate\Console\Command;
use App\Console\Commands\Action;
use App\Models\Tenants\ChsoneCreditNote;
use App\Models\Tenants\ChsoneGrpLedgerTree;
use App\Models\Tenants\IncomeInvoiceGeneralSetting;
use App\Models\Tenants\IncomeUnitInvoice;

class IncomeNonMemberDetailsDataSource extends Action
{
    protected $signature = 'datasource:IncomeNonMemberDetails {flowId} {parentId} {input}';

    protected $description = 'Get the Non Member bill details';

    public function apply()
    {
        //$id is a unit id
        $id = $this->input['id'];

        // check if unit id is valid or not from chsone_units_master table
        $unit = $this->tenantDB()->table('chsone_units_master')
            ->where('unit_id', $id)
            ->get();

        if(count($unit) == 0) {
            $this->status = 'error';
            $this->message = 'Please provide a valid unit id';
            $this->statusCode = 404;
            $this->data = [];
            return;
        }
        $invoice_number = $this->input['invoice_number'];
        // according to this invoice number need to find out the id from income_common_billing_charges table
        if ($invoice_number) {
            $obj = $this->tenantDB()->table('income_nonmember_bills as inb')
                ->select('*')
                ->where('inb.bill_number', $invoice_number)
                ->first();
            if ($obj) {
                $obj = $this->tenantDB()->table('income_nonmember_bills as inb')
                    ->select(
                        'inb.*',
                        'unitInvoice.principal_amount',
                        'unitInvoice.interest_amount',
                        'unitInvoice.outstanding_principal',
                        'unitInvoice.outstanding_interest',
                        'unitInvoice.roundoff_amount'
                    )
                    ->leftJoin('chsone_nonmember_master as memmaster', 'inb.nonmember_id', '=', 'memmaster.nonmember_id')
                    ->leftJoin('income_unit_invoices as unitInvoice', 'inb.bill_number', '=', 'unitInvoice.invoice_number')
                    ->where('inb.bill_number', $invoice_number)
                    ->orderBy('inb.nonmember_id', 'DESC');
                    $result = $obj->first();

                // need to fecth the data from chsone_taxlog table according to $result->invoice_number
                /*$taxLog = $this->tenantDB()->table('chsone_tax_log')
                    ->select('tax_amount','tax_class_id','tax_rule','tax_rule_id','tax_rate','tax_rate_type')
                    ->where('invoice_number', $result->invoice_number)
                    ->get();

                $taxLog = $taxLog->toArray();
                $result->taxLog = $taxLog;

                // first check if the taxLog is empty or not
                if (!empty($taxLog)) {
                    // need to fecth gst number from chsone_tax_classesm table according to $result->tax_class_id
                    $gstNumber = $this->tenantDB()->table('chsone_tax_classes')
                    ->select('tax_categories_footer')
                    ->where('tax_class_id', $taxLog[0]->tax_class_id)
                    ->first();

                    if($gstNumber) {
                        $result->gstNumber = $gstNumber->tax_categories_footer;
                    } else {
                        $result->gstNumber = '';
                    }
                } else {
                    $result->gstNumber = '';
                }*/

                // Credit Note Adjustment Data
                $arrCreditNoteText = array();
                $objCreditNote = IncomeUnitInvoice::where('invoice_number', $invoice_number)->first();
                $result = json_decode(json_encode($result), true);
                $result['creditNoteAdjustment'] = $objCreditNote;

                // add general settings data
                $arrGeneralSettingData = array('soc_id' => $this->input['company_id'], 'setting_key' => array('INCOME_PAYMENT_INSTRUCTION', 'INCOME_SHOW_INTEREST_BREAKUP', 'INVOICE_LOGO', 'PAYMENT_TRANSACTION_RECEIPT_LAST_PERIOD', 'SHOW_SOCIETY_SIGNATURE', 'SHOW_CHSONE_FOOTER', 'SHOW_BANK_DETAIL'));
                $arrInvoiceGeneralSetting = $this->getInvoiceGeneralSetting($arrGeneralSettingData);

                if (!empty($arrInvoiceGeneralSetting)) {
                    foreach ($arrInvoiceGeneralSetting as $key => $eachGeneralSetting) {
                        if ($eachGeneralSetting['setting_key'] == 'INCOME_PAYMENT_INSTRUCTION') {
                            $result['arrInvoiceGeneralNote'] = explode(PHP_EOL, $eachGeneralSetting['setting_value']);
                        }
                        if (strtolower($eachGeneralSetting['setting_key']) == 'income_show_interest_breakup' && $eachGeneralSetting['setting_value'] == 1) {
                            $result['showInterestBreakup'] = 1;
                        } else {
                            $result['showInterestBreakup'] = 0;
                        }
                        if (strtolower($eachGeneralSetting['setting_key']) == 'show_society_signature' && strtolower($eachGeneralSetting['setting_value']) == 'yes') {
                            $result['showSocietySignature'] = true;
                        } else {
                            $result['showSocietySignature'] = false;
                        }
                        if (strtolower($eachGeneralSetting['setting_key']) == 'show_chsone_footer' && strtolower($eachGeneralSetting['setting_value']) == 'yes') {
                            $result['showChsoneFooter'] = true;
                        } else {
                            $result['showChsoneFooter'] = false;
                        }
                        if (strtolower($eachGeneralSetting['setting_key']) == 'show_bank_detail' && strtolower($eachGeneralSetting['setting_value']) == 'yes') {
                            $result['showBankDetail'] = 1;
                        } else {
                            $result['showBankDetail'] = 0;
                        }
                    }
                }

                // get bank account details
                $arrAccountDetail = $this->getDefaultBankAccountDetailIncidental(array("soc_id" => $this->input["company_id"]));
                if (empty($arrAccountDetail)) {
                    $arrAccountDetail = $this->getDefaultBankAccountDetail(array("soc_id" => $this->input["company_id"]));
                }
                $result['arrBankDetail'] = $arrAccountDetail;
                $this->data = $result;
            } else {
                $this->status = false;
                $this->statusCode = 400;
                $this->message = "No data found for this invoice number";
                $this->data = [];
                return;
            }
        } else {
            $this->status = false;
            $this->statusCode = 400;
            $this->message = "Please provide a invoice number";
            $this->data = [];
            return;
        }
    }

    public function getInvoiceGeneralSetting($data = [])
    {
        $arrInvoiceGeneralSetting = [];

        // Ensure the required keys are present in the input data
        if (!empty($data['soc_id']) && !empty($data['setting_key']) && is_array($data['setting_key'])) {
            $objInvoiceGeneralSetting = IncomeInvoiceGeneralSetting::where('soc_id', $data['soc_id'])
                ->whereIn('setting_key', $data['setting_key'])
                ->get();

            if (!empty($objInvoiceGeneralSetting)) {
                $arrInvoiceGeneralSetting = $objInvoiceGeneralSetting->toArray();
            }
        }

        return $arrInvoiceGeneralSetting;
    }

    public function getDefaultBankAccountDetailIncidental($data = [])
    {
        $arrAccountDetail = [];

        if (!empty($data['soc_id'])) {
            $resultset = $this->tenantDB()->table('chsone_grp_ledger_tree as grpLedger')
            ->select([
                'grpLedger.ledger_account_id',
                'grpLedger.ledger_account_name',
                'grpLedger.context',
                'account.account_name',
                'account.default_account',
                'account.bank_name',
                'account.account_number',
                'account.bank_address',
                'account.branch',
                'account.bank_city',
                'account.bank_ifsc',
                'account.account_id'
            ])
            ->where('grpLedger.soc_id', $data['soc_id'])
            ->where('grpLedger.entity_type', 'ledger')
            ->where('grpLedger.status', 1)
            ->where('account.default_bank_for_incidental', 1)
            ->whereIn('grpLedger.context', ['bank'])
            ->leftJoin('chsone_accounts_master as account', 'grpLedger.ledger_account_id', '=', 'account.ledger_account_id')
            ->first();

            if ($resultset) {
                $arrAccountDetail = (array) $resultset; // Convert the object to an associative array
            }
        }
        return $arrAccountDetail;
    }

    public function getDefaultBankAccountDetail($data = [])
    {
        $arrAccountDetail = [];

        // Build the query using Laravel's Query Builder
        try {
                $query = $this->tenantDB()->table('chsone_grp_ledger_tree as grpLedger')
                ->select([
                    'grpLedger.ledger_account_id',
                    'grpLedger.ledger_account_name',
                    'grpLedger.context',
                    'account.account_name',
                    'account.default_account',
                    'account.bank_name',
                    'account.account_number',
                    'account.bank_address',
                    'account.branch',
                    'account.bank_city',
                    'account.bank_ifsc',
                    'account.account_id',
                    'account.account_name'
                ])
                ->where('grpLedger.soc_id', $data['soc_id'])
                ->where('grpLedger.entity_type', 'ledger')
                ->where('grpLedger.status', 1)
                ->where('account.default_account', 1)
                ->whereIn('grpLedger.context', ['bank'])
                ->leftJoin('chsone_accounts_master as account', 'grpLedger.ledger_account_id', '=', 'account.ledger_account_id');

            // Execute the query and get the first result
            $resultset = $query->first();

            if (!empty($resultset)) {
                $arrAccountDetail = (array) $resultset; // Convert the object to an associative array
            }

            return $arrAccountDetail;
        } catch (\Exception $e) {
            // Log the error
            throw new \Exception("Error in getDefaultBankAccountDetail: " . $e->getMessage());
        }
    }
}
