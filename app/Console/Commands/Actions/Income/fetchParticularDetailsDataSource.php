<?php

namespace App\Console\Commands\Actions\Income;

use Illuminate\Console\Command;
use App\Console\Commands\Action;

class fetchParticularDetailsDataSource extends Action
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'datasource:fetchParticularDetails {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Fetch particular details data source.';

    /**
     * Execute the console command.
     */
    public function apply()
    {
        $obj = $this->tenantDB()->table('income_common_area_charges')
        ->select(
            'particular',
            'rate as particular_amount',
            'id as particular_id',
        )
        ->where('id', $this->input['billing_type'])
        ->first();

        $this->data = $obj;
    }
}
