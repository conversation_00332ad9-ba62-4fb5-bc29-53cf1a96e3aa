<?php

namespace App\Console\Commands\Actions\Income;

use Illuminate\Console\Command;
use App\Console\Commands\Action;
use Illuminate\Support\Facades\DB;

class fetchDataForAddCommonBillDataSource extends Action
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'datasource:fetchDataForAddCommonBill {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Fetch data for add common bill data source.';

    /**
     * Execute the console command.
     */
    public function apply()
    {
        $obj = $this->tenantDB()->table('income_common_billing_charges as icb')
        ->select(
            // 'icb.id',
            // 'icb.soc_id',
            // 'icb.fk_member_id',
            // 'icb.fk_unit_id as unit_id',
            // 'icb.invoice_number',
            // 'icb.billing_type',
            'icb.from_date',
            'icb.to_date',
            'icb.due_date',
            'icb.bill_date',
            'units.soc_building_name',
            'units.unit_flat_number',
            'members.member_first_name',
            'members.member_last_name',
            'members.gstin',
            DB::raw("CONCAT(units.soc_building_name, '-', units.unit_flat_number) as flat_number"),
            DB::raw("CONCAT(members.member_first_name, ' ', members.member_last_name) as member_name")
        )
        ->leftJoin('chsone_units_master as units', 'icb.fk_unit_id', '=', 'units.unit_id')
        ->leftJoin('chsone_members_master as members', 'icb.fk_member_id', '=', 'members.id')
        ->where('icb.fk_unit_id', '=', $this->input['unit_id'])
        // ->whereIn('icb.payment_status', ['unpaid', 'partialpaid'])
        ->where('icb.billing_type', '=', $this->input['billing_type'])
        ->orderBy('icb.id', 'desc');

        $result = $obj->first();

        // $this->data = $this->format($result->toArray());
        $this->data = $result;

    }
}
