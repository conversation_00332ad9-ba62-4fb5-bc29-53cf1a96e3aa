<?php

namespace App\Console\Commands\Actions\Income;

use Illuminate\Console\Command;
use App\Console\Commands\Action;
use App\Models\Tenants\ChsoneGrpLedgerTree;
use App\Models\Tenants\ChsoneLedgerTransaction;
use App\Models\Tenants\ChsoneUnitsMaster;

class InsertIncidentalReceiptInChsoneLedgerTransactionsDataSource extends Action
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'datasource:InsertIncidentalReceiptInChsoneLedgerTransactions {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Insert Incidental Receipt In Chsone Ledger Transactions DataSource';

    /**
     * Execute the console command.
     */
    public function apply()
    {
        $data = $this->input;

        $data['receipt_note'] = $data['receipt_note'] ?? '';

        // fetch ledger account id and name from chsone_grp_ledger_tree table based on the payment mode and entity type
        if($data['receipt_mode'] == 'cash'){
            $paymentmodeLedgerId = ChsoneGrpLedgerTree::where('entity_type', 'ledger')->where('context', 'cash')->first();
            $from_ledger_id = $paymentmodeLedgerId->ledger_account_id ?? '';
            $from_ldger_name = $paymentmodeLedgerId->ledger_account_name ?? '';
        } elseif($data['receipt_mode'] == 'cheque' || $data['receipt_mode'] == 'cashtransfer') {
            $paymentmodeLedgerId = ChsoneGrpLedgerTree::where('entity_type', 'ledger')->where('context', 'bank')->where('ledger_account_id', $data['bank_account'])->first();
            $from_ledger_id = $paymentmodeLedgerId->ledger_account_id ?? '';
            $from_ldger_name = $paymentmodeLedgerId->ledger_account_name ?? '';
        }

        // fetch ledger account id and name from chsone_grp_ledger_tree table based on the unit id
        $obj = $this->tenantDB()->table('chsone_units_master as units')
        ->select('ledger.ledger_account_id', 'ledger.ledger_account_name')
        ->leftJoin('chsone_grp_ledger_tree as ledger', 'ledger.ledger_account_id', '=', 'units.ledger_account_id')
        ->where('units.unit_id', '=', $data['unit_id']);

        $result = $obj->first();
        $to_ledger_id = $result->ledger_account_id ?? '';
        $to_ldger_name = $result->ledger_account_name ?? '';

        // insert the row in chsone_ledger_transactions table
        $chsoneLedgerTransaction = new ChsoneLedgerTransaction;
        $chsoneLedgerTransaction->soc_id = $data['company_id'] ?? '';
        $chsoneLedgerTransaction->transaction_date = $data['receipt_date'] ?? '';
        $chsoneLedgerTransaction->ledger_account_id = $from_ledger_id;
        $chsoneLedgerTransaction->ledger_account_name = $from_ldger_name;
        $chsoneLedgerTransaction->voucher_type = 'receipt';
        $chsoneLedgerTransaction->voucher_reference_number = $data['receipt_number'] ?? '';
        $chsoneLedgerTransaction->voucher_reference_id = $data['receipt_id'] ?? '';
        $chsoneLedgerTransaction->transaction_type = 'dr';
        $chsoneLedgerTransaction->payment_mode = $data['receipt_mode'] ?? '';
        // $chsoneLedgerTransaction->payment_reference = $data['transaction_reference'] ?? '';
        if($data['receipt_mode'] == 'cheque') {
            $chsoneLedgerTransaction->payment_reference = $data['cheque_number'] ?? '';
        } elseif($data['receipt_mode'] == 'cashtransfer') {
            $chsoneLedgerTransaction->payment_reference = $data['transaction_reference'] ?? '';
        } else {
            $chsoneLedgerTransaction->payment_reference = '';
        }
        $chsoneLedgerTransaction->transaction_amount = $data['invoice_amount'] ?? '';
        $chsoneLedgerTransaction->txn_from_id = 0;
        $chsoneLedgerTransaction->memo_desc = '(Receipt No-'. $data['receipt_number'] .') Amount received from '. $data['received_from'] .' against invoice '. $data['invoice_number'] .' paid on '. $data['receipt_date'].' through cash ['.$data['receipt_note'].']';
        $chsoneLedgerTransaction->is_opening_balance = 0;
        $chsoneLedgerTransaction->is_reconciled = 0;
        $chsoneLedgerTransaction->is_cancelled = 0;
        $chsoneLedgerTransaction->created_by = $data['user_id'] ?? '0';
        $chsoneLedgerTransaction->added_on = date('Y-m-d H:i:s');
        $chsoneLedgerTransaction->save();

        if($chsoneLedgerTransaction->save()) {

            $last_id = $this->tenantDB()->table('chsone_ledger_transactions')->orderBy('txn_id', 'desc')->first();

            $secondChsoneLedgerTransaction = new ChsoneLedgerTransaction;
            $secondChsoneLedgerTransaction->soc_id = $data['company_id'] ?? '';
            $secondChsoneLedgerTransaction->transaction_date = $data['receipt_date'] ?? '';
            $secondChsoneLedgerTransaction->ledger_account_id = $to_ledger_id;
            $secondChsoneLedgerTransaction->ledger_account_name = $to_ldger_name;
            $secondChsoneLedgerTransaction->voucher_type = 'receipt';
            $secondChsoneLedgerTransaction->voucher_reference_number = $data['receipt_number'] ?? '';
            $secondChsoneLedgerTransaction->voucher_reference_id = $data['receipt_id'] ?? '';
            $secondChsoneLedgerTransaction->transaction_type = 'cr';
            $secondChsoneLedgerTransaction->payment_mode = $data['receipt_mode'] ?? '';
            // $secondChsoneLedgerTransaction->payment_reference = $data['transaction_reference'] ?? '';
            if($data['receipt_mode'] == 'cheque') {
                $secondChsoneLedgerTransaction->payment_reference = $data['cheque_number'] ?? '';
            } elseif($data['receipt_mode'] == 'cashtransfer') {
                $secondChsoneLedgerTransaction->payment_reference = $data['transaction_reference'] ?? '';
            } else {
                $secondChsoneLedgerTransaction->payment_reference = '';
            }
            $secondChsoneLedgerTransaction->transaction_amount = $data['invoice_amount'] ?? '';
            $secondChsoneLedgerTransaction->txn_from_id = $last_id->txn_id;
            $secondChsoneLedgerTransaction->memo_desc = '(Receipt No-'. $data['receipt_number'] .') Amount received from '. $data['received_from'] .' against invoice '. $data['invoice_number'] .' paid on '. $data['receipt_date'].' through cash ['.$data['receipt_note'].']';
            $secondChsoneLedgerTransaction->is_opening_balance = 0;
            $secondChsoneLedgerTransaction->is_reconciled = 0;
            $secondChsoneLedgerTransaction->is_cancelled = 0;
            $secondChsoneLedgerTransaction->created_by = $data['user_id'] ?? '0';
            $secondChsoneLedgerTransaction->added_on = date('Y-m-d H:i:s');
            $secondChsoneLedgerTransaction->save();

            if($secondChsoneLedgerTransaction->save()) {

                // check if tds_amount is set then insert record in the chsone_ledger_transactions table
                if(isset($data['tds_amount']) && !empty($data['tds_amount'])) {
                    $insertTdsLedgerTransaction = $this->insertTdsLedgerTransaction($data);

                    if(!$insertTdsLedgerTransaction) {
                        $this->message = "Unable to insert tds ledger transaction";
                        $this->status = 'failed';
                        $this->statusCode = 400;
                    }
                }

                $this->message = "Incidental Ledger Transactions created successfully";
                $this->status = 'success';
                $this->statusCode = 200;
            } else {
                $this->message = "Unable to create Incidental Ledger Transactions";
                $this->status = 'error';
                $this->statusCode = 400;
            }
        } else {
            $this->message = "Unable to create Incidental Ledger Transactions";
            $this->status = 'error';
            $this->statusCode = 400;
        }
    }

    public function insertTdsLedgerTransaction($data) {

        // fetch ledger account id and name from chsone_grp_ledger_tree table based on the payment mode and entity type
        if($data['payment_mode'] == 'cash' || $data['payment_mode'] == 'cashtransfer'){
            $paymentmodeLedgerId = ChsoneGrpLedgerTree::where('ledger_account_name', 'tds receivable')->first();
            $from_ledger_id = $paymentmodeLedgerId->ledger_account_id ?? '';
            $from_ldger_name = $paymentmodeLedgerId->ledger_account_name ?? '';
        }

        if($data['receipt_mode'] == 'cheque') {
            $payment_reference = $data['cheque_number'] ?? '';
        } elseif($data['receipt_mode'] == 'cashtransfer') {
            $payment_reference = $data['transaction_reference'] ?? '';
        } else {
            $payment_reference = '';
        }

        $obj = $this->tenantDB()->table('chsone_ledger_transactions')
        ->insert([
            'soc_id' => $data['company_id'],
            'transaction_date' => $data['receipt_date'],
            'ledger_account_id' => $from_ledger_id,
            'ledger_account_name' => $from_ldger_name,
            'voucher_type' => 'receipt',
            'voucher_reference_number' => $data['receipt_number'],
            'voucher_reference_id' => $data['receipt_id'],
            'transaction_type' => 'dr',
            'payment_mode' => $data['receipt_mode'] ?? '',
            'payment_reference' => $payment_reference ?? '',
            'transaction_amount' => $data['invoice_amount'] ?? '0.000',
            'txn_from_id' => 0,
            'memo_desc' => 'TDS amount deducted against Invoice '. $data['invoice_number'] .' dated '. $data['receipt_date'].' through '. $data['receipt_mode'] .' with payment ref. ('.$payment_reference.')',
            'is_opening_balance' => 0,
            'is_reconciled' => 0,
            'is_cancelled' => 0,
            'created_by' => $data['created_by'] ?? '0',
            'added_on' => date('Y-m-d H:i:s'),
        ]);

        $result = $this->tenantDB()->table('chsone_ledger_transactions')->orderBy('txn_id', 'desc')->first();

        // fetch ledger_account_id from chsone_units_master table whose unit_id is $data['unit_id']
        $unitLedgerId = ChsoneUnitsMaster::where('unit_id', $data['unit_id'])->first();
        $to_ledger_id = $unitLedgerId->nonmember_ledger_id;
        // fetch ledger_account_name from chsone_grp_ledger_tree table whose ledger_account_id is $to_ledger_id
        $unitLedgerName = ChsoneGrpLedgerTree::where('ledger_account_id', $to_ledger_id)->first();
        $to_ledger_name = $unitLedgerName->ledger_account_name;

        $obj2 = $this->tenantDB()->table('chsone_ledger_transactions')
        ->insert([
            'soc_id' => $data['company_id'],
            'transaction_date' => $data['receipt_date'],
            'ledger_account_id' => $to_ledger_id,
            'ledger_account_name' => $to_ledger_name,
            'voucher_type' => 'receipt',
            'voucher_reference_number' => $data['receipt_number'],
            'voucher_reference_id' => $data['receipt_id'],
            'transaction_type' => 'cr',
            'payment_mode' => $data['receipt_mode'] ?? '',
            'payment_reference' => $payment_reference ?? '',
            'transaction_amount' => $data['invoice_amount'] ?? '0.000',
            'txn_from_id' => $result->txn_id,
            'memo_desc' => 'TDS amount deducted against Invoice '. $data['invoice_number'] .' dated '. $data['receipt_date'].' through '. $data['receipt_mode'] .' with payment ref. ('.$payment_reference.')',
            'is_opening_balance' => 0,
            'is_reconciled' => 0,
            'is_cancelled' => 0,
            'created_by' => $data['created_by'] ?? '0',
            'added_on' => date('Y-m-d H:i:s'),
        ]);

        if($obj && $obj2) {
            return true;
        } else {
            return false;
        }
    }
}
