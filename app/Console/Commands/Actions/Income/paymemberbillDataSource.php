<?php

namespace App\Console\Commands\Actions\Income;

use Illuminate\Console\Command;
use App\Console\Commands\Action;
use App\Models\Tenants\ChsoneAccountsMaster;
use App\Models\Tenants\ChsoneCreditAccount;
use App\Models\Tenants\ChsoneGrpLedgerTree;
use App\Models\Tenants\ChsoneLedgerTransaction;
use App\Models\Tenants\ChsoneMembersMaster;
use App\Models\Tenants\ChsoneMemberTypeMaster;
use App\Models\Tenants\ChsoneTaxLog;
use App\Models\Tenants\ChsoneUnitsMaster;
use App\Models\Tenants\ExpenseInvoicePaymentTracker;
use App\Models\Tenants\IncomeAccount;
use App\Models\Tenants\IncomeInvoiceGeneralSetting;
use App\Models\Tenants\IncomeInvoiceParticular;
use App\Models\Tenants\IncomeInvoicePayment;
use App\Models\Tenants\IncomeInvoicePaymentTracker;
use App\Models\Tenants\IncomeInvoiceRule;
use App\Models\Tenants\IncomeInvoiceSetting;
use App\Models\Tenants\IncomeLatePaymentCharge;
use App\Models\Tenants\IncomeUnitInvoice;
use App\Models\Tenants\SocAccountFinancialYearMaster;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\DB;

class paymemberbillDataSource extends Action
{
    protected $constants;

    public function __construct() {
        parent::__construct();
        $this->constants = Config::get("constants");
    }

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'datasource:paymemberbill {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Make member payments';

    /**
     * Execute the console command.
     */
    public function apply()
    {
        $arrPostData = $this->input;
        $arrPostData['user_id'] = $this->input['user_id'] ?? 0;

        $arrDataListener['soc_id'] = $arrPostData['soc_id'] = $this->input['company_id'];
        $arrDataListener['unit_id'] = $arrPostData['unit_id'] = $this->input['unit_id'];
        $arrPostData['bill_type'] = 'member';
        $arrPostData['allowed_partial_paid'] = 'Yes';
        if($arrPostData['payment_mode'] == 'cheque') {
            $arrPostData['transaction_reference'] = $arrPostData['cheque_number'];
            $arrPostData['payment_instrument'] = $arrPostData['bank_name'];
        }

        // check payment mode is cash then fetch ledger account id from chsone_accounts_master whose default_account = 1
        if (strtolower($arrPostData['payment_mode']) == 'cash') {
            $arrPostData['bank_account'] = ChsoneAccountsMaster::where('soc_id', $arrPostData['soc_id'])->where('default_account', 1)->value('ledger_account_id');
        }

        // fetch general setting data
        $generalsettingparameter = $this->getgeneralsetting($arrDataListener);

        // get member detail
        $arrIncomeInvoiceMemberDetail = $this->getMemberDetail($arrDataListener);
        if (!empty($arrIncomeInvoiceMemberDetail)) {
            $arrDataListener['member_id'] = $arrIncomeInvoiceMemberDetail['id'];
        }

        // get unit details by unit id
        $arrUnitDetail = $this->getUnitDetailById($arrDataListener);

        // get unit invoice unpaid bill
        $arrIncomeInvoiceDetail = $this->getUnitInvoiceUnpaidBill($arrDataListener);

        // get post data
        // $arrPostData['member_paid_invoice'] = trim($this->input['member_paid_invoice'], ',');
        // Convert array to comma-separated string if it's an array
        $arrPostData['member_paid_invoice'] = is_array($this->input['member_paid_invoice']) 
            ? trim(implode(',', $this->input['member_paid_invoice']), ',')
            : trim($this->input['member_paid_invoice'], ',');

        // check if partial payment is allowed or not and if writeoff amount is greater than 0
        if (strtolower($generalsettingparameter['ALLOWED_PARTIAL_PAYMENT']) == 'no' || (!empty($this->input['writeoff_amount']) && $this->input['writeoff_amount'] > 0)) {
            $arrPostData['total_unpaid_amount'] = $this->getMemberUnpaidDueByInvoice(array('arrIncomeInvoiceDetail' => $arrIncomeInvoiceDetail, 'member_paid_invoice' => $this->input['member_paid_invoice']));
        }

        //For TDS adjustment
        if (!empty($arrPostData['tds_amount']) && $arrPostData['tds_amount'] > 0) {
            $arrPostData['payment_amount'] = (float) $arrPostData['payment_amount'] + $arrPostData['tds_amount'];
        }

        //Do not create bill if financial years accounting closed    
        $arrPostData['arrCloseAccountPayment'] = $this->getClosedAccountDetailByDate(array('soc_id' => $arrPostData['company_id'], 'bill_date' => $arrPostData['payment_date']));

        $arrPostData['writeoff_amount'] = preg_replace('/\s+/', '', $arrPostData['writeoff_amount']);

        //Dynamically select unpaid invoices based on amount paid but not in case of writeoff
        if (empty($arrPostData['writeoff_amount'])) {
            $memberPaidInvoices = $this->getMemberPaidInvoices(array('payment_amount' => $arrPostData['payment_amount'], 'unpaidInvoiceDetail' => $arrIncomeInvoiceDetail));
            if (!empty($memberPaidInvoices)) {
                $arrPostData['member_paid_invoice'] = trim(implode(',', $memberPaidInvoices), ',');
            }
        }

        // Generate payment receipt number
        $arrPostData['receipt_number'] = $this->generate_receipt_id(array('soc_id' => $arrPostData['company_id']));

        // Generate payment token
        $paymentToken = $arrPostData['payment_token'] = $this->generatePaymentToken(array('arrPaymentTracker' => $arrPostData));

        // Insert payment details in income_invoice_payment_tracker table
        $arrResponseTracker = $this->saveInvoicePaymentTracker(array('soc_id' => $arrPostData['company_id'], 'unit_id' => $arrPostData['unit_id'], 'postData' => $arrPostData));
        $arrPostData['payment_tracker_id'] = $arrResponseTracker['payment_tracker_id'];
        
        // payment token verification
        $isTokenValid = $this->paymentTokenVerification(array('soc_id' => $arrPostData['soc_id'], 'paymentToken' => $paymentToken));

        // check if payment token is valid or payment tracker id is not empty
        if ($isTokenValid && !empty($arrResponseTracker['payment_tracker_id'])) {
            //Update the transaction status to in process
            $this->updateIncomePaymentTrackerStatus(array('soc_id' => $arrPostData['soc_id'], 'updated_by' => $arrPostData['user_id'], 'payment_tracker_id' => $arrResponseTracker['payment_tracker_id'], 'transaction_status' => 'in_process'));

            if (in_array(strtolower($arrPostData['payment_mode']), $this->constants['payment_mode_for_clearance'])) {
                if (!empty($arrResponseTracker) && strtolower($arrResponseTracker['status']) == 'success') {
                    if (isset($arrPostData['writeoff_amount']) && !empty($arrPostData['writeoff_amount']) && $arrPostData['writeoff_amount'] > 0) {
                        $arrResponse = $this->updateUnitInvoiceStatus(array('soc_id' => $arrPostData['soc_id'], 'status' => 'in_process', 'invoice_number' => $arrPostData['member_paid_invoice']));
                    } else {
                        $arrResponse = $arrResponseTracker;
                    }
                    $succMsg = ucwords($arrUnitDetail['soc_building_name']) . '/' . ucwords($arrUnitDetail['unit_flat_number']) . ' receipt has successfully added to receipt tracker waiting for clearance.';
                }
            } else {
                $paymentvalue = $arrPostData['payment_amount'] + $arrPostData['writeoff_amount'] + $arrPostData['tds_amount'];
                if (!empty($arrResponseTracker) && strtolower($arrResponseTracker['status']) == 'success') {

                    if (isset($arrPostData['writeoff_amount']) && !empty($arrPostData['writeoff_amount']) && $arrPostData['writeoff_amount'] > 0 && $paymentvalue == $arrIncomeInvoiceDetail[0]['total_invoice_due']) {
                        $arrResponse = $this->_invoiceWrtieoffPaymentTracker($arrPostData['unit_id'], array('arrPostData' => $arrPostData, 'arrIncomeInvoiceDetail' => $arrIncomeInvoiceDetail, 'arrIncomeInvoiceMemberDetail' => $arrIncomeInvoiceMemberDetail));
                    } else {
                        $arrResponse = $this->_invoicePaymentTracker($arrPostData['unit_id'], array('arrPostData' => $arrPostData, 'arrIncomeInvoiceDetail' => $arrIncomeInvoiceDetail, 'arrIncomeInvoiceMemberDetail' => $arrIncomeInvoiceMemberDetail));
                    }
                    $succMsg = ucwords($arrUnitDetail['soc_building_name']) . ' / ' . ucwords($arrUnitDetail['unit_flat_number']) . ' receipt has added successfully';
                }
            }
        } else {
            //$arrUpdatePaymentTrackerListener = array('soc_id'=>$auth['soc_id'], 'payment_tracker_id'=>$arrResponseTracker['payment_tracker_id'], 'status'=>'N');
            if (!empty($arrResponseTracker['payment_tracker_id'])) {
                $this->updateIncomePaymentTrackerStatus(array('soc_id' => $arrPostData['soc_id'], 'updated_by' => $arrPostData['user_id'], 'payment_tracker_id' => $arrResponseTracker['payment_tracker_id'], 'status' => 'N', 'transaction_status' => 'complete'));
            }

            $this->status = 'error';
            $this->message = 'Unable to complete transaction, Please try later.';
            $this->statusCode = 400;
        }

        // check response
        if (!empty($arrResponse) && strtolower($arrResponse['status']) == 'success') {
            $trackertStatus = 'Y';

            if (in_array(strtolower($arrPostData['payment_mode']), $this->constants['payment_mode_for_clearance'])) {
                $trackertStatus = 'P';

                if (strtolower($arrPostData['payment_mode']) == 'cheque' && $arrPostData['payment_date'] > date('Y-m-d')) {
                    $trackertStatus = 'R';
                }
            }

            // Update payment tracker status
            if (!empty($arrResponseTracker['payment_tracker_id'])) {

                $arrUpdatePaymentTrackerDetail = array('soc_id' => $arrPostData['soc_id'], 'updated_by' => $arrPostData['user_id'], 'payment_tracker_id' => $arrResponseTracker['payment_tracker_id'], 'status' => $trackertStatus, 'transaction_status' => 'complete');
                $this->updateIncomePaymentTrackerStatus($arrUpdatePaymentTrackerDetail);
            }

            /* Start notification cade */
                // get payment details for send notification here
                // $paymentTracker = IncomeInvoicePaymentTracker::where('id = ' . $arrResponseTracker['payment_tracker_id'])->get();

                // if (count($paymentTracker)) {
                //     $amount = $paymentTracker[0]->payment_amount;
                //     $flag = false;
                //     $user_ids = [];

                //     // get member details
                //     $members = ChsoneMembersMaster::where('status', 1)->where('fk_unit_id', $paymentTracker[0]['unit_id'])->get();

                //     if (count($members) > 0) {
                //         foreach ($members as $member) {
                //             array_push($user_ids, $member['user_id']);
                //         }
                //     }

                //     if (in_array(strtolower($paymentTracker[0]['payment_mode']), $this->constants['payment_mode_for_member_clearance'])) {

                //         if (strtolower($paymentTracker[0]['payment_mode']) == 'cheque') {
                //             if ($trackertStatus == 'P') {
                //                 $pushData['title'] = $this->constants['notifications']['cheque_received']['title'];
                //                 $message = $this->constants['notifications']['cheque_received']['message'];
                //                 $message = \ChsOne\Helper\CommonHelper::prepareMessage($message, array($amount));
                //                 $flag = true;
                //             }
                //             if ($trackertStatus == 'R') {
                //                 $pushData['title'] = $this->constants['notifications']['cheque_submit_by_member']['title'];
                //                 $message = $this->constants['notifications']['cheque_submit_by_member']['message'];
                //                 $message = \ChsOne\Helper\CommonHelper::prepareMessage($message, array($amount));
                //                 $flag = true;
                //             }
                //         }

                //         if (strtolower($paymentTracker[0]['payment_mode']) == 'cashtransfer') {
                //             if ($trackertStatus == 'P') {
                //                 $pushData['title'] = $this->constants['notifications']['eft_received']['title'];
                //                 $message = $this->constants['notifications']['eft_received']['message'];
                //                 $message = \ChsOne\Helper\CommonHelper::prepareMessage($message, array($amount));
                //                 $flag = true;
                //             }
                //             if ($trackertStatus == 'R') {
                //                 $pushData['title'] = $this->constants['notifications']['eft_submit_by_member']['title'];
                //                 $message = $this->constants['notifications']['eft_submit_by_member']['message'];
                //                 $message = \ChsOne\Helper\CommonHelper::prepareMessage($message, array($amount));
                //                 $flag = true;
                //             }
                //         }
                //     } else {
                //         if ($paymentTracker[0]['status'] == 'Y') {
                //             $pushData['title'] = $this->constants['notifications']['amount_received']['title'];
                //             $message = $this->constants['notifications']['amount_received']['message'];
                //             $message = \ChsOne\Helper\CommonHelper::prepareMessage($message, array($amount));
                //             $flag = true;
                //         }
                //     }

                //     if ($flag) {
                //         $pushData['message'] = $message;
                //         $pushData['user_ids'] = $user_ids;
                //         \ChsOne\Helper\CommonHelper::pushMobileNotification($pushData);
                //     }
                // }
            /* End notification cade */

            $this->status = 'success';
            $this->message = $succMsg;
            $this->statusCode = 200;
        } else {
            $this->status = 'error';
            $this->message = 'Unable to complete transaction, Please try later.';
            $this->statusCode = 400;
        }

        // if transaction failed then update the status of payment tracker
        if (empty($arrResponse) || (!empty($arrResponse) && strtolower($arrResponse['status']) != 'success')) {
            if (!empty($arrResponseTracker['payment_tracker_id'])) {
                $this->updateIncomePaymentTrackerStatus(array('soc_id' => $arrPostData['soc_id'], 'updated_by' => $arrPostData['user_id'], 'payment_tracker_id' => $arrResponseTracker['payment_tracker_id'], 'status' => 'N', 'transaction_status' => 'complete'));
            }
        }
    }

    // get general setting
    public function getGeneralSetting($data = [])
    {
        $finalArray = $this->_getAllSetting($data);

        // Use Eloquent to fetch the latest IncomeInvoiceSetting based on soc_id and effective_date
        $incomeInvoiceSetting = IncomeInvoiceSetting::where('soc_id', $data['soc_id'])
            ->orderBy('effective_date', 'DESC')
            ->first();
        if ($incomeInvoiceSetting) {
            $finalArray['generalsettingid'] = $incomeInvoiceSetting->id;
            $finalArray['invoicing_frequency'] = !empty($incomeInvoiceSetting->invoicing_frequency) ? $incomeInvoiceSetting->invoicing_frequency : '';
            $finalArray['effective_date'] = $incomeInvoiceSetting->effective_date;
        } else {
            $finalArray['generalsettingid'] = null;
            $finalArray['invoicing_frequency'] = '';
            $finalArray['effective_date'] = null;
        }

        // Get additional settings using a helper method
        $finalArray['general_setting_key_ids'] = $this->_getAllSettingWithId($data);

        return $finalArray;
    }

    // get all settings
    private function _getAllSetting($data) {
        $incomeinvoicesetting = IncomeInvoiceSetting::where('soc_id', $data['soc_id'])->first();
        $incomeinvoicegeneralsetting = IncomeInvoiceGeneralSetting::where('soc_id', $data['soc_id'])->get();
        foreach ($incomeinvoicegeneralsetting as $key => $value) {
            $final_array[$value['setting_key']] = $value['setting_value'];
        }
        return $final_array;
    }

    // get all settings with id
    private function _getAllSettingWithId($data) {
        $incomeinvoicegeneralsetting = IncomeInvoiceGeneralSetting::where('soc_id', $data['soc_id'])->get();
        foreach ($incomeinvoicegeneralsetting as $key => $value) {
            $final_array[$value['setting_key']] = $value['id'];
        }
        return $final_array;
    }

    // get unit invoice unpaid bill
    public function getUnitInvoiceUnpaidBill($data = array())
    {
        $arrAllUnitsInvoice = array();
        $todaysDate = date('Y-m-d');
        
        $query = IncomeUnitInvoice::where('soc_id', $data['soc_id'])
        ->where('fk_unit_id', $data['unit_id'])
        ->whereIn('payment_status', ['unpaid', 'partialpaid'])
        ->where('status', '!=', 'cancelled');
        
        if (!isset($data['in_progress']) || $data['in_progress'] == true) {
            $query->where('status', '!=', 'in_process');
        }

        if (!empty($data['from_date']) && !empty($data['to_date'])) {
            $from_date = (strrchr($data['from_date'], '/')) ? $this->getDatabaseDate($data['from_date']) : $data['from_date'];
            $to_date = (strrchr($data['to_date'], '/')) ? $this->getDatabaseDate($data['to_date']) : $data['to_date'];
            $query->where('due_date','>=' , $from_date)->where('due_date','<=', $to_date);
        }
        // Assuming the "UnitsInvoice" model corresponds to the "units_invoices" table
        $objUnitsInvoice = $query->orderBy('unit_invoice_id', 'asc')->get();

        if (!empty($objUnitsInvoice)) {
            $arrUnitsInvoice = $objUnitsInvoice->toArray();
            if (!empty($arrUnitsInvoice)) {
                $arrAllUnitsInvoice = $this->getUnpaidInvoiceDetail(array(
                    'arrUnitsInvoice' => $arrUnitsInvoice,
                    'member_id' => $data['member_id'],
                    'soc_id' => $data['soc_id'],
                    'unit_id' => $data['unit_id']
                ));
            }
        }
        return $arrAllUnitsInvoice;
    }

    // get member unpaid due by invoice
    function getUnpaidInvoiceDetail($data = array())
    {
        $arrAllUnitsInvoice = array();
        $latest_due_date = null;
        $last_invoice_details = array();
        $todaysDate = date('Y-m-d');
        if (!empty($data['arrUnitsInvoice'])) {
            $arrUnitsInvoice = $data['arrUnitsInvoice'];

            $arrLastUnitInvoice = $this->getLastUnitInvoiceByUnit(array(
                'soc_id' => $data['soc_id'],
                'unit_id' => $data['unit_id']
            ));

            $total = $total_advance_amount = $total_outstanding_principal_amount = $total_interest_amount = $totalDue = 0;
            $arrUnitsInvoice = json_decode(json_encode($arrUnitsInvoice), true);
            foreach ($arrUnitsInvoice as $eachUnitInvoice) {
                $singleUnitInvoice = array();
                $singleUnitInvoice['soc_id'] = $eachUnitInvoice['soc_id'];
                $singleUnitInvoice['member_id'] = !empty($data['member_id']) ? $data['member_id'] : '';
                $singleUnitInvoice['unit_id'] = $eachUnitInvoice['fk_unit_id'];
                $singleUnitInvoice['soc_building_name'] = $eachUnitInvoice['soc_building_name'];
                $singleUnitInvoice['unit_name'] = $eachUnitInvoice['unit_name'];
                $singleUnitInvoice['unit_invoice_id'] = $eachUnitInvoice['unit_invoice_id'];
                $singleUnitInvoice['unit_invoice_number'] = $eachUnitInvoice['invoice_number'];
                if (!empty($arrLastUnitInvoice) && isset($arrLastUnitInvoice['invoice_number'])) {
                    $singleUnitInvoice['unit_first_invoice'] = $eachUnitInvoice['unit_first_invoice'] = $arrLastUnitInvoice['invoice_number'];
                }
                $singleUnitInvoice['principal_amount'] = $eachUnitInvoice['principal_amount'];
                $singleUnitInvoice['interest_amount'] = $singleUnitInvoice['late_payment_interest'] = $eachUnitInvoice['interest_amount'];
                $singleUnitInvoice['advance_amount'] = $eachUnitInvoice['advance_amount'];
                $singleUnitInvoice['roundoff_amount'] = $eachUnitInvoice['roundoff_amount'];
                $singleUnitInvoice['payment_status'] = $eachUnitInvoice['payment_status'];
                $singleUnitInvoice['from_date'] = $eachUnitInvoice['from_date'];
                $singleUnitInvoice['to_date'] = $eachUnitInvoice['to_date'];

                $singleUnitInvoice['invoice_date'] = !empty($eachUnitInvoice['created_date']) ? current(explode(' ', $eachUnitInvoice['created_date'])) : '';
                $singleUnitInvoice['due_date'] = !empty($eachUnitInvoice['due_date']) ? $eachUnitInvoice['due_date'] : '';
                $singleUnitInvoice['due_date_status'] = (!empty($singleUnitInvoice['due_date']) && $todaysDate > $singleUnitInvoice['due_date']) ? 'red' : 'green';
                $singleUnitInvoice['late_charge_tax_detail'] = (!empty($singleUnitInvoice['interest_amount']) && $singleUnitInvoice['interest_amount'] > 0) ? $this->getMemberInvoiceLateChargeTaxDetail(array(
                'soc_id' => $eachUnitInvoice['soc_id'],
                'invoice_number' => $eachUnitInvoice['invoice_number']
                )) : null;
                $total_advance_amount += (float) $eachUnitInvoice['advance_amount'];

                $total_interest_amount += $singleUnitInvoice['interest_amount'];
                $total_interest_amount = (!empty($singleUnitInvoice['late_charge_tax_detail']['total_tax']) && $singleUnitInvoice['late_charge_tax_detail']['total_tax'] > 0) ? $total_interest_amount + $singleUnitInvoice['late_charge_tax_detail']['total_tax'] : $total_interest_amount;

                $arrgetdata = array(
                    "conditions" => " soc_id = '" . $singleUnitInvoice['soc_id'] . "' and fk_unit_invoice_id = '" . $singleUnitInvoice['unit_invoice_id'] . "' and fk_unit_id = '" . $singleUnitInvoice['unit_id'] . "' and invoice_number = '" . $singleUnitInvoice['unit_invoice_number'] . "'",
                    "order" => "created_date desc"
                );
                $objIncomeInvoiceParticular = $this->tenantDB()->table('income_invoice_particular')
                    ->whereRaw($arrgetdata['conditions'])
                    ->get();
                // $objIncomeInvoiceParticular = json_decode(json_encode($objIncomeInvoiceParticular), true);
                if (!empty($objIncomeInvoiceParticular)) {
                    $singleUnitInvoice['invoice_particulars'] = $objIncomeInvoiceParticular->toArray();
                    $singleUnitInvoice['invoice_amount_detail'] = $this->_getTotalInvoiceAmount($singleUnitInvoice['invoice_particulars'], $eachUnitInvoice);
                    if (!empty($singleUnitInvoice['invoice_amount_detail']['partialLatePaymentCharge'])) {
                        $total_interest_amount = round((float) $total_interest_amount - (float) $singleUnitInvoice['invoice_amount_detail']['partialLatePaymentCharge'], 4);
                    }
                    // $total += bcsub((float) $singleUnitInvoice['invoice_amount_detail']['finalInvoiceAmount'], (float) $singleUnitInvoice['invoice_amount_detail']['partialpaidAmount'], 4);
                    $total += (float) number_format((float) $singleUnitInvoice['invoice_amount_detail']['finalInvoiceAmount'] - (float) $singleUnitInvoice['invoice_amount_detail']['partialpaidAmount'], 4, '.', '');                    
                }
                //check below key is unit_first_invoice
                if (strtolower($eachUnitInvoice['unit_invoice_id']) == strtolower($eachUnitInvoice['invoice_number']) && strtolower($eachUnitInvoice['payment_status']) != 'paid') {
                    $total_outstanding_principal_amount = (float) $eachUnitInvoice['principal_amount'];
                }

                $arrLatePaymentCharges = array();
                $arrLatePaymentCharges = $this->calculateLatePaymentCharges($singleUnitInvoice);

                $singleUnitInvoice['apply_late_payment_charges'] = $arrLatePaymentCharges['apply_late_payment_charges'];
                $singleUnitInvoice['late_payment_charges_detail'] = (!empty($arrLatePaymentCharges['late_payment_charges_detail'])) ? $arrLatePaymentCharges['late_payment_charges_detail'] : array();
                $singleUnitInvoice['late_payment_charges'] = (!empty($arrLatePaymentCharges['total_late_payment_charges'])) ? $arrLatePaymentCharges['total_late_payment_charges'] : 0;
                $singleUnitInvoice['total_late_payment_chargeable_amount'] = (!empty($arrLatePaymentCharges['total_late_payment_chargeable_amount'])) ? $arrLatePaymentCharges['total_late_payment_chargeable_amount'] : 0;
                $singleUnitInvoice['total_invoice_due'] = (float) ($total + $total_interest_amount + $total_outstanding_principal_amount - $total_advance_amount);
                $singleUnitInvoice['single_invoice_due'] = (float) (round($singleUnitInvoice['total_invoice_due'] - $totalDue, 3));
                $totalDue = $singleUnitInvoice['total_invoice_due'];

                $latest_due_date = $singleUnitInvoice['due_date'];
                $last_invoice_details['due_date'] = $eachUnitInvoice['due_date'];
                $last_invoice_details['from_date'] = $eachUnitInvoice['from_date'];
                $last_invoice_details['to_date'] = $eachUnitInvoice['to_date'];
                $last_invoice_details['invoice_number'] = $eachUnitInvoice['invoice_number'];
                array_push($arrAllUnitsInvoice, $singleUnitInvoice); // $i++;
            }
            $arrAllUnitsInvoice['total_unpaid_invoice_amount'] = (float) ($total + $total_interest_amount + $total_outstanding_principal_amount - $total_advance_amount);
            $arrAllUnitsInvoice['latest_due_date'] = $latest_due_date;
            $arrAllUnitsInvoice['last_invoice_details'] = $last_invoice_details;
        }
        return $arrAllUnitsInvoice;
    }

    // get last unit invoice by unit
    public function getLastUnitInvoiceByUnit($data = array())
    {
        $arrUnitsInvoice = array();

        $query = IncomeUnitInvoice::query()
        ->where('soc_id', $data['soc_id'])
        ->where('fk_unit_id', $data['unit_id'])
        ->where('status', '!=', 'cancelled');

        if (isset($data['order_by']) && !empty($data['order_by'])) {
            $query->orderByRaw($data['order_by']);
        }
        $objUnitsInvoice = $query->first();

        if (!empty($objUnitsInvoice)) {
            $arrUnitsInvoice = $objUnitsInvoice->toArray();
        }

        return $arrUnitsInvoice;
    }

    // get member invoice late charge tax detail
    public function getMemberInvoiceLateChargeTaxDetail($data = array())
    {
        $arrTaxClass = array(
            'tax_detail' => null,
            'total_tax' => 0
        );

        $query = ChsoneTaxLog::where('soc_id', $data['soc_id'])
        ->whereNull('particular_id')
        ->where('particular', 'LateCharge');
        
        if (is_array($data['invoice_number'])) {
            $query->whereIn('invoice_number', $data['invoice_number']);
        } else {
            $query->where('invoice_number', $data['invoice_number']);
        }
    
        $objTaxClass = $query->get();

        if (!empty($objTaxClass)) {
            $arrTaxClass['tax_detail'] = $objTaxClass->toArray();
            foreach ($arrTaxClass['tax_detail'] as $eachTaxLog) {
                $arrTaxClass['total_tax'] += $eachTaxLog['tax_amount'];
            }
        }
        return $arrTaxClass;
    }

    // get total invoice amount
    private function _getTotalInvoiceAmount($data, $arrUnitInvoice = array())
    {
        $arrTotal = array('totalInvoiceAmount' => 0, 'totalTaxApplicable' => 0, 'totalTaxExemption' => 0, 'partialpaidAmount' => 0, 'finalInvoiceAmount' => $arrUnitInvoice['roundoff_amount'], 'partialLatePaymentCharge' => 0);
        $data = json_decode(json_encode($data), true);
        if (!empty($data)) {
            foreach ($data as $eachInvoiceParticular) {
                $arrTotal['totalInvoiceAmount'] += $eachInvoiceParticular['amount'];
                $applied_tax = (!empty($eachInvoiceParticular['tax_applicable']) && !is_array($eachInvoiceParticular['tax_applicable'])) ? unserialize($eachInvoiceParticular['tax_applicable']) : $eachInvoiceParticular['tax_applicable'];
                // print_r($applied_tax);
                $tax_exemption = (!empty($eachInvoiceParticular['tax_exemptions']) && !is_array($eachInvoiceParticular['tax_exemptions'])) ? unserialize($eachInvoiceParticular['tax_exemptions']) : $eachInvoiceParticular['tax_exemptions'];
                $applied_tax_total = (is_array($applied_tax)) ? $applied_tax['total'] : $applied_tax;
                $tax_exemption_total = (is_array($tax_exemption)) ? $tax_exemption['total'] : $tax_exemption;
                $arrTotal['totalTaxApplicable'] += (float) $applied_tax_total;
                $arrTotal['totalTaxExemption'] += (float) $tax_exemption_total;
                $arrTotal['finalInvoiceAmount'] += (float) $eachInvoiceParticular['amount'] + ($applied_tax_total + $tax_exemption_total);
            }
        }
        // get partial pad amount
        if (!empty($arrUnitInvoice['payment_status']) && in_array($arrUnitInvoice['payment_status'], array('partialpaid','paid'))) 
        {
            $objIncomeInvoicePayment = IncomeInvoicePayment::where('soc_id', $arrUnitInvoice['soc_id'])->where('fk_unit_id', $arrUnitInvoice['fk_unit_id'])->where('fk_unit_invoice_id', $arrUnitInvoice['unit_invoice_id'])->where('invoice_number', $arrUnitInvoice['invoice_number'])->get();

            if (!empty($objIncomeInvoicePayment)) {

                $arrIncomeInvoicePayment = $objIncomeInvoicePayment->toArray();
                // echo "<pre>";print_r($arrIncomeInvoicePayment);exit;
                foreach ($arrIncomeInvoicePayment as $eachIncomeInvoicePayment) {
                    $arrTotal['partialpaidAmount'] += (float) ($eachIncomeInvoicePayment['payment_amount'] + $eachIncomeInvoicePayment['tds_deducted'] + $eachIncomeInvoicePayment['writeoff_amount']);
                    $arrTotal['partialLatePaymentCharge'] += (float) $eachIncomeInvoicePayment['late_payment_charges'];
                }
                // $arrTotal['finalInvoiceAmount'] -= $arrTotal['invoice_amount_detail']['partialpaidAmount'];
            }
        }
        return $arrTotal;
    }

    // calculate late payment charges
    public function calculateLatePaymentCharges($data = array())
    {
        $latePaymentCharge = 0;
        $todaysDate = date('Y-m-d');
        $arrLatePaymentCharges = array();
        $arrAllLatePaymentCharges = array();
        $arrLatePaymentCharges['apply_late_payment_charges'] = 0;
        $total_late_payment_chargeable_amount = 0;

        if (!empty($data['due_date'])) {
            $dueDate = $data['due_date'];
            $lateChargeCalculationDate = new \DateTime($data['from_date']);
            $lateChargeCalculationDate = $lateChargeCalculationDate->modify("-1 day")->format('Y-m-d'); // For today/now, don't pass an arg.

            if ($todaysDate > $dueDate) {
                // set late payment charges key
                if (strtolower($data['payment_status']) != 'paid') {
                    $arrLatePaymentCharges['apply_late_payment_charges'] = 1;
                }
                if (!empty($data['invoice_particulars'])) {
                    // print_r($data);//exit;
                    // print_r($data);
                    $arrData = $this->_getRulewiseTotalAmount($data, $data['payment_status']);
                    // exit;
                    // echo '################# Late payment Interest ##############################';
                    // print_r($data['invoice_particulars']);
                    // print_r($arrData);exit;
                    // $arrLatePaymentCharges['particular_late_charges'] = $arrData;
                    $total_late_payment_chargeable_amount = 0;
                    $arrGeneralInvoiceSetting = $this->getInvoiceSetting(array(
                        'soc_id' => $data['soc_id']
                    )); // Get general setting
                    $arrLatePaymentdetails = $this->getLatePaymentCharges(array(
                        'soc_id' => $data['soc_id']
                    )); // Get late charges rule

                    foreach ($arrData as $rule => $amount) {

                        $objincomeinvoicerule = IncomeInvoiceRule::where('id', $rule)
                        ->where('soc_id', $data['soc_id'])
                        ->first();

                        if (!empty($objincomeinvoicerule)) {
                            $arrRuleWiseCharges = array();
                            $arrIncomeinvoicerule = $objincomeinvoicerule->toArray();
                            if (isset($arrIncomeinvoicerule['apply_late_payment_interest']) && $arrIncomeinvoicerule['apply_late_payment_interest'] == 1) {
                                // $arrLatePayment = array("conditions" => " soc_id = " . $data['soc_id'] . " and effective_date <= '" . $this->getCurrentDate('database') . "' and calculate_from = 'billdate'", "order" => "effective_date desc");
                                // $objLatePaymentdetails = \ChsOne\Models\LatePaymentCharges::findfirst($arrLatePayment);
                                if (!empty($arrLatePaymentdetails)) {
                                    $arrRuleWiseCharges['rule_id'] = $rule;
                                    $singleRuleCharges = $this->calculateMonthlyLateCharges(array(
                                        'soc_id' => $data['soc_id'],
                                        'dueDate' => $dueDate,
                                        'amount' => $amount,
                                        'arrLatePaymentdetails' => $arrLatePaymentdetails,
                                        'arrGeneralInvoiceSetting' => $arrGeneralInvoiceSetting,
                                        'lateChargeCalculationDate' => $lateChargeCalculationDate
                                    ));

                                    $arrRuleWiseCharges['late_payment_charges'] = $singleRuleCharges;
                                    $total_late_payment_chargeable_amount += $amount;
                                    $latePaymentCharge += $singleRuleCharges;
                                    array_push($arrAllLatePaymentCharges, $arrRuleWiseCharges);
                                }
                            }
                        }
                    }
                }
            }
        }
        $arrLatePaymentCharges['late_payment_charges_detail'] = $arrAllLatePaymentCharges;
        $arrLatePaymentCharges['total_late_payment_charges'] = $latePaymentCharge;
        $arrLatePaymentCharges['total_late_payment_chargeable_amount'] = $total_late_payment_chargeable_amount;
        return $arrLatePaymentCharges;
    }

    private function _getRulewiseTotalAmount($data = array(), $paymentStatus)
    {
        $arrRule = array();
        $data = json_decode(json_encode($data), true);
        if (!empty($data)) {
            foreach ($data['invoice_particulars'] as $eachParticular) {

                $finalParticularAmount = 0;

                // Exclude noc from total amount
                $finalParticularAmount = (float) $eachParticular['amount'];

                // Add applicable tax amount
                if (!empty($eachParticular['tax_applicable'])) {
                    $arrTaxApplicable = unserialize($eachParticular['tax_applicable']);
                    $finalParticularAmount += (float) $arrTaxApplicable['total'];
                }

                // Subtract applicable tax exemption amount
                if (!empty($eachParticular['tax_exemptions'])) {
                    $arrTax_exemptions = unserialize($eachParticular['tax_exemptions']);
                    $finalParticularAmount += (float) $arrTax_exemptions['total'];
                }

                if (array_key_exists($eachParticular['fk_rule_id'], $arrRule)) {
                    if (strtolower($eachParticular['is_particular_paid']) == 'y') {
                        $arrRule[$eachParticular['fk_rule_id']] += 0;
                    } elseif (strtolower($eachParticular['is_particular_paid']) == 'p') {
                        $arrRule[$eachParticular['fk_rule_id']] += (!empty($eachParticular['particular_paid_amount'])) ? round($finalParticularAmount - $eachParticular['particular_paid_amount'], 4) : $finalParticularAmount;
                    } else {
                        $arrRule[$eachParticular['fk_rule_id']] += $finalParticularAmount;
                    }
                } else {
                    if (strtolower($eachParticular['is_particular_paid']) == 'y') {
                        $arrRule[$eachParticular['fk_rule_id']] = 0;
                    } elseif (strtolower($eachParticular['is_particular_paid']) == 'p') {
                        $arrRule[$eachParticular['fk_rule_id']] = (isset($eachParticular['particular_paid_amount'])) ? $finalParticularAmount - $eachParticular['particular_paid_amount'] : $finalParticularAmount;
                    } else {
                        $arrRule[$eachParticular['fk_rule_id']] = $finalParticularAmount;
                    }
                }
                if ($paymentStatus == 'paidafterduedate') {
                    $arrRule[$eachParticular['fk_rule_id']] += $finalParticularAmount;
                }
            }
        }
        // print_r($arrRule);exit;
        return $arrRule;
    }

    public function getInvoiceSetting($data = array())
    {
        $arrInvoiceSetting = array();
        $objInvoiceSetting = IncomeInvoiceSetting::where('soc_id', $data['soc_id'])->first();
        if (!empty($objInvoiceSetting)) {
            $arrInvoiceSetting = $objInvoiceSetting->toArray();
        }
        return $arrInvoiceSetting;
    }

    public function getLatePaymentCharges($data = array())
    {
        $effective_date = date('Y-m-d');
        if (!empty($data['bill_date'])) {
            $effective_date = (strrchr($data['bill_date'], '/')) ? $data['bill_date'] : $data['bill_date'];
        }
        if (empty($data['type']) || $data['type'] == '') {
            $type = "type = 'maintenance'";
        } else {
            $type = "type = '" . $data['type'] . "'";
        }

        $objLatePaymentdetails = IncomeLatePaymentCharge::where('type', $type)
        ->where('soc_id', $data['soc_id'])
        ->where('effective_date', '<=', $effective_date)
        ->orderBy('effective_date', 'desc')
        ->first();

        $arrLatePaymentdetails = (!empty($objLatePaymentdetails)) ? $objLatePaymentdetails->toArray() : array();
        return $arrLatePaymentdetails;
    }

    public function calculateMonthlyLateCharges($data = array())
    {
        if ($data['arrLatePaymentdetails']['calculate_for'] == 'perday') {
            //            return $this->calculateDailyLateCharges($event, $component, $data);
        }
        $arrLatePaymentdetails = $data['arrLatePaymentdetails'];
        $arrInvoiceSetting = $data['arrGeneralInvoiceSetting'];
        // $dueDate = $data['dueDate'];
        $amount = $data['amount'];

        //         echo '<br><pre>';print_r($data);
        // $todaysDate = $this->getCurrentDate('database');
        $singleRuleCharges = 0;
        if (!empty($arrLatePaymentdetails)) {
            $numberOfMonth = 1;
            if (!empty($arrInvoiceSetting)) {
                switch (strtolower($arrInvoiceSetting['invoicing_frequency'])) {
                    case 'quarterly':
                        $numberOfMonth = 3;
                        break;
                    case 'half_yearly':
                        $numberOfMonth = 6;
                        break;
                    case 'yearly':
                        $numberOfMonth = 12;
                        break;
                    case 'monthly':
                    default:
                        $numberOfMonth = 1;
                        break;
                }
            }

            if (!empty($arrLatePaymentdetails['calculate_from']) && strtolower($arrLatePaymentdetails['calculate_from']) == 'duedate' && !empty($data['dueDate'])) {
                /**
                 * Converted to daily calculations while setting is set to charge
                 * late payment charge from due date
                 */
                //                return $this->calculateDailyLateCharges($event, $component, $data);
                // Test pending
                $end_date = $data['lateChargeCalculationDate'];
                if (isset($data['paidDate']) && !empty($data['paidDate'])) {
                    $end_date = $data['paidDate'];
                }
                $numberOfMonth = $this->getNumberOfMonth(array(
                    'start_date' => $data['dueDate'],
                    'end_date' => $end_date
                ));
                // echo 'numberOfMonth=>'.$numberOfMonth.'##';
            }
            // echo 'numberOfMonth=>'.$numberOfMonth.'##';
            // else
            // {
            // //$arrInvoiceSetting = $this->getInvoiceSetting($event, $component, array('soc_id'=>$data['soc_id']));//get all Unit details
            // }

            if ($arrLatePaymentdetails['interest_amount_type'] == 'fixed') {
                $singleRuleCharges = (float) round($arrLatePaymentdetails['simple_interest'] * $numberOfMonth, 4);
            } else {
                // echo $dueDate.'||'.$todaysDate;
                // $numberOfMonth = 1;//$this->getNumberOfMonth($event, $component, array('start_date' => $dueDate, 'end_date' => $todaysDate));

                $t = (float) round(($numberOfMonth / 12), 4); // exit;//convert to number of year
                $p = (float) $amount; // exit;
                $r = (float) ($arrLatePaymentdetails['simple_interest'] / 100); // exit;

                if (isset($arrLatePaymentdetails['interest_type']) && !empty($arrLatePaymentdetails['interest_type']) && in_array(strtolower($arrLatePaymentdetails['interest_type']), array(
                    'monthly',
                    'quarterly',
                    'half_yearly',
                    'yearly'
                ))) {
                    $n = 12;
                    switch (strtolower($arrLatePaymentdetails['interest_type'])) {
                        case 'quarterly':
                            $n = 4;
                            break;
                        case 'half_yearly':
                            $n = 2;
                            break;
                        case 'yearly':
                            $n = 1;
                            break;
                        case 'monthly':
                        default:
                            $n = 12;
                            break;
                    }
                    $A = (float) round(($p * pow(1 + ($r / $n), $n * $t)), 4); // compound interest formulae
                    $singleRuleCharges = (float) round($A - $p, 4);
                } else {
                    // echo '||'.$p.'*'.$r.'*'.$t.'||';
                    $singleRuleCharges = (float) round(($p * $r * $t), 4); // simple interest formulae
                }
            }
        }

        //         echo 'Rate=='.$singleRuleCharges.'Bye';
        return $singleRuleCharges;
    }

    public function getNumberOfMonth($data = array())
    {
        $begin = new \DateTime($data['start_date']);
        $end = new \DateTime($data['end_date']);
        // $end = $end->modify( '+1 month' );

        $interval = \DateInterval::createFromDateString('1 month');

        $period = new \DatePeriod($begin, $interval, $end);
        $counter = 0;
        foreach ($period as $dt) {
            $counter++;
        }
        if ($counter == 0) {
            $counter = 1;
        }
        return $counter;
    }

    // get member detail
    public function getMemberDetail($data = array())
    {
        $arrMemberMaster = [];
        $id = ChsoneMemberTypeMaster::where('soc_id', $data['soc_id'])
            ->where('member_type_name', 'Primary')
            ->value('member_type_id');

        $objMemberMaster = ChsoneMembersMaster::where('soc_id', $data['soc_id'])
            ->where('fk_unit_id', $data['unit_id'])
            ->where('status', 1)
            ->where('member_type_id', $id)
            ->first();

        if ($objMemberMaster) {
            $arrMemberMaster = $objMemberMaster->toArray();
        }

        return $arrMemberMaster;
    }

    // get unit detail by id
    public function getUnitDetailById(array $data = array())
    {
        $soc_id = $data['soc_id'];
        $unit_id = $data['unit_id'];
        $objUnitdetails = ChsoneUnitsMaster::where('soc_id', $soc_id)->where('unit_id', $unit_id)->first();
        if (!empty($objUnitdetails)) {
            $arrUnitDetails = $objUnitdetails->toArray();
        }
        return $arrUnitDetails;
    }

    // get member unpaid due by invoice
    public function getMemberUnpaidDueByInvoice($data = array())
    {
        $totalUnpaidDue = 0;
        if (!empty($data['member_paid_invoice'])) {
            if (empty($data['arrIncomeInvoiceDetail'])) {
                if (!empty($data['soc_id']) && !empty($data['unit_id']) && !empty($data['member_id'])) {
                    $data['arrIncomeInvoiceDetail'] = $this->getUnitInvoiceUnpaidBill(array(
                        'soc_id' => $data['soc_id'],
                        'unit_id' => $data['unit_id'],
                        'member_id' => $data['member_id']
                    ));
                }
            }

            if (!empty($data['arrIncomeInvoiceDetail'])) {
                if (!is_array($data['member_paid_invoice'])) {
                    $data['member_paid_invoice'] = explode(',', $data['member_paid_invoice']);
                }

                foreach ($data['arrIncomeInvoiceDetail'] as $eachInvoiceDetail) {
                    if (is_array($eachInvoiceDetail) && isset($eachInvoiceDetail['unit_invoice_number']) && isset($eachInvoiceDetail['single_invoice_due'])) {
                        if (in_array($eachInvoiceDetail['unit_invoice_number'], $data['member_paid_invoice'])) {
                            $totalUnpaidDue += (float) $eachInvoiceDetail['single_invoice_due'];
                        }
                    }
                }
            }
        }

        return $totalUnpaidDue;
    }

    // get closed account detail by date
    public function getClosedAccountDetailByDate($data=array()){
        $arrAccountMasterDetail = [];

        $billDate = !empty($data['bill_date']) ? $data['bill_date'] : date('Y-m-d');

        $objAccountMasterDetail = SocAccountFinancialYearMaster::where('soc_id', $data['soc_id'])
            ->where('closed', 1)
            ->where('confirmed', 1)
            // ->whereBetween($billDate, ['fy_start_date', 'fy_end_date'])
            ->where('fy_start_date', '<=', $billDate)
            ->where('fy_end_date', '>=', $billDate)
            ->first();

        if (!empty($objAccountMasterDetail)) {
            $arrAccountMasterDetail = $objAccountMasterDetail->toArray();
        }

        return $arrAccountMasterDetail;
    }

    // get member paid invoices
    public function getMemberPaidInvoices($data = array())
    {
        $arrMemberPaidInvoices = array();
        if (!empty($data['unpaidInvoiceDetail'])) {
            foreach ($data['unpaidInvoiceDetail'] as $eachInvoiceDetail) {
                if (isset($eachInvoiceDetail['unit_invoice_number']) && !empty($eachInvoiceDetail['unit_invoice_number'])) {
                    array_push($arrMemberPaidInvoices, $eachInvoiceDetail['unit_invoice_number']);
                    if ($eachInvoiceDetail['total_invoice_due'] >= $data['payment_amount']) {
                        break;
                    }
                }
            }
            return $arrMemberPaidInvoices;
        }
    }

    // generate receipt id
    public function generate_receipt_id($data = array()) {
        $settingDetails = IncomeInvoiceGeneralSetting::where('soc_id', $data['soc_id'])->where('setting_key', 'INCOME_RECEIPT_PREFIX')->first();

        // get last receipt number
        $last_receipt_number = $this->tenantDB()->table('income_invoice_payment_tracker')->orderBy('id', 'desc')->first();
        if($last_receipt_number && $last_receipt_number->receipt_number) {
            $numericPart = (int)preg_replace('/\D/', '', $last_receipt_number->receipt_number);
            // Increment the numeric part
            $newNumericPart = $numericPart + 1;
            // Format the new invoice number
            $newReceiptNumber = $settingDetails->setting_value . str_pad($newNumericPart, 5, '0', STR_PAD_LEFT);
        } else {
            $newReceiptNumber = $settingDetails->setting_value . '00001';
        }
        
        return $newReceiptNumber;
    }

    // generate payment token
    public function generatePaymentToken($data = array())
    {
        $paymentToken = '';
        if (!empty($data['arrPaymentTracker'])) {
            $arrPaymentDetail['soc_id'] = !empty($data['arrPaymentTracker']['soc_id']) ? $data['arrPaymentTracker']['soc_id'] : '';
            $arrPaymentDetail['received_from'] = $data['arrPaymentTracker']['received_from'];
            $arrPaymentDetail['bill_type'] = $data['arrPaymentTracker']['bill_type'];
            $arrPaymentDetail['payment_amount'] = $data['arrPaymentTracker']['payment_amount'];
            $arrPaymentDetail['payment_mode'] = $data['arrPaymentTracker']['payment_mode'];
            $arrPaymentDetail['invoice_number'] = !empty($data['arrPaymentTracker']['invoice_number']) ? $data['arrPaymentTracker']['invoice_number'] : '';
            $arrPaymentDetail['payment_date'] = $data['arrPaymentTracker']['payment_date'];
            $arrPaymentDetail['vendor_id'] = !empty($data['arrPaymentTracker']['vendor_id']) ? $data['arrPaymentTracker']['vendor_id'] : '';
            $arrPaymentDetail['vendor_bill_payment_amount'] = !empty($data['arrPaymentTracker']['vendor_bill_payment_amount']) ? $data['arrPaymentTracker']['vendor_bill_payment_amount'] : '';
            $arrPaymentDetail['vendor_bill_type_purchase'] = !empty($data['arrPaymentTracker']['vendor_bill_type_purchase']) ? $data['arrPaymentTracker']['vendor_bill_type_purchase'] : '';
            $arrPaymentDetail['vendor_bill_receipt_number'] = !empty($data['arrPaymentTracker']['vendor_bill_receipt_number']) ? $data['arrPaymentTracker']['vendor_bill_receipt_number'] : '';
            $arrPaymentDetail['vendor_bill_payment_date'] = !empty($data['arrPaymentTracker']['vendor_bill_payment_date']) ? $data['arrPaymentTracker']['vendor_bill_payment_date'] : '';

            if (!empty($arrPaymentDetail)) {
                $paymentToken = md5(time() . serialize($arrPaymentDetail));
            }
        }

        return $paymentToken;
    }

    // save invoice payment tracker
    public function saveInvoicePaymentTracker($data = array())
    {
        $arrResponse = array(
            'status' => 'error',
            'message' => array()
        );

        if (isset($data) && !empty($data['soc_id']) && !empty($data['postData'])) {

            $arrOtherInfo = array();
            if (isset($data['postData']) && !empty($data['postData'])) {

                if (strtolower($data['postData']['bill_type']) == 'member') {
                    $arrOtherInfo["member_detail"]["bank_ledger"] = $data['postData']['bank_account'];
                    if (!empty($data['postData']['writeoff_amount']) && $data['postData']['writeoff_amount'] > 0) {
                        if (!empty($data['postData']['total_particular'])) {
                            for ($i = 1; $i <= $data['postData']['total_particular']; $i++) {
                                $arrOtherInfo["writeoff_detail"]["rule_$i"] = $data["postData"]["rule_$i"];
                                $arrOtherInfo["writeoff_detail"]["particular_$i"] = $data["postData"]["particular_$i"];
                                $arrOtherInfo["writeoff_detail"]["writeoff_amount_$i"] = $data["postData"]["writeoff_amount_$i"];
                            }
                            $arrOtherInfo["writeoff_detail"]["total_particular"] = $data['postData']['total_particular'];
                        }
                    }
                } elseif (strtolower($data['postData']['bill_type']) == 'suspense') {
                    $arrOtherInfo['suspense_detail']['bank_ledger'] = $data['postData']['bank_account'];
                }
            }
            // print_r($arrOtherInfo);exit;
            $objIncomeInvoicePaymentTracker = new IncomeInvoicePaymentTracker();
            $objIncomeInvoicePaymentTracker->created_date = date('Y-m-d H:i:s');
            $objIncomeInvoicePaymentTracker->created_by = (!empty($data['postData']['user_id'])) ? $data['postData']['user_id'] : 0;
            $objIncomeInvoicePaymentTracker->soc_id = (!empty($data['soc_id'])) ? $data['soc_id'] : '';
            $objIncomeInvoicePaymentTracker->unit_id = (!empty($data['unit_id'])) ? $data['unit_id'] : '';
            $objIncomeInvoicePaymentTracker->invoice_number = $data['postData']['member_paid_invoice'];
            $objIncomeInvoicePaymentTracker->receipt_number = (!empty($data['postData']['receipt_number'])) ? $data['postData']['receipt_number'] : 0;
            $objIncomeInvoicePaymentTracker->bill_type = $data['postData']['bill_type'];
            $objIncomeInvoicePaymentTracker->payment_mode = trim($data['postData']['payment_mode']);
            $objIncomeInvoicePaymentTracker->transaction_reference = !empty($data['postData']['transaction_reference']) ? $data['postData']['transaction_reference'] : '';
            $objIncomeInvoicePaymentTracker->payment_instrument = !empty($data['postData']['payment_instrument']) ? $data['postData']['payment_instrument'] : '';
            $objIncomeInvoicePaymentTracker->received_from = $data['postData']['received_from'];
            $objIncomeInvoicePaymentTracker->transaction_charges = !empty($data['postData']['transaction_charges']) ? $data['postData']['transaction_charges'] : 0;
            $objIncomeInvoicePaymentTracker->total_due_amount = $data['postData']['total_unpaid_amount'];
            $objIncomeInvoicePaymentTracker->late_payment_charges = !empty($data['postData']['late_payment_charges']) ? $data['postData']['late_payment_charges'] : 0;
            $objIncomeInvoicePaymentTracker->writeoff_amount = !empty($data['postData']['writeoff_amount']) ? $data['postData']['writeoff_amount'] : 0;
            $objIncomeInvoicePaymentTracker->payment_amount = $data['postData']['payment_amount'];
            $objIncomeInvoicePaymentTracker->other_information = serialize($arrOtherInfo);
            $objIncomeInvoicePaymentTracker->tds_deducted = !empty($data['postData']['tds_amount']) ? $data['postData']['tds_amount'] : 0;
            $objIncomeInvoicePaymentTracker->status = 'N'; // (!empty($data['postData']['payment_mode']) && in_array(strtolower($data['postData']['payment_mode']), $this->constants['payment_mode_for_clearance'] )) ? 'P' : 'Y';
            $objIncomeInvoicePaymentTracker->updated_date = date('Y-m-d H:i:s');
            $objIncomeInvoicePaymentTracker->updated_by = !empty($data['postData']['user_id']) ? $data['postData']['user_id'] : 0;

            $objIncomeInvoicePaymentTracker->payment_date = ((!empty($data['postData']['payment_date']) && strrchr($data['postData']['payment_date'], '/')) ? $this->getDatabaseDate($data['postData']['payment_date']) : ((!empty($data['postData']['payment_date'])) ? $data['postData']['payment_date'] : $this->getCurrentDate('database')));
            $objIncomeInvoicePaymentTracker->payment_note = !empty($data['postData']['payment_note']) ? $data['postData']['payment_note'] : '';

            //Insert cheque date only in case of payment mode CHEQUE
            $objIncomeInvoicePaymentTracker->cheque_date = ((!empty($data['postData']['cheque_date']) && strrchr($data['postData']['cheque_date'], '/')) ? $this->getDatabaseDate($data['postData']['cheque_date']) : ((!empty($data['postData']['cheque_date'])) ? $data['postData']['cheque_date'] : ''));

            $objIncomeInvoicePaymentTracker->attachment_name = !empty($data['postData']['attachment_name']) ? $data['postData']['attachment_name'] : '';

            $objIncomeInvoicePaymentTracker->payment_token = !empty($data['postData']['payment_token']) ? trim($data['postData']['payment_token']) : '';
            // Update Payment status and Transaction status for facility advance payment
            if (isset($data['postData']['booked_by']) && !empty($data['postData']['booked_by']) && isset($data['postData']['bill_from']) && strtolower($data['postData']['bill_from']) == 'facility') {
                $objIncomeInvoicePaymentTracker->transaction_status = 'complete';
                if (in_array(strtolower($data['postData']['payment_mode']), $this->constants['payment_mode_for_clearance'])) {
                    $objIncomeInvoicePaymentTracker->status = 'P';
                } else {
                    $objIncomeInvoicePaymentTracker->status = 'Y';
                }
            }

            if (!$objIncomeInvoicePaymentTracker->save()) {
                $arrMessages = [];
                foreach ($objIncomeInvoicePaymentTracker->getErrors() as $error) {
                    $arrMessages[] = $error;
                }
                $arrResponse['message'] = $arrMessages;
            } else {
                $arrResponse['status'] = 'success';
                $arrResponse['payment_tracker_id'] = $objIncomeInvoicePaymentTracker->id;
            }

            unset($objIncomeInvoicePaymentTracker);
        }
        return $arrResponse;
    }

    // payment token verification
    public function paymentTokenVerification($data = array())
    {
        if (!empty($data['paymentToken'])) {
            $arrPaymentTrackerDetail = $this->getPaymentTrackerDetailByPaymentToken($data);
            if (!empty($arrPaymentTrackerDetail)) {
                if (in_array(strtolower($arrPaymentTrackerDetail['transaction_status']), array('in_process')) || in_array(strtolower($arrPaymentTrackerDetail['status']), array('y'))) {
                    return false;
                }
            } else {
                return false;
            }
        } else {
            return false;
        }
        return true;
    }

    // get payment tracker detail by payment token
    public function getPaymentTrackerDetailByPaymentToken($data = array())
    {
        $arrIncomeInvoicePaymentTracker = [];

        if (!empty($data['paymentToken'])) {
            $query = [
                ['soc_id', '=', $data['soc_id']],
                ['payment_token', '=', $data['paymentToken']]
            ];

            if (!empty($data['type']) && $data['type'] == 'expense') {
                $objIncomeInvoicePaymentTracker = ExpenseInvoicePaymentTracker::where($query)->first();
            } else {
                $objIncomeInvoicePaymentTracker = IncomeInvoicePaymentTracker::where($query)->first();
            }

            if (!empty($objIncomeInvoicePaymentTracker)) {
                $arrIncomeInvoicePaymentTracker = $objIncomeInvoicePaymentTracker->toArray();
            }
        }

        return $arrIncomeInvoicePaymentTracker;
    }

    // Update payment tracker status in income invoice payment tracker table
    public function updateIncomePaymentTrackerStatus($data = array())
    {
        $arrResponse['status'] = 'error';
        $data['updated_date'] = date('Y-m-d H:i:s');
        $updateResponse = $this->updateIncomeInvoicePaymentTrackerStatus($data);
        if (!empty($updateResponse) && strtolower($updateResponse['status']) == 'success') {
            $arrResponse['status'] = 'success';
        }
        return $arrResponse;
    }

    public function updateIncomeInvoicePaymentTrackerStatus($data, $full_list = false)
    {
        $arrResponse = ['status' => 'success', 'message' => []];

        $updateData = [
            'updated_date' => $data['updated_date']
        ];

        if (!empty($data['status'])) {
            $updateData['status'] = $data['status'];
        }
        if (!empty($data['transaction_status'])) {
            $updateData['transaction_status'] = $data['transaction_status'];
        }
        if (!empty($data['invoice_number'])) {
            $updateData['invoice_number'] = $data['invoice_number'];
        }
        if (!empty($data['payment_date'])) {
            $updateData['payment_date'] = $data['payment_date'];
        }
        if (!empty($data['other_information'])) {
            $updateData['other_information'] = $data['other_information'];
        }
        if (!empty($data['reversal_note'])) {
            $updateData['reversal_note'] = $data['reversal_note'];
        }
        if (!empty($data['unit_id'])) {
            $updateData['unit_id'] = $data['unit_id'];
        }
        if (!empty($data['bill_type'])) {
            $updateData['bill_type'] = $data['bill_type'];
        }
        if (!empty($data['total_unpaid_amount'])) {
            $updateData['total_due_amount'] = $data['total_unpaid_amount'];
        }

        $result = IncomeInvoicePaymentTracker::where('soc_id', $data['soc_id'])
            ->where('id', $data['payment_tracker_id'])
            ->update($updateData);

        if (!$result) {
            $arrResponse = ['status' => 'error', 'message' => ['Failed to update payment tracker']];
        }

        return $arrResponse;
    }

    // update unit invoice status in income unit invoice table
    public function updateUnitInvoiceStatus($data = array())
    {
        $arrResponse['status'] = 'error';
        $updateResponse = $this->updateInvoiceStatus(array(
            'soc_id' => $data['soc_id'],
            'status' => $data['status'],
            'invoice_number' => $data['invoice_number'],
            'updated_date' => date('Y-m-d')
        ));
        if (!empty($updateResponse) && strtolower($updateResponse['status']) == 'success') {
            $arrResponse['status'] = 'success';
        }
        return $arrResponse;
    }

    public function updateInvoiceStatus($data,$full_list = false)
    {
        $arrResponse = array('status'=>'success','message'=>array());

        $invoice_number = str_replace(",","','",$data['invoice_number']);

        // Updating a single column
        $result = IncomeUnitInvoice::where('soc_id', $data['soc_id'])
            ->whereIn('invoice_number', $invoice_number)
            ->update([
                'status' => $data['status'],
                'updated_date' => $data['updated_date']
            ]);

        if(!$result){
            $arrResponse = array('status'=>'error','message'=>array('Failed to update invoice status'));
        }
        
        return $arrResponse;
    }

    // income invoice payment tracker detail
    public function _invoicePaymentTracker($unit_id, $arrData = array()) 
    {
        $arrResponse = array('status' => 'error');
        $arrPostData = $arrData['arrPostData'];
        $arrPostData['user_id'] =$this->input['user_id'] ?? 0;
        $arrPostData['soc_id'] = $arrPostData['soc_id'];
        if(!empty($arrPostData)) {
            $arrIncomeInvoiceMemberDetail = $arrData['arrIncomeInvoiceMemberDetail'];
            $arrIncomeInvoiceDetail = $arrData['arrIncomeInvoiceDetail'];

            $arrInvoiceDataListener['invoice_detail'] = $arrIncomeInvoiceDetail;
            $arrInvoiceDataListener['postedData'] = $arrPostData;
            $arrReceiptDetail = array('payment_tracker_id' => $arrPostData['payment_tracker_id'], 'receipt_number' => $arrPostData['receipt_number'], 'bill_type' => $arrPostData['bill_type']);
            $oustandingAmountPaid = 0;
            
            if ((empty($arrPostData['total_unpaid_amount']) || $arrPostData['total_unpaid_amount'] <= 0) && empty($arrPostData['member_paid_invoice'])) {
                if ($arrPostData['payment_amount'] > 0) {
                    $countLedgerEntry = 0;

                    if (!empty($arrIncomeInvoiceMemberDetail)) {
                        $arrPostData['unit_id'] = $arrIncomeInvoiceMemberDetail['fk_unit_id'];
                        $arrPostData['member_id'] = $arrIncomeInvoiceMemberDetail['id'];
                    }

                    if (!empty($arrPostData['payment_date']) && strrchr($arrPostData['payment_date'], '-')) {
                        $arrPostData['payment_date'] = $arrPostData['payment_date'];
                    }

                    $arrResponse = $this->paidAdvanceInvoiceAmount(array('arrPostData' => $arrPostData, 'arrReceiptDetail' => $arrReceiptDetail));
                    if ($arrResponse['status'] == 'success') {
                        if (ACCOUNT_MODULE_EXIST == 1) {

                            $arrListnerData = array('soc_id' => $arrPostData['soc_id'], 'unit_id' => $unit_id);
                            $arrUnitDetails = $this->getUnitDetailById($arrListnerData);

                            if (!empty($arrUnitDetails)) {
                                if ($arrUnitDetails['ledger_account_id']) {
                                    $arrListnerData['ledger_id'] = $arrUnitDetails['ledger_account_id'];
                                } else {
                                    $arrListnerData['ledger_name'] = 'BLDG#' . strtoupper($arrUnitDetails['soc_building_name']) . '-' . $arrUnitDetails['unit_flat_number'];
                                    $arrListnerData['context'] = ACCOUNT_RECEIVABLE_GROUP;
                                }
                                $arrUnitLedgerDetails = $this->checkledgerExistNew($arrListnerData);

                                if (isset($arrInvoiceDataListener['postedData']['receiptFrom']) && $arrInvoiceDataListener['postedData']['receiptFrom'] == 'suspense') {

                                    $suspenseLedgerDetails['soc_id'] = $arrPostData['soc_id'];
                                    $suspenseLedgerDetails['unit_id'] = $arrPostData['unit_id'];
                                    $suspenseLedgerDetails['ledger_name'] = 'Suspense Account';
                                    $suspenseLedgerDetails['context'] = ACCOUNT_RECEIVABLE_GROUP;
                                    $arrSuspenseLedgerDetails = $this->checkledgerExistNew($suspenseLedgerDetails);

                                    $arrUnitLedgerDetails['suspenseLedgerDetails'] = $arrSuspenseLedgerDetails;
                                }

                                if (!empty($arrUnitLedgerDetails)) {
                                    $countLedgerEntry = $this->paymentLedgerEntry(array('arrPostData' => $arrPostData, 'arrUnitLedgerDetails' => $arrUnitLedgerDetails)); //get all Unit details
                                }
                            }
                        }
                        
                        // add email and sms notification for payment receipt here
                        $arrResponse = array('status' => 'success');
                    }
                }
                return $arrResponse;
            } else {
                if (isset($arrPostData['writeoff_amount']) && $arrPostData['writeoff_amount'] > 0) {
                    $arrInvoiceDataListener['postedData']['payment_amount'] += $arrPostData['writeoff_amount'];
                }
                $arrInvoicePaymentDetail = $this->setInvoicePaymentData($arrInvoiceDataListener);
                
                if (isset($arrPostData['writeoff_amount']) && $arrPostData['writeoff_amount'] > 0) {
                    $arrInvoicePaymentDetail[0]['payment_amount'] = (float) $arrInvoicePaymentDetail[0]['payment_amount'] - $arrInvoicePaymentDetail[0]['writeoff_amount'];
                    // $arrInvoicePaymentDetail['writeoff_amount']=$arrPostData['writeoff_amount'];
                }
                //get all Unit details
                //get tds deducted payment entries
                if (!empty($arrPostData['tds_amount']) && $arrPostData['tds_amount'] > 0) {
                    $arrInvoicePaymentDetail = $this->getPaymentDetailsAfterTds(array('arrInvoicePaymentDetail' => $arrInvoicePaymentDetail, 'arrPostData' => $arrPostData)); //get payment details of all settled invoices after tds deduction
                }
                if (isset($arrInvoicePaymentDetail['outstanding_payment_amount']) && !empty($arrInvoicePaymentDetail['outstanding_payment_amount'])) {
                    $arrOustandingAmountDetail = $arrInvoicePaymentDetail['outstanding_payment_amount'];
                    unset($arrInvoicePaymentDetail['outstanding_payment_amount']);
                }
            }
            // check invoice payment details
            if (!empty($arrInvoicePaymentDetail)) {

                if (!empty($arrInvoicePaymentDetail["paidInvoiceLedger"])) {
                    $arrPostData['member_paid_invoice'] = $arrInvoicePaymentDetail["paidInvoiceLedger"];
                    unset($arrInvoicePaymentDetail['paidInvoiceLedger']);
                }
                $arrInvoicePaymentResponse = $this->saveIncomeInvoicePayment(array('paymentDetail' => $arrInvoicePaymentDetail, 'arrReceiptDetail' => $arrReceiptDetail)); //get all Unit details
                if (!empty($arrInvoicePaymentResponse['success']) && count($arrInvoicePaymentDetail) == count($arrInvoicePaymentResponse['success'])) {
                    $countLedgerEntry = 0; //exit;
                    if (ACCOUNT_MODULE_EXIST == 1) {

                        $arrListnerData = array('soc_id' => $arrPostData['soc_id'], 'unit_id' => $unit_id);
                        $arrUnitDetails = $this->getUnitDetailById($arrListnerData);

                        if ($arrUnitDetails['ledger_account_id']) {
                            $arrListnerData['ledger_id'] = $arrUnitDetails['ledger_account_id'];
                        } else {
                            $arrListnerData['ledger_name'] = 'BLDG#' . strtoupper($arrUnitDetails['soc_building_name']) . '-' . $arrUnitDetails['unit_flat_number'];
                            $arrListnerData['context'] = ACCOUNT_RECEIVABLE_GROUP;
                        }

                        $arrUnitLedgerDetails = $this->checkledgerExistNew($arrListnerData);

                        if (isset($arrInvoiceDataListener['postedData']['receiptFrom']) && $arrInvoiceDataListener['postedData']['receiptFrom'] == 'suspense') {

                            $suspenseLedgerDetails['soc_id'] = $arrPostData['soc_id'];
                            $suspenseLedgerDetails['unit_id'] = $arrPostData['unit_id'];
                            $suspenseLedgerDetails['ledger_name'] = 'Suspense Account';
                            $suspenseLedgerDetails['context'] = ACCOUNT_RECEIVABLE_GROUP;
                            $arrSuspenseLedgerDetails = $this->checkledgerExistNew($suspenseLedgerDetails);

                            $arrUnitLedgerDetails['suspenseLedgerDetails'] = $arrSuspenseLedgerDetails;
                        }
                        $countLedgerEntry = $this->paymentLedgerEntry(array('arrPostData' => $arrPostData, 'arrUnitLedgerDetails' => $arrUnitLedgerDetails)); //get all Unit details
                        //ledger entries for late payment charges
                        foreach ($arrInvoicePaymentDetail as $eachInvoiceDetail) {
                            $objQueryBuiler = $this->updateIncomeInvoiceParticular(array('soc_id' => $arrPostData['soc_id'], 'arrParticularDetail' => $eachInvoiceDetail, 'updated_date' => date('Y-m-d H:i:s')));
                        }
                    }

                    // add email and sms notification for payment receipt here
                    $arrResponse = array('status' => 'success');
                }
            }
        }
        return $arrResponse;
    }

    // get paid advance invoice amount
    public function paidAdvanceInvoiceAmount($data = array())
    {
        $arrResponse = array(
            'status' => 'error'
        );
        if (!empty($data['arrPostData']) && !empty($data['arrReceiptDetail'])) {
            if (!empty($data['arrPostData']['tds_amount']) && $data['arrPostData']['tds_amount'] > 0) {
                $data['arrPostData']['payment_amount'] = (float) round($data['arrPostData']['payment_amount'] - $data['arrPostData']['tds_amount'], 3);
            }
            $arrResponse = $this->saveInvoicePaymentDetail(array(
                'paymentDetail' => $data['arrPostData'], 'arrReceiptDetail' => $data['arrReceiptDetail']
            ));
            if ($arrResponse['status'] == 'success') {

                $arrCreditData['soc_id'] = $data['arrPostData']['soc_id'];
                $arrCreditData['account_id'] = $data['arrPostData']['unit_id'];
                $arrCreditData['account_context'] = 'unit';
                $arrCreditData['transaction_type'] = 'cr';
                $arrCreditData['context'] = 'system';
                $arrCreditData['payment_tracker_id'] = $data['arrReceiptDetail']['payment_tracker_id'];
                $arrCreditData['credit_used_type'] = 'adjustable';
                $arrCreditData['used_for'] = 'maintenance';
                $arrCreditData['payment_date'] = $data['arrPostData']['payment_date'];
                $arrCreditData['narration'] = 'Amount Rs ' . $data['arrPostData']['payment_amount'] . ' has credited from advance ';
                $arrCreditData['payment_amount'] = $data['arrPostData']['payment_amount'];
                $arrCreditData['payment_mode'] = $data['arrPostData']['payment_mode_hidden'] ?? '';

                if (!empty($data['arrPostData']['member_id'])) {
                    $arrMemberDetail = $this->getPrimaryMemberById(array(
                        'soc_id' => $arrCreditData['soc_id'],
                        'member_id' => $data['arrPostData']['member_id']
                    ));
                }
                $arrCreditData['account_name'] = $arrMemberDetail['member_first_name'] . ' ' . $arrMemberDetail['member_last_name'];

                $creditAccountresponse = $this->saveCreditAccountResponse(array(
                    'auth' => true,
                    'process' => 'fetch',
                    'soc_id' => $arrCreditData['soc_id'],
                    'id' => '',
                    'data' => $arrCreditData,
                    'user' => '',
                    'username' => ''
                ));
                if ($creditAccountresponse['error']) {
                    $arrResponse['status'] = 'error';
                    $arrResponse['message'] = 'unable to create debit note';
                }
            }
        }
        return $arrResponse;
    }

    public function saveInvoicePaymentDetail($data = array())
    {
        $arrResponse = array(
            'status' => 'error'
        );

        if (isset($data['paymentDetail']) && !empty($data['paymentDetail']) && isset($data['arrReceiptDetail']) && !empty($data['arrReceiptDetail'])) {
            $objIncomeInvoicePayment = new IncomeInvoicePayment();
            $objIncomeInvoicePayment->created_date = $this->getCurrentDate('database');
            $objIncomeInvoicePayment->created_by = (!empty($data['paymentDetail']['user_id'])) ? $data['paymentDetail']['user_id'] : 0;
            $objIncomeInvoicePayment->soc_id = (!empty($data['paymentDetail']['soc_id'])) ? $data['paymentDetail']['soc_id'] : '';
            $objIncomeInvoicePayment->fk_member_id = (!empty($data['paymentDetail']['member_id'])) ? $data['paymentDetail']['member_id'] : '';
            $objIncomeInvoicePayment->fk_unit_id = (!empty($data['paymentDetail']['unit_id'])) ? $data['paymentDetail']['unit_id'] : '';
            $objIncomeInvoicePayment->fk_unit_invoice_id = (!empty($data['paymentDetail']['unit_invoice_id'])) ? $data['paymentDetail']['unit_invoice_id'] : 0;
            // $objIncomeInvoicePayment->fk_rule_id = (!empty($eachInsertData['rule_id'])) ? $eachInsertData['rule_id'] : '';
            $objIncomeInvoicePayment->invoice_number = (!empty($data['paymentDetail']['invoice_number'])) ? $data['paymentDetail']['invoice_number'] : 0;
            $objIncomeInvoicePayment->receipt_number = $data['arrReceiptDetail']['receipt_number'];
            // $objIncomeInvoicePayment->particular = $eachInsertData['particular'];

            $objIncomeInvoicePayment->payment_amount = $data['paymentDetail']['payment_amount'];
            $objIncomeInvoicePayment->tds_deducted = (!empty($data['paymentDetail']['tds_amount']) && $data['paymentDetail']['tds_amount'] > 0) ? $data['paymentDetail']['tds_amount'] : 0;
            $objIncomeInvoicePayment->writeoff_amount = (!empty($data['paymentDetail']['writeoff_amount']) && $data['paymentDetail']['writeoff_amount'] > 0) ? $data['paymentDetail']['writeoff_amount'] : 0;
            $objIncomeInvoicePayment->payment_mode = $data['paymentDetail']['payment_mode'];
            $objIncomeInvoicePayment->transaction_reference = !empty($data['paymentDetail']['transaction_reference']) ? $data['paymentDetail']['transaction_reference'] : '';
            $objIncomeInvoicePayment->transaction_charges = !empty($data['paymentDetail']['transaction_charges']) ? $data['paymentDetail']['transaction_charges'] : 0;
            $objIncomeInvoicePayment->late_payment_charges = !empty($data['paymentDetail']['late_payment_charges']) ? $data['paymentDetail']['late_payment_charges'] : 0;
            $objIncomeInvoicePayment->payment_instrument = !empty($data['paymentDetail']['payment_instrument']) ? $data['paymentDetail']['payment_instrument'] : '';
            // $objIncomeInvoicePayment->received_from = $data['paymentDetail']['received_from'];
            // $objIncomeInvoicePayment->optional_particular = $eachInsertData['optional_particular'];
            // $objIncomeInvoicePayment->optional_particular_amount = $eachInsertData['optional_particular_amount'];
            $objIncomeInvoicePayment->status = 'success';
            // $objIncomeInvoicePayment->tax_exemptions = $eachInsertData['tax_exemptions'];
            // $objIncomeInvoicePayment->booking_ids = $eachInsertData['booking_ids']; //for common area
            $objIncomeInvoicePayment->updated_date = date('Y-m-d');
            $objIncomeInvoicePayment->updated_by = 0;

            $objIncomeInvoicePayment->payment_date = !empty($data['paymentDetail']['payment_date']) ? $this->getDatabaseDate($data['paymentDetail']['payment_date']) : $this->getCurrentDate('database');

            if (!$objIncomeInvoicePayment->save()) {
                foreach ($objIncomeInvoicePayment->getMessages() as $messages) {
                    $arrMessages[] = (string) $messages;
                }
                $arrResponse['message'] = $arrMessages;
            } else {
                $arrResponse = array(
                    'status' => 'success'
                );
            }
        }
        return $arrResponse;
    }

    // check ledger exist or not
    public function checkledgerExistNew($data=array())
    {
        $arrClinetLedgerDetails = array();
        $query = ChsoneGrpLedgerTree::where('soc_id', $data['soc_id']);
        
        if (isset($data['ledger_name']) && !empty($data['ledger_name'])) {
            $query->where('context', $data['context'])
                  ->where('entity_type', "ledger")
                  ->where('ledger_account_name', $data['ledger_name']);
    
            if (isset($data['behaviour']) && !empty($data['behaviour'])) {
                $query->where('behaviour', $data['behaviour']);
            }
        } elseif (isset($data['ledger_id']) && !empty($data['ledger_id'])) {
            $query->where('ledger_account_id', $data['ledger_id']);
        }

        $objBookerLedger = $query->first();

        //an outsider has booked for the society. Check if exists by name; otherwise, create new.
        if(!empty($objBookerLedger)) {
            $arrClinetLedgerDetails['recieving_ledger_id'] = $objBookerLedger->ledger_account_id;
            $arrClinetLedgerDetails['receiver_name'] = $objBookerLedger->ledger_account_name;
        }
        elseif(!empty($data['group_name']))
        {
            $name         = $data['ledger_name'];
            $entity_type  = "ledger";
            $grp_ledg_id  = "";
            $parent       = $this->getParentGroupId($data, TRUE);
            
            if(empty($parent->ledger_account_id))
            {
                return $arrClinetLedgerDetails;
            }
            $parent_group = $parent->ledger_account_id;
            
            $ledger_id    = $this->manipulate($name, $entity_type, $grp_ledg_id , $parent_group, "", '', 0, '', '', '', '', '', 0, $data['soc_id']);
            if ($ledger_id) {
                $arrClinetLedgerDetails['recieving_ledger_id'] = $ledger_id;
                $arrClinetLedgerDetails['receiver_name'] = $data['ledger_name'];
            }
        }
        unset($objBookerLedger);
        return $arrClinetLedgerDetails;
    }

    public function getParentGroupId($arrData)
    {
        $parent = ChsoneGrpLedgerTree::where('soc_id', $arrData['soc_id'])
            ->where('ledger_account_name', $arrData['group_name'])
            ->where('entity_type', "group")
            ->where('behaviour', $arrData['behaviour'])
            ->where('context', $arrData['context'])
            ->first();
        if ($parent) {
            return $parent;
        } else {
            return false;
        }
    }

    public function manipulate($name, $entity_type = ENTITY_TYPE_GROUP, $grp_ledg_id = "", $parent_group = 0, $behaviour = "", $ledg_id = '', $opening_balance = 0, $update_led_id = '', $ledger_start_date = '', $ledger_type = '', $context = '', $txn_id = 0, $soc_id = 0, $is_reco = 0)
    {
        $config = Config::get("constants");
        if(empty($soc_id)){
            $soc_id = $this->input['company_id'];
        }
        $dup = $this->_checkGroupNameDuplication($parent_group, $name, $ledg_id, $update_led_id);
        
        $arrFYDetail = $this->getCurrentFinancialYear(array('soc_id' => $soc_id));
        $arrFYDetail = json_decode(json_encode($arrFYDetail), true);
        $ledger_start_date = $arrFYDetail['fy_start_date'];
        if ($dup == 0) {
            $behaviour = trim(strtolower($behaviour));

            $grp_ledg_tree = new ChsoneGrpLedgerTree();

            if ($ledg_id) {
                $grp_ledg_tree = $this->getLedger($ledg_id);
            }

            $grp_ledg_tree->entity_type = $entity_type;
            $grp_ledg_tree->soc_id = $soc_id;
            $grp_ledg_tree->ledger_account_name = $name;
            $grp_ledg_tree->ledger_start_date = $ledger_start_date;
            $grp_ledg_tree->context_ref_id = 0;

            if (!empty($ledger_type) && $ledger_type != "NULL" && $ledger_type != '') {
                $grp_ledg_tree->operating_type = $ledger_type;
            } else {
                $grp_ledg_tree->operating_type = '';
            }

            if (!empty($grp_ledg_id)) {
                $grp_ledg_tree->ledger_account_id = $grp_ledg_id;
            }

            if ($update_led_id != '') {
                $grp_ledg_tree->ledger_account_id = $update_led_id;
            }
            if (!empty($parent_group)) {
                $grp_ledg_tree->parent_id = $parent_group;
                $ledger_props = $this->_getLedgerProps($parent_group);
            } else {
                $grp_ledg_tree->parent_id = 0;
            }

            if (!empty($behaviour)) {
                $grp_ledg_tree->behaviour = $behaviour;
                $grp_ledg_tree->nature_of_account = $config->nature_account->$behaviour;
                $grp_ledg_tree->report_head = $config->report_head->$behaviour;
            } else {
                $grp_ledg_tree->behaviour = $ledger_props["behaviour"];
                $grp_ledg_tree->nature_of_account = $ledger_props["nature_account"];
                $grp_ledg_tree->report_head = $ledger_props["report_head"];
            }

            if (!empty($context) && $context != '') {
                $grp_ledg_tree->context = $context;
            } else {
                $grp_ledg_tree->context = $ledger_props["context"];
            }
            $grp_ledg_tree->defined_by = "user";
            $grp_ledg_tree->status = 1;
            $grp_ledg_tree->added_on = date("Y-m-d H:i:s");
            $grp_ledg_tree->created_by = ($this->input['user_id']) ? $this->input['user_id'] : 0;


            if ($grp_ledg_tree->save()) {
                //Opening balance should not go in case on Income Expense ledger
                if (!in_array(strtolower($grp_ledg_tree->behaviour), array("income", "expense"))) {
                    $txn_date = $ledger_start_date;
                    $narration = 'entry for opening balance';
                    $txn_id = $this->addTxn($grp_ledg_tree->ledger_account_id, $opening_balance, $narration, $txn_date, "", $grp_ledg_tree->nature_of_account, "", "", "", "", $name, $is_opning = 1, $is_reco);
                }

                return $grp_ledg_tree->ledger_account_id;
            } else {
                return false;
            }
        } else {
            return "DUP" . $dup;
        }
    }

    private function _checkGroupNameDuplication($parent, $name, $ledg_id = "", $update_led_id = "")
    {

        $soc_id = $this->input['company_id'];

        $bind[1] = strtolower(trim($name));
        $ledgers_count = 0;
        if ($ledg_id) {
            $ledgers_count = ChsoneGrpLedgerTree::where("soc_id", $soc_id)->where('ledger_account_name', strtolower(trim($name)))->where('ledger_account_id', '!=', $ledg_id)->count();
        }
        if ($update_led_id) {
            $ledgers_count = ChsoneGrpLedgerTree::where("soc_id", $soc_id)->where('ledger_account_name', strtolower(trim($name)))->where('ledger_account_id', '!=', $update_led_id)->count();
        }
        
        return $ledgers_count;
    }

    public function getCurrentFinancialYear($data=array()){
       
        $arrFYDetail = array();
        $arrFYDetail = SocAccountFinancialYearMaster::where("soc_id", $data['soc_id'])->where("confirmed", 0)->orderBy("account_closing_id", "asc")->first();
        
        if(!empty($arrFYDetail))
        {
            $arrFYDetail = $arrFYDetail->toArray();
        }
        return $arrFYDetail;
    }

    public function getLedger($ledger_acct_id, $return_object = 1)
    {
        //fetch ledger details from chsone_grp_ledger_tree whose ledger_account_id = $ledger_acct_id
        $ledger = ChsoneGrpLedgerTree::where("ledger_account_id", $ledger_acct_id)->first();

        if ($ledger) {
            if ($return_object) {
                return $ledger;
            } else {
                return $ledger->toArray();
            }
        } else {
            return false;
        }
    }

    public function _getLedgerProps($parent_group)
    {
        $array = array();

        if (is_numeric($parent_group)) {
            $ledger_props = ChsoneGrpLedgerTree::select('ledger_account_name', 'nature_of_account', 'behaviour', 'report_head', 'context')
            ->where('ledger_account_id', $parent_group)
            ->first();
        }

        $array = $ledger_props;
        return $array;
    }

    public function addTxn($ledger_id, $txn_amt, $narration, $txn_date, $voucher_type = VOUCHER_JOURNAL, $txn_type = "", $from_txn_id = "", $pay_mode = "", $pay_ref = "", $opening_balance = "", $ledger_name = "", $is_opning = 1, $is_reco = 0)
    {
        return $this->_addTransaction($is_reco, $ledger_id, $txn_amt, $narration, $txn_date, $voucher_type, $txn_type, $from_txn_id, $pay_mode, $pay_ref, $ledger_name, $is_opning = 1);
    }

    private function _addTransaction($is_reco = 0, $ledger_id, $txn_amt, $narration, $txn_date, $voucher_type = VOUCHER_JOURNAL, $txn_type = "", $from_txn_id = "", $pay_mode = "", $pay_ref = "", $name = "", $is_opning = "", $other_recp_ref = "", $soc_id = "", $created_by = "", $voucher_reference_id = "", $voucher_reference_number = "", $is_cancelled = 0)
    {
        $txn = new ChsoneLedgerTransaction();
        if (empty($soc_id)) {
            $soc_id = $this->input['company_id'];
        }

        if (!empty($txn_type) || $txn_type != "") {
            $type_txn = $txn_type;
        } else {
            if ($from_txn_id) {
                $mode     = "to";
                $type_txn = "cr";
            } else {
                $mode     = "from";
                $type_txn = "dr";
            }
        }

        //echo "s_opning".$is_opning;exit;
        if ($is_opning == 1) {

            $txn_entrt = ChsoneLedgerTransaction::where('soc_id', $soc_id)
            ->where('is_opening_balance', 1)
            ->where('ledger_account_id', $ledger_id)
            ->where('transaction_date', $txn_date)
            ->first();

            $txn->value_date = $txn_date;
            $txn->txn_id = (isset($txn_entrt->txn_id)  && !empty($txn_entrt->txn_id)) ? $txn_entrt->txn_id : '';
            if (!empty($txn->txn_id)) {
                $txn = ChsoneLedgerTransaction::findFirst("txn_id = " . $txn->txn_id);
            }
        }

        $txn->soc_id = $soc_id;
        $txn->transaction_date = $txn_date;
        $txn->ledger_account_id = $ledger_id;
        $txn->ledger_account_name = $name;
        $txn->voucher_type = $voucher_type;
        $txn->transaction_type = $type_txn; //$ledger_name_obj->nature_of_account;//$txn_type;
        $txn->payment_mode = $pay_mode;
        $txn->payment_reference = $pay_ref;
        $txn->transaction_amount = (is_null($txn_amt)) ? $txn->transaction_amount : $txn_amt;
        $txn->other_reference_id = $other_recp_ref;
        $txn->txn_from_id = $from_txn_id;
        $txn->memo_desc = $narration;
        $txn->is_opening_balance = $is_opning;
        $txn->is_reconciled = $is_reco;
        /*Added New Column in transaction*/
        $txn->voucher_reference_number = $voucher_reference_number;
        $txn->voucher_reference_id = $voucher_reference_id;
        $txn->is_cancelled = $is_cancelled;
        /*End New Column in transaction*/
        $txn->created_by = $this->input['user_id'];
        if (isset($created_by) && !empty($created_by)) {
            $txn->created_by = $created_by;
        }
        $txn->added_on = date("Y-m-d H:i:s");

        if ($txn->save()) {
            return $txn->txn_id;
        } else {
            print_r($txn->getMessages);
        }
        //echo "<pre>";print_r($a->getMessages);exit;
        return false;
    }

    public function paymentLedgerEntry($data = array())
    {
        $countLedgerEntry = 0;
        if (ACCOUNT_MODULE_EXIST == 1) {
            $arrPostData = $data['arrPostData'];
            $arrPostData['company_id'] = $arrPostData['soc_id'];
            $arrUnitLedgerDetails = $data['arrUnitLedgerDetails'];

            if (isset($arrPostData['clearance_date']) && !empty($arrPostData['clearance_date'])) {
                $arrPostData['payment_date'] = $arrPostData['clearance_date'];
            }

            if (empty($arrPostData['payment_date'])) {
                $arrPostData['payment_date'] = date('Y-m-d');
            }

            if (!empty($arrPostData['payment_mode']) && !in_array(strtolower($arrPostData['payment_mode']), $this->constants['payment_mode_for_receipt_reversal'])) {
                //  print_r(array(trim($arrPostData['payment_instrument']) . '_PG_LEDGER_ID'));
                $arrInvoiceGeneralSetting = $this->getInvoiceGeneralSetting(array('soc_id' => $arrPostData['company_id'], 'setting_key' => array(trim($arrPostData['payment_instrument']) . '_PG_LEDGER_ID')));

                if (!empty($arrInvoiceGeneralSetting[0]['setting_value'])) {
                    $arrIncomeAccounts = $this->checkledgerExistNew(array('soc_id' => $arrPostData['company_id'], 'ledger_id' => $arrInvoiceGeneralSetting[0]['setting_value']));
                    $arrIncomeAccounts['ledger_id'] = $arrIncomeAccounts['recieving_ledger_id'];
                    $arrIncomeAccounts['ledger_name'] = $arrIncomeAccounts['receiver_name'];
                } else {
                    if (!empty($data['to_ledger'])) {
                        $arrIncomeAccounts['ledger_id'] = $data['to_ledger']['recieving_ledger_id'];
                        $arrIncomeAccounts['ledger_name'] = $data['to_ledger']['receiver_name'];
                    } else {
                        return 1;
                    }
                }
            } else {
                if (isset($data['arrUnitLedgerDetails']['suspenseLedgerDetails']) && !empty($data['arrUnitLedgerDetails']['suspenseLedgerDetails'])) {
                    $arrIncomeAccounts['ledger_id'] = $data['arrUnitLedgerDetails']['suspenseLedgerDetails']['recieving_ledger_id'];
                    $arrIncomeAccounts['ledger_name'] = $data['arrUnitLedgerDetails']['suspenseLedgerDetails']['receiver_name'];
                } else {
                    // Getting bank/cash ledger details
                    $arrAccountDetail = $this->getBankCashAccountDetail(array(
                        'soc_id' => $arrPostData['company_id']
                    )); // get all Unit details
                    $arrLedgerAccountDetail = $this->getLedgerAccountDetail(array(
                        'account_detail' => $arrAccountDetail
                    )); // get all Unit details
                    if (!empty($arrLedgerAccountDetail)) {
                        if (!empty($arrLedgerAccountDetail['cash']['ledger_id'])) {
                            $arrIncomeAccounts['ledger_id'] = $arrLedgerAccountDetail['cash']['ledger_id'];
                            $arrIncomeAccounts['ledger_name'] = $arrLedgerAccountDetail['cash']['ledger_name'];
                        } elseif (!empty($arrLedgerAccountDetail['bank']['ledger_id'])) {
                            $arrIncomeAccounts['ledger_id'] = $arrLedgerAccountDetail['bank']['ledger_id'];
                            $arrIncomeAccounts['ledger_name'] = $arrLedgerAccountDetail['bank']['ledger_name'];
                        } else {
                            // Handle error: No valid ledger_id found
                            throw new \Exception('No valid cash or bank ledger_id found');
                        }

                        if (strtolower($arrPostData['payment_mode']) != 'cash') {
                            if (!empty($arrPostData['bank_account']) && !empty($arrLedgerAccountDetail['arrBank'][$arrPostData['bank_account']])) {
                                $arrIncomeAccounts['ledger_id'] = $arrPostData['bank_account'];
                                $arrIncomeAccounts['ledger_name'] = $arrLedgerAccountDetail['arrBank'][$arrPostData['bank_account']];
                            } elseif (!empty($arrLedgerAccountDetail['bank']['ledger_id'])) {
                                $arrIncomeAccounts['ledger_id'] = $arrLedgerAccountDetail['bank']['ledger_id'];
                                $arrIncomeAccounts['ledger_name'] = $arrLedgerAccountDetail['bank']['ledger_name'];
                            } else {
                                // Handle error: No valid bank ledger_id found
                                throw new \Exception('No valid bank ledger_id found');
                            }
                        }
                    }
                }
            }

            if ($arrPostData['payment_mode'] == 'cashtransfer') {
                $paymentModeForNarration = DISPLAY_CASH_TRANSFER;
            } else {
                $paymentModeForNarration = $arrPostData['payment_mode'];
            }

            // $paymentModeForNarration = $arrPostData ['payment_mode'];
            $strNarration = '';
            $arrPostData['transaction_reference'] = !empty($arrPostData['transaction_reference']) ? $arrPostData['transaction_reference'] : '';
            $arrPostData['payment_instrument'] = !empty($arrPostData['payment_instrument']) ? $arrPostData['payment_instrument'] : '';
            if (!empty($arrPostData['payment_mode']) && in_array(strtolower($arrPostData['payment_mode']), $this->constants['payment_mode_for_clearance'])) {
                $strNarration = ' with transaction ref. (' . $arrPostData['transaction_reference'] . ', ' . $arrPostData['payment_instrument'] . ')';
            } elseif (!empty($arrPostData['payment_mode']) && strtolower($arrPostData['payment_mode']) == 'cashtransfer') {
                $strNarration = ' with payment ref. (' . $arrPostData['transaction_reference'] . ')';
            } elseif (!empty($arrPostData['payment_mode']) && !in_array(strtolower($arrPostData['payment_mode']), $this->constants['payment_mode_for_receipt_reversal'])) {
                $strNarration = ' with transaction ref. (' . $arrPostData['transaction_reference'] . ')';
                switch ($arrPostData['payment_mode']) {
                    case 'YES_BANK_PG':
                        $paymentModeForNarration = DISPLAY_YES_BANK;
                        break;
                    case 'YES_BANK_ECOLLECT':
                        $paymentModeForNarration = DISPLAY_YES_BANK_ECOLLECT;
                        break;
                    case 'MOBIKWIK_WALLET':
                        $paymentModeForNarration = DISPLAY_MOBIKWIK_WALLET;
                        break;
                    case 'MOBIKWIK_PG':
                        $paymentModeForNarration = DISPLAY_MOBIKWIK_PG;
                        break;
                    case 'CASHFREE_PG':
                        $paymentModeForNarration = DISPLAY_CASHFREE_PG;
                        break;
                    case 'PAYTM_PG':
                        $paymentModeForNarration = DISPLAY_PAYTM;
                        break;
                    case 'HDFC_PG':
                        $paymentModeForNarration = DISPLAY_HDFC_PG;
                        break;
                    case 'ATOM_PG':
                        $paymentModeForNarration = DISPLAY_ATOM_PG;
                        break;
                    case 'cashtransfer':
                        $paymentModeForNarration = DISPLAY_CASH_TRANSFER;
                        break;
                }
            }
            // echo $arrIncomeInvoicePaymentTracker['payment_mode'];exit;
            // Ledger entry for payment amount
            $arrLedgerTransactionData['soc_id'] = $arrPostData['soc_id'];
            $arrLedgerTransactionData['user_id'] = $arrPostData['user_id'];
            $arrLedgerTransactionData['voucher_type'] = isset($arrPostData['voucher_type_credit']) ? VOUCHER_CREDIT : VOUCHER_RECEIPT;
            $arrLedgerTransactionData['voucher_reference_number'] = $arrPostData['receipt_number'];
            $arrLedgerTransactionData['voucher_reference_id'] = $arrPostData['payment_tracker_id'];
            $arrLedgerTransactionData['from_ledger_id'] = $arrIncomeAccounts['ledger_id'];
            $arrLedgerTransactionData['to_ledger_id'] = $arrUnitLedgerDetails['recieving_ledger_id'];
            $arrLedgerTransactionData['transaction_date'] = !empty($arrPostData['payment_date']) ? $this->getDatabaseDate($arrPostData['payment_date']) : $this->getCurrentDate('database');
            $arrLedgerTransactionData['transaction_amount'] = $arrPostData['payment_amount'];

            $arrLedgerTransactionData['narration'] = 'Amount received against Invoice ' . $arrPostData['member_paid_invoice'] . ' dated ' . $this->getDisplayDate($arrPostData['payment_date']) . ' through ' . $paymentModeForNarration . $strNarration;
            if ((empty($arrPostData['total_unpaid_amount']) || $arrPostData['total_unpaid_amount'] <= 0) && empty($arrPostData['member_paid_invoice'])) {
                $arrLedgerTransactionData['narration'] = 'Advance payment received from ' . ucwords($arrPostData['received_from']) . ' dated ' . $this->getDisplayDate($arrPostData['payment_date']) . ' through ' . $paymentModeForNarration . $strNarration;
            }
            if ($arrPostData['bill_type'] == 'suspense') {
                $arrLedgerTransactionData['narration'] = 'Unidenfied payment from ' . ucwords($arrPostData['received_from']) . ' dated ' . $this->getDisplayDate($arrPostData['payment_date']) . ' through ' . $paymentModeForNarration . $strNarration;
            }

            $arrLedgerTransactionData['from_ledger_name'] = $arrIncomeAccounts['ledger_name'];
            $arrLedgerTransactionData['to_ledger_name'] = $arrUnitLedgerDetails['receiver_name'];
            $arrLedgerTransactionData['payment_reference'] = !empty($arrPostData['transaction_reference']) ? $arrPostData['transaction_reference'] : '';
            $arrLedgerTransactionData['transaction_type'] = '';
            $arrLedgerTransactionData['mode_of_payment'] = $arrPostData['payment_mode'];
            $arrLedgerTransactionData['other_payment_ref'] = '';
            // Code to replace from ledger id From Bank/Cash
            if (isset($data['from_ledger'])) {
                $arrLedgerTransactionData['from_ledger_id'] = $data['from_ledger']['recieving_ledger_id'];
                $arrLedgerTransactionData['from_ledger_name'] = $data['from_ledger']['receiver_name'];
            }
            if (isset($data['to_ledger'])) {
                $arrLedgerTransactionData['to_ledger_id'] = $data['to_ledger']['recieving_ledger_id'];
                $arrLedgerTransactionData['to_ledger_name'] = $data['to_ledger']['receiver_name'];
            }
            if (isset($data['narration'])) {
                $arrLedgerTransactionData['narration'] = $data['narration'];
            }
            $arrLedgerTransactionData['narration'] = '(Receipt No-' . $arrPostData['receipt_number'] . ') ' . $arrLedgerTransactionData['narration'];
            if (!empty($arrPostData['payment_note'])) {
                $arrLedgerTransactionData['narration'] .= ' [' . $arrPostData['payment_note'] . ']';
            }

            //echo 'pay'; print_r($arrLedgerTransactionData); exit();
            if (!empty($arrPostData['tds_amount']) && $arrPostData['tds_amount'] > 0) {
                $arrLedgerTransactionData['transaction_amount'] = round($arrPostData['payment_amount'] - $arrPostData['tds_amount'], 2);
                $arrLedgerEntry = $this->transactionLedgerEntry($arrLedgerTransactionData);
                if (isset($arrLedgerEntry['error']) && !empty($arrLedgerEntry['error'])) {
                    $countLedgerEntry++;
                } else {
                    $arrLedgerTransactionData['transaction_amount'] = $arrPostData['tds_amount'];
                    $arrListnerData['soc_id'] = $arrPostData['soc_id'];
                    $arrListnerData['ledger_name'] = 'tds receivable';
                    $arrListnerData['context'] = ''; // CURRENT_ASSETS_GROUP;
                    $arrListnerData['behaviour'] = ASSET;

                    $arrTdsLedger = $this->checkledgerExistNew($arrListnerData);
                    $arrLedgerTransactionData['from_ledger_id'] = $arrTdsLedger['recieving_ledger_id'];
                    $arrLedgerTransactionData['from_ledger_name'] = $arrTdsLedger['receiver_name'];
                    $arrLedgerTransactionData['narration'] = 'TDS amount deducted against Invoice ' . $arrPostData['member_paid_invoice'] . ' dated ' . $this->getDisplayDate($arrPostData['payment_date']) . ' through ' . $paymentModeForNarration . $strNarration;

                    $arrLedgerEntry = $this->transactionLedgerEntry($arrLedgerTransactionData);
                    if (isset($arrLedgerEntry['error']) && !empty($arrLedgerEntry['error'])) {
                        $countLedgerEntry++;
                    }
                }
            } else {
                $arrLedgerEntry = $this->transactionLedgerEntry($arrLedgerTransactionData);
                if (isset($arrLedgerEntry['error']) && !empty($arrLedgerEntry['error'])) {
                    $countLedgerEntry++;
                }
            }
            //Ledger entry for invoice writeoff
            if (!empty($arrPostData['writeoff_amount']) && $arrPostData['writeoff_amount'] > 0) {
                if ($arrPostData['writeoff_amount'] >= 1000) {
                    $arrExpenseWriteOff = $this->checkledgerExistNew(array('soc_id' => $arrPostData['soc_id'], 'ledger_name' => 'Expense Bad Debt', 'context' => EXPENSE, 'behaviour' => EXPENSE));
                    if (empty($arrExpenseWriteOff['recieving_ledger_id'])) {
                        $arrParentExpense = $this->getLedgerDetail(array('soc_id' => $arrPostData['soc_id'], 'ledger_name' => 'Indirect Expense', 'context' => EXPENSE, 'behaviour' => EXPENSE));
                        if (!empty($arrParentExpense['ledger_account_id'])) {
                            $arrExpenseWriteOff['receiver_name'] = 'Expense Bad Debt';
                            $arrExpenseWriteOff['recieving_ledger_id'] = $this->createNewLedgerExit(array('soc_id' => $arrPostData['soc_id'], 'ledger_name' => $arrExpenseWriteOff['receiver_name'], 'context' => EXPENSE, 'behaviour' => EXPENSE, 'parent_id' => $arrParentExpense['ledger_account_id']));
                        }
                    }
                } else {
                    $arrExpenseWriteOff = $this->checkledgerExistNew(array('soc_id' => $arrPostData['soc_id'], 'ledger_name' => 'Expense Write Off', 'context' => EXPENSE, 'behaviour' => EXPENSE));
                }
                if (empty($arrExpenseWriteOff['recieving_ledger_id'])) {
                    return 1;
                }
                $arrLedgerTransactionData['transaction_amount'] = $arrPostData['writeoff_amount'];
                $arrLedgerTransactionData['from_ledger_id'] = $arrExpenseWriteOff['recieving_ledger_id'];
                $arrLedgerTransactionData['from_ledger_name'] = $arrExpenseWriteOff['receiver_name'];

                $arrLedgerTransactionData['narration'] = 'Amount writeoff against Invoice ' . $arrPostData['member_paid_invoice'] . ' dated ' . $this->getDisplayDate($arrPostData['payment_date']);
                $arrLedgerEntry = $this->transactionLedgerEntry($arrLedgerTransactionData);
                if (isset($arrLedgerEntry['error']) && !empty($arrLedgerEntry['error'])) {
                    $countLedgerEntry++;
                }
            }
        }
        return $countLedgerEntry;
    }

    public function getInvoiceGeneralSetting($data = array())
    {
        $arrInvoiceGeneralSetting = array();
        $objInvoiceGeneralSetting = IncomeInvoiceGeneralSetting::where('soc_id', $data['soc_id'])->whereIn('setting_key', $data['setting_key'])->get();
        if (!empty($objInvoiceGeneralSetting)) {
            $arrInvoiceGeneralSetting = $objInvoiceGeneralSetting->toArray();
        }
        return $arrInvoiceGeneralSetting;
    }

    public function getBankCashAccountDetail($data = array())
    {
        $arrAccountDetail = array();

        $query = $this->tenantDB()->table('chsone_grp_ledger_tree as grpLedger')
        ->select(
            'grpLedger.ledger_account_id',
            'grpLedger.ledger_account_name',
            'grpLedger.context',
            'account.default_account',
            'account.default_bank_for_incidental',
            'account.default_bank_for_nonmember',
            'account.bank_name',
            'account.account_number',
            'account.bank_address',
            'account.bank_city',
            'account.bank_ifsc',
            'account.account_id'
        )
        ->join('chsone_accounts_master as account', 'grpLedger.ledger_account_id', '=', 'account.ledger_account_id')
        ->where('grpLedger.soc_id', $data['soc_id'])
        ->where('grpLedger.entity_type', 'ledger')
        ->where('grpLedger.status', 1);


        if (isset($data['only_bank'])) {
            $query->where('grpLedger.context', 'bank');
        } else {
            $query->whereIn('grpLedger.context', ['cash', 'bank']);
        }

        // Execute the query
        $resultset = $query->get();
        $arrAccountDetail = json_decode(json_encode($resultset), true);
        return $arrAccountDetail;
    }

    public function getLedgerAccountDetail($data = array())
    {
        $arrAccountDetail = array(
            'cash' => array(),
            'bank' => array(),
            'arrBank' => array()
        );
        if (!empty($data['account_detail'])) {
            foreach ($data['account_detail'] as $eachAccountDetail) {
                if (empty($arrAccountDetail['cash']) && strtolower($eachAccountDetail['context']) == 'cash') {
                    $arrAccountDetail['cash']['ledger_id'] = $eachAccountDetail['ledger_account_id'];
                    $arrAccountDetail['cash']['ledger_name'] = $eachAccountDetail['ledger_account_name'];
                    $arrAccountDetail['arrCash'][$eachAccountDetail['ledger_account_id']] = $eachAccountDetail['ledger_account_name'];
                } elseif (strtolower($eachAccountDetail['context']) == 'bank') {
                    if ($eachAccountDetail['default_account'] == 1) {
                        $arrAccountDetail['bank']['ledger_id'] = $eachAccountDetail['ledger_account_id'];
                        $arrAccountDetail['bank']['ledger_name'] = $eachAccountDetail['ledger_account_name'];
                    }
                    $arrAccountDetail['arrBank'][$eachAccountDetail['ledger_account_id']] = $eachAccountDetail['ledger_account_name'];
                }
                if (isset($data['default_bank_incidental']) && $data['default_bank_incidental'] && $eachAccountDetail['default_bank_for_incidental'] == 1) {
                    $arrAccountDetail['bank']['ledger_id'] = $eachAccountDetail['ledger_account_id'];
                    $arrAccountDetail['bank']['ledger_name'] = $eachAccountDetail['ledger_account_name'];
                }
                if (isset($data['default_bank_nonmember']) && $data['default_bank_nonmember'] && $eachAccountDetail['default_bank_for_nonmember'] == 1) {
                    $arrAccountDetail['bank']['ledger_id'] = $eachAccountDetail['ledger_account_id'];
                    $arrAccountDetail['bank']['ledger_name'] = $eachAccountDetail['ledger_account_name'];
                }
            }
            if (empty($arrAccountDetail['bank'])) {
                foreach ($arrAccountDetail['arrBank'] as $key => $value) {
                    $arrAccountDetail['bank']['ledger_id'] = $key;
                    $arrAccountDetail['bank']['ledger_name'] = $value;
                    break;
                }
            }
        }

        return $arrAccountDetail;
    }

    public function transactionLedgerEntry($data=array())
    {
        $arrResponse = array();
        if(empty(trim($data['from_ledger_id'])) || empty(trim($data['to_ledger_id'])))
        {
            $arrResponse = array('error'=>true);
            return $arrResponse;
        }
        if(empty($data['transaction_date']))
        {
            $data['transaction_date'] = $this->getCurrentDate('database');
        }

        $transaction_id = $this->executeTransactionEntry($data); 
        if($transaction_id){
            $arrResponse = array('success'=>true, 'transaction_id'=>$transaction_id);
        }else{
            $arrResponse = array('error'=>true);
        }
        
        return $arrResponse;
    }

    public function executeTransactionEntry($data)
    {
        $vouchers_array = [];
        if ((strpos($data['voucher_type'], '_') !== false) && (strpos($data['voucher_type'], '_') > 0)) {
            $vouchers_array = explode('_', $data['voucher_type']);
            $data['voucher_type'] = $vouchers_array[0];
        }

        $data['is_opening'] = 0;
        $data['transaction_from_id'] = '';
        $data['transaction_from_id'] = $this->_addTransactionEntry($data);

        if (!empty($data['transaction_from_id'])) {
            if (count($vouchers_array) >= 2) {
                $data['voucher_type'] = $vouchers_array[1];
            }
            if ($this->_addTransactionEntry($data)) {
                return $data['transaction_from_id'];
            } else {
                return false;
            }
        } else {
            return false;
        }
    }

    private function _addTransactionEntry($data)
    {
        $txn = new ChsoneLedgerTransaction();
        if (!empty($data['transaction_from_id'])) {
            $mode = "to";
            $data['transaction_type'] = "cr";
            $ledger_id = $data['to_ledger_id'];
            $ledger_name = $data['to_ledger_name'];
        } else {
            $mode = "from";
            $data['transaction_type'] = "dr";
            $ledger_id = $data['from_ledger_id'];
            $ledger_name = $data['from_ledger_name'];
        }
        //echo "s_opning".$is_opning;exit;
        if (!empty($data['is_opening']) && $data['is_opening'] == 1) {
            $txn_entrt = ChsoneLedgerTransaction::where('soc_id', $data['soc_id'])
                ->where('is_opening_balance', 1)
                ->where('ledger_account_id', $ledger_id)
                ->first();

            $txn->txn_id = (isset($txn_entrt->txn_id)  && !empty($txn_entrt->txn_id)) ? $txn_entrt->txn_id : '';
        }
        $txn->soc_id = $data['soc_id'];
        $txn->transaction_date = $data['transaction_date'];
        $txn->ledger_account_id = $ledger_id;
        $txn->ledger_account_name = $ledger_name;
        $txn->voucher_type = $data['voucher_type'];
        $txn->transaction_type = $data['transaction_type']; //$ledger_name_obj->nature_of_account;//$txn_type;
        $txn->payment_mode = $data['mode_of_payment'];
        $txn->payment_reference = $data['payment_reference'];
        $txn->transaction_amount = $data['transaction_amount'];
        $txn->other_reference_id = $data['other_payment_ref'];
        $txn->txn_from_id = $data['transaction_from_id'];
        $txn->memo_desc = $data['narration'];
        $txn->is_opening_balance = $data['is_opening'];
        $txn->is_reconciled = (!empty($data['is_reconciled'])) ? $data['is_reconciled'] : 0;;
        $txn->created_by = (!empty($data['user_id'])) ? $data['user_id'] : 0;
        $txn->added_on = date("Y-m-d H:i:s");
        $txn->voucher_reference_number = (!empty($data['voucher_reference_number'])) ? $data['voucher_reference_number'] : '';
        $txn->voucher_reference_id = (!empty($data['voucher_reference_id'])) ? $data['voucher_reference_id'] : '';
        $txn->is_cancelled = (!empty($data['is_cancelled'])) ? 1 : 0;

        if ($txn->save()) {
            return $txn->txn_id;
        } else {
            return false;
        }
    }

    public function getLedgerDetail($data=array()){
        $soc_id = $data['soc_id'];
        if (!empty($soc_id)) {
            // Build the query
            $query = DB::table('chsone_grp_ledger_tree')
                ->where('soc_id', $data['soc_id']);

            if (!empty($data['ledger_id'])) {
                $query->where('ledger_account_id', $data['ledger_id']);
            }
            if (!empty($data['ledger_name'])) {
                $query->where('ledger_account_name', $data['ledger_name']);
            }
            if (!empty($data['behaviour'])) {
                $query->where('behaviour', $data['behaviour']);
            }
            if (!empty($data['context'])) {
                $query->where('context', $data['context']);
            }
            if (!empty($data['entity_type'])) {
                $query->where('entity_type', $data['entity_type']);
            }

            // Execute the query and fetch the first result
            $ledgerDetail = $query->first();

            // Return the result as an array if not empty
            if (!empty($ledgerDetail)) {
                return (array)$ledgerDetail;
            }
        }

        return false;
    }

    public function createNewLedgerExit($data=array())
    {
        $auth = $data['auth'];
        $ledger_name = $data['ledger_name'];
        $parent_id = $data['parent_id'];
        $behaviour = $data['behaviour'];
        $context = $data['context'];
        
        //Check whether ledger exist or not
        $objBookerLedger = ChsoneGrpLedgerTree::where('soc_id', $auth['soc_id'])
        ->where('entity_type', ENTITY_TYPE_LEDGER)
        ->where('ledger_account_name', $ledger_name)
        ->where('parent_id', $parent_id)
        ->where('behaviour', $behaviour)
        ->first();
        
        if(!empty($objBookerLedger)) {
            return $objBookerLedger->ledger_account_id;
        }
        else 
        {
            //Create new ledger if does not exist
            $ledger_account_id = $this->manipulate($ledger_name, ENTITY_TYPE_LEDGER, "", $parent_id, $behaviour, '', 0, '', '' ,null, $context);
            if (gettype($ledger_account_id) != 'boolean' && strpos($ledger_account_id, 'DUP') === false) {
                return $ledger_account_id;
            }
        }
        return false;
    }

    public function setInvoicePaymentData($data = array())
    {
        $flagToExitLoop = $paid_advance_amount = $paidCount = 0;
        $paidLedgerList = '';
        $paidAmount = (float) $data['postedData']['payment_amount'];
        $arrPaidInvoices = explode(',', $data['postedData']['member_paid_invoice']);
        $arrInvoicePaymentDetail = array();
        if (isset($data['postedData']['total_unpaid_amount']) && !empty($data['postedData']['total_unpaid_amount']) && round($data['postedData']['payment_amount'], 1) > round($data['postedData']['total_unpaid_amount'], 1)) {
            $paid_advance_amount = (float) round($data['postedData']['payment_amount'] - $data['postedData']['total_unpaid_amount'], 3);
        }
        foreach ($data['invoice_detail'] as $eachInvoiceDetail) {
            // if invoice number is not in list of paid invoices then got to next iteration
            if (!in_array($eachInvoiceDetail['unit_invoice_number'], $arrPaidInvoices)) {
                continue;
            }
            $eachInvoicePaymentDetail = array();
            $invoiceAmount = 0;
            // Added late payment tax charges
            $eachInvoiceDetail['late_charge_tax_detail']['total_tax'] = $eachInvoiceDetail['late_charge_tax_detail']['total_tax'] ?? 0;
            $eachInvoiceDetail['late_payment_interest'] = $eachInvoiceDetail['late_payment_interest'] ?? 0;
            $eachInvoiceDetail['interest_amount'] += $eachInvoiceDetail['late_charge_tax_detail']['total_tax'];
            $eachInvoiceDetail['late_payment_interest'] += $eachInvoiceDetail['late_charge_tax_detail']['total_tax'];
            $paidLedgerList .= $eachInvoiceDetail['unit_invoice_number'] . ',';
            $eachInvoicePaymentDetail['soc_id'] = $eachInvoiceDetail['soc_id'];
            $eachInvoicePaymentDetail['user_id'] = $data['postedData']['user_id'];
            $eachInvoicePaymentDetail['member_id'] = $eachInvoiceDetail['member_id'];
            $eachInvoicePaymentDetail['unit_id'] = $eachInvoiceDetail['unit_id'];
            $eachInvoicePaymentDetail['unit_invoice_id'] = $eachInvoiceDetail['unit_invoice_id'];
            $eachInvoicePaymentDetail['from_date'] = $eachInvoiceDetail['from_date'];
            $eachInvoicePaymentDetail['to_date'] = $eachInvoiceDetail['to_date'];
            $eachInvoicePaymentDetail['invoice_date'] = $eachInvoiceDetail['invoice_date'];
            $eachInvoicePaymentDetail['invoice_number'] = $eachInvoiceDetail['unit_invoice_number'];
            $eachInvoicePaymentDetail['unit_first_invoice'] = $eachInvoiceDetail['unit_first_invoice'];
            $eachInvoicePaymentDetail['principal_amount'] = $eachInvoiceDetail['principal_amount'];
            $eachInvoicePaymentDetail['interest_amount'] = $eachInvoiceDetail['interest_amount'];
            $eachInvoicePaymentDetail['advance_amount'] = $eachInvoiceDetail['advance_amount'];
            $eachInvoicePaymentDetail['payment_mode'] = $data['postedData']['payment_mode'];
            $eachInvoicePaymentDetail['transaction_reference'] = !empty($data['postedData']['transaction_reference']) ? $data['postedData']['transaction_reference'] : '';
            $eachInvoicePaymentDetail['transaction_charges'] = !empty($data['postedData']['transaction_charges']) ? $data['postedData']['transaction_charges'] : 0;
            $eachInvoicePaymentDetail['payment_date'] = $data['postedData']['payment_date'];
            if (isset($data['postedData']['writeoff_amount']) && $data['postedData']['writeoff_amount'] > 0) {

                $eachInvoicePaymentDetail['writeoff_amount'] = $data['postedData']['writeoff_amount'];
            }
            $eachInvoicePaymentDetail['late_payment_charges'] = !empty($eachInvoiceDetail['late_payment_interest']) ? $eachInvoiceDetail['late_payment_interest'] : 0;
            $eachInvoicePaymentDetail['payment_instrument'] = (!empty($data['postedData']['payment_instrument'])) ? $data['postedData']['payment_instrument'] : '';
            if ($eachInvoiceDetail['unit_invoice_number'] == $eachInvoiceDetail['unit_first_invoice'] && round($paidAmount, 1) > 0 && strtolower($eachInvoiceDetail['payment_status']) != 'paid') {
                // print_r($eachInvoicePaymentDetail);exit;
                $arrOustandingPayment = array();
                $outstandingAmountPayment = 0;
                $partialPaidAmount = isset($eachInvoiceDetail['invoice_amount_detail']['partialpaidAmount']) ? $eachInvoiceDetail['invoice_amount_detail']['partialpaidAmount'] : 0;
                $partialPaidInterest = isset($eachInvoiceDetail['invoice_amount_detail']['partialLatePaymentCharge']) ? $eachInvoiceDetail['invoice_amount_detail']['partialLatePaymentCharge'] : 0;
                $totalPaidAmount = $partialPaidAmount + $partialPaidInterest;
                // exit;
                // if(!empty($data['postedData']['outstanding_amount']) && $data['postedData']['outstanding_amount']>0)
                {
                    if (strtolower($eachInvoiceDetail['payment_status']) == 'unpaid') {
                        // Initialize principal_amount variable
                        $principal_amount = 0;

                        // Calculation for late payment charges
                        // $eachInvoicePaymentDetail['late_payment_charges'] . '||';
                        if (!empty($eachInvoicePaymentDetail['interest_amount']) && $eachInvoicePaymentDetail['interest_amount'] > 0) {
                            if ($paidAmount > $eachInvoicePaymentDetail['late_payment_charges']) {
                                $paidAmount = (float) round($paidAmount - $eachInvoicePaymentDetail['late_payment_charges'], 4);
                            } elseif (round($paidAmount, 1) == round($eachInvoicePaymentDetail['late_payment_charges'], 1)) {
                                $paidAmount = 0;
                            } else {
                                $eachInvoicePaymentDetail['late_payment_charges'] = (float) $paidAmount;
                                $paidAmount = 0;
                            }
                        }

                        // Calculation for outstanding principal amount
                        // $eachInvoicePaymentDetail['late_payment_charges'] . '||' . $eachInvoicePaymentDetail['principal_amount'] . '||' . $paidAmount . '||' . $eachInvoicePaymentDetail['late_payment_charges'] . '||';
                        if (!empty($eachInvoicePaymentDetail['principal_amount']) && $eachInvoicePaymentDetail['principal_amount'] > 0 && $paidAmount > 0) {
                            $principal_amount = $eachInvoicePaymentDetail['principal_amount'];
                            if ($paidAmount > $principal_amount) {
                                $paidAmount = (float) round($paidAmount - $principal_amount, 4);
                            } elseif (round($paidAmount, 1) == round($principal_amount, 1)) {
                                $paidAmount = 0;
                            } else {
                                $principal_amount = (float) $paidAmount;
                                $paidAmount = 0;
                            }
                        }

                        $outstandingAmountPayment = $principal_amount + $eachInvoicePaymentDetail['late_payment_charges']; // exit;
                    } elseif (strtolower($eachInvoiceDetail['payment_status']) == 'partialpaid') {
                        // Initialize principal_amount variable
                        $principal_amount = 0;
                        if (!empty($eachInvoicePaymentDetail['interest_amount'])) {
                            if ($partialPaidInterest < $eachInvoicePaymentDetail['interest_amount']) {
                                $eachInvoicePaymentDetail['late_payment_charges'] = $remainingInterestAmount = (float) round($eachInvoicePaymentDetail['interest_amount'] - $partialPaidInterest, 4);

                                if ($paidAmount > $remainingInterestAmount) {
                                    $paidAmount = (float) round($paidAmount - $remainingInterestAmount, 4);
                                } elseif (round($paidAmount, 1) == round($remainingInterestAmount, 1)) {
                                    $paidAmount = 0;
                                } else {
                                    $eachInvoicePaymentDetail['late_payment_charges'] = (float) $paidAmount;
                                    $paidAmount = 0;
                                }
                            } else {
                                $eachInvoicePaymentDetail['late_payment_charges'] = 0;
                            }
                        }

                        // Calculation for outstanding principal amount
                        if (!empty($eachInvoicePaymentDetail['principal_amount']) && $eachInvoicePaymentDetail['principal_amount'] > 0 && $partialPaidAmount < $eachInvoicePaymentDetail['principal_amount'] && $paidAmount > 0) {
                            if ($partialPaidAmount > 0) {
                                if ($partialPaidAmount < $eachInvoicePaymentDetail['principal_amount']) {
                                    $remainingPartialPaid = (float) round($eachInvoicePaymentDetail['principal_amount'] - $partialPaidAmount, 4);
                                    if ($paidAmount > $remainingPartialPaid) {
                                        $paidAmount = (float) round($paidAmount - $remainingPartialPaid, 4);
                                    } elseif (round($paidAmount, 1) == round($remainingPartialPaid, 1)) {
                                        $paidAmount = 0;
                                    } else {
                                        $remainingPartialPaid = (float) $paidAmount;
                                        $paidAmount = 0;
                                    }
                                    $principal_amount = $remainingPartialPaid;
                                }
                            } elseif ($partialPaidAmount == 0 && $eachInvoicePaymentDetail['principal_amount'] > 0) {
                                $principal_amount = $eachInvoicePaymentDetail['principal_amount'];
                                if ($paidAmount > $principal_amount) {
                                    $paidAmount = (float) round($paidAmount - $principal_amount, 4);
                                } elseif (round($paidAmount, 1) == round($principal_amount, 1)) {
                                    $paidAmount = 0;
                                } else {
                                    $principal_amount = (float) $paidAmount;
                                    $paidAmount = 0;
                                }
                            }
                        }
                        $outstandingAmountPayment = $principal_amount + $eachInvoicePaymentDetail['late_payment_charges'];
                    }

                    $eachInvoicePaymentDetail['oustanding_amount_paid'] = $principal_amount;
                    if ($outstandingAmountPayment > 0) {
                        $arrIncomeAcount = $this->getIncomeAccountByName(array(
                            'soc_id' => $eachInvoiceDetail['soc_id'],
                            'account_name' => 'maintenancefee',
                            'account_type' => 'member'
                        ));
                        $arrOustandingPayment = array(
                            'type' => 'outstanding_amount',
                            'amount' => $outstandingAmountPayment
                        );
                        if (!empty($arrIncomeAcount)) {
                            $arrOustandingPayment['unit_invoice_number'] = $eachInvoiceDetail['unit_invoice_number'];
                            $arrOustandingPayment['account_name'] = $arrIncomeAcount['account_name'];
                            $arrOustandingPayment['bank_ledger_id'] = $arrIncomeAcount['fk_ledger_id'];
                            $arrOustandingPayment['cash_ledger_id'] = $arrIncomeAcount['fk_cash_ledger_id'];
                        }
                    }
                }

                if ($paidAmount <= 0) {
                    // $eachInvoicePaymentDetail['late_payment_charges'] = '';
                    $eachInvoicePaymentDetail['status'] = 'partialpaid';
                    $eachInvoicePaymentDetail['payment_amount'] = 0;
                    $eachInvoicePaymentDetail['ledger_detail'] = array();
                    array_push($arrInvoicePaymentDetail, $eachInvoicePaymentDetail);
                    break;
                }
            }

            if ($paidAmount > 0) {
                $eachInvoiceDetail['temp_advance_amount'] = $eachInvoiceDetail['advance_amount'];
                
                if (strtolower($eachInvoiceDetail['payment_status']) == 'unpaid') {

                    if ($eachInvoiceDetail['unit_invoice_number'] != $eachInvoiceDetail['unit_first_invoice'] && !empty($eachInvoicePaymentDetail['late_payment_charges']) && $eachInvoicePaymentDetail['late_payment_charges'] > 0) {
                        if (round($paidAmount, 1) > round($eachInvoicePaymentDetail['late_payment_charges'], 1)) {
                            $paidAmount = (float) round($paidAmount - $eachInvoicePaymentDetail['late_payment_charges'], 4);
                            
                        } elseif (round($paidAmount, 1) == round($eachInvoicePaymentDetail['late_payment_charges'], 1)) {
                            $paidAmount = 0;
                        } else {
                            $eachInvoicePaymentDetail['late_payment_charges'] = $paidAmount; // (float)round($eachInvoicePaymentDetail['late_payment_charges'] - $paidAmount,4);
                            $paidAmount = 0;
                        }
                    }
                } elseif (strtolower($eachInvoiceDetail['payment_status']) == 'partialpaid') {
                    if ($eachInvoiceDetail['unit_invoice_number'] != $eachInvoiceDetail['unit_first_invoice'] && !empty($eachInvoicePaymentDetail['late_payment_charges']) && $eachInvoicePaymentDetail['late_payment_charges'] > 0) {
                        if (!empty($eachInvoiceDetail['advance_amount']) && $eachInvoiceDetail['advance_amount'] > 0 && $eachInvoiceDetail['interest_amount'] > 0) {
                            if ($eachInvoiceDetail['advance_amount'] >= $eachInvoiceDetail['interest_amount']) {
                                $eachInvoicePaymentDetail['late_payment_charges'] = 0;
                                $eachInvoiceDetail['temp_advance_amount'] = (float) round((float) $eachInvoiceDetail['advance_amount'] - (float) $eachInvoiceDetail['interest_amount'], 4);
                            } elseif ($eachInvoiceDetail['advance_amount'] < $eachInvoiceDetail['interest_amount']) {
                                $eachInvoicePaymentDetail['late_payment_charges'] = (float) round((float) $eachInvoiceDetail['interest_amount'] - (float) $eachInvoiceDetail['advance_amount'], 4);
                                $eachInvoiceDetail['temp_advance_amount'] = 0;
                            }
                        }
                        if (!empty($eachInvoicePaymentDetail['late_payment_charges']) && $eachInvoicePaymentDetail['late_payment_charges'] > 0) {
                            if ($eachInvoicePaymentDetail['late_payment_charges'] >= $eachInvoiceDetail['invoice_amount_detail']['partialLatePaymentCharge']) {
                                $eachInvoicePaymentDetail['late_payment_charges'] = (float) round((float) $eachInvoicePaymentDetail['late_payment_charges'] - (float) $eachInvoiceDetail['invoice_amount_detail']['partialLatePaymentCharge'], 4);
                            } else {
                                $eachInvoicePaymentDetail['late_payment_charges'] = 0;
                            }
                        }
                        if (round($paidAmount, 1) > round($eachInvoicePaymentDetail['late_payment_charges'], 1)) {
                            $paidAmount = (float) round($paidAmount - $eachInvoicePaymentDetail['late_payment_charges'], 4);
                            // $eachInvoicePaymentDetail['late_payment_charges'] = 0;
                        } elseif (round($paidAmount, 1) == round($eachInvoicePaymentDetail['late_payment_charges'], 1)) {
                            $paidAmount = 0;
                        } else {
                            $eachInvoicePaymentDetail['late_payment_charges'] = $paidAmount;
                            $paidAmount = 0;
                        }
                    }
                }
                // echo '########################### paid amount #######################################';
                $partialPaidAmount = 0;
                if (!empty($eachInvoiceDetail['invoice_amount_detail']['finalInvoiceAmount'])) {
                    $invoiceAmount = (float) $eachInvoiceDetail['invoice_amount_detail']['finalInvoiceAmount'];
                    if (!empty($eachInvoiceDetail['temp_advance_amount']) && $eachInvoiceDetail['temp_advance_amount'] > 0) {
                        $invoiceAmount = (float) ($invoiceAmount > $eachInvoiceDetail['temp_advance_amount']) ? round($invoiceAmount - $eachInvoiceDetail['temp_advance_amount'], 4) : 0;
                    }
                    if (!empty($eachInvoiceDetail['invoice_amount_detail']['partialpaidAmount']) && $eachInvoiceDetail['invoice_amount_detail']['partialpaidAmount'] > 0) {
                        $partialPaidAmount = (float) $eachInvoiceDetail['invoice_amount_detail']['partialpaidAmount'];
                        if ($eachInvoiceDetail['unit_invoice_number'] == $eachInvoiceDetail['unit_first_invoice'] && !empty($eachInvoiceDetail['principal_amount'])) {
                            $partialPaidAmount = ($partialPaidAmount > $eachInvoiceDetail['principal_amount']) ? (float) round($partialPaidAmount - $eachInvoiceDetail['principal_amount'], 4) : 0;
                        }
                        $invoiceAmount = (float) ($invoiceAmount > $partialPaidAmount) ? round($invoiceAmount - $partialPaidAmount, 4) : 0;
                    }
                }

                if (round($paidAmount, 1) < round($invoiceAmount, 1)) {
                    $flagToExitLoop = 1;
                    $eachInvoicePaymentDetail['status'] = 'partialpaid';
                    if ($eachInvoiceDetail['unit_invoice_number'] == $eachInvoiceDetail['unit_first_invoice'] && strtolower($eachInvoiceDetail['payment_status'] == 'unpaid')) {
                        $eachInvoicePaymentDetail['oustanding_amount_paid'] = $eachInvoiceDetail['principal_amount'];
                    }
                    $eachInvoicePaymentDetail['payment_amount'] = $paidAmount;
                } elseif (round($paidAmount, 1) >= round($invoiceAmount, 1)) {
                    $eachInvoicePaymentDetail['payment_amount'] = (float) $invoiceAmount;

                    $paidCount++;
                    if (count($arrPaidInvoices) == $paidCount) {
                        $eachInvoicePaymentDetail['paid_advance_amount'] = $paid_advance_amount;
                        $eachInvoicePaymentDetail['payment_amount'] = round($invoiceAmount - $paid_advance_amount, 4);
                    }
                    $eachInvoicePaymentDetail['status'] = 'paid';
                    $eachInvoicePaymentDetail['payment_amount'] = (float) $invoiceAmount;
                    if ($paidAmount == $invoiceAmount) {
                        $flagToExitLoop = 1;
                    } else {
                        $paidAmount = (float) round($paidAmount - $invoiceAmount, 4);
                    }
                }

                $arrRuleIds = array();

                // convert object to json and then to array
                $eachInvoiceDetail['invoice_particulars'] = json_decode(json_encode($eachInvoiceDetail['invoice_particulars']), true);
                // Get ledger entry details
                foreach ($eachInvoiceDetail['invoice_particulars'] as $eachInvoiceParticular) {
                    if (strtolower($eachInvoiceParticular['bill_type']) == 'maintenance') {
                        $arrRuleIds[] = $eachInvoiceParticular['fk_rule_id'];
                    } elseif (strtolower($eachInvoiceParticular['bill_type']) == 'incident') {
                        $arrIncidentRuleIds[] = $eachInvoiceParticular['fk_rule_id'];
                    }
                }
                if (!empty($arrRuleIds)) {
                    $strRuleIds = implode(',', array_unique($arrRuleIds));
                }
                if (!empty($arrIncidentRuleIds)) {
                    $strIncidentRuleIds = implode(',', array_unique($arrIncidentRuleIds));
                } else {
                    $strIncidentRuleIds = '';
                }
                // echo '<pre>########################### Rule Ids #############################################';
                if ($strRuleIds || $strIncidentRuleIds) {
                    $arrData = array(
                        'auth' => array(
                            'soc_id' => $eachInvoiceDetail['soc_id']
                        ),
                        'ruleIds' => $strRuleIds,
                        'incidentRuleIds' => $strIncidentRuleIds,
                        'account_type' => 'member',
                        'invoice_number' => $eachInvoiceDetail['unit_invoice_number'],
                        'late_payment_charges' => $eachInvoiceDetail['late_payment_charges_detail'],
                        'payment_status' => $eachInvoicePaymentDetail['status'],
                        'paid_amount' => $eachInvoicePaymentDetail['payment_amount'],
                        'partial_paid_amount' => $partialPaidAmount
                    );
                    $eachInvoicePaymentDetail['ledger_detail'] = $this->getIncomeAccountDetail($arrData);
                }

                array_push($arrInvoicePaymentDetail, $eachInvoicePaymentDetail);

                if ($flagToExitLoop == 1) {
                    break;
                }
            }
        }
        if (!empty($paidLedgerList)) {
            $arrInvoicePaymentDetail['paidInvoiceLedger'] = trim($paidLedgerList, ',');
        }
        return $arrInvoicePaymentDetail;
    }

    public function getIncomeAccountByName($data = array())
    {
        $arrIncomeAccount = [];
        $objIncomeAccount = IncomeAccount::where('soc_id', $data['soc_id'])
            ->where('account_name', $data['account_name'])
            ->where('account_type', $data['account_type'])
            ->first();

        if (!empty($objIncomeAccount)) {
            $arrIncomeAccount = $objIncomeAccount->toArray();
        }

        return $arrIncomeAccount;
    }

    public function getIncomeAccountDetail($data = array())
    {
        $arrIncomeDetailData = $arrIncomeDetail = $arrIncidentIncomeDetail = array();
        if (!empty($data['ruleIds'])) {
            $arrIncomeDetail = $this->getIncomeAccountInvoiceParticular($data);
            $arrIncomeDetail = json_decode(json_encode($arrIncomeDetail), true);
        }
        if (!empty($data['incidentRuleIds'])) {
            $arrIncidentIncomeDetail = $this->getIncidentAccountInvoiceParticular($data);
            $arrIncidentIncomeDetail = json_decode(json_encode($arrIncidentIncomeDetail), true);
        }

        $arrIncomeDetail = $this->getParticularsPaymentAccountOrder(array(
            'arrMaintenanceAccountDetail' => $arrIncomeDetail,
            'arrIncidentAccountDetail' => $arrIncidentIncomeDetail
        ));

        if (!empty($arrIncomeDetail)) {
            // echo '####################### income account detail ###################################3';
            $totalParticularPaidAmount = 0;
            foreach ($arrIncomeDetail as $eachIncomeDetail) {
                $finalInvoiceAmount = 0;
                $arrTaxApplicable = $arrTax_exemptions = array();
                $key = strtolower($eachIncomeDetail['account_name']);
                $finalInvoiceAmount = (float) $eachIncomeDetail['amount'];
                // Add applicable tax amount
                if (!empty($eachIncomeDetail['tax_applicable'])) {
                    $arrTaxApplicable = unserialize($eachIncomeDetail['tax_applicable']);
                    $finalInvoiceAmount += (float) $arrTaxApplicable['total'];
                }
                // Subtract applicable tax exemption amount
                if (!empty($eachIncomeDetail['tax_exemptions'])) {
                    $arrTax_exemptions = unserialize($eachIncomeDetail['tax_exemptions']);
                    $finalInvoiceAmount += (float) $arrTax_exemptions['total'];
                }

                if (strtolower($eachIncomeDetail['is_particular_paid']) == 'y') {
                    $totalParticularPaidAmount += $finalInvoiceAmount;
                }

                if (strtolower($eachIncomeDetail['is_particular_paid']) == 'p') {
                    $totalParticularPaidAmount += (float) $eachIncomeDetail['particular_paid_amount'];
                    $finalInvoiceAmount = (float) round($finalInvoiceAmount - $eachIncomeDetail['particular_paid_amount'], 4);
                }

                if ($data['paid_amount'] > 0 && in_array(strtolower($eachIncomeDetail['is_particular_paid']), array('n','p'))) {
                    // echo 'test';
                    $arrNewIncomeDetail = array();
                    if (array_key_exists($key, $arrIncomeDetailData)) {
                        $arrIncomeDetailData[$key]['amount'] += (float) $eachIncomeDetail['amount'];
                        $arrIncomeDetailData[$key]['particularId'] .= ',' . $eachIncomeDetail['particularId'];
                        $arrIncomeDetailData[$key]['ledger_amount'] += (float) $finalInvoiceAmount;
                        $arrIncomeDetailData[$key]['optional'] = $eachIncomeDetail['particular'];
                    } else {
                        $arrNewIncomeDetail['particularId'] = $eachIncomeDetail['particularId'];
                        $arrNewIncomeDetail['particular'] = $eachIncomeDetail['particular'];
                        $arrNewIncomeDetail['rule_id'] = $eachIncomeDetail['id'];
                        $arrNewIncomeDetail['account_name'] = $eachIncomeDetail['account_name'];
                        $arrNewIncomeDetail['income_ledger_id'] = $eachIncomeDetail['fk_income_ledger_id'];
                        $arrNewIncomeDetail['bank_ledger_id'] = $eachIncomeDetail['fk_ledger_id'];
                        $arrNewIncomeDetail['cash_ledger_id'] = $eachIncomeDetail['fk_cash_ledger_id'];
                        $arrNewIncomeDetail['amount'] = (float) $eachIncomeDetail['amount'];

                        $arrNewIncomeDetail['tax_applicable'] = $arrTaxApplicable;
                        $arrNewIncomeDetail['tax_exemptions'] = $arrTax_exemptions;

                        $arrNewIncomeDetail['ledger_amount'] = (float) $finalInvoiceAmount;
                        $arrNewIncomeDetail['apply_late_payment_interest'] = $eachIncomeDetail['apply_late_payment_interest'];
                        $arrNewIncomeDetail['is_particular_paid'] = 'Y';
                        $arrNewIncomeDetail['particular_paid_amount'] = $eachIncomeDetail['particular_paid_amount'];
                        if (strtolower($eachIncomeDetail['particular']) == 'noc') {
                            $arrNewIncomeDetail['optional'] = $eachIncomeDetail['particular'];
                        }
                        $arrIncomeDetailData[$key] = $arrNewIncomeDetail;
                    }

                    if (strtolower($data['payment_status']) == 'partialpaid') {
                        if (!empty($arrIncomeDetailData[$key]['optional']) && $arrIncomeDetailData[$key]['optional'] == 'noc') { // echo 'hello||'.$arrIncomeDetailData[$key]['ledger_amount'].'||'.$partialOptionalHeadAmount;exit;
                            if (!empty($data['partial_paid_amount']) && $data['partial_paid_amount'] > 0) {
                                $arrIncomeDetailData[$key]['ledger_amount'] = (float) round(($totalParticularPaidAmount + $arrIncomeDetailData[$key]['ledger_amount']) - $data['partial_paid_amount'], 4);
                            }
                            $arrIncomeDetailData[$key]['ledger_amount'] = (float) round($arrIncomeDetailData[$key]['ledger_amount'] - $partialOptionalHeadAmount, 4);
                        }

                        if (round($arrIncomeDetailData[$key]['ledger_amount'], 1) <= round($data['paid_amount'], 1)) {
                            // NOC adjustment
                            if (!empty($arrIncomeDetailData[$key]['optional']) && strtolower($arrIncomeDetailData[$key]['optional']) == 'noc') {
                                $ledgerAmount = (float) round($arrIncomeDetailData[$key]['ledger_amount'] + $partialOptionalHeadAmount, 4);
                            } else {
                                $ledgerAmount = $arrIncomeDetailData[$key]['ledger_amount'];
                            }

                            // checking for next particular total remaining paid amount
                            if (round($arrIncomeDetailData[$key]['ledger_amount'], 1) < round($data['paid_amount'], 1)) {
                                $data['paid_amount'] = (float) round($data['paid_amount'] - $arrIncomeDetailData[$key]['ledger_amount'], 4);
                            } else {
                                $data['paid_amount'] = 0;
                            }
                            $arrIncomeDetailData[$key]['ledger_amount'] = $ledgerAmount;
                        } else {
                            // NOC adjustment
                            if (!empty($arrIncomeDetailData[$key]['optional']) && $arrIncomeDetailData[$key]['optional'] == 'noc') {
                                $ledgerAmount = (float) round($data['paid_amount'] + $partialOptionalHeadAmount, 4);
                                $arrIncomeDetailData[$key]['ledger_amount'] = $ledgerAmount;
                                $data['paid_amount'] = 0;
                                $arrIncomeDetailData['noc']['particularId'] = $eachIncomeDetail['particularId'];
                                $arrIncomeDetailData['noc']['is_particular_paid'] = 'P';
                            } else {
                                $ledgerAmount = $data['paid_amount'];
                                $arrIncomeDetailData[$key]['ledger_amount'] = $ledgerAmount;
                                $data['paid_amount'] = 0;
                                $arrIncomeDetailData[$key]['is_particular_paid'] = 'P';
                            }
                        }

                        if (!empty($arrIncomeDetailData[$key]['particular']) && strtolower($arrIncomeDetailData[$key]['particular']) == 'maintenancefee') {
                            $partialOptionalHeadAmount = $arrIncomeDetailData[$key]['ledger_amount'];
                        }
                    } elseif (strtolower($data['payment_status']) == 'paid') {
                        if (!empty($arrIncomeDetailData[$key]['optional']) && strtolower($arrIncomeDetailData[$key]['optional']) == 'noc') {
                            if (!empty($data['partial_paid_amount']) && $data['partial_paid_amount'] > 0) {
                                $finalInvoiceAmount = (float) round(($totalParticularPaidAmount + $finalInvoiceAmount) - $data['partial_paid_amount'], 4);
                            }

                            $optionalAmount = $finalInvoiceAmount;
                            if (!empty($witouhtOptionalHeadAmount)) {
                                $finalInvoiceAmount += $witouhtOptionalHeadAmount;
                            }

                        } elseif (!empty($data['partial_paid_amount']) && $data['partial_paid_amount'] > 0) {
                            if (round($finalInvoiceAmount, 1) > round($data['partial_paid_amount'], 1)) {
                                // $finalInvoiceAmount = (float)round($finalInvoiceAmount - $data['partial_paid_amount'], 4);
                            }
                        }

                        if (!empty($arrIncomeDetailData[$key]['particular']) && strtolower($arrIncomeDetailData[$key]['particular']) == 'maintenancefee') {
                            $witouhtOptionalHeadAmount = $finalInvoiceAmount;
                        }
                        $arrIncomeDetailData[$key]['ledger_amount'] = $finalInvoiceAmount;
                        $arrIncomeDetailData[$key]['is_particular_paid'] = 'Y';
                        if (empty($arrIncomeDetailData[$key]['optional'])) {
                            $data['paid_amount'] = (float) round($data['paid_amount'] - $finalInvoiceAmount, 4);
                        } elseif (!empty($optionalAmount)) {
                            $data['paid_amount'] = (float) round($data['paid_amount'] - $optionalAmount, 4);
                        }
                    } else {
                        $arrIncomeDetailData[$key]['ledger_amount'] = $finalInvoiceAmount;
                    }
                }
            }
        }
        // echo '######################### ledge particular head ##############################';
        return $arrIncomeDetailData;
    }

    public function getIncomeAccountInvoiceParticular($data = array())
    {
        $arrIncomeDetailData = array();
        if (is_array($data['ruleIds']) && !empty($data['ruleIds'])) {
            $data['ruleIds'] = implode(',', array_unique($data['ruleIds']));
        }
        $resultset = $this->tenantDB()->table('income_invoice_rules as incomerule')
        ->select(
            'invoiceparticular.id as particularId',
            'invoiceparticular.particular',
            'incomerule.id',
            'incomeaccount.account_name',
            'incomeaccount.fk_income_ledger_id',
            'incomeaccount.fk_ledger_id',
            'incomeaccount.fk_cash_ledger_id',
            'unitinvoice.advance_amount',
            'invoiceparticular.amount',
            'invoiceparticular.tax_applicable',
            'invoiceparticular.tax_exemptions',
            'incomerule.apply_late_payment_interest',
            'invoiceparticular.is_particular_paid',
            'invoiceparticular.particular_paid_amount'
        )
        ->leftJoin('income_accounts as incomeaccount', 'incomerule.income_account_id', '=', 'incomeaccount.account_id')
        ->leftJoin('income_invoice_particular as invoiceparticular', 'incomerule.id', '=', 'invoiceparticular.fk_rule_id')
        ->leftJoin('income_unit_invoices as unitinvoice', 'invoiceparticular.invoice_number', '=', 'unitinvoice.invoice_number')
        ->where('incomerule.soc_id', $data['auth']['soc_id'])
        ->whereIn('incomerule.id', explode(',', $data['ruleIds']))
        ->where('incomeaccount.account_type', $data['account_type'])
        ->where('invoiceparticular.invoice_number', $data['invoice_number'])
        ->orderBy('incomerule.apply_late_payment_interest', 'DESC')
        ->orderBy('invoiceparticular.id', 'ASC')
        ->get();

        if (!empty($resultset)) {
            $arrIncomeDetailData = $resultset->toArray();
        }
        return $arrIncomeDetailData;
    }

    public function getIncidentAccountInvoiceParticular($data = array())
    {
        $arrIncomeDetailData = array();
        if (is_array($data["incidentRuleIds"]) && !empty($data["incidentRuleIds"])) {
            $data["incidentRuleIds"] = implode(',', array_unique($data["incidentRuleIds"]));
        }
        $resultset = $this->tenantDB()->table('IncomeCommonBilling as incomerule')
        ->select([
            'invoiceparticular.id as particularId',
            'invoiceparticular.particular',
            'incomerule.id',
            'invoiceparticular.particular as account_name',
            'incomerule.ledger_id as fk_income_ledger_id',
            'unitinvoice.advance_amount',
            'invoiceparticular.amount',
            'invoiceparticular.tax_applicable',
            'invoiceparticular.tax_exemptions',
            'incomerule.apply_late_payment_interest',
            'invoiceparticular.is_particular_paid',
            'invoiceparticular.particular_paid_amount'
        ])
        ->leftJoin('IncomeInvoiceParticular as invoiceparticular', 'incomerule.id', '=', 'invoiceparticular.fk_rule_id')
        ->leftJoin('UnitsInvoice as unitinvoice', 'invoiceparticular.invoice_number', '=', 'unitinvoice.invoice_number')
        ->where('incomerule.soc_id', $data['auth']['soc_id'])
        ->whereIn('incomerule.id', explode(',', $data["incidentRuleIds"]))
        ->where('invoiceparticular.invoice_number', $data['invoice_number'])
        ->orderBy('incomerule.apply_late_payment_interest', 'DESC')
        ->orderBy('invoiceparticular.id', 'ASC')
        ->get();
        if (!empty($resultset)) {
            $arrIncomeDetailData = $resultset->toArray();
        }
        return $arrIncomeDetailData;
    }

    public function getParticularsPaymentAccountOrder($data = array())
    {
        $arrIncomeDetailData = array_merge($data['arrMaintenanceAccountDetail'], $data['arrIncidentAccountDetail']);
        uasort($arrIncomeDetailData, function ($item1, $item2) {
            if ($item1['apply_late_payment_interest'] == $item2['apply_late_payment_interest']) {
                return 0;
            }
            return $item1['apply_late_payment_interest'] > $item2['apply_late_payment_interest'] ? -1 : 1;
        });
        return $arrIncomeDetailData;
    }

    public function getPaymentDetailsAfterTds($data = array())
    {
        if (!empty($data['arrInvoicePaymentDetail'])) {
            $arrInvoiceDetail = array();
            // Get tds and payment amount after ratio proportion calculation
            foreach ($data['arrInvoicePaymentDetail'] as $eachInvoicePaymentDetail) {
                if (isset($eachInvoicePaymentDetail['invoice_number']) && !empty($eachInvoicePaymentDetail['invoice_number'])) {
                    $otherAmountPaid = 0;
                    if (isset($eachInvoicePaymentDetail['oustanding_amount_paid']) && !empty($eachInvoicePaymentDetail['oustanding_amount_paid'])) {
                        $otherAmountPaid += $eachInvoicePaymentDetail['oustanding_amount_paid'];
                    }
                    if (isset($eachInvoicePaymentDetail['paid_advance_amount']) && !empty($eachInvoicePaymentDetail['paid_advance_amount'])) {
                        $otherAmountPaid += $eachInvoicePaymentDetail['paid_advance_amount'];
                    }
                    $arrInvoiceDetail[$eachInvoicePaymentDetail['invoice_number']]['total_invoice_amount'] = (float) round($eachInvoicePaymentDetail['late_payment_charges'] + $eachInvoicePaymentDetail['payment_amount'] + $otherAmountPaid, 3);
                    $arrInvoiceDetail[$eachInvoicePaymentDetail['invoice_number']]['total_tds_amount'] = (float) $data['arrPostData']['tds_amount'];
                    $arrInvoiceDetail[$eachInvoicePaymentDetail['invoice_number']]['total_payment_amount'] = (float) $data['arrPostData']['payment_amount'];
                    $arrInvoiceDetail[$eachInvoicePaymentDetail['invoice_number']]['percentage'] = (float) round(($arrInvoiceDetail[$eachInvoicePaymentDetail['invoice_number']]['total_invoice_amount'] * 100) / $arrInvoiceDetail[$eachInvoicePaymentDetail['invoice_number']]['total_payment_amount'], 3);
                    $arrInvoiceDetail[$eachInvoicePaymentDetail['invoice_number']]['invoice_tds_amount'] = (float) round(($arrInvoiceDetail[$eachInvoicePaymentDetail['invoice_number']]['percentage'] * $arrInvoiceDetail[$eachInvoicePaymentDetail['invoice_number']]['total_tds_amount']) / 100, 3);
                    $arrInvoiceDetail[$eachInvoicePaymentDetail['invoice_number']]['invoice_payment_amount'] = (float) round(($eachInvoicePaymentDetail['payment_amount'] - $arrInvoiceDetail[$eachInvoicePaymentDetail['invoice_number']]['invoice_tds_amount']), 3);
                }
            }
            // print_r($arrInvoiceDetail);//exit;
            // Set payment data again for further proccessing
            $arrInvoicePaymentDetails = array();
            foreach ($data['arrInvoicePaymentDetail'] as $key => $eachInvoicePaymentDetail) {
                if (isset($eachInvoicePaymentDetail['invoice_number']) && !empty($eachInvoicePaymentDetail['invoice_number'])) {
                    foreach ($arrInvoiceDetail as $tdsKey => $eachInvoiceDetail) {
                        if (strtolower($eachInvoicePaymentDetail['invoice_number']) == strtolower($tdsKey)) {
                            // quick fix again nned to wwrok on this code (tds isssue in late payment charges)
                            if (!empty($eachInvoicePaymentDetail['late_payment_charges']) && empty($eachInvoicePaymentDetail['payment_amount'])) {
                                $eachInvoicePaymentDetail['payment_amount'] = $eachInvoicePaymentDetail['tds_amount'] = 0;
                            } else {
                                $eachInvoicePaymentDetail['payment_amount'] = $eachInvoiceDetail['invoice_payment_amount'];
                                $eachInvoicePaymentDetail['tds_amount'] = $eachInvoiceDetail['invoice_tds_amount'];
                            }
                        }
                    }
                }
                $arrInvoicePaymentDetails[$key] = $eachInvoicePaymentDetail;
            }
        }
        return $arrInvoicePaymentDetails;
        // print_r($arrInvoicePaymentDetails);exit;
    }

    public function saveIncomeInvoicePayment($data = array())
    {
        $arrResponse = array(
            'error' => array(),
            'success' => array()
        );
        $arrMemberDetail = array();
        if (isset($data['paymentDetail']) && !empty($data['paymentDetail']) && isset($data['arrReceiptDetail']) && !empty($data['arrReceiptDetail'])) {
            foreach ($data['paymentDetail'] as $eachInsertData) {
                if (isset($eachInsertData['invoice_number']) && !empty($eachInsertData['invoice_number'])) {
                    if (empty($arrMemberDetail) && !empty($eachInsertData['member_id'])) {
                        $arrMemberDetail = $this->getPrimaryMemberById(array(
                            'soc_id' => $eachInsertData['soc_id'],
                            'member_id' => $eachInsertData['member_id']
                        ));
                    }

                    $objIncomeInvoicePayment = new IncomeInvoicePayment();
                    $objIncomeInvoicePayment->created_date = date('Y-m-d H:i:s');
                    $objIncomeInvoicePayment->created_by = (!empty($eachInsertData['user_id'])) ? $eachInsertData['user_id'] : 0;
                    $objIncomeInvoicePayment->soc_id = (!empty($eachInsertData['soc_id'])) ? $eachInsertData['soc_id'] : '';
                    $objIncomeInvoicePayment->fk_member_id = (!empty($eachInsertData['member_id'])) ? $eachInsertData['member_id'] : '';
                    $objIncomeInvoicePayment->fk_unit_id = (!empty($eachInsertData['unit_id'])) ? $eachInsertData['unit_id'] : '';
                    $objIncomeInvoicePayment->fk_unit_invoice_id = (!empty($eachInsertData['unit_invoice_id'])) ? $eachInsertData['unit_invoice_id'] : '';
                    $objIncomeInvoicePayment->invoice_number = $eachInsertData['invoice_number'];
                    $objIncomeInvoicePayment->receipt_number = $data['arrReceiptDetail']['receipt_number'];
                    if (!empty($eachInsertData['oustanding_amount_paid'])) {
                        $eachInsertData['payment_amount'] += $eachInsertData['oustanding_amount_paid'];
                    }
                    $objIncomeInvoicePayment->payment_amount = $eachInsertData['payment_amount'];
                    // if(isset($eachInsertData['paid_advance_amount']) && !empty($eachInsertData['paid_advance_amount']))
                    // {
                    // $objIncomeInvoicePayment->payment_amount += (float)$eachInsertData['paid_advance_amount'];
                    // }
                    $objIncomeInvoicePayment->payment_mode = $eachInsertData['payment_mode'];
                    $objIncomeInvoicePayment->transaction_reference = !empty($eachInsertData['transaction_reference']) ? $eachInsertData['transaction_reference'] : '';
                    $objIncomeInvoicePayment->transaction_charges = !empty($eachInsertData['transaction_charges']) ? $eachInsertData['transaction_charges'] : 0;
                    $objIncomeInvoicePayment->late_payment_charges = !empty($eachInsertData['late_payment_charges']) ? $eachInsertData['late_payment_charges'] : 0;
                    $objIncomeInvoicePayment->payment_instrument = !empty($eachInsertData['payment_instrument']) ? $eachInsertData['payment_instrument'] : '';
                    $objIncomeInvoicePayment->tds_deducted = !empty($eachInsertData['tds_amount']) ? $eachInsertData['tds_amount'] : 0;
                    $objIncomeInvoicePayment->writeoff_amount = !empty($eachInsertData['writeoff_amount']) ? $eachInsertData['writeoff_amount'] : 0;
                    $objIncomeInvoicePayment->status = 'success';
                    $objIncomeInvoicePayment->updated_date = date('Y-m-d H:i:s');
                    $objIncomeInvoicePayment->updated_by = 0;

                    $objIncomeInvoicePayment->payment_date = !empty($eachInsertData['payment_date']) ? $this->getDatabaseDate($eachInsertData['payment_date']) : $this->getCurrentDate('database');

                    if (!$objIncomeInvoicePayment->save()) {
                        $singleMsg = '|';
                        foreach ($objIncomeInvoicePayment->getMessages() as $messages) {
                            $arrMessages[] = (string) $messages;
                            $singleMsg .= (string) $messages . '|';
                        }
                        $arrResponse['error'][] = array(
                            'invoice_number' => $objIncomeInvoicePayment->invoice_number,
                            'message' => $singleMsg
                        );
                    } else {
                        // Update advance payment
                        if (isset($eachInsertData['paid_advance_amount']) && !empty($eachInsertData['paid_advance_amount'])) {
                            $arrCreditData['soc_id'] = $objIncomeInvoicePayment->soc_id;
                            $arrCreditData['account_id'] = $objIncomeInvoicePayment->fk_unit_id;
                            $arrCreditData['account_context'] = 'unit';
                            $arrCreditData['transaction_type'] = 'cr';
                            $arrCreditData['context'] = 'system';
                            $arrCreditData['payment_tracker_id'] = $data['arrReceiptDetail']['payment_tracker_id'];
                            $arrCreditData['credit_used_type'] = 'adjustable';
                            $arrCreditData['used_for'] = 'maintenance';
                            $arrCreditData['payment_date'] = $eachInsertData['payment_date'];
                            $arrCreditData['narration'] = 'Amount Rs ' . $eachInsertData['paid_advance_amount'] . ' has credited from Advance';
                            $arrCreditData['payment_amount'] = $eachInsertData['paid_advance_amount'];
                            $arrCreditData['account_name'] = $arrMemberDetail['member_first_name'] . ' ' . $arrMemberDetail['member_last_name'];
                            $creditAccountresponse = $this->saveCreditAccountResponse(array(
                                'auth' => true,
                                'process' => 'fetch',
                                'soc_id' => $arrCreditData['soc_id'],
                                'id' => '',
                                'data' => $arrCreditData,
                                'user' => '',
                                'username' => ''
                            ));
                            if ($creditAccountresponse['error']) {
                                $arrResponse['error'][] = array(
                                    'invoice_number' => $objIncomeInvoicePayment->invoice_number,
                                    'message' => 'unable to create debit note'
                                );
                            }
                        }

                        // Changing status of invoice
                        $objUnitsInvoice = IncomeUnitInvoice::where('soc_id', $eachInsertData['soc_id'])
                            ->where('fk_unit_id', $eachInsertData['unit_id'])
                            ->where('unit_invoice_id', $eachInsertData['unit_invoice_id'])
                            ->where('invoice_number', $eachInsertData['invoice_number'])
                            ->first();
                        if (!empty($objUnitsInvoice)) {

                            $objUnitsInvoice->status = $eachInsertData['status'];
                            $objUnitsInvoice->payment_status = $eachInsertData['status'];
                            $objUnitsInvoice->updated_date = date('Y-m-d H:i:s');
                            $objUnitsInvoice->updated_by = 0;
                            if (!$objUnitsInvoice->save()) {
                                $singleMsg = '|';
                                foreach ($objUnitsInvoice->getMessages() as $messages) {
                                    $arrMessages[] = (string) $messages;
                                    $singleMsg .= (string) $messages . '|';
                                }
                                $arrResponse['error'][] = array(
                                    'invoice_number' => $objUnitsInvoice->invoice_number,
                                    'message' => $singleMsg
                                );
                            } else {
                                $arrResponse['success'][] = array(
                                    'invoice_number' => $objUnitsInvoice->invoice_number
                                );
                            }
                        }
                    }
                }
            }
            unset($objIncomeInvoicePayment);
        }
        return $arrResponse;
    }

    public function getPrimaryMemberById($data = array())
    {
        $resultset = $this->tenantDB()->table('chsone_members_master as memberMaster')
        ->select([
            'memberMaster.id',
            'memberMaster.member_first_name',
            'memberMaster.member_last_name'
        ])
        ->leftJoin('chsone_member_type_master as memberType', 'memberMaster.member_type_id', '=', 'memberType.member_type_id')
        ->where('memberMaster.soc_id', $data['soc_id'])
        ->where('memberMaster.id', $data['member_id'])
        ->where('memberType.member_type_name', 'Primary')
        ->where('memberMaster.status', 1)
        ->first();

        $resultset = json_decode(json_encode($resultset), true);

        return $resultset;
    }

    public function saveCreditAccountResponse($data = array())
    {
        $response = ['error' => false, 'data' => null];

        try {
            if (empty($data)) {
                $this->status = 'error';
                $this->message = 'No data values provided.';
                $this->statusCode = 400;
                return;
            }
            $user = $data['user'];
            $id = $data['id'];
            $username = (!empty($data['username']) ? $data['username'] : null);

            $data = $data['data'];
            $data['username'] = $username;
            $data['user'] = $user;

            if ($id) {
                $cr = ChsoneCreditAccount::where('credit_account_id', $id)
                    ->where('soc_id', $data['soc_id'])
                    ->first();

                $cr->updated_by = $user;
                $cr->updated_date = date('Y-m-d H:i:s');
                $cr->use_credit_after = (!empty($data['adjustable_date']) ? $data['adjustable_date'] : null);
                $cr->is_locked = 0;
                $cr->use_credit_for = (!empty($data['used_for']) ? $data['used_for'] : null);

                $cr->is_invoice_rectification = ((isset($data['is_invoice_rectification']) && !empty($data['is_invoice_rectification'])) ? $data['is_invoice_rectification'] : null);
                $cr->income_account_id = ((isset($data['income_account_ledger_id']) && !empty($data['income_account_ledger_id'])) ? $data['income_account_ledger_id'] : null);
                $cr->narration = $data['narration'];
                $cr->use_credit = (isset($data['credit_used_type']) && !empty($data['credit_used_type']) ? $data['credit_used_type'] : 'adjustable');
                // Save data
                if ($cr->save()) {
                    $response['data'] = $cr->credit_account_id;
                } else {
                    $response['error'] = true;
                    $response['data'] = ['message' => $cr->getErrors()];
                }
            } else {
                if ($data['credit_used_type'] == 'both') {
                    $data['credit_used_type'] = 'adjustable';
                    $data['payment_amount'] = $data['adjustable_amount'];
                    $saveResponse = $this->saveCreditNote($data);
                    if ($saveResponse['error'] == false) {
                        $data['credit_used_type'] = 'refundable';
                        $data['payment_amount'] = $data['refundable_amount'];
                        $data['used_for'] = NULL;
                        $data['adjustable_date'] = NULL;

                        $saveResponse = $this->saveCreditNote($data);
                        if ($saveResponse['error'] == false) {
                            $response['data'] = $saveResponse['id'];
                        }
                    }
                } else {
                    $saveResponse = $this->saveCreditNote($data);
                    if ($saveResponse['error'] == false) {
                        $response['data'] = $saveResponse['id'];
                    }
                }
            }
        } catch (\Exception $e) {
            $response['error'] = true;
            $response['data'] = ['message' => $e->getMessage()];
        } finally {
            if (empty($response['data'])) {
                $response['data'] = ['message' => 'working is ok'];
                $response['error'] = false;
            }
            return $response;
        }
    }

    public function saveCreditNote($data)
    {
        $cr = new ChsoneCreditAccount();
        $cr->created_by = isset($data['user']) ? $data['user'] : 0;
        $cr->created_date = $this->getCurrentDate('database');
        $cr->soc_id = $data['soc_id'];
        if (isset($data['invoice_no']) && !empty($data['invoice_no'])) {
            $cr->invoice_number = $data['invoice_no'];
        } elseif (isset($data['invoice_number']) && !empty($data['invoice_number'])) {
            $cr->invoice_number = $data['invoice_number'];
        } else {
            $cr->invoice_number = null;
        }
        $cr->is_invoice_rectification = ((isset($data['is_invoice_rectification']) && !empty($data['is_invoice_rectification'])) ? $data['is_invoice_rectification'] : null);
        $cr->income_account_id = ((isset($data['income_account_ledger_id']) && !empty($data['income_account_ledger_id'])) ? $data['income_account_ledger_id'] : null);
        $cr->payment_tracker_id = (isset($data['payment_tracker_id']) && !empty($data['payment_tracker_id']) ? $data['payment_tracker_id'] : null);
        $cr->account_id = $data['account_id'];
        $cr->account_name = $data['account_name'];
        $cr->account_context = $data['account_context'];
        $cr->amount = $data['payment_amount'];
        $cr->payment_mode = (isset($data['payment_mode']) && !empty($data['payment_mode']) ? $data['payment_mode'] : null);
        $cr->payment_date = (isset($data['payment_date']) && !empty($data['payment_date']) ? $this->getDatabaseDate($data['payment_date']) : null);
        $cr->transaction_type = $data['transaction_type'];
        $cr->narration = $data['narration'];
        $cr->use_credit = (isset($data['credit_used_type']) && !empty($data['credit_used_type']) ? $data['credit_used_type'] : 'adjustable');
        $cr->use_credit_after = (isset($data['adjustable_date']) && !empty($data['adjustable_date']) ? $data['adjustable_date'] : null);
        $cr->is_locked = (isset($data['is_locked']) && !empty($data['is_locked']) ? $data['is_locked'] : 0);
        $cr->use_credit_for = (isset($data['used_for']) && !empty($data['used_for']) ? $data['used_for'] : null);
        $cr->reference_no = (isset($data['transaction_reference']) && !empty($data['transaction_reference']) ? $data['transaction_reference'] : null);
        $cr->context = (isset($data['context']) && !empty($data['context']) ? $data['context'] : 'system');
        $cr->created_name = isset($data['username']) ? $data['username'] : '';
        // Save data
        if ($cr->save()) {
            $data['error'] = false;
            $data['id'] = $cr->credit_account_id;
            return $data;
        } else {
            $data['error'] = true;
            $data['message'] = array('message' => $cr->getMessages());
            return $data;
        }
    }

    public function updateIncomeInvoiceParticular($data, $full_list = false)
    {
        if (isset($data['arrParticularDetail']['ledger_detail']) && !empty($data['arrParticularDetail']['ledger_detail'])) {
            foreach ($data['arrParticularDetail']['ledger_detail'] as $key => $eachLedgerDetail) {
                $particular_ids = explode(',', $eachLedgerDetail['particularId']);
                $particular_paid_amount = (float) $eachLedgerDetail['particular_paid_amount'];
        
                if (isset($eachLedgerDetail['ledger_amount']) && !empty($eachLedgerDetail['ledger_amount'])) {
                    $particular_paid_amount += (float) $eachLedgerDetail['ledger_amount'];
                }
        
                // Updating the records
                IncomeInvoiceParticular::whereIn('id', $particular_ids)
                ->update([
                    'is_particular_paid' => $eachLedgerDetail['is_particular_paid'],
                    'particular_paid_amount' => $particular_paid_amount,
                    'updated_date' => $data['updated_date']
                ]);
            }
        }
    }

    public function _invoiceWrtieoffPaymentTracker($unit_id, $arrData = array())
    {
        $arrResponse = array('status' => 'error');
        $arrPostData = $arrData['arrPostData'];
        // check arrPostData is not empty
        if (!empty($arrPostData)) {
            $arrIncomeInvoiceMemberDetail = $arrData['arrIncomeInvoiceMemberDetail'];
            $arrIncomeInvoiceDetail = $arrData['arrIncomeInvoiceDetail'];
            $arrReceiptDetail = array('payment_tracker_id' => $arrPostData['payment_tracker_id'], 'receipt_number' => $arrPostData['receipt_number'], 'bill_type' => $arrPostData['bill_type']);

            $arrInvoiceDataListener['invoice_detail'] = $arrIncomeInvoiceDetail;
            $arrInvoiceDataListener['postedData'] = $arrPostData;
            $arrInvoiceDataListener['postedData']['member_id'] = $arrIncomeInvoiceMemberDetail['id'];
            $arrInvoiceDataListener['postedData']['soc_id'] = $arrPostData['soc_id'];

            $oustandingAmountPaid = 0;
            $arrInvoicePaymentDetail = $this->setInvoiceWriteoffPayment($arrInvoiceDataListener);

            $arrPaidInvoiceDetail = $arrInvoicePaymentDetail['arrPaidInvoiceDetail'];

            if (!empty($arrPostData['tds_amount']) && $arrPostData['tds_amount'] > 0) {
                $arrPaidInvoiceDetail = $this->getPaymentDetailsAfterTds(array('arrInvoicePaymentDetail' => $arrPaidInvoiceDetail, 'arrPostData' => $arrPostData));
            }
            if (!empty($arrPaidInvoiceDetail)) {
                //transaction to rollback invoice records in case of failure
                $arrInvoicePaymentResponse = $this->saveIncomeInvoicePayment(array('paymentDetail' => $arrPaidInvoiceDetail, 'arrReceiptDetail' => $arrReceiptDetail));
                if (!empty($arrInvoicePaymentResponse['success']) && count($arrPaidInvoiceDetail) == count($arrInvoicePaymentResponse['success'])) {
                    $countLedgerEntry = 0;

                    if (ACCOUNT_MODULE_EXIST == 1) {
                        $accountDetail = $this->getBankCashAccountDetail(array('soc_id' => $arrPostData['soc_id']));
                        $arrAccountLedgerDetail = $this->getLedgerAccountDetail(array('account_detail' => $accountDetail));

                        $incomeInvoiceParticular = new IncomeInvoiceParticular();
                        $objQueryBuiler = $incomeInvoiceParticular->updateIncomeInvoiceParticularStatus(array('soc_id' => $arrPostData['soc_id'], 'invoice_number' => $arrPostData['member_paid_invoice'], 'updated_date' => date('Y-m-d H:i:s')));

                        $arrListnerData = array('auth' => $arrPostData, 'unit_id' => $unit_id);
                        $arrUnitDetails = $this->getUnitDetailById($arrListnerData);

                        if ($arrUnitDetails['ledger_account_id']) {
                            $arrListnerData['ledger_id'] = $arrUnitDetails['ledger_account_id'];
                        } else {
                            $arrListnerData['ledger_name'] = 'BLDG#' . strtoupper($arrUnitDetails['soc_building_name']) . '-' . $arrUnitDetails['unit_flat_number'];
                            $arrListnerData['context'] = ACCOUNT_RECEIVABLE_GROUP;
                        }
                        $arrUnitLedgerDetails = $this->checkledgerExistNew($arrListnerData);

                        $countLedgerEntry = $this->paymentLedgerEntry(array('auth' => $arrPostData, 'arrPostData' => $arrPostData, 'arrUnitLedgerDetails' => $arrUnitLedgerDetails));
                    }

                    if ($countLedgerEntry == 0) {
                        // add here code for email and sms notification

                        $arrResponse['status'] = 'success';
                    }
                }
            }
        }

        return $arrResponse;
    }

    public function setInvoiceWriteoffPayment($data = array())
    {
        $postedData = $data['postedData'];
        $arrInvoiceDetail = $data['invoice_detail'];
        $arrInvoiceNumber = explode(',', $postedData['member_paid_invoice']);
        $arrPaidInvoiceDetail = $arrTempLedgerDetail = $arrLedgerDetail = array();
        $i = 0;
        $paidAmount = (float) round($postedData['payment_amount'] / count($arrInvoiceNumber), 3);
        $strRuleId = '';
        $totalUnpaidAmount = $previousTotalPending = 0;
        foreach ($arrInvoiceDetail as $eachInvoiceDetail) {
            if (in_array($eachInvoiceDetail['unit_invoice_number'], $arrInvoiceNumber)) {

                // Added late payment tax charges
                $eachInvoiceDetail['interest_amount'] += $eachInvoiceDetail['late_charge_tax_detail']['total_tax'];
                $eachInvoiceDetail['late_payment_interest'] += $eachInvoiceDetail['late_charge_tax_detail']['total_tax'];

                $advanceAmount = $eachInvoiceDetail['advance_amount'];
                $finalInvoiceAmount = $eachInvoiceDetail['invoice_amount_detail']['finalInvoiceAmount'];
                $latePaymentCharges = $eachInvoiceDetail['late_payment_interest'];
                $latePaymentOutstanding = 0;
                if (!empty($eachInvoiceDetail['advance_amount']) && $eachInvoiceDetail['advance_amount'] > 0) {
                    if ($eachInvoiceDetail['advance_amount'] < $finalInvoiceAmount) {
                        $finalInvoiceAmount = (float) round($finalInvoiceAmount - $eachInvoiceDetail['advance_amount'], 3);
                    } else {
                        $finalInvoiceAmount = 0;
                    }
                } elseif ($eachInvoiceDetail['unit_invoice_number'] == $eachInvoiceDetail['unit_first_invoice']) {
                    $latePaymentCharges = $eachInvoiceDetail['late_payment_interest'];
                    $latePaymentOutstanding = (float) $eachInvoiceDetail['principal_amount'];
                }
                // Deduct amount for partial paid
                if (strtolower($eachInvoiceDetail['payment_status']) == 'partialpaid') {
                    if (isset($eachInvoiceDetail['invoice_amount_detail']) && !empty($eachInvoiceDetail['invoice_amount_detail'])) {
                        $advanceAmount += (float) (isset($eachInvoiceDetail['invoice_amount_detail']['partialpaidAmount'])) ? $eachInvoiceDetail['invoice_amount_detail']['partialpaidAmount'] : 0;
                        $advanceAmount += (float) (isset($eachInvoiceDetail['invoice_amount_detail']['partialLatePaymentCharge'])) ? $eachInvoiceDetail['invoice_amount_detail']['partialLatePaymentCharge'] : 0;
                    }
                    $latePaymentCharges = round($latePaymentCharges - $eachInvoiceDetail['invoice_amount_detail']['partialLatePaymentCharge'], 4);
                    if ($latePaymentCharges < 0) {
                        $latePaymentCharges = 0;
                    }
                    $finalInvoiceAmount = round($finalInvoiceAmount - $eachInvoiceDetail['invoice_amount_detail']['partialpaidAmount'], 4);
                }

                $finalInvoiceAmount += $latePaymentOutstanding + $latePaymentCharges;
                $totalUnpaidAmount += (float) $finalInvoiceAmount;
                // $percent = (float)round(($finalInvoiceAmount * 100)/$data['total_unpaid_amount'],2);
                // $arrNewResult[$i]['writeoff_amount'] = (float)round(($arrNewResult[$i]['percent'] * $paidAmount)/100,2);

                $arrPaidInvoiceDetail[$i]['soc_id'] = $eachInvoiceDetail['soc_id'];
                $arrPaidInvoiceDetail[$i]['member_id'] = $postedData['member_id'];
                $arrPaidInvoiceDetail[$i]['unit_id'] = $eachInvoiceDetail['unit_id'];
                $arrPaidInvoiceDetail[$i]['unit_invoice_id'] = $eachInvoiceDetail['unit_invoice_id'];
                $arrPaidInvoiceDetail[$i]['invoice_number'] = $eachInvoiceDetail['unit_invoice_number'];
                $arrPaidInvoiceDetail[$i]['payment_amount'] = 0;
                $arrPaidInvoiceDetail[$i]['payment_mode'] = $postedData['payment_mode'];
                $arrPaidInvoiceDetail[$i]['transaction_reference'] = !empty($postedData['transaction_reference']) ? $postedData['transaction_reference'] : '';
                $arrPaidInvoiceDetail[$i]['payment_instrument'] = !empty($postedData['payment_instrument']) ? $postedData['payment_instrument'] : '';
                $arrPaidInvoiceDetail[$i]['transaction_charges'] = !empty($postedData['transaction_charges']) ? $postedData['transaction_charges'] : 0;
                $arrPaidInvoiceDetail[$i]['late_payment_charges'] = $latePaymentCharges;
                $arrPaidInvoiceDetail[$i]['from_date'] = $eachInvoiceDetail['from_date'];
                $arrPaidInvoiceDetail[$i]['to_date'] = $eachInvoiceDetail['to_date'];
                $arrPaidInvoiceDetail[$i]['status'] = 'paid';
                $arrPaidInvoiceDetail[$i]['payment_status'] = 'paid';
                $arrPaidInvoiceDetail[$i]['unpaid_invoice_amount'] = (float) round($eachInvoiceDetail['total_invoice_due'] - $previousTotalPending, 3); // $finalInvoiceAmount;
                $arrPaidInvoiceDetail[$i]['payment_date'] = $postedData['payment_date'];
                $previousTotalPending = $eachInvoiceDetail['total_invoice_due'];
                // $arrPaidInvoiceDetail[$i]['unpaid_all_invoice_amount'] = $totalUnpaidAmount;
                $i++;
            }
        }
        $arrFinalPaidInvoiceDetail = array();
        $i = 0;
        if (!empty($arrPaidInvoiceDetail)) {
            foreach ($arrPaidInvoiceDetail as $eachPaidInvoice) {
                $arrFinalPaidInvoiceDetail[$i] = $eachPaidInvoice;
                $arrFinalPaidInvoiceDetail[$i]['percent'] = (float) round(($arrFinalPaidInvoiceDetail[$i]['unpaid_invoice_amount'] * 100) / $totalUnpaidAmount, 2);
                $arrFinalPaidInvoiceDetail[$i]['payment_amount'] = (float) round(($arrFinalPaidInvoiceDetail[$i]['percent'] * $postedData['payment_amount']) / 100, 2);
                if ($arrFinalPaidInvoiceDetail[$i]['payment_amount'] > $arrFinalPaidInvoiceDetail[$i]['unpaid_invoice_amount']) {
                    $arrFinalPaidInvoiceDetail[$i]['payment_amount'] = $arrFinalPaidInvoiceDetail[$i]['unpaid_invoice_amount'];
                }
                $arrFinalPaidInvoiceDetail[$i]['writeoff_amount'] = (float) round($arrFinalPaidInvoiceDetail[$i]['unpaid_invoice_amount'] - $arrFinalPaidInvoiceDetail[$i]['payment_amount'], 3);
                $arrFinalPaidInvoiceDetail[$i]['payment_amount'] = (float) round($arrFinalPaidInvoiceDetail[$i]['payment_amount'] - $arrFinalPaidInvoiceDetail[$i]['late_payment_charges'], 3);
                $arrFinalPaidInvoiceDetail[$i]['unpaid_all_invoice_amount'] += $eachPaidInvoice['unpaid_invoice_amount']; // $totalUnpaidAmount;
                $i++;
            }
        }

        return array(
            'arrPaidInvoiceDetail' => $arrFinalPaidInvoiceDetail
        );
    }
}

