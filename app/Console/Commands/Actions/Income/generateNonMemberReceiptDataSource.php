<?php

namespace App\Console\Commands\Actions\Income;

use Illuminate\Console\Command;
use App\Console\Commands\Action;
use App\Models\Tenants\IncomeInvoicePaymentTracker;
use App\Models\Tenants\IncomeInvoiceGeneralSetting;
use App\Models\Tenants\IncomeNonmemberBillPayment;
use App\Models\Tenants\ChsoneGrpLedgerTree;
use App\Models\Tenants\ChsoneNonmemberMaster;

class generateNonMemberReceiptDataSource extends Action
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'datasource:generateNonMemberReceipt {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Generate Non Member Receipt DataSource';

    /**
     * Execute the console command.
     */
    public function apply()
    {
        $data = $this->input;
        $nonMemberBill = $this->input['nonMemberBill'];

        // dd($data);

        // echo '<br/><br/>';

        // dd($nonMemberBill);

        // check if bank_account_id is empty then fecth default bank account id from chsone_accounts_master table
        if(empty($data['bank_account_id'])) {
            $defaultBankAccount = $this->tenantDB()->table('chsone_accounts_master')->where('soc_id', $data['company_id'])->where('status', '1')->where('default_account', '1')->first();
            $data['bank_account_id'] = $defaultBankAccount->account_id ?? '';
        }

        // Create new receipt number
        $newReceiptNumber = $this->generate_receipt_id($data);

        // insert data inot income_invoice_payment_tracker table
        $incomeInvoicePaymentTracker = new IncomeInvoicePaymentTracker;
        $incomeInvoicePaymentTracker->soc_id = $data['company_id'] ?? '0';
        $incomeInvoicePaymentTracker->unit_id = $data['unit_id'] ?? '0';
        $incomeInvoicePaymentTracker->invoice_number = $nonMemberBill['bill_number'] ?? '';
        $incomeInvoicePaymentTracker->receipt_number = $newReceiptNumber;
        $incomeInvoicePaymentTracker->bill_type = 'nonmember';
        $incomeInvoicePaymentTracker->payment_mode = $data['payment_mode'] ?? '';
        if($data['payment_mode'] == 'cheque') {
            $incomeInvoicePaymentTracker->transaction_reference = $data['cheque_number'] ?? '';
            $incomeInvoicePaymentTracker->payment_instrument = !empty($data['payment_instrument']) ? $data['payment_instrument'] : (!empty($data['bank_branch']) ? $data['bank_branch'] : '');
            $incomeInvoicePaymentTracker->cheque_date = $data['cheque_date'] ?? '';
        } elseif($data['payment_mode'] == 'cashtransfer') {
            $incomeInvoicePaymentTracker->transaction_reference = $data['transaction_reference'] ?? '';
        } else {
            $incomeInvoicePaymentTracker->transaction_reference = '';
        }
        $incomeInvoicePaymentTracker->received_from = $nonMemberBill['billed_name'] ?? '';
        $incomeInvoicePaymentTracker->total_due_amount = $data['total_due_amount'] ?? '0.000';
        $incomeInvoicePaymentTracker->late_payment_charges = $data['late_payment_charges'] ?? '0.000';
        $incomeInvoicePaymentTracker->writeoff_amount = $data['writeoff_amount'] ?? '0.000';
        $incomeInvoicePaymentTracker->payment_amount = $data['payment_amount'] ?? '0.000';
        $incomeInvoicePaymentTracker->tds_deducted = $data['tds_amount'] ?? '0.000';
        if($data['payment_mode'] == 'cash') {
            $incomeInvoicePaymentTracker->status = 'Y';
        } elseif($data['payment_mode'] == 'cheque') {
            $incomeInvoicePaymentTracker->status = 'P';
        } elseif($data['payment_mode'] == 'cashtransfer') {
            $incomeInvoicePaymentTracker->status = 'Y';
        }
        $incomeInvoicePaymentTracker->transaction_status = 'complete';
        $incomeInvoicePaymentTracker->payment_token = $this->generate_payment_token($data, $nonMemberBill);
        $incomeInvoicePaymentTracker->payment_note = $data['payment_note'] ?? '';
        $incomeInvoicePaymentTracker->payment_date = $data['payment_date'] ?? date('Y-m-d');
        $incomeInvoicePaymentTracker->created_by = $data['created_by'] ?? '0';
        $incomeInvoicePaymentTracker->updated_by = $data['updated_by'] ?? '0';
        $incomeInvoicePaymentTracker->created_date = $data['created_date'] ?? date('Y-m-d');
        $incomeInvoicePaymentTracker->updated_date = $data['updated_date'] ?? date('Y-m-d');

        // create other information
        $arrOtherInfo = array();
        $arrOtherInfo['nonmember_detail']['nonmember_bill_id'] = $nonMemberBill['nonmember_bill_id'];
        $arrOtherInfo['nonmember_detail']['nonmemberincomeaccount'] = $data['nonmemberincomeaccount'];
        $arrOtherInfo['nonmember_detail']['narration'] = $data['payment_note'] ?? '';
        $arrOtherInfo['nonmember_detail']['bank_ledger'] = $data['bank_account_id'] ?? '';
        $arrOtherInfo['nonmember_detail']['advance_payment'] = $data['advance_amount'] ?? '';

        $incomeInvoicePaymentTracker['other_information'] = serialize($arrOtherInfo);

        $incomeInvoicePaymentTracker->save();
        if ($incomeInvoicePaymentTracker->save()) {

            if($data['payment_mode'] == 'cash' || $data['payment_mode'] == 'cashtransfer') {

                // update advnace amount and payment_status in income_nonmember_bills table
                if($data['booking_charge'] > $data['payment_amount']) {
                    $paymentStatus = 'partialpaid';
                } elseif($data['booking_charge'] == $data['payment_amount']) {
                    $paymentStatus = 'paid';
                }

                $updateNonMemberBill = $this->tenantDB()->table('income_nonmember_bills')->where('nonmember_bill_id', $nonMemberBill['nonmember_bill_id'])->update(['advance_amount' => $data['payment_amount'], 'payment_status' => $paymentStatus]);
                if(!$updateNonMemberBill) {
                    $this->message = "Unable to update non member bill";
                    $this->status = 'failed';
                    $this->statusCode = 400;
                }

                // insert record in the income_nonmember_bill_payments table 
                $nonMemberBillPayment = $this->inserNonMemberBillPayment($data, $nonMemberBill);

                if(!$nonMemberBillPayment) {
                    $this->message = "Unable to insert non member bill payment";
                    $this->status = 'failed';
                    $this->statusCode = 400;
                }

                // insert record in the chsone_ledger_transactions table
                $insertLedgerTransaction = $this->insertLedgerTransaction($data, $nonMemberBill, $incomeInvoicePaymentTracker);
                if(!$insertLedgerTransaction) {
                    $this->message = "Unable to insert ledger transaction";
                    $this->status = 'failed';
                    $this->statusCode = 400;
                }

                // check if tds_amount is set then insert record in the chsone_ledger_transactions table
                if(isset($data['tds_amount']) && !empty($data['tds_amount'])) {
                    $insertTdsLedgerTransaction = $this->insertTdsLedgerTransaction($data, $nonMemberBill, $incomeInvoicePaymentTracker);

                    if(!$insertTdsLedgerTransaction) {
                        $this->message = "Unable to insert tds ledger transaction";
                        $this->status = 'failed';
                        $this->statusCode = 400;
                    }
                }
            }

            $this->message = "Non Member Invoice and Receipt created successfully";
            $this->status = 'success';
            $this->statusCode = 200;
            $this->data = $incomeInvoicePaymentTracker->toArray();
        } else {
            $this->message = "Unable to create receipt";
            $this->status = 'failed';
            $this->statusCode = 400;
        }
    }

    public function generate_receipt_id($data) {
        $settingDetails = IncomeInvoiceGeneralSetting::where('soc_id', $data['company_id'])->where('setting_key', 'INCOME_RECEIPT_PREFIX')->first();

        // get last receipt number
        $last_receipt_number = $this->tenantDB()->table('income_invoice_payment_tracker')->orderBy('id', 'desc')->first();
        if($last_receipt_number && $last_receipt_number->receipt_number) {
            
            $numericPart = (int)preg_replace('/\D/', '', $last_receipt_number->receipt_number);
            // Increment the numeric part
            $newNumericPart = $numericPart + 1;
            // Format the new invoice number
            $newReceiptNumber = $settingDetails->setting_value . str_pad($newNumericPart, 5, '0', STR_PAD_LEFT);
        } else {
            $newReceiptNumber = $settingDetails->setting_value . '00001';
        }
        
        return $newReceiptNumber;
    }

    public function generate_payment_token($data, $nonMemberBill) {
        $paymentToken = '';
        if (!empty($data)) {
            $arrPaymentDetail['soc_id'] = $data['company_id'];
            $arrPaymentDetail['received_from'] = $nonMemberBill['billed_name'];
            $arrPaymentDetail['bill_type'] = 'nonmember';
            $arrPaymentDetail['payment_amount'] = $data['payment_amount'];
            $arrPaymentDetail['payment_mode'] = $data['payment_mode'];
            $arrPaymentDetail['invoice_number'] = $nonMemberBill['bill_number'];
            $arrPaymentDetail['payment_date'] = $data['payment_date'];
            if (!empty($arrPaymentDetail)) {
                $paymentToken = md5(time() . serialize($arrPaymentDetail));
            }
            return $paymentToken;
        }
    }

    public function inserNonMemberBillPayment($data, $nonMemberBill) {
        $nonMemberBillPayment = new IncomeNonmemberBillPayment;
        $nonMemberBillPayment->soc_id = $data['company_id'];
        $nonMemberBillPayment->fk_nonmember_bill_id = $nonMemberBill['nonmember_bill_id'];
        $nonMemberBillPayment->bill_number = $nonMemberBill['bill_number'];
        $nonMemberBillPayment->payment_mode = $data['payment_mode'];
        $nonMemberBillPayment->payment_type = 'receipt';
        $nonMemberBillPayment->payment_amount = $data['payment_amount'];
        $nonMemberBillPayment->tax_deducted = $data['tax_deducted'] ?? '0.000';
        $nonMemberBillPayment->tds_deducted = $data['tds_amount'] ?? '0.000';
        $nonMemberBillPayment->transaction_charges = '0.000';
        $nonMemberBillPayment->discount_amount = '0.000';
        $nonMemberBillPayment->notes = $data['payment_note'] ?? '';
        $nonMemberBillPayment->status = 1;
        $nonMemberBillPayment->approved = 1;
        $nonMemberBillPayment->payment_date = $data['payment_date'];
        $nonMemberBillPayment->created_by = $data['created_by'] ?? '0';
        $nonMemberBillPayment->updated_by = $data['updated_by'] ?? '0';
        $nonMemberBillPayment->created_date = date('Y-m-d');
        $nonMemberBillPayment->updated_date = date('Y-m-d');
        $nonMemberBillPayment->save();

        return $nonMemberBillPayment;
    }

    public function insertLedgerTransaction($data, $nonMemberBill, $incomeInvoicePaymentTracker) {

        $payment_note = $data['payment_note'] ?? '';
        // fetch ledger account id and name from chsone_grp_ledger_tree table based on the payment mode and entity type
        if($data['payment_mode'] == 'cash'){
            $paymentmodeLedgerId = ChsoneGrpLedgerTree::where('entity_type', 'ledger')->where('context', 'cash')->where('ledger_account_name' , 'like', '%Cash%')->orderBy('ledger_account_id', 'desc')->first();
            $from_ledger_id = $paymentmodeLedgerId->ledger_account_id ?? '';
            $from_ldger_name = $paymentmodeLedgerId->ledger_account_name ?? '';
        } elseif($data['payment_mode'] == 'cheque' || $data['payment_mode'] == 'cashtransfer') {
            $paymentmodeLedgerId = ChsoneGrpLedgerTree::where('entity_type', 'ledger')->where('context', 'bank')->where('ledger_account_id', $data['bank_account_id'])->first();
            $from_ledger_id = $paymentmodeLedgerId->ledger_account_id ?? '';
            $from_ldger_name = $paymentmodeLedgerId->ledger_account_name ?? '';
        }

        $obj = $this->tenantDB()->table('chsone_ledger_transactions')
        ->insert([
            'soc_id' => $data['company_id'],
            'transaction_date' => $data['payment_date'],
            'ledger_account_id' => $from_ledger_id,
            'ledger_account_name' => $from_ldger_name,
            'voucher_type' => 'receipt',
            'voucher_reference_number' => $incomeInvoicePaymentTracker['receipt_number'],
            'voucher_reference_id' => $incomeInvoicePaymentTracker['id'],
            'transaction_type' => 'dr',
            'payment_mode' => $data['payment_mode'] ?? '',
            'payment_reference' => $data['transaction_reference'] ?? '',
            'transaction_amount' => $data['payment_amount'] ?? '0.000',
            'txn_from_id' => 0,
            'memo_desc' => '(Receipt No-'. $incomeInvoicePaymentTracker['receipt_number'] .') Amount received against invoice '. $nonMemberBill['bill_number'] .' paid on '. $data['payment_date'].' through '. $data['payment_mode'] .' ['.$payment_note.']',
            'is_opening_balance' => 0,
            'is_reconciled' => 0,
            'is_cancelled' => 0,
            'created_by' => $data['created_by'] ?? '0',
            'added_on' => date('Y-m-d H:i:s'),
        ]);

        $result = $this->tenantDB()->table('chsone_ledger_transactions')->orderBy('txn_id', 'desc')->first();

        // fetch ledger account id and name from chsone_grp_ledger_tree table based on the billed_name
        // $incomeLedgerId = ChsoneGrpLedgerTree::where('entity_type', 'ledger')->where('context', 'sundrydebtors')->where('ledger_account_name', $nonMemberBill['billed_name'])->first();
        $incomeLedgerId = ChsoneGrpLedgerTree::where('entity_type', 'ledger')->where('ledger_account_name', $nonMemberBill['billed_name'])->first();

        $obj2 = $this->tenantDB()->table('chsone_ledger_transactions')
        ->insert([
            'soc_id' => $data['company_id'],
            'transaction_date' => $data['payment_date'],
            'ledger_account_id' => $incomeLedgerId->ledger_account_id ?? 0,
            'ledger_account_name' => $nonMemberBill['billed_name'],
            'voucher_type' => 'receipt',
            'voucher_reference_number' => $incomeInvoicePaymentTracker['receipt_number'],
            'voucher_reference_id' => $incomeInvoicePaymentTracker['id'],
            'transaction_type' => 'cr',
            'payment_mode' => $data['payment_mode'] ?? '',
            'payment_reference' => $data['transaction_reference'] ?? '',
            'transaction_amount' => $data['payment_amount'] ?? '0.000',
            'txn_from_id' => $result->txn_id,
            'memo_desc' => '(Receipt No-'. $incomeInvoicePaymentTracker['receipt_number'] .') Amount received against invoice '. $nonMemberBill['bill_number'] .' paid on '. $data['payment_date'].' through '. $data['payment_mode'] .' ['.$payment_note.']',
            'is_opening_balance' => 0,
            'is_reconciled' => 0,
            'is_cancelled' => 0,
            'created_by' => $data['created_by'] ?? '0',
            'added_on' => date('Y-m-d H:i:s'),
        ]);

        if($obj && $obj2) {
            return true;
        } else {
            return false;
        }
    }

    public function insertTdsLedgerTransaction($data, $nonMemberBill, $incomeInvoicePaymentTracker) {

        // fetch ledger account id and name from chsone_grp_ledger_tree table based on the payment mode and entity type
        if($data['payment_mode'] == 'cash' || $data['payment_mode'] == 'cashtransfer'){
            $paymentmodeLedgerId = ChsoneGrpLedgerTree::where('ledger_account_name', 'tds receivable')->first();
            $from_ledger_id = $paymentmodeLedgerId->ledger_account_id ?? '';
            $from_ldger_name = $paymentmodeLedgerId->ledger_account_name ?? '';
        }

        $obj = $this->tenantDB()->table('chsone_ledger_transactions')
        ->insert([
            'soc_id' => $data['company_id'],
            'transaction_date' => $data['payment_date'],
            'ledger_account_id' => $from_ledger_id,
            'ledger_account_name' => $from_ldger_name,
            'voucher_type' => 'receipt',
            'voucher_reference_number' => $incomeInvoicePaymentTracker['receipt_number'],
            'voucher_reference_id' => $incomeInvoicePaymentTracker['id'],
            'transaction_type' => 'dr',
            'payment_mode' => $data['payment_mode'] ?? '',
            'payment_reference' => $data['transaction_reference'] ?? '',
            'transaction_amount' => $data['payment_amount'] ?? '0.000',
            'txn_from_id' => 0,
            'memo_desc' => 'TDS amount deducted against Invoice '. $nonMemberBill['bill_number'] .' dated '. $data['payment_date'].' through '. $payment_note .' with payment ref. ('.$data['transaction_reference'].')',
            'is_opening_balance' => 0,
            'is_reconciled' => 0,
            'is_cancelled' => 0,
            'created_by' => $data['created_by'] ?? '0',
            'added_on' => date('Y-m-d H:i:s'),
        ]);

        $result = $this->tenantDB()->table('chsone_ledger_transactions')->orderBy('txn_id', 'desc')->first();

        // fetch ledger_account_id from chsone_nonmember_master table whose nonmember_id is $newNonmemberBill->nonmember_id
        $nonmemberMaster = ChsoneNonmemberMaster::where('nonmember_id', $nonMemberBill['nonmember_id'])->first();
        $to_ldger_id = $nonmemberMaster->nonmember_ledger_id;

        $obj2 = $this->tenantDB()->table('chsone_ledger_transactions')
        ->insert([
            'soc_id' => $data['company_id'],
            'transaction_date' => $data['payment_date'],
            'ledger_account_id' => $to_ldger_id,
            'ledger_account_name' => $nonMemberBill['billed_name'],
            'voucher_type' => 'receipt',
            'voucher_reference_number' => $incomeInvoicePaymentTracker['receipt_number'],
            'voucher_reference_id' => $incomeInvoicePaymentTracker['id'],
            'transaction_type' => 'cr',
            'payment_mode' => $data['payment_mode'] ?? '',
            'payment_reference' => $data['transaction_reference'] ?? '',
            'transaction_amount' => $data['payment_amount'] ?? '0.000',
            'txn_from_id' => $result->txn_id,
            'memo_desc' => 'TDS amount deducted against Invoice '. $nonMemberBill['bill_number'] .' dated '. $data['payment_date'].' through '. $data['payment_mode'] .' with payment ref. ('.$data['transaction_reference'].')',
            'is_opening_balance' => 0,
            'is_reconciled' => 0,
            'is_cancelled' => 0,
            'created_by' => $data['created_by'] ?? '0',
            'added_on' => date('Y-m-d H:i:s'),
        ]);

        if($obj && $obj2) {
            return true;
        } else {
            return false;
        }
    }
}
