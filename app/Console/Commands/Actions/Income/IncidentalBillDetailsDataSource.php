<?php

namespace App\Console\Commands\Actions\Income;

use Illuminate\Console\Command;
use App\Console\Commands\Action;
use App\Models\Tenants\ChsoneCreditNote;
use App\Models\Tenants\ChsoneGrpLedgerTree;
use App\Models\Tenants\IncomeInvoiceGeneralSetting;

class IncidentalBillDetailsDataSource extends Action
{
    protected $signature = 'datasource:IncidentalBillDetails {flowId} {parentId} {input}';

    protected $description = 'Get the incidental bill details';

    public function apply()
    {
        //$id is a unit id
        $id = $this->input['id'];

        // check if unit id is valid or not from chsone_units_master table
        $unit = $this->tenantDB()->table('chsone_units_master')
            ->where('unit_id', $id)
            ->get();

        if(count($unit) == 0) {
            $this->status = 'error';
            $this->message = 'Please provide a valid unit id';
            $this->statusCode = 404;
            $this->data = [];
            return;
        }

        $invoice_number = $this->input['invoice_number'];

        // according to this invoice number need to find out the id from income_common_billing_charges table
        if ($invoice_number) {
            $obj = $this->tenantDB()->table('income_common_billing_charges as icb')
                ->select('icb.id')
                ->where('icb.invoice_number', $invoice_number)
                ->where('icb.fk_unit_id', $id)
                ->first();

            if ($obj) {
                $invoice_id = $obj->id;

                $obj = $this->tenantDB()->table('income_common_billing_charges as icb')
                    ->select(
                        'icb.id',
                        'icb.fk_unit_id AS unit_id',
                        'icb.invoice_number',
                        'icb.billing_type',
                        'icac.particular',
                        'icb.advance_amount',
                        'icb.amount',
                        'icb.payment_status',
                        'icb.status',
                        'icb.bill_date',
                        'icb.from_date',
                        'icb.to_date',
                        'icb.due_date',
                        'icb.bill_from',
                        'icb.payment_mode',
                        'icb.transaction_charges',
                        'icb.discount_amount',
                        'icb.payment_instrument',
                        'unit.unit_id',
                        'unit.unit_flat_number',
                        'unit.soc_building_name',
                        'memmaster.member_first_name',
                        'memmaster.gstin',
                        'memmaster.member_last_name',
                        'memmaster.member_mobile_number',
                        'memmaster.member_email_id',
                        'unitInvoice.member_gstin',
                        'unitInvoice.principal_amount',
                        'unitInvoice.interest_amount',
                        'unitInvoice.outstanding_principal',
                        'unitInvoice.outstanding_interest',
                        'unitInvoice.roundoff_amount'
                    )
                    ->leftJoin('chsone_members_master as memmaster', 'icb.fk_member_id', '=', 'memmaster.id')
                    ->leftJoin('chsone_units_master as unit', 'icb.fk_unit_id', '=', 'unit.unit_id')
                    ->leftJoin('income_unit_invoices as unitInvoice', 'icb.invoice_number', '=', 'unitInvoice.invoice_number')
                    ->leftJoin('income_common_area_charges as icac', 'icb.billing_type', '=', 'icac.id')
                    ->leftJoin('income_common_billing_payment as icbp', 'icb.id', '=', 'icbp.fk_common_bill_id')
                    ->where('icb.payment_status', '!=', 'cancelled')
                    ->where('icb.id', $invoice_id)
                    ->selectRaw('IF(SUM(icbp.payment_amount) = 0, 0, SUM(icbp.payment_amount)) AS payment_amount')
                    ->selectRaw('(SELECT IF(SUM(ctl.tax_amount) = 0, 0, SUM(ctl.tax_amount)) FROM chsone_tax_log AS ctl WHERE ctl.invoice_number = unitInvoice.invoice_number) AS tax_amount')
                    ->orderBy('icb.id', 'DESC');

                $result = $obj->first();

                // need to fecth the data from chsone_taxlog table according to $result->invoice_number
                $taxLog = $this->tenantDB()->table('chsone_tax_log')
                    ->select('tax_amount','tax_class_id','tax_rule','tax_rule_id','tax_rate','tax_rate_type')
                    ->where('invoice_number', $result->invoice_number)
                    ->get();

                $taxLog = $taxLog->toArray();
                $result->taxLog = $taxLog;

                // first check if the taxLog is empty or not
                if (!empty($taxLog)) {
                    // need to fecth gst number from chsone_tax_classesm table according to $result->tax_class_id
                    $gstNumber = $this->tenantDB()->table('chsone_tax_classes')
                    ->select('tax_categories_footer')
                    ->where('tax_class_id', $taxLog[0]->tax_class_id)
                    ->first();

                    if($gstNumber) {
                        $result->gstNumber = $gstNumber->tax_categories_footer;
                    } else {
                        $result->gstNumber = '';
                    }
                } else {
                    $result->gstNumber = '';
                }

                // Credit Note Adjustment Data
                $arrCreditNoteText = array();
                $objCreditNote = ChsoneCreditNote::where('credited_invoice_number', $result->invoice_number)->first();
                $objCreditNote = json_decode(json_encode($objCreditNote), true);
                if (!empty($objCreditNote)) {
                    $creditNotes = $objCreditNote;
                    if (sizeof($creditNotes) > 0) {
                        $arrCreditNoteText = $creditNotes;
                    } else {
                        $arrCreditNoteText = array();
                    }
                } else {
                    $arrCreditNoteText = array();
                }

                $result->creditNoteAdjustment = $arrCreditNoteText;

                // add general settings data
                $arrGeneralSettingData = array('soc_id' => $this->input['company_id'], 'setting_key' => array('COMMON_INCOME_PAYMENT_INSTRUCTION', 'INCOME_SHOW_INTEREST_BREAKUP', 'INVOICE_LOGO', 'PAYMENT_TRANSACTION_RECEIPT_LAST_PERIOD', 'SHOW_SOCIETY_SIGNATURE', 'SHOW_CHSONE_FOOTER', 'SHOW_BANK_DETAIL'));
                $arrInvoiceGeneralSetting = $this->getInvoiceGeneralSetting($arrGeneralSettingData);

                if (!empty($arrInvoiceGeneralSetting)) {
                    foreach ($arrInvoiceGeneralSetting as $key => $eachGeneralSetting) {
                        if (strtolower($eachGeneralSetting['setting_key']) == 'common_income_payment_instruction') {
                            $result->arrInvoiceGeneralNote = explode(PHP_EOL, $eachGeneralSetting['setting_value']);
                        }
                        if (strtolower($eachGeneralSetting['setting_key']) == 'income_show_interest_breakup' && $eachGeneralSetting['setting_value'] == 1) {
                            $result->showInterestBreakup = 1;
                        } else {
                            $result->showInterestBreakup = 0;
                        }
                        if (strtolower($eachGeneralSetting['setting_key']) == 'show_society_signature' && strtolower($eachGeneralSetting['setting_value']) == 'yes') {
                            $result->showSocietySignature = true;
                        } else {
                            $result->showSocietySignature = false;
                        }
                        if (strtolower($eachGeneralSetting['setting_key']) == 'show_chsone_footer' && strtolower($eachGeneralSetting['setting_value']) == 'yes') {
                            $result->showChsoneFooter = true;
                        } else {
                            $result->showChsoneFooter = false;
                        }
                        if (strtolower($eachGeneralSetting['setting_key']) == 'show_bank_detail' && strtolower($eachGeneralSetting['setting_value']) == 'yes') {
                            $result->showBankDetail = 1;
                        } else {
                            $result->showBankDetail = 0;
                        }
                    }
                }

                // get bank account details
                $arrAccountDetail = $this->getDefaultBankAccountDetailIncidental(array("soc_id" => $this->input["company_id"]));
                if (empty($arrAccountDetail)) {
                    $arrAccountDetail = $this->getDefaultBankAccountDetail(array("soc_id" => $this->input["company_id"]));
                }

                $result->arrAccountDetail = $arrAccountDetail;

                $this->data = $result;

            } else {
                $this->status = false;
                $this->statusCode = 400;
                $this->message = "No data found for this invoice number";
                $this->data = [];
                return;
            }
        } else {
            $this->status = false;
            $this->statusCode = 400;
            $this->message = "Please provide a invoice number";
            $this->data = [];
            return;
        }
    }

    public function getInvoiceGeneralSetting($data = [])
    {
        $arrInvoiceGeneralSetting = [];

        // Ensure the required keys are present in the input data
        if (!empty($data['soc_id']) && !empty($data['setting_key']) && is_array($data['setting_key'])) {
            $objInvoiceGeneralSetting = IncomeInvoiceGeneralSetting::where('soc_id', $data['soc_id'])
                ->whereIn('setting_key', $data['setting_key'])
                ->get();

            if (!empty($objInvoiceGeneralSetting)) {
                $arrInvoiceGeneralSetting = $objInvoiceGeneralSetting->toArray();
            }
        }

        return $arrInvoiceGeneralSetting;
    }

    public function getDefaultBankAccountDetailIncidental($data = [])
    {
        $arrAccountDetail = [];

        if (!empty($data['soc_id'])) {
            $resultset = $this->tenantDB()->table('chsone_grp_ledger_tree as grpLedger')
            ->select([
                'grpLedger.ledger_account_id',
                'grpLedger.ledger_account_name',
                'grpLedger.context',
                'account.account_name',
                'account.default_account',
                'account.bank_name',
                'account.account_number',
                'account.bank_address',
                'account.branch',
                'account.bank_city',
                'account.bank_ifsc',
                'account.account_id'
            ])
            ->where('grpLedger.soc_id', $data['soc_id'])
            ->where('grpLedger.entity_type', 'ledger')
            ->where('grpLedger.status', 1)
            ->where('account.default_bank_for_incidental', 1)
            ->whereIn('grpLedger.context', ['bank'])
            ->leftJoin('chsone_accounts_master as account', 'grpLedger.ledger_account_id', '=', 'account.ledger_account_id')
            ->first();

            if ($resultset) {
                $arrAccountDetail = (array) $resultset; // Convert the object to an associative array
            }
        }
        return $arrAccountDetail;
    }

    public function getDefaultBankAccountDetail($data = [])
    {
        $arrAccountDetail = [];

        // Build the query using Laravel's Query Builder
        try {
                $query = $this->tenantDB()->table('chsone_grp_ledger_tree as grpLedger')
                ->select([
                    'grpLedger.ledger_account_id',
                    'grpLedger.ledger_account_name',
                    'grpLedger.context',
                    'account.account_name',
                    'account.default_account',
                    'account.bank_name',
                    'account.account_number',
                    'account.bank_address',
                    'account.branch',
                    'account.bank_city',
                    'account.bank_ifsc',
                    'account.account_id',
                    'account.account_name'
                ])
                ->where('grpLedger.soc_id', $data['soc_id'])
                ->where('grpLedger.entity_type', 'ledger')
                ->where('grpLedger.status', 1)
                ->where('account.default_account', 1)
                ->whereIn('grpLedger.context', ['bank'])
                ->leftJoin('chsone_accounts_master as account', 'grpLedger.ledger_account_id', '=', 'account.ledger_account_id');

            // Execute the query and get the first result
            $resultset = $query->first();

            if (!empty($resultset)) {
                $arrAccountDetail = (array) $resultset; // Convert the object to an associative array
            }

            return $arrAccountDetail;
        } catch (\Exception $e) {
            // Log the error
            throw new \Exception("Error in getDefaultBankAccountDetail: " . $e->getMessage());
        }
    }
}
