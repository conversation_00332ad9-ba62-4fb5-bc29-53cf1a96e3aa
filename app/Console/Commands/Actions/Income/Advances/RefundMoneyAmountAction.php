<?php

namespace App\Console\Commands\Actions\Income\Advances;

use App\Console\Commands\Action;
use App\Http\Traits\MongoTraits;
use Illuminate\Support\Facades\Config;

// STEP 8 : Create a new action class whitch extends Action class. Keep signature  same as you specified in workflow.
class RefundMoneyAmountAction extends Action
{
    use MongoTraits; // Use the MongoTraits trait in this class

    protected $constants;

    public function __construct()
    {
        parent::__construct();
        $this->constants = Config::get("constants");
    }

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'datasource:refundMoneyAmount {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Refund Money';

    /**
     * Execute the console command.
     */

    public function apply()
    {
        $soc_id = $this->input['company_id'];
        $account_id = $this->input['account_id'];
        $type = $this->input['type'];

        if($type == "member"){
            $type = "unit";
        }

        // check if the account_id is valid or not from chsone_credit_accounts table
        $CreditDetails = $this->tenantDB()->table('chsone_credit_accounts')->where('account_id', $account_id)->first();

        if (!$CreditDetails) {
            $this->status = "error";
            $this->statusCode = 400;
            $this->message = "Invalid Account Id";
            return;
        }

        $obj = $this->tenantDB()->table('chsone_credit_accounts AS cr_acc')
            ->selectRaw(
                'cr_acc.account_id AS id,
                        cr_acc.account_id,
                        MAX(cr_acc.account_name) AS account_name'
            )
            // Earlier this line was causing error uncomment in future if wanted
            // ->join('chsone_vendors_master AS vendormaster', 'cr_acc.account_id', '=', 'vendormaster.vendor_id')
            ->where('cr_acc.account_context', '=', $type)
            ->where('cr_acc.account_id', '=', $account_id)
            ->where('cr_acc.soc_id', '=', $soc_id)
            ->groupBy(
                'cr_acc.account_id'
            )
            ->orderByDesc('cr_acc.credit_account_id')

->selectRaw('
    SUM(IF(cr_acc.transaction_type = "cr", cr_acc.amount, 0)) AS total_cr,
    SUM(IF(cr_acc.transaction_type = "dr", cr_acc.amount, 0)) AS total_dr,
    SUM(IF(cr_acc.transaction_type = "dr" AND cr_acc.use_credit = "refundable", cr_acc.amount, 0)) AS total_refundable_dr,
    SUM(IF(cr_acc.transaction_type = "cr" AND cr_acc.use_credit = "refundable", cr_acc.amount, 0)) AS total_refundable_cr,
    SUM(IF(cr_acc.transaction_type = "dr" AND cr_acc.use_credit = "adjustable", cr_acc.amount, 0)) AS total_adjustable_dr,
    SUM(IF(cr_acc.transaction_type = "cr" AND cr_acc.use_credit = "adjustable", cr_acc.amount, 0)) AS total_adjustable_cr,
    SUM(IF(cr_acc.transaction_type = "dr" AND cr_acc.use_credit = "adjustable", cr_acc.amount, 0)) - SUM(IF(cr_acc.transaction_type = "cr" AND cr_acc.use_credit = "adjustable", cr_acc.amount, 0)) AS total_adjustable,
    SUM(IF(cr_acc.transaction_type = "dr" AND cr_acc.use_credit = "refundable", cr_acc.amount, 0)) - SUM(IF(cr_acc.transaction_type = "cr" AND cr_acc.use_credit = "refundable", cr_acc.amount, 0)) AS total_refundable,
    SUM(IF(cr_acc.transaction_type = "dr", cr_acc.amount, 0)) - SUM(IF(cr_acc.transaction_type = "cr", cr_acc.amount, 0)) AS total_balance,
    IF(
        SUM(IF(cr_acc.transaction_type = "dr" AND cr_acc.use_credit = "refundable", cr_acc.amount, 0)) 
        - SUM(IF(cr_acc.transaction_type = "cr" AND cr_acc.use_credit = "refundable", cr_acc.amount, 0)) > 0, 
        1, 0
    ) AS is_reversable
        ');
        // $obj = $this->filter($obj);
        $result = $obj->first();

        $this->data = $result;
    }
}
