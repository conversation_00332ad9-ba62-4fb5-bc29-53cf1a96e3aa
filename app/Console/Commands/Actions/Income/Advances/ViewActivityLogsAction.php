<?php

namespace App\Console\Commands\Actions\Income\Advances;

use Illuminate\Console\Command;
use App\Console\Commands\Action;

class ViewActivityLogsAction extends Action
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'datasource:viewActivityLogs {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'View Activity Logs for Members and Nonmembers';

    protected $formatter = [
        'id' => "",
        'credit_account_id' => "",
        'account_name' => "",
        'amount' => "",
        'account_context' => "",
        'type' => "",
        'payment_mode' => "",
        'narration' => "",
        'use_credit' => "",
        'adjust_against' => "",
        'payment_reference' => "",
        'transaction_type' => "",
        'payment_date' => "",
        'bill_rectification' => "",
        'adjust_after' => "",
        'created_name' => "",
        'updated_by' => "",
        'created_date' => "",
        'updated_date' => ""
    ];

    protected $formatterByKeys = ['id'];

    public function apply()
    {
        $id = $this->input['id'];
        if (isset($this->input['type'])) {
            if (strtolower($this->input['type']) == "member") {
                $type = "unit";
            } else {
                $type = $this->input['type'];
            }
        }
        $viewActivityLogs = $this->tenantDB()->table('chsone_credit_accounts')
                                ->select(
                                    'credit_account_id AS id',
                                    'account_name',
                                    'amount',
                                    'account_context',
                                    'type',
                                    'narration',
                                    'payment_mode',
                                    'use_credit',
                                    'use_credit_for AS adjust_against',
                                    'reference_no AS payment_reference',
                                    'transaction_type',
                                    'payment_date',
                                    'is_invoice_rectification AS bill_rectification',
                                    'use_credit_after AS adjust_after',
                                    'created_name',
                                    'created_by',
                                    'updated_by',
                                    'created_date',
                                    'updated_date'
                                )
                                ->where('credit_account_id', $id)
                                ->where('account_context', $type)
                                ->orderByDesc('credit_account_id')
                                ->first();
        $this->data = $viewActivityLogs;
    }
}
