<?php

namespace App\Console\Commands\Actions\Income\Advances;

use Illuminate\Console\Command;
use App\Console\Commands\Action;
use Illuminate\Support\Arr;

class GetMemberAdvancesDataSource extends Action
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'datasource:GetMemberAdvances {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    // protected  $smartFormatter = [
    //     'unit_id' => [
    //         'unit_id' => '',
    //         'advances' => [
    //             'cr_amount' => '',
    //             'dr_amount' => '',
    //             'advances' => '',
    //         ]
    //     ]
    // ];

    protected $formatter = [
        'unit_id' => '',
        'cr_amount' => '',
        'dr_amount' => '',
        'advances' => '',
    ];

    protected $formatterByKeys = ['unit_id'];

    protected $mapper = [
        'unit_id' => 'account_id',
    ];

    /**
     * Execute the console command.
     */
    public function apply()
    {
        $obj = $this->tenantDB()->table('chsone_credit_accounts')
        ->select('account_id as unit_id')
        ->where('is_locked', '!=', 1)
        ->where('use_credit_for', 'maintenance')
        ->where('account_context', 'unit')
        ->groupBy('account_id')
        ->selectRaw('SUM(CASE WHEN transaction_type = "cr" THEN amount ELSE 0 END) AS cr_amount')
        ->selectRaw('SUM(CASE WHEN transaction_type = "dr" THEN amount ELSE 0 END) AS dr_amount')
        ->selectRaw('SUM(CASE WHEN transaction_type = "cr" THEN amount ELSE 0 END) - SUM(CASE WHEN transaction_type = "dr" THEN amount ELSE 0 END) AS advances');

        $obj = $this->filter($obj);
        // $obj = $obj->orderBy('account_id', 'asc');
        $advances = $obj->get();
        // $this->data = $this->smartFormat($advances->toArray());
        $this->data = $this->format($advances->toArray());
        $this->message = 'Get payments success';
        $this->statusCode = 200;
    }
}
