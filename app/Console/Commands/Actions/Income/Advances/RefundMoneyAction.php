<?php

namespace App\Console\Commands\Actions\Income\Advances;

use App\Console\Commands\Action;
use App\Http\Traits\MongoTraits;
use App\Models\Tenants\ChsoneLedgerTransaction;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\DB;

// STEP 8 : Create a new action class whitch extends Action class. Keep signature  same as you specified in workflow.
class RefundMoneyAction extends Action
{

    use MongoTraits; // Use the MongoTraits trait in this class

    protected $constants;

    public function __construct()
    {
        parent::__construct();
        $this->constants = Config::get("constants");
    }

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'datasource:refundMoney {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Refund Money';

    /**
     * Execute the console command.
     */

    public function apply()
    {
        // $this->input['account_context'] = 'unit';
        $ip = $this->input['additional_data']['ip'];
        $this->input['soc_id'] = $this->input['company_id'];
        $this->input['account_id'] = $this->input['id'];
        $data[] = [];
        $data = $this->input;
        $data['id'] = $this->input['account_id'];
        $data['account_id'] = $this->input['account_id'];

        $data['payment_date'] = date("Y-m-d");

        $data['soc_id'] = $this->input['company_id'];
        $creditData = $this->input;
        $creditData['account_id'] = $this->input['account_id'];
        // Use isset to avoid undefined array key notice
        $data['account_context'] = isset($this->input['account_context'])
            ? $this->input['account_context']
            : (isset($this->input['type']) ? $this->input['type'] : null);
        $creditData['account_context'] = isset($this->input['account_context'])
            ? $this->input['account_context']
            : ($this->input['type'] ?? null);

        $refund_date = $this->input['refund_date'] ?? date("Y-m-d");
        $refund_ref = $this->input['refund_reference'] ?? '';
        $refund_narration = $this->input['refund_narration'] ?? '';

        // found $creditData['account_name'] from chsone_grp_ledger_tree table
        if($creditData['account_context'] == 'vendor'){
            $creditData['account_name'] = $this->tenantDB()->table('chsone_vendors_master')
            ->where('vendor_id', $creditData['account_id'])
            ->value('vendor_name');
        }else{
            $creditData['account_name'] = $this->tenantDB()->table('chsone_grp_ledger_tree')
            ->where('ledger_account_id', $creditData['account_id'])
            ->value('ledger_account_name');
        }
        
        $data['account_name'] = $creditData['account_name'];

        if ($creditData['payment_mode'] != 'cash') {

            $checkBankAccount = $this->tenantDB()->table('chsone_accounts_master')->where('ledger_account_id', $creditData['bank_account'])->first();

            if (empty($checkBankAccount)) {
                $this->message = "Bank account not found.";
                $this->status = 'error';
                $this->statusCode = 400;
                return;
            }

            $add_narration = '';
            if (!empty($refund_ref)) {
                $add_narration .= ' ( ' . $refund_ref . ' )';
            }

            if (!empty($refund_date)) {
                $add_narration .= 'on ' . $refund_date . '.';
            }

            if (!empty($refund_narration)) {
                $add_narration .= ' [' . $refund_narration . ']';
            }
        } else {
            $add_narration = '';
        }
        $creditData['payment_date'] = !empty($refund_date) ? ($refund_date) : date("Y/m/d");
        $creditData['transaction_type'] = 'dr';
        $creditData['narration'] = $data['narration'] = 'Amount of Rs ' . $this->input['payment_amount'] . ' has been refunded through ' . ucfirst($this->input['payment_mode']) . $add_narration;
        if ($creditData['account_context'] == 'nonmember' || $creditData['account_context'] == 'unit') {
            $creditData['context'] = 'user';
            $data['context'] = 'user';
            $data['type'] = 'income';
        } elseif ($creditData['account_context'] == 'vendor') {
            $creditData['context'] = 'system';
            $data['context'] = 'system';
            $data['type'] = 'expense';
        }
        $creditData['credit_used_type'] = 'refundable';
        $data['credit_used_type'] = 'refundable';
        $narration = $creditData['narration'];
        if (empty($data['id'])) {
            $this->message = "No Record found";
            $this->status = "error";
            $this->statusCode = 400;
            return;

            // $arrResponse = array('status' => 'error', 'message' => 'No Record found');
            // $this->session->set("err_msg", $arrResponse['message']);
            // echo json_encode($arrResponse);
            // exit();
        } elseif (!empty($data['id'])) {
            $creditData['refund_option'] = $this->input['payment_mode'];
            $creditData['bank_account'] = $this->input['bank_account'];
            $creditData['action'] = 'refund-money';
            //            $this->afterSubmitFormValidation($data);
            // $this->soc_db_w = $this->di->getShared('soc_db_w');
            // $this->soc_db_w->begin();
            //add reference in ledger entry
            $arrPaymentTrackerDetail = $this->getReceiptTrackerByCreditId($data['soc_id'], $data['id']); //get all Unit details

            if (!empty($arrPaymentTrackerDetail)) {
                $creditData['receipt_number'] = $arrPaymentTrackerDetail->receipt_number ?? '';
                $creditData['payment_tracker_id'] = $arrPaymentTrackerDetail->id ?? '';
            }
            if ($creditData['account_context'] == 'nonmember') {

                $arrAccountDetail = $this->getBankCashAccountDetail(array('soc_id' => $data['soc_id'])); //get all Unit details

                $arrLedgerAccountDetail = $this->getLedgerAccountDetail(array('account_detail' => $arrAccountDetail)); //get all Unit details

                if (!empty($arrLedgerAccountDetail)) {

                    $arrToLedgerDetails['recieving_ledger_id'] = $arrLedgerAccountDetail['cash']['ledger_id'];
                    $arrToLedgerDetails['receiver_name'] = $arrLedgerAccountDetail['cash']['ledger_name'];
                    if (strtolower($creditData['payment_mode']) != 'cash') {
                        if (!empty($creditData['bank_account'])) {
                            $arrToLedgerDetails['recieving_ledger_id'] = $creditData['bank_account'];
                            $arrToLedgerDetails['receiver_name'] = $arrLedgerAccountDetail['arrBank'][$creditData['bank_account']];
                        } else {
                            $arrToLedgerDetails['recieving_ledger_id'] = $arrLedgerAccountDetail['bank']['ledger_id'];
                            $arrToLedgerDetails['receiver_name'] = $arrLedgerAccountDetail['bank']['ledger_name'];
                        }
                    }
                }
                $arrFromLedgerDetails = $this->checkledgerExistOrCreate(array("soc_id" => $data['soc_id'], 'ledger_name' => $creditData['account_id'] . '-' . $creditData['account_name'], 'user_id' => $creditData['user_id'] ?? 0));

                //Insert transaction entry
                $creditData['income_account_ledger_id'] = $arrFromLedgerDetails['recieving_ledger_id'];
                $narration = $creditData['narration'];
                $intBookerLedgerDetails = $this->payNonmemberBillLedger(array('soc_id' => $data['soc_id'], 'postData' => $creditData, 'arrBookerLedgerDetails' => $arrToLedgerDetails, 'to_leger' => $arrToLedgerDetails, 'from_ledger' => $arrFromLedgerDetails, 'narration' => $narration));
                if (!$intBookerLedgerDetails) {

                    $this->message = "Unable to complete transaction, Please try later.";
                    $this->status = "error";
                    $this->statusCode = 400;
                    return;

                    // $this->soc_db_w->rollback();
                    // $this->session->set("err_msg", 'Unable to complete transaction, Please try later.');
                    // if($tab == 'nonmember'){
                    //     return $this->response->redirect($this->config->system->full_base_url . "credit-accounts/nonmemberAdvances");
                    // }else{
                    //     return $this->response->redirect($this->config->system->full_base_url . "credit-accounts/memberAdvances");
                    // }
                }
                $id = $this->addCreditAmountresponse($creditData);
            } elseif ($creditData['account_context'] == 'unit') {
                $arrDataListener['soc_id'] = $data['soc_id'];

                $arrDataListener['unit_id'] = $data['id'];

                $arrIncomeInvoiceMemberDetail = $this->getMemberDetail($arrDataListener); //get all Unit details

                $arrAccountDetail = $this->getBankCashAccountDetail(array('soc_id' => $data['soc_id'])); //get all Unit details

                $arrLedgerAccountDetail = $this->getLedgerAccountDetail(array('account_detail' => $arrAccountDetail)); //get all Unit details

                if (!empty($arrLedgerAccountDetail)) {

                    $arrToLedgerDetails['recieving_ledger_id'] = $arrLedgerAccountDetail['cash']['ledger_id'];
                    $arrToLedgerDetails['receiver_name'] = $arrLedgerAccountDetail['cash']['ledger_name'];
                    if (strtolower($creditData['payment_mode']) != 'cash') {
                        if (!empty($creditData['bank_account'])) {
                            $arrToLedgerDetails['recieving_ledger_id'] = $creditData['bank_account'];
                            $arrToLedgerDetails['receiver_name'] = $arrLedgerAccountDetail['arrBank'][$creditData['bank_account']];
                        } else {
                            $arrToLedgerDetails['recieving_ledger_id'] = $arrLedgerAccountDetail['bank']['ledger_id'];
                            $arrToLedgerDetails['receiver_name'] = $arrLedgerAccountDetail['bank']['ledger_name'];
                        }
                    }
                }

                $unitData = $this->tenantDB()->table('chsone_units_master')
                    ->where('unit_id', $creditData['account_id'])
                    ->first();

                if (empty($unitData)) {
                    $this->message = "No Record found";
                    $this->status = "error";
                    $this->statusCode = 400;
                    return;
                }

                $unitData = json_decode(json_encode($unitData), true);

                $buildingUnit = $unitData['soc_building_name'] . '-' . $unitData['unit_flat_number'];
                $arrFromLedgerDetails = $this->checkledgerExistOrCreate(array("soc_id" => $data['soc_id'], 'ledger_name' => $buildingUnit, 'user_id' => $creditData['user_id'] ?? 0));

                $creditData['income_account_ledger_id'] = $arrFromLedgerDetails['recieving_ledger_id'];
                $arrResponse = $this->createMemberLedgerCredit(array('soc_id' => $data['soc_id'], 'unit_id' => $creditData['account_id'], 'arrPostData' => $creditData, 'arrIncomeInvoiceMemberDetail' => $arrIncomeInvoiceMemberDetail, 'from_ledger' => $arrFromLedgerDetails, 'to_ledger' => $arrToLedgerDetails, 'narration' => $narration));

                if ($arrResponse['status'] == 'error') {
                    $this->message = "Unable to complete transaction, Please try later.";
                    $this->status = "error";
                    $this->statusCode = 400;
                    return;
                    // $this->soc_db_w->rollback();
                    // $this->session->set("err_msg", 'Unable to complete transaction, Please try later.');
                    // return $this->response->redirect($this->config->system->full_base_url . "credit-accounts/memberAdvances");
                }
                $id = $this->addCreditAmountresponse($creditData);
            } else {
                // this is for vendor
                $arrAccountDetail = $this->getBankCashAccountDetail(array('soc_id' => $data['soc_id'])); //get all Unit details

                $arrLedgerAccountDetail = $this->getLedgerAccountDetail(array('account_detail' => $arrAccountDetail)); //get all Unit details

                if (!empty($arrLedgerAccountDetail)) {

                    $arrToLedgerDetails['recieving_ledger_id'] = $arrLedgerAccountDetail['cash']['ledger_id'];
                    $arrToLedgerDetails['receiver_name'] = $arrLedgerAccountDetail['cash']['ledger_name'];
                    if (strtolower($creditData['payment_mode']) != 'cash') {
                        if (!empty($creditData['bank_account'])) {
                            $arrToLedgerDetails['recieving_ledger_id'] = $creditData['bank_account'];
                            $arrToLedgerDetails['receiver_name'] = $arrLedgerAccountDetail['arrBank'][$creditData['bank_account']];
                        } else {
                            $arrToLedgerDetails['recieving_ledger_id'] = $arrLedgerAccountDetail['bank']['ledger_id'];
                            $arrToLedgerDetails['receiver_name'] = $arrLedgerAccountDetail['bank']['ledger_name'];
                        }
                    }
                }

                $to_ledger_id = $this->_createVendorLegder($data['account_id'], true);
                $name = $this->getLedger($to_ledger_id, 0)->ledger_account_name;
                //Insert transaction entry
                if (ACCOUNT_MODULE_EXIST == 1) {

                    $this->executeVouchers(
                        VOUCHER_PAYMENT,
                        $arrToLedgerDetails['recieving_ledger_id'], //to
                        $to_ledger_id, //from
                        $data['payment_date'],
                        $data['payment_amount'],
                        $data['narration'],
                        $arrToLedgerDetails['receiver_name'], // to name
                        $name, //from name
                        "",
                        "",
                        $data['payment_mode'],
                        "",
                        $data['soc_id'],
                        "",
                        ""
                    );
                    $creditaccount = $data;
                    $creditaccount['vendor'] = $data['account_name'];
                    $creditaccount['vendor_id'] = $data['account_id'];
                    $creditaccount['vendor_bill_payment_amount'] = $data['payment_amount'];
                    $creditaccount['vendor_bill_type_purchase'] = $data['payment_mode'];
                    $creditaccount['vendor_bill_payment_date'] = $data['payment_date'];
                    $creditaccount['transaction_type'] = 'cr';
                    $creditaccount['context'] = $data['context'];
                    $response = $this->saveCreditAccountResponse(array('auth' => true, 'process' => 'fetch', 'soc_id' => $data['soc_id'], 'id' => 0, 'data' => $creditaccount, 'user' => $this->input['user_id']??0, 'username' => $this->input['user']['first_name'] . ' ' . $this->input['user']['last_name']));
                    if (!empty($response['error'])) {
                        $this->message = "Unable to complete transaction, Please try later.";
                        $this->status = "error";
                        $this->statusCode = 400;
                        return;
                    } else {
                        $this->message = "Money has been refunded through " . $creditData['payment_mode'];
                        $this->status = "success";
                        $this->statusCode = 200;
                        return;
                    }
                }
            }

            if ($id['error'] == true) {
                $this->message = "Unable to complete transaction, Please try later.";
                $this->status = "error";
                $this->statusCode = 400;
                return;
            }

            $notification_title = 'Advanced Refunded';

            $document = [
                'title' => $notification_title,
                'desc' => $creditData['narration'],
                'scope' => 'group',
                'module' => 'chsone_credit_accounts',
                'company_id' => $this->input['company_id'],
                'user_ids' => $this->input['user_id'] ?? 0,
                'created_by' => $this->input['user_id'] ?? 0,
                'username' => $this->input['user']['first_name'] . ' ' . $this->input['user']['last_name'],
                'ip_address' => $ip,
                'role' => 'admin',
                'approve_link' => $this->input['approve_link'] ?? '',
                'action_url' => $this->input['action_url'] ?? '',
                'deny_link' => $this->input['deny_link'] ?? '',
                'id' => $this->input['id'] ?? '',
                'notified_status' => $this->input['notified_status'] ?? 0,
                'to_be_notified' => $this->input['to_be_notified'] ?? 0,
                'member_id' => $this->input['member_id'] ?? [],
                'all_admin' => $this->input['all_admin'] ?? [],
                'notification_id' => $this->input['notification_id'] ?? '',
                'date_time' => date(format: "d-M-Y H:i:s"),

            ];

            $this->addNotification($document);
            $this->message = "Money has been refunded through " . $creditData['payment_mode'];
            $this->status = "success";
            $this->statusCode = 200;
            return;
        }
    }

    public function getBankCashAccountDetail($data = [])
    {
        // Define the base query with joins and column selections
        $query = $this->tenantDB()->table('chsone_grp_ledger_tree as grpLedger')
            ->select([
                'grpLedger.ledger_account_id',
                'grpLedger.ledger_account_name',
                'grpLedger.context',
                'account.default_account',
                'account.default_bank_for_incidental',
                'account.default_bank_for_nonmember',
                'account.bank_name',
                'account.account_number',
                'account.bank_address',
                'account.bank_city',
                'account.bank_ifsc',
                'account.account_id',
            ])
            ->leftJoin('chsone_accounts_master as account', 'grpLedger.ledger_account_id', '=', 'account.ledger_account_id')
            ->where('grpLedger.soc_id', $data['soc_id'])
            ->where('grpLedger.entity_type', 'ledger')
            ->where('grpLedger.status', 1);

        // Filter by context based on 'only_bank' flag
        if (!empty($data['only_bank'])) {
            $query->where('grpLedger.context', 'bank');
        } else {
            $query->whereIn('grpLedger.context', ['cash', 'bank']);
        }

        // Execute the query and get results as an array
        $arrAccountDetail = $query->get()->toArray();

        return $arrAccountDetail;
    }

    public function getLedgerAccountDetail(array $data): array
    {
        $arrAccountDetail = [
            'cash' => [],
            'bank' => [],
            'arrBank' => [],
            'arrCash' => [],
        ];

        $data = json_decode(json_encode($data), true);
        // Check if account details are provided
        if (!empty($data['account_detail'])) {

            foreach ($data['account_detail'] as $eachAccountDetail) {

                $context = strtolower($eachAccountDetail['context']);
                $ledgerId = $eachAccountDetail['ledger_account_id'];
                $ledgerName = $eachAccountDetail['ledger_account_name'];

                // Handle cash account details
                if ($context === 'cash' && empty($arrAccountDetail['cash'])) {
                    $arrAccountDetail['cash'] = [
                        'ledger_id' => $ledgerId,
                        'ledger_name' => $ledgerName,
                    ];
                    $arrAccountDetail['arrCash'][$ledgerId] = $ledgerName;
                }

                // Handle bank account details
                if ($context === 'bank') {
                    if ($eachAccountDetail['default_account'] == 1) {
                        $arrAccountDetail['bank'] = [
                            'ledger_id' => $ledgerId,
                            'ledger_name' => $ledgerName,
                        ];
                    }
                    $arrAccountDetail['arrBank'][$ledgerId] = $ledgerName;

                    // Default bank for incidental cases
                    if (!empty($data['default_bank_incidental']) && $eachAccountDetail['default_bank_for_incidental'] == 1) {
                        $arrAccountDetail['bank'] = [
                            'ledger_id' => $ledgerId,
                            'ledger_name' => $ledgerName,
                        ];
                    }

                    // Default bank for non-member cases
                    if (!empty($data['default_bank_nonmember']) && $eachAccountDetail['default_bank_for_nonmember'] == 1) {
                        $arrAccountDetail['bank'] = [
                            'ledger_id' => $ledgerId,
                            'ledger_name' => $ledgerName,
                        ];
                    }
                }
            }

            // Set the first available bank if no default is selected
            if (empty($arrAccountDetail['bank']) && !empty($arrAccountDetail['arrBank'])) {
                $firstBank = reset($arrAccountDetail['arrBank']);
                $firstBankId = key($arrAccountDetail['arrBank']);
                $arrAccountDetail['bank'] = [
                    'ledger_id' => $firstBankId,
                    'ledger_name' => $firstBank,
                ];
            }
        }

        return $arrAccountDetail;
    }

    public function checkledgerExistOrCreate($data = [])
    {
        $arrClientLedgerDetails = [];
        $data['user_id'] = $data['user_id'] ?? 0;
        $ledgerName = $data['ledger_name'];

        // An outsider has booked for the society. Check if exists by name; otherwise, create new.
        $objBookerLedger = $this->tenantDB()->table('chsone_grp_ledger_tree')
            ->where('soc_id', $data['soc_id'])
            ->where('context', 'security deposit')
            ->where('entity_type', ENTITY_TYPE_LEDGER)
            ->where('ledger_account_name', $ledgerName)
            ->first();

        $objBookerLedger = json_decode(json_encode($objBookerLedger), true);

        if (!empty($objBookerLedger)) {
            $arrClientLedgerDetails['recieving_ledger_id'] = $objBookerLedger['ledger_account_id'];
            $arrClientLedgerDetails['receiver_name'] = $objBookerLedger['ledger_account_name'];
        } else {
            // Ledger for the outsider does not exist. Create a ledger entry based on sundry debtors group.
            $objSecurityDepositGroup = $this->tenantDB()->table('chsone_grp_ledger_tree')
                ->where('soc_id', $data['soc_id'])
                ->where('entity_type', ENTITY_TYPE_GROUP)
                ->where('ledger_account_name', 'Security Deposit')
                ->first();

            if ($objSecurityDepositGroup) {

                $ledgerAccountId = $this->manipulate(
                    $ledgerName,
                    ENTITY_TYPE_LEDGER,
                    "",
                    $objSecurityDepositGroup->ledger_account_id,
                    $objSecurityDepositGroup->behaviour,
                    '',
                    0,
                    '',
                    '',
                    null,
                    $objSecurityDepositGroup->context,
                    0,
                    $data['soc_id'],
                    0,
                    $data['user_id']
                );

                if (gettype($ledgerAccountId) != 'boolean' && strpos('DUP', $ledgerAccountId) === false) {
                    $arrClientLedgerDetails['recieving_ledger_id'] = $ledgerAccountId;
                    $arrClientLedgerDetails['receiver_name'] = $ledgerName;
                }
            }
        }

        return $arrClientLedgerDetails;
    }

    public function getReceiptTrackerByCreditId($socId, $creditId)
    {
        $arrMemberInvoiceDetail = [];

        if (!empty($socId) && !empty($creditId)) {
            $resultset = $this->tenantDB()->table('income_invoice_payment_tracker as I')
                ->select('I.id', 'I.receipt_number')
                ->leftJoin('chsone_credit_accounts as C', 'C.payment_tracker_id', '=', 'I.id')
                ->where('I.soc_id', $socId)
                ->where('C.credit_account_id', $creditId)
                ->first();

            if (!$resultset) {
                $arrMemberInvoiceDetail = $resultset;
            }
        }

        return $arrMemberInvoiceDetail;
    }
    public function createMemberLedgerCredit($arrData = [])
    {
        $arrResponse = ['status' => 'error'];
        $arrPostData = $arrData['arrPostData'];

        if (!empty($arrPostData)) {
            // Member
            $arrIncomeInvoiceMemberDetail = $arrData['arrIncomeInvoiceMemberDetail'];
            if ($arrPostData['payment_amount'] > 0) {
                $countLedgerEntry = 0;

                if (!empty($arrIncomeInvoiceMemberDetail)) {
                    $arrPostData['unit_id'] = $arrIncomeInvoiceMemberDetail['fk_unit_id'];
                    $arrPostData['member_id'] = $arrIncomeInvoiceMemberDetail['id'];
                }

                if (!empty($arrPostData['payment_date']) && str_contains($arrPostData['payment_date'], '-')) {
                    $arrPostData['payment_date'] = $this->getDisplayDate($arrPostData['payment_date']);
                }

                if (ACCOUNT_MODULE_EXIST == 1) {
                    $arrListnerData = ['soc_id' => $arrData['soc_id'], 'unit_id' => $arrData['unit_id']];
                    $arrUnitDetails = $this->getUnitDetailById($arrListnerData);

                    $arrUnitDetails = json_decode(json_encode($arrUnitDetails), true);
                    if (!empty($arrUnitDetails)) {
                        $arrListnerData['ledger_id'] = $arrUnitDetails['ledger_account_id'];
                        $arrUnitLedgerDetails = $this->checkledgerExistNew($arrListnerData);

                        if (!empty($arrUnitLedgerDetails)) {

                            $countLedgerEntry = $this->paymentLedgerEntry([
                                'soc_id' => $arrData['soc_id'],
                                'arrPostData' => $arrPostData,
                                'arrUnitLedgerDetails' => $arrUnitLedgerDetails,
                                'from_ledger' => $arrData['from_ledger'] ?? '',
                                'to_ledger' => $arrData['to_ledger'] ?? '',
                                'narration' => $arrData['narration'] ?? '',
                            ]);

                            if (!$countLedgerEntry) {

                                $arrResponse['status'] = 'success';
                            }
                        }
                    }

                    // Send notification
                    if ($countLedgerEntry == 0) {
                        // Send notification code
                    }
                }
            }
        }

        return $arrResponse;
    }

    public function addCreditAmountresponse($data, $id = 0)
    {

        $id = ($id) ? $id : '';
        if ($data['credit_used_type'] == 'adjustable' && empty($data['adjustable_date'])) {
            //            $data['adjustable_date'] = date("Y-m-d");  // store today date
        }

        $response = $this->saveCreditAccountResponse(array(
            'auth' => true,
            'process' => 'fetch',
            'soc_id' => $data['soc_id'],
            'id' => $id,
            'data' => $data,
            'user' => $data['user_id'] ?? 0,
            'username' => $data['user']['first_name'] . ' ' . $data['user']['last_name']
        ));

        return $response;
    }

    public function manipulate($name, $entity_type = ENTITY_TYPE_GROUP, $grp_ledg_id = "", $parent_group = 0, $behaviour = "", $ledg_id = '', $opening_balance = 0, $update_led_id = '', $ledger_start_date = '', $ledger_type = '', $context = '', $txn_id = 0, $soc_id = 0, $is_reco = 0, $user_id = 0)
    {
        $dup = $this->_checkGroupNameDuplication($name, $ledg_id, $update_led_id, $soc_id);

        $arrFYDetail = $this->getCurrentFinancialYear(['soc_id' => $soc_id]);
        $arrFYDetail = json_decode(json_encode($arrFYDetail), true);
        $ledger_start_date = $arrFYDetail['fy_start_date'];

        if ($dup == 0) {
            $behaviour = trim(strtolower($behaviour));
            $grpLedgTree = $this->tenantDB()->table('chsone_grp_ledger_tree')->where('ledger_account_id', $ledg_id)->first();

            if (empty($grpLedgTree)) {
                $grpLedgTree = [
                    'ledger_account_id' => $ledg_id,
                    'entity_type' => $entity_type,
                    'soc_id' => $soc_id,
                    'ledger_account_name' => $name,
                    'ledger_start_date' => $ledger_start_date,
                    'context_ref_id' => 0,
                    'operating_type' => !empty($ledger_type) && $ledger_type != "NULL" ? $ledger_type : '',
                    'parent_id' => !empty($parent_group) ? $parent_group : HEAD_GROUP_VAL,
                    'behaviour' => !empty($behaviour) ? $behaviour : ($ledger_props["behaviour"] ?? ''),
                    'nature_of_account' => !empty($behaviour) ? config("nature_account.$behaviour") : ($ledger_props["nature_account"] ?? ''),
                    'report_head' => !empty($behaviour) ? config("report_head.$behaviour") : ($ledger_props["report_head"] ?? ''),
                    'context' => !empty($context) ? $context : ($ledger_props["context"] ?? ''),
                    'defined_by' => USER,
                    'status' => ACTIVE,
                    'added_on' => now(),
                    'created_by' => $user_id ?? 0,
                ];
                $grpLedgTreeId = $this->tenantDB()->table('chsone_grp_ledger_tree')->insertGetId($grpLedgTree);
                $grpLedgTree['ledger_account_id'] = $grpLedgTreeId;
            } else {
                // Update existing record with new data
                $this->tenantDB()->table('chsone_grp_ledger_tree')->where('ledger_account_id', $ledg_id)->update([
                    'entity_type' => $entity_type,
                    'soc_id' => $soc_id,
                    'ledger_account_name' => $name,
                    'ledger_start_date' => $ledger_start_date,
                    'context_ref_id' => 0,
                    'operating_type' => !empty($ledger_type) && $ledger_type != "NULL" ? $ledger_type : '',
                    'parent_id' => !empty($parent_group) ? $parent_group : HEAD_GROUP_VAL,
                    'behaviour' => !empty($behaviour) ? $behaviour : ($ledger_props["behaviour"] ?? ''),
                    'nature_of_account' => !empty($behaviour) ? config("nature_account.$behaviour") : ($ledger_props["nature_account"] ?? ''),
                    'report_head' => !empty($behaviour) ? config("report_head.$behaviour") : ($ledger_props["report_head"] ?? ''),
                    'context' => !empty($context) ? $context : ($ledger_props["context"] ?? ''),
                    'defined_by' => USER,
                    'status' => ACTIVE,
                    'added_on' => now(),
                    'created_by' => $user_id ?? 0,
                ]);
            }

            if (!in_array(strtolower($behaviour), [INCOME, EXPENSE])) {
                $txn_date = $ledger_start_date;
                $narration = 'entry for opening balance';

                if ($txn_id = $this->addTxn(
                    $ledg_id,
                    $opening_balance,
                    $narration,
                    $txn_date,
                    "",
                    $grpLedgTree['nature_of_account'],
                    "",
                    "",
                    "",
                    "",
                    $name,
                    $is_opning = 1,
                    $is_reco

                )) {
                    // Transaction added
                } else {
                    // Handle transaction rollback if needed
                }
            }

            return $ledg_id;
        } else {
            return "DUP" . $dup;
        }
    }

    public function payNonmemberBillLedger($data = array())
    {
        $config = Config::get("constants");

        $success = 1;
        if (ACCOUNT_MODULE_EXIST == 1) {
            $PostData = $data['postData'];

            if (empty($PostData['bill_number'])) {
                $PostData['bill_number'] = $PostData['member_paid_invoice'] ?? '';
            }
            if (isset($PostData['clearance_date']) && !empty($PostData['clearance_date'])) {
                $PostData['payment_date'] = $PostData['clearance_date'];
            }
            $strNarration = '';

            if (empty($data['arrBookerLedgerDetails'])) {
                $arrListnerData['soc_id'] = $data['soc_id'];
                $arrListnerData['ledger_name'] = $PostData['booker_name'];
                $arrBookerLedgerDetails = $this->checkledgerExist($arrListnerData);
            } else {
                $arrBookerLedgerDetails = $data['arrBookerLedgerDetails'];
            }

            //$arrIncomeAccounts = $data['arrIncomeAccounts'];

            //Getting bank/cash ledger details
            $arrAccountDetail = $this->getBankCashAccountDetail(array('soc_id' => $data['soc_id'])); //get all Unit details

            $arrLedgerAccountDetail = $this->getLedgerAccountDetail(array('account_detail' => $arrAccountDetail)); //get all Unit details

            if (!empty($arrLedgerAccountDetail)) {

                $arrIncomeAccounts['ledger_id'] = $arrLedgerAccountDetail['cash']['ledger_id'];
                $arrIncomeAccounts['ledger_name'] = $arrLedgerAccountDetail['cash']['ledger_name'];
                if (strtolower($PostData['payment_mode']) != 'cash') {
                    if (!empty($PostData['bank_account'])) {
                        $arrIncomeAccounts['ledger_id'] = $PostData['bank_account'];
                        $arrIncomeAccounts['ledger_name'] = $arrLedgerAccountDetail['arrBank'][$PostData['bank_account']];
                    } else {
                        $arrIncomeAccounts['ledger_id'] = $arrLedgerAccountDetail['bank']['ledger_id'];
                        $arrIncomeAccounts['ledger_name'] = $arrLedgerAccountDetail['bank']['ledger_name'];
                    }

                    $strNarration = ' with transaction ref. (';

                    if (!empty($PostData['transaction_reference'])) {
                        $strNarration .= $PostData['transaction_reference'];
                    }

                    if (!empty($PostData['payment_instrument'])) {
                        // Add a separator if there's already a transaction reference
                        if (!empty($PostData['transaction_reference'])) {
                            $strNarration .= ', ';
                        }
                        $strNarration .= $PostData['payment_instrument'];
                    }

                    $strNarration .= ')';

                    // If there's nothing in the parentheses, remove the placeholder text
                    if ($strNarration == ' with transaction ref. ()') {
                        $strNarration = '';
                    }
                    if (!empty($PostData['payment_mode']) && strtolower($PostData['payment_mode']) == 'cashtransfer') {
                        $strNarration = ' with payment ref. (' . (!empty($PostData['transaction_reference']) ? $PostData['transaction_reference'] : null) . ')';
                    }
                }
            }

            $arrListnerData = array();
            $arrListnerData['soc_id'] = $data['soc_id'];
            $arrListnerData['voucher_type'] = isset($PostData['voucher_type_credit']) ? VOUCHER_CREDIT : VOUCHER_RECEIPT ?? '';
            $arrListnerData['voucher_reference_number'] = $PostData['receipt_number'] ?? '';
            $arrListnerData['voucher_reference_id'] = $PostData['payment_tracker_id'] ?? '';
            $arrListnerData['from_ledger_id'] = $arrIncomeAccounts['ledger_id'];
            $arrListnerData['to_ledger_id'] = $arrBookerLedgerDetails['recieving_ledger_id'];

            $arrListnerData['transaction_date'] = !empty($PostData['payment_date']) ? $this->getDatabaseDate($PostData['payment_date']) : $this->getCurrentDate('database');
            $arrListnerData['transaction_amount'] = $PostData['payment_amount'];

            if ($PostData['payment_mode'] == 'cashtransfer') {
                $payment_mode_display = DISPLAY_CASH_TRANSFER ?? '';
            } else {
                $payment_mode_display = ucfirst($PostData['payment_mode']);
            }

            $arrListnerData['narration'] = '(Receipt No-' . (!empty($PostData['receipt_number']) ? $PostData['receipt_number'] : '') . ') Amount received against Invoice ' . $PostData['bill_number'] . ' dated ' . $this->getCurrentDate('display') . ' through ' . $payment_mode_display . $strNarration; //$key . ' bill pay';//$PostData['narration'];
            $arrListnerData['from_ledger_name'] = $arrIncomeAccounts['ledger_name'];
            $arrListnerData['to_ledger_name'] = $arrBookerLedgerDetails['receiver_name'];
            $arrListnerData['payment_reference'] = $PostData['transaction_reference'] ?? '';
            $arrListnerData['transaction_type'] = $PostData['transaction_type'];
            $arrListnerData['mode_of_payment'] = $PostData['payment_mode'];
            $arrListnerData['other_payment_ref'] = $PostData['other_payment_ref'] ?? '';

            if (!empty($PostData['payment_note'])) {
                $arrListnerData['narration'] .= ' [' . $PostData['payment_note'] . ']';
            }
            //Code to replace from ledger id From Bank/Cash
            if (isset($data['from_ledger'])) {
                $arrListnerData['from_ledger_id'] = $data['from_ledger']['recieving_ledger_id'];
                $arrListnerData['from_ledger_name'] = $data['from_ledger']['receiver_name'];
            }
            if (isset($data['to_ledger'])) {
                $arrListnerData['to_ledger_id'] = $data['to_ledger']['recieving_ledger_id'];
                $arrListnerData['to_ledger_name'] = $data['to_ledger']['receiver_name'];
            }

            if (isset($data['narration'])) {
                $arrListnerData['narration'] = '(Receipt No-' . (!empty($PostData['receipt_number']) ? $PostData['receipt_number'] : '') . ') ' . $data['narration'];
            }

            // echo "arrListnerData"; echo "<pre>"; print_r($arrListnerData); echo "</pre>"; exit;

            if (!empty($PostData['tds_amount']) && $PostData['tds_amount'] > 0) {
                $arrListnerData['transaction_amount'] = round($PostData['payment_amount'] - $PostData['tds_amount'], 2);
                //print_r($arrListnerData);
                $arrLedgerEntry = $this->transactionLedgerEntry($arrListnerData);
                if (isset($arrLedgerEntry['error']) && !empty($arrLedgerEntry['error'])) {
                    $success = 0;
                } else {
                    $arrListnerData['transaction_amount'] = $PostData['tds_amount'];
                    $arrLedgerData['soc_id'] = $data['soc_id'];
                    $arrLedgerData['ledger_name'] = 'tds receivable';
                    $arrLedgerData['context'] = ''; //CURRENT_ASSETS_GROUP;
                    $arrLedgerData['behaviour'] = ASSET;

                    $arrTdsLedger = $this->checkledgerExistNew($arrLedgerData);
                    $arrListnerData['from_ledger_id'] = $arrTdsLedger['recieving_ledger_id'];
                    $arrListnerData['from_ledger_name'] = $arrTdsLedger['receiver_name'];

                    if ($PostData['payment_mode'] == 'cashtransfer') {
                        $payment_mode_display = DISPLAY_CASH_TRANSFER;
                    } else {
                        $payment_mode_display = ucfirst($PostData['payment_mode']);
                    }

                    $arrListnerData['narration'] = 'TDS amount deducted against Invoice ' . $PostData['bill_number'] . ' dated ' . $this->getCurrentDate('display') . ' through ' . $payment_mode_display . $strNarration; //'Invoice '.$arrPostData['member_paid_invoice'].' '.$eachLedgerDetail['particular'].' Write Off';

                    //print_r($arrListnerData);
                    $arrLedgerEntry = $this->transactionLedgerEntry($arrListnerData);
                    if (isset($arrLedgerEntry['error']) && !empty($arrLedgerEntry['error'])) {
                        $success = 0;
                    }
                }
            } else {
                $arrLedgerEntry = $this->transactionLedgerEntry($arrListnerData);
                if (isset($arrLedgerEntry['error']) && !empty($arrLedgerEntry['error'])) {
                    $success = 0;
                }
            }
        }
        //exit;
        return $success;
    }

    public function getMemberDetail($data = [])
    {
        $arrMemberMaster = [];

        // Use parameterized queries to avoid SQL injection
        $memberType = $this->tenantDB()->table('chsone_member_type_master')
            ->where('soc_id', $data['soc_id'])
            ->where('member_type_name', 'Primary')
            ->first();

        if (!$memberType) {
            // Log or handle the case where no member type is found
            // \Log::error('Member type "Primary" not found for soc_id: ' . $data['soc_id']);
            // return $arrMemberMaster;
            $this->message = 'Member type "Primary" not found for soc_id: ' . $data['soc_id'];
            $this->status = 400;
        }

        // Fetch the member master details using the member type ID and unit ID
        $objMemberMaster = $this->tenantDB()->table('chsone_members_master')
            ->where('soc_id', $data['soc_id'])
            ->where('fk_unit_id', $data['unit_id'])
            ->where('status', 1)
            ->where('member_type_id', $memberType->member_type_id)
            ->first();
        $objMemberMaster = json_decode(json_encode($objMemberMaster), true);

        return $objMemberMaster;
    }

    public function checkledgerExist($data = [])
    {
        $arrClientLedgerDetails = [];
        $soc_id = $data['soc_id'];
        $ledgerName = $data['ledger_name'];

        // Check if the ledger already exists for the outsider based on provided criteria
        $existingLedger = $this->tenantDB()->table('chsone_grp_ledger_tree')
            ->where('soc_id', $data['soc_id'])
            ->where('context', 'sundrydebtors')
            ->where('entity_type', ENTITY_TYPE_LEDGER)
            ->where('ledger_account_name', $ledgerName)
            ->first();

        if (!empty($existingLedger)) {
            // Ledger exists, so set the receiving ledger details
            $arrClientLedgerDetails['recieving_ledger_id'] = $existingLedger->ledger_account_id;
            $arrClientLedgerDetails['receiver_name'] = $existingLedger->ledger_account_name;
        } else {
            // Ledger does not exist, create a new ledger under the Sundry Debtors group
            $sundryDebtorGroup = $this->tenantDB()->table('chsone_grp_ledger_tree')
                ->where('soc_id', $data['soc_id'])
                ->where('entity_type', ENTITY_TYPE_GROUP)
                ->where('ledger_account_name', SUNDRY_DR)
                ->first();

            if ($sundryDebtorGroup) {
                // Create a new ledger entry under the Sundry Debtors group
                $ledgerAccountId = $this->manipulate(
                    $ledgerName,
                    ENTITY_TYPE_LEDGER,
                    "",
                    $sundryDebtorGroup->ledger_account_id,
                    $sundryDebtorGroup->behaviour,
                    '',
                    0,
                    '',
                    '',
                    null,
                    $sundryDebtorGroup->context,
                    0,
                    $data['soc_id'],
                    0,
                    $data['user_id'] ?? 0

                );

                // Check if the new ledger was created successfully
                if (is_string($ledgerAccountId) && strpos($ledgerAccountId, 'DUP') === false) {
                    $arrClientLedgerDetails['recieving_ledger_id'] = $ledgerAccountId;
                    $arrClientLedgerDetails['receiver_name'] = $ledgerName;
                }
            }
        }

        return $arrClientLedgerDetails;
    }

    public function getUnitDetailById(array $data = [])
    {
        $soc_id = $data['soc_id'];
        $unit_id = $data['unit_id'];

        $arrUnitDetails = $this->tenantDB()->table('chsone_units_master')->where('soc_id', $soc_id)
            ->where('unit_id', $unit_id)
            ->first();

        return !empty($arrUnitDetails) ? $arrUnitDetails : [];
    }

    public function paymentLedgerEntry($data = array())
    {
        $config = Config::get("constants");

        $countLedgerEntry = 0;

        if (ACCOUNT_MODULE_EXIST == 1) {

            $soc_id = $data['soc_id'];
            $arrPostData = $data['arrPostData'];
            $arrUnitLedgerDetails = $data['arrUnitLedgerDetails'];
            $arrUnitLedgerDetails = json_decode(json_encode($arrUnitLedgerDetails), true);

            if (isset($arrPostData['clearance_date']) && !empty($arrPostData['clearance_date'])) {
                $arrPostData['payment_date'] = $arrPostData['clearance_date'];
            }

            if (empty($arrPostData['payment_date'])) {
                $arrPostData['payment_date'] = $this->getCurrentDate('display');
            }

            //$arrIncomeAccounts = $this->incometrackerevent->incometracker('accountListener:checkledgerExistNew', array('auth' => $data['auth'], 'ledger_id' => $arrPostData['bank_account']));
            // print_r($this->constants['payment_mode_for_receipt_reversal']); exit();
            if (!empty($arrPostData['payment_mode']) && !in_array(strtolower($arrPostData['payment_mode']), PAYMENT_MODE_FOR_RECEIPT_REVERSAL)) {
                //  print_r(array(trim($arrPostData['payment_instrument']) . '_PG_LEDGER_ID'));
                $arrInvoiceGeneralSetting = $this->getInvoiceGeneralSetting(array('soc_id' => $data['soc_id'], 'setting_key' => array(trim((!empty($arrPostData['payment_instrument']) ? $arrPostData['payment_instrument'] : '')) . '_PG_LEDGER_ID')));

                if (!empty($arrInvoiceGeneralSetting->setting_value)) {
                    $arrIncomeAccounts = $this->checkledgerExistNew(array('soc_id' => $data['soc_id'], 'ledger_id' => $arrInvoiceGeneralSetting->setting_value ?? ''));
                    $arrIncomeAccounts['ledger_id'] = $arrIncomeAccounts['recieving_ledger_id'];
                    $arrIncomeAccounts['ledger_name'] = $arrIncomeAccounts['receiver_name'];
                } else {

                    if (!empty($data['to_ledger'])) {
                        $arrIncomeAccounts['ledger_id'] = $data['to_ledger']['recieving_ledger_id'];
                        $arrIncomeAccounts['ledger_name'] = $data['to_ledger']['receiver_name'];
                    } else {
                        return 1;
                    }
                }
            } else {

                if (isset($data['arrUnitLedgerDetails']['suspenseLedgerDetails']) && !empty($data['arrUnitLedgerDetails']['suspenseLedgerDetails'])) {
                    $arrIncomeAccounts['ledger_id'] = $data['arrUnitLedgerDetails']['suspenseLedgerDetails']['recieving_ledger_id'];
                    $arrIncomeAccounts['ledger_name'] = $data['arrUnitLedgerDetails']['suspenseLedgerDetails']['receiver_name'];
                } else {

                    // Getting bank/cash ledger details
                    $arrAccountDetail = $this->getBankCashAccountDetail(array('soc_id' => $data['soc_id'])); // get all Unit details

                    $arrLedgerAccountDetail = $this->getLedgerAccountDetail(array(
                        'account_detail' => $arrAccountDetail,
                    )); // get all Unit details

                    if (!empty($arrLedgerAccountDetail)) {
                        $arrIncomeAccounts['ledger_id'] = $arrLedgerAccountDetail['cash']['ledger_id'];
                        $arrIncomeAccounts['ledger_name'] = $arrLedgerAccountDetail['cash']['ledger_name'];
                        if (strtolower($arrPostData['payment_mode']) != 'cash') {
                            if (!empty($arrPostData['bank_account'])) {
                                $arrIncomeAccounts['ledger_id'] = $arrPostData['bank_account'];
                                $arrIncomeAccounts['ledger_name'] = $arrLedgerAccountDetail['arrBank'][$arrPostData['bank_account']];
                            } else {
                                $arrIncomeAccounts['ledger_id'] = $arrLedgerAccountDetail['bank']['ledger_id'];
                                $arrIncomeAccounts['ledger_name'] = $arrLedgerAccountDetail['bank']['ledger_name'];
                            }
                        }
                    }
                }
            }

            if ($arrPostData['payment_mode'] == 'cashtransfer') {
                $paymentModeForNarration = DISPLAY_CASH_TRANSFER ?? 'Electronic Fund Transfer';
            } else {
                $paymentModeForNarration = $arrPostData['payment_mode'] ?? null;
            }

            // $paymentModeForNarration = $arrPostData ['payment_mode'];
            $strNarration = '';
            if (!empty($arrPostData['payment_mode']) && in_array(strtolower($arrPostData['payment_mode']), payment_mode_for_clearance)) {
                $strNarration = ' with transaction ref. (' . (!empty($arrPostData['transaction_reference']) ? $arrPostData['transaction_reference'] : '') . ', ' . (!empty($arrPostData['payment_instrument']) ? $arrPostData['payment_instrument'] : '') . ')';
            } elseif (!empty($arrPostData['payment_mode']) && strtolower($arrPostData['payment_mode']) == 'cashtransfer') {
                $strNarration = ' with payment ref. (' . (!empty($arrPostData['transaction_reference']) ? $arrPostData['transaction_reference'] : '') . ')';
            } elseif (!empty($arrPostData['payment_mode']) && !in_array(strtolower($arrPostData['payment_mode']), payment_mode_for_receipt_reversal)) {
                $strNarration = ' with transaction ref. (' . (!empty($arrPostData['transaction_reference']) ? $arrPostData['transaction_reference'] : '') ?? '' . ')';

                switch ($arrPostData['payment_mode']) {
                    case 'YES_BANK_PG':
                        $paymentModeForNarration = DISPLAY_YES_BANK;
                        break;
                    case 'YES_BANK_ECOLLECT':
                        $paymentModeForNarration = DISPLAY_YES_BANK_ECOLLECT;
                        break;
                    case 'MOBIKWIK_WALLET':
                        $paymentModeForNarration = MOBIKWIK_WALLET;
                        break;
                    case 'MOBIKWIK_PG':
                        $paymentModeForNarration = DISPLAY_MOBIKWIK_PG;
                        break;
                    case 'CASHFREE_PG':
                        $paymentModeForNarration = DISPLAY_CASHFREE_PG;
                        break;
                    case 'PAYTM_PG':
                        $paymentModeForNarration = DISPLAY_PAYTM;
                        break;
                    case 'HDFC_PG':
                        $paymentModeForNarration = DISPLAY_HDFC_PG;
                        break;
                    case 'ATOM_PG':
                        $paymentModeForNarration = DISPLAY_ATOM_PG;
                        break;
                    case 'cashtransfer':
                        $paymentModeForNarration = DISPLAY_CASH_TRANSFER;
                        break;
                }
            }
            // echo $arrIncomeInvoicePaymentTracker['payment_mode'];exit;
            // Ledger entry for payment amount
            $arrLedgerTransactionData = array(
                'soc_id' => $soc_id,
            );

            $arrLedgerTransactionData['voucher_type'] = isset($arrPostData['voucher_type_credit']) ? $this->constants['VOUCHER_CREDIT'] : $this->constants['VOUCHER_RECEIPT'] ?? '';
            $arrLedgerTransactionData['voucher_reference_number'] = $arrPostData['receipt_number'] ?? '';
            $arrLedgerTransactionData['voucher_reference_id'] = $arrPostData['payment_tracker_id'] ?? '';
            $arrLedgerTransactionData['from_ledger_id'] = $arrIncomeAccounts['ledger_id'];
            $arrLedgerTransactionData['to_ledger_id'] = $arrUnitLedgerDetails['receiving_ledger_id'];
            $arrLedgerTransactionData['transaction_date'] = !empty($arrPostData['payment_date']) ? $this->getDatabaseDate($arrPostData['payment_date']) : $this->getCurrentDate('database');
            $arrLedgerTransactionData['transaction_amount'] = $arrPostData['payment_amount'];

            // $arrLedgerTransactionData['narration'] = 'Amount received against Invoice ' . $arrPostData['member_paid_invoice']  ?? '' . ' dated ' . $this->getDisplayDate($arrPostData['payment_date']) . ' through ' . $paymentModeForNarration . $strNarration; // 'Invoice '.$arrPostData['member_paid_invoice'].' '.$eachLedgerDetail['particular'].' Write Off';
            $arrLedgerTransactionData['narration'] = 'Amount received against Invoice ' .
                (!empty($arrPostData['member_paid_invoice']) ? $arrPostData['member_paid_invoice'] : '') .
                ' dated ' . $this->getDisplayDate($arrPostData['payment_date']) .
                ' through ' . $paymentModeForNarration .
                $strNarration;

            if ((empty($arrPostData['total_unpaid_amount']) || $arrPostData['total_unpaid_amount'] <= 0) && empty($arrPostData['member_paid_invoice'])) {
                // $arrLedgerTransactionData['narration'] = 'Advance payment received from ' . ucwords($arrPostData['received_from']) . ' dated ' . $this->getDisplayDate($arrPostData['payment_date']) . ' through ' . $paymentModeForNarration . $strNarration; // $eachInvoiceDetail['invoice_number'] . ' late payment charges';
                $arrLedgerTransactionData['narration'] = 'Amount received against Invoice ' .
                    ($arrPostData['member_paid_invoice'] ?? '') .
                    ' dated ' . $this->getDisplayDate($arrPostData['payment_date']) .
                    ' through ' . $paymentModeForNarration .
                    $strNarration;
            }

            if (!empty($arrPostData['bill_type']) && $arrPostData['bill_type'] == 'suspense') {
                $arrLedgerTransactionData['narration'] = 'Unidenfied payment from ' . ucwords($arrPostData['received_from']) . ' dated ' . $this->getDisplayDate($arrPostData['payment_date']) . ' through ' . $paymentModeForNarration . $strNarration;
            }

            $arrLedgerTransactionData['from_ledger_name'] = $arrIncomeAccounts['ledger_name'];
            $arrLedgerTransactionData['to_ledger_name'] = $arrUnitLedgerDetails['receiver_name'];
            $arrLedgerTransactionData['payment_reference'] = !empty($arrPostData['transaction_reference'])
                ? $arrPostData['transaction_reference']
                : null;

            // $arrLedgerTransact ionData['payment_reference']=0;
            $arrLedgerTransactionData['transaction_type'] = '';
            $arrLedgerTransactionData['mode_of_payment'] = $arrPostData['payment_mode'] ?? null;
            $arrLedgerTransactionData['other_payment_ref'] = '';

            // Code to replace from ledger id From Bank/Cash

            if (!empty($data['from_ledger'])) {

                $arrLedgerTransactionData['from_ledger_id'] = $data['from_ledger']['recieving_ledger_id'];
                $arrLedgerTransactionData['from_ledger_name'] = $data['from_ledger']['receiver_name'];
            }
            if (!empty($data['to_ledger']) && !empty($data['to_ledger']['recieving_ledger_id']) && !empty($data['to_ledger']['receiver_name'])) {
                $arrLedgerTransactionData['to_ledger_id'] = $data['to_ledger']['recieving_ledger_id'];
                $arrLedgerTransactionData['to_ledger_name'] = $data['to_ledger']['receiver_name'];
            }

            if (isset($data['narration'])) {
                $arrLedgerTransactionData['narration'] = $data['narration'];
            }
            $arrLedgerTransactionData['narration'] = '(Receipt No-' . (!empty($arrPostData['receipt_number']) ? $arrPostData['receipt_number'] : '') . ') ' . $arrLedgerTransactionData['narration'];

            if (!empty($arrPostData['payment_note'])) {
                $arrLedgerTransactionData['narration'] .= ' [' . $arrPostData['payment_note'] . ']';
            }

            //echo 'pay'; print_r($arrLedgerTransactionData); exit();
            if (!empty($arrPostData['tds_amount']) && $arrPostData['tds_amount'] > 0) {

                $arrLedgerTransactionData['transaction_amount'] = round($arrPostData['payment_amount'] - $arrPostData['tds_amount'], 2);
                $arrLedgerEntry = $this->transactionLedgerEntry($arrLedgerTransactionData);
                if (isset($arrLedgerEntry['error']) && !empty($arrLedgerEntry['error'])) {
                    $countLedgerEntry++;
                } else {
                    $arrLedgerTransactionData['transaction_amount'] = $arrPostData['tds_amount'];
                    $arrListnerData['soc_id'] = $soc_id;
                    $arrListnerData['ledger_name'] = 'tds receivable';
                    $arrListnerData['context'] = ''; // CURRENT_ASSETS_GROUP;
                    $arrListnerData['behaviour'] = ASSET;

                    $arrTdsLedger = $this->checkledgerExistNew($arrListnerData);
                    $arrLedgerTransactionData['from_ledger_id'] = $arrTdsLedger['recieving_ledger_id'];
                    $arrLedgerTransactionData['from_ledger_name'] = $arrTdsLedger['receiver_name'];
                    $arrLedgerTransactionData['narration'] = 'TDS amount deducted against Invoice ' . $arrPostData['member_paid_invoice'] . ' dated ' . $this->getDisplayDate($arrPostData['payment_date']) . ' through ' . $paymentModeForNarration . $strNarration; // 'Invoice '.$arrPostData['member_paid_invoice'].' '.$eachLedgerDetail['particular'].' Write Off';

                    $arrLedgerEntry = $this->transactionLedgerEntry($arrLedgerTransactionData);
                    if (isset($arrLedgerEntry['error']) && !empty($arrLedgerEntry['error'])) {
                        $countLedgerEntry++;
                    }
                }
            } else {

                //echo 'led';
                $arrLedgerEntry = $this->transactionLedgerEntry($arrLedgerTransactionData);

                // print_r($arrLedgerEntry); exit();
                if (isset($arrLedgerEntry['error']) && !empty($arrLedgerEntry['error'])) {
                    $countLedgerEntry++;
                }
            }

            //Ledger entry for invoice writeoff
            if (!empty($arrPostData['writeoff_amount']) && $arrPostData['writeoff_amount'] > 0) {
                if ($arrPostData['writeoff_amount'] >= 1000) {
                    $arrExpenseWriteOff = $this->checkledgerExistNew(array('soc_id' => $data['soc_id'], 'ledger_name' => 'Expense Bad Debt', 'context' => EXPENSE, 'behaviour' => EXPENSE));
                    if (empty($arrExpenseWriteOff['recieving_ledger_id'])) {
                        $arrParentExpense = $this->getLedgerDetail(array('soc_id' => $data['soc_id'], 'ledger_name' => 'Indirect Expense', 'context' => EXPENSE, 'behaviour' => EXPENSE));
                        if (!empty($arrParentExpense['ledger_account_id'])) {
                            $arrExpenseWriteOff['receiver_name'] = 'Expense Bad Debt';
                            $arrExpenseWriteOff['recieving_ledger_id'] = $this->createNewLedgerExit(array('soc_id' => $data['soc_id'], 'ledger_name' => $arrExpenseWriteOff['receiver_name'], 'context' => EXPENSE, 'behaviour' => EXPENSE, 'parent_id' => $arrParentExpense['ledger_account_id']));
                        }
                    }
                } else {
                    $arrExpenseWriteOff = $this->checkledgerExistNew(array('soc_id' => $data['soc_id'], 'ledger_name' => 'Expense Write Off', 'context' => EXPENSE, 'behaviour' => EXPENSE));
                }
                if (empty($arrExpenseWriteOff['recieving_ledger_id'])) {
                    return 1;
                }
                $arrLedgerTransactionData['transaction_amount'] = $arrPostData['writeoff_amount'];
                $arrLedgerTransactionData['from_ledger_id'] = $arrExpenseWriteOff['recieving_ledger_id'];
                $arrLedgerTransactionData['from_ledger_name'] = $arrExpenseWriteOff['receiver_name'];

                $arrLedgerTransactionData['narration'] = 'Amount writeoff against Invoice ' . $arrPostData['member_paid_invoice'] . ' dated ' . $this->getDisplayDate($arrPostData['payment_date']); //'Invoice '.$arrPostData['member_paid_invoice'].' '.$eachLedgerDetail['particular'].' Write Off';
                $arrLedgerEntry = $this->transactionLedgerEntry($arrLedgerTransactionData);
                if (isset($arrLedgerEntry['error']) && !empty($arrLedgerEntry['error'])) {
                    $countLedgerEntry++;
                }
            }
        }

        return $countLedgerEntry;
    }

    public function transactionLedgerEntry($data = [])
    {
        // Initialize response array
        $arrResponse = [];

        // Validate required fields
        if (empty(trim($data['from_ledger_id'])) || empty(trim($data['to_ledger_id']))) {
            return ['error' => true, 'message' => 'From or To ledger ID is missing'];
        }
        // Set default transaction date if not provided
        $data['transaction_date'] = $data['transaction_date'] ?? date('Y-m-d H:i:s');

        // Execute the transaction entry and capture the transaction ID
        $transaction_id = $this->executeTransactionEntry($data);

        // Check if the transaction was successful
        if ($transaction_id) {
            $arrResponse = ['success' => true, 'transaction_id' => $transaction_id];
        } else {
            $arrResponse = ['error' => true, 'message' => 'Transaction failed'];
        }

        return $arrResponse;
    }

    public function executeTransactionEntry($data)
    {
        $vouchers_array = [];

        //         // Check if voucher type contains an underscore and split if found
        if (isset($data['voucher_type']) && strpos($data['voucher_type'], '_') !== false) {
            $vouchers_array = explode('_', $data['voucher_type']);
            $data['voucher_type'] = $vouchers_array[0]; // Set the primary voucher type
        }
        // Attempt the first transaction entry
        $data['transaction_from_id'] = $this->_addTransactionEntry($data);

        // If the first transaction entry is successful
        if (!empty($data['transaction_from_id'])) {
            // Debug to confirm the first transaction ID

            // Check if a secondary voucher type exists and needs processing
            if (count($vouchers_array) >= 2) {
                $data['voucher_type'] = $vouchers_array[1]; // Set the secondary voucher type
            }

            // Attempt the second transaction entry
            $secondEntrySuccess = $this->_addTransactionEntry($data);
            // Check if the second entry was successful
            if ($secondEntrySuccess) {
                // Optional: commit transaction if using DB transactions
                // $this->soc_db_w->commit();
                return $data['transaction_from_id'];
            } else {
                // Optional: rollback transaction if the second entry fails
                // $this->soc_db_w->rollback();
                // dd('Failed to insert second transaction entry');
                // Log::error('Failed to insert second transaction entry', ['data' => $data]);
                return false;
            }
        } else {
            // Optional: rollback transaction if the first entry fails
            // $this->soc_db_w->rollback();
            // dd('Failed to insert first transaction entry');
            // Log::error('Failed to insert first transaction entry', ['data' => $data]);
            return false;
        }
    }

    public function _addTransactionEntry($data)
    {
        // Set transaction type and ledger details based on the mode
        if (!empty($data['transaction_from_id'])) {
            $data['transaction_type'] = "cr";
            $ledger_id = $data['to_ledger_id'];
            $ledger_name = $data['to_ledger_name'];
        } else {
            $data['transaction_type'] = "dr";
            $ledger_id = $data['from_ledger_id'];
            $ledger_name = $data['from_ledger_name'];
        }

        // Ensure data is an array
        $data = json_decode(json_encode($data), true);
        $soc_id = $data['soc_id'];

        // Check for opening balance transaction
        if (!empty($data['is_opening']) && $data['is_opening'] == 1) {
            $conditions = [
                ['soc_id', '=', $soc_id],
                ['is_opening_balance', '=', 1],
                ['ledger_account_id', '=', $ledger_id],
            ];

            $txn_entry = $this->tenantDB()->table('chsone_ledger_transactions')
                ->where($conditions)
                ->first();

            $data['txn_id'] = $txn_entry->txn_id ?? '';
        }

        // Prepare the data for insertion
        $transactionData = [
            'soc_id' => (string) $soc_id,
            'voucher_type' => (string) $data['voucher_type'],
            'voucher_reference_number' => (int) ($data['voucher_reference_number'] ?? 0),
            'voucher_reference_id' => (int) ($data['voucher_reference_id'] ?? 0),
            'transaction_date' => (string) $data['transaction_date'],
            'ledger_account_id' => (int) $ledger_id,
            'ledger_account_name' => (string) $ledger_name,
            'transaction_type' => (string) $data['transaction_type'],
            'payment_mode' => isset($data['payment_mode']) ? (string) $data['payment_mode'] : null,
            'transaction_amount' => (float) ($data['transaction_amount'] ?? 0),
            'other_reference_id' => (string) ($data['other_payment_ref'] ?? ''),
            'txn_from_id' => (string) ($data['transaction_from_id'] ?? ''),
            'memo_desc' => (string) $data['narration'],
            'is_opening_balance' => (int) ($data['is_opening'] ?? 0),
            'is_reconciled' => (int) ($data['is_reconciled'] ?? 0),
            'created_by' => (int) ($data['user_id'] ?? 0),
            'added_on' => date('Y-m-d H:i:s'),
            'is_cancelled' => !empty($data['is_cancelled']) ? 1 : 0,
        ];

        if (!empty($data['payment_reference'])) {
            $transactionData['payment_reference'] = $data['payment_reference'];
        }

        // Log the transaction data to ensure it's correct before insertion
        // Log::info('Attempting transaction entry', ['transactionData' => $transactionData]);

        // Attempt to insert the transaction data
        $txn = $this->tenantDB()->table('chsone_ledger_transactions')->insertGetId($transactionData);

        // Return the transaction ID if successful, otherwise false
        return $txn ? $txn : false;
    }

    public function saveCreditAccountResponse($data = array())
    {
        $user = $data['user'];
        $id = $data['id'];
        id:

        $data = $data['data'];
        $data['user'] = $user;

        if ($id) {
            // Prepare the data to be updated
            $updateData = [
                'updated_by' => $user,
                'updated_date' => date('Y-m-d H:i:s'),
                'use_credit_after' => !empty($data['adjustable_date']) ? $data['adjustable_date'] : null,
                'is_locked' => 0,
                'use_credit_for' => !empty($data['used_for']) ? $data['used_for'] : null,
                'is_invoice_rectification' => isset($data['is_invoice_rectification']) && !empty($data['is_invoice_rectification']) ? $data['is_invoice_rectification'] : null,
                'income_account_id' => isset($data['income_account_ledger_id']) && !empty($data['income_account_ledger_id']) ? $data['income_account_ledger_id'] : null,
                'narration' => $data['narration'],
                'use_credit' => isset($data['credit_used_type']) && !empty($data['credit_used_type']) ? $data['credit_used_type'] : 'adjustable',
            ];

            // Update the record in the database
            $result = $this->tenantDB()
                ->table('chsone_credit_accounts')
                ->where('credit_account_id', $id)
                ->where('soc_id', $data['soc_id'])
                ->update($updateData);

            // Save data
            if ($result) {
                return $result;
            } else {
                return false;
            }
        } else {
            if ($data['credit_used_type'] == 'both') {
                $data['credit_used_type'] = 'adjustable';
                $data['payment_amount'] = $data['adjustable_amount'];
                $saveResponse = $this->saveCreditNote($data);
                if ($saveResponse['error'] == false) {
                    $data['credit_used_type'] = 'refundable';
                    $data['payment_amount'] = $data['refundable_amount'];
                    $data['used_for'] = null;
                    $data['adjustable_date'] = null;

                    $saveResponse = $this->saveCreditNote($data);
                    if ($saveResponse['error'] == false) {
                        return $saveResponse;
                    }
                }
            } else {
                $saveResponse = $this->saveCreditNote($data);
                if ($saveResponse['error'] == false) {
                    return $saveResponse;
                }
            }
        }
    }

    public function saveCreditNote($data)
    {
        // Initialize the response array
        $response = [
            'error' => true,
            'message' => '',
        ];

        try {
            // Create an array to hold the credit account data
            $creditAccount = [];

            // Set the properties from the request data
            $creditAccount['created_by'] = $data['user_id'] ?? 0; // Use authenticated user or passed user
            $creditAccount['created_date'] = date('Y-m-d H:i:s'); // Use current date-time
            $creditAccount['soc_id'] = $data['soc_id'] ?? null; // Ensure soc_id is present

            $creditAccount['invoice_number'] = $data['invoice_no'] ?? $data['invoice_number'] ?? null; // Use invoice_no if present, otherwise invoice_number
            $creditAccount['is_invoice_rectification'] = $data['is_invoice_rectification'] ?? null; // Default to null if not set
            $creditAccount['income_account_id'] = $data['income_account_ledger_id'] ?? null; // Default to null if not set
            $creditAccount['payment_tracker_id'] = $data['payment_tracker_id'] ?? null; // Default to null if not set
            $creditAccount['account_id'] = $data['account_id'] ?? null; // Ensure account_id is present
            $creditAccount['account_name'] = $data['account_name'] ?? null; // Ensure account_name is present
            $creditAccount['account_context'] = $data['account_context'] ?? null; // Default to null if not set
            $creditAccount['amount'] = $data['payment_amount'] ?? 0; // Default to 0 if not set
            $creditAccount['payment_mode'] = $data['payment_mode'] ?? null; // Default to null if not set
            $creditAccount['payment_date'] = !empty($data['payment_date']) ? $this->getDatabaseDate($data['payment_date']) : null; // Use a method to format the date
            $creditAccount['transaction_type'] = $data['transaction_type'] ?? null; // Ensure transaction_type is present
            $creditAccount['narration'] = $data['narration'] ?? null; // Default to null if not set
            $creditAccount['use_credit'] = $data['credit_used_type'] ?? 'adjustable'; // Default to 'adjustable' if not set
            $creditAccount['use_credit_after'] = $data['adjustable_date'] ?? null; // Default to null if not set
            $creditAccount['is_locked'] = $data['is_locked'] ?? 0; // Default to 0 if not set
            $creditAccount['use_credit_for'] = $data['used_for'] ?? null; // Default to null if not set
            $creditAccount['reference_no'] = $data['transaction_reference'] ?? null; // Default to null if not set
            $creditAccount['context'] = $data['context'] ?? 'system'; // Default to 'system' if not set

            // Insert the data and get the inserted ID using insertGetId()
            $creditAccountId = $this->tenantDB()->table('chsone_credit_accounts')->insertGetId($creditAccount);

            // Save the CreditAccount and return a successful response
            if ($creditAccountId) {
                $response['error'] = false;
                $response['id'] = $creditAccountId;
            }
        } catch (\Exception $e) {
            // Handle exceptions and return an error response
            $response['message'] = $e->getMessage();
        }

        return $response;
    }
    private function _checkGroupNameDuplication($name, $ledg_id = "", $update_led_id = "", $soc_id)
    {
        $query = $this->tenantDB()->table('chsone_grp_ledger_tree')->where('soc_id', $soc_id)
            ->whereRaw('LOWER(ledger_account_name) = ?', [strtolower(trim($name))]);

        if ($ledg_id) {
            $query->where('ledger_account_id', '!=', $ledg_id);
        }

        if ($update_led_id) {
            $query->where('ledger_account_id', '!=', $update_led_id);
        }

        $ledgers_count = $query->count();

        return $ledgers_count;
    }

    public function getInvoiceGeneralSetting(array $data = [])
    {

        $invoiceGeneralSetting = $this->tenantDB()->table('income_invoice_general_settings')
            ->where('soc_id', $data['soc_id'])
            ->whereIn('setting_key', $data['setting_key'])
            ->first();

        return $invoiceGeneralSetting !== null ? (array) $invoiceGeneralSetting : [];
    }

    public function _createVendorLegder($vendor_name, $isAdvance = false)
    {
        $vendor_dts  = $this->getVendorSingle($vendor_name);
        $name         = strtoupper($vendor_dts->vendor_name) . "-" . $vendor_name;
        $entity_type  = ENTITY_TYPE_LEDGER;
        $grp_ledg_id = "";
        $ledger_id = "";
        if ($isAdvance) {
            if ($vendor_dts->vendor_advance_ledger_id == 0) {
                $parent       = $this->getGroupId(VENDOR_ADVANCE_GROUP, true);
                $parent_group = $parent->ledger_account_id;
                $name = "ADV#" . $name;
                $ledger_id    = $this->manipulate($name, $entity_type, $grp_ledg_id, $parent_group, "", '', 0);
                $ledger_id    = str_replace("DUP", "", $ledger_id);

                if ($vendor_dts->vendor_advance_ledger_id == 0) {
                    // $vendor_dts->vendor_advance_ledger_id = $ledger_id;
                    // $vendor_dts->save();

                    // Update the vendor record in the database
                    DB::connection('tenant')->table('chsone_vendors_master')
                    ->where('vendor_id', $vendor_dts->vendor_id)
                    ->update(['vendor_advance_ledger_id' => $ledger_id]);
                }
            } else {
                $ledger_id    = $vendor_dts->vendor_advance_ledger_id;
            }
        } else {
            if ($vendor_dts->vendor_ledger_id == 0) {
                $parent       = $this->getGroupId(VENDOR_ADVANCE_GROUP, true);
                $parent_group = $parent->ledger_account_id;
                $ledger_id    = $this->manipulate($name, $entity_type, $grp_ledg_id, $parent_group, "", '', 0);
                $ledger_id    = str_replace("DUP", "", $ledger_id);

                if ($vendor_dts->vendor_ledger_id == 0) {
                    // $vendor_dts->vendor_ledger_id = $ledger_id;
                    // $vendor_dts->save();

                    // Update the vendor record in the database
                    DB::connection('tenant')->table('chsone_vendors_master')
                    ->where('vendor_id', $vendor_dts->vendor_id)
                    ->update(['vendor_ledger_id' => $ledger_id]);
                }
            } else {
                $ledger_id    = $vendor_dts->vendor_ledger_id;
            }
        }

        return $ledger_id;
    }

    public function getVendorSingle($vendor_id)
    {
        $vendorDts = DB::connection('tenant')->table('chsone_vendors_master')->where('vendor_id', $vendor_id)
            ->first();
        return $vendorDts;
    }


    public function executeVouchers(
        $voucher_type = "",
        $from_ledg_id = "",
        $to_ledg_id = "",
        $txn_date,
        $txn_amt,
        $narration,
        $from_ledger_name,
        $to_ledger_name,
        $recp_ref = "",
        $type = "",
        $mode_of_payment = "",
        $other_recp_ref = "",
        $soc_id = "",
        $created_by = "",
        $voucher_reference_id = "",
        $voucher_reference_number = "",
        $is_cancelled = 0
        ) {
        $vouchers_array = [];
        // Check if the voucher type contains an underscore and split it if needed
        if (strpos($voucher_type, '_') !== false) {
            $vouchers_array = explode('_', $voucher_type);
            $voucher_type = $vouchers_array[0];
        }

        // Start a database transaction
        $this->tenantDB()->beginTransaction();

        try {
            // Add the first transaction entry (debit)
            $txn_from_id = $this->_addTransaction(
                0,
                $from_ledg_id,
                $txn_amt,
                $narration,
                $txn_date,
                $voucher_type,
                $type,
                "",
                $mode_of_payment,
                $recp_ref,
                $from_ledger_name,
                0,
                $other_recp_ref,
                $soc_id,
                $created_by,
                $voucher_reference_id,
                $voucher_reference_number,
                $is_cancelled
            );

            // Check if the transaction was successful
            if ($txn_from_id) {
                // Handle the case where the voucher type has a secondary part
                if (count($vouchers_array) >= 2) {
                    $voucher_type = $vouchers_array[1];
                }

                // Add the second transaction entry (credit)
                $txn_to_id = $this->_addTransaction(
                    0,
                    $to_ledg_id,
                    $txn_amt,
                    $narration,
                    $txn_date,
                    $voucher_type,
                    $type,
                    $txn_from_id,
                    $mode_of_payment,
                    $recp_ref,
                    $to_ledger_name,
                    0,
                    $other_recp_ref,
                    $soc_id,
                    $created_by,
                    $voucher_reference_id,
                    $voucher_reference_number,
                    $is_cancelled
                );

                if ($txn_to_id) {
                    // Commit the transaction if both entries are successful
                    $this->tenantDB()->commit();
                    return $txn_from_id;
                } else {
                    // Rollback if the second transaction fails
                    $this->tenantDB()->rollBack();
                    return false;
                }
            } else {
                // Rollback if the first transaction fails
                $this->tenantDB()->rollBack();
                return false;
            }
        } catch (\Exception $e) {
            // Rollback in case of any exception
            $this->tenantDB()->rollBack();
            return false;
        }
    }

    public function _addTransaction($is_reco = 0, $ledger_id, $txn_amt, $narration, $txn_date, $voucher_type = VOUCHER_JOURNAL, $txn_type = "", $from_txn_id = "", $pay_mode = "", $pay_ref = "", $name = "", $is_opning = "", $other_recp_ref = "", $soc_id = "", $created_by = "", $voucher_reference_id = "", $voucher_reference_number = "", $is_cancelled = 0)
    {
        $txn = new ChsoneLedgerTransaction();
        if (empty($soc_id)) {
            $soc_id = $this->input['company_id'];
        }

        if (!empty($txn_type) || $txn_type != "") {
            $type_txn = $txn_type;
        } else {
            if ($from_txn_id) {
                $mode     = "to";
                $type_txn = "cr";
            } else {
                $mode     = "from";
                $type_txn = "dr";
            }
        }

        //echo "s_opning".$is_opning;exit;
        if ($is_opning == 1) {

            $txn_entrt = ChsoneLedgerTransaction::where('soc_id', $soc_id)
            ->where('is_opening_balance', 1)
            ->where('ledger_account_id', $ledger_id)
            ->where('transaction_date', $txn_date)
            ->first();

            $txn->value_date = $txn_date;
            $txn->txn_id = (isset($txn_entrt->txn_id)  && !empty($txn_entrt->txn_id)) ? $txn_entrt->txn_id : '';
            if (!empty($txn->txn_id)) {
                $txn = ChsoneLedgerTransaction::findFirst("txn_id = " . $txn->txn_id);
            }
        }

        $txn->soc_id = $soc_id;
        $txn->transaction_date = $txn_date;
        $txn->ledger_account_id = $ledger_id;
        $txn->ledger_account_name = $name;
        $txn->voucher_type = $voucher_type;
        $txn->transaction_type = $type_txn; //$ledger_name_obj->nature_of_account;//$txn_type;
        $txn->payment_mode = $pay_mode;
        $txn->payment_reference = $pay_ref;
        $txn->transaction_amount = (is_null($txn_amt)) ? $txn->transaction_amount : $txn_amt;
        $txn->other_reference_id = $other_recp_ref;
        $txn->txn_from_id = $from_txn_id;
        $txn->memo_desc = $narration;
        $txn->is_opening_balance = $is_opning;
        $txn->is_reconciled = $is_reco;
        /*Added New Column in transaction*/
        $txn->voucher_reference_number = $voucher_reference_number;
        $txn->voucher_reference_id = $voucher_reference_id;
        $txn->is_cancelled = $is_cancelled;
        /*End New Column in transaction*/
        $txn->created_by = $this->input['user_id'];
        if (isset($created_by) && !empty($created_by)) {
            $txn->created_by = $created_by;
        }
        $txn->added_on = date("Y-m-d H:i:s");

        if ($txn->save()) {
            return $txn->txn_id;
        } else {
            print_r($txn->getMessages);
        }
        //echo "<pre>";print_r($a->getMessages);exit;
        return false;
    }
}
