<?php

namespace App\Console\Commands\Actions\Income\Advances;

use Illuminate\Console\Command;
use App\Console\Commands\Action;
use App\Models\Tenants\ChsoneCreditAccount;
use App\Models\Tenants\ChsoneUnitsMaster;
use Illuminate\Support\Facades\DB;

// STEP 8 : Create a new action class whitch extends Action class. Keep signature  same as you specified in workflow.
class MemberAdvance extends Action
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'datasource:memberAdvance {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Get Advances List';
    protected $schema = [
        "table" => [
            "tableTitle" => "Advances - ",
            "is_searchable" => true,
            "options" => [
                [
                    "title" => "Members",
                    "redirect" => "/admin/credit-accounts/memberAdvances",
                    "selected" => true
                ],
                [
                    "title" => "Non-Members",
                    "redirect" => "/admin/credit-accounts/nonMemberAdvances"
                ]
            ],
            "select_by" => [
                "building_unit_name" => "Building / Unit Number",
                "society_name" => "Building",
                "unit_number" => "Unit Number"
            ],
            "actions" => [
                [
                    "title" => "New Advances",
                    "icon" => "ri-add-circle-line",
                    "color" => "primary",
                    "redirect" => "/admin/credit-accounts/add",
                    "variant" => "contained"
                ]
            ],
            "fields" => [
                "*"
            ],
            "columns" => [
                [
                    [
                        "title" => "Building / UnitNo",
                        "key" => "building_unit_name"
                    ],
                    [
                        "title" => "Refundable Balance ₹",
                        "key" => "total_refundable",
                    ],
                    [
                        "title" => "Adjustable Balance ₹",
                        "key" => "total_adjustable"
                    ],
                    [
                        "title" => "Balance ₹",
                        "key" => "total_balance"
                    ],
                    [
                        "title" => "Actions",
                        "type" => "actions",
                        "key" => "actions",
                        "actions" => [
                            [
                                "title" => "View",
                                "icon" => "ri-eye-line",
                                "href" => "/admin/credit-accounts/viewAllTransactions/member/:id",
                                "color" => "primary",
                                "marginRight" => "5px"
                            ],
                            [
                                "title" => "Refund Money",
                                "icon" => "ri-loop-left-line",
                                "form" => "vendorAdvanceRefundMoney?type=member",
                                "color" => "primary",
                                "marginRight" => "5px",
                                "disable_on" => [
                                    "disable" => [
                                        false
                                    ]
                                ]
                            ]
                        ]
                    ]
                ],
                // [
                //     [
                //         "title" => "Refundable Balance",
                //         "key" => "total_summary_refundable"
                //     ],
                //     [
                //         "title" => "Adjustable Balance",
                //         "key" => "total_summary_adjustable"
                //     ]
                // ]
            ]
         ]
    ];


    // STEP 9: Specified formatter for your action. This will format your data as per your requirement.
    // Formatter will 3 level depth array. 1st level for outer array and 2nd level for inner array and so on.
    protected $formatter = [
        "id" => "",
        "account_name" => "",
        "society_name" => "",
        "unit_number" => "",
        "building_unit_name" => "concat:society_name,unit_number",
        "total_cr" => "",
        "total_dr" => "",
        "total_refundable_dr" => "",
        "total_refundable_cr" => "",
        "total_adjustable_dr" => "",
        "total_adjustable_cr" => "",
        "total_adjustable" => "",
        "total_refundable" => "",
        "total_balance" => "",
        "disable" => "checkDisable:total_refundable"
    ];
    protected $formatterByKeys = ['id'];

    protected $mapper = [
        "id" => "cr_acc.account_id",
        'unit_id' => "unitmaster.unit_id",
        'unit_number' => 'unitmaster.unit_flat_number',
        'building' => 'unitmaster.soc_building_name',
        'unit' => 'unitmaster.unit_id'
    ];

    /**
     * Execute the console command.
     */



    public function apply()
    {
        $building_unit_name = '';
        $society_name = '';
        $unit_number = '';
        $soc_id = $this->input['company_id'];
        $searchTerm = $this->input['filters']['search'] ?? '';

        $page = $this->input['page'] ?? 1;
        $per_page = !empty($this->input['per_page']) ? $this->input['per_page'] : ($this->hugeData ? 100000 : 10);
        $offset = ($page - 1) * $per_page;

        if (isset($this->input['filters'])) {
            $building_unit_name = !empty($this->input['filters']['building_unit_name']) ? $this->input['filters']['building_unit_name'] : '';
            $society_name = !empty($this->input['filters']['society_name']) ? $this->input['filters']['society_name'] : '';
            $unit_number = !empty($this->input['filters']['unit_number']) ? $this->input['filters']['unit_number'] : '';
            //$status = !empty($this->input['filters']['status']) ? $this->input['filters']['status'] : '';
        }

        try {
            // Start with the base query without aggregation
            /*$query = $this->tenantDB()->table('chsone_credit_accounts as cr_acc')
                ->leftJoin('chsone_units_master as unitmaster', 'cr_acc.account_id', '=', 'unitmaster.unit_id')
                ->where('cr_acc.account_context', "unit");*/

            // Apply filters before aggregation




            $query = $this->tenantDB()->table('chsone_credit_accounts as cr_acc')
                ->select([
                    'cr_acc.credit_account_id',
                    'cr_acc.account_id as id',
                    'cr_acc.account_name',
                    'cr_acc.use_credit',
                    'cr_acc.use_credit_after',
                    'cr_acc.use_credit_for',
                    'cr_acc.amount',
                    'cr_acc.transaction_type',
                    'unitmaster.soc_building_name as society_name',
                    'unitmaster.unit_flat_number as unit_number',
                    'unitmaster.unit_id',
                    DB::raw('ROUND(SUM(IF(cr_acc.transaction_type = "cr", cr_acc.amount, 0)), 2) AS total_cr'),
                    DB::raw('ROUND(SUM(IF(cr_acc.transaction_type = "dr", cr_acc.amount, 0)), 2) AS total_dr'),
                    DB::raw('ROUND(SUM(IF(cr_acc.transaction_type = "dr" AND cr_acc.use_credit = "refundable", cr_acc.amount, 0)), 2) AS total_refundable_dr'),
                    DB::raw('ROUND(SUM(IF(cr_acc.transaction_type = "cr" AND cr_acc.use_credit = "refundable", cr_acc.amount, 0)), 2) AS total_refundable_cr'),
                    DB::raw('ROUND(SUM(IF(cr_acc.transaction_type = "dr" AND cr_acc.use_credit = "adjustable", cr_acc.amount, 0)), 2) AS total_adjustable_dr'),
                    DB::raw('ROUND(SUM(IF(cr_acc.transaction_type = "cr" AND cr_acc.use_credit = "adjustable", cr_acc.amount, 0)), 2) AS total_adjustable_cr'),
                    DB::raw('FORMAT(SUM(IF(cr_acc.transaction_type = "cr" AND cr_acc.use_credit = "adjustable", cr_acc.amount, 0)) - SUM(IF(cr_acc.transaction_type = "dr" AND cr_acc.use_credit = "adjustable", cr_acc.amount, 0)), 2) AS total_adjustable'),
                    DB::raw('FORMAT(SUM(IF(cr_acc.transaction_type = "cr" AND cr_acc.use_credit = "refundable", cr_acc.amount, 0)) - SUM(IF(cr_acc.transaction_type = "dr" AND cr_acc.use_credit = "refundable", cr_acc.amount, 0)), 2) AS total_refundable'),
                    DB::raw('FORMAT(SUM(IF(cr_acc.transaction_type = "cr", cr_acc.amount, 0)) - SUM(IF(cr_acc.transaction_type = "dr", cr_acc.amount, 0)), 2) AS total_balance'),
                ])
                ->join('chsone_units_master as unitmaster', 'cr_acc.account_id', '=', 'unitmaster.unit_id')
                ->where('cr_acc.soc_id', $soc_id)
                ->where('cr_acc.account_context', 'unit')
                ->groupBy('cr_acc.account_id')
                ->orderByDesc('cr_acc.credit_account_id');


            /*if ($status) {
                $query->where('cr_acc.status', 1);
            }*/

            if ($building_unit_name) {
                $query->where(function ($query) use ($building_unit_name) {
                    $query->where(DB::raw("CONCAT(unitmaster.soc_building_name, '/', unitmaster.unit_flat_number)"), 'like', '%' . $building_unit_name . '%')
                        ->orWhere('unitmaster.unit_flat_number', 'like', '%' . $building_unit_name . '%')
                        ->orWhere('unitmaster.soc_building_name', 'like', '%' . $building_unit_name . '%');
                });
            }

            if ($society_name) {
                $query->where('unitmaster.soc_building_name', 'like', '%' . $society_name . '%');
            }

            if ($unit_number) {
                $query->where('unitmaster.unit_flat_number', 'like', '%' . $unit_number . '%');
            }

            //$result = $query->get();

            // If there's a search term, use HAVING for aggregated values and WHERE for non-aggregated values
            if ($searchTerm) {
                $query->havingRaw("total_adjustable LIKE ?", ['%' . $searchTerm . '%'])
                    ->orHavingRaw("total_refundable LIKE ?", ['%' . $searchTerm . '%'])
                    ->orHavingRaw("total_balance LIKE ?", ['%' . $searchTerm . '%'])
                    ->orHavingRaw("account_name LIKE ?", ['%' . $searchTerm . '%'])
                    ->orHavingRaw("soc_building_name LIKE ?", ['%' . $searchTerm . '%'])
                    ->orHavingRaw("unit_flat_number LIKE ?", ['%' . $searchTerm . '%'])
                    ->orHavingRaw("unit_id LIKE ?", ['%' . $searchTerm . '%'])
                    ->orHavingRaw("society_name LIKE ?", ['%' . $searchTerm . '%'])
                    ->orHavingRaw("unit_number LIKE ?", ['%' . $searchTerm . '%']);
            }


            // Clone the query before pagination for summary calculation
            $fullQuery = clone $query;

            // Get count and paginated results
            $count = $query->get()->count();
            $result = $query->offset($offset)->limit($per_page)->get();
            $totals = $result->reduce(function ($carry, $item) {
                $ref = (float) str_replace(',', '', $item->total_refundable);
                $adj = (float) str_replace(',', '', $item->total_adjustable);
            
                $carry['total_summary_refundable'] += $ref;
                $carry['total_summary_adjustable']  += $adj;
                return $carry;
            }, [
                'total_summary_refundable' => 0.0,
                'total_summary_adjustable' => 0.0,
            ]);
            
            // Get full results (no offset/limit)
            $fullResults = $fullQuery->get();

            // Calculate totals from the full result
            $totals = $fullResults->reduce(function ($carry, $item) {
                $ref = (float) str_replace(',', '', $item->total_refundable);
                $adj = (float) str_replace(',', '', $item->total_adjustable);

                $carry['total_summary_refundable'] += $ref;
                $carry['total_summary_adjustable'] += $adj;
                return $carry;
            }, [
                'total_summary_refundable' => 0.0,
                'total_summary_adjustable' => 0.0,
            ]);

            // inject id at the front
            $totals = ['id' => 1] + $totals;
            
            // format as strings
            $totals['total_summary_refundable'] = (int) round($totals['total_summary_refundable']);
            $totals['total_summary_adjustable']  = (int) round($totals['total_summary_adjustable']);



            // Format and set the data
            $final = [
                $this->format($result->toArray()),
                [ $totals ]   
            ];
            
            $this->data = $final;
            $this->meta['schema'] = $this->input['reportSchema'] ?? $this->schema;
            $this->meta['pagination']['total'] = $count;
        } catch (\Exception $e) {
            $this->status = 'error';
            $this->message = 'Something went wrong: ' . $e->getMessage();
        }
    }

    public function concat($a, $b)
    {
        return $a . '/' . $b;
    }

    public function checkDisable($total_refundable)
    {
        return $total_refundable > 0 ? true : false;
    }
}
