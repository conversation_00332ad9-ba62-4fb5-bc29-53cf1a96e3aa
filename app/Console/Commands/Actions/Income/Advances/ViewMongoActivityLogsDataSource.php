<?php

namespace App\Console\Commands\Actions\Income\Advances;

use App\Console\Commands\Action;
use App\Http\Traits\MongoTraits;

class ViewMongoActivityLogsDataSource extends Action
{
    use MongoTraits;

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'datasource:viewMongoActivityLogs {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'View Mongo Activity Logs Data Source';

    /**
     * Execute the console command.
     */

    public function apply()
    {
        $id = $this->input['id'];

        $module = $this->input['module'];
        $company_id = $this->input['company_id'];
        $id = $this->input['id'];
        $fields = $this->input['fields'] ?? [];
        if (!empty($fields)) {
            $fields = json_decode($fields);
        }

        $this->data = $this->showNotification($module, $company_id, $id, $fields);

    }
}
