<?php

namespace App\Console\Commands\Actions\Income\Advances;

use Illuminate\Console\Command;
use App\Console\Commands\Action;
use App\Models\Tenants\ChsoneUnitsMaster;
use Illuminate\Support\Facades\Route;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class ViewTransactionAction extends Action
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'datasource:viewTransaction {flowId} {parentId} {input}';


    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'View Transaction List of Member/Non Member/Vendors Advances';

    protected $schema = [];

    protected $formatter =  [
        "id" => "",
        "account_id" => "",
        "account_context" => "",
        "credit_account_id" => "",
        "soc_id" => "",
        "payment_date" => "",
        "account_name" => "",
        "amount" => 'getAmountPaid:amount',
        "type" => "",
        "payment_mode" => "",
        "transaction_type" => "",
        "narration" => "",
        "use_credit" => "",
        "use_credit_for" => "",
        "is_invoice_rectification" => "",
        "context" => "",
        "disable" => ""
    ];


    /**
     * Execute the console command.
     */
    public function apply()
    {
        $id = $this->input['id'];
        $type = $this->input['type'];
        $accountId = $type;
        $accountType = $id;

        if (strtolower($accountType) == "member") {
            $unit_name = '';
            $accountType = "unit";
            // fetch unit_details from chsone_units_master table
            $unitDetails = ChsoneUnitsMaster::where('unit_id', $accountId)->first();
            $unit_name = $unitDetails->soc_building_name .' / '.$unitDetails->unit_flat_number;
        } elseif (strtolower($accountId) == "vendor") {
            $accountType = "vendor";
        } elseif(strtolower($accountId) == "nonmember") {
            $accountType = "nonmember";
        }

        switch ($accountType) {
            case 'unit':
                $this->schema = [
                    "table" => [
                        "tableTitle" => "View Transactions",
                        "actions" => [
                            [
                                "title" => "Back",
                                "icon" => "ri-arrow-left-line",
                                "color" => "secondary",
                                "redirect" =>  "/admin/credit-accounts/memberAdvances",
                            ]

                        ],
                        "fields" => [
                            "*"
                        ],
                        "columns" => [
                            [
                                "title" => "Date",
                                "key" => "payment_date"
                            ],
                            [
                                "title" => "Name",
                                "key" => "account_name"
                            ],
                            [
                                "title" => "Narration",
                                "key" => "narration"
                            ],
                            [
                                "title" => "Amount",
                                "key" => "amount",
                                "type"=>"number",
                                "currency"=>"inr"
                            ],
                            [
                                "title" => "CR/DR",
                                "key" => "transaction_type"
                            ],
                            [
                                "title" => "Payment Mode",
                                "key" => "payment_mode"
                            ],
                            [
                                "title" => "Type",
                                "key" => "use_credit"
                            ],
                            [
                                "title" => "Actions",
                                "type" => "actions",
                                "key" => "actions",
                                "actions" => [
                                    [
                                        "title" => "Can't Edit because it is System Generated",
                                        "icon" => "ri-progress-3-line",
                                        "color" => "primary",
                                        "disable_on" => [
                                            "disable" => [
                                                1
                                            ]
                                        ],
                                        "hide_on"=>[
                                            "disable"=>[
                                                0
                                            ]
                                        ]


                                    ],
                                    [
                                        "title" => "Edit",
                                        "icon" => "ri-edit-box-line",
                                        "href" => "/admin/credit-accounts/add/:id",
                                        "color" => "primary",
                                            "hide_on" => [
                                                "disable" => [
                                                    1
                                                ]
                                            ]
                                    ],
                                    [
                                        "title" => "View Activity Logs",
                                        "icon" => "ri-list-unordered",
                                        "href" => "/admin/credit-accounts/viewActivityLogs/$id/:id",
                                        "color" => "primary",
                                    ]
                                ]
                            ]
                        ]
                    ]
                ];
                break;
            case 'nonmember':
                $this->schema = [
                    "table" => [
                        "tableTitle" => "View Transactions",
                        "actions" => [
                            [
                                "title" => "Back",
                                "icon" => "ri-arrow-left-line",
                                "color" => "secondary",
                                "redirect" =>  "/admin/credit-accounts/nonMemberAdvances",
                            ]

                        ],
                        "fields" => [
                            "*"
                        ],
                        "columns" => [
                            [
                                "title" => "Date",
                                "key" => "payment_date"
                            ],
                            [
                                "title" => "Name",
                                "key" => "account_name"
                            ],
                            [
                                "title" => "Narration",
                                "key" => "narration"
                            ],
                            [
                                "title" => "Amount",
                                "key" => "amount",
                                "type"=>"number",
                                "currency"=>"inr"
                            ],
                            [
                                "title" => "CR/DR",
                                "key" => "transaction_type"
                            ],
                            [
                                "title" => "Payment Mode",
                                "key" => "payment_mode"
                            ],
                            [
                                "title" => "Type",
                                "key" => "use_credit"
                            ],
                            [
                                "title" => "Actions",
                                "type" => "actions",
                                "key" => "actions",
                                "actions" => [
                                    [
                                        "title" => "Can't Edit because it is System Generated",
                                        "icon" => "ri-progress-3-line",
                                        "color" => "primary",
                                        "disable_on" => [
                                            "disable" => [
                                                1
                                            ]
                                        ],
                                        "hide_on"=>[
                                            "disable"=>[
                                                0
                                            ]
                                        ]


                                    ],
                                    [
                                        "title" => "Edit",
                                        "icon" => "ri-edit-box-line",
                                        "href" => "/admin/credit-accounts/add/:id",
                                        "color" => "primary",
                                            "hide_on" => [
                                                "disable" => [
                                                    1
                                                ]
                                            ]
                                    ],
                                    [
                                        "title" => "View Activity Logs",
                                        "icon" => "ri-list-unordered",
                                        "href" => "/admin/credit-accounts/viewActivityLogs/$id/:id",
                                        "color" => "primary",
                                    ]
                                ]
                            ]
                        ]
                    ]
                ];
                break;
            case 'vendor':
                $this->schema = [
                    "table" => [
                        "tableTitle" => "View Transactions",
                        "actions" => [
                            [
                                "title" => "Back",
                                "icon" => "ri-arrow-left-line",
                                "color" => "#6c757d",
                                "redirect" =>  "/admin/vendorbill/vendorAdvances",
                            ]
                        ],
                        "fields" => [
                            "*"
                        ],
                        "columns" => [
                            [
                                "title" => "Date",
                                "key" => "payment_date"
                            ],
                            [
                                "title" => "Narration",
                                "key" => "narration"
                            ],
                            [
                                "title" => "Amount",
                                "key" => "amount",
                                "type"=>"number",
                                "currency"=>"inr"
                            ],
                            [
                                "title" => "CR/DR",
                                "key" => "transaction_type"
                            ],
                            [
                                "title" => "Payment Mode",
                                "key" => "payment_mode"
                            ],
                            [
                                "title" => "Type",
                                "key" => "use_credit"
                            ],
                            [
                                "title" => "Actions",
                                "type" => "actions",
                                "key" => "actions",
                                "actions" => [
                                    [
                                        "title" => "Can't Edit because it is System Generated",
                                        "icon" => "ri-progress-3-line",
                                        "color" => "primary",
                                        "disable_on" => [
                                            "disable" => [
                                                1
                                            ]
                                        ],
                                        "hide_on" => [
                                            "disable" => [
                                                    0
                                            ]
                                        ],
                                    ],
                                    [
                                        "title" => "Edit",
                                        "icon" => "ri-edit-box-line",
                                        "href" => "/admin/vendorbill/addVendorAdvances/:id",
                                        "color" => "primary",
                                        "hide_on" => [
                                                "disable" => [
                                                    1
                                                ],
                                        ],
                                    ],
                                    // [
                                    //     "title" => "Edit",
                                    //     "icon" => "ri-edit-box-line",
                                    //     "href" => "/admin/vendorbill/editVendorAdvances/:id",
                                    //     "color" => "primary",
                                    //     "hide_on" => [
                                    //             "disable" => [
                                    //                1
                                    //             ],
                                    //             "use_credit" => [
                                    //                 "refundable"
                                    //             ]
                                    //     ],
                                    // ]
                                ]
                            ]
                        ]
                    ]
                ];
                break;
        }

        // add pagination
        $page = $this->input['page'] ?? 1;
        $per_page = !empty($this->input['per_page']) ? $this->input['per_page']: ($this->hugeData ? 100000 : 10);
        $offset = ($page - 1) * $per_page;

        $obj = $this->tenantDB()->table('chsone_credit_accounts as cr_acc')
        ->selectRaw(
            'credit_account_id as id,
            account_id,
            account_context,
            credit_account_id,
            soc_id,
            payment_date,
            account_name,
            amount,
            CONCAT(UPPER(LEFT(type, 1)), LOWER(SUBSTRING(type, 2))) as type,
            COALESCE(NULLIF(payment_mode, ""), "NA") as payment_mode,  -- Use COALESCE to default payment_mode to NA if NULL
            UPPER(transaction_type) as transaction_type,
            narration,
            CONCAT(UPPER(LEFT(use_credit, 1)), LOWER(SUBSTRING(use_credit, 2))) as use_credit,
            CONCAT(UPPER(LEFT(COALESCE(use_credit_for, "NA"), 1)), LOWER(SUBSTRING(COALESCE(use_credit_for, "NA"), 2))) as use_credit_for,  -- Use COALESCE to default use_credit_for to NA if NULL
            is_invoice_rectification,
            context,
            CASE
            WHEN context = "system"
                 OR use_credit = "adjustable"
                 AND use_credit_after IS NOT NULL AND use_credit_after <= ?
            THEN true
            ELSE false
        END AS disable',[
            Carbon::now()->format('Y-m-d')
        ]
        )
        ->where('cr_acc.account_id', $accountId)
        ->where('cr_acc.account_context', $accountType)
        ->orderByDesc('cr_acc.credit_account_id');

        // add count and pagination
        $count = $obj->count();

        $obj = $obj->offset($offset);
        $obj = $obj->limit($per_page);

        $result = $obj->get();

        $this->data = $this->format($result->toArray());
        $this->meta['pagination']['total']=$count;
        $this->meta['schema'] = $this->schema;

        if($accountType == "vendor" || $accountType == "nonmember") {
            $vendorName = '';
            foreach($this->data as $details) {
                $vendorName = $details['account_name'];
            }
            if($vendorName){
                $this->meta['schema']['table']['tableTitle'] = $vendorName . ' / View Transactions';
            }
        } else {
            $this->meta['schema']['table']['tableTitle'] = $unit_name . ' / View Transactions';
        }
    }

    public function getAmountPaid($payment_amount)
    {

        $payment_amount = (float) $payment_amount;

        //return '₹ ' . number_format($payment_amount, 2) ;
        return $payment_amount;
    }
}
