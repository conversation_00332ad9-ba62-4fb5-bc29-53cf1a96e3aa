<?php

namespace App\Console\Commands\Actions\Income\Advances;

use _PHPStan_62c6a0a8b\Nette\Neon\Exception;
use App\Console\Commands\Action;
use App\Models\Tenants\ChsoneGrpLedgerTree;
use App\Models\Tenants\ChsoneNonmemberMaster;
use App\Models\Tenants\ChsoneTaxLog;
use App\Models\Tenants\IncomeAccount;
use App\Models\Tenants\IncomeInvoiceParticular;
use App\Models\Tenants\IncomeInvoiceRule;
use App\Models\Tenants\IncomeUnitInvoice;
use Illuminate\Support\Facades\Config;
use DateTime;

// Import DateTime correctly
use Illuminate\Support\Facades\DB;

// STEP 8 : Create a new action class whitch extends Action class. Keep signature  same as you specified in workflow.
class AddAdvancesAction extends Action
{

    protected $constants;

    public function __construct()
    {
        parent::__construct();
        $this->constants = Config::get("constants");
    }

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'datasource:addAdvances {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Add Advances';

    /**
     * Execute the console command.
     */
    public function apply()
    {
        try {
            // $this->input['account_context'] = $this->input['account_type'] = 'unit';
            $this->input['user_id'] = $this->input['user_id'] ?? 0;
            $this->input['user'] = $this->input['user'] ?? 0;
            $postData = $this->input;
            $postData['soc_id'] = $this->input['company_id'];
            $memberData = $this->getMember($this->input['account_context'], $this->input['account_id_member']);
            if (empty($memberData)) {
                $this->message = "Member not found.";
                $this->status = 'error';
                $this->statusCode = 400;
                return;
            }


            $memberData = json_decode(json_encode($memberData), true);
            $postData['search_account_name'] = $memberData['building'] . '-' . $memberData['building_unit'];
            if ($postData['payment_mode'] !== 'cheque') {
                $paymentDate = new DateTime($postData['payment_date']);
                $currentDate = new DateTime();

                if ($paymentDate > $currentDate) {
                    $this->message = "Payment date should be less than or equal to the current date for the selected payment mode.";
                    $this->status = 'error';
                    $this->statusCode = 400;
                    return;
                }
            }

            if ($postData['credit_used_type'] == 'both') {

                $combinedAmount = $postData['adjustable_amount'] + $postData['refundable_amount'];

                if ($combinedAmount != $postData['payment_amount']) {
                    $this->message = "The sum of adjustable amount and refundable amount should be equal to the payment amount.";
                    $this->status = 'error';
                    $this->statusCode = 400;
                    return;
                }
            }

            if ($postData['account_context'] == 'unit' && ($postData['credit_used_type'] == 'both' || $postData['credit_used_type'] == 'adjustable')) {
                if (empty($postData['used_for'])) {
                    $this->message = "used_for is required for unit context.";
                    $this->status = 'error';
                    $this->statusCode = 400;
                    return;
                }
            }

            if ($postData['payment_mode'] !== 'cash' && $postData['bank_account']) {
                $checkBankAccount = $this->tenantDB()->table('chsone_accounts_master')->where('ledger_account_id', $postData['bank_account'])->first();

                if (empty($checkBankAccount)) {
                    $this->message = "Bank account not found.";
                    $this->status = 'error';
                    $this->statusCode = 400;
                    return;
                }
            } else {
                // fetch cash in hand ledger_account_id ffrom chsone_grp_ledger_tree
                $cashInHandLedgerId = ChsoneGrpLedgerTree::where('ledger_account_name', 'like', '%Cash%')->where ('context','cash')->orderBy('ledger_account_id','desc')->first()->ledger_account_id;
                $postData['bank_account'] = $cashInHandLedgerId;
            }

            $postData['payment_note'] = $postData['narration'];
            // echo "<pre>";print_r($postData);exit;
            $data = $this->formatData($postData);

            $data['account_name'] = $memberData['title'];

            $data['action'] = 'insert';
            $data['user_id'] = $this->input['user_id'];
            $data['unit_id'] = $memberData['unit_id'];

            if (strtolower($data['credit_used_type']) == 'builder_deposit') {

                $postData['soc_id'] = $data['soc_id'];

                $postData['transaction_type'] = 'cr';
                $arrCreditData['soc_id'] = $data['soc_id'];
                $arrCreditData['account_id'] = $postData['account_id'];
                $arrCreditData['account_context'] = 'nonmember';
                $arrCreditData['transaction_type'] = 'cr';
                $arrCreditData['context'] = 'system';
                $arrCreditData['credit_used_type'] = 'adjustable';
                $arrCreditData['used_for'] = '';
                $arrCreditData['payment_date'] = !empty($data['payment_date']) ? $data['payment_date'] : $this->getCurrentDate('display');
                $arrCreditData['narration'] = 'Amount Rs ' . $data['payment_amount'] . ' has credited from Advance of ' . $data['bill_number'];
                $arrCreditData['payment_amount'] = $data['payment_amount'];
                $arrCreditData['account_name'] = $data['account_name'];

                $arrResponse = $this->saveCreditAccountResponse(array('process' => 'fetch', 'soc_id' => $data['soc_id'], 'id' => '', 'data' => $arrCreditData, 'user' => $this->input['user_id']));

                if (empty($arrResponse)) {

                    $this->message = "Unable to complete transaction, Please try later.";
                    $this->status = 'error';
                    $this->statusCode = 400;
                    // $this->session->set("err_msg", 'Unable to complete transaction, Please try later.');
                } else {
                    $this->message = "Builder deposit created successfully.";
                    $this->status = 'success';
                    $this->statusCode = 200;
                    // $this->session->set('suc_msg', 'Builder deposit created successfully.');
                }
                // return $this->response->redirect($this->config->system->full_base_url . "credit-accounts/memberAdvances");
            } else {

                $this->contextCreditAccount($data, 0, $postData['tab'] ?? '', $postData['page'] ?? '');
            }
        } catch (\Exception $e) {
            $this->message = $e->getMessage();
            $this->status = 'error';
            $this->statusCode = 400;
        }
    }

    public function saveCreditAccountResponse($data = array())
    {
        try {

            $user = $data['user'] ?? 0;
            $id = $data['id'] ?? '';
            $data = $data['data'];

            if ($id) {
                // Prepare the data to be updated
                $updateData = [
                    'updated_by' => $this->input['user_id'] ?? 0,
                    'updated_date' => date('Y-m-d H:i:s'),
                    'use_credit_after' => !empty($data['adjustable_date']) ? $data['adjustable_date'] : null,
                    'is_locked' => 0,
                    'use_credit_for' => !empty($data['used_for']) ? $data['used_for'] : null,
                    'is_invoice_rectification' => isset($data['is_invoice_rectification']) && !empty($data['is_invoice_rectification']) ? $data['is_invoice_rectification'] : null,
                    'income_account_id' => isset($data['income_account_ledger_id']) && !empty($data['income_account_ledger_id']) ? $data['income_account_ledger_id'] : null,
                    'narration' => $data['narration'],
                    'use_credit' => isset($data['credit_used_type']) && !empty($data['credit_used_type']) ? $data['credit_used_type'] : 'adjustable',
                ];

                // Update the record in the database
                $result = $this->tenantDB()
                    ->table('chsone_credit_accounts')
                    ->where('credit_account_id', $id)
                    ->where('soc_id', $data['soc_id'])
                    ->update($updateData);

                // Save data
                if ($result) {
                    return $result;
                } else {
                    return false;
                }
            } else {
                if ($data['credit_used_type'] == 'both') {
                    $data['credit_used_type'] = 'adjustable';
                    $data['payment_amount'] = $data['adjustable_amount'];
                    $saveResponse = $this->saveCreditNote($data);
                    if ($saveResponse['error'] == false) {
                        $data['credit_used_type'] = 'refundable';
                        $data['payment_amount'] = $data['refundable_amount'];
                        $data['used_for'] = null;
                        $data['adjustable_date'] = null;

                        $saveResponse = $this->saveCreditNote($data);
                        if ($saveResponse['error'] == false) {
                            return $saveResponse;
                        }
                    }
                } else {
                    $saveResponse = $this->saveCreditNote($data);
                    if ($saveResponse['error'] == false) {
                        return $saveResponse;
                    }
                }
            }
        }catch (\Exception $e) {
            $this->message = $e->getMessage();
            $this->status = 'error';
            $this->statusCode = 400;
        }

    }

    public function contextCreditAccount($data, $id = 0, $tab = '', $page = '')
    {
        /** If Type 'unit' Then Member Code Else Non-member Code* */
        if ($data['account_context'] == 'unit') {
            $this->usedForCreditAccount('unit', $data, $id, $tab, $page);

        } else {
            $this->usedForCreditAccount('nonmember', $data, $id, $tab, $page);
        }
    }

    public function usedForCreditAccount($type, $data, $id, $tab, $page)
    {
        /** If Type 'unit' Then Member Code Else Non-member Code* */

        if ($type === 'unit') {
            /** If Type 'refundable' Then Refundable Code Else Adjustable Code* */
            if ($data['credit_used_type'] == 'refundable') {

                $this->paymentTransaction($data, $id, $tab, $page);

            } elseif ($data['credit_used_type'] == 'both') {
                $this->paymentTransaction($data, $id, $tab, $page);
            } else {
                /** If 'Is Rectification 1' Then Rectification Code Else Adjustable Code* */
                if ($data['is_invoice_rectification']) {

                    $this->revisedBillRectification($data, $id);
                } else {

                    $this->paymentTransaction($data, $id, $tab, $page);
                }
            }
        } else {
            /** If Type 'refundable' Then Refundable Code Else Adjustable Code* */
            if ($data['credit_used_type'] == 'refundable') {

                $this->paymentTransaction($data, $id);
            } elseif ($data['credit_used_type'] == 'both') {
                $this->paymentTransaction($data, $id, $tab, $page);
            }else {
                /** If 'Is Rectification 1' Then Rectification Code Else Adjustable Code* */
                if ($data['is_invoice_rectification']) {
                    $this->billRectification($data, $id);
                } else {
                    $this->paymentTransaction($data, $id, $tab, $page);
                }
            }
        }
    }

//     public function paymentTransaction($data, $id, $tab = '', $page = '')
//     {

//         /** If Type 'unit' Then Member Code Else Non-member Code* */
//         if ($data['account_context'] == 'unit') {
//             $arrDataListener['soc_id'] = $data['soc_id'];
//             $arrDataListener['unit_id'] = $data['unit_id'];
//             //only for members
//             $arrIncomeInvoiceMemberDetail = $this->getMemberDetail($arrDataListener); //get all Unit details

//             if (!empty($arrIncomeInvoiceMemberDetail)) {
//                 $arrDataListener['member_id'] = $arrIncomeInvoiceMemberDetail['id'];
//             }

//             //Payment Tracker
//             $arrgenerateInvoiceid = array('soc_id' => $data['soc_id']);

//             $data['receipt_number'] = $this->generate_receipt_id($arrgenerateInvoiceid);

//             $paymentToken = $data['payment_token'] = $this->generatePaymentToken(array('arrPaymentTracker' => $data));

//             $arrResponseTracker = $this->saveInvoicePaymentTracker(array('soc_id' => $data['soc_id'], 'unit_id' => $data['unit_id'], 'postData' => $data)); //get all Unit details

//             $data['payment_tracker_id'] = $arrResponseTracker['payment_tracker_id'];

//             $isTokenValid = $this->paymentTokenVerification(array('soc_id' => $data['soc_id'], 'paymentToken' => $paymentToken));

//             $creditData['credit_account_detail'] = $data; // to store credit account data
//             $intBookerLedgerDetails = null;
//             if ($isTokenValid && !empty($arrResponseTracker['payment_tracker_id'])) {
//                 $this->updateIncomePaymentTrackerStatus(array('soc_id' => $data['soc_id'], 'updated_by' => $data['user_id'], 'payment_tracker_id' => $arrResponseTracker['payment_tracker_id'], 'transaction_status' => 'in_process'));
//                 //In case of cheque or DD

//                 if (in_array(strtolower($data['payment_mode']), $this->constants['payment_mode_for_clearance'])) {

//                     if (!empty($arrResponseTracker)) {
//                         $arrResponse = $arrResponseTracker;
//                         $succMsg = 'Payment has successfully added to payment tracker waiting for clearance.';
//                         preg_match('#\((.*?)\)#', $data['search_account_name'], $match);
//                         $buildingUnit = $match[1];
//                         $creditData['credit_account_detail']['buildingUnit'] = $buildingUnit;
//                     }
//                 } else {

//                     //$creditData = array();
//                     if (!empty($arrResponseTracker)) {

//                         $arrResponse = $arrResponseTracker;
//                         //ledger member
//                         if ($data['credit_used_type'] == 'refundable') {
//                             preg_match('#\((.*?)\)#', $data['search_account_name'], $match);
//                             $buildingUnit = $match[1];

//                             if ($data['payment_mode'] == 'cashtransfer') {$payment_mode = $this->constants['DISPLAY_CASH_TRANSFER'] ?? 'Electronic Fund Transfer';} else { $payment_mode = ucfirst($data['payment_mode']);}
//                             $narration = 'Refundable deposit recieved via ' . $payment_mode; //. '(' . ucfirst($data['narration']) . ')';

//                             $arrToLedgerDetails = $this->checkledgerExistOrCreate(array("soc_id" => $data['soc_id'], 'ledger_name' => $buildingUnit));

//                             $data['income_account_ledger_id'] = $arrToLedgerDetails['recieving_ledger_id'];
//                             $arrResponse = $this->createMemberLedgerCredit(array("soc_id" => $data['soc_id'], 'unit_id' => $data['unit_id'], 'arrPostData' => $data, 'arrIncomeInvoiceMemberDetail' => $arrIncomeInvoiceMemberDetail, 'to_ledger' => $arrToLedgerDetails, 'narration' => $narration));

//                             // $succMsg = ucwords($match[1]) . ' ' . VENDORBILLPAYMENT_SUCC_MSG;
//                         } else {

//                             if ($data['payment_mode'] == 'cashtransfer') {
//                                 $payment_mode = $this->constants['DISPLAY_CASH_TRANSFER'] ?? 'Electronic Fund Transfer';
//                             } else { $payment_mode = ucfirst($data['payment_mode']);}

//                             $narration = 'Adjusatble advances recieved via ' . $payment_mode;
//                             $arrResponse = $this->createMemberLedgerCredit(array("soc_id" => $data['soc_id'], 'unit_id' => $data['unit_id'], 'arrPostData' => $data, 'arrIncomeInvoiceMemberDetail' => $arrIncomeInvoiceMemberDetail, 'to_ledger' => [], 'narration' => $narration));
//                             // $succMsg = ucwords($match[1]) . ' ' . VENDORBILLPAYMENT_SUCC_MSG;
//                         }
//                     }
//                 }
//             } else {

//                 // Transaction complete with out payment
//                 if (!empty($arrResponseTracker['payment_tracker_id'])) {
//                     $this->updateIncomePaymentTrackerStatus(array('soc_id' => $data['soc_id'], 'updated_by' => $data['user_id'], 'payment_tracker_id' => $arrResponseTracker['payment_tracker_id'], 'status' => 'N', 'transaction_status' => 'complete'));
//                 }
//             }

//             //member redirect after response
//             if ($data['bill_type'] == 'creditaccount-member' && (!empty($arrResponse))) {
//                 $trackertStatus = 'Y';

//                 if (in_array(strtolower($data['payment_mode']), $this->constants['payment_mode_for_clearance'])) {
//                     $trackertStatus = 'P';
//                     if (strtolower($data['payment_mode']) == 'cheque' && $this->getDatabaseDate($data['payment_date']) > $this->getCurrentDate('database')) {
//                         $trackertStatus = 'R'; //Update received cheque to pdc state
//                     }
//                 }

//                 if (!empty($arrResponseTracker['payment_tracker_id'])) {
//                     $creditData = $this->escpaeSingleQuotes($creditData['credit_account_detail']);
//                     $this->updateIncomePaymentTrackerStatus(array('soc_id' => $data['soc_id'], 'updated_by' => $data['user_id'], 'payment_tracker_id' => $arrResponseTracker['payment_tracker_id'], 'status' => $trackertStatus, 'transaction_status' => 'complete', 'other_information' => serialize($creditData)));
//                     $data['payment_tracker_id'] = $arrResponseTracker['payment_tracker_id'];
//                 }

//                 //In case of cheque or DD
//                 if (in_array(strtolower($data['payment_mode']), $this->constants['payment_mode_for_clearance'])) {

//                 } else {
//                     $id = $this->addCreditAmountresponse($data, $id);
//                     if ($id['error'] == true) {
//                         // $this->soc_db_w->rollback(); //rollback in case of failure
//                         // $this->session->set("err_msg", $id['data']['message'][0]);
// //                        return $this->response->redirect($this->config->system->full_base_url . "credit-accounts/list/".$tab.'/?page='.$page);
//                         // if($tab == 'member'){
//                         //     return $this->response->redirect($this->config->system->full_base_url . "credit-accounts/memberAdvances");
//                         // }else{
//                         //     return $this->response->redirect($this->config->system->full_base_url . "credit-accounts/nonmemberAdvances");
//                         // }
//                     }
//                 }
//                 /*$socUsers = $this->session->get("socUsers");
//                 $notification_title = 'Advanced recieved For '.ucwords($match[1]);
//                 $notification_data = ['title' => $notification_title,
//                 'desc' => $narration,
//                 'module' => 'chsone_credit_accounts',
//                 'scope' => 'group',
//                 'role' => 'admin',
//                 'pk_id' => $id['data'],
//                 'date_time' => date("d-M-Y H:i:s"),
//                 'member_id' => [],
//                 'user_id' => [],
//                 'username' => $data['user_first_name'] . ' ' . $this->auth['user_last_name'],
//                 'created_by' => $this->auth['user_id'],
//                 'soc_id' => $this->auth['soc_id'],
//                 'to_be_notified' => 0,
//                 'all_staff' => [], // used as to send notification to all staff
//                 'all_admin' => $socUsers['socAdminUsers']]; // used as to send notification to all admin
//                 \ChsOne\Helper\CommonHelper::addNotification($notification_data);*/

//                 // $this->soc_db_w->commit(); //commit all records
//                 // $this->view->setVar("id", $id);
//                 // $this->session->set('suc_msg', $succMsg);
// //                return $this->response->redirect($this->config->system->full_base_url . "credit-accounts/list/".$tab.'/?page='.$page);
// //                if($tab == 'member'){
//                 // return $this->response->redirect($this->config->system->full_base_url . "credit-accounts/memberAdvances");
// //                }else{
// //                    return $this->response->redirect($this->config->system->full_base_url . "credit-accounts/nonmemberAdvances");
// //                }
//             } else {
//                 // $this->soc_db_w->rollback(); //rollback in case of failure
//                 // $this->session->set("err_msg", 'Unable to complete transaction, Please try later.');

//                 if (empty($arrResponse) || (!empty($arrResponse) && strtolower($arrResponse['status']) != 'success')) {
//                     if (!empty($arrResponseTracker['payment_tracker_id'])) {
//                         $this->updateIncomePaymentTrackerStatus(array('soc_id' => $data['soc_id'], 'updated_by' => $data['user_id'], 'payment_tracker_id' => $arrResponseTracker['payment_tracker_id'], 'status' => 'N', 'transaction_status' => 'complete'));
//                     }
//                 }
// //                return $this->response->redirect($this->config->system->full_base_url . "credit-accounts/list/".$tab.'/?page='.$page);
// //                if($tab == 'member'){
//                 // return $this->response->redirect($this->config->system->full_base_url . "credit-accounts/memberAdvances");
// //                }else{
// //                    return $this->response->redirect($this->config->system->full_base_url . "credit-accounts/nonmemberAdvances");
// //                }
//             }
//         } else {

//             $nonmemberData = $this->tenantDB()->table('chsone_nonmember_master')
//                 ->where('nonmember_id', $data['account_id'])
//                 ->first();

//             $nonmemberData = $nonmemberData ? (array) $nonmemberData : [];

//             $data['nonmember_ledger_id'] = $nonmemberData['nonmember_ledger_id'];
//             //Transaction Started
//             // $this->soc_db_w = $this->di->getShared('soc_db_w');

//             //Payment Tracker

//             $arrgenerateInvoiceid = array("soc_id" => $data['soc_id']);

//             $data['receipt_number'] = $this->generate_receipt_id($arrgenerateInvoiceid);

//             $paymentToken = $data['payment_token'] = $this->generatePaymentToken(array('arrPaymentTracker' => $data));

//             $arrResponseTracker = $this->saveInvoicePaymentTracker(array('soc_id' => $data['soc_id'], 'unit_id' => $data['unit_id'], 'postData' => $data)); //get all Unit details

//             $data['payment_tracker_id'] = $arrResponseTracker['payment_tracker_id'];

//             $isTokenValid = $this->paymentTokenVerification(array('soc_id' => $data['soc_id'], 'paymentToken' => $paymentToken));
//             $creditData['credit_account_detail'] = $data; // to store credit account data
//             // $this->soc_edb_w->begin();

//             $intBookerLedgerDetails = null;
//             if ($isTokenValid && !empty($arrResponseTracker['payment_tracker_id'])) {
//                 $this->updateIncomePaymentTrackerStatus(array('soc_id' => $data['soc_id'], 'updated_by' => $data['user_id'], 'payment_tracker_id' => $arrResponseTracker['payment_tracker_id'], 'transaction_status' => 'in_process'));
//                 //In case of cheque or DD

//                 if (in_array(strtolower($data['payment_mode']), $this->constants['payment_mode_for_clearance'])) {

//                     if (!empty($arrResponseTracker)) {
//                         $succMsg = 'Payment has successfully added to payment tracker waiting for clearance.';
//                         $creditData['credit_account_detail']['buildingUnit'] = $data['account_name'];
//                         $creditData = $this->escpaeSingleQuotes($creditData['credit_account_detail']);
//                         $this->updateIncomePaymentTrackerStatus(array('soc_id' => $data['soc_id'], 'updated_by' => $data['user_id'], 'payment_tracker_id' => $arrResponseTracker['payment_tracker_id'], 'status' => 'P', 'transaction_status' => 'complete', 'other_information' => serialize($creditData)));
//                         // $this->soc_db_w->commit(); //commit all records
//                         // $this->view->setVar("id", $id);
//                         // $this->session->set('suc_msg', $succMsg);
// //                        return $this->response->redirect($this->config->system->full_base_url . "credit-accounts/list/".$tab.'/?page='.$page);
// //                        if($tab == 'member'){
// //                            return $this->response->redirect($this->config->system->full_base_url . "credit-accounts/memberAdvances");
// //                        }else{
//                         // return $this->response->redirect($this->config->system->full_base_url . "credit-accounts/nonmemberAdvances");
// //                        }
//                     }
//                 } else {
// //                    $creditData = array();
//                     if (!empty($arrResponseTracker)) {
//                         $arrResponse = $arrResponseTracker;
//                         if ($data['credit_used_type'] == 'refundable') {
//                             $arrToLedgerDetails = $this->checkledgerExistOrCreate(array("soc_id" => $data['soc_id'], 'ledger_name' => $data['account_id'] . '-' . $data['account_name']));
//                             //Insert transaction entry
//                             $data['income_account_ledger_id'] = $arrToLedgerDetails['recieving_ledger_id'];
//                             if ($data['payment_mode'] == 'cashtransfer') {$payment_mode = $this->constants['DISPLAY_CASH_TRANSFER'] ?? 'Electronic Fund Transfer';} else { $payment_mode = ucfirst($data['payment_mode']);}
//                             $narration = 'Refundable deposit recieved via ' . $payment_mode . ').';
//                             $intBookerLedgerDetails = $this->payNonmemberBillLedger(array("soc_id" => $data['soc_id'], 'postData' => $data, 'arrBookerLedgerDetails' => $arrToLedgerDetails, 'to_ledger' => $arrToLedgerDetails, 'narration' => $narration));
//                         } else {
// //                                        $arrBookerLedgerDetails = $this->incometrackerevent->incometracker('accountListener:checkledgerExistNew', array('auth' => $this->auth, 'ledger_name' => 'Nonmember Income', 'context' => SUNDRY_DEBTORS_GROUP));
//                             $arrBookerLedgerDetails['recieving_ledger_id'] = $nonmemberData['nonmember_ledger_id'];
//                             $arrBookerLedgerDetails['receiver_name'] = $data['account_name'];
//                             if ($data['payment_mode'] == 'cashtransfer') {$payment_mode = $this->constants['DISPLAY_CASH_TRANSFER'] ?? 'Electronic Fund Transfer';} else { $payment_mode = ucfirst($data['payment_mode']);}
//                             $narration = 'Advance payment received from ' . ucfirst($data['account_name']) . ' dated ' . $this->getDisplayDate($data['payment_date']) . ' through ' . $payment_mode;
//                             if ($data['payment_mode'] == 'cashtransfer') {
//                                 $narration = $narration . '  with payment ref. (' . (!empty($data['transaction_reference'])?$data['transaction_reference']:null) . ')';
//                             }
//                             //Insert transaction entry
//                             $intBookerLedgerDetails = $this->payNonmemberBillLedger(array("soc_id" => $data['soc_id'], 'postData' => $data, 'arrBookerLedgerDetails' => $arrBookerLedgerDetails, 'narration' => $narration));
//                         }
//                     }
//                 }
//             } else {
//                 // Transaction complete with out payment
//                 if (!empty($arrResponseTracker['payment_tracker_id'])) {
//                     $this->updateIncomePaymentTrackerStatus(array("soc_id" => $data['soc_id'], 'updated_by' => $data['user_id'], 'payment_tracker_id' => $arrResponseTracker['payment_tracker_id'], 'status' => 'N', 'transaction_status' => 'complete'));
//                 }
//                 // $this->soc_db_w->rollback(); //rollback all records
//                 // $this->view->setVar("id", $id);
//                 // $this->session->set("err_msg", 'Unable to complete transaction, Please try later.');
// //                return $this->response->redirect($this->config->system->full_base_url . "credit-accounts/list/".$tab.'/?page='.$page);
// //                if($tab == 'member'){
// //                    return $this->response->redirect($this->config->system->full_base_url . "credit-accounts/memberAdvances");
// //                }else{
//                 // return $this->response->redirect($this->config->system->full_base_url . "credit-accounts/nonmemberAdvances");
// //                }
//             }

//             //Non-member redirect after response
//             if ($data['bill_type'] == 'creditaccount-nonmember' && (!empty($intBookerLedgerDetails))) {
//                 $trackertStatus = 'Y';
//                 if (in_array(strtolower($data['payment_mode']), $this->constants['payment_mode_for_clearance'])) {
//                     $trackertStatus = 'P';
//                 }
//                 if (!empty($arrResponseTracker['payment_tracker_id'])) {
//                     $this->updateIncomePaymentTrackerStatus(array("soc_id" => $data['soc_id'], 'updated_by' => $data['user_id'], 'payment_tracker_id' => $arrResponseTracker['payment_tracker_id'], 'status' => $trackertStatus, 'transaction_status' => 'complete'));
//                     $data['payment_tracker_id'] = $arrResponseTracker['payment_tracker_id'];
//                 }

//                 $id = $this->addCreditAmountresponse($data);
//                 if ($id['error'] == true) {
// //                     $this->soc_db_w->rollback(); //rollback in case of failure
// //                     $this->session->set("err_msg", $id['data']['message'][0]);
// // //                    return $this->response->redirect($this->config->system->full_base_url . "credit-accounts/list/".$tab.'/?page='.$page);
// //                     if($tab == 'member'){
// //                         return $this->response->redirect($this->config->system->full_base_url . "credit-accounts/memberAdvances");
// //                     }else{
// //                         return $this->response->redirect($this->config->system->full_base_url . "credit-accounts/nonmemberAdvances");
// //                     }
//                 }
//                 if ($data['payment_mode'] == 'cashtransfer') {$payment_mode = $this->constants['DISPLAY_CASH_TRANSFER'] ?? 'Electronic Fund Transfer';} else { $payment_mode = ucfirst($data['payment_mode']);}
//                 $narration = 'Advances recieved of RS ' . $data['payment_amount'] . 'via ' . $payment_mode;
//                 /*$socUsers = $this->session->get("socUsers");
//                 $notification_title = 'Advanced recieved For '.ucwords($data['account_name']);
//                 $notification_data = ['title' => $notification_title,
//                 'desc' => $narration,
//                 'module' => 'chsone_credit_accounts',
//                 'scope' => 'group',
//                 'role' => 'admin',
//                 'pk_id' => $id['data'],
//                 'date_time' => date("d-M-Y H:i:s"),
//                 'member_id' => [],
//                 'user_id' => [],
//                 'username' => $this->auth['user_first_name'] . ' ' . $this->auth['user_last_name'],
//                 'created_by' => $this->auth['user_id'],
//                 'soc_id' => $this->auth['soc_id'],
//                 'to_be_notified' => 0,
//                 'all_staff' => [], // used as to send notification to all staff
//                 'all_admin' => $socUsers['socAdminUsers']]; // used as to send notification to all admin
//                 \ChsOne\Helper\CommonHelper::addNotification($notification_data);*/

//                 // $this->soc_db_w->commit();
//                 $successmsg = "Payment done succesfully";
//                 // $this->session->set("suc_msg", $successmsg);
// //                return $this->response->redirect($this->config->system->full_base_url . "credit-accounts/list/".$tab.'/?page='.$page);
//                 if ($tab == 'member') {
//                     // return $this->response->redirect($this->config->system->full_base_url . "credit-accounts/memberAdvances");
//                 } else {
//                     // return $this->response->redirect($this->config->system->full_base_url . "credit-accounts/nonmemberAdvances");
//                 }
//             } else {
//                 // $this->soc_db_w->rollback(); //rollback in case of failure
//                 // $this->session->set("err_msg", 'Unable to complete transaction, Please try later.');

//                 if (empty($arrResponse) || (!empty($arrResponse) && strtolower($arrResponse['status']) != 'success')) {
//                     if (!empty($arrResponseTracker['payment_tracker_id'])) {
//                         // $this->incometrackerevent->incometracker('MemberIncomeDetail:updateIncomePaymentTrackerStatus', array('soc_id' => $this->auth['soc_id'], 'updated_by' => $this->auth['user_id'], 'payment_tracker_id' => $arrResponseTracker['payment_tracker_id'], 'status' => 'N', 'transaction_status' => 'complete'));
//                     }
//                 }
// //                return $this->response->redirect($this->config->system->full_base_url . "credit-accounts/list/".$tab.'/?page='.$page);
//                 if ($tab == 'member') {
//                     // return $this->response->redirect($this->config->system->full_base_url . "credit-accounts/memberAdvances");
//                 } else {
//                     // return $this->response->redirect($this->config->system->full_base_url . "credit-accounts/nonmemberAdvances");
//                 }
//             }
//         }
//     }

    public function paymentTransaction($data, $id, $tab = '', $page = '')
    {
        $arrResponse = [];
        $creditData = ['credit_account_detail' => $data];

        // Determine Member or Non-Member Code

        if ($data['account_context'] === 'unit') {
            $arrDataListener = [
                'soc_id' => $data['soc_id'],
                'unit_id' => $data['unit_id'],
            ];

            $arrIncomeInvoiceMemberDetail = $this->getMemberDetail($arrDataListener);
            if (!empty($arrIncomeInvoiceMemberDetail)) {
                $arrDataListener['member_id'] = $arrIncomeInvoiceMemberDetail['id'];
            }

            // Generate Receipt and Payment Token
            $data['receipt_number'] = $this->generate_receipt_id(['soc_id' => $data['soc_id']]);
            $paymentToken = $data['payment_token'] = $this->generatePaymentToken(['arrPaymentTracker' => $data]);

            // Save Payment Tracker and validate token
            $arrResponseTracker = $this->saveInvoicePaymentTracker([
                'soc_id' => $data['soc_id'],
                'unit_id' => $data['unit_id'],
                'postData' => $data,
            ]);
            $data['payment_tracker_id'] = $arrResponseTracker['payment_tracker_id'];

            $isTokenValid = $this->paymentTokenVerification([
                'soc_id' => $data['soc_id'],
                'paymentToken' => $paymentToken,
            ]);

            if ($isTokenValid && !empty($arrResponseTracker['payment_tracker_id'])) {
                $this->updateIncomePaymentTrackerStatus([
                    'soc_id' => $data['soc_id'],
                    'updated_by' => $data['user_id'],
                    'payment_tracker_id' => $arrResponseTracker['payment_tracker_id'],
                    'transaction_status' => 'in_process',
                ]);

                if (in_array(strtolower($data['payment_mode']), $this->constants['payment_mode_for_clearance'])) {
                    // preg_match('#\((.*?)\)#', $data['search_account_name'], $match);
                    $buildingUnit = $data['search_account_name'];
                    $creditData['credit_account_detail']['buildingUnit'] = $buildingUnit;
                    $arrResponse = $arrResponseTracker;
                } else {
                    // preg_match('#\((.*?)\)#', $data['search_account_name'], $match);
                    $buildingUnit = $data['search_account_name'];

                    $paymentMode = $data['payment_mode'] === 'cashtransfer'
                        ? ($this->constants['DISPLAY_CASH_TRANSFER'] ?? 'Electronic Fund Transfer')
                        : ucfirst($data['payment_mode']);
                    $narration = $data['credit_used_type'] === 'refundable'
                        ? 'Refundable deposit received via ' . $paymentMode
                        : 'Adjustable advances received via ' . $paymentMode;

                    $arrToLedgerDetails = $this->checkledgerExistOrCreate([
                        "soc_id" => $data['soc_id'],
                        'ledger_name' => $buildingUnit,
                    ]);

                    $data['income_account_ledger_id'] = $arrToLedgerDetails['recieving_ledger_id'];
                    $arrResponse = $this->createMemberLedgerCredit([
                        "soc_id" => $data['soc_id'],
                        'unit_id' => $data['unit_id'],
                        'arrPostData' => $data,
                        'arrIncomeInvoiceMemberDetail' => $arrIncomeInvoiceMemberDetail,
                        'to_ledger' => $arrToLedgerDetails,
                        'narration' => $narration,
                    ]);
                    // return $arrResponse;

                }
            } else {
                if (!empty($arrResponseTracker['payment_tracker_id'])) {
                    $this->updateIncomePaymentTrackerStatus([
                        'soc_id' => $data['soc_id'],
                        'updated_by' => $data['user_id'],
                        'payment_tracker_id' => $arrResponseTracker['payment_tracker_id'],
                        'status' => 'N',
                        'transaction_status' => 'complete',
                    ]);
                }
            }

            if ($data['bill_type'] === 'creditaccount-member' && !empty($arrResponse)) {
                $trackertStatus = 'Y';
                if (in_array(strtolower($data['payment_mode']), $this->constants['payment_mode_for_clearance'])) {
                    $trackertStatus = 'P';
                    if (strtolower($data['payment_mode']) === 'cheque' && $this->getDatabaseDate($data['payment_date']) > $this->getCurrentDate('database')) {
                        $trackertStatus = 'R';
                    }
                }


                $this->updateIncomePaymentTrackerStatus([
                    'soc_id' => $data['soc_id'],
                    'updated_by' => $data['user_id'],
                    'payment_tracker_id' => $arrResponseTracker['payment_tracker_id'],
                    'status' => $trackertStatus,
                    'transaction_status' => 'complete',
                    'other_information' => serialize($creditData['credit_account_detail']),
                ]);
                $data['payment_tracker_id'] = $arrResponseTracker['payment_tracker_id'];

                $this->addCreditAmountresponse($data, $id);
            }
        } else {
            $nonmemberData = $this->tenantDB()->table('chsone_nonmember_master')
                ->where('nonmember_id', $data['account_id'])
                ->first();

            $nonmemberData = $nonmemberData ? (array)$nonmemberData : [];
            $data['nonmember_ledger_id'] = $nonmemberData['nonmember_ledger_id'];

            $data['receipt_number'] = $this->generate_receipt_id(['soc_id' => $data['soc_id']]);
            $paymentToken = $data['payment_token'] = $this->generatePaymentToken(['arrPaymentTracker' => $data]);

            $arrResponseTracker = $this->saveInvoicePaymentTracker([
                'soc_id' => $data['soc_id'],
                'unit_id' => $data['unit_id'],
                'postData' => $data,
            ]);
            $data['payment_tracker_id'] = $arrResponseTracker['payment_tracker_id'];

            $isTokenValid = $this->paymentTokenVerification([
                'soc_id' => $data['soc_id'],
                'paymentToken' => $paymentToken,
            ]);

            if ($isTokenValid) {
                $this->updateIncomePaymentTrackerStatus([
                    'soc_id' => $data['soc_id'],
                    'updated_by' => $data['user_id'],
                    'payment_tracker_id' => $arrResponseTracker['payment_tracker_id'],
                    'transaction_status' => 'in_process',
                ]);

                $arrBookerLedgerDetails = [
                    'recieving_ledger_id' => $nonmemberData['nonmember_ledger_id'],
                    'receiver_name' => $nonmemberData['first_name'].' '.$nonmemberData['last_name'],
                ];

                $data['account_name'] = $nonmemberData['first_name'].' '.$nonmemberData['last_name'];

                $paymentMode = $data['payment_mode'] === 'cashtransfer'
                    ? ($this->constants['DISPLAY_CASH_TRANSFER'] ?? 'Electronic Fund Transfer')
                    : ucfirst($data['payment_mode']);
                $narration = 'Advance payment received from ' . ucfirst($data['account_name']) . ' dated ' . $this->getDisplayDate($data['payment_date']) . ' through ' . $paymentMode;

                $this->payNonmemberBillLedger([
                    'soc_id' => $data['soc_id'],
                    'postData' => $data,
                    'arrBookerLedgerDetails' => $arrBookerLedgerDetails,
                    'narration' => $narration,
                ]);

                $this->addCreditAmountresponse($data, $id);
            } else {
                if (!empty($arrResponseTracker['payment_tracker_id'])) {
                    $this->updateIncomePaymentTrackerStatus([
                        'soc_id' => $data['soc_id'],
                        'updated_by' => $data['user_id'],
                        'payment_tracker_id' => $arrResponseTracker['payment_tracker_id'],
                        'status' => 'N',
                        'transaction_status' => 'complete',
                    ]);
                }
            }
        }
    }


    public function revisedBillRectification($data, $id)
    {
        $text = $data['search_account_name'];
        preg_match('#\((.*?)\)#', $text, $match); // To get Building name and unit number

        $addToCredit = false;
        $arrIncomeInvoiceDetail = array();
        $arrDataListener['soc_id'] = $data['soc_id'];
        $arrDataListener['unit_id'] = $data['unit_id'];
        $arrDataListener['show_cancelled_invoice'] = 1;
        $arrDataListener['searchData'] = array('invoice_number' => array($data['list_invoices']), 'apply' => 'apply');
        $arrIncomeInvoiceDetail = $this->getUnitInvoiceDetail('MemberIncomeDetail:getUnitInvoiceDetail', $arrDataListener); //get all Unit details
        $initialDueAmount = abs($arrIncomeInvoiceDetail['total_unpaid_invoice_amount']);
        $invoiceStatus = $arrIncomeInvoiceDetail['unit_invoice_detail'][0]['payment_status'];

        if ($data['bill_type'] == 'creditaccount-nonmember') {

            $nonmemberData = $this->tenantDB()->table('chsone_nonmember_master')->where('nonmember_id', $data['account_id'])->first();
            $nonmemberData = json_decode(json_encode($nonmemberData), true);

            $data['nonmember_ledger_id'] = $nonmemberData['nonmember_ledger_id'];

            $arrToLedgerDetails['recieving_ledger_id'] = $nonmemberData['nonmember_ledger_id'];
            $arrToLedgerDetails['receiver_name'] = $data['account_name'];

            $arrFromLedgerDetails = $this->checkledgerExistNew(array("soc_id" => $data['soc_id'], 'ledger_id' => $data['income_account_ledger_id']));
            $narration = 'Invoice Rectification for ' . $data['narration'];
            $intBookerLedgerDetails = $this->payNonmemberBillLedger(array("soc_id" => $data['soc_id'], 'postData' => $data, 'arrBookerLedgerDetails' => $arrToLedgerDetails, 'to_ledger' => $arrToLedgerDetails, 'from_ledger' => $arrFromLedgerDetails, 'narration' => $narration));
            if (!$intBookerLedgerDetails) {
                $this->status = 'error';
                $this->statusCode = 400;
                $this->message = 'Unable to Complete Transaction.';
                return;
            }
            $this->status = 'success';
            $this->statusCode = 200;
            $this->message = ucwords($match[1]) . ' ' . VENDORBILLPAYMENT_SUCC_MSG;
            return;
        } else {
            $selectRuleIds = isset($selectRuleIds) ? $selectRuleIds : [];
            $selectRuleIdsStr = implode(',', $selectRuleIds);
            $rules = IncomeInvoiceRule::select('id','income_account_id')->whereIn('id', $selectRuleIdsStr)->get()->toArray();
            $totalRectifyAmount = (float)0;
            if (count($this->finalTaxes) > 0) {
                foreach ($this->finalParticulars as $key => $particular) {
                    $totalRectifyAmount = (float)$totalRectifyAmount + (float)$particular['amount'];
                    $response = $this->saveUnitwiseParticulars(array('data' => $particular));
                    if ($response['error'] == true) {
                        $this->status = 'error';
                        $this->statusCode = 400;
                        $this->message = 'Unable to saved particulars.';
                        return;
                    }
                    $this->finalTaxes[$key]['particular_id'] = $response['data'];
                    $totalRectifyAmount = (float)$totalRectifyAmount + (float)$this->finalTaxes[$key]['tax_amount'];
                    $response = $this->saveTaxLog(array('data' => $this->finalTaxes[$key]));
                    if ($response['error'] == true) {
                        $this->status = 'error';
                        $this->statusCode = 400;
                        $this->message = 'Unable to saved particulars.';
                        return;
                    }
                }
                if ($invoiceStatus == 'partialpaid') {
                    $remaing = (float)abs((float)$initialDueAmount + (float)$totalRectifyAmount);
                    $addToCredit = true;
                    $data['payment_amount'] = abs((float)$arrIncomeInvoiceDetail['total_unpaid_invoice_amount'] - (float)$totalRectifyAmount);
                    if ($remaing >= 0) {
                        $addToCredit = false;
                        $data['payment_amount'] = abs((float)$initialDueAmount + (float)$totalRectifyAmount);
                    } else {
                        //Update Invoice status to paid
                        $objUnitInvoice = IncomeUnitInvoice::where('invoice_number', $data['list_invoices'])->first();
                        $objUnitInvoice->payment_status = 'paid';
                        $objUnitInvoice->save();
                    }
                } elseif ($invoiceStatus == 'paid') {
                    $addToCredit = true;
                    $data['payment_amount'] = abs((float)$initialDueAmount + (float)$totalRectifyAmount);
                }
            } else {
                $totalRectifyAmount = (float)0;
                $arrDataListener['soc_id'] = $data['soc_id'];
                $arrDataListener['unit_id'] = $data['unit_id'];
                $arrIncomeInvoiceMemberDetail = $this->getMemberDetail($arrDataListener); //get all Unit details
                if (!empty($arrIncomeInvoiceMemberDetail)) {
                    $arrDataListener['member_id'] = $arrIncomeInvoiceMemberDetail['id'];
                }
                $narration = 'Invoice Rectification for ' . $data['narration'];
                foreach ($this->finalParticulars as $key => $particular) {
                    $totalRectifyAmount = (float)$totalRectifyAmount + (float)$particular['amount'];
                    foreach ($rules as $rule) {
                        if ($rule['id'] == $particular['actual_rule_id']) {
                            $data['payment_amount'] = abs($particular['particular_paid_amount']);
                            $ledger = IncomeAccount::where('account_id', $rule['income_account_id'])->get()->toArray();
                            $arrFromLedgerDetails = $this->checkledgerExistNew(array("soc_id" => $data['soc_id'], 'ledger_id' => $ledger['fk_income_ledger_id']));
                            $arrResponse = $this->createMemberLedgerCredit(array("soc_id" => $data['soc_id'], 'unit_id' => $data['unit_id'], 'arrPostData' => $data, 'arrIncomeInvoiceMemberDetail' => $arrIncomeInvoiceMemberDetail, 'from_ledger' => $arrFromLedgerDetails, 'narration' => $narration));
                            if ($arrResponse['status'] !== 'success') {
                                $this->status = 'error';
                                $this->statusCode = 400;
                                $this->message = 'Unable to Complete Transaction.';
                                return;
                            }
                            $data['payment_amount'] = 0;
                        }
                    }
                    unset($this->finalParticulars[$key]['actual_rule_id']);
                }
                /** Batch Insert * */
                $invoiceParticulars = new IncomeInvoiceParticular();
                $columns = array('soc_id', 'invoice_number', 'fk_unit_invoice_id', 'fk_unit_id', 'fk_rule_id', 'bill_type', 'particular', 'amount', 'tax_applicable', 'tax_exemptions', 'is_particular_paid', 'particular_paid_amount', 'booking_ids', 'created_date', 'created_by');
                $invoiceParticulars->columns = $columns;
                $invoiceParticulars->data = $this->finalParticulars;
                $result = $invoiceParticulars->save();
                if (!$result) {
                    $this->status = 'error';
                    $this->statusCode = 400;
                    $this->message = 'Unable to Complete Particular Transaction.';
                    return;
                }

                if ($invoiceStatus == 'partialpaid') {
                    $remaing = (float)abs((float)$initialDueAmount + (float)$totalRectifyAmount);
                    $addToCredit = true;
                    $data['payment_amount'] = abs((float)$initialDueAmount + (float)$totalRectifyAmount);
                    if ($remaing >= 0) {
                        $addToCredit = false;
                        $data['payment_amount'] = abs((float)$initialDueAmount + (float)$totalRectifyAmount);
                    } else {
                        //Update Invoice status to paid
                        $objUnitInvoice = IncomeUnitInvoice::where('invoice_number', $data['list_invoices']);
                        $objUnitInvoice->payment_status = 'paid';
                        $objUnitInvoice->save();
                        die('true');
                    }
                } elseif ($invoiceStatus == 'paid') {
                    $addToCredit = true;
                    $data['payment_amount'] = abs((float)$initialDueAmount + (float)$totalRectifyAmount);
                }

                $arrIncomeInvoiceDetail = array();
                $arrDataListener['soc_id'] = $data['soc_id'];
                $arrDataListener['unit_id'] = $data['unit_id'];
                $arrDataListener['show_cancelled_invoice'] = 1;
                $arrDataListener['searchData'] = array('invoice_number' => array($data['list_invoices']), 'apply' => 'apply');
                $arrIncomeInvoiceDetail = $this->getUnitInvoiceDetail($arrDataListener); //get all Unit details
                if ((float)$arrIncomeInvoiceDetail['total_unpaid_invoice_amount'] > 0) {
                    $this->status = 'success';
                    $this->statusCode = 200;
                    $this->message = 'Invoice Rectify For ' . $data['list_invoices'] . ' without credit note added';
                    return;
                }

                $addToCredit = true;
                $data['payment_amount'] = abs((float)$arrIncomeInvoiceDetail['total_unpaid_invoice_amount'] - (float)$totalRectifyAmount);
            }
            $this->status = 'success';
            $this->statusCode = 200;
            $this->message = ucwords($match[1]) . ' ' . VENDORBILLPAYMENT_SUCC_MSG;
            return;
        }
        if ($addToCredit) {
            $id = $this->addCreditAmountresponse($data, $id);
            if ($id['error'] == true) {
                $this->status = 'error';
                $this->statusCode = 400;
                $this->message = $id['data']['message'][0];
                return;
            }
            $this->status = 'success';
            $this->statusCode = 200;
            $this->message = 'Invoice Rectify For ' . $data['list_invoices'] . ' with credit note added';
            return;
        } else {
            $this->status = 'success';
            $this->statusCode = 200;
            $this->message = 'Invoice Rectify For ' . $data['list_invoices'] . ' without credit note added';
            return;
        }
    }

    public function saveUnitwiseParticulars($data = [])
    {
        $logger = $data['logger'] ?? null;
        unset($data['logger']);

        $response = [
            'error' => [],
            'success' => []
        ];

        if (isset($data['insertData']) && !empty($data['insertData'])) {
            foreach ($data['insertData'] as $insertData) {
                // Find existing record or create a new one
                $invoiceParticular = IncomeInvoiceParticular::where('soc_id', $this->input['company_id'])
                    ->where('fk_unit_id', $data['unit_id'])
                    ->where('fk_unit_invoice_id', $data['unit_invoice_id'])
                    ->where('fk_rule_id', $insertData['rule_id'])
                    ->where('particular', $insertData['particular'])
                    ->first();

                if (!$invoiceParticular) {
                    $invoiceParticular = new IncomeInvoiceParticular();
                    $invoiceParticular->created_date = date('Y-m-d');
                    $invoiceParticular->created_by = 0;
                }

                $invoiceParticular->soc_id = $insertData['soc_id'] ?? '';
                $invoiceParticular->fk_unit_id = $insertData['unit_id'] ?? '';
                $invoiceParticular->fk_unit_invoice_id = $data['unit_invoice_id'] ?? '';
                $invoiceParticular->fk_rule_id = $insertData['rule_id'] ?? '';
                $invoiceParticular->invoice_number = $insertData['invoice_number'];
                $invoiceParticular->particular = $insertData['particular'];
                $invoiceParticular->amount = $insertData['amount'];
                $invoiceParticular->bill_type = $insertData['bill_type'] ?? 'maintenance 3443';
                $invoiceParticular->tax_applicable = $insertData['tax_applicable'];
                $invoiceParticular->tax_exemptions = $insertData['tax_exemptions'];
                $invoiceParticular->is_particular_paid = $insertData['is_particular_paid'];
                $invoiceParticular->particular_paid_amount = $insertData['particular_paid_amount'] ?? 0;
                $invoiceParticular->booking_ids = $insertData['booking_ids'];
                $invoiceParticular->updated_date = date('Y-m-d');
                $invoiceParticular->updated_by = 0;

                if ($data['trial'] === 'live') {

                    if (!$invoiceParticular->save()) {
                        $messages = $invoiceParticular->errors()->all();
                        $loggerMsg = '|' . implode('|', $messages) . '|';
                        $response['error'][] = [
                            'particular' => $invoiceParticular->particular,
                            'message' => $loggerMsg
                        ];
                    } else {
                        $response['success'][] = [
                            'particular' => $invoiceParticular->particular,
                            'particular_id' => $invoiceParticular->id
                        ];
                    }
                } else {
                    $response['success'][] = [
                        'particular' => $invoiceParticular->particular,
                        'particular_id' => $invoiceParticular->id
                    ];
                }
            }
            unset($invoiceParticular);
            unset($logger);
        }

        return $response;
    }

    public function saveTaxLog($data = array())
    {
        try {
            if (!empty($data['arrTaxLogDetail'])) {
                foreach ($data['arrTaxLogDetail'] as $eachTaxLog) {
                    if (!empty($eachTaxLog['tax_detail']) && !empty($eachTaxLog['tax_exemption_detail'])) {
                        $eachTaxLog['tax_detail'] = array_merge($eachTaxLog['tax_detail'], $eachTaxLog['tax_exemption_detail']);
                    } else if (!empty($eachTaxLog['tax_exemption_detail'])) {
                        $eachTaxLog['tax_detail'] = $eachTaxLog['tax_exemption_detail'];
                    }
                    if (empty($eachTaxLog['tax_detail'])) {
                        $eachTaxLog['tax_detail'][] = [
                            'particular_amount' => 0,
                            'tax_class' => null,
                            'tax_class_id' => null,
                            'tax_rule' => null,
                            'tax_rule_id' => null,
                            'tax_rate' => 0.00,
                            'tax_rate_type' => null,
                            'tax_amount' => 0.00
                        ];
                    }

                    foreach ($eachTaxLog['tax_detail'] as $eachTaxLogDetail) {
                        $objTaxLog = new ChsoneTaxLog();
                        $objTaxLog->created_date = date('Y-m-d'); //$this->getCurrentDate('database');
                        $objTaxLog->soc_id = $eachTaxLog['soc_id'];
                        $objTaxLog->invoice_number = $eachTaxLog['invoice_number'];
                        $objTaxLog->particular = $eachTaxLog['particular'];
                        $objTaxLog->particular_id = $eachTaxLog['particular_id'];

                        $objTaxLog->particular_amount = $eachTaxLogDetail['particular_amount'];
                        $objTaxLog->tax_class = $eachTaxLogDetail['tax_class'];
                        $objTaxLog->tax_class_id = $eachTaxLogDetail['tax_class_id'];
                        $objTaxLog->tax_rule = $eachTaxLogDetail['tax_rule'];
                        $objTaxLog->tax_rule_id = $eachTaxLogDetail['tax_rule_id'];
                        $objTaxLog->tax_rate = $eachTaxLogDetail['tax_rate'];
                        $objTaxLog->tax_rate_type = $eachTaxLogDetail['tax_rate_type'];
                        $objTaxLog->tax_amount = $eachTaxLogDetail['tax_amount'];

                        if (!$objTaxLog->save()) {
                            //                        //print_r($objTaxLog->getMessages() );exit();
                            $singleMsg = '|';
                            foreach ($objTaxLog->getMessages() as $messages) {
                                $arrMessages[] = (string)$messages;
                                $singleMsg .= (string)$messages . '|';
                            }
                            $arrResponse['error'][] = array('invoice_number' => $objTaxLog->invoice_number, 'message' => $singleMsg);
                            return $arrResponse;
                        } else {

                            $arrResponse['success'][] = array('invoice_number' => $objTaxLog->invoice_number);
                            //                        //print_r($arrResponse);exit();
                        }
                    }
                }
            }
            return $arrResponse;
        }catch (\Throwable $e) {
                dd([
                    'message' => $e->getMessage(),
                    'file' => $e->getFile(),
                    'line' => $e->getLine()
                ]);
            }
    }

    public function billRectification($data, $id)
    {
        $text = $data['search_account_name'];
        preg_match('#\((.*?)\)#', $text, $match); // To get Building name and unit number

        if ($data['bill_type'] == 'creditaccount-nonmember') {
            $nonmemberData = ChsoneNonmemberMaster::where("nonmember_id", $data['account_id'])->get()->toArray();
            $data['nonmember_ledger_id'] = $nonmemberData['nonmember_ledger_id'];

            $arrToLedgerDetails['recieving_ledger_id'] = $nonmemberData['nonmember_ledger_id'];
            $arrToLedgerDetails['receiver_name'] = $data['account_name'];
            $arrFromLedgerDetails = $this->checkledgerExistNew(array("soc_id" => $data['soc_id'], 'ledger_id' => $data['income_account_ledger_id']));
            $narration = 'Invoice Rectification for ' . $data['narration'];
            $intBookerLedgerDetails = $this->payNonmemberBillLedger(array("soc_id" => $data['soc_id'], 'postData' => $data, 'arrBookerLedgerDetails' => $arrToLedgerDetails, 'to_ledger' => $arrToLedgerDetails, 'from_ledger' => $arrFromLedgerDetails, 'narration' => $narration));
            if (!$intBookerLedgerDetails) {
                $this->status = 'error';
                $this->statusCode = 400;
                $this->message = 'Unable to Complete Transaction.';
                return;

            }
            $this->status = 'success';
            $this->statusCode = 200;
            $this->message = ucwords($match[1]) . ' ' . VENDORBILLPAYMENT_SUCC_MSG;
            return;
        } else {
            $arrDataListener['soc_id'] = $this->data['soc_id'];
            $arrDataListener['unit_id'] = $data['unit_id'];
            $arrIncomeInvoiceMemberDetail = $this->getMemberDetail($arrDataListener); //get all Unit details
            if (!empty($arrIncomeInvoiceMemberDetail)) {
                $arrDataListener['member_id'] = $arrIncomeInvoiceMemberDetail['id'];
            }
            $narration = 'Invoice Rectification for ' . $data['narration'];
            $arrFromLedgerDetails = $this->checkledgerExistNew(array("soc_id" => $data['soc_id'], 'ledger_id' => $data['income_account_ledger_id']));
            $arrResponse = $this->createMemberLedgerCredit(array("soc_id" => $data['soc_id'], 'unit_id' => $data['unit_id'], 'arrPostData' => $data, 'arrIncomeInvoiceMemberDetail' => $arrIncomeInvoiceMemberDetail, 'from_ledger' => $arrFromLedgerDetails, 'narration' => $narration));
            if ($arrResponse['status'] !== 'success') {
                $this->status = 'error';
                $this->statusCode = 400;
                $this->message = 'Unable to Complete Transaction.';
                return;
                // $this->session->set("err_msg", 'Unable to Complete Transaction.');
                // return $this->response->redirect($this->config->system->full_base_url . "credit-accounts/memberAdvances");
            }
            $this->status = 'success';
            $this->statusCode = 200;
            $this->message = ucwords($match[1]) . ' ' . VENDORBILLPAYMENT_SUCC_MSG;
            return;
        }
        $id = $this->addCreditAmountresponse($data, $id);
        if ($id['error'] == true) {
            // $this->soc_db_w->rollback(); //rollback in case of failure
            // $this->session->set("err_msg", $id['data']['message'][0]);
            // return $this->response->redirect($this->config->system->full_base_url . "credit-accounts/memberAdvances");
        }
        // $this->soc_db_w->commit(); //commit all records
        // $this->view->setVar("id", $id);
        // $this->session->set('suc_msg', $succMsg);
        // return $this->response->redirect($this->config->system->full_base_url . "credit-accounts/memberAdvances");
    }

    public function getMemberDetail($data = [])
    {
        $arrMemberMaster = [];

        // Use parameterized queries to avoid SQL injection
        $memberType = $this->tenantDB()->table('chsone_member_type_master')
            ->where('soc_id', $data['soc_id'])
            ->where('member_type_name', 'Primary')
            ->first();

        if (!$memberType) {
            // Log or handle the case where no member type is found
            // \Log::error('Member type "Primary" not found for soc_id: ' . $data['soc_id']);
            // return $arrMemberMaster;
            $this->message = 'Member type "Primary" not found for soc_id: ' . $data['soc_id'];
            $this->status = 400;
        }

        // Fetch the member master details using the member type ID and unit ID
        $objMemberMaster = $this->tenantDB()->table('chsone_members_master')
            ->where('soc_id', $data['soc_id'])
            ->where('fk_unit_id', $data['unit_id'])
            ->where('status', 1)
            ->where('member_type_id', $memberType->member_type_id)
            ->first();
        $objMemberMaster = json_decode(json_encode($objMemberMaster), true);

        return $objMemberMaster;
    }

    public function generate_receipt_id($data = [])
    {
        $soc_id = $data['soc_id'];
        $currentDate = date('Y-m-d H:i:s');

        // Prepare listener data for fetching applicable settings
        $arrListenerdataForApplicableTaxes = [
            'soc_id' => $soc_id,
            'effective_date' => $currentDate,
        ];

        // Fetch general settings for income receipt prefix and related data
        $arrGeneralSettings = $this->getAllSettings(
            $arrListenerdataForApplicableTaxes
        );

        // Default receipt number
        $receipt_number = str_pad("1", 5, "0", STR_PAD_LEFT);

        // Generate receipt number if the prefix is set in general settings
        if (isset($arrGeneralSettings['INCOME_RECEIPT_PREFIX'])) {
            $data['prefix'] = $arrGeneralSettings['INCOME_RECEIPT_PREFIX'];
            $getMaxReceiptID = $this->getMaxReceiptID($data);

            // Calculate the next receipt number
            if ($getMaxReceiptID) {
                $maxReceiptNumber = ltrim(ltrim($getMaxReceiptID, $arrGeneralSettings['INCOME_RECEIPT_PREFIX']), "0");
                $receiptnum = (int)$maxReceiptNumber + (int)$arrGeneralSettings['INCOME_RECEIPT_NUMBER_INCREMENT'];
            } else {
                $receiptnum = (int)$arrGeneralSettings['INCOME_RECEIPT_START_NUMBER'];
            }

            // Format the receipt number with padding
            $paddingNum = str_pad($receiptnum, 5, "0", STR_PAD_LEFT);
            $receipt_number = $arrGeneralSettings['INCOME_RECEIPT_PREFIX'] . $paddingNum;
        }

        return $receipt_number;
    }

    public function getMaxReceiptID($data = [])
    {
        // Validate required parameters
        if (empty($data['soc_id']) || empty($data['prefix'])) {
            return false;
        }

        // Use $this->tenantDB() to connect to the database and fetch the maximum receipt number
        $result = $this->tenantDB()->table('income_invoice_payment_tracker')
            ->where('soc_id', $data['soc_id'])
            ->whereRaw('receipt_number REGEXP ?', [$data['prefix'] . '+[0-9]'])
            ->max('receipt_number');

        // Check if a result was returned and fetch the receipt number
        if (!empty($result)) {
            return $result;
        }

        return false;
    }

    public function generatePaymentToken($data = [])
    {
        // Initialize an empty payment token
        $paymentToken = '';

        // Check if payment tracker data is provided
        if (!empty($data['arrPaymentTracker'])) {
            // Extract payment details from the provided data
            $arrPaymentDetail = [
                'soc_id' => $data['arrPaymentTracker']['soc_id'],
                'received_from' => $data['arrPaymentTracker']['received_from'] ?? '',
                'bill_type' => $data['arrPaymentTracker']['bill_type'] ?? '',
                'payment_amount' => $data['arrPaymentTracker']['payment_amount'],
                'payment_mode' => $data['arrPaymentTracker']['payment_mode'] ?? '',
                'invoice_number' => $data['arrPaymentTracker']['invoice_number'] ?? '',
                'payment_date' => $data['arrPaymentTracker']['payment_date'],
                'vendor_id' => $data['arrPaymentTracker']['vendor_id'] ?? '',
                'vendor_bill_payment_amount' => $data['arrPaymentTracker']['vendor_bill_payment_amount'] ?? '',
                'vendor_bill_type_purchase' => $data['arrPaymentTracker']['vendor_bill_type_purchase'] ?? '',
                'vendor_bill_receipt_number' => $data['arrPaymentTracker']['vendor_bill_receipt_number'] ?? '',
                'vendor_bill_payment_date' => $data['arrPaymentTracker']['vendor_bill_payment_date'] ?? '',
            ];

            // Generate a unique payment token using the serialized payment details
            if (!empty($arrPaymentDetail)) {
                $paymentToken = md5(time() . serialize($arrPaymentDetail));
            }
        }

        // Return the generated payment token
        return $paymentToken;
    }

    public function saveInvoicePaymentTracker($data = array())
    {

        if (isset($data) && !empty($data['soc_id']) && !empty($data['postData'])) {
            $arrOtherInfo = array();
            if (isset($data['postData']) && !empty($data['postData'])) {

                // print_r($data['postData']);
                if (strtolower($data['postData']['bill_type']) == 'member') {
                    $arrOtherInfo["member_detail"]["bank_ledger"] = $data['postData']['bank_account'];
                    if (!empty($data['postData']['writeoff_amount']) && $data['postData']['writeoff_amount'] > 0) {
                        if (!empty($data['postData']['total_particular'])) {
                            for ($i = 1; $i <= $data['postData']['total_particular']; $i++) {
                                $arrOtherInfo["writeoff_detail"]["rule_$i"] = $data["postData"]["rule_$i"];
                                $arrOtherInfo["writeoff_detail"]["particular_$i"] = $data["postData"]["particular_$i"];
                                $arrOtherInfo["writeoff_detail"]["writeoff_amount_$i"] = $data["postData"]["writeoff_amount_$i"];
                            }
                            $arrOtherInfo["writeoff_detail"]["total_particular"] = $data['postData']['total_particular'];
                        }
                    }
                } elseif (strtolower($data['postData']['bill_type']) == 'common_bill') {
                    $arrOtherInfo['common_bill_detail']['common_bill_id'] = $data['postData']['common_bill_id'];
                    $arrOtherInfo['common_bill_detail']['bank_ledger'] = $data['postData']['bank_account'];
                    $arrOtherInfo['common_bill_detail']['bill_type_category'] = $data['postData']['bill_type_category'];
                    $arrOtherInfo['common_bill_detail']['payment_type'] = $data['postData']['payment_type'];
                } elseif (strtolower($data['postData']['bill_type']) == 'nonmember') {
                    $arrOtherInfo['nonmember_detail']['nonmember_bill_id'] = $data['postData']['nonmember_bill_id'] ?? '';
                    $arrOtherInfo['nonmember_detail']['nonmemberincomeaccount'] = $data['postData']['nonmemberincomeaccount'] ?? '';
                    $arrOtherInfo['nonmember_detail']['narration'] = $data['postData']['narration'] ?? '';
                    $arrOtherInfo['nonmember_detail']['bank_ledger'] = $data['postData']['bank_account'] ?? $data['postData']['payer_bank_details'] ?? '';
                    if (isset($data['postData']['advance_payment']) && !empty($data['postData']['advance_payment'])) {
                        $arrOtherInfo['nonmember_detail']['advance_payment'] = $data['postData']['advance_payment'];
                    }
                } elseif (strtolower($data['postData']['bill_type']) == 'suspense') {
                    $arrOtherInfo['suspense_detail']['bank_ledger'] = $data['postData']['bank_account'];
                }
            }

            // Initialize the object properties directly in an array
            $objIncomeInvoicePaymentTracker = [
                'created_date' => $this->getCurrentDate('database'),
                'created_by' => $data['postData']['user_id'] ?? 0,
                'soc_id' => $data['soc_id'] ?? '',
                'unit_id' => $data['unit_id'] ?? '',
                'invoice_number' => $data['postData']['member_paid_invoice'] ?? '',
                'receipt_number' => $data['postData']['receipt_number'],
                'bill_type' => $data['postData']['bill_type'] ?? '',
                'payment_mode' => trim($data['postData']['payment_mode']),
                'transaction_reference' => $data['postData']['transaction_reference'] ?? null,
                'payment_instrument' => $data['postData']['payment_instrument'] ?? '',
                'received_from' => $data['postData']['received_from'],
                'transaction_charges' => $data['postData']['transaction_charges'] ?? 0.000,
                'total_due_amount' => $data['postData']['total_unpaid_amount'] ?? 0.000,
                'late_payment_charges' => $data['postData']['late_payment_charges'] ?? 0.000,
                'writeoff_amount' => $data['postData']['writeoff_amount'] ?? 0.000,
                'payment_amount' => $data['postData']['payment_amount'],
                'other_information' => serialize($arrOtherInfo),
                'tds_deducted' => $data['postData']['tds_amount'] ?? 0.000,
                'status' => 'N', // Default status
                'updated_date' => $this->getCurrentDate('database'),
                'updated_by' => 0,
                // 'payment_date' => $this->getFormattedDate($data['postData']['payment_date']),
                'payment_date' => $data['postData']['payment_date'],
                'payment_note' => $data['postData']['payment_note'] ?? '',
                // 'cheque_date' => $this->getFormattedDate($data['postData']['cheque_date']),
                'cheque_date' => $data['postData']['cheque_date'] ?? '',
                'attachment_name' => $data['postData']['attachment_name'] ?? '',
                'payment_token' => trim($data['postData']['payment_token'] ?? ''),
            ];

            // Check and update status for facility advance payment
            if (
                isset($data['postData']['booked_by']) &&
                !empty($data['postData']['booked_by']) &&
                isset($data['postData']['bill_from']) &&
                strtolower($data['postData']['bill_from']) == 'facility'
            ) {
                $objIncomeInvoicePaymentTracker['transaction_status'] = 'complete';
                $objIncomeInvoicePaymentTracker['status'] = in_array(strtolower($data['postData']['payment_mode']), $this->constants['payment_mode_for_clearance']) ? 'P' : 'Y';
            }

            // Insert into the database
            $objIncomeInvoicePaymentTrackerData = $this->tenantDB()->table('income_invoice_payment_tracker')->insertGetId($objIncomeInvoicePaymentTracker);

            $arrResponse['payment_tracker_id'] = $objIncomeInvoicePaymentTrackerData;

            unset($objIncomeInvoicePaymentTracker);
        }
        return $arrResponse;

    }

    public function formatData($data)
    {

        $memberData = $this->getMember($this->input['account_context'], $this->input['account_id_member']);
        $memberData = json_decode(json_encode($memberData), true);
        $data['adjustable_date'] = (!empty($data['adjustable_date']) && $data['adjustable_date'] !== null) ? $this->getDatabaseDate($data['adjustable_date']) : null;
        $data['transaction_type'] = 'cr';
        $data['created_name'] = $data['account_name'] ?? $memberData['title'];
        $data['received_from'] = $data['account_name'] ?? $memberData['title'];
        $data['bill_type'] = ($data['account_context'] == 'unit') ? 'creditaccount-member' : 'creditaccount-nonmember';
        $data['transaction_type'] = 'cr';
        $data['context'] = 'user';
        $data['is_locked'] = $data['credit_used_type'] == 'refundable' ? 1 : 0;
        $data['unit_id'] = $data['account_context'] == 'unit' ? $data['account_id'] : 0;
        $data['is_invoice_rectification'] = isset($data['is_invoice_rectification']) ? $data['is_invoice_rectification'] : 0;
        $data['income_account_ledger_id'] = ($data['credit_used_type'] == 'adjustable' && $data['is_invoice_rectification'] == '1') ? $data['account_context'] == 'unit' ? $data['income_account'] : $data['nonmember_income_account'] : null;

        return $data;
    }

    public function paymentTokenVerification($data = array())
    {
        if (!empty($data['paymentToken'])) {
            $arrPaymentTrackerDetail = $this->getPaymentTrackerDetailByPaymentToken($data);
            if (!empty($arrPaymentTrackerDetail)) {
                if (in_array(strtolower($arrPaymentTrackerDetail['transaction_status']), array('in_process')) || in_array(strtolower($arrPaymentTrackerDetail['status']), array('y'))) {
                    return false;
                }
            } else {
                return false;
            }
        } else {
            return false;
        }
        return true;
    }

    public function getPaymentTrackerDetailByPaymentToken($data)
    {

        $arrIncomeInvoicePaymentTracker = array();
        if (!empty($data['paymentToken'])) {

            $objIncomeInvoicePaymentTracker = $this->tenantDB()->table('income_invoice_payment_tracker')
                ->where('soc_id', $data['soc_id'])
                ->where('payment_token', $data['paymentToken'])
                ->first();

            $objIncomeInvoicePaymentTracker = json_decode(json_encode($objIncomeInvoicePaymentTracker), true);

            if (!empty($data['type']) && $data['type'] == 'expense') {

                $objIncomeInvoicePaymentTracker = $this->tenantDB()->table('expense_invoice_payment_tracker')
                    ->where('soc_id', $data['soc_id'])
                    ->where('payment_token', $data['paymentToken'])
                    ->first();

            }
            if (!empty($objIncomeInvoicePaymentTracker)) {

                $arrIncomeInvoicePaymentTracker = $objIncomeInvoicePaymentTracker;
            }
        }

        return $arrIncomeInvoicePaymentTracker;
    }

    public function updateIncomePaymentTrackerStatus($data = array())
    {
        $arrResponse['status'] = 'error';
        $data['updated_date'] = $this->getCurrentDate('database');
        $updateResponse = $this->updateIncomeInvoicePaymentTrackerStatus($data);
        if (!empty($updateResponse) && strtolower($updateResponse['status']) == 'success') {
            $arrResponse['status'] = 'success';
        }
        return $arrResponse;
    }

    public function updateIncomeInvoicePaymentTrackerStatus($data)
    {
        $arrResponse = ['status' => 'success', 'message' => []];

        // Building the update data array
        $updateData = [];

        if (!empty($data['updated_date'])) {
            $updateData['updated_date'] = $data['updated_date'];
        }
        if (!empty($data['status'])) {
            $updateData['status'] = $data['status'];
        }

        if (!empty($data['transaction_status'])) {
            $updateData['transaction_status'] = $data['transaction_status'];
        }
        if (!empty($data['invoice_number'])) {
            $updateData['invoice_number'] = $data['invoice_number'];
        }
        if (!empty($data['payment_date'])) {
            $updateData['payment_date'] = $data['payment_date'];
        }
        if (!empty($data['other_information'])) {
            $updateData['other_information'] = $data['other_information'];
        }
        if (!empty($data['reversal_note'])) {
            $updateData['reversal_note'] = $data['reversal_note'];
        }
        if (!empty($data['unit_id'])) {
            $updateData['unit_id'] = $data['unit_id'];
        }
        if (!empty($data['bill_type'])) {
            $updateData['bill_type'] = $data['bill_type'];
        }
        if (!empty($data['total_unpaid_amount'])) {
            $updateData['total_due_amount'] = $data['total_unpaid_amount'];
        }

        try {
            // Update the record in the database
            $result = $this->tenantDB()->table('income_invoice_payment_tracker')
                ->where('soc_id', $data['soc_id'])
                ->where('id', $data['payment_tracker_id'])
                ->update($updateData);

            if ($result === 0) {
                $arrResponse = ['status' => 'error', 'message' => ['No rows updated']];
            }
        } catch (\Exception $e) {
            $arrResponse = ['status' => 'error', 'message' => [$e->getMessage()]];
        }

        return $arrResponse;
    }

    public function checkledgerExistOrCreate($data = [])
    {
        $arrClientLedgerDetails = [];

        $ledgerName = $data['ledger_name'];

        // An outsider has booked for the society. Check if exists by name; otherwise, create new.
        $objBookerLedger = $this->tenantDB()->table('chsone_grp_ledger_tree')
            ->where('soc_id', $data['soc_id'])
            ->where('context', 'security deposit')
            ->where('entity_type', $this->constants['ENTITY_TYPE_LEDGER'])
            ->where('ledger_account_name', $ledgerName)
            ->first();

        $objBookerLedger = json_decode(json_encode($objBookerLedger), true);

        if (!empty($objBookerLedger)) {
            $arrClientLedgerDetails['recieving_ledger_id'] = $objBookerLedger['ledger_account_id'];
            $arrClientLedgerDetails['receiver_name'] = $objBookerLedger['ledger_account_name'];
        } else {
            // Ledger for the outsider does not exist. Create a ledger entry based on sundry debtors group.
            $objSecurityDepositGroup = $this->tenantDB()->table('chsone_grp_ledger_tree')
                ->where('soc_id', $data['soc_id'])
                ->where('entity_type', $this->constants['ENTITY_TYPE_GROUP'])
                ->where('ledger_account_name', 'Security Deposit')
                ->first();

            if ($objSecurityDepositGroup) {

                $ledgerAccountId = $this->manipulate(
                    $ledgerName,
                    $this->constants['ENTITY_TYPE_LEDGER'],
                    "",
                    $objSecurityDepositGroup->ledger_account_id,
                    $objSecurityDepositGroup->behaviour,
                    '',
                    0,
                    '',
                    '',
                    null,
                    $objSecurityDepositGroup->context,
                    0,
                    $data['soc_id']
                );

                if (gettype($ledgerAccountId) != 'boolean' && strpos('DUP', $ledgerAccountId) === false) {
                    $arrClientLedgerDetails['recieving_ledger_id'] = $ledgerAccountId;
                    $arrClientLedgerDetails['receiver_name'] = $ledgerName;
                }
            }
        }

        return $arrClientLedgerDetails;
    }

    public function manipulate($name, $entity_type = ENTITY_TYPE_GROUP, $grp_ledg_id = "", $parent_group = 0, $behaviour = "", $ledg_id = '', $opening_balance = 0, $update_led_id = '', $ledger_start_date = '', $ledger_type = '', $context = '', $txn_id = 0, $soc_id = 0, $is_reco = 0, $user_id = 0)
    {
        $dup = $this->_checkGroupNameDuplication($name, $ledg_id, $update_led_id, $soc_id);

        $arrFYDetail = $this->getCurrentFinancialYear(['soc_id' => $soc_id]);
        $arrFYDetail = json_decode(json_encode($arrFYDetail), true);
        $ledger_start_date = $arrFYDetail['fy_start_date'];

        if ($dup == 0) {
            $behaviour = trim(strtolower($behaviour));
            $grpLedgTree = $this->tenantDB()->table('chsone_grp_ledger_tree')->where('ledger_account_id', $ledg_id)->first();

            if (empty($grpLedgTree)) {
                $grpLedgTree = [
                    'ledger_account_id' => $ledg_id,
                    'entity_type' => $entity_type,
                    'soc_id' => $soc_id,
                    'ledger_account_name' => $name,
                    'ledger_start_date' => $ledger_start_date,
                    'context_ref_id' => 0,
                    'operating_type' => !empty($ledger_type) && $ledger_type != "NULL" ? $ledger_type : '',
                    'parent_id' => !empty($parent_group) ? $parent_group : HEAD_GROUP_VAL,
                    'behaviour' => !empty($behaviour) ? $behaviour : ($ledger_props["behaviour"] ?? ''),
                    'nature_of_account' => !empty($behaviour) ? config("nature_account.$behaviour") : ($ledger_props["nature_account"] ?? ''),
                    'report_head' => !empty($behaviour) ? config("report_head.$behaviour") : ($ledger_props["report_head"] ?? ''),
                    'context' => !empty($context) ? $context : ($ledger_props["context"] ?? ''),
                    'defined_by' => $this->constants['USER'],
                    'status' => $this->constants['ACTIVE'],
                    'added_on' => now(),
                    'created_by' => $user_id ?? 0,
                ];
                $grpLedgTreeId = $this->tenantDB()->table('chsone_grp_ledger_tree')->insertGetId($grpLedgTree);
                $grpLedgTree['ledger_account_id'] = $grpLedgTreeId;
                $ledg_id = $grpLedgTreeId;
            } else {
                // Update existing record with new data
                $this->tenantDB()->table('chsone_grp_ledger_tree')->where('ledger_account_id', $ledg_id)->update([
                    'entity_type' => $entity_type,
                    'soc_id' => $soc_id,
                    'ledger_account_name' => $name,
                    'ledger_start_date' => $ledger_start_date,
                    'context_ref_id' => 0,
                    'operating_type' => !empty($ledger_type) && $ledger_type != "NULL" ? $ledger_type : '',
                    'parent_id' => !empty($parent_group) ? $parent_group : HEAD_GROUP_VAL,
                    'behaviour' => !empty($behaviour) ? $behaviour : ($ledger_props["behaviour"] ?? ''),
                    'nature_of_account' => !empty($behaviour) ? config("nature_account.$behaviour") : ($ledger_props["nature_account"] ?? ''),
                    'report_head' => !empty($behaviour) ? config("report_head.$behaviour") : ($ledger_props["report_head"] ?? ''),
                    'context' => !empty($context) ? $context : ($ledger_props["context"] ?? ''),
                    'defined_by' => USER,
                    'status' => ACTIVE,
                    'added_on' => now(),
                    'created_by' => $user_id ?? 0,
                ]);
            }

            if (!in_array(strtolower($behaviour), [INCOME, EXPENSE])) {
                $txn_date = $ledger_start_date;
                $narration = 'entry for opening balance';

                if ($txn_id = $this->addTxn(
                    $ledg_id,
                    $opening_balance,
                    $narration,
                    $txn_date,
                    "",
                    $grpLedgTree['nature_of_account'],
                    "",
                    "",
                    "",
                    "",
                    $name,
                    $is_opning = 1,
                    $is_reco

                )) {
                    // Transaction added
                } else {
                    // Handle transaction rollback if needed
                }
            }

            return $ledg_id;
        } else {
            return "DUP" . $dup;
        }
    }

    public function addTxn($ledger_id, $txn_amt, $narration, $txn_date, $voucher_type = VOUCHER_JOURNAL, $txn_type = "", $from_txn_id = "", $pay_mode = "", $pay_ref = "", $opening_balance = "", $ledger_name = "", $is_opning = 1, $is_reco = 0)
    {
        return $this->_addTransaction($is_reco, $ledger_id, $txn_amt, $narration, $txn_date, $voucher_type, $txn_type, $from_txn_id, $pay_mode, $pay_ref, $ledger_name, $is_opning = 1);
    }

    private function _addTransaction($ledger_id, $txn_amt, $narration, $txn_date, $voucher_type = VOUCHER_JOURNAL, $txn_type = "", $from_txn_id = "", $pay_mode = "", $pay_ref = "", $name = "", $is_opning = 0, $other_recp_ref = "", $soc_id = "", $created_by = "", $voucher_reference_id = "", $voucher_reference_number = "", $is_cancelled = 0, $is_reco = 0)
    {

        if (!empty($txn_type) || $txn_type != "") {
            $type_txn = $txn_type;
        } else {
            if ($from_txn_id) {
                $mode = MODE_TO;
                $type_txn = "cr";
            } else {
                $mode = MODE_FROM;
                $type_txn = "dr";
            }
        }

        $txnData = [
            'soc_id' => $soc_id,
            'transaction_date' => $txn_date,
            'ledger_account_id' => $ledger_id,
            'ledger_account_name' => $name,
            'voucher_type' => $voucher_type,
            'transaction_type' => $type_txn,
            'payment_mode' => $pay_mode,
            'payment_reference' => $pay_ref,
            'transaction_amount' => $txn_amt,
            'other_reference_id' => $other_recp_ref,
            'txn_from_id' => $from_txn_id,
            'memo_desc' => $narration,
            'is_opening_balance' => $is_opning,
            'is_reconciled' => $is_reco,
            'voucher_reference_number' => $voucher_reference_number,
            'voucher_reference_id' => $voucher_reference_id,
            'is_cancelled' => $is_cancelled,
            'created_by' => empty($created_by) ? $created_by : 0,
            'added_on' => now(),
        ];

        if ($is_opning == 1) {
            $txnEntry = $this->tenantDB()->table('chsone_ledger_transactions')
                ->where('soc_id', $soc_id)
                ->where('is_opening_balance', 1)
                ->where('ledger_account_id', $ledger_id)
                ->where('transaction_date', $txn_date)
                ->first();

            if ($txnEntry) {
                $txnData['txn_id'] = $txnEntry->txn_id;
                $this->tenantDB()->table('chsone_ledger_transactions')
                    ->where('txn_id', $txnEntry->txn_id)
                    ->update($txnData);
                return $txnEntry->txn_id;
            }
        }
        $txn_id = $this->tenantDB()->table('chsone_ledger_transactions')->insertGetId($txnData);
        return $txn_id;

    }

    private function _checkGroupNameDuplication($name, $ledg_id = "", $update_led_id = "", $soc_id)
    {
        $query = $this->tenantDB()->table('chsone_grp_ledger_tree')->where('soc_id', $soc_id)
            ->whereRaw('LOWER(ledger_account_name) = ?', [strtolower(trim($name))]);

        if ($ledg_id) {
            $query->where('ledger_account_id', '!=', $ledg_id);
        }

        if ($update_led_id) {
            $query->where('ledger_account_id', '!=', $update_led_id);
        }

        $ledgers_count = $query->count();

        return $ledgers_count;
    }

    public function getCurrentFinancialYear($data = [])
    {
        $arrFYDetail = $this->tenantDB()->table('soc_account_financial_year_master')->where('soc_id', $data['soc_id'])
            ->where('confirmed', 0)
            ->orderBy('account_closing_id', 'asc')
            ->first();

        return !empty($arrFYDetail) ? $arrFYDetail : [];
    }

    public function createMemberLedgerCredit($arrData = [])
    {
        $arrResponse = ['status' => 'error'];
        $arrPostData = $arrData['arrPostData'];

        if (!empty($arrPostData)) {
            // Member
            $arrIncomeInvoiceMemberDetail = $arrData['arrIncomeInvoiceMemberDetail'];
            if ($arrPostData['payment_amount'] > 0) {
                $countLedgerEntry = 0;

                if (!empty($arrIncomeInvoiceMemberDetail)) {
                    $arrPostData['unit_id'] = $arrIncomeInvoiceMemberDetail['fk_unit_id'];
                    $arrPostData['member_id'] = $arrIncomeInvoiceMemberDetail['id'];
                }

                if (!empty($arrPostData['payment_date']) && str_contains($arrPostData['payment_date'], '-')) {
                    $arrPostData['payment_date'] = $this->getDisplayDate($arrPostData['payment_date']);
                }

                if ($this->constants['ACCOUNT_MODULE_EXIST'] == 1) {
                    $arrListnerData = ['soc_id' => $arrData['soc_id'], 'unit_id' => $arrPostData['unit_id']];
                    $arrUnitDetails = $this->getUnitDetailById($arrListnerData);

                    $arrUnitDetails = json_decode(json_encode($arrUnitDetails), true);
                    if (!empty($arrUnitDetails)) {
                        $arrListnerData['ledger_id'] = $arrUnitDetails['ledger_account_id'];
                        $arrUnitLedgerDetails = $this->checkledgerExistNew($arrListnerData);

                        if (!empty($arrUnitLedgerDetails)) {
                            $countLedgerEntry = $this->paymentLedgerEntry([
                                'soc_id' => $arrData['soc_id'],
                                'arrPostData' => $arrPostData,
                                'arrUnitLedgerDetails' => $arrUnitLedgerDetails,
                                'from_ledger' => $arrData['from_ledger'] ?? '',
                                'to_ledger' => $arrData['to_ledger'] ?? '',
                                'narration' => $arrData['narration'],
                            ]);

                            if (!$countLedgerEntry) {

                                $arrResponse['status'] = 'success';
                            }
                        }
                    }

                    // Send notification
                    if ($countLedgerEntry == 0) {
                        // Send notification code
                    }
                }
            }
        }

        return $arrResponse;
    }

    public function getUnitDetailById(array $data = [])
    {
        $soc_id = $data['soc_id'];
        $unit_id = $data['unit_id'];

        $arrUnitDetails = $this->tenantDB()->table('chsone_units_master')->where('soc_id', $soc_id)
            ->where('unit_id', $unit_id)
            ->first();

        return !empty($arrUnitDetails) ? $arrUnitDetails : [];
    }

    public function paymentLedgerEntry($data = array())
    {
        $config = Config::get("constants");

        $countLedgerEntry = 0;

        if ($this->constants['ACCOUNT_MODULE_EXIST'] == 1) {

            $soc_id = $data['soc_id'];
            $arrPostData = $data['arrPostData'];
            $arrUnitLedgerDetails = $data['arrUnitLedgerDetails'];
            $arrUnitLedgerDetails = json_decode(json_encode($arrUnitLedgerDetails), true);

            if (isset($arrPostData['clearance_date']) && !empty($arrPostData['clearance_date'])) {
                $arrPostData['payment_date'] = $arrPostData['clearance_date'];
            }

            if (empty($arrPostData['payment_date'])) {
                $arrPostData['payment_date'] = $this->getCurrentDate('display');
            }

            //$arrIncomeAccounts = $this->incometrackerevent->incometracker('accountListener:checkledgerExistNew', array('auth' => $data['auth'], 'ledger_id' => $arrPostData['bank_account']));
            // print_r($this->constants['payment_mode_for_receipt_reversal']); exit();
            if (!empty($arrPostData['payment_mode']) && !in_array(strtolower($arrPostData['payment_mode']), $this->constants['payment_mode_for_receipt_reversal'])) {
                //  print_r(array(trim($arrPostData['payment_instrument']) . '_PG_LEDGER_ID'));
                $arrInvoiceGeneralSetting = $this->getInvoiceGeneralSetting(array('soc_id' => $data['soc_id'], 'setting_key' => array(trim($arrPostData['payment_instrument']) . '_PG_LEDGER_ID')));

                if (!empty($arrInvoiceGeneralSetting[0]['setting_value'])) {
                    $arrIncomeAccounts = $this->checkledgerExistNew(array('soc_id' => $data['soc_id'], 'ledger_id' => $arrInvoiceGeneralSetting[0]['setting_value']));
                    $arrIncomeAccounts['ledger_id'] = $arrIncomeAccounts['recieving_ledger_id'];
                    $arrIncomeAccounts['ledger_name'] = $arrIncomeAccounts['receiver_name'];
                } else {

                    if (!empty($data['to_ledger'])) {
                        $arrIncomeAccounts['ledger_id'] = $data['to_ledger']['recieving_ledger_id'];
                        $arrIncomeAccounts['ledger_name'] = $data['to_ledger']['receiver_name'];
                    } else {
                        return 1;
                    }
                }
            } else {

                if (isset($data['arrUnitLedgerDetails']['suspenseLedgerDetails']) && !empty($data['arrUnitLedgerDetails']['suspenseLedgerDetails'])) {
                    $arrIncomeAccounts['ledger_id'] = $data['arrUnitLedgerDetails']['suspenseLedgerDetails']['recieving_ledger_id'];
                    $arrIncomeAccounts['ledger_name'] = $data['arrUnitLedgerDetails']['suspenseLedgerDetails']['receiver_name'];
                } else {

                    // Getting bank/cash ledger details
                    $arrAccountDetail = $this->getBankCashAccountDetail(array('soc_id' => $data['soc_id'])); // get all Unit details

                    $arrLedgerAccountDetail = $this->getLedgerAccountDetail(array(
                        'account_detail' => $arrAccountDetail,
                    )); // get all Unit details

                    if (!empty($arrLedgerAccountDetail)) {
                        $arrIncomeAccounts['ledger_id'] = $arrLedgerAccountDetail['cash']['ledger_id'];
                        $arrIncomeAccounts['ledger_name'] = $arrLedgerAccountDetail['cash']['ledger_name'];
                        if (strtolower($arrPostData['payment_mode']) != 'cash') {
                            if (!empty($arrPostData['bank_account'])) {
                                $arrIncomeAccounts['ledger_id'] = $arrPostData['bank_account'];
                                $arrIncomeAccounts['ledger_name'] = $arrLedgerAccountDetail['arrBank'][$arrPostData['bank_account']];
                            } else {
                                $arrIncomeAccounts['ledger_id'] = $arrLedgerAccountDetail['bank']['ledger_id'];
                                $arrIncomeAccounts['ledger_name'] = $arrLedgerAccountDetail['bank']['ledger_name'];
                            }
                        }
                    }
                }
            }

            if ($arrPostData['payment_mode'] == 'cashtransfer') {
                $paymentModeForNarration = $this->constants['DISPLAY_CASH_TRANSFER'] ?? 'Electronic Fund Transfer';
            } else {
                $paymentModeForNarration = $arrPostData['payment_mode'] ?? null;
            }

            // $paymentModeForNarration = $arrPostData ['payment_mode'];
            $strNarration = '';
            if (!empty($arrPostData['payment_mode']) && in_array(strtolower($arrPostData['payment_mode']), $this->constants['payment_mode_for_clearance'])) {
                $strNarration = ' with transaction ref. (' . (!empty($arrPostData['transaction_reference']) ? $arrPostData['transaction_reference'] : null) . ', ' . $arrPostData['payment_instrument'] . ')';
            } elseif (!empty($arrPostData['payment_mode']) && strtolower($arrPostData['payment_mode']) == 'cashtransfer') {
                $strNarration = ' with payment ref. (' . (!empty($arrPostData['transaction_reference']) ? $arrPostData['transaction_reference'] : null) . ')';
            } elseif (!empty($arrPostData['payment_mode']) && !in_array(strtolower($arrPostData['payment_mode']), $this->constants['payment_mode_for_receipt_reversal'])) {
                $strNarration = ' with transaction ref. (' . (!empty($arrPostData['transaction_reference']) ? $arrPostData['transaction_reference'] : null) . ')';

                switch ($arrPostData['payment_mode']) {
                    case 'YES_BANK_PG':
                        $paymentModeForNarration = DISPLAY_YES_BANK;
                        break;
                    case 'YES_BANK_ECOLLECT':
                        $paymentModeForNarration = DISPLAY_YES_BANK_ECOLLECT;
                        break;
                    case 'MOBIKWIK_WALLET':
                        $paymentModeForNarration = MOBIKWIK_WALLET;
                        break;
                    case 'MOBIKWIK_PG':
                        $paymentModeForNarration = DISPLAY_MOBIKWIK_PG;
                        break;
                    case 'CASHFREE_PG':
                        $paymentModeForNarration = DISPLAY_CASHFREE_PG;
                        break;
                    case 'PAYTM_PG':
                        $paymentModeForNarration = DISPLAY_PAYTM;
                        break;
                    case 'HDFC_PG':
                        $paymentModeForNarration = DISPLAY_HDFC_PG;
                        break;
                    case 'ATOM_PG':
                        $paymentModeForNarration = DISPLAY_ATOM_PG;
                        break;
                    case 'cashtransfer':
                        $paymentModeForNarration = DISPLAY_CASH_TRANSFER;
                        break;
                }
            }
            // echo $arrIncomeInvoicePaymentTracker['payment_mode'];exit;
            // Ledger entry for payment amount
            $arrLedgerTransactionData = array(
                'soc_id' => $soc_id,
            );

            $arrLedgerTransactionData['voucher_type'] = isset($arrPostData['voucher_type_credit']) ? $this->constants['VOUCHER_CREDIT'] : $this->constants['VOUCHER_RECEIPT'] ?? '';
            $arrLedgerTransactionData['voucher_reference_number'] = $arrPostData['receipt_number'];
            $arrLedgerTransactionData['voucher_reference_id'] = $arrPostData['payment_tracker_id'];
            $arrLedgerTransactionData['from_ledger_id'] = $arrIncomeAccounts['ledger_id'];
            $arrLedgerTransactionData['to_ledger_id'] = $arrUnitLedgerDetails['receiving_ledger_id'];
            $arrLedgerTransactionData['transaction_date'] = !empty($arrPostData['payment_date']) ? $this->getDatabaseDate($arrPostData['payment_date']) : $this->getCurrentDate('database');
            $arrLedgerTransactionData['transaction_amount'] = $arrPostData['payment_amount'];

            // $arrLedgerTransactionData['narration'] = 'Amount received against Invoice ' . $arrPostData['member_paid_invoice']  ?? '' . ' dated ' . $this->getDisplayDate($arrPostData['payment_date']) . ' through ' . $paymentModeForNarration . $strNarration; // 'Invoice '.$arrPostData['member_paid_invoice'].' '.$eachLedgerDetail['particular'].' Write Off';
            $arrLedgerTransactionData['narration'] = 'Amount received against Invoice ' .
                (!empty($arrPostData['member_paid_invoice']) ? $arrPostData['member_paid_invoice'] : '') .
                ' dated ' . $this->getDisplayDate($arrPostData['payment_date']) .
                ' through ' . $paymentModeForNarration .
                $strNarration;

            if ((empty($arrPostData['total_unpaid_amount']) || $arrPostData['total_unpaid_amount'] <= 0) && empty($arrPostData['member_paid_invoice'])) {
                // $arrLedgerTransactionData['narration'] = 'Advance payment received from ' . ucwords($arrPostData['received_from']) . ' dated ' . $this->getDisplayDate($arrPostData['payment_date']) . ' through ' . $paymentModeForNarration . $strNarration; // $eachInvoiceDetail['invoice_number'] . ' late payment charges';
                $arrLedgerTransactionData['narration'] = 'Amount received against Invoice ' .
                    ($arrPostData['member_paid_invoice'] ?? '') .
                    ' dated ' . $this->getDisplayDate($arrPostData['payment_date']) .
                    ' through ' . $paymentModeForNarration .
                    $strNarration;

            }
            if ($arrPostData['bill_type'] == 'suspense') {
                $arrLedgerTransactionData['narration'] = 'Unidenfied payment from ' . ucwords($arrPostData['received_from']) . ' dated ' . $this->getDisplayDate($arrPostData['payment_date']) . ' through ' . $paymentModeForNarration . $strNarration;
            }

            $arrLedgerTransactionData['from_ledger_name'] = $arrIncomeAccounts['ledger_name'];
            $arrLedgerTransactionData['to_ledger_name'] = $arrUnitLedgerDetails['receiver_name'];
            $arrLedgerTransactionData['payment_reference'] = !empty($arrPostData['transaction_reference'])
                ? $arrPostData['transaction_reference']
                : null;

            // $arrLedgerTransact ionData['payment_reference']=0;
            $arrLedgerTransactionData['transaction_type'] = '';
            $arrLedgerTransactionData['mode_of_payment'] = $arrPostData['payment_mode'] ?? null;
            $arrLedgerTransactionData['other_payment_ref'] = '';

            // Code to replace from ledger id From Bank/Cash

            if (!empty($data['from_ledger'])) {

                $arrLedgerTransactionData['from_ledger_id'] = $data['from_ledger']['recieving_ledger_id'];
                $arrLedgerTransactionData['from_ledger_name'] = $data['from_ledger']['receiver_name'];
            }
            if (!empty($data['to_ledger']) && !empty($data['to_ledger']['recieving_ledger_id']) && !empty($data['to_ledger']['receiver_name'])) {
                $arrLedgerTransactionData['to_ledger_id'] = $data['to_ledger']['recieving_ledger_id'];
                $arrLedgerTransactionData['to_ledger_name'] = $data['to_ledger']['receiver_name'];
            }

            if (isset($data['narration'])) {
                $arrLedgerTransactionData['narration'] = $data['narration'];
            }
            $arrLedgerTransactionData['narration'] = '(Receipt No-' . $arrPostData['receipt_number'] . ') ' . $arrLedgerTransactionData['narration'];

            if (!empty($arrPostData['payment_note'])) {
                $arrLedgerTransactionData['narration'] .= ' [' . $arrPostData['payment_note'] . ']';
            }

            //echo 'pay'; print_r($arrLedgerTransactionData); exit();
            if (!empty($arrPostData['tds_amount']) && $arrPostData['tds_amount'] > 0) {

                $arrLedgerTransactionData['transaction_amount'] = round($arrPostData['payment_amount'] - $arrPostData['tds_amount'], 2);
                $arrLedgerEntry = $this->transactionLedgerEntry($arrLedgerTransactionData);
                if (isset($arrLedgerEntry['error']) && !empty($arrLedgerEntry['error'])) {
                    $countLedgerEntry++;
                } else {
                    $arrLedgerTransactionData['transaction_amount'] = $arrPostData['tds_amount'];
                    $arrListnerData['soc_id'] = $soc_id;
                    $arrListnerData['ledger_name'] = 'tds receivable';
                    $arrListnerData['context'] = ''; // CURRENT_ASSETS_GROUP;
                    $arrListnerData['behaviour'] = ASSET;

                    $arrTdsLedger = $this->checkledgerExistNew($arrListnerData);
                    $arrLedgerTransactionData['from_ledger_id'] = $arrTdsLedger['recieving_ledger_id'];
                    $arrLedgerTransactionData['from_ledger_name'] = $arrTdsLedger['receiver_name'];
                    $arrLedgerTransactionData['narration'] = 'TDS amount deducted against Invoice ' . $arrPostData['member_paid_invoice'] . ' dated ' . $this->getDisplayDate($arrPostData['payment_date']) . ' through ' . $paymentModeForNarration . $strNarration; // 'Invoice '.$arrPostData['member_paid_invoice'].' '.$eachLedgerDetail['particular'].' Write Off';

                    $arrLedgerEntry = $this->transactionLedgerEntry($arrLedgerTransactionData);
                    if (isset($arrLedgerEntry['error']) && !empty($arrLedgerEntry['error'])) {
                        $countLedgerEntry++;
                    }
                }
            } else {

                //echo 'led';
                $arrLedgerEntry = $this->transactionLedgerEntry($arrLedgerTransactionData);

                // print_r($arrLedgerEntry); exit();
                if (isset($arrLedgerEntry['error']) && !empty($arrLedgerEntry['error'])) {
                    $countLedgerEntry++;
                }
            }

            //Ledger entry for invoice writeoff
            if (!empty($arrPostData['writeoff_amount']) && $arrPostData['writeoff_amount'] > 0) {
                if ($arrPostData['writeoff_amount'] >= 1000) {
                    $arrExpenseWriteOff = $this->checkledgerExistNew(array('soc_id' => $data['soc_id'], 'ledger_name' => 'Expense Bad Debt', 'context' => EXPENSE, 'behaviour' => EXPENSE));
                    if (empty($arrExpenseWriteOff['recieving_ledger_id'])) {
                        $arrParentExpense = $this->getLedgerDetail(array('soc_id' => $data['soc_id'], 'ledger_name' => 'Indirect Expense', 'context' => EXPENSE, 'behaviour' => EXPENSE));
                        if (!empty($arrParentExpense['ledger_account_id'])) {
                            $arrExpenseWriteOff['receiver_name'] = 'Expense Bad Debt';
                            $arrExpenseWriteOff['recieving_ledger_id'] = $this->createNewLedgerExit(array('soc_id' => $data['soc_id'], 'ledger_name' => $arrExpenseWriteOff['receiver_name'], 'context' => EXPENSE, 'behaviour' => EXPENSE, 'parent_id' => $arrParentExpense['ledger_account_id']));
                        }
                    }
                } else {
                    $arrExpenseWriteOff = $this->checkledgerExistNew(array('soc_id' => $data['soc_id'], 'ledger_name' => 'Expense Write Off', 'context' => EXPENSE, 'behaviour' => EXPENSE));
                }
                if (empty($arrExpenseWriteOff['recieving_ledger_id'])) {
                    return 1;
                }
                $arrLedgerTransactionData['transaction_amount'] = $arrPostData['writeoff_amount'];
                $arrLedgerTransactionData['from_ledger_id'] = $arrExpenseWriteOff['recieving_ledger_id'];
                $arrLedgerTransactionData['from_ledger_name'] = $arrExpenseWriteOff['receiver_name'];

                $arrLedgerTransactionData['narration'] = 'Amount writeoff against Invoice ' . $arrPostData['member_paid_invoice'] . ' dated ' . $this->getDisplayDate($arrPostData['payment_date']); //'Invoice '.$arrPostData['member_paid_invoice'].' '.$eachLedgerDetail['particular'].' Write Off';
                $arrLedgerEntry = $this->transactionLedgerEntry($arrLedgerTransactionData);
                if (isset($arrLedgerEntry['error']) && !empty($arrLedgerEntry['error'])) {
                    $countLedgerEntry++;
                }
            }
        }

        return $countLedgerEntry;
    }

    public function getBankCashAccountDetail($data = [])
    {
        // Define the base query with joins and column selections
        $query = $this->tenantDB()->table('chsone_grp_ledger_tree as grpLedger')
            ->select([
                'grpLedger.ledger_account_id',
                'grpLedger.ledger_account_name',
                'grpLedger.context',
                'account.default_account',
                'account.default_bank_for_incidental',
                'account.default_bank_for_nonmember',
                'account.bank_name',
                'account.account_number',
                'account.bank_address',
                'account.bank_city',
                'account.bank_ifsc',
                'account.account_id',
            ])
            ->leftJoin('chsone_accounts_master as account', 'grpLedger.ledger_account_id', '=', 'account.ledger_account_id')
            ->where('grpLedger.soc_id', $data['soc_id'])
            ->where('grpLedger.entity_type', 'ledger')
            ->where('grpLedger.status', 1);

        // Filter by context based on 'only_bank' flag
        if (!empty($data['only_bank'])) {
            $query->where('grpLedger.context', 'bank');
        } else {
            $query->whereIn('grpLedger.context', ['cash', 'bank']);
        }

        // Execute the query and get results as an array
        $arrAccountDetail = $query->get()->toArray();

        return $arrAccountDetail;
    }

    public function getLedgerAccountDetail(array $data): array
    {
        $arrAccountDetail = [
            'cash' => [],
            'bank' => [],
            'arrBank' => [],
            'arrCash' => [],
        ];

        $data = json_decode(json_encode($data), true);
        // Check if account details are provided
        if (!empty($data['account_detail'])) {

            foreach ($data['account_detail'] as $eachAccountDetail) {

                $context = strtolower($eachAccountDetail['context']);
                $ledgerId = $eachAccountDetail['ledger_account_id'];
                $ledgerName = $eachAccountDetail['ledger_account_name'];

                // Handle cash account details
                if ($context === 'cash' && empty($arrAccountDetail['cash'])) {
                    $arrAccountDetail['cash'] = [
                        'ledger_id' => $ledgerId,
                        'ledger_name' => $ledgerName,
                    ];
                    $arrAccountDetail['arrCash'][$ledgerId] = $ledgerName;
                }

                // Handle bank account details
                if ($context === 'bank') {
                    if ($eachAccountDetail['default_account'] == 1) {
                        $arrAccountDetail['bank'] = [
                            'ledger_id' => $ledgerId,
                            'ledger_name' => $ledgerName,
                        ];
                    }
                    $arrAccountDetail['arrBank'][$ledgerId] = $ledgerName;

                    // Default bank for incidental cases
                    if (!empty($data['default_bank_incidental']) && $eachAccountDetail['default_bank_for_incidental'] == 1) {
                        $arrAccountDetail['bank'] = [
                            'ledger_id' => $ledgerId,
                            'ledger_name' => $ledgerName,
                        ];
                    }

                    // Default bank for non-member cases
                    if (!empty($data['default_bank_nonmember']) && $eachAccountDetail['default_bank_for_nonmember'] == 1) {
                        $arrAccountDetail['bank'] = [
                            'ledger_id' => $ledgerId,
                            'ledger_name' => $ledgerName,
                        ];
                    }
                }
            }

            // Set the first available bank if no default is selected
            if (empty($arrAccountDetail['bank']) && !empty($arrAccountDetail['arrBank'])) {
                $firstBank = reset($arrAccountDetail['arrBank']);
                $firstBankId = key($arrAccountDetail['arrBank']);
                $arrAccountDetail['bank'] = [
                    'ledger_id' => $firstBankId,
                    'ledger_name' => $firstBank,
                ];
            }
        }

        return $arrAccountDetail;
    }

    public function transactionLedgerEntry($data = [])
    {
        // Initialize response array
        $arrResponse = [];

        // Validate required fields
        if (empty(trim($data['from_ledger_id'])) || empty(trim($data['to_ledger_id']))) {
            return ['error' => true, 'message' => 'From or To ledger ID is missing'];
        }
        // Set default transaction date if not provided
        $data['transaction_date'] = $data['transaction_date'] ?? date('Y-m-d H:i:s');

        // Execute the transaction entry and capture the transaction ID
        $transaction_id = $this->executeTransactionEntry($data);

        // Check if the transaction was successful
        if ($transaction_id) {
            $arrResponse = ['success' => true, 'transaction_id' => $transaction_id];
        } else {
            $arrResponse = ['error' => true, 'message' => 'Transaction failed'];
        }

        return $arrResponse;
    }

    public function executeTransactionEntry($data)
    {
        $vouchers_array = [];

        //         // Check if voucher type contains an underscore and split if found
        if (isset($data['voucher_type']) && strpos($data['voucher_type'], '_') !== false) {
            $vouchers_array = explode('_', $data['voucher_type']);
            $data['voucher_type'] = $vouchers_array[0]; // Set the primary voucher type
        }
        // Attempt the first transaction entry
        $data['transaction_from_id'] = $this->_addTransactionEntry($data);

        // If the first transaction entry is successful
        if (!empty($data['transaction_from_id'])) {
            // Debug to confirm the first transaction ID

            // Check if a secondary voucher type exists and needs processing
            if (count($vouchers_array) >= 2) {
                $data['voucher_type'] = $vouchers_array[1]; // Set the secondary voucher type
            }

            // Attempt the second transaction entry
            $secondEntrySuccess = $this->_addTransactionEntry($data);
            // Check if the second entry was successful
            if ($secondEntrySuccess) {
                // Optional: commit transaction if using DB transactions
                // $this->soc_db_w->commit();
                return $data['transaction_from_id'];
            } else {
                // Optional: rollback transaction if the second entry fails
                // $this->soc_db_w->rollback();
                // dd('Failed to insert second transaction entry');
                // Log::error('Failed to insert second transaction entry', ['data' => $data]);
                return false;
            }
        } else {
            // Optional: rollback transaction if the first entry fails
            // $this->soc_db_w->rollback();
            // dd('Failed to insert first transaction entry');
            // Log::error('Failed to insert first transaction entry', ['data' => $data]);
            return false;
        }
    }

    public function _addTransactionEntry($data)
    {
        // Set transaction type and ledger details based on the mode
        if (!empty($data['transaction_from_id'])) {
            $data['transaction_type'] = "cr";
            $ledger_id = $data['to_ledger_id'];
            $ledger_name = $data['to_ledger_name'];
        } else {
            $data['transaction_type'] = "dr";
            $ledger_id = $data['from_ledger_id'];
            $ledger_name = $data['from_ledger_name'];
        }

        // Ensure data is an array
        $data = json_decode(json_encode($data), true);
        $soc_id = $data['soc_id'];

        // Check for opening balance transaction
        if (!empty($data['is_opening']) && $data['is_opening'] == 1) {
            $conditions = [
                ['soc_id', '=', $soc_id],
                ['is_opening_balance', '=', 1],
                ['ledger_account_id', '=', $ledger_id],
            ];

            $txn_entry = $this->tenantDB()->table('chsone_ledger_transactions')
                ->where($conditions)
                ->first();

            $data['txn_id'] = $txn_entry->txn_id ?? '';
        }

        // Prepare the data for insertion
        $transactionData = [
            'soc_id' => (string)$soc_id,
            'voucher_type' => (string)$data['voucher_type'],
            'voucher_reference_number' => (string)($data['voucher_reference_number'] ?? 0),
            'voucher_reference_id' => (int)($data['voucher_reference_id'] ?? 0),
            'transaction_date' => (string)$data['transaction_date'],
            'ledger_account_id' => (int)$ledger_id,
            'ledger_account_name' => (string)$ledger_name,
            'transaction_type' => (string)$data['transaction_type'],
            'payment_mode' => isset($data['payment_mode']) ? (string)$data['payment_mode'] : null,
            'transaction_amount' => (float)($data['transaction_amount'] ?? 0),
            'other_reference_id' => (string)($data['other_payment_ref'] ?? ''),
            'txn_from_id' => (string)($data['transaction_from_id'] ?? ''),
            'memo_desc' => (string)$data['narration'],
            'is_opening_balance' => (int)($data['is_opening'] ?? 0),
            'is_reconciled' => (int)($data['is_reconciled'] ?? 0),
            'created_by' => (int)($data['user_id'] ?? 0),
            'added_on' => date('Y-m-d H:i:s'),
            'is_cancelled' => !empty($data['is_cancelled']) ? 1 : 0,
        ];

        if (!empty($data['payment_reference'])) {
            $transactionData['payment_reference'] = $data['payment_reference'];
        }

        // Log the transaction data to ensure it's correct before insertion
        // Log::info('Attempting transaction entry', ['transactionData' => $transactionData]);

        // Attempt to insert the transaction data
        $txn = $this->tenantDB()->table('chsone_ledger_transactions')->insertGetId($transactionData);

        // Return the transaction ID if successful, otherwise false
        return $txn ? $txn : false;
    }

    public function createNewLedgerExit($data = [])
    {
        // Extract necessary variables from the data array
        $soc_id = $data['soc_id'];
        $ledger_name = $data['ledger_name'];
        $parent_id = $data['parent_id'];
        $behaviour = $data['behaviour'];
        $context = $data['context'];

        // Check whether the ledger already exists
        $objBookerLedger = $this->tenantDB()->table('chsone_grp_ledger_tree')
            ->where('soc_id', $data['soc_id'])
            ->where('entity_type', ENTITY_TYPE_LEDGER)
            ->where('ledger_account_name', $ledger_name)
            ->where('parent_id', $parent_id)
            ->where('behaviour', $behaviour)
            ->first();

        // If the ledger exists, return its account ID
        if (!empty($objBookerLedger)) {
            return $objBookerLedger->ledger_account_id;
        }

        // Create a new ledger if it does not exist
        $ledger_account_id = $this->manipulate($ledger_name, ENTITY_TYPE_LEDGER, $parent_id, $behaviour, $context);

        // Return the new ledger ID if creation is successful
        if ($ledger_account_id && strpos($ledger_account_id, 'DUP') === false) {
            return $ledger_account_id;
        }

        // Return false if the ledger creation fails
        return false;
    }

    public function escpaeSingleQuotes(array $array)
    {
        return collect($array)->mapWithKeys(function ($value, $key) {
            if (is_array($value)) {
                return [$key => $this->escpaeSingleQuotes($value)];
            }
            return [$key => is_string($value) ? addslashes($value) : $value];
        })->toArray();
    }

    public function addCreditAmountresponse($data, $id = 0)
    {
        $id = ($id) ? $id : '';

        if ($data['credit_used_type'] == 'adjustable' && empty($data['adjustable_date'])) {
        //            $data['adjustable_date'] = date("Y-m-d");  // store today date
        }
        $username = is_array($data['user'])
            ? trim(($data['user']['first_name'] ?? '') . ' ' . ($data['user']['last_name'] ?? ''))
            : '';

        $response = $this->saveCreditAccountResponse(array('auth' => true, 'process' => 'fetch', 'soc_id' => $data['soc_id'], 'id' => $id,
            'data' => $data, 'user' => $data['user_id'], 'username' => $username));

        return $response;
    }

    public function saveCreditNote($data)
    {
        // Initialize the response array
        $response = [
            'error' => true,
            'message' => '',
        ];

        try {
            // Create an array to hold the credit account data
            $creditAccount = [];

            // Set the properties from the request data
            $creditAccount['created_by'] = $data['user_id'] ?? 0; // Use authenticated user or passed user
            $creditAccount['created_date'] = date('Y-m-d H:i:s'); // Use current date-time
            $creditAccount['soc_id'] = $data['soc_id'] ?? null; // Ensure soc_id is present

            $creditAccount['invoice_number'] = $data['invoice_no'] ?? $data['invoice_number'] ?? null; // Use invoice_no if present, otherwise invoice_number
            $creditAccount['is_invoice_rectification'] = $data['is_invoice_rectification'] ?? null; // Default to null if not set
            $creditAccount['income_account_id'] = $data['income_account_ledger_id'] ?? null; // Default to null if not set
            $creditAccount['payment_tracker_id'] = $data['payment_tracker_id'] ?? null; // Default to null if not set
            $creditAccount['account_id'] = $data['account_id'] ?? null; // Ensure account_id is present
            $creditAccount['account_name'] = $data['account_name'] ?? null; // Ensure account_name is present
            $creditAccount['account_context'] = $data['account_context'] ?? null; // Default to null if not set
            $creditAccount['amount'] = $data['payment_amount'] ?? 0; // Default to 0 if not set
            $creditAccount['payment_mode'] = $data['payment_mode'] ?? null; // Default to null if not set
            $creditAccount['payment_date'] = !empty($data['payment_date']) ? $this->getDatabaseDate($data['payment_date']) : null; // Use a method to format the date
            $creditAccount['transaction_type'] = $data['transaction_type'] ?? null; // Ensure transaction_type is present
            $creditAccount['narration'] = $data['narration'] ?? null; // Default to null if not set
            $creditAccount['use_credit'] = $data['credit_used_type'] ?? 'adjustable'; // Default to 'adjustable' if not set
            $creditAccount['use_credit_after'] = $data['adjustable_date'] ?? null; // Default to null if not set
            $creditAccount['is_locked'] = $data['is_locked'] ?? 0; // Default to 0 if not set
            $creditAccount['use_credit_for'] = $data['used_for'] ?? null; // Default to null if not set
            $creditAccount['reference_no'] = $data['transaction_reference'] ?? null; // Default to null if not set
            $creditAccount['context'] = $data['context'] ?? 'system'; // Default to 'system' if not set

            // Insert the data and get the inserted ID using insertGetId()
            $creditAccountId = $this->tenantDB()->table('chsone_credit_accounts')->insertGetId($creditAccount);

            // Save the CreditAccount and return a successful response
            if ($creditAccountId) {
                $response['error'] = false;
                $response['id'] = $creditAccountId;
            }

        } catch (\Exception $e) {
            // Handle exceptions and return an error response
            $response['message'] = $e->getMessage();
        }

        return $response;
    }

    public function saveCreditNoteResponse($data)
    {

        foreach ($data['particular_data'] as $item) {
            // Prepare data to be saved using an associative array
            $creditData = [
                'soc_id' => $data['soc_id'],
                'account_id' => $data['account_id'],
                'credit_id' => $data['credit_account_id'],
                'credit_type' => $data['bill_type'],
                'rectified_invoice_id' => $data['invoice_id'],
                'rectified_invoice_number' => $data['invoice_number'],
                'rectified_particular_id' => $item['rectified_particular'],
                'rectified_particular_name' => $item['rectified_name'],
                'amount' => $item['rectified_value'],
                'status' => 1,
                'created_by' => $data['user_id'] ?? 0,
                'created_date' => date('Y-m-d H:i:s'),
                'payment_date' => $data['payment_date'],
            ];

            $result = $this->tenantDB()->table('chsone_credit_note')->insertGetId($creditData);

        }

        // Save the model and handle the response
        if ($result) {
            return [
                'error' => false,
                'id' => $result,
            ];
        } else {
            return [
                'error' => true,
            ];
        }
    }

    public function getInvoiceGeneralSetting(array $data = [])
    {

        $invoiceGeneralSetting = $this->tenantDB()->table('income_invoice_general_settings')
            ->where('soc_id', $data['soc_id'])
            ->whereIn('setting_key', $data['setting_key'])
            ->get();

        return $invoiceGeneralSetting->isNotEmpty() ? $invoiceGeneralSetting->toArray() : [];
    }

    public function payNonmemberBillLedger($data = array())
    {
        $config = Config::get("constants");

        $success = 1;
        if ($this->constants['ACCOUNT_MODULE_EXIST'] == 1) {
            $PostData = $data['postData'];

            if (empty($PostData['bill_number'])) {
                $PostData['bill_number'] = $PostData['member_paid_invoice'] ?? '';
            }
            if (isset($PostData['clearance_date']) && !empty($PostData['clearance_date'])) {
                $PostData['payment_date'] = $PostData['clearance_date'];
            }
            $strNarration = '';

            if (empty($data['arrBookerLedgerDetails'])) {
                $arrListnerData['soc_id'] = $data['soc_id'];
                $arrListnerData['ledger_name'] = $PostData['booker_name'];
                $arrBookerLedgerDetails = $this->checkledgerExist($arrListnerData);
            } else {
                $arrBookerLedgerDetails = $data['arrBookerLedgerDetails'];
            }

            //$arrIncomeAccounts = $data['arrIncomeAccounts'];

            //Getting bank/cash ledger details
            $arrAccountDetail = $this->getBankCashAccountDetail(array('soc_id' => $data['soc_id'])); //get all Unit details

            $arrLedgerAccountDetail = $this->getLedgerAccountDetail(array('account_detail' => $arrAccountDetail)); //get all Unit details

            if (!empty($arrLedgerAccountDetail)) {

                $arrIncomeAccounts['ledger_id'] = $arrLedgerAccountDetail['cash']['ledger_id'];
                $arrIncomeAccounts['ledger_name'] = $arrLedgerAccountDetail['cash']['ledger_name'];
                if (strtolower($PostData['payment_mode']) != 'cash') {
                    if (!empty($PostData['bank_account'])) {
                        $arrIncomeAccounts['ledger_id'] = $PostData['bank_account'];
                        $arrIncomeAccounts['ledger_name'] = $arrLedgerAccountDetail['arrBank'][$PostData['bank_account']];
                    } else {
                        $arrIncomeAccounts['ledger_id'] = $arrLedgerAccountDetail['bank']['ledger_id'];
                        $arrIncomeAccounts['ledger_name'] = $arrLedgerAccountDetail['bank']['ledger_name'];
                    }

                    $strNarration = ' with transaction ref. (' . (!empty($PostData['transaction_reference']) ? $PostData['transaction_reference'] : null) . ', ' . $PostData['payment_instrument'] . ')';
                    if (!empty($PostData['payment_mode']) && strtolower($PostData['payment_mode']) == 'cashtransfer') {
                        $strNarration = ' with payment ref. (' . (!empty($PostData['transaction_reference']) ? $PostData['transaction_reference'] : null) . ')';
                    }
                }
            }

            $arrListnerData = array();
            $arrListnerData['soc_id'] = $data['soc_id'];
            $arrListnerData['voucher_type'] = isset($PostData['voucher_type_credit']) ? VOUCHER_CREDIT : $this->constants['VOUCHER_RECEIPT'] ?? '';
            $arrListnerData['voucher_reference_number'] = $PostData['receipt_number'];
            $arrListnerData['voucher_reference_id'] = $PostData['payment_tracker_id'];
            $arrListnerData['from_ledger_id'] = $arrIncomeAccounts['ledger_id'];
            $arrListnerData['to_ledger_id'] = $arrBookerLedgerDetails['recieving_ledger_id'];

            $arrListnerData['transaction_date'] = !empty($PostData['payment_date']) ? $this->getDatabaseDate($PostData['payment_date']) : $this->getCurrentDate('database');
            $arrListnerData['transaction_amount'] = $PostData['payment_amount'];

            if ($PostData['payment_mode'] == 'cashtransfer') {
                $payment_mode_display = $this->constants['DISPLAY_CASH_TRANSFER'] ?? '';
            } else {
                $payment_mode_display = ucfirst($PostData['payment_mode']);
            }

            $arrListnerData['narration'] = '(Receipt No-' . $PostData['receipt_number'] . ') Amount received against Invoice ' . $PostData['bill_number'] . ' dated ' . $this->getCurrentDate('display') . ' through ' . $payment_mode_display . $strNarration; //$key . ' bill pay';//$PostData['narration'];
            $arrListnerData['from_ledger_name'] = $arrIncomeAccounts['ledger_name'];
            $arrListnerData['to_ledger_name'] = $arrBookerLedgerDetails['receiver_name'];
            $arrListnerData['payment_reference'] = $PostData['transaction_reference'] ?? null;
            $arrListnerData['transaction_type'] = $PostData['transaction_type'];
            $arrListnerData['mode_of_payment'] = $PostData['payment_mode'];
            $arrListnerData['other_payment_ref'] = $PostData['other_payment_ref'] ?? '';


            if (!empty($PostData['payment_note'])) {
                $arrListnerData['narration'] .= ' [' . $PostData['payment_note'] . ']';
            }
            //Code to replace from ledger id From Bank/Cash
            if (isset($data['from_ledger'])) {
                $arrListnerData['from_ledger_id'] = $data['from_ledger']['recieving_ledger_id'];
                $arrListnerData['from_ledger_name'] = $data['from_ledger']['receiver_name'];
            }
            if (isset($data['to_ledger'])) {
                $arrListnerData['to_ledger_id'] = $data['to_ledger']['recieving_ledger_id'];
                $arrListnerData['to_ledger_name'] = $data['to_ledger']['receiver_name'];
            }

            if (isset($data['narration'])) {
                $arrListnerData['narration'] = '(Receipt No-' . $PostData['receipt_number'] . ') ' . $data['narration'];
            }

            // echo "arrListnerData"; echo "<pre>"; print_r($arrListnerData); echo "</pre>"; exit;


            if (!empty($PostData['tds_amount']) && $PostData['tds_amount'] > 0) {
                $arrListnerData['transaction_amount'] = round($PostData['payment_amount'] - $PostData['tds_amount'], 2);
                //print_r($arrListnerData);
                $arrLedgerEntry = $this->transactionLedgerEntry($arrListnerData);
                if (isset($arrLedgerEntry['error']) && !empty($arrLedgerEntry['error'])) {
                    $success = 0;
                } else {
                    $arrListnerData['transaction_amount'] = $PostData['tds_amount'];
                    $arrLedgerData['sco_id'] = $data['soc_id'];
                    $arrLedgerData['ledger_name'] = 'tds receivable';
                    $arrLedgerData['context'] = ''; //CURRENT_ASSETS_GROUP;
                    $arrLedgerData['behaviour'] = ASSET;

                    $arrTdsLedger = $this->checkledgerExistNew($arrLedgerData);
                    $arrListnerData['from_ledger_id'] = $arrTdsLedger['recieving_ledger_id'];
                    $arrListnerData['from_ledger_name'] = $arrTdsLedger['receiver_name'];

                    if ($PostData['payment_mode'] == 'cashtransfer') {
                        $payment_mode_display = DISPLAY_CASH_TRANSFER;
                    } else {
                        $payment_mode_display = ucfirst($PostData['payment_mode']);
                    }

                    $arrListnerData['narration'] = 'TDS amount deducted against Invoice ' . $PostData['bill_number'] . ' dated ' . $this->getCurrentDate('display') . ' through ' . $payment_mode_display . $strNarration; //'Invoice '.$arrPostData['member_paid_invoice'].' '.$eachLedgerDetail['particular'].' Write Off';

                    //print_r($arrListnerData);
                    $arrLedgerEntry = $this->transactionLedgerEntry($arrListnerData);
                    if (isset($arrLedgerEntry['error']) && !empty($arrLedgerEntry['error'])) {
                        $success = 0;
                    }
                }
            } else {
                $arrLedgerEntry = $this->transactionLedgerEntry($arrListnerData);
                if (isset($arrLedgerEntry['error']) && !empty($arrLedgerEntry['error'])) {
                    $success = 0;
                }
            }
        }
        //exit;
        return $success;
    }

    public function checkledgerExist($data = [])
    {
        $arrClientLedgerDetails = [];
        $soc_id = $data['soc_id'];
        $ledgerName = $data['ledger_name'];

        // Check if the ledger already exists for the outsider based on provided criteria
        $existingLedger = $this->tenantDB()->table('chsone_grp_ledger_tree')
            ->where('soc_id', $data['soc_id'])
            ->where('context', 'sundrydebtors')
            ->where('entity_type', ENTITY_TYPE_LEDGER)
            ->where('ledger_account_name', $ledgerName)
            ->first();

        if (!empty($existingLedger)) {
            // Ledger exists, so set the receiving ledger details
            $arrClientLedgerDetails['recieving_ledger_id'] = $existingLedger->ledger_account_id;
            $arrClientLedgerDetails['receiver_name'] = $existingLedger->ledger_account_name;
        } else {
            // Ledger does not exist, create a new ledger under the Sundry Debtors group
            $sundryDebtorGroup = $this->tenantDB()->table('chsone_grp_ledger_tree')
                ->where('soc_id', $data['soc_id'])
                ->where('entity_type', ENTITY_TYPE_GROUP)
                ->where('ledger_account_name', SUNDRY_DR)
                ->first();

            if ($sundryDebtorGroup) {
                // Create a new ledger entry under the Sundry Debtors group
                $ledgerAccountId = $this->manipulate(
                    $ledgerName,
                    ENTITY_TYPE_LEDGER,
                    "",
                    $sundryDebtorGroup->ledger_account_id,
                    $sundryDebtorGroup->behaviour,
                    '',
                    0,
                    '',
                    '',
                    null,
                    $sundryDebtorGroup->context
                );

                // Check if the new ledger was created successfully
                if (is_string($ledgerAccountId) && strpos($ledgerAccountId, 'DUP') === false) {
                    $arrClientLedgerDetails['recieving_ledger_id'] = $ledgerAccountId;
                    $arrClientLedgerDetails['receiver_name'] = $ledgerName;
                }
            }
        }

        return $arrClientLedgerDetails;
    }

    public function getMember($keyword, $id)
    {

        $obj = $this->tenantDB()->table('chsone_members_master as members_master')
            ->select(
                'members_master.id',
                'units_master.soc_building_name AS building',
                'units_master.unit_flat_number AS building_unit',
                'units_master.unit_id AS unit_id',
                DB::raw("CONCAT(members_master.member_first_name, ' ', members_master.member_last_name, '(', units_master.soc_building_name, '-', units_master.unit_flat_number, ')') AS title")
            )
            ->leftJoin('chsone_units_master as units_master', 'units_master.unit_id', '=', 'members_master.fk_unit_id')
            ->leftJoin('chsone_member_type_master as mtype', 'mtype.member_type_id', '=', 'members_master.member_type_id');
        // if (!empty($keyword)) {
        //     $obj->where(DB::raw("CONCAT(members_master.member_first_name, ' ', members_master.member_last_name, '(', units_master.soc_building_name, '-', units_master.unit_flat_number, ')')"), 'LIKE', '%' . $keyword . '%');
        // }

        if (!empty($id)) {
            $obj->where('members_master.id', $id);
        }

        $result = $obj->first();

        return $result;
    }


}
