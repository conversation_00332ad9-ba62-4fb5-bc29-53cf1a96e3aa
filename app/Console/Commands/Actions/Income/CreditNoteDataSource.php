<?php

namespace App\Console\Commands\Actions\Income;

use Illuminate\Support\Facades\DB;
use App\Console\Commands\Action;

class CreditNoteDataSource extends Action
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'datasource:CreditNoteList {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Get the credit note details';

    protected $formatter =  [
        'id' => '',
        'account_id' => '',
        'credit_id' => '',
        'unit_id' => '',
        'soc_building_name' => '',
        'unit_flat_number' => '',
        'rectified_invoice_number' => '',
        'credited_invoice_number' => '',
        'rectified_particular_name' => '',
        'credit_type' => '',
        'payment_date' => '',
        'amount' => '',
    ];

    protected $formatterByKeys =  ['id'];

    protected $mapper = [
        'id' => 'CN.id',
    ];

    /**
     * Execute the console command.
     */
    public function apply()
    {

        $page = $this->input['page'] ?? 1;
        $per_page = !empty($this->input['per_page']) ? $this->input['per_page']: ($this->hugeData ? 100000 : 10);

        $offset = ($page - 1) * $per_page;
        $searchTerm = $this->input['filters']['search'] ?? '';

        $obj = $this->tenantDB()->table('chsone_credit_note AS CN')
            ->select(
                'CN.id',
                'CN.account_id',
                'CN.credit_id',
                'UM.unit_id',
                'UM.soc_building_name',
                'UM.unit_flat_number',
                'CN.rectified_invoice_number',
                'CN.credited_invoice_number',
                'CN.rectified_particular_name',
                DB::raw('CASE WHEN CN.credit_type IS NULL OR CN.credit_type = "" THEN "Non Member" ELSE CN.credit_type END AS credit_type'),
                'CN.payment_date',
                DB::raw('FORMAT(CN.amount, 2) AS amount')  // Convert amount to float with 2 decimal places
                )
            ->join('chsone_units_master AS UM', 'CN.account_id', '=', 'UM.unit_id')
            ->where('CN.status', 1)
            ->orderBy('CN.id', 'DESC');
          

            $columns =[
               'CN.id',
                'CN.account_id',
                'CN.credit_id',
                'UM.unit_id',
                'UM.soc_building_name',
                'UM.unit_flat_number',
                'CN.rectified_invoice_number',
                'CN.credited_invoice_number',
                'CN.rectified_particular_name',
                'CN.credit_type',
                'CN.payment_date',
                'CN.amount'
            ];

            if ($searchTerm) {
                $obj->where(function ($q) use ($columns, $searchTerm) {
                    foreach ($columns as $column) {
                        $q->orWhere($column, 'LIKE', '%' . $searchTerm . '%');
                    }
                });
            }

            $count = $obj->get()->count();

            $obj = $obj->offset($offset);
            $obj = $obj->limit($per_page);

            $result = $obj->get();

        $this->data = $this->format($result->toArray());
        $this->meta['pagination']['total'] = $count;

    }
}
