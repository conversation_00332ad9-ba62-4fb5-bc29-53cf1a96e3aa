<?php

namespace App\Console\Commands\Actions\Income;

use Illuminate\Console\Command;
use App\Console\Commands\Action;

class ReceiptTrackerDataSource extends Action
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'datasource:ReceiptTrackerList {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Get the receipt tracker details';

    protected $formatterByKeys =  ['id'];

    protected $mapper = [
        'id' => 'id',
    ];

    /**
     * Execute the console command.
     */
    public function apply()
    {
        $postedValues = request()->query();
        $companyId = $this->input['company_id'];
        $transaction_reference = '';
        $receipt_number = '';
        $invoice_number = '';
        $payment_date = '';
        $unit_flat_number = '';
        $member_name = '';
        $status = [];
        $payment_mode = [];
        $pdc = [];
        $suspense = [];

        $page = $this->input['page'] ?? 1;
        $per_page = !empty($this->input['per_page']) ? $this->input['per_page']: ($this->hugeData ? 100000 : 10);
        $offset = ($page - 1) * $per_page;
        $s3Url = env('S3_AWS_URL').'/';

        if (isset($this->input['filters'])) {
           $transaction_reference = !empty($this->input['filters']['transaction_reference']) ? $this->input['filters']['transaction_reference'] : '';
            unset($this->input['filters']['transaction_reference']);

            $receipt_number = !empty($this->input['filters']['receipt_number']) ? $this->input['filters']['receipt_number'] : '';
            unset($this->input['filters']['receipt_number']);

            $invoice_number = !empty($this->input['filters']['invoice_number']) ? $this->input['filters']['invoice_number'] : '';
            unset($this->input['filters']['invoice_number']);

            $payment_date = !empty($this->input['filters']['payment_date']) ? $this->input['filters']['payment_date'] : '';
            unset($this->input['filters']['payment_date']);

            $unit_flat_number = !empty($this->input['filters']['unit_flat_number']) ? $this->input['filters']['unit_flat_number'] : '';
            unset($this->input['filters']['unit_flat_number']);

            $member_name = !empty($this->input['filters']['member_name']) ? $this->input['filters']['member_name'] : '';
            unset($this->input['filters']['member_name']);

            $status = isset($this->input['filters']['status']) ? explode(',', $this->input['filters']['status']) : [];
            unset($this->input['filters']['status']);

            $payment_mode = isset($this->input['filters']['payment_mode']) ? explode(',', $this->input['filters']['payment_mode']) : [];
            unset($this->input['filters']['payment_mode']);

            $pdc = isset($this->input['filters']['pdc']) ? explode(',', $this->input['filters']['pdc']) : [];
            unset($this->input['filters']['pdc']);

            $suspense = isset($this->input['filters']['suspense']) ? explode(',', $this->input['filters']['suspense']) : [];
            unset($this->input['filters']['suspense']);

        }

        $obj = $this->tenantDB()->table('income_invoice_payment_tracker AS ipt')
            ->select('ipt.id', 'ipt.unit_id', 'ipt.receipt_number', 'ipt.bill_type', 'ipt.payment_mode', 'ipt.received_from', 'ipt.payment_amount', 'ipt.status', 'ipt.transaction_status', 'ipt.payment_date', 'ipt.payment_instrument')
            ->leftJoin('chsone_units_master AS cum', 'ipt.unit_id', '=', 'cum.unit_id')
            ->selectRaw("IF(ipt.invoice_number IS NULL or ipt.invoice_number = '', 'NA', ipt.invoice_number) as invoice_number")
            ->selectRaw("IF(ipt.transaction_reference IS NULL or ipt.transaction_reference = '', 'NA', ipt.transaction_reference) as transaction_reference")
            ->selectRaw("IF(ipt.unit_id IS NULL or ipt.unit_id = '0', 'NA', CONCAT(cum.soc_building_name, ' / ', cum.unit_flat_number)) as society_unit_name")
            ->selectRaw("
                CASE
                    WHEN ipt.payment_mode = 'cash' THEN 'NA'
                    WHEN ipt.payment_mode = 'cheque' THEN CONCAT('CHQ:', IFNULL(ipt.transaction_reference, 'NA'), ' / ', IFNULL(ipt.payment_instrument, 'NA'))
                    ELSE CONCAT('Id:', IFNULL(ipt.transaction_reference, 'NA'))
                END as transaction_reference
            ")->selectRaw(
    "CONCAT(?, CAST(? AS UNSIGNED), '/receipts/', ipt.attachment_name) AS attachment",
    [$s3Url, $companyId]
);
           // ->orderBy('ipt.receipt_number', 'desc')

        if($transaction_reference) {
            $obj = $obj->where('ipt.transaction_reference', 'like', '%' . $transaction_reference . '%');
        }

        if($receipt_number) {
            $obj = $obj->where('ipt.receipt_number', 'like', '%' . $receipt_number . '%');
        }

        if($invoice_number) {
            $obj = $obj->where('ipt.invoice_number', 'like', '%' . $invoice_number . '%');
        }

        if($payment_date) {
            $obj = $obj->where('ipt.payment_date', 'like', '%' . $payment_date . '%');
        }

        if($unit_flat_number) {
            $obj = $obj->where('cum.unit_flat_number', 'like', '%' . $unit_flat_number . '%');
        }

        if($member_name) {
            $obj = $obj->where('ipt.received_from', 'like', '%' . $member_name . '%');
        }

        if($status) {
            $obj = $obj->whereIn('ipt.status', $status);
        }

        if($payment_mode) {
            $obj = $obj->whereIn('ipt.payment_mode', $payment_mode);
        }

        if($pdc) {
            $obj = $obj->whereIn('ipt.transaction_status', $pdc);
        }

        if($suspense) {
            $obj = $obj->whereIn('ipt.transaction_status', $suspense);
        }


        if (empty($status) && empty($payment_mode)) {
            $obj = $obj->whereNotIn('ipt.status', ['N','reversed']);
        }


       $obj=  $obj->orderBy('ipt.receipt_number', 'desc');

        $count = $obj->count();

        $obj = $obj->offset($offset);
        $obj = $obj->limit($per_page);

        $result = $obj->get();
        $this->data = $result;

        foreach ($this->data as $key => $value) {
            if($value->payment_mode=="cashtransfer"){
                $this->data[$key]->payment_mode = "Electronic Fund Transfer";
            }
            if($value->payment_mode=="cash"){
                $this->data[$key]->payment_mode = "Cash";
            }
            if($value->payment_mode=="cheque"){
                $this->data[$key]->payment_mode = "Cheque";
            }
            if($value->payment_mode=="HDFCPG"){
                $this->data[$key]->payment_mode = "PG HDFC";
            }
            if($value->payment_mode=="srvmobikwik"){
                $this->data[$key]->payment_mode = "Mobikwik";
            }
            if($value->payment_mode=="srvybecollect"){
                $this->data[$key]->payment_mode = "Ecollect Yes Bank";
            }
            if($value->payment_mode=="YESPG"){
                $this->data[$key]->payment_mode = "PG Yes Bank";
            }
            if($value->payment_mode=="srvmobikwikpg"){
                $this->data[$key]->payment_mode = "PG Mobikwik";
            }
            if($value->payment_mode=="CASHFREEPG"){
                $this->data[$key]->payment_mode = "PG Cashfree";
            }

            $this->data[$key]->attachment = $value->attachment;
        }
        $this->meta['pagination']['total']=$count;

    }
}
