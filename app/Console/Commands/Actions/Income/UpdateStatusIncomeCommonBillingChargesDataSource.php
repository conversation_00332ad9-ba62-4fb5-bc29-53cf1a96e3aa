<?php

namespace App\Console\Commands\Actions\Income;

use Illuminate\Console\Command;
use App\Console\Commands\Action;
use App\Models\Tenants\ChsoneCreditAccount;
use App\Models\Tenants\IncomeCommonBillingCharge;
use App\Models\Tenants\IncomeCommonBillingPayment;

class UpdateStatusIncomeCommonBillingChargesDataSource extends Action
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'datasource:UpdateStatusIncomeCommonBillingCharges {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Update Status Income Common Billing Charges DataSource';

    /**
     * Execute the console command.
     */
    public function apply()
    {
        $data = $this->input;

        // create an update query to update the status of income_common_billing_charges table based on the invoice number
        // $income_common_billing_charges = \App\Models\Tenants\IncomeCommonBillingCharge::where('invoice_number', $data['invoice_number'])
        // ->update([
        //     'payment_status' => 'partialpaid',
        //     'updated_date' => date('Y-m-d H:i:s')
        // ]);

        // initialize the advance variable
        $advance = 0;

        // create an select query to check amount from income_common_billing_charges with invoice_amount and accordint to that set update query to update the status of income_common_billing_charges table based on the invoice number
        $income_common_billing_charges = \App\Models\Tenants\IncomeCommonBillingCharge::where('invoice_number', $data['invoice_number'])->first();
        $amount = $income_common_billing_charges->amount;
        $invoice_amount = $this->input['invoice_amount'];
        $status = 'partialpaid';
        if($amount == $invoice_amount) {
            $status = 'paid';
        } elseif($amount < $invoice_amount) {
            $status = 'paid';
            $advance = $amount - $invoice_amount;
        }
        elseif($invoice_amount == 0) {
            $status = 'unpaid';
        }

        $income_common_billing_charges = \App\Models\Tenants\IncomeCommonBillingCharge::where('invoice_number', $data['invoice_number'])
        ->update([
            'payment_status' => $status,
            'updated_date' => date('Y-m-d H:i:s')
        ]);

        // insert row income_common_billing_payment
        $income_common_billing_payment = new IncomeCommonBillingPayment;
        $insertIncomeCommonBillingPayment = $this->insertData($income_common_billing_payment, $data);

        if ($insertIncomeCommonBillingPayment === true) {

            // check if advance amount is there then insert the entry in chsone_credit_account table
            $advanceEntry = $this->InsertAdvanceEntry($data, $advance);

            if($advanceEntry == false) {
                $this->message = "Error while inserting advance entry";
                $this->status = 'failed';
                $this->statusCode = 400;
            }

            $this->message = "Status updated successfully and payment details inserted successfully";
            $this->status = 'success';
            $this->statusCode = 200;
        } else {
            $this->message = "Status updated successfully but unable to insert payment details";
            $this->status = 'failed';
            $this->statusCode = 400;
        }
    }

    public function insertData($income_common_billing_payment, $data)
    {
        $income_common_billing_payment->soc_id = $data['company_id'];
        $income_common_billing_payment->fk_common_bill_id = $data['invoice_id'];
        $income_common_billing_payment->unit_id = $data['unit_id'];
        $income_common_billing_payment->payment_mode = $data['receipt_mode'];
        if($data['receipt_mode'] == 'cheque') {
            $income_common_billing_payment->transaction_reference = $data['cheque_number'] ?? '';
            $income_common_billing_payment->payment_instrument = $data['bank_name'] ?? '';
        } elseif($data['receipt_mode'] == 'cashtransfer') {
            $income_common_billing_payment->transaction_reference = $data['transaction_reference'] ?? '';
        }
        $income_common_billing_payment->payment_amount = $data['invoice_amount'] ?? '0.000';
        $income_common_billing_payment->tds_deducted = $data['tds_deducted'] ?? '0.000';
        $income_common_billing_payment->transaction_charges = $data['transaction_charges'] ?? '0.000';
        $income_common_billing_payment->discount_amount = $data['discount_amount'] ?? '0.000';
        $income_common_billing_payment->payment_date = $data['receipt_date'] ?? '';
        $income_common_billing_payment->created_by = $data['user_id'] ?? '0';
        $income_common_billing_payment->created_date = date("Y-m-d");

        $income_common_billing_payment->save();

        if ($income_common_billing_payment->save()) {
            return true;
        } else {
            return false;
        }
    }

    public function InsertAdvanceEntry($data, $advance)
    {
        $advanceEntry = new ChsoneCreditAccount();
        $advanceEntry->soc_id = $data['company_id'];
        $advanceEntry->payment_date = $data['receipt_date'];
        $advanceEntry->account_id = $data['unit_id'];
        $advanceEntry->account_name = $data['received_from'];
        $advanceEntry->account_context = 'Unit';
        $advanceEntry->amount = $advance;
        $advanceEntry->type = 'income';
        $advanceEntry->payment_mode = $data['receipt_mode'];
        $advanceEntry->transaction_type = 'cr';
        $advanceEntry->narration = "Amount Rs ".$advance." has credited from Advance of ".$data['invoice_number'];
        $advanceEntry->use_credit = 'adjustable';
        $advanceEntry->is_locked = 0;
        $advanceEntry->income_account_id = 0;
        $advanceEntry->use_credit_for = 'incidental';
        $advanceEntry->context = 'system';
        // $advanceEntry->created_name = $data['user_name'];
        // $advanceEntry->created_by = $data['user_id'];
        $advanceEntry->created_date = date("Y-m-d");

        if ($advanceEntry->save()) {
            return true;
        } else {
            return false;
        }
    }
}
