<?php

namespace App\Console\Commands\Actions\Income;

use Illuminate\Console\Command;
use App\Console\Commands\Action;

class IncidentalBillListDataSource extends Action
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'datasource:IncidentalBillList {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Get the incidental bill list';

    protected $formatter = [
        'id' => '',
        'unit_id' => '',
        'invoice_number' => '',
        'billing_type' => '',
        'particular' => '',
        'advance_amount' => '',
        'amount' => '',
        'payment_status' => '',
        'status' => '',
        'bill_date' => '',
        'from_date' => '',
        'to_date' => '',
        'due_date' => '',
        'bill_from' => '',
        'payment_mode' => '',
        'transaction_charges' => '',
        'discount_amount' => '',
        'payment_instrument' => '',
        'unit_id' => '',
        'unit_flat_number' => '',
        'soc_building_name' => '',
        'member_first_name' => '',
        'gstin' => '',
        'member_last_name' => '',
        'member_mobile_number' => '',
        'member_gstin' => '',
        'principal_amount' => '',
        'interest_amount' => '',
        'outstanding_principal' => '',
        'outstanding_interest' => '',
        'roundoff_amount' => '',
        'payment_amount' => '',
        'tax_amount' => ''
    ];

    protected $formatterByKeys = ['id'];

    protected $mapper = [
        'id' => 'icb.id',
    ];

    /**
     * Execute the console command.
     */
    public function apply()
    {
        $obj = $this->tenantDB()->table('income_common_billing_charges as icb')
        ->select(
            'icb.id',
            'icb.fk_unit_id AS unit_id',
            'icb.invoice_number',
            'icb.billing_type',
            'icac.particular',
            'icb.advance_amount',
            'icb.amount',
            'icb.payment_status',
            'icb.status',
            'icb.bill_date',
            'icb.from_date',
            'icb.to_date',
            'icb.due_date',
            'icb.bill_from',
            'icb.payment_mode',
            'icb.transaction_charges',
            'icb.discount_amount',
            'icb.payment_instrument',
            'unit.unit_id',
            'unit.unit_flat_number',
            'unit.soc_building_name',
            'memmaster.member_first_name',
            'memmaster.gstin',
            'memmaster.member_last_name',
            'memmaster.member_mobile_number',
            'unitInvoice.member_gstin',
            'unitInvoice.principal_amount',
            'unitInvoice.interest_amount',
            'unitInvoice.outstanding_principal',
            'unitInvoice.outstanding_interest',
            'unitInvoice.roundoff_amount'
        )
        ->leftJoin('chsone_members_master as memmaster', 'icb.fk_member_id', '=', 'memmaster.id')
        ->leftJoin('chsone_units_master as unit', 'icb.fk_unit_id', '=', 'unit.unit_id')
        ->leftJoin('income_unit_invoices as unitInvoice', 'icb.invoice_number', '=', 'unitInvoice.invoice_number')
        ->leftJoin('income_common_area_charges as icac', 'icb.billing_type', '=', 'icac.id')
        ->leftJoin('income_common_billing_payment as icbp', 'icb.id', '=', 'icbp.fk_common_bill_id')
        ->where('icb.payment_status', '!=', 'cancelled')
        ->groupBy(
            'icb.id',
            'icb.fk_unit_id',
            'icb.invoice_number',
            'icb.billing_type',
            'icac.particular',
            'icb.advance_amount',
            'icb.amount',
            'icb.payment_status',
            'icb.status',
            'icb.bill_date',
            'icb.from_date',
            'icb.to_date',
            'icb.due_date',
            'icb.bill_from',
            'icb.payment_mode',
            'icb.transaction_charges',
            'icb.discount_amount',
            'icb.payment_instrument',
            'unit.unit_id',
            'unit.unit_flat_number',
            'unit.soc_building_name',
            'memmaster.member_first_name',
            'memmaster.gstin',
            'memmaster.member_last_name',
            'memmaster.member_mobile_number',
            'unitInvoice.member_gstin',
            'unitInvoice.principal_amount',
            'unitInvoice.interest_amount',
            'unitInvoice.outstanding_principal',
            'unitInvoice.outstanding_interest',
            'unitInvoice.roundoff_amount',
            'unitInvoice.invoice_number'
        )
        ->selectRaw('IF(SUM(icbp.payment_amount) = 0, 0, SUM(icbp.payment_amount)) AS payment_amount')
        ->selectRaw('(SELECT IF(SUM(ctl.tax_amount) = 0, 0, SUM(ctl.tax_amount)) FROM chsone_tax_log AS ctl WHERE ctl.invoice_number = unitInvoice.invoice_number) AS tax_amount')
        ->orderBy('icb.id', 'DESC');

        $result = $obj->get();

        $this->data = $this->format($result->toArray());
    }
}
