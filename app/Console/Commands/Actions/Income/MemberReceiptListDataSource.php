<?php

namespace App\Console\Commands\Actions\Income;

use Illuminate\Console\Command;
use App\Console\Commands\Action;

class MemberReceiptListDataSource extends Action
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'datasource:MemberReceiptList {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Get the member receipts list';

    protected $formatter =  [
        'id' => '',
        'unit_id' => '',
        'invoice_number' => '',
        'receipt_number' => '',
        'bill_type' => '',
        'payment_mode' => '',
        'received_from' => '',
        'payment_amount' => '',
        'status' => '',
        'transaction_status' => '',
        'payment_date' => '',
        'payment_instrument' => '',
        'transaction_reference' => '',
        'soc_building_name' => '',
        'unit_flat_number' => '',
        'society_unit_name' => 'concat:soc_building_name,unit_flat_number'
    ];

    protected $formatterByKeys =  ['id'];

    protected $mapper = [
        'id' => 'id',
    ];

    /**
     * Execute the console command.
     */
    public function apply()
    {
        $unitId = $this->input['unit_id'];
        $receipt_number = $this->input['filters']['receipt_number'] ?? '';
        $status = isset($this->input['filters']['status']) ? explode(',', $this->input['filters']['status']) : [];
        $payment_mode  = isset($this->input['filters']['payment_mode']) ? explode(',', $this->input['filters']['payment_mode']) : [];

        // check if unit id is valid or not from chsone_units_master table
        $unit = $this->tenantDB()->table('chsone_units_master')
            ->where('unit_id', $unitId)
            ->get();
        
        if(count($unit) == 0) {
            $this->status = 'error';
            $this->message = 'Please provide a valid unit id';
            $this->statusCode = 400;
            $this->data = [];
            return;
        }

        $obj = $this->tenantDB()->table('income_invoice_payment_tracker AS ipt')
            ->select('ipt.id', 'ipt.unit_id', 'ipt.receipt_number', 'ipt.bill_type', 'ipt.payment_mode', 'ipt.received_from', 'ipt.payment_amount', 'ipt.status', 'ipt.transaction_status', 'ipt.payment_date', 'ipt.payment_instrument', 'soc_building_name', 'unit_flat_number')
            ->leftJoin('chsone_units_master AS cum', 'ipt.unit_id', '=', 'cum.unit_id')
            ->whereNotIn('ipt.status', ['N', 'reversed'])
            ->where('ipt.unit_id', $unitId)
            ->orderBy('ipt.receipt_number', 'desc')
            ->orderBy('ipt.payment_date', 'desc')
            ->selectRaw("IF(ipt.invoice_number IS NULL or ipt.invoice_number = '', 'NA', ipt.invoice_number) as invoice_number")
            ->selectRaw("IF(ipt.transaction_reference IS NULL or ipt.transaction_reference = '', 'NA', ipt.transaction_reference) as transaction_reference");


            if ($receipt_number) {
                $obj = $obj->where('ipt.receipt_number', 'like', '%' . $receipt_number . '%');
            }

            if ($status) {
                $obj = $obj->whereIn('ipt.status', $status);
            }


            if($payment_mode){
                $obj = $obj->whereIn('ipt.payment_mode', $payment_mode);
            }
        
        $result = $obj->get();

        $this->data = $this->format($result->toArray());


        //fetch credit balance of unit
        $creditQuery = "SELECT 
            SUM(amount) AS cr_amount
        FROM
        chsone_credit_accounts
        WHERE
            transaction_type = 'cr'
                AND is_locked != 1
                AND account_context = 'unit'
                AND account_id = $unitId;";

        $credit = $this->tenantDB()->select($creditQuery);

        $debitQuery = "SELECT 
            SUM(amount) AS dr_amount
        FROM
        chsone_credit_accounts
        WHERE
            transaction_type = 'dr'
                AND is_locked != 1
                AND account_context = 'unit'
                AND account_id = $unitId;";

        $debit = $this->tenantDB()->select($debitQuery);

        // calculate credit balance
        $this->meta['info'] = [
            'title' => 'Credit Balance',
            'credit_balance' => '₹ '. number_format($credit[0]->cr_amount - $debit[0]->dr_amount, 2)
        ];
    }

    public function concat($soc_building_name, $unit_flat_number)
    {
        return $unit_flat_number . ' / ' . $soc_building_name;
    }
}
