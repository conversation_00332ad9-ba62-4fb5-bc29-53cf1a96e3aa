<?php

namespace App\Console\Commands\Actions\Income;

use Illuminate\Console\Command;
use App\Console\Commands\Action;
use App\Models\Tenants\IncomeCommonBillingCharge;
use Illuminate\Support\Facades\DB;

class fetchDataForPayCommonBillDataSource extends Action
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'datasource:fetchDataForPayCommonBill {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Fetch data for pay common bill data source.';

    protected $formatter = [
        'id' => '',
        'soc_id' => '',
        'member_id' => '',
        'unit_id' => '',
        'invoice_number' => '',
        'billing_type' => '',
        'from_date' => '',
        'to_date' => '',
        'amount' => '',
        'soc_building_name' => '',
        'unit_flat_number' => '',
        'member_first_name' => '',
        'member_last_name' => '',
        'particular' => '',
        'payment_amount' => '',
        'tax_amount' => '',
        'unit_number' => 'getUnitNumber:soc_building_name,unit_flat_number',
        'member_name' => 'getMemberName:member_first_name,member_last_name',
        'received_from' => 'getMemberName:member_first_name,member_last_name',
        'total_due_amount' => 'getTotalDueAmount:amount,tax_amount,payment_amount',
        'invoice_amount' => 'getTotalDueAmount:amount,tax_amount,payment_amount',
    ];

    protected $formatterByKeys = ['id'];

    protected $mapper = [
        'id' => 'id',
    ];

    /**
     * Execute the console command.
     */
    public function apply()
    {
        $id = $this->input['id'];
        
        // check if invoice id is valid or not from income_common_billing_charges table
        $incidentalBillDetails = $this->tenantDB()->table('income_common_billing_charges')
            ->where('id', $id)
            ->get();
        
        if(count($incidentalBillDetails) == 0) {
            $this->status = 'error';
            $this->message = 'Please provide a valid invoice id';
            $this->statusCode = 400;
            $this->data = [];
            return;
        }

        // $get_latest_name = $this->input['get_latest_name'];
        $soc_id = $this->input['company_id'];

        $obj = $this->tenantDB()->table('income_common_billing_charges as icb')
        ->select(
            'icb.id',
            'icb.soc_id',
            'icb.fk_member_id as member_id',
            'icb.fk_unit_id as unit_id',
            'icb.invoice_number',
            'icb.billing_type',
            'icb.from_date',
            'icb.to_date',
            'icb.amount',
            'units.soc_building_name',
            'units.unit_flat_number',
            // 'members.member_first_name',
            // 'members.member_last_name',
            'particular',
        )
        ->leftJoin('chsone_units_master as units', 'icb.fk_unit_id', '=', 'units.unit_id')
        // ->leftJoin('chsone_members_master as members', 'icb.fk_member_id', '=', 'members.id')
        ->leftJoin('income_common_area_charges as icac', 'icb.billing_type', '=', 'icac.id')
        ->leftJoin('income_common_billing_payment as icbp', 'icb.id', '=', 'icbp.fk_common_bill_id')
        ->leftJoin('income_unit_invoices as unitInvoice', 'icb.invoice_number', '=', 'unitInvoice.invoice_number')
        ->where('icb.id', '=', $id)
        ->where('icb.soc_id', '=', $soc_id)
        ->selectRaw('IF(SUM(icbp.payment_amount) = 0, 0, SUM(icbp.payment_amount)) AS payment_amount')
        ->selectRaw('(SELECT IF(SUM(ctl.tax_amount) = 0, 0, SUM(ctl.tax_amount)) FROM chsone_tax_log AS ctl WHERE ctl.invoice_number = unitInvoice.invoice_number) AS tax_amount');

        $result = $obj->get();

        // now fetch max member details according to the $incidentalBillDetails[0]['fk_unit_id'] and pass into the $result array
        if(!empty($incidentalBillDetails[0]->fk_unit_id)) {
            $unit_id = $incidentalBillDetails[0]->fk_unit_id;
            $arrCommonBillingCharges = $this->tenantDB()->table('income_common_billing_charges as icb')
            ->join('chsone_members_master as memmaster', function ($join) {
                $join->on('memmaster.id', '=', DB::raw('(
                    SELECT MAX(icb_sub.fk_member_id)
                    FROM income_common_billing_charges as icb_sub
                    WHERE icb_sub.fk_unit_id = icb.fk_unit_id
                )'));
            })
            ->where('icb.fk_unit_id', $unit_id)
            ->where('icb.soc_id', $soc_id)
            ->select('memmaster.member_first_name', 'memmaster.member_last_name')
            ->selectRaw('CONCAT(member_first_name, " ", member_last_name) as member_name')
            ->selectRaw('CONCAT(member_first_name, " ", member_last_name) as received_from')
            ->first();

            if(!empty($arrCommonBillingCharges)) {
                $result[0]->member_first_name = $arrCommonBillingCharges->member_first_name;
                $result[0]->member_last_name = $arrCommonBillingCharges->member_last_name;
                $result[0]->member_name = $arrCommonBillingCharges->member_name;
                $result[0]->received_from = $arrCommonBillingCharges->received_from;
            }
        }
        $result = $this->format($result->toArray());
        $this->data = $result[0];
    }

    public function getUnitNumber($soc_building_name, $unit_flat_number)
    {
        return $soc_building_name . '-' . $unit_flat_number;
    }

    public function getMemberName($member_first_name, $member_last_name)
    {
        return $member_first_name . ' ' . $member_last_name;
    }

    public function getTotalDueAmount($amount, $tax_amount, $payment_amount)
    {
        return (float)$amount + (float)$tax_amount - (float)$payment_amount;
    }
    
}
