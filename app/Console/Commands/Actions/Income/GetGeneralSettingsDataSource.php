<?php

namespace App\Console\Commands\Actions\Income;

use Illuminate\Console\Command;
use App\Console\Commands\Action;

class GetGeneralSettingsDataSource extends Action
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'datasource:GetGeneralSettings {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Get list of Members';

    protected $formatter =  [];

    protected $formatterByKeys =  [];

    protected $smartFormatter = [];

    protected $mapper = [];

    /**
     * Execute the console command.
     */

    public function apply()
    {
        $soc_id = request()->query()['company_id'];
        $incomeinvoicesetting = $this->tenantDB()->table('income_invoice_general_settings')
            ->get()
            ->toArray();

        $incomeinvoicesettingArray = json_decode(json_encode($incomeinvoicesetting), true);
        foreach ($incomeinvoicesettingArray as $key => $value) {
            $final_array[$value['setting_key']] = $value['setting_value'];
        }
        return $this->data = $final_array;
        
    }
}
