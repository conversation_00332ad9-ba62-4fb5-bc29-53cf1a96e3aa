<?php

namespace App\Console\Commands\Actions\Income;

use Illuminate\Support\Facades\DB;
use App\Console\Commands\Action;
use App\Models\Tenants\IncomeNonmemberBillPayment;
use App\Models\Tenants\NonMemberIncomePayment;
use Illuminate\Support\Facades\Route;

class NonMemberBillListDueDataSource extends Action
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'datasource:NonMemberBillListDue {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Get the non member bill list due';

    /**
     * Execute the console command.
     */
    public function apply()
    {
        $obj = $this->tenantDB()->table('income_nonmember_bills AS inb')
        ->leftJoin(DB::raw('(
            SELECT 
                SUM(payment_amount) AS payment_amount,
                SUM(tax_deducted) AS tax_deducted,
                SUM(tds_deducted) AS tds_deducted,
                fk_nonmember_bill_id
            FROM income_nonmember_bill_payments
            WHERE soc_id = '.$this->input['company_id'].'
            GROUP BY fk_nonmember_bill_id
        ) AS inbp'), 'inb.nonmember_bill_id', '=', 'inbp.fk_nonmember_bill_id')
        ->selectRaw("
            ROUND((
                SUM(bill_amount) + 
                SUM(total_taxes)
            ) - (
                SUM(discount_amount) + 
                SUM(advance_amount) + 
                (
                    COALESCE(SUM(inbp.payment_amount), 0) + 
                    COALESCE(SUM(inbp.tds_deducted), 0) - 
                    SUM(advance_amount)
                ) + 
                SUM(credit_amount)
            ), 2) AS total_due
        ")->first();

        
        $result = $obj;
        $this->data = $result;
    }
}
