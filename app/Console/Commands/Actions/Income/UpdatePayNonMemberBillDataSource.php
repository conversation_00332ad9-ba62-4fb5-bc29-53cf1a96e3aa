<?php

namespace App\Console\Commands\Actions\Income;

use App\Console\Commands\Action;
use App\Models\Tenants\ChsoneGrpLedgerTree;
use App\Models\Tenants\ChsoneLedgerTransaction;
use App\Models\Tenants\SocAccountFinancialYearMaster;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Config;

class UpdatePayNonMemberBillDataSource extends Action
{

    protected $constants;

    public function __construct()
    {
        parent::__construct();
        $this->constants = Config::get("constants");
    }

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'datasource:updatePayNonMemberBill {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */

    public function apply()
    {
        $arroptions = [];
        $this->input['user_id'] = $this->input['user_id'] ?? 0;
        // $billNumber = $this->input['id'] ?? '';
        $this->input['bill_number'] = $billNumber = $this->input['invoice_number'];
        $data = $this->input;
        $data = json_decode(json_encode($data), true);

        $data['soc_id'] = $data['company_id'];
        // Fetch unpaid income details
        $arrNonMemberIncome = $this->getNonmemberUnpaidIncomeDetail($data);

        if (empty($arrNonMemberIncome)) {
            // Session::flash('err_msg', 'No record found.');
            $this->message = 'Bill number not found';
            $this->status = 'error';
            $this->statusCode = 404;
            $this->data = [];
            return;
            // return redirect()->route('income.nonmember');
        }


        // Fetch payment details

        if (!empty($arrNonMemberIncome)) {
            $arrPaymentDetail = $this->getNonmemberPaymentDetail($arrNonMemberIncome);
            if (!empty($arrPaymentDetail)) {
                $totalPaidAmount = (float) $arrPaymentDetail['payment_amount'] + $arrPaymentDetail['tds_deducted'];
            }
        }

        if (!empty($totalPaidAmount) && $totalPaidAmount > 0) {
            $totalPaidAmount = (float) round($totalPaidAmount - $arrNonMemberIncome['advance_amount'], 2);
        }

        // Prepare options for form view
        $arroptions = $this->preparePaymentOptions($billNumber, $arrNonMemberIncome, $totalPaidAmount);

        $generalsettingparameter = $this->getGeneralSetting($data);

        $arroptions['generalsetting'] = $generalsettingparameter;
        // Fetch bank and cash accounts
        $arrAccountDetail = $this->getBankCashAccountDetail($data['soc_id']);
        $arrLedgerAccountDetail = $this->getLedgerAccountDetail(array('account_detail' => $arrAccountDetail, 'default_bank_nonmember' => true)); //get all Unit details
        if (empty($arrLedgerAccountDetail)) {
            $arrLedgerAccountDetail = $this->getLedgerAccountDetail(array('account_detail' => $arrAccountDetail)); //get all Unit details
        }


        $showBankAccount = 0;
        if (!empty($arrLedgerAccountDetail['arrBank'])) {
            $arroptions['bank_account'] = $arrLedgerAccountDetail['arrBank'];
            $arroptions['bank_selected'] = $arrLedgerAccountDetail['bank']['ledger_id'];
            if (count($arrLedgerAccountDetail['arrBank']) > 1) {
                $showBankAccount = 1;
            }
        }

        $totalDueAmount = (float) round($arroptions['due_amount'] - $arroptions['advance_amount'] - $arroptions['partial_paid_amount'], 3);

        $data['bill_type'] = 'nonmember';
        if (!empty($data['tds_amount']) && $data['tds_amount'] > 0) {
            $data['payment_amount'] = (float) $data['payment_amount'] + $data['tds_amount'];
        }
        //Getting extra paid amount of nonmember for credit note
        if ($data['payment_amount'] > $totalDueAmount) {
            $data['extraPaidAmount'] = (float) round($data['payment_amount'] - $totalDueAmount, 3);
        }

        $data['arrCloseAccountPayment'] = $this->getClosedAccountDetailByDate(array('soc_id' => $data['soc_id'], 'bill_date' => $data['payment_date']));
        $arrListnerData = [];

        if (!empty($arrNonMemberIncome['nonmember_id'])) {

            $arrNonmemberMasterDetail = $this->getNonmemberDetail(array('soc_id' => $data['soc_id'], 'nonmember_id' => $arrNonMemberIncome['nonmember_id']));
            $arrNonmemberMasterDetail = json_decode(json_encode($arrNonmemberMasterDetail), true);

            if (!empty($arrNonmemberMasterDetail['nonmember_ledger_id'])) {
                $data['ledger_id'] = $arrNonmemberMasterDetail['nonmember_ledger_id'];
                $arrBookerLedgerDetails = $this->checkledgerExistNew($data);

            }
        } else {
            $arrListnerData['soc_id'] = $data['soc_id'];
            $arrListnerData['ledger_name'] = 'Nonmember Income';
            $arrListnerData['context'] = SUNDRY_DEBTORS_GROUP;

            $arrBookerLedgerDetails = $this->createNonmemberLedgerExit($arrListnerData);
        }

        // Initialize payment data array
        $arrListnerPaymentData = [];

        // Set payment details
        $data['payment_type'] = 'receipt';
        $data['bill_number'] = $data['member_paid_invoice'] = $billNumber;
        $data['narration'] = ""; // Assign narration if available
        if($data['payment_mode'] == 'cheque' || $data['payment_mode'] == 'cashtransfer') {
            $data['transaction_reference'] = !empty($data['cheque_number'] ?? '') ? $data['cheque_number'] : (!empty($data['transaction_reference'] ?? '') ? $data['transaction_reference'] : '');   
        } else {
            $data['transaction_reference'] = '-';
        }
        $data['payment_instrument'] = isset($data['payer_bank_details']) ? $data['payer_bank_details'] : (isset($data['bank_and_branch_name']) ? $data['bank_and_branch_name'] : '');

        // Set invoice details and authentication data
        $arrListnerPaymentData['invoice_details'] = $data;
        // $arrListnerPaymentData['auth'] = $this->session->get('auth');
        $arrListnerPaymentData['nonmember_invoice_id'] = $arrNonMemberIncome['nonmember_bill_id'];
        // Prepare payment tracker data
        $arrPaymentTrackerData = $arrListnerPaymentData['invoice_details'];

        // Generate receipt number and payment token
        $arrPaymentTrackerData['receipt_number'] = $this->generate_receipt_id(
            ['soc_id' => $data['soc_id']]
        );

        $arrPaymentTrackerData['payment_token'] = $paymentToken = $this->generatePaymentToken(
            ['arrPaymentTracker' => $arrPaymentTrackerData]
        );

        $arrPaymentTrackerData['user_id'] = $this->input['user_id'];
        $arrPaymentTrackerData['nonmember_bill_id'] = $arrNonMemberIncome['nonmember_bill_id'];

        // Save invoice payment tracker
        $arrResponseTracker = $this->saveInvoicePaymentTracker(
            ['soc_id' => $data['soc_id'], 'postData' => $arrPaymentTrackerData]
        );

        $isTokenValid = $this->paymentTokenVerification(array('soc_id' => $data['soc_id'], 'paymentToken' => $paymentToken)); //get all Unit details

        if ($isTokenValid && !empty($arrResponseTracker['payment_tracker_id'])) {
            $this->updateIncomeInvoicePaymentTrackerStatus(array('soc_id' => $data['soc_id'], 'updated_by' => $this->input['user_id'], 'payment_tracker_id' => $arrResponseTracker['payment_tracker_id'], 'transaction_status' => 'in_process'));
            if (!empty($arrResponseTracker)) {
                // Check if the payment mode exists and is not one of the modes that require clearance
                $paymentMode = strtolower($arrListnerPaymentData['invoice_details']['payment_mode']) ?? '';

                if ($paymentMode && !in_array($paymentMode, $this->constants['payment_mode_for_clearance'])) {
                    // Code to execute when payment mode is not for clearance

                    $arrListnerPaymentData['soc_id'] = $data['soc_id'];
                    $arrListnerPaymentData['user_id'] = $this->input['user_id'];
                    $addmemberincomepayment = $this->addadvancepayment($arrListnerPaymentData);

                    if ($addmemberincomepayment['success'] == true) {
                        if ($data['payment_amount'] > 0) {
                            $payment_status = 'paid';
                            if ($data['payment_amount'] < $data['due_amount']) {
                                $payment_status = 'partialpaid';
                            }

                            //Update only if payment status is not same
                            if (strtolower($arrNonMemberIncome['payment_status']) != strtolower($payment_status)) {

                                $updateIncomeStatus = $this->updateNonmemberIncomeStatus(array('soc_id' => $data['soc_id'], 'status' => $payment_status, 'bill_number' => $data['bill_number']));

                            }

                            //Update only in case of extra payment
                            if (!empty($data['extraPaidAmount']) && $data['extraPaidAmount'] > 0) {
                                $arrCreditData['soc_id'] = $data['soc_id'];
                                $arrCreditData['account_id'] = $arrNonMemberIncome['nonmember_id'];
                                $arrCreditData['account_context'] = 'nonmember';
                                $arrCreditData['transaction_type'] = 'cr';
                                $arrCreditData['context'] = 'system';
                                $arrCreditData['credit_used_type'] = 'adjustable';
                                $arrCreditData['used_for'] = '';
                                $arrCreditData['payment_date'] = !empty($data['payment_date']) ? $data['payment_date'] : $this->getCurrentDate('display');
                                $arrCreditData['narration'] = 'Amount Rs ' . $data['extraPaidAmount'] . ' has credited from Advance of ' . $data['bill_number'];
                                $arrCreditData['payment_amount'] = $data['extraPaidAmount'];
                                $arrCreditData['account_name'] = $arrNonMemberIncome['billed_name'];
                                $arrResponse = $this->saveCreditAccountResponse(array('process' => 'fetch', 'soc_id' => $data['soc_id'], 'id' => '', 'data' => $arrCreditData, 'user' => $this->input['user_id']));

                            }
                        }
                        $data['soc_id'] = $arrPaymentTrackerData['soc_id'];
                        $data['receipt_number'] = $arrPaymentTrackerData['receipt_number'];
                        $data['payment_tracker_id'] = $arrResponseTracker['payment_tracker_id'];
                        $arrIncomeAccounts = [];
                        $intBookerLedgerDetails = $this->payNonmemberBillLedger(array('data' => $data, 'arrBookerLedgerDetails' => $arrBookerLedgerDetails, 'arrIncomeAccounts' => $arrIncomeAccounts));
                    }
                } else {

                    $this->updateIncomeInvoicePaymentTrackerStatus(array('soc_id' => $data['soc_id'], 'updated_by' => $this->input['user_id'], 'payment_tracker_id' => $arrResponseTracker['payment_tracker_id'], 'status' => 'P', 'transaction_status' => 'complete'));

                    $this->message = 'payment is succesfully added to tracker waiting for clearance.';
                    $this->status = 'success';
                    $this->data = [];
                    return;
                    // $this->soc_db_w->commit();
                    // $successmsg = "payment is succesfully added to tracker waiting for clearance.";
                    // $this->session->set("succ_msg", $successmsg);
                    // return $this->response->redirect($this->config->system->full_base_url . "income-details/incomenonmember?page=" . $page);
                }
            }
        } else {
            if (!empty($arrResponseTracker['payment_tracker_id'])) {
                $this->updateIncomeInvoicePaymentTrackerStatus(array('soc_id' => $data['soc_id'], 'updated_by' => $this->input['user_id'], 'payment_tracker_id' => $arrResponseTracker['payment_tracker_id'], 'status' => 'N', 'transaction_status' => 'complete'));
            }
            $this->message = 'Payment transaction failed';
            $this->data = [];
        }

        if (!empty($intBookerLedgerDetails)) {

            //echo 'rajeshw';exit;
            $trackertStatus = 'Y';
            if (in_array(strtolower($data['payment_mode']), $this->constants['payment_mode_for_clearance'])) {
                $trackertStatus = 'P';
            }
            if (!empty($arrResponseTracker['payment_tracker_id'])) {
                $this->updateIncomeInvoicePaymentTrackerStatus(array('soc_id' => $data['soc_id'], 'updated_by' => $this->input['user_id'], 'payment_tracker_id' => $arrResponseTracker['payment_tracker_id'], 'status' => $trackertStatus, 'transaction_status' => 'complete'));
            }
            $this->message = 'Payment done successfully';
            $this->status = 'success';
            $this->data = [];
            return;

        } else {

            $this->message = 'Unbale to complete transaction, Please try later.';
            $this->status = 'error';
            $this->data = [];
            if (!empty($arrResponseTracker['payment_tracker_id'])) {
                $this->updateIncomeInvoicePaymentTrackerStatus(array('soc_id' => $data['soc_id'], 'updated_by' => $this->input['user_id'], 'payment_tracker_id' => $arrResponseTracker['payment_tracker_id'], 'status' => 'N', 'transaction_status' => 'complete'));
            }
        }

        /**
         * Handles the rollback of a transaction and updates the payment tracker status.
         *
         * @param string $paymentTrackerId
         * @param array $auth
         * @param string $status
         */

    }

    public function calculateTotalPaidAmount($paymentDetails, $nonMemberIncome)
    {
        $totalPaidAmount = 0;
        if (!empty($paymentDetails)) {
            $totalPaidAmount = (float) $paymentDetails['payment_amount'] + $paymentDetails['tds_deducted'];
            if (!empty($totalPaidAmount) && $totalPaidAmount > 0) {
                $totalPaidAmount = round($totalPaidAmount - $nonMemberIncome['advance_amount'], 2);
            }
        }
        return $totalPaidAmount;
    }

    public function preparePaymentOptions($billNumber, $nonMemberIncome, $totalPaidAmount)
    {
        return [
            'bill_number' => $billNumber,
            'booker_name' => $nonMemberIncome['billed_name'],
            'bill_total_amount' => round($nonMemberIncome['bill_amount'], 2),
            'discount_amount' => round($nonMemberIncome['discount_amount'], 2),
            'advance_amount' => round($nonMemberIncome['advance_amount'] + $nonMemberIncome['credit_amount'], 2),
            'partial_paid_amount' => $totalPaidAmount,
            'tax_deduction' => round($nonMemberIncome['total_taxes'], 2),
            'due_amount' => round($nonMemberIncome['bill_amount'] - $nonMemberIncome['total_deduction'] + $nonMemberIncome['total_taxes'] - $totalPaidAmount - $nonMemberIncome['advance_amount'] - $nonMemberIncome['credit_amount'], 2),
        ];
    }

    public function setupBankAccounts($ledgerAccountDetail, &$options)
    {
        $showBankAccount = 0;
        if (!empty($ledgerAccountDetail['arrBank'])) {
            $options['bank_account'] = $ledgerAccountDetail['arrBank'];
            $options['bank_selected'] = $ledgerAccountDetail['bank']['ledger_id'];
            if (count($ledgerAccountDetail['arrBank']) > 1) {
                $showBankAccount = 1;
            }
        }
        return $showBankAccount;
    }

    public function adjustPaymentData(&$postData, $totalDueAmount)
    {
        if (!empty($postData['tds_amount']) && $postData['tds_amount'] > 0) {
            $postData['payment_amount'] += $postData['tds_amount'];
        }
        if ($postData['payment_amount'] > $totalDueAmount) {
            $postData['extraPaidAmount'] = round($postData['payment_amount'] - $totalDueAmount, 3);
        }
    }

    public function getNonmemberUnpaidIncomeDetail(array $data): array
    {

        // Fetch the unpaid or partially paid income details based on the provided criteria
        $nonmemberIncome = $this->tenantDB()->table('income_nonmember_bills')
            ->where('soc_id', $data['soc_id'])
            ->whereIn('payment_status', ['unpaid', 'partialpaid'])
            ->where(function ($query) use ($data) {
                // Checking for bill_number or nonmember_bill_id
                $query->where('bill_number', $data['bill_number'] ?? '')
                    ->orWhere('nonmember_bill_id', $data['nonmember_bill_id'] ?? '');
            })
            ->first();

        // Convert the result to an array if a record is found, otherwise return an empty array
        return $nonmemberIncome ? (array) $nonmemberIncome : [];
    }

    public function getNonmemberPaymentDetail($data)
    {
        // Initialize the payment details array with default values
        $arrPaymentDetail = [
            'payment_amount' => 0,
            'tax_deducted' => 0,
            'tds_deducted' => 0,
            'discount_amount' => 0,
        ];

        // Fetch all payment records based on the provided soc_id and nonmember_bill_id
        $nonmemberIncomePayments = $this->tenantDB()->table('income_nonmember_bill_payments')->where('soc_id', $data['soc_id'])
            ->where('fk_nonmember_bill_id', $data['nonmember_bill_id'])
            ->get();

        // Iterate through each payment record and accumulate the payment details
        foreach ($nonmemberIncomePayments as $payment) {
            $arrPaymentDetail['bill_number'] = $payment->bill_number;
            $arrPaymentDetail['payment_amount'] += round((float) $payment->payment_amount, 2);
            $arrPaymentDetail['tax_deducted'] += round((float) $payment->tax_deducted, 2);
            $arrPaymentDetail['tds_deducted'] += round((float) $payment->tds_deducted, 2);
            $arrPaymentDetail['discount_amount'] += round((float) $payment->discount_amount, 2);
        }

        return $arrPaymentDetail;
    }
    public function getGeneralSetting(array $data): array
    {
        // Fetch all settings using public method
        $finalArray = $this->getAllSettings($data);

        // Retrieve the latest income invoice setting based on the effective date
        $incomeInvoiceSetting = $this->tenantDB()->table('income_invoice_settings')->where('soc_id', $data['soc_id'])
            ->orderBy('effective_date', 'DESC')
            ->first();

        // Check if income invoice setting exists and populate the final array with relevant data
        if ($incomeInvoiceSetting) {
            $finalArray['generalsettingid'] = $incomeInvoiceSetting->id;
            $finalArray['invoicing_frequency'] = $incomeInvoiceSetting->invoicing_frequency ?? '';
            $finalArray['effective_date'] = $incomeInvoiceSetting->effective_date;
        }

        // Add general setting key IDs to the final array
        $finalArray['general_setting_key_ids'] = $this->getAllSettingWithId($data);

        return $finalArray;
    }

    public function getAllSettings($data)
    {
        $finalArray = [];

        // Fetch general settings based on soc_id
        $incomeInvoiceGeneralSettings = $this->tenantDB()->table('income_invoice_general_settings')->where('soc_id', $data['soc_id'])->get();

        // Populate the final array with setting key-value pairs
        foreach ($incomeInvoiceGeneralSettings as $setting) {
            $finalArray[$setting->setting_key] = $setting->setting_value;
        }

        return $finalArray;
    }

    public function getAllSettingWithId(array $data): array
    {
        $finalArray = [];

        // Fetch all general settings with their IDs based on soc_id
        $incomeInvoiceGeneralSettings = $this->tenantDB()->table('income_invoice_general_settings')->where('soc_id', $data['soc_id'])->get();

        // Populate the final array with setting key and corresponding IDs
        foreach ($incomeInvoiceGeneralSettings as $setting) {
            $finalArray[$setting->setting_key] = $setting->id;
        }

        return $finalArray;
    }

    public function getBankCashAccountDetail($soc_id = [])
    {
        // Define the base query to fetch account and ledger details
        $query = $this->tenantDB()->table('chsone_grp_ledger_tree as grpLedger')
            ->select(
                'grpLedger.ledger_account_id',
                'grpLedger.ledger_account_name',
                'grpLedger.context',
                'account.default_account',
                'account.default_bank_for_incidental',
                'account.default_bank_for_nonmember',
                'account.bank_name',
                'account.account_number',
                'account.bank_address',
                'account.bank_city',
                'account.bank_ifsc',
                'account.account_id'
            )
            ->join('chsone_accounts_master as account', 'grpLedger.ledger_account_id', '=', 'account.ledger_account_id')
            ->where('grpLedger.soc_id', $soc_id)
            ->where('grpLedger.entity_type', 'ledger')
            ->where('grpLedger.status', 1);

        // Modify the query based on 'only_bank' parameter
        if (!empty($data['only_bank'])) {
            $query->where('grpLedger.context', 'bank');
        } else {
            $query->whereIn('grpLedger.context', ['cash', 'bank']);
        }

        // Execute the query and convert the results to an array
        $accountDetails = $query->get()->toArray();

        return $accountDetails;
    }

    public function getLedgerAccountDetail(array $data): array
    {
        $arrAccountDetail = [
            'cash' => [],
            'bank' => [],
            'arrBank' => [],
            'arrCash' => [],
        ];

        $data = json_decode(json_encode($data), true);
        // Check if account details are provided
        if (!empty($data['account_detail'])) {

            foreach ($data['account_detail'] as $eachAccountDetail) {

                $context = strtolower($eachAccountDetail['context']);
                $ledgerId = $eachAccountDetail['ledger_account_id'];
                $ledgerName = $eachAccountDetail['ledger_account_name'];

                // Handle cash account details
                if ($context === 'cash' && empty($arrAccountDetail['cash'])) {
                    $arrAccountDetail['cash'] = [
                        'ledger_id' => $ledgerId,
                        'ledger_name' => $ledgerName,
                    ];
                    $arrAccountDetail['arrCash'][$ledgerId] = $ledgerName;
                }

                // Handle bank account details
                if ($context === 'bank') {
                    if ($eachAccountDetail['default_account'] == 1) {
                        $arrAccountDetail['bank'] = [
                            'ledger_id' => $ledgerId,
                            'ledger_name' => $ledgerName,
                        ];
                    }
                    $arrAccountDetail['arrBank'][$ledgerId] = $ledgerName;

                    // Default bank for incidental cases
                    if (!empty($data['default_bank_incidental']) && $eachAccountDetail['default_bank_for_incidental'] == 1) {
                        $arrAccountDetail['bank'] = [
                            'ledger_id' => $ledgerId,
                            'ledger_name' => $ledgerName,
                        ];
                    }

                    // Default bank for non-member cases
                    if (!empty($data['default_bank_nonmember']) && $eachAccountDetail['default_bank_for_nonmember'] == 1) {
                        $arrAccountDetail['bank'] = [
                            'ledger_id' => $ledgerId,
                            'ledger_name' => $ledgerName,
                        ];
                    }
                }
            }

            // Set the first available bank if no default is selected
            if (empty($arrAccountDetail['bank']) && !empty($arrAccountDetail['arrBank'])) {
                $firstBank = reset($arrAccountDetail['arrBank']);
                $firstBankId = key($arrAccountDetail['arrBank']);
                $arrAccountDetail['bank'] = [
                    'ledger_id' => $firstBankId,
                    'ledger_name' => $firstBank,
                ];
            }
        }

        return $arrAccountDetail;
    }

    public function getClosedAccountDetailByDate($data = [])
    {
        // Initialize an empty array to store account master details
        $arrAccountMasterDetail = [];

        // Determine the bill date: use the provided date or set the current date
        $billDate = !empty($data['bill_date'])
        ? $data['bill_date']
        : date('Y-m-d H:i:s');

        // Query to find the closed account details within the financial year date range
        $objAccountMasterDetail = $this->tenantDB()->table('soc_account_financial_year_master')
            ->where('soc_id', $data['soc_id'])
            ->where('closed', 1)
            ->where('confirmed', operator: 1)
            ->where('fy_start_date', '<=', $billDate)
            ->where('fy_end_date', '>=', $billDate)
            ->first();

        // Convert the result to an array if found, otherwise return an empty array
        if (!empty($objAccountMasterDetail)) {
            $arrAccountMasterDetail = (array) $objAccountMasterDetail;
        }

        return $arrAccountMasterDetail;
    }

    public function getNonmemberDetail($data)
    {
        $query = $this->tenantDB()->table('chsone_nonmember_master')->where('soc_id', $data['soc_id']);

        // Check if a specific nonmember_id is provided
        if (!empty($data['nonmember_id'])) {
            $nonmember = $query->where('nonmember_id', $data['nonmember_id'])->first();
        } else {
            $nonmember = $query->get();
        }

        // Convert the result to an array if a record is found, otherwise return an empty array
        return !empty($nonmember) ? (array) $nonmember : [];
    }

    public function checkledgerExistNew($data = [])
    {
        $data = json_decode(json_encode($data), true);
        $arrNonmemberAccounts = [];
        $soc_id = $data['soc_id'];
        $arrClinetLedgerDetails = [];

        $query = ChsoneGrpLedgerTree::where('soc_id', $data['soc_id']);
        
        if (isset($data['ledger_name']) && !empty($data['ledger_name'])) {
            $query->where('context', $data['context'])
                  ->where('entity_type', "ledger")
                  ->where('ledger_account_name', $data['ledger_name']);
    
            if (isset($data['behaviour']) && !empty($data['behaviour'])) {
                $query->where('behaviour', $data['behaviour']);
            }
        } elseif (isset($data['ledger_id']) && !empty($data['ledger_id'])) {
            $query->where('ledger_account_id', $data['ledger_id']);
        }

        // Check if the ledger exists
        $objBookerLedger = $query->first();

        if (!empty($objBookerLedger)) {

            $arrClinetLedgerDetails['recieving_ledger_id'] = $objBookerLedger->ledger_account_id;
            $arrClinetLedgerDetails['receiver_name'] = $objBookerLedger->ledger_account_name;

        } elseif (!empty($data['group_name'])) 
        {
            $name = $data['ledger_name'] ?? '';
            $entity_type = ENTITY_TYPE_LEDGER;
            $grp_ledg_id = "";

            // Fetch parent group ID
            $parent = $this->getParentGroupId($data);
            if (empty($parent->ledger_account_id)) {
                return $arrClinetLedgerDetails;
            }

            $parent_group = $parent->ledger_account_id;

            // Create a new ledger
            $ledger_id = $this->manipulate($name, $entity_type, $grp_ledg_id, $parent_group, "", '', 0, '', '', '', '', '', $soc_id, 0);

            if ($ledger_id) {
                $arrClinetLedgerDetails['recieving_ledger_id'] = $ledger_id;
                $arrClinetLedgerDetails['receiver_name'] = $data['ledger_name'];
            }
        }
        unset($objBookerLedger);
        return $arrClinetLedgerDetails;
    }

    public function getParentGroupId($arrData)
    {
        // Fetch parent group details based on provided criteria
        $parent = ChsoneGrpLedgerTree::where('soc_id', $arrData['soc_id'])
            ->where('ledger_account_name', $arrData['group_name'])
            ->where('entity_type', "group")
            ->where('behaviour', $arrData['behaviour'])
            ->where('context', $arrData['context'])
            ->first();
        if ($parent) {
            return $parent;
        } else {
            return false;
        }
    }

    public function manipulate($name, $entity_type = ENTITY_TYPE_GROUP, $grp_ledg_id = "", $parent_group = 0, $behaviour = "", $ledg_id = '', $opening_balance = 0, $update_led_id = '', $ledger_start_date = '', $ledger_type = '', $context = '', $txn_id = 0, $soc_id = 0, $is_reco = 0)
    {
        $config = Config::get("constants");
        if(empty($soc_id)){
            $soc_id = $this->input['company_id'];
        }
        $dup = $this->_checkGroupNameDuplication($parent_group, $name, $ledg_id, $update_led_id, $soc_id);

        $arrFYDetail = $this->getCurrentFinancialYear(['soc_id' => $soc_id]);
        $arrFYDetail = json_decode(json_encode($arrFYDetail), true);
        $ledger_start_date = $arrFYDetail['fy_start_date'];

        if ($dup == 0) {
            $behaviour = trim(strtolower($behaviour));

            $grp_ledg_tree = new ChsoneGrpLedgerTree();
            
            if ($ledg_id) {
                $grp_ledg_tree = $this->getLedger($ledg_id);
            }

            $grp_ledg_tree->entity_type = $entity_type;
            $grp_ledg_tree->soc_id = $soc_id;
            $grp_ledg_tree->ledger_account_name = $name;
            $grp_ledg_tree->ledger_start_date = $ledger_start_date;
            $grp_ledg_tree->context_ref_id = 0;

            if (!empty($ledger_type) && $ledger_type != "NULL" && $ledger_type != '') {
                $grp_ledg_tree->operating_type = $ledger_type;
            } else {
                $grp_ledg_tree->operating_type = '';
            }

            if (!empty($grp_ledg_id)) {
                $grp_ledg_tree->ledger_account_id = $grp_ledg_id;
            }

            if ($update_led_id != '') {
                $grp_ledg_tree->ledger_account_id = $update_led_id;
            }
            if (!empty($parent_group)) {
                $grp_ledg_tree->parent_id = $parent_group;
                $ledger_props = $this->_getLedgerProps($parent_group);
            } else {
                $grp_ledg_tree->parent_id = 0;
            }

            if (!empty($behaviour)) {
                $grp_ledg_tree->behaviour = $behaviour;
                $grp_ledg_tree->nature_of_account = $config->nature_account->$behaviour;
                $grp_ledg_tree->report_head = $config->report_head->$behaviour;
            } else {
                $grp_ledg_tree->behaviour = $ledger_props["behaviour"];
                $grp_ledg_tree->nature_of_account = $ledger_props["nature_account"];
                $grp_ledg_tree->report_head = $ledger_props["report_head"];
            }

            if (!empty($context) && $context != '') {
                $grp_ledg_tree->context = $context;
            } else {
                $grp_ledg_tree->context = $ledger_props["context"];
            }
            $grp_ledg_tree->defined_by = "user";
            $grp_ledg_tree->status = 1;
            $grp_ledg_tree->added_on = date("Y-m-d H:i:s");
            $grp_ledg_tree->created_by = ($this->input['user_id']) ? $this->input['user_id'] : 0;


            if ($grp_ledg_tree->save()) {
                //Opening balance should not go in case on Income Expense ledger
                if (!in_array(strtolower($grp_ledg_tree->behaviour), array("income", "expense"))) {
                    $txn_date = $ledger_start_date;
                    $narration = 'entry for opening balance';
                    $txn_id = $this->addTxn($grp_ledg_tree->ledger_account_id, $opening_balance, $narration, $txn_date, "", $grp_ledg_tree->nature_of_account, "", "", "", "", $name, $is_opning = 1, $is_reco);
                }

                return $grp_ledg_tree->ledger_account_id;
            } else {
                return false;
            }
        } else {
            return "DUP" . $dup;
        }
    }

    public function getCurrentFinancialYear(array $data = [])
    {
        // Fetch the current financial year details based on the provided conditions
        $arrFYDetail = array();
        $arrFYDetail = SocAccountFinancialYearMaster::where("soc_id", $data['soc_id'])->where("confirmed", 0)->orderBy("account_closing_id", "asc")->first();
        
        if(!empty($arrFYDetail))
        {
            $arrFYDetail = $arrFYDetail->toArray();
        }
        return $arrFYDetail;
    }

    public function _checkGroupNameDuplication($parent, $name, $ledg_id = "", $update_led_id = "", $soc_id)
    {

        $bind[1] = strtolower(trim($name));
        $ledgers_count = 0;
        if ($ledg_id) {
            $ledgers_count = ChsoneGrpLedgerTree::where("soc_id", $soc_id)->where('ledger_account_name', strtolower(trim($name)))->where('ledger_account_id', '!=', $ledg_id)->count();
        }
        if ($update_led_id) {
            $ledgers_count = ChsoneGrpLedgerTree::where("soc_id", $soc_id)->where('ledger_account_name', strtolower(trim($name)))->where('ledger_account_id', '!=', $update_led_id)->count();
        }
        
        return $ledgers_count;
    }
    public function addTxn($ledger_id, $txn_amt, $narration, $txn_date, $voucher_type = VOUCHER_JOURNAL, $txn_type = "", $from_txn_id = "", $pay_mode = "", $pay_ref = "", $opening_balance = "", $ledger_name = "", $is_opning = 1, $is_reco = 0, $soc_id = 0) 
    {
        return $this->_addTransaction($is_reco, $ledger_id, $txn_amt, $narration, $txn_date, $voucher_type, $txn_type, $from_txn_id, $pay_mode, $pay_ref, $ledger_name, $is_opning, $soc_id);
    }

    public function _addTransaction($is_reco = 0, $ledger_id, $txn_amt, $narration, $txn_date, $voucher_type = VOUCHER_JOURNAL, $txn_type = "", $from_txn_id = "", $pay_mode = "", $pay_ref = "", $name = "", $is_opning = "", $other_recp_ref = "", $soc_id = "", $created_by = "", $voucher_reference_id = "", $voucher_reference_number = "", $is_cancelled = 0) 
    {
        $txn = new ChsoneLedgerTransaction();
        if (empty($soc_id)) {
            $soc_id = $this->input['company_id'];
        }

        if (!empty($txn_type) || $txn_type != "") {
            $type_txn = $txn_type;
        } else {
            if ($from_txn_id) {
                $mode     = "to";
                $type_txn = "cr";
            } else {
                $mode     = "from";
                $type_txn = "dr";
            }
        }

        if ($is_opning == 1) {

            $txn_entrt = ChsoneLedgerTransaction::where('soc_id', $soc_id)
            ->where('is_opening_balance', 1)
            ->where('ledger_account_id', $ledger_id)
            ->where('transaction_date', $txn_date)
            ->first();

            $txn->value_date = $txn_date;
            $txn->txn_id = (isset($txn_entrt->txn_id)  && !empty($txn_entrt->txn_id)) ? $txn_entrt->txn_id : '';
            if (!empty($txn->txn_id)) {
                $txn = ChsoneLedgerTransaction::findFirst("txn_id = " . $txn->txn_id);
            }
        }

        $txn->soc_id = $soc_id;
        $txn->transaction_date = $txn_date;
        $txn->ledger_account_id = $ledger_id;
        $txn->ledger_account_name = $name;
        $txn->voucher_type = $voucher_type;
        $txn->transaction_type = $type_txn; //$ledger_name_obj->nature_of_account;//$txn_type;
        $txn->payment_mode = $pay_mode;
        $txn->payment_reference = $pay_ref;
        $txn->transaction_amount = (is_null($txn_amt)) ? $txn->transaction_amount : $txn_amt;
        $txn->other_reference_id = $other_recp_ref;
        $txn->txn_from_id = $from_txn_id;
        $txn->memo_desc = $narration;
        $txn->is_opening_balance = $is_opning;
        $txn->is_reconciled = $is_reco;
        /*Added New Column in transaction*/
        $txn->voucher_reference_number = $voucher_reference_number;
        $txn->voucher_reference_id = $voucher_reference_id;
        $txn->is_cancelled = $is_cancelled;
        /*End New Column in transaction*/
        $txn->created_by = $this->input['user_id'] ?? 0;
        if (isset($created_by) && !empty($created_by)) {
            $txn->created_by = $created_by;
        }
        $txn->added_on = date("Y-m-d H:i:s");

        if ($txn->save()) {
            return $txn->txn_id;
        } else {
            print_r($txn->getMessages);
        }
        return false;
    }

    public function createNonmemberLedgerExit(array $data = [])
    {
        // Initialize the response array
        $arrResponse = [];

        // Check if the ledger already exists
        if (!empty($data)) {
            $arrResponse = $this->checkledgerExistNew($data);
        }

        $context = '';

        // If the ledger does not exist, proceed to create a new one
        if (empty($arrResponse)) {
            // Set up the parent ledger data
            $arrParentLedger = array_merge($data, [
                'ledger_name' => 'Sundry Debtors',
                'context' => SUNDRY_DEBTORS_GROUP,
                'behaviour' => ASSET,
                'entity_type' => ENTITY_TYPE_GROUP,
            ]);

            // Fetch existing parent ledger group details
            $arrParentLedgerDetail = $this->getExistLedgerGroup($arrParentLedger);

            // Check if parent ledger group exists
            if (!empty($arrParentLedgerDetail['recieving_ledger_id'])) {
                // Create a new ledger if it does not exist

                // Attempt to manipulate and create a new ledger
                $ledger_account_id = $this->manipulate(
                    $data['ledger_name'],
                    ENTITY_TYPE_LEDGER,
                    '',
                    $arrParentLedgerDetail['recieving_ledger_id'],
                    ASSET,
                    '',
                    0,
                    '',
                    '',
                    null,
                    $context // Make sure this variable is properly defined
                );

                // Check if the ledger creation was successful and not a duplicate
                if ($ledger_account_id !== false && strpos($ledger_account_id, 'DUP') === false) {
                    $arrResponse['recieving_ledger_id'] = $ledger_account_id;
                    $arrResponse['receiver_name'] = $data['ledger_name'];
                }
            }
        }

        return $arrResponse;
    }

    public function getExistLedgerGroup(array $data = [])
    {
        $arrClinetLedgerDetails = [];
        $auth = $data['auth'];

        // Construct the query condition based on the presence of ledger_name or ledger_id
        $query = $this->tenantDB()->table('chsone_grp_ledger_tree')
            ->where('soc_id', $auth['soc_id']);

        if (isset($data['ledger_name']) && !empty($data['ledger_name'])) {
            $query->where('context', $data['context'])
                ->where('entity_type', $data['entity_type'])
                ->where('ledger_account_name', $data['ledger_name']);

            // Add behaviour condition if provided
            if (isset($data['behaviour']) && !empty($data['behaviour'])) {
                $query->where('behaviour', $data['behaviour']);
            }
        } elseif (isset($data['ledger_id']) && !empty($data['ledger_id'])) {
            $query->where('ledger_account_id', $data['ledger_id']);
        }

        // Fetch the first matching ledger group record
        $objBookerLedger = $query->first();

        // If a ledger is found, populate the response array with its details
        if (!empty($objBookerLedger)) {
            $arrClinetLedgerDetails['recieving_ledger_id'] = $objBookerLedger->ledger_account_id;
            $arrClinetLedgerDetails['receiver_name'] = $objBookerLedger->ledger_account_name;
        }

        return $arrClinetLedgerDetails;
    }

    public function generate_receipt_id($data = [])
    {
        $soc_id = $data['soc_id'];
        $currentDate = date('Y-m-d H:i:s');

        // Prepare listener data for fetching applicable settings
        $arrListenerdataForApplicableTaxes = [
            'soc_id' => $soc_id,
            'effective_date' => $currentDate,
        ];

        // Fetch general settings for income receipt prefix and related data
        $arrGeneralSettings = $this->getAllSettings(
            $arrListenerdataForApplicableTaxes
        );

        // Default receipt number
        $receipt_number = str_pad("1", 5, "0", STR_PAD_LEFT);

        // Generate receipt number if the prefix is set in general settings
        if (isset($arrGeneralSettings['INCOME_RECEIPT_PREFIX'])) {
            $data['prefix'] = $arrGeneralSettings['INCOME_RECEIPT_PREFIX'];
            $getMaxReceiptID = $this->getMaxReceiptID($data);

            // Calculate the next receipt number
            if ($getMaxReceiptID) {
                $maxReceiptNumber = ltrim(ltrim($getMaxReceiptID, $arrGeneralSettings['INCOME_RECEIPT_PREFIX']), "0");
                $receiptnum = (int) $maxReceiptNumber + (int) $arrGeneralSettings['INCOME_RECEIPT_NUMBER_INCREMENT'];
            } else {
                $receiptnum = (int) $arrGeneralSettings['INCOME_RECEIPT_START_NUMBER'];
            }

            // Format the receipt number with padding
            $paddingNum = str_pad($receiptnum, 5, "0", STR_PAD_LEFT);
            $receipt_number = $arrGeneralSettings['INCOME_RECEIPT_PREFIX'] . $paddingNum;
        }

        return $receipt_number;
    }

    public function getMaxReceiptID($data = [])
    {
        // Validate required parameters
        if (empty($data['soc_id']) || empty($data['prefix'])) {
            return false;
        }

        // Use $this->tenantDB() to connect to the database and fetch the maximum receipt number
        $result = $this->tenantDB()->table('income_invoice_payment_tracker')
            ->where('soc_id', $data['soc_id'])
            ->whereRaw('receipt_number REGEXP ?', [$data['prefix'] . '+[0-9]'])
            ->max('receipt_number');

        // Check if a result was returned and fetch the receipt number
        if (!empty($result)) {
            return $result;
        }

        return false;
    }

    public function generatePaymentToken($data = [])
    {
        // Initialize an empty payment token
        $paymentToken = '';

        // Check if payment tracker data is provided
        if (!empty($data['arrPaymentTracker'])) {
            // Extract payment details from the provided data
            $arrPaymentDetail = [
                'soc_id' => $data['arrPaymentTracker']['soc_id'],
                'received_from' => $data['arrPaymentTracker']['received_from'] ?? '',
                'bill_type' => $data['arrPaymentTracker']['bill_type'],
                'payment_amount' => $data['arrPaymentTracker']['payment_amount'],
                'payment_mode' => $data['arrPaymentTracker']['payment_mode'] ?? '',
                'invoice_number' => $data['arrPaymentTracker']['invoice_number'] ?? '',
                'payment_date' => $data['arrPaymentTracker']['payment_date'],
                'vendor_id' => $data['arrPaymentTracker']['vendor_id'] ?? '',
                'vendor_bill_payment_amount' => $data['arrPaymentTracker']['vendor_bill_payment_amount'] ?? '',
                'vendor_bill_type_purchase' => $data['arrPaymentTracker']['vendor_bill_type_purchase'] ?? '',
                'vendor_bill_receipt_number' => $data['arrPaymentTracker']['vendor_bill_receipt_number'] ?? '',
                'vendor_bill_payment_date' => $data['arrPaymentTracker']['vendor_bill_payment_date'] ?? '',
            ];

            // Generate a unique payment token using the serialized payment details
            if (!empty($arrPaymentDetail)) {
                $paymentToken = md5(time() . serialize($arrPaymentDetail));
            }
        }

        // Return the generated payment token
        return $paymentToken;
    }

    public function saveInvoicePaymentTracker($data = array())
    {

        if (isset($data) && !empty($data['soc_id']) && !empty($data['postData'])) {
            $arrOtherInfo = array();
            if (isset($data['postData']) && !empty($data['postData'])) {

                // print_r($data['postData']);
                if (strtolower($data['postData']['bill_type']) == 'member') {
                    $arrOtherInfo["member_detail"]["bank_ledger"] = $data['postData']['bank_account'];
                    if (!empty($data['postData']['writeoff_amount']) && $data['postData']['writeoff_amount'] > 0) {
                        if (!empty($data['postData']['total_particular'])) {
                            for ($i = 1; $i <= $data['postData']['total_particular']; $i++) {
                                $arrOtherInfo["writeoff_detail"]["rule_$i"] = $data["postData"]["rule_$i"];
                                $arrOtherInfo["writeoff_detail"]["particular_$i"] = $data["postData"]["particular_$i"];
                                $arrOtherInfo["writeoff_detail"]["writeoff_amount_$i"] = $data["postData"]["writeoff_amount_$i"];
                            }
                            $arrOtherInfo["writeoff_detail"]["total_particular"] = $data['postData']['total_particular'];
                        }
                    }
                } elseif (strtolower($data['postData']['bill_type']) == 'common_bill') {
                    $arrOtherInfo['common_bill_detail']['common_bill_id'] = $data['postData']['common_bill_id'];
                    $arrOtherInfo['common_bill_detail']['bank_ledger'] = $data['postData']['bank_account'];
                    $arrOtherInfo['common_bill_detail']['bill_type_category'] = $data['postData']['bill_type_category'];
                    $arrOtherInfo['common_bill_detail']['payment_type'] = $data['postData']['payment_type'];
                } elseif (strtolower($data['postData']['bill_type']) == 'nonmember') {
                    $arrOtherInfo['nonmember_detail']['nonmember_bill_id'] = $data['postData']['nonmember_bill_id'];
                    $arrOtherInfo['nonmember_detail']['nonmemberincomeaccount'] = $data['postData']['nonmemberincomeaccount'] ?? '';
                    $arrOtherInfo['nonmember_detail']['narration'] = $data['postData']['narration'];
                    $arrOtherInfo['nonmember_detail']['bank_ledger'] = $data['postData']['bank_account'] ?? $data['postData']['payer_bank_details'] ?? $data['postData']['bank_and_branch_name'] ?? '';
                    if (isset($data['postData']['advance_payment']) && !empty($data['postData']['advance_payment'])) {
                        $arrOtherInfo['nonmember_detail']['advance_payment'] = $data['postData']['advance_payment'];
                    }
                } elseif (strtolower($data['postData']['bill_type']) == 'suspense') {
                    $arrOtherInfo['suspense_detail']['bank_ledger'] = $data['postData']['bank_account'];
                }
            }

            // Initialize the object properties directly in an array
            $objIncomeInvoicePaymentTracker = [
                'created_date' => $this->getCurrentDate('database'),
                'created_by' => $this->input['user_id'] ?? 0,
                'soc_id' => $data['soc_id'] ?? '',
                'unit_id' => $data['unit_id'] ?? '',
                'invoice_number' => $data['postData']['member_paid_invoice'],
                'receipt_number' => $data['postData']['receipt_number'],
                'bill_type' => $data['postData']['bill_type'],
                'payment_mode' => $data['postData']['payment_mode'] ?? '',
                'transaction_reference' => $data['postData']['transaction_reference'] ?? '',
                'payment_instrument' => $data['postData']['payment_instrument'] ?? '',
                'received_from' => $data['postData']['received_from'],
                'transaction_charges' => $data['postData']['transaction_charges'] ?? 0.000,
                'total_due_amount' => $data['postData']['total_unpaid_amount'] ?? 0.000,
                'late_payment_charges' => $data['postData']['late_payment_charges'] ?? 0.000,
                'writeoff_amount' => $data['postData']['writeoff_amount'] ?? 0.000,
                'payment_amount' => $data['postData']['payment_amount'],
                'other_information' => serialize($arrOtherInfo),
                'tds_deducted' => $data['postData']['tds_amount'] ?? 0.000,
                'status' => 'N', // Default status
                'updated_date' => $this->getCurrentDate('database'),
                'updated_by' => 0,
                'payment_date' => $data['postData']['payment_date'] ?? '',
                'payment_note' => $data['postData']['payment_note'] ?? '',
                'cheque_date' => $data['postData']['cheque_date'] ?? '',
                'attachment_name' => $data['postData']['attachment_name'] ?? '',
                'payment_token' => trim($data['postData']['payment_token'] ?? ''),
            ];

            // Check and update status for facility advance payment
            if (
                isset($data['postData']['booked_by']) &&
                !empty($data['postData']['booked_by']) &&
                isset($data['postData']['bill_from']) &&
                strtolower($data['postData']['bill_from']) == 'facility'
            ) {
                $objIncomeInvoicePaymentTracker['transaction_status'] = 'complete';
                $objIncomeInvoicePaymentTracker['status'] = in_array(strtolower($data['postData']['payment_mode']), $this->constants['payment_mode_for_clearance']) ? 'P' : 'Y';
            }

            // Insert into the database
            $objIncomeInvoicePaymentTrackerData = $this->tenantDB()->table('income_invoice_payment_tracker')->insertGetId($objIncomeInvoicePaymentTracker);

            $arrResponse['payment_tracker_id'] = $objIncomeInvoicePaymentTrackerData;

            unset($objIncomeInvoicePaymentTracker);
        }
        return $arrResponse;

    }

    /**
     * Set transaction status based on conditions.
     */
    public function setTransactionStatus($tracker, $postData)
    {
        if (
            isset($postData['booked_by']) &&
            !empty($postData['booked_by']) &&
            isset($postData['bill_from']) &&
            strtolower($postData['bill_from']) === 'facility'
        ) {
            $tracker->transaction_status = 'complete';
            $tracker->status = in_array(strtolower($postData['payment_mode']), $this->constants['payment_mode_for_clearance'])
            ? 'P' : 'Y';
        }
    }

    /**
     * Mocked method to format date strings to the required database format.
     */
    public function getFormattedDate($date)
    {
        if (!empty($date) && strrchr($date, '/')) {
            return date('Y-m-d', strtotime($date));
        }
        return $date ?: date('Y-m-d');
    }

    public function paymentTokenVerification($data = [])
    {
        // Check if payment token is provided
        if (empty($data['paymentToken'])) {
            return false;
        }

        // Retrieve payment tracker details using the payment token
        $arrPaymentTrackerDetail = $this->getPaymentTrackerDetailByPaymentToken($data);

        // Verify if the payment tracker details exist and check status conditions
        if (empty($arrPaymentTrackerDetail)) {
            return false;
        }

        $transactionStatus = strtolower($arrPaymentTrackerDetail['transaction_status']);
        $status = strtolower($arrPaymentTrackerDetail['status']);

        // Check if the transaction is in progress or already completed
        if (in_array($transactionStatus, ['in_process']) || in_array($status, ['y'])) {
            return false;
        }

        // If none of the conditions match, return true indicating a valid token
        return true;
    }

    public function getPaymentTrackerDetailByPaymentToken(array $data = [])
    {
        // Initialize the result array
        $arrIncomeInvoicePaymentTracker = [];

        // Check if the payment token and soc_id are provided
        if (empty($data['paymentToken']) || empty($data['soc_id'])) {
            return $arrIncomeInvoicePaymentTracker;
        }

        // Define the query for income tracker
        $query = $this->tenantDB()->table('income_invoice_payment_tracker')
            ->where('soc_id', $data['soc_id'])
            ->where('payment_token', $data['paymentToken']);

        // Check if type is 'expense' and adjust the table accordingly
        if (!empty($data['type']) && $data['type'] === 'expense') {
            $query = $this->tenantDB()->table('expense_invoice_payment_tracker')
                ->where('soc_id', $data['soc_id'])
                ->where('payment_token', $data['paymentToken']);
        }

        // Fetch the first record matching the conditions
        $objIncomeInvoicePaymentTracker = $query->first();

        // Convert the object to an array if found
        if ($objIncomeInvoicePaymentTracker) {
            $arrIncomeInvoicePaymentTracker = (array) $objIncomeInvoicePaymentTracker;
        }

        return $arrIncomeInvoicePaymentTracker;
    }

    public function updateIncomeInvoicePaymentTrackerStatus($data, $full_list = false)
    {

        $arrResponse = ['status' => 'success', 'message' => []];

        // Initialize an array to store the fields to be updated
        $updatedFields = [];
        // Map data fields to be updated, only add fields that are set and not empty
        $fieldsToUpdate = [
            'updated_date' => date('Y-m-d H:i:s') ?? null,
            'status' => $data['status'] ?? null,
            'transaction_status' => $data['transaction_status'] ?? null,
            'invoice_number' => $data['invoice_number'] ?? null,
            'payment_date' => $data['payment_date'] ?? null,
            'other_information' => $data['other_information'] ?? null,
            'reversal_note' => $data['reversal_note'] ?? null,
            'unit_id' => $data['unit_id'] ?? null,
            'bill_type' => $data['bill_type'] ?? null,
            'total_due_amount' => $data['total_unpaid_amount'] ?? null,
        ];

        // Build the update fields array by filtering out null values
        foreach ($fieldsToUpdate as $column => $value) {
            if (!is_null($value)) {
                $updatedFields[$column] = $value;
            }
        }

        // Ensure fields to update are present
        if (empty($updatedFields)) {
            return ['status' => 'error', 'message' => ['No fields to update']];
        }

        try {
            // Use the Laravel query builder to update the table
            $updateResult = $this->tenantDB()
                ->table('income_invoice_payment_tracker')
                ->where('soc_id', $data['soc_id'])
                ->where('id', $data['payment_tracker_id'])
                ->update($updatedFields);

            // Check if the update was successful
            if (!$updateResult) {
                return false;
            }
        } catch (\Exception $e) {
            // Handle exceptions and return error message
            return false;
        }

        return true;
    }

    public function addadvancepayment($data = [])
    {
        if(isset($data['arrListnerPaymentData']['invoice_details']) && !empty($data['arrListnerPaymentData']['invoice_details'])) {
            $arrPostData = $data['arrListnerPaymentData']['invoice_details'];
        } else {
            $arrPostData = $data['invoice_details'];
        }
        // dd($arrPostData);
        // Prepare the data to be inserted into the database
        $insertData = [
            'nonmember_bill_payment_id' => '',
            'soc_id' => $data['soc_id'],
            'fk_nonmember_bill_id' => $data['nonmember_invoice_id'],
            'bill_number' => $arrPostData['bill_number'],
            'payment_mode' => $arrPostData['payment_mode'] ?? '',
            'payment_type' => $arrPostData['payment_type'] ?? '',
            'transaction_reference' => $arrPostData['transaction_reference'] ?? '-',
            'tax_deducted' => isset($arrPostData['tax_deducted']) ? $arrPostData['tax_deducted'] : 0,
            'tds_deducted' => $arrPostData['tds_amount'] ?? 0,
            'payment_amount' => isset($arrPostData['tds_amount']) ? round($arrPostData['payment_amount'] - $arrPostData['tds_amount'], 3) : $arrPostData['payment_amount'],
            'transaction_charges' => isset($arrPostData['transaction_charges']) ? $arrPostData['transaction_charges'] : 0,
            'discount_amount' => isset($arrPostData['discount_amount']) ? $arrPostData['discount_amount'] : 0,
            'payment_instrument' => $arrPostData['payment_instrument'] ?? '-',
            'payer_bank_details' => isset($data['payer_bank_details']) ? $data['payer_bank_details'] : (isset($data['bank_and_branch_name']) ? $data['bank_and_branch_name'] : '-'),
            'notes' => $arrPostData['narration'] ?? '',
            'status' => 1,
            'approved' => 1,
            'created_date' => date('Y-m-d' ),
            'created_by' => $arrPostData['user_id'] ?? 0,
            'updated_date' => date('Y-m-d' ),
            'updated_by' => $arrPostData['user_id'] ?? 0,
        ];

        // Insert the data into the non_member_income_payments table using Laravel query builder
        try {
            $result = $this->tenantDB()->table('income_nonmember_bill_payments')->insert($insertData);

            if ($result) {
                return [
                    'success' => true,
                    'nonmember_bill_id' => $insertData['fk_nonmember_bill_id'],
                ];
            }

            return [
                'success' => false,
                'arrMessage' => ['Failed to save payment data.'],
            ];
        } catch (\Exception $e) {
            // Handle exceptions and capture error messages
            return [
                'success' => false,
                'arrMessage' => [$e->getMessage()],
            ];
        }
    }

    public function updateNonmemberIncomeStatus($data, $full_list = false)
    {
        // Initialize response array with success status
        $arrResponse = ['status' => 'success', 'message' => []];

        // Extract bill numbers and format them for the query
        $billNumbers = explode(',', $data['bill_number']); // Convert string to array
        $billNumbers = array_map('trim', $billNumbers); // Trim whitespace from each bill number

        try {
            // Ensure the updated_date is in the correct format
            $updatedDate = date('Y-m-d H:i:s');

            // Update payment status in the database using the Laravel query builder style
            $result = $this->tenantDB()->table('income_nonmember_bills')
                ->where('soc_id', $data['soc_id'])
                ->whereIn('bill_number', $billNumbers)
                ->update([
                    'payment_status' => $data['status'],
                    'updated_date' => $updatedDate,
                ]);

            // Check if the update was successful
            if (!$result) {
                $arrResponse['status'] = 'error';
                $arrResponse['message'][] = 'Failed to update payment status.';
            }
        } catch (\Exception $e) {
            // Handle any exceptions and log errors
            $arrResponse['status'] = 'error';
            $arrResponse['message'][] = $e->getMessage();
            // Optionally log the error for debugging
            // Log::error('Error updating nonmember income status: ' . $e->getMessage());
        }

        return $arrResponse;
    }

    public function saveCreditAccountResponse($data = array())
    {
        $user = $data['user'];
        $id = $data['id'];

        $data = $data['data'];
        $data['user'] = $user;

        if ($id) {
            // Prepare the data to be updated
            $updateData = [
                'updated_by' => $user,
                'updated_date' => date('Y-m-d H:i:s'),
                'use_credit_after' => !empty($data['adjustable_date']) ? $data['adjustable_date'] : null,
                'is_locked' => 0,
                'use_credit_for' => !empty($data['used_for']) ? $data['used_for'] : null,
                'is_invoice_rectification' => isset($data['is_invoice_rectification']) && !empty($data['is_invoice_rectification']) ? $data['is_invoice_rectification'] : null,
                'income_account_id' => isset($data['income_account_ledger_id']) && !empty($data['income_account_ledger_id']) ? $data['income_account_ledger_id'] : null,
                'narration' => $data['narration'],
                'use_credit' => isset($data['credit_used_type']) && !empty($data['credit_used_type']) ? $data['credit_used_type'] : 'adjustable',
            ];

            // Update the record in the database
            $result = $this->tenantDB()
                ->table('chsone_credit_accounts')
                ->where('credit_account_id', $id)
                ->where('soc_id', $data['soc_id'])
                ->update($updateData);

            // Save data
            if ($result) {
                return $result;
            } else {
                return false;
            }
        } else {
            if ($data['credit_used_type'] == 'both') {
                $data['credit_used_type'] = 'adjustable';
                $data['payment_amount'] = $data['adjustable_amount'];
                $saveResponse = $this->saveCreditNote($data);
                if ($saveResponse['error'] == false) {
                    $data['credit_used_type'] = 'refundable';
                    $data['payment_amount'] = $data['refundable_amount'];
                    $data['used_for'] = null;
                    $data['adjustable_date'] = null;

                    $saveResponse = $this->saveCreditNote($data);
                    if ($saveResponse['error'] == false) {
                        return $saveResponse;
                    }
                }
            } else {
                $saveResponse = $this->saveCreditNote($data);
                if ($saveResponse['error'] == false) {
                    return $saveResponse;
                }
            }
        }

    }

    public function saveCreditNote($data)
    {

        // Prepare data to be saved using an associative array
        $creditData = [
            'created_by' => $data['user'],
            'created_date' => date('Y-m-d H:i:s'),
            'soc_id' => $data['soc_id'],
            'invoice_number' => $data['invoice_no'] ?? $data['invoice_number'] ?? null,
            'is_invoice_rectification' => $data['is_invoice_rectification'] ?? null,
            'income_account_id' => $data['income_account_ledger_id'] ?? null,
            'payment_tracker_id' => $data['payment_tracker_id'] ?? null,
            'account_id' => $data['account_id'],
            'account_name' => $data['account_name'],
            'account_context' => $data['account_context'],
            'amount' => $data['payment_amount'],
            'payment_mode' => $data['payment_mode'] ?? '',
            'payment_date' => !empty($data['payment_date']) ? $data['payment_date'] : null,
            'transaction_type' => $data['transaction_type'],
            'narration' => $data['narration'],
            'use_credit' => $data['credit_used_type'] ?? 'adjustable',
            'use_credit_after' => $data['adjustable_date'] ?? null,
            'is_locked' => $data['is_locked'] ?? 0,
            'use_credit_for' => $data['used_for'] ?? null,
            'reference_no' => $data['transaction_reference'] ?? null,
            'context' => $data['context'] ?? 'system',
            'created_name' => $data['username'] ?? '',
        ];

        $result = $this->tenantDB()->table('chsone_credit_accounts')->insertGetId($creditData);

        // Save the model and handle the response
        if ($result) {
            return [
                'error' => false,
                'id' => $result,
            ];
        } else {
            return [
                'error' => true,
            ];
        }
    }
    public function payNonmemberBillLedger($data = array())
    {
        $success = 1;
        if (ACCOUNT_MODULE_EXIST == 1) {
            $PostData = $data['data'];
            $soc_id = $PostData['soc_id'];

            if (empty($PostData['bill_number'])) {
                $PostData['bill_number'] = $PostData['member_paid_invoice'] ?? '';
            }
            if (isset($PostData['clearance_date']) && !empty($PostData['clearance_date'])) {
                $PostData['payment_date'] = $PostData['clearance_date'];
            }
            $strNarration = '';

            if (empty($data['arrBookerLedgerDetails'])) {
                $arrListnerData['soc_id'] = $soc_id;
                $arrListnerData['ledger_name'] = $PostData['booker_name'];
                $arrBookerLedgerDetails = $this->checkledgerExist($arrListnerData);
            } else {
                $arrBookerLedgerDetails = $data['arrBookerLedgerDetails'];
            }
            //$arrIncomeAccounts = $data['arrIncomeAccounts'];

            //Getting bank/cash ledger details
            $arrAccountDetail = $this->getBankCashAccountDetail($PostData['soc_id']); //get all Unit details
            $arrLedgerAccountDetail = $this->getLedgerAccountDetail(array('account_detail' => $arrAccountDetail)); //get all Unit details

            if (!empty($arrLedgerAccountDetail)) {
                $arrIncomeAccounts['ledger_id'] = $arrLedgerAccountDetail['cash']['ledger_id'];
                $arrIncomeAccounts['ledger_name'] = $arrLedgerAccountDetail['cash']['ledger_name'];
                if (strtolower($PostData['payment_mode']) != 'cash') {
                    if (!empty($PostData['bank_account'])) {
                        $arrIncomeAccounts['ledger_id'] = $PostData['bank_account'];
                        $arrIncomeAccounts['ledger_name'] = $arrLedgerAccountDetail['arrBank'][$PostData['bank_account']];
                    } else {
                        $arrIncomeAccounts['ledger_id'] = $arrLedgerAccountDetail['bank']['ledger_id'];
                        $arrIncomeAccounts['ledger_name'] = $arrLedgerAccountDetail['bank']['ledger_name'];
                    }

                    $strNarration = ' with transaction ref. (' . ($PostData['transaction_reference'] ?? '') . ', ' . ($PostData['payment_instrument'] ?? '') . ')';
                    if (!empty($PostData['payment_mode']) && strtolower($PostData['payment_mode']) == 'cashtransfer') {
                        $strNarration = ' with payment ref. (' . ($PostData['transaction_reference'] ?? '') . ')';
                    }
                }
            }

            $arrListnerData = array();
            $arrListnerData['soc_id'] = $soc_id;
            $arrListnerData['voucher_type'] = isset($PostData['voucher_type_credit']) ? VOUCHER_CREDIT : VOUCHER_RECEIPT;
            $arrListnerData['voucher_reference_number'] = $PostData['receipt_number'];
            $arrListnerData['voucher_reference_id'] = $PostData['payment_tracker_id'];
            $arrListnerData['from_ledger_id'] = $arrIncomeAccounts['ledger_id'];
            $arrListnerData['to_ledger_id'] = $arrBookerLedgerDetails['recieving_ledger_id'];

            $arrListnerData['transaction_date'] = !empty($PostData['payment_date']) ? $PostData['payment_date'] : date('Y-m-d H:i:s');
            $arrListnerData['transaction_amount'] = $PostData['payment_amount'];

            if (isset($PostData['payment_mode']) && $PostData['payment_mode'] == 'cashtransfer') {
                $payment_mode_display = DISPLAY_CASH_TRANSFER;
            } else {
                $payment_mode_display = ucfirst($PostData['payment_mode'] ?? 'cash');
            }

            $arrListnerData['narration'] = '(Receipt No-' . $PostData['receipt_number'] . ') Amount received against Invoice ' . 
                $PostData['bill_number'] . ' dated ' . date('Y-m-d H:i:s') . 
                ' through ' . $payment_mode_display . $strNarration;

            $arrListnerData['from_ledger_name'] = $arrIncomeAccounts['ledger_name'];
            $arrListnerData['to_ledger_name'] = $arrBookerLedgerDetails['receiver_name'];
            $arrListnerData['payment_reference'] = $PostData['transaction_reference'] ?? '';
            $arrListnerData['transaction_type'] = $PostData['transaction_type'] ?? '';
            $arrListnerData['mode_of_payment'] = $PostData['payment_mode'] ?? 'cash';
            $arrListnerData['other_payment_ref'] = $PostData['other_payment_ref'] ?? '';
            if (!empty($PostData['payment_note'])) {
                $arrListnerData['narration'] .= ' [' . $PostData['payment_note'] . ']';
            }
            //Code to replace from ledger id From Bank/Cash
            if (isset($data['from_ledger'])) {
                $arrListnerData['from_ledger_id'] = $data['from_ledger']['recieving_ledger_id'];
                $arrListnerData['from_ledger_name'] = $data['from_ledger']['receiver_name'];
            }
            if (isset($data['to_ledger'])) {
                $arrListnerData['to_ledger_id'] = $data['to_ledger']['recieving_ledger_id'];
                $arrListnerData['to_ledger_name'] = $data['to_ledger']['receiver_name'];
            }

            if (isset($data['narration'])) {
                $arrListnerData['narration'] = '(Receipt No-' . $PostData['receipt_number'] . ') ' . $data['narration'];
            }

            if (!empty($PostData['tds_amount']) && $PostData['tds_amount'] > 0) {
                $arrListnerData['transaction_amount'] = round($PostData['payment_amount'] - $PostData['tds_amount'], 2);
                //print_r($arrListnerData);

                $arrLedgerEntry = $this->transactionLedgerEntry($arrListnerData);
                if (isset($arrLedgerEntry['error']) && !empty($arrLedgerEntry['error'])) {
                    $success = 0;
                } else {
                    $arrListnerData['transaction_amount'] = $PostData['tds_amount'];
                    $arrLedgerData['soc_id'] = $soc_id;
                    $arrLedgerData['ledger_name'] = 'tds receivable';
                    $arrLedgerData['context'] = ''; //CURRENT_ASSETS_GROUP;
                    $arrLedgerData['behaviour'] = ASSET;

                    $arrTdsLedger = $this->checkledgerExistNew($arrLedgerData);
                    $arrListnerData['from_ledger_id'] = $arrTdsLedger['recieving_ledger_id'];
                    $arrListnerData['from_ledger_name'] = $arrTdsLedger['receiver_name'];

                    if (isset($PostData['payment_mode']) && $PostData['payment_mode'] == 'cashtransfer') {
                        $payment_mode_display = DISPLAY_CASH_TRANSFER;
                    } else {
                        $payment_mode_display = ucfirst($PostData['payment_mode'] ?? 'cash');
                    }

                    $arrListnerData['narration'] = 'TDS amount deducted against Invoice ' . $PostData['bill_number'] . ' dated ' . $this->getCurrentDate('display') . ' through ' . $payment_mode_display . $strNarration; //'Invoice '.$arrPostData['member_paid_invoice'].' '.$eachLedgerDetail['particular'].' Write Off';

                    //print_r($arrListnerData);
                    $arrLedgerEntry = $this->transactionLedgerEntry($arrListnerData);
                    if (isset($arrLedgerEntry['error']) && !empty($arrLedgerEntry['error'])) {
                        $success = 0;
                    }
                }
            } else {

                $arrLedgerEntry = $this->transactionLedgerEntry($arrListnerData);
                if (isset($arrLedgerEntry['error']) && !empty($arrLedgerEntry['error'])) {
                    $success = 0;
                }
            }
        }
        //exit;
        return $success;
    }

    public function checkledgerExist($data = [])
    {
        $arrClientLedgerDetails = [];
        $soc_id = $data['soc_id'];
        $ledgerName = $data['ledger_name'];

        // Check if the ledger already exists for the outsider based on provided criteria
        $existingLedger = $this->tenantDB()->table('chsone_grp_ledger_tree')
            ->where('soc_id', $data['soc_id'])
            ->where('context', 'sundrydebtors')
            ->where('entity_type', ENTITY_TYPE_LEDGER)
            ->where('ledger_account_name', $ledgerName)
            ->first();

        if (!empty($existingLedger)) {
            // Ledger exists, so set the receiving ledger details
            $arrClientLedgerDetails['recieving_ledger_id'] = $existingLedger->ledger_account_id;
            $arrClientLedgerDetails['receiver_name'] = $existingLedger->ledger_account_name;
        } else {
            // Ledger does not exist, create a new ledger under the Sundry Debtors group
            $sundryDebtorGroup = $this->tenantDB()->table('chsone_grp_ledger_tree')
                ->where('soc_id', $soc_id)
                ->where('entity_type', ENTITY_TYPE_GROUP)
                ->where('ledger_account_name', SUNDRY_DR)
                ->first();

            if ($sundryDebtorGroup) {
                // Create a new ledger entry under the Sundry Debtors group
                $ledgerAccountId = $this->manipulate(
                    $ledgerName,
                    ENTITY_TYPE_LEDGER,
                    "",
                    $sundryDebtorGroup->ledger_account_id,
                    $sundryDebtorGroup->behaviour,
                    '',
                    0,
                    '',
                    '',
                    null,
                    $sundryDebtorGroup->context
                );

                // Check if the new ledger was created successfully
                if (is_string($ledgerAccountId) && strpos($ledgerAccountId, 'DUP') === false) {
                    $arrClientLedgerDetails['recieving_ledger_id'] = $ledgerAccountId;
                    $arrClientLedgerDetails['receiver_name'] = $ledgerName;
                }
            }
        }

        return $arrClientLedgerDetails;
    }

    public function transactionLedgerEntry($data = [])
    {
        // Initialize the response array
        $arrResponse = ['error' => true];

        // Validate required fields: from_ledger_id and to_ledger_id
        if (empty(trim($data['from_ledger_id'])) || empty(trim($data['to_ledger_id']))) {
            return $arrResponse;
        }

        // Set the transaction date if not provided
        $data['transaction_date'] = $data['transaction_date'] ?? date('Y-m-d H:i:s');

        // Initialize the Transaction component and execute the transaction entry
        $transactionId = $this->executeTransactionEntry($data);

        // Check if the transaction was successful and update the response accordingly
        if ($transactionId) {
            $arrResponse = ['success' => true, 'transaction_id' => $transactionId];
        }

        return $arrResponse;
    }

    public function executeTransactionEntry($data)
    {
        // Initialize voucher array and handle voucher type if it contains an underscore
        $vouchersArray = [];
        if (strpos($data['voucher_type'], '_') !== false) {
            $vouchersArray = explode('_', $data['voucher_type']);
            $data['voucher_type'] = $vouchersArray[0];
        }

        // Set default values for transaction data
        $data['is_opening'] = 0;
        $data['transaction_from_id'] = '';

        // Attempt to add the first transaction entry
        $data['transaction_from_id'] = $this->_addTransactionEntry($data);

        // Check if the first transaction entry was successful
        if (!empty($data['transaction_from_id'])) {
            // Set the voucher type to the second part if available
            if (count($vouchersArray) >= 2) {
                $data['voucher_type'] = $vouchersArray[1];
            }

            // Attempt to add the second transaction entry
            if ($this->_addTransactionEntry($data)) {

                // Commit and return the transaction ID if successful
                return $data['transaction_from_id'];
            }
        }

        // If any transaction fails, handle rollback (commented out for illustrative purposes)
        // $this->soc_db_w->rollback();

        // Return null or false if the transaction failed
        return null;
    }

    public function _addTransactionEntry($data)
    {
        // Determine the mode, transaction type, ledger ID, and ledger name based on whether it's a "from" or "to" transaction
        if (!empty($data['transaction_from_id'])) {
            $data['transaction_type'] = "cr";
            $ledger_id = $data['to_ledger_id'];
            $ledger_name = $data['to_ledger_name'];
        } else {
            $data['transaction_type'] = "dr";
            $ledger_id = $data['from_ledger_id'];
            $ledger_name = $data['from_ledger_name'];
        }

        $data = json_decode(json_encode($data), true);
        $soc_id = $data['soc_id'];

        // Check for opening balance transaction if applicable
        if (!empty($data['is_opening']) && $data['is_opening'] == 1) {
            $conditions = [
                ['soc_id', '=', $soc_id],
                ['is_opening_balance', '=', 1],
                ['ledger_account_id', '=', $ledger_id],
            ];

            $txn_entry = $this->tenantDB()->table('chsone_ledger_transactions')
                ->where($conditions)
                ->first();

            // If found, use the existing transaction ID
            $data['txn_id'] = $txn_entry->txn_id ?? '';
        }

        // Prepare the data for insertion
        $transactionData = [
            'soc_id' => $soc_id,
            'transaction_date' => $data['transaction_date'],
            'ledger_account_id' => $ledger_id,
            'ledger_account_name' => $ledger_name,
            'voucher_type' => $data['voucher_type'],
            'transaction_type' => $data['transaction_type'] ?? '',
            'payment_mode' => $data['mode_of_payment'] ?? '',
            'payment_reference' => $data['payment_reference'] ?? '',
            'transaction_amount' => $data['transaction_amount'],
            'other_reference_id' => $data['other_payment_ref'] ?? '',
            'txn_from_id' => $data['transaction_from_id'],
            'memo_desc' => $data['narration'],
            'is_opening_balance' => $data['is_opening'] ?? 0,
            'is_reconciled' => $data['is_reconciled'] ?? 0,
            'created_by' => $this->input['user_id'] ?? 0,
            'added_on' => date('Y-m-d H:i:s'),
            'voucher_reference_number' => $data['voucher_reference_number'] ?? '',
            'voucher_reference_id' => $data['voucher_reference_id'] ?? '',
            'is_cancelled' => !empty($data['is_cancelled']) ? 1 : 0,
        ];

        // Insert the data using the create method and return the transaction ID
        $txn = $this->tenantDB()->table('chsone_ledger_transactions')->insertGetId($transactionData);

        return $txn ? $txn : false;
    }

    public function _getLedgerProps($parent_group)
    {
        $array = array();

        if (is_numeric($parent_group)) {
            $ledger_props = ChsoneGrpLedgerTree::select('ledger_account_name', 'nature_of_account', 'behaviour', 'report_head', 'context')
            ->where('ledger_account_id', $parent_group)
            ->first();
        }

        $array = $ledger_props;
        return $array;
    }
    public function rollbackTransaction($paymentTrackerId, $data, $status = 'N')
    {

        if (!empty($paymentTrackerId)) {
            $this->updateIncomePaymentTrackerStatus(
                [
                    'soc_id' => $data['soc_id'],
                    'updated_by' => $this->input['user_id'],
                    'payment_tracker_id' => $paymentTrackerId,
                    'status' => $status,
                    'transaction_status' => 'complete',
                ]
            );
        }
    }

}
