<?php

namespace App\Console\Commands\Actions\Income;

use Illuminate\Console\Command;
use App\Console\Commands\Action;
use Illuminate\Support\Facades\DB;

class MemberReceiptReportDataSource extends Action
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'datasource:MemberReceiptReport {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Get the member receipts report';
    
    protected $formatter = [
        'id' => '',
        'receipt_number' => '',
        'receipt_type' => '',
        'paid_by' => '',
        'invoice_number' => '',
        'payment_reference' => '',
        'unit' => '',
        'payment_amount' => '',
        'payment_date' => '',
        'payment_mode' => '',
    ];

    protected $formatterByKeys = ['id'];

    /**
     * Execute the console command.
     */
    
    public function apply()
    {
        $request = $this->input;
        $from_date = date('Y-m-01');
        $to_date = date('Y-m-t');

        if (isset($request['filters'])) {
            $date = $this->getFromAndToDate($request['filters']);
            $from_date = $date[0];
            $to_date = $date[1];
        }
        
        $payment_date = $request['filters']['payment_date'] ?? null;
        $member_name = $request['filters']['member_name'] ?? null;
        $unit_no = $request['filters']['unit_no'] ?? null;
        $invoice_no = $request['filters']['invoice_no'] ?? null;
        $receipt_no = $request['filters']['receipt_no'] ?? null;
        $cheque_no = $request['filters']['cheque_no'] ?? null;

        $payment_mode = $request['filters']['payment_mode'] ?? null;
        $receipt_type = $request['filters']['receipt_type'] ?? null;
    
        if (array_key_exists('from_date', $request)) {
            $from_date = $request['from_date'];
        }
    
        if (array_key_exists('to_date', $request)) {
            $to_date = $request['to_date'];
        }

        $obj = $this->tenantDB()->table('income_invoice_payment_tracker as inc')
            ->select(
                'inc.id', 'inc.soc_id', 'inc.unit_id', 'inc.invoice_number', 'inc.receipt_number',
                'inc.bill_type as receipt_type', 'inc.transaction_reference as payment_reference',
                'inc.payment_date as receipt_date',
                'inc.received_from as paid_by', 'inc.payment_amount', 'inc.payment_date', 'inc.payment_mode',
                'inc.invoice_number',
                DB::raw("CONCAT(chsone_unit.soc_building_name, ' / ', chsone_unit.unit_flat_number) AS unit"),

                DB::raw("(SELECT SUM(inc_inner.payment_amount) FROM income_invoice_payment_tracker AS inc_inner
                WHERE inc_inner.payment_date BETWEEN '$from_date' AND '$to_date' 
                AND inc_inner.id = inc.id) AS total_amount")
            )
            ->leftJoin('chsone_units_master as chsone_unit', 'inc.unit_id', '=', 'chsone_unit.unit_id')
            ->whereNotIn('inc.status', ['N', 'reversed','P'])
            ->whereBetween('inc.payment_date', [$from_date, $to_date]);
            
        if (array_key_exists('bill_type', $request)) {
            $bill_type = (!empty($request['bill_type']) &&  $request['bill_type'] != 'All') ? $request['bill_type'] : null;
            $bill_type = $request['bill_type'];
            if ($bill_type) {
                $obj->where('inc.bill_type', $bill_type);
            }
        }
        
        if (!empty($payment_date)) {
            $obj->where('inc.payment_date', $this->getDatabaseDate($payment_date));
        }
        
        if (!empty($member_name)) {
            $obj->where('inc.received_from', 'LIKE', '%' . $member_name . '%');
        }

        if (!empty($receipt_type)) {
            $obj->where('inc.bill_type', $receipt_type);
        }
        
        if (!empty($unit_no)) {
            $obj->where(function ($query) use ($unit_no) {
                $query->where('chsone_unit.unit_flat_number', 'LIKE', '%' . $unit_no . '%');
            });
        }
        
        if (!empty($payment_mode)) {
            $obj->where('inc.payment_mode', $payment_mode);
        }
        
        if (!empty($invoice_no)) {
            $obj->where('inc.invoice_number', $invoice_no);
        }
        
        if (!empty($receipt_no)) {
            $obj->where('inc.receipt_number', $receipt_no);
        }
        
        if (!empty($cheque_no)) {
            $obj->where('inc.transaction_reference', $cheque_no);
        }
        

        if (!empty($filteredValue)) {
            switch ($filteredValue) {
                case 'cheque':
                    $obj->where('inc.payment_mode', $filteredValue);
                    break;
    
                case 'cashtransfer':
                    $obj->where('inc.payment_mode', $filteredValue);
                    break;
    
                case 'cash':
                    $obj->where('inc.payment_mode', $filteredValue);
                    break;    
            }
        }
            
        $obj->groupBy('inc.id');
        $obj->orderBy('inc.id', 'desc');
        $result = $obj->get();
    
        $data = $result->map(function ($item) {
            $billTypeMapping = [
                '' => 'All',
                'member' => 'Maintenance',
                'common_bill' => 'Incidental',
                'member_common_bill' => 'Maintenance / Incidental',
                'creditaccount-member' => 'Member Advance',
                'nonmember' => 'Non Member',
                'creditaccount-nonmember' => 'Non Member Advance',
            ];
            $item->receipt_type = $billTypeMapping[$item->receipt_type] ?? $item->receipt_type;
            return $item;
        });
    
        $totalAmount = $data->sum('total_amount');
        $paymentAmount = $data->sum('payment_amount');
        
        if($totalAmount == !null) {
            $finalAmt = [
                'total_amount' => $totalAmount,
            ];
        } else if($totalAmount == null) {
            $finalAmt = [
                'total_amount' => $paymentAmount,
            ];
        }

        $result = [
            $data,
            [ $finalAmt ]
        ];
    
        $this->data = $result;
    }

    public function getFromAndToDate($inputs){
        // $date = $inputs['transaction_date'];
        // $dateRange = explode(',', $date);
        $dateRange = $inputs;
        $from_date = $dateRange['startDate'] ?? date("Y-m-01");
        $to_date = $dateRange['endDate'] ?? date("Y-m-t");
        return array($from_date, $to_date);
    }
}
