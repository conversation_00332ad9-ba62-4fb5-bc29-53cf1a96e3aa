<?php

namespace App\Console\Commands\Actions\Income;

use Illuminate\Support\Facades\DB;
use App\Console\Commands\Action;
use App\Models\Income\ChsoneInvoiceGeneration;
use App\Models\Tenants\ChsoneInvoiceGeneration as TenantsChsoneInvoiceGeneration;
use App\Models\Tenants\IncomeUnitInvoice;

class EditMemberNameDataSource extends Action
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'datasource:EditMemberNameDataSource {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Edit Member Name data source';

    protected $formatter =  [];

    protected $formatterByKeys =  [];

    protected $mapper = [];

    /**
     * Execute the console command.
     */
    public function apply()
    {
        $soc_id = $this->input['company_id'];
        $unit_id = $this->input['unit_id'];
        $type = $this->input['type'];
        $update_all_units = $this->input['update_all_units'] ?? 1;

        $arrDataListener = [];
        $arrDataListener['soc_id'] = $soc_id;
        $arrDataListener['type'] = $type;
        $arrDataListener['unit_id'] = $unit_id;

        if (isset($update_all_units) && $update_all_units == 1) {
            $arrDataListener['update_all_units'] = 1;
            $arrMemberDetail = $this->getBulkMemberUnitDetails($arrDataListener);
            $result = $arrMemberDetail;
        }

        $arrDataListener['member_details'] = $result;
        $updateMemberDetail = $this->updateBulkMemberUnitDetails($arrDataListener); //get all Unit details

        if (!empty($updateMemberDetail) && $updateMemberDetail['success']) {
            $this->status = 'success';
            $this->message = 'Member Name has been updated successfully.';
            $this->statusCode = 200;
        }/* else {
            $this->status = 'success';
            $this->message = 'Error in updating member name.';
            $this->statusCode = 400;
        }*/
    }

    public function getBulkMemberUnitDetails($data = array())
    {
        $resultset = [];
        if ($data['type'] == 'unit') {
            if (isset($data['update_all_units']) && $data['update_all_units'] == 1) {
                $resultset = IncomeUnitInvoice::select(
                    DB::raw('CONCAT(units.soc_building_name, " / ", units.unit_flat_number) AS unit_name'),
                    DB::raw('CONCAT(members.member_first_name, " ", members.member_last_name) AS member_name'),
                    'income_unit_invoices.fk_unit_id AS unit_id',
                    'income_unit_invoices.invoice_number AS member_id'
                )
                    ->leftJoin('chsone_units_master AS units', 'income_unit_invoices.fk_unit_id', '=', 'units.unit_id')
                    ->leftJoin('chsone_members_master AS members', 'units.unit_id', '=', 'members.fk_unit_id')
                    ->where('income_unit_invoices.fk_unit_id', $data['unit_id'])
                    ->where('income_unit_invoices.soc_id', $data['soc_id'])
                    ->get()
                    ->toArray();
            }
        } else if ($data['type'] == 'invoice') {
            $resultset = IncomeUnitInvoice::select(
                DB::raw('CONCAT(units.soc_building_name, " / ", units.unit_flat_number) AS unit_name'),
                DB::raw('CONCAT(members.member_first_name, " ", members.member_last_name) AS member_name'),
                'income_unit_invoices.fk_unit_id AS unit_id',
                'income_unit_invoices.invoice_number AS member_id'
            )
            ->leftJoin('chsone_units_master AS units', 'income_unit_invoices.fk_unit_id', '=', 'units.unit_id')
            ->leftJoin('chsone_members_master AS members', 'units.unit_id', '=', 'members.fk_unit_id')
            ->where('income_unit_invoices.invoice_number', $data['unit_id'])
            ->where('income_unit_invoices.soc_id', $data['soc_id'])
            ->get()
            ->toArray();
        }
        //$queryBuilder->getQuery()->getSQL()
        return $resultset;
    }

    public function updateBulkMemberUnitDetails($data = array())
    {
        $finalArray = [];
        
        if ($data['type'] == 'unit') {
            foreach ($data['member_details'] as $value) {
                $unitInvoiceData = IncomeUnitInvoice::where('soc_id', $data['soc_id'])
                               ->where('fk_unit_id', $value['unit_id'])
                               ->get();
                //echo "<pre>";print_r($unitInvoice->toArray());die;
                foreach ($unitInvoiceData as $unitInvoice) {
                    $unitInvoice->bill_to = $value['member_name'];
                    $unitInvoice->updated_date = date('Y-m-d');

                    if (!$unitInvoice->save()) {
                        $arrMessage = [];
                        foreach ($unitInvoice->getMessages() as $messages) {
                            $arrMessage[] = (string) $messages;
                        }
                        $finalArray['success'] = false;
                        $finalArray['arrMessage'] = $unitInvoice->fk_unit_id;
                    } else {
                        $finalArray['success'] = true;
                        $finalArray['arrMessage'] = $unitInvoice->fk_unit_id;
                    }
                }
            }
        } else if ($data['type'] == 'invoice') {

            foreach ($data['member_details'] as $value) {

                $unitInvoice = IncomeUnitInvoice::where('soc_id', $data['soc_id'])
                                ->where('invoice_number', $value['member_id'])
                                ->where('fk_unit_id', $value['unit_id'])
                                ->first();

                $unitInvoice->bill_to = $value['member_name'];
                $unitInvoice->updated_date = date('Y-m-d');

                if (!$unitInvoice->save()) {
                    $arrMessage = [];
                    foreach ($unitInvoice->getMessages() as $messages) {
                        $arrMessage[] = (string) $messages;
                    }
                    $finalArray['success'] = false;
                    $finalArray['arrMessage'] = $unitInvoice->fk_unit_id;
                } else {
                    $finalArray['success'] = true;
                    $finalArray['arrMessage'] = $unitInvoice->fk_unit_id;
                }
            }
        }
        return $finalArray;
    }
}
