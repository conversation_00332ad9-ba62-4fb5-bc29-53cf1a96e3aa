<?php

namespace App\Console\Commands\Actions\Income\Maintenance;

use Illuminate\Console\Command;
use App\Console\Commands\Action;

class fetchDataPayMemberBillDataSource extends Action
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'datasource:fetchDataPayMemberBill {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Fetch data for member payments';

    /**
     * Execute the console command.
     */
    public function apply()
    {
        $unit_id = $this->input['unit_id'];
        $soc_id = $this->input['company_id'];

        // check unit id is valid or not from chsone_units_master table
        $unit = $this->tenantDB()->table('chsone_units_master')
            ->where('unit_id', $unit_id)
            ->first();

        if(!$unit) {
            $this->status = 'error';
            $this->message = 'Please provide a valid unit id';
            $this->statusCode = 400;
            $this->data = [];
            return;
        }   

        // fecth member name from chsone_members_master table where fk_unit_id = $unit_id and soc_id = $soc_id and member_type_id = 1
        $member = $this->tenantDB()->table('chsone_members_master')
        ->selectRaw('CONCAT(member_first_name, " ", member_last_name) as member_name')
        ->selectRaw('CONCAT(member_first_name, " ", member_last_name) as received_from')
            ->where('fk_unit_id', $unit_id)
            ->where('soc_id', $soc_id)
            ->where('member_type_id', 1)
            ->first();

        if(!$member) {
            $this->status = 'error';
            $this->message = 'No data found for the given unit id';
            $this->statusCode = 400;
            $this->data = [];
            return;
        }

        $this->data = $member;
    }
}
