<?php

namespace App\Console\Commands\Actions\Income\Maintenance;

use Illuminate\Console\Command;
use App\Console\Commands\Action;
use Illuminate\Support\Arr;

class InvoicePerticularDataSource extends Action
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'datasource:maintenance-invoice-perticular {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Maintenance invoice with perticular data source';


    protected $formatter =  [
        'unit_name' => '',
        'unit_id' => '',
        'invoices' =>
           [
            'invoice_number' => '',
            'bill_to' => '',
            'unit_invoice_id' => '',
            'member_id' => '',
            'member_gstin' => '',
            'from_date' => '',
            'to_date' => '',
            'due_date' => '',
            'soc_building_floor' => '',
            'soc_building_name' => '',
            'status' => "",
            'payment_status' => '',
            'principal_amount' => '',
            'interest_amount' => '',
            'outstanding_interest' => '',
            'outstanding_principal' => '',
            'roundoff_amount' => '',
            'advance_amount' => '',
            'created_date' => '',
            'particulars' => [
                'particular' => '',
                'amount' => '',
                'tax_applicable_arr' => 'getUnserialize:tax_applicable',
                'tax_exemptions_arr' => 'getUnserialize:tax_exemptions',
                'particular_paid_amount' => '',
                'bill_type' => '',
                'tax_applicable_value' => 'getTotalValue:tax_applicable_arr',
                'tax_exemptions_value' => 'getTotalValue:tax_exemptions_arr',
                'total_particular_amount' => 'adds:amount,tax_applicable_value,tax_exemptions_value'
            ],
            // 'total_tax_applicable' => 'add:total_tax_applicable,particulars.particular.tax_applicable_value',
            // 'total_tax_exemptions' => '',
            //'total_amount' => 'add:total_amount,particulars.particular.total_particular_amount',
            'invoice_total_amount' => 'addPerticulars:particulars',
        ],
        //'due_amount' => 'add:due_amount,invoices.invoice_number.total_amount',
        'id' => 'setValue:unit_id',

    ];

    protected $smartFormatter = [
        'unit_id' => [
            'unit_name' => '',
            'unit_id' => '',
            'invoices' => [
                'invoice_number' => [
                    'invoice_number' => '',
                    'bill_to' => '',
                    'unit_invoice_id' => '',
                    'member_id' => '',
                    'member_gstin' => '',
                    'from_date' => '',
                    'to_date' => '',
                    'due_date' => '',
                    'soc_building_floor' => '',
                    'soc_building_name' => '',
                    'status' => "",
                    'payment_status' => '',
                    'principal_amount' => 'casting:double,0.00',
                    'interest_amount' => 'casting:double,0.00',
                    'outstanding_interest' => 'casting:double,0.00',
                    'outstanding_principal' => 'casting:double,0.00',
                    'roundoff_amount' => 'casting:double,0.00',
                    'advance_amount' => 'casting:double,0.00',
                    'created_date' => '',
                    'particulars' => [
                        'particular' => [
                            'particular' => '',
                            'amount' => 'casting:double,0.00',
                            'tax_applicable' => 'getUnserialize->getTotalValue->casting:double,0.00',
                            'tax_exemptions' => 'getUnserialize->getTotalValue->casting:double,0.00',
                            'particular_paid_amount' => 'casting:double,0.00',
                            'bill_type' => '',
                            'total_particular_amount' => 'adds:amount,tax_applicable,tax_exemptions'
                            ]
                        ],

                    'total_amount' => 'sum:particulars.*.amount',
                    'invoice_total_amount' => 'adds:total_amount,interest_amount',

                ],
            ],
            //'due_amount' => 'add:due_amount,invoices.invoice_number.total_amount',
            'id' => 'setValue:unit_id',
        ],
    ];

    protected $formatterByKeys =  ['unit_id','invoice_number', 'particular'];

    protected $mapper = [
        'unit_id' => 'UI.fk_unit_id',
        'particular' => 'particular',
        'payment_status' => 'UI.payment_status',
    ];

    /**
     * Execute the console command.
     */
    public function apply()
    {
        $obj = $this->tenantDB()->table('income_unit_invoices as UI')
        ->select(
            'UI.fk_unit_id as unit_id',
            'UI.unit_invoice_id',
            'UI.invoice_number',
            'UI.unit_name',
            'UI.bill_to',
            'UI.member_id',
            'UI.member_gstin',
            'UI.from_date',
            'UI.to_date',
            'UI.due_date',
            'UI.soc_building_floor',
            'UI.soc_building_name',
            'UI.status',
            'UI.payment_status',
            'UI.principal_amount',
            'UI.interest_amount',
            'UI.outstanding_interest',
            'UI.outstanding_principal',
            'UI.roundoff_amount',
            'UI.advance_amount',
            'UI.created_date',
            'IP.particular',
            'IP.amount',
            'IP.tax_applicable',
            'IP.tax_exemptions',
            'IP.particular_paid_amount',
            'IP.bill_type',
            'UI.created_date'
        )
        ->rightJoin('income_invoice_particular as IP', 'UI.invoice_number', '=', 'IP.invoice_number')
        ->where('UI.status', '<>', 'cancelled');

        $obj = $this->filter($obj);
        if (isset($this->input['search_by']) && isset($this->input['search_key'])) {
            $searchBy = $this->input['search_by'];
            $searchKey = $this->input['search_key'];
            $obj = $obj->whereRaw("{$searchBy} = '{$searchKey}'");
        }else if(isset($this->input['search_by']) && empty($this->input['search_key'])){
                $this->message = "Search key not found.";
                $this->status = 'error';
                $this->statusCode = 400;
                return false;
        }
        $obj = $obj->orderBy('UI.fk_unit_id', 'asc');
        $result = $obj->get();
        $primaryDS = $this->smartFormat($result->toArray());
        $this->data = $primaryDS;
        return $this->data;
    }


    public function addPerticulars($particulars)
    {
        $total = 0;
        foreach($particulars as $particular) {
            $total += (float)$particular['total_particular_amount'];
        }
        return $total;
    }



    // Function to build the tree structure
    public function buildTree($data, $parentId = 0)
    {
        $tree = [];

        foreach ($data as $row) {
            if ($row->parent_id == $parentId) {
                // Create an array for the current node
                $node = [
                    'ledger_account_id' => $row->ledger_account_id,
                    'ledger_account_name' => $row->ledger_account_name,
                    'nature_of_account' => $row->nature_of_account,
                    'entity_type' => $row->entity_type,
                    'behaviour' => $row->behaviour,
                    'children' => []
                ];

                // Recursively build the child nodes
                $children = $this->buildTree($data, $row->ledger_account_id);
                if (!empty($children)) {
                    $node['children'] = $children;
                }

                // Add the node to the tree
                $tree[] = $node;
            }
        }

        return $tree;
    }






}
