<?php

namespace App\Console\Commands\Actions\Income;

use Illuminate\Console\Command;
use App\Console\Commands\Action;
use App\Models\Tenants\ChsoneTaxLog;
use App\Models\Tenants\IncomeCommonBillingCharge;
use App\Models\Tenants\IncomeInvoicePayment;
use App\Models\Tenants\IncomeInvoicePaymentTracker;
use App\Models\Tenants\IncomeInvoiceRule;
use App\Models\Tenants\IncomeInvoiceSetting;
use App\Models\Tenants\IncomeLatePaymentCharge;
use App\Models\Tenants\IncomeUnitInvoice;
use Illuminate\Support\Facades\DB;

class updateIncomePaymentTrackerDataSource extends Action
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'datasource:updateIncomePaymentTracker {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command to update income payment tracker';

    /**
     * Execute the console command.
     */
    public function apply()
    {
        // check whether the payment tracker id is valid or not from income_invoice_payment_tracker table
        $data = $this->input;
        $data['payment_amount'] = $data['receipt_amount'];
        $arrDataListenerNew['soc_id'] = $data['company_id'];

        $payments = $this->tenantDB()->table('income_invoice_payment_tracker')
            ->where('id', $data['payment_tracker_id'])
            ->get();
  
        if (count($payments) == 0) {
            $this->status = 'error';
            $this->message = 'Please provide a valid payment tracker id';
            $this->statusCode = 400;
            $this->data = [];
            return;
        }

        // get the income_invoice_payment_tracker details for the given payment tracker id from income_invoice_payment_tracker table
        $arrPaymentTrackerListener = array('soc_id' => $data['company_id'], 'payment_tracker_id' => $data['payment_tracker_id'], 'status' => array('P', 'R'));
        $arrPaymentTrackerData = $this->getInvoicePaymentTrackerDetail($arrPaymentTrackerListener);

        $arrDataListenerNew['unit_id'] = $arrPaymentTrackerData['unit_id'];
        // $bankLedgId = $arrPaymentTrackerData['other_information']['member_detail']['bank_ledger'] ?? 0;

        // Unserialize the other_information field if it is a serialized string
        if (is_string($arrPaymentTrackerData['other_information'])) {
            $arrPaymentTrackerData['other_information'] = unserialize($arrPaymentTrackerData['other_information']);
        }

        // Check if other_information is an array before accessing its nested data
        if (is_array($arrPaymentTrackerData['other_information'])) {

            if ($payments[0]->bill_type == 'member') {
                $bankLedgId = $arrPaymentTrackerData['other_information']['member_detail']['bank_ledger'] ?? 0;
            } elseif ($payments[0]->bill_type == 'common_bill') {
                $bankLedgId = $arrPaymentTrackerData['other_information']['common_bill_detail']['bank_ledger'] ?? 0;
            } elseif ($payments[0]->bill_type == 'nonmember') {
                $bankLedgId = $arrPaymentTrackerData['other_information']['nonmember_detail']['bank_ledger'] ?? 0;
            } elseif ($payments[0]->bill_type == 'creditaccount-member') {
                $bankLedgId = $arrPaymentTrackerData['other_information']['credit_account_detail']['bank_ledger'] ?? 0;
            } elseif ($payments[0]->bill_type == 'creditaccount-nonmember') {
                $bankLedgId = $arrPaymentTrackerData['other_information']['suspense_detail']['bank_ledger'] ?? 0;
            } elseif ($payments[0]->bill_type == 'member_common_bill') {
                $bankLedgId = $arrPaymentTrackerData['other_information']['suspense_detail']['bank_ledger'] ?? 0;
            }
        }

        // fetch the member details for the given unit_id from chsone_members_master table

        $members_details = $this->tenantDB()->table('chsone_members_master as members')
        ->leftJoin('chsone_member_type_master as member_type', 'members.member_type_id', '=', 'member_type.member_type_id')
            ->where('fk_unit_id', $arrPaymentTrackerData['unit_id'])
            ->where('member_type_name' , 'Primary')
            ->first();

        // This block is for non members
        if (empty($members_details)) {
            // non member logic
            if (is_string($arrPaymentTrackerData['other_information'])) {
                $arrPaymentTrackerData['other_information'] = unserialize($arrPaymentTrackerData['other_information']);
            }
        
            // Extract nonmember_id from other_information (if available)
            $nonmember_bill_id = $arrPaymentTrackerData['other_information']['nonmember_detail']['nonmember_bill_id'] ?? null;

            $nonmember_details =  $this->tenantDB()->table('income_nonmember_bills as non_members')
                ->where('non_members.nonmember_bill_id', $nonmember_bill_id)
                ->first();

            $nonmember_details = json_decode(json_encode($nonmember_details), true);
        } 
        $members_details = json_decode(json_encode($members_details), true);
        
        if (!empty($members_details)) {
            $arrDataListenerNew['member_id'] = $members_details['id'];
        } elseif (!empty($nonmember_details)) {
            $arrDataListenerNew['nonmember_id'] = $nonmember_details['nonmember_id'];
        }

        // fetch member_paid_invoice from income_invoice_payment_tracker table
        $arrOtherInfoNew = array();
        if ($payments[0]->bill_type == 'maintenance') {

            $bill_type = 'member';
            $arrOtherInfoNew['member_detail']['bank_ledger'] = $bankLedgId;

            $unpaidInvoiceDetail = $this->getUnitInvoiceUnpaidBill($arrDataListenerNew);
            $memberPaidInvoices = $this->getMemberPaidInvoices(array('payment_amount' => $data['payment_amount'], 'unpaidInvoiceDetail' => $unpaidInvoiceDetail));

            if (!empty($memberPaidInvoices)) {
                $arrDataListenerNew['member_paid_invoice'] = implode(',', $memberPaidInvoices);
            }
        } else if ($payments[0]->bill_type == 'incident') {
            $bill_type = 'common_bill';
            $arrOtherInfoNew['common_bill_detail']['bank_ledger'] = $bankLedgId;
            $arrOtherInfoNew['common_bill_detail']['payment_type'] = 'quickpay';

            $unpaidInvoiceDetail = $this->getIncidentInvoiceUnpaidBill($arrDataListenerNew);
            $arrIncidentPaymentInvoices = $this->getIncidentPaymentInvoices(array('postData' => $data, 'arrUnpaidInvoices' => $unpaidInvoiceDetail));

            if (!empty($arrIncidentPaymentInvoices)) {
                $arrDataListenerNew['member_paid_invoice'] = $arrIncidentPaymentInvoices['member_paid_invoice'];
            }
        } else if ($payments[0]->bill_type == 'creditaccount-member') {
            $creditAccountDetail = $arrPaymentTrackerData['other_information']['credit_account_detail'] ?? [];
            $availableCredit = $creditAccountDetail['available_credit'] ?? 0;
            $creditUsedType = $creditAccountDetail['credit_used_type'] ?? 'adjustable';
        
            // Adjust payment amount using available credit
            $paymentAmount = $data['payment_amount'];
            if ($availableCredit >= $paymentAmount) {
                $creditUsed = $paymentAmount;
                $availableCredit -= $paymentAmount;
                $paymentAmount = 0; // Fully covered by credit
            } else {
                $creditUsed = $availableCredit;
                $paymentAmount -= $availableCredit;
                $availableCredit = 0; // Credit fully utilized
            }

            // Update credit account details in other_information
            $arrOtherInfoNew['credit_account_detail']['available_credit'] = $availableCredit;
            $arrOtherInfoNew['credit_account_detail']['credit_used'] = $creditUsed;

            if (!empty($memberPaidInvoices)) {
                $arrDataListenerNew['member_paid_invoice'] = implode(',', $memberPaidInvoices);
            }

            $bill_type = 'creditaccount-member';
            $arrOtherInfoNew['credit_account_detail']['bank_ledger'] = $bankLedgId;
        } else if ($payments[0]->bill_type == 'creditaccount-nonmember') {
            $creditAccountDetail = $arrPaymentTrackerData['other_information']['credit_account_detail'] ?? [];
            $availableCredit = $creditAccountDetail['available_credit'] ?? 0;
            $creditUsedType = $creditAccountDetail['credit_used_type'] ?? 'adjustable';
        
            // Adjust payment amount using available credit
            $paymentAmount = $data['payment_amount'];
            if ($availableCredit >= $paymentAmount) {
                $creditUsed = $paymentAmount;
                $availableCredit -= $paymentAmount;
                $paymentAmount = 0; // Fully covered by credit
            } else {
                $creditUsed = $availableCredit;
                $paymentAmount -= $availableCredit;
                $availableCredit = 0; // Credit fully utilized
            }

            // Update credit account details in other_information
            $arrOtherInfoNew['credit_account_detail']['available_credit'] = $availableCredit;
            $arrOtherInfoNew['credit_account_detail']['credit_used'] = $creditUsed;

            if (!empty($memberPaidInvoices)) {
                $arrDataListenerNew['member_paid_invoice'] = implode(',', $memberPaidInvoices);
            }

            $bill_type = 'creditaccount-nonmember';
            $arrOtherInfoNew['credit_account_detail']['bank_ledger'] = $bankLedgId;
        }

        // For TDS adjustment
        if (isset($data['tds_amount']) && !empty($data['tds_amount']) && $data['tds_amount'] > 0) {
            $data['payment_amount'] = (float) $data['payment_amount'] + $data['tds_amount'];
        }

        if (!empty($data['used_for']) && !empty($data['member_id'])) {
            $arrUpdatePaymentTrackerRes = $this->updateIncomePaymentTrackerStatus(array('soc_id' => $data['company_id'], 'updated_by' => $data['user_id'], 'payment_tracker_id' => $data['payment_tracker_id'], 'unit_id' => $arrPaymentTrackerData['unit_id'], 'invoice_number' => $arrDataListenerNew['member_paid_invoice'], 'bill_type' => $bill_type, 'other_information' => serialize($arrOtherInfoNew), 'total_unpaid_amount' => $data['total_due_amount'], 'payment_date' => $this->getDatabaseDate($data['payment_date'])));
        } else {
            $arrUpdatePaymentTrackerRes = $this->updateChequeInvoicePaymentTracker(['arrPostData' => $data, 'oldTrackerData' => $arrPaymentTrackerData, 'soc_id' => $data['company_id']]); // update cheque payment tracker
        }

        if (isset($arrUpdatePaymentTrackerRes['status']) && $arrUpdatePaymentTrackerRes['status'] == 'success') {
            $this->status = 'success';
            $this->message = 'Cheque Payment Updated Successfully.';
            $this->statusCode = 200;
        } else {
            $this->status = 'error';
            $this->message = 'Unable to Update Cheque Payment.';
            $this->statusCode = 400;
        }
    }

    // Get invoice payment tracker detail
    public function getInvoicePaymentTrackerDetail($data = array())
    {
        $arrIncomeInvoicePaymentTracker = array();
        // Start building the query for IncomeInvoicePaymentTracker model
        $query = IncomeInvoicePaymentTracker::where('soc_id', $data['soc_id'])->where('id', $data['payment_tracker_id']);

        // Add conditions for unit_id if present
        if (isset($data['unit_id']) && !empty($data['unit_id'])) {
            $query->where('unit_id', $data['unit_id']);
        }

        // Add conditions for status if present
        if (isset($data['status']) && !empty($data['status'])) {
            if (is_array($data['status'])) {
                $query->whereIn('status', $data['status']);
            } else {
                $query->where('status', $data['status']);
            }
        }

        // Add conditions for current_date if present
        if (isset($data['current_date']) && !empty($data['current_date'])) {
            $query->where('payment_date', '<=', $data['current_date']);
        }

        // Add conditions for payment reversal if applicable
        if (!empty($data['payment_reversal']) && strtolower($data['payment_reversal']) == 'y') {
            $query->where('created_date', '>=', ACTIVE_PAYMENT_REVERSAL_DATE);
        }

        // Add conditions for bill_type if present
        if (isset($data['bill_type']) && !empty($data['bill_type'])) {
            $query->whereIn('bill_type', $data['bill_type']);
        }

        // Get the first result from the query
        $objIncomeInvoicePaymentTracker = $query->first();
        if (!empty($objIncomeInvoicePaymentTracker)) {
            $arrIncomeInvoicePaymentTracker = $objIncomeInvoicePaymentTracker->toArray();

            $arrIncomeInvoicePaymentTracker['member_paid_invoice'] = $arrIncomeInvoicePaymentTracker['invoice_number'];
            $arrIncomeInvoicePaymentTracker['receipt_number'] = strtoupper($arrIncomeInvoicePaymentTracker['receipt_number']);
            $arrIncomeInvoicePaymentTracker['total_unpaid_amount'] = $arrIncomeInvoicePaymentTracker['total_due_amount'];
            unset($arrIncomeInvoicePaymentTracker['invoice_number'], $arrIncomeInvoicePaymentTracker['total_due_amount']);
            $arrIncomeInvoicePaymentTracker['other_information'] = unserialize($arrIncomeInvoicePaymentTracker['other_information']);

            // set payment for
            $arrIncomeInvoicePaymentTracker['payment_for'] = 'Maintenance Invoice';
            if (strtolower($arrIncomeInvoicePaymentTracker['bill_type']) == 'common_bill') {
                $arrIncomeInvoicePaymentTracker['payment_for'] = 'Incidental Bill';
            } elseif (strtolower($arrIncomeInvoicePaymentTracker['bill_type']) == 'nonmember') {
                $arrIncomeInvoicePaymentTracker['payment_for'] = 'Nonmember Bill';
            } elseif (strtolower($arrIncomeInvoicePaymentTracker['bill_type']) == 'suspense') {
                $arrIncomeInvoicePaymentTracker['payment_for'] = 'Suspense Reciept';
            } elseif (strtolower($arrIncomeInvoicePaymentTracker['bill_type']) == 'creditaccount-member') {
                if (
                    !empty($arrIncomeInvoicePaymentTracker['other_information']['credit_account_detail']['credit_used_type']) &&
                    strtolower($arrIncomeInvoicePaymentTracker['other_information']['credit_account_detail']['credit_used_type']) == 'refundable'
                ) {
                    $arrIncomeInvoicePaymentTracker['payment_for'] = 'Member Refundable Advance';
                } elseif (
                    !empty($arrIncomeInvoicePaymentTracker['other_information']['credit_account_detail']['credit_used_type']) &&
                    strtolower($arrIncomeInvoicePaymentTracker['other_information']['credit_account_detail']['credit_used_type']) == 'adjustable'
                ) {
                    $arrIncomeInvoicePaymentTracker['payment_for'] = 'Member Adjustable Advance';
                }
            }
            if (!empty($arrIncomeInvoicePaymentTracker['payment_mode']) && in_array($arrIncomeInvoicePaymentTracker['payment_mode'], array(YES_BANK_PG, YES_BANK_ECOLLECT, PAYTM_PG, MOBIKWIK_PG, CASHFREE_PG, MOBIKWIK_WALLET, HDFC_PG, ATOM_PG))) {
                switch ($arrIncomeInvoicePaymentTracker['payment_mode']) {
                    case YES_BANK_PG:
                        $arrIncomeInvoicePaymentTracker['online_payment_display_mode'] = DISPLAY_YES_BANK;
                        break;
                    case YES_BANK_ECOLLECT:
                        $arrIncomeInvoicePaymentTracker['online_payment_display_mode'] = DISPLAY_YES_BANK_ECOLLECT;
                        break;
                    case MOBIKWIK_WALLET:
                        $arrIncomeInvoicePaymentTracker['online_payment_display_mode'] = DISPLAY_MOBIKWIK_WALLET;
                        break;
                    case PAYTM_PG:
                        $arrIncomeInvoicePaymentTracker['online_payment_display_mode'] = DISPLAY_PAYTM;
                        break;
                    case MOBIKWIK_PG:
                        $arrIncomeInvoicePaymentTracker['online_payment_display_mode'] = DISPLAY_MOBIKWIK_PG;
                        break;
                    case CASHFREE_PG:
                        $arrIncomeInvoicePaymentTracker['online_payment_display_mode'] = DISPLAY_CASHFREE_PG;
                        break;
                    case HDFC_PG:
                        $arrIncomeInvoicePaymentTracker['online_payment_display_mode'] = DISPLAY_HDFC_PG;
                        break;
                    case ATOM_PG:
                        $arrIncomeInvoicePaymentTracker['online_payment_display_mode'] = DISPLAY_ATOM_PG;
                        break;
                }
            }

            // format date
            if (!empty($arrIncomeInvoicePaymentTracker['created_date'])) {
                $payment_date = current(explode(' ', $arrIncomeInvoicePaymentTracker['created_date']));
                if (!empty($payment_date)) {
                    $arrIncomeInvoicePaymentTracker['created_date'] = $payment_date;
                }
            }

            // setting other information field
            if (!empty($arrIncomeInvoicePaymentTracker['other_information'])) {
                // setting writeoff particular fields
                if (!empty($arrIncomeInvoicePaymentTracker['other_information']['writeoff_detail'])) {
                    $arrIncomeInvoicePaymentTracker['total_particular'] = $arrIncomeInvoicePaymentTracker['other_information']['writeoff_detail']['total_particular'];
                    for ($i = 1; $i <= $arrIncomeInvoicePaymentTracker['total_particular']; $i++) {
                        $arrIncomeInvoicePaymentTracker["rule_$i"] = $arrIncomeInvoicePaymentTracker['other_information']['writeoff_detail']["rule_$i"];
                        $arrIncomeInvoicePaymentTracker["particular_$i"] = $arrIncomeInvoicePaymentTracker['other_information']['writeoff_detail']["particular_$i"];
                        $arrIncomeInvoicePaymentTracker["writeoff_amount_$i"] = $arrIncomeInvoicePaymentTracker['other_information']['writeoff_detail']["writeoff_amount_$i"];
                    }
                }
            }
        }
        return $arrIncomeInvoicePaymentTracker;
    }

    // get unit invoice unpaid bill
    public function getUnitInvoiceUnpaidBill($data = array())
    {
        $arrAllUnitsInvoice = array();
        $todaysDate = date('Y-m-d');
        
        $query = IncomeUnitInvoice::where('soc_id', $data['soc_id'])
        ->where('fk_unit_id', $data['unit_id'])
        ->whereIn('payment_status', ['unpaid', 'partialpaid'])
        ->where('status', '!=', 'cancelled');
        
        if (!isset($data['in_progress']) || $data['in_progress'] == true) {
            $query->where('status', '!=', 'in_process');
        }

        if (!empty($data['from_date']) && !empty($data['to_date'])) {
            $from_date = (strrchr($data['from_date'], '/')) ? $this->getDatabaseDate($data['from_date']) : $data['from_date'];
            $to_date = (strrchr($data['to_date'], '/')) ? $this->getDatabaseDate($data['to_date']) : $data['to_date'];
            $query->where('due_date','>=' , $from_date)->where('due_date','<=', $to_date);
        }
        // Assuming the "UnitsInvoice" model corresponds to the "units_invoices" table
        $objUnitsInvoice = $query->orderBy('unit_invoice_id', 'asc')->get();

        if (!empty($objUnitsInvoice)) {
            $arrUnitsInvoice = $objUnitsInvoice->toArray();
            if (!empty($arrUnitsInvoice)) {
                $arrAllUnitsInvoice = $this->getUnpaidInvoiceDetail(array(
                    'arrUnitsInvoice' => $arrUnitsInvoice,
                    'member_id' => $data['member_id'],
                    'soc_id' => $data['soc_id'],
                    'unit_id' => $data['unit_id']
                ));
            }
        }
        return $arrAllUnitsInvoice;
    }

    // get member unpaid due by invoice
    function getUnpaidInvoiceDetail($data = array())
    {
        $arrAllUnitsInvoice = array();
        $latest_due_date = null;
        $last_invoice_details = array();
        $todaysDate = date('Y-m-d');
        if (!empty($data['arrUnitsInvoice'])) {
            $arrUnitsInvoice = $data['arrUnitsInvoice'];

            $arrLastUnitInvoice = $this->getLastUnitInvoiceByUnit(array(
                'soc_id' => $data['soc_id'],
                'unit_id' => $data['unit_id']
            ));

            $total = $total_advance_amount = $total_outstanding_principal_amount = $total_interest_amount = $totalDue = 0;
            $arrUnitsInvoice = json_decode(json_encode($arrUnitsInvoice), true);
            foreach ($arrUnitsInvoice as $eachUnitInvoice) {
                $singleUnitInvoice = array();
                $singleUnitInvoice['soc_id'] = $eachUnitInvoice['soc_id'];
                $singleUnitInvoice['member_id'] = !empty($data['member_id']) ? $data['member_id'] : '';
                $singleUnitInvoice['unit_id'] = $eachUnitInvoice['fk_unit_id'];
                $singleUnitInvoice['soc_building_name'] = $eachUnitInvoice['soc_building_name'];
                $singleUnitInvoice['unit_name'] = $eachUnitInvoice['unit_name'];
                $singleUnitInvoice['unit_invoice_id'] = $eachUnitInvoice['unit_invoice_id'];
                $singleUnitInvoice['unit_invoice_number'] = $eachUnitInvoice['invoice_number'];
                if (!empty($arrLastUnitInvoice) && isset($arrLastUnitInvoice['invoice_number'])) {
                    $singleUnitInvoice['unit_first_invoice'] = $eachUnitInvoice['unit_first_invoice'] = $arrLastUnitInvoice['invoice_number'];
                }
                $singleUnitInvoice['principal_amount'] = $eachUnitInvoice['principal_amount'];
                $singleUnitInvoice['interest_amount'] = $singleUnitInvoice['late_payment_interest'] = $eachUnitInvoice['interest_amount'];
                $singleUnitInvoice['advance_amount'] = $eachUnitInvoice['advance_amount'];
                $singleUnitInvoice['roundoff_amount'] = $eachUnitInvoice['roundoff_amount'];
                $singleUnitInvoice['payment_status'] = $eachUnitInvoice['payment_status'];
                $singleUnitInvoice['from_date'] = $eachUnitInvoice['from_date'];
                $singleUnitInvoice['to_date'] = $eachUnitInvoice['to_date'];

                $singleUnitInvoice['invoice_date'] = !empty($eachUnitInvoice['created_date']) ? current(explode(' ', $eachUnitInvoice['created_date'])) : '';
                $singleUnitInvoice['due_date'] = !empty($eachUnitInvoice['due_date']) ? $eachUnitInvoice['due_date'] : '';
                $singleUnitInvoice['due_date_status'] = (!empty($singleUnitInvoice['due_date']) && $todaysDate > $singleUnitInvoice['due_date']) ? 'red' : 'green';
                $singleUnitInvoice['late_charge_tax_detail'] = (!empty($singleUnitInvoice['interest_amount']) && $singleUnitInvoice['interest_amount'] > 0) ? $this->getMemberInvoiceLateChargeTaxDetail(array(
                'soc_id' => $eachUnitInvoice['soc_id'],
                'invoice_number' => $eachUnitInvoice['invoice_number']
                )) : null;
                $total_advance_amount += (float) $eachUnitInvoice['advance_amount'];

                $total_interest_amount += $singleUnitInvoice['interest_amount'];
                $total_interest_amount = (!empty($singleUnitInvoice['late_charge_tax_detail']['total_tax']) && $singleUnitInvoice['late_charge_tax_detail']['total_tax'] > 0) ? $total_interest_amount + $singleUnitInvoice['late_charge_tax_detail']['total_tax'] : $total_interest_amount;

                $arrgetdata = array(
                    "conditions" => " soc_id = '" . $singleUnitInvoice['soc_id'] . "' and fk_unit_invoice_id = '" . $singleUnitInvoice['unit_invoice_id'] . "' and fk_unit_id = '" . $singleUnitInvoice['unit_id'] . "' and invoice_number = '" . $singleUnitInvoice['unit_invoice_number'] . "'",
                    "order" => "created_date desc"
                );
                $objIncomeInvoiceParticular = $this->tenantDB()->table('income_invoice_particular')
                    ->whereRaw($arrgetdata['conditions'])
                    ->get();
                // $objIncomeInvoiceParticular = json_decode(json_encode($objIncomeInvoiceParticular), true);
                if (!empty($objIncomeInvoiceParticular)) {
                    $singleUnitInvoice['invoice_particulars'] = $objIncomeInvoiceParticular->toArray();
                    $singleUnitInvoice['invoice_amount_detail'] = $this->_getTotalInvoiceAmount($singleUnitInvoice['invoice_particulars'], $eachUnitInvoice);
                    if (!empty($singleUnitInvoice['invoice_amount_detail']['partialLatePaymentCharge'])) {
                        $total_interest_amount = round((float) $total_interest_amount - (float) $singleUnitInvoice['invoice_amount_detail']['partialLatePaymentCharge'], 4);
                    }
                    // $total += bcsub((float) $singleUnitInvoice['invoice_amount_detail']['finalInvoiceAmount'], (float) $singleUnitInvoice['invoice_amount_detail']['partialpaidAmount'], 4);
                    $total += round(
                        (float) $singleUnitInvoice['invoice_amount_detail']['finalInvoiceAmount'] - (float) $singleUnitInvoice['invoice_amount_detail']['partialpaidAmount'],
                        4
                    );
                }
                //check below key is unit_first_invoice
                if (strtolower($eachUnitInvoice['unit_invoice_id']) == strtolower($eachUnitInvoice['invoice_number']) && strtolower($eachUnitInvoice['payment_status']) != 'paid') {
                    $total_outstanding_principal_amount = (float) $eachUnitInvoice['principal_amount'];
                }

                $arrLatePaymentCharges = array();
                $arrLatePaymentCharges = $this->calculateLatePaymentCharges($singleUnitInvoice);

                $singleUnitInvoice['apply_late_payment_charges'] = $arrLatePaymentCharges['apply_late_payment_charges'];
                $singleUnitInvoice['late_payment_charges_detail'] = (!empty($arrLatePaymentCharges['late_payment_charges_detail'])) ? $arrLatePaymentCharges['late_payment_charges_detail'] : array();
                $singleUnitInvoice['late_payment_charges'] = (!empty($arrLatePaymentCharges['total_late_payment_charges'])) ? $arrLatePaymentCharges['total_late_payment_charges'] : 0;
                $singleUnitInvoice['total_late_payment_chargeable_amount'] = (!empty($arrLatePaymentCharges['total_late_payment_chargeable_amount'])) ? $arrLatePaymentCharges['total_late_payment_chargeable_amount'] : 0;
                $singleUnitInvoice['total_invoice_due'] = (float) ($total + $total_interest_amount + $total_outstanding_principal_amount - $total_advance_amount);
                $singleUnitInvoice['single_invoice_due'] = (float) (round($singleUnitInvoice['total_invoice_due'] - $totalDue, 3));
                $totalDue = $singleUnitInvoice['total_invoice_due'];

                $latest_due_date = $singleUnitInvoice['due_date'];
                $last_invoice_details['due_date'] = $eachUnitInvoice['due_date'];
                $last_invoice_details['from_date'] = $eachUnitInvoice['from_date'];
                $last_invoice_details['to_date'] = $eachUnitInvoice['to_date'];
                $last_invoice_details['invoice_number'] = $eachUnitInvoice['invoice_number'];
                array_push($arrAllUnitsInvoice, $singleUnitInvoice); // $i++;
            }
            $arrAllUnitsInvoice['total_unpaid_invoice_amount'] = (float) ($total + $total_interest_amount + $total_outstanding_principal_amount - $total_advance_amount);
            $arrAllUnitsInvoice['latest_due_date'] = $latest_due_date;
            $arrAllUnitsInvoice['last_invoice_details'] = $last_invoice_details;
        }
        return $arrAllUnitsInvoice;
    }

    // get last unit invoice by unit
    public function getLastUnitInvoiceByUnit($data = array())
    {
        $arrUnitsInvoice = array();

        $query = IncomeUnitInvoice::query()
        ->where('soc_id', $data['soc_id'])
        ->where('fk_unit_id', $data['unit_id'])
        ->where('status', '!=', 'cancelled');

        if (isset($data['order_by']) && !empty($data['order_by'])) {
            $query->orderByRaw($data['order_by']);
        }
        $objUnitsInvoice = $query->first();

        if (!empty($objUnitsInvoice)) {
            $arrUnitsInvoice = $objUnitsInvoice->toArray();
        }

        return $arrUnitsInvoice;
    }

    // get member invoice late charge tax detail
    public function getMemberInvoiceLateChargeTaxDetail($data = array())
    {
        $arrTaxClass = array(
            'tax_detail' => null,
            'total_tax' => 0
        );

        $query = ChsoneTaxLog::where('soc_id', $data['soc_id'])
        ->whereNull('particular_id')
        ->where('particular', 'LateCharge');
        
        if (is_array($data['invoice_number'])) {
            $query->whereIn('invoice_number', $data['invoice_number']);
        } else {
            $query->where('invoice_number', $data['invoice_number']);
        }
    
        $objTaxClass = $query->get();

        if (!empty($objTaxClass)) {
            $arrTaxClass['tax_detail'] = $objTaxClass->toArray();
            foreach ($arrTaxClass['tax_detail'] as $eachTaxLog) {
                $arrTaxClass['total_tax'] += $eachTaxLog['tax_amount'];
            }
        }
        return $arrTaxClass;
    }

    // get total invoice amount
    private function _getTotalInvoiceAmount($data, $arrUnitInvoice = array())
    {
        $arrTotal = array('totalInvoiceAmount' => 0, 'totalTaxApplicable' => 0, 'totalTaxExemption' => 0, 'partialpaidAmount' => 0, 'finalInvoiceAmount' => $arrUnitInvoice['roundoff_amount'], 'partialLatePaymentCharge' => 0);
        $data = json_decode(json_encode($data), true);
        if (!empty($data)) {
            foreach ($data as $eachInvoiceParticular) {
                $arrTotal['totalInvoiceAmount'] += $eachInvoiceParticular['amount'];
                $applied_tax = (!empty($eachInvoiceParticular['tax_applicable']) && !is_array($eachInvoiceParticular['tax_applicable'])) ? unserialize($eachInvoiceParticular['tax_applicable']) : $eachInvoiceParticular['tax_applicable'];
                // print_r($applied_tax);
                $tax_exemption = (!empty($eachInvoiceParticular['tax_exemptions']) && !is_array($eachInvoiceParticular['tax_exemptions'])) ? unserialize($eachInvoiceParticular['tax_exemptions']) : $eachInvoiceParticular['tax_exemptions'];
                $applied_tax_total = (is_array($applied_tax)) ? $applied_tax['total'] : $applied_tax;
                $tax_exemption_total = (is_array($tax_exemption)) ? $tax_exemption['total'] : $tax_exemption;
                $arrTotal['totalTaxApplicable'] += (float) $applied_tax_total;
                $arrTotal['totalTaxExemption'] += (float) $tax_exemption_total;
                $arrTotal['finalInvoiceAmount'] += (float) $eachInvoiceParticular['amount'] + ($applied_tax_total + $tax_exemption_total);
            }
        }
        // get partial pad amount
        if (!empty($arrUnitInvoice['payment_status']) && in_array($arrUnitInvoice['payment_status'], array('partialpaid','paid'))) 
        {
            $objIncomeInvoicePayment = IncomeInvoicePayment::where('soc_id', $arrUnitInvoice['soc_id'])->where('fk_unit_id', $arrUnitInvoice['fk_unit_id'])->where('fk_unit_invoice_id', $arrUnitInvoice['unit_invoice_id'])->where('invoice_number', $arrUnitInvoice['invoice_number'])->get();

            if (!empty($objIncomeInvoicePayment)) {

                $arrIncomeInvoicePayment = $objIncomeInvoicePayment->toArray();
                // echo "<pre>";print_r($arrIncomeInvoicePayment);exit;
                foreach ($arrIncomeInvoicePayment as $eachIncomeInvoicePayment) {
                    $arrTotal['partialpaidAmount'] += (float) ($eachIncomeInvoicePayment['payment_amount'] + $eachIncomeInvoicePayment['tds_deducted'] + $eachIncomeInvoicePayment['writeoff_amount']);
                    $arrTotal['partialLatePaymentCharge'] += (float) $eachIncomeInvoicePayment['late_payment_charges'];
                }
                // $arrTotal['finalInvoiceAmount'] -= $arrTotal['invoice_amount_detail']['partialpaidAmount'];
            }
        }
        return $arrTotal;
    }

    // calculate late payment charges
    public function calculateLatePaymentCharges($data = array())
    {
        $latePaymentCharge = 0;
        $todaysDate = date('Y-m-d');
        $arrLatePaymentCharges = array();
        $arrAllLatePaymentCharges = array();
        $arrLatePaymentCharges['apply_late_payment_charges'] = 0;
        $total_late_payment_chargeable_amount = 0;

        if (!empty($data['due_date'])) {
            $dueDate = $data['due_date'];
            $lateChargeCalculationDate = new \DateTime($data['from_date']);
            $lateChargeCalculationDate = $lateChargeCalculationDate->modify("-1 day")->format('Y-m-d'); // For today/now, don't pass an arg.

            if ($todaysDate > $dueDate) {
                // set late payment charges key
                if (strtolower($data['payment_status']) != 'paid') {
                    $arrLatePaymentCharges['apply_late_payment_charges'] = 1;
                }
                if (!empty($data['invoice_particulars'])) {
                    // print_r($data);//exit;
                    // print_r($data);
                    $arrData = $this->_getRulewiseTotalAmount($data, $data['payment_status']);
                    // exit;
                    // echo '################# Late payment Interest ##############################';
                    // print_r($data['invoice_particulars']);
                    // print_r($arrData);exit;
                    // $arrLatePaymentCharges['particular_late_charges'] = $arrData;
                    $total_late_payment_chargeable_amount = 0;
                    $arrGeneralInvoiceSetting = $this->getInvoiceSetting(array(
                        'soc_id' => $data['soc_id']
                    )); // Get general setting
                    $arrLatePaymentdetails = $this->getLatePaymentCharges(array(
                        'soc_id' => $data['soc_id']
                    )); // Get late charges rule

                    foreach ($arrData as $rule => $amount) {

                        $objincomeinvoicerule = IncomeInvoiceRule::where('id', $rule)
                        ->where('soc_id', $data['soc_id'])
                        ->first();

                        if (!empty($objincomeinvoicerule)) {
                            $arrRuleWiseCharges = array();
                            $arrIncomeinvoicerule = $objincomeinvoicerule->toArray();
                            if (isset($arrIncomeinvoicerule['apply_late_payment_interest']) && $arrIncomeinvoicerule['apply_late_payment_interest'] == 1) {
                                // $arrLatePayment = array("conditions" => " soc_id = " . $data['soc_id'] . " and effective_date <= '" . $this->getCurrentDate('database') . "' and calculate_from = 'billdate'", "order" => "effective_date desc");
                                // $objLatePaymentdetails = \ChsOne\Models\LatePaymentCharges::findfirst($arrLatePayment);
                                if (!empty($arrLatePaymentdetails)) {
                                    $arrRuleWiseCharges['rule_id'] = $rule;
                                    $singleRuleCharges = $this->calculateMonthlyLateCharges(array(
                                        'soc_id' => $data['soc_id'],
                                        'dueDate' => $dueDate,
                                        'amount' => $amount,
                                        'arrLatePaymentdetails' => $arrLatePaymentdetails,
                                        'arrGeneralInvoiceSetting' => $arrGeneralInvoiceSetting,
                                        'lateChargeCalculationDate' => $lateChargeCalculationDate
                                    ));

                                    $arrRuleWiseCharges['late_payment_charges'] = $singleRuleCharges;
                                    $total_late_payment_chargeable_amount += $amount;
                                    $latePaymentCharge += $singleRuleCharges;
                                    array_push($arrAllLatePaymentCharges, $arrRuleWiseCharges);
                                }
                            }
                        }
                    }
                }
            }
        }
        $arrLatePaymentCharges['late_payment_charges_detail'] = $arrAllLatePaymentCharges;
        $arrLatePaymentCharges['total_late_payment_charges'] = $latePaymentCharge;
        $arrLatePaymentCharges['total_late_payment_chargeable_amount'] = $total_late_payment_chargeable_amount;
        return $arrLatePaymentCharges;
    }

    // get rulewise total amount
    private function _getRulewiseTotalAmount($data = array(), $paymentStatus)
    {
        $arrRule = array();
        $data = json_decode(json_encode($data), true);
        if (!empty($data)) {
            foreach ($data['invoice_particulars'] as $eachParticular) {

                $finalParticularAmount = 0;

                // Exclude noc from total amount
                $finalParticularAmount = (float) $eachParticular['amount'];

                // Add applicable tax amount
                if (!empty($eachParticular['tax_applicable'])) {
                    $arrTaxApplicable = unserialize($eachParticular['tax_applicable']);
                    $finalParticularAmount += (float) $arrTaxApplicable['total'];
                }

                // Subtract applicable tax exemption amount
                if (!empty($eachParticular['tax_exemptions'])) {
                    $arrTax_exemptions = unserialize($eachParticular['tax_exemptions']);
                    $finalParticularAmount += (float) $arrTax_exemptions['total'];
                }

                if (array_key_exists($eachParticular['fk_rule_id'], $arrRule)) {
                    if (strtolower($eachParticular['is_particular_paid']) == 'y') {
                        $arrRule[$eachParticular['fk_rule_id']] += 0;
                    } elseif (strtolower($eachParticular['is_particular_paid']) == 'p') {
                        $arrRule[$eachParticular['fk_rule_id']] += (!empty($eachParticular['particular_paid_amount'])) ? round($finalParticularAmount - $eachParticular['particular_paid_amount'], 4) : $finalParticularAmount;
                    } else {
                        $arrRule[$eachParticular['fk_rule_id']] += $finalParticularAmount;
                    }
                } else {
                    if (strtolower($eachParticular['is_particular_paid']) == 'y') {
                        $arrRule[$eachParticular['fk_rule_id']] = 0;
                    } elseif (strtolower($eachParticular['is_particular_paid']) == 'p') {
                        $arrRule[$eachParticular['fk_rule_id']] = (isset($eachParticular['particular_paid_amount'])) ? $finalParticularAmount - $eachParticular['particular_paid_amount'] : $finalParticularAmount;
                    } else {
                        $arrRule[$eachParticular['fk_rule_id']] = $finalParticularAmount;
                    }
                }
                if ($paymentStatus == 'paidafterduedate') {
                    $arrRule[$eachParticular['fk_rule_id']] += $finalParticularAmount;
                }
            }
        }
        // print_r($arrRule);exit;
        return $arrRule;
    }

    public function getInvoiceSetting($data = array())
    {
        $arrInvoiceSetting = array();
        $objInvoiceSetting = IncomeInvoiceSetting::where('soc_id', $data['soc_id'])->first();
        if (!empty($objInvoiceSetting)) {
            $arrInvoiceSetting = $objInvoiceSetting->toArray();
        }
        return $arrInvoiceSetting;
    }

    public function getLatePaymentCharges($data = array())
    {
        $effective_date = date('Y-m-d');
        if (!empty($data['bill_date'])) {
            $effective_date = (strrchr($data['bill_date'], '/')) ? $data['bill_date'] : $data['bill_date'];
        }
        if (empty($data['type']) || $data['type'] == '') {
            $type = "type = 'maintenance'";
        } else {
            $type = "type = '" . $data['type'] . "'";
        }

        $objLatePaymentdetails = IncomeLatePaymentCharge::where('type', $type)
        ->where('soc_id', $data['soc_id'])
        ->where('effective_date', '<=', $effective_date)
        ->orderBy('effective_date', 'desc')
        ->first();

        $arrLatePaymentdetails = (!empty($objLatePaymentdetails)) ? $objLatePaymentdetails->toArray() : array();
        return $arrLatePaymentdetails;
    }

    public function calculateMonthlyLateCharges($data = array())
    {
        if ($data['arrLatePaymentdetails']['calculate_for'] == 'perday') {
            //            return $this->calculateDailyLateCharges($event, $component, $data);
        }
        $arrLatePaymentdetails = $data['arrLatePaymentdetails'];
        $arrInvoiceSetting = $data['arrGeneralInvoiceSetting'];
        // $dueDate = $data['dueDate'];
        $amount = $data['amount'];

        //         echo '<br><pre>';print_r($data);
        // $todaysDate = $this->getCurrentDate('database');
        $singleRuleCharges = 0;
        if (!empty($arrLatePaymentdetails)) {
            $numberOfMonth = 1;
            if (!empty($arrInvoiceSetting)) {
                switch (strtolower($arrInvoiceSetting['invoicing_frequency'])) {
                    case 'quarterly':
                        $numberOfMonth = 3;
                        break;
                    case 'half_yearly':
                        $numberOfMonth = 6;
                        break;
                    case 'yearly':
                        $numberOfMonth = 12;
                        break;
                    case 'monthly':
                    default:
                        $numberOfMonth = 1;
                        break;
                }
            }

            if (!empty($arrLatePaymentdetails['calculate_from']) && strtolower($arrLatePaymentdetails['calculate_from']) == 'duedate' && !empty($data['dueDate'])) {
                /**
                 * Converted to daily calculations while setting is set to charge
                 * late payment charge from due date
                 */
                //                return $this->calculateDailyLateCharges($event, $component, $data);
                // Test pending
                $end_date = $data['lateChargeCalculationDate'];
                if (isset($data['paidDate']) && !empty($data['paidDate'])) {
                    $end_date = $data['paidDate'];
                }
                $numberOfMonth = $this->getNumberOfMonth(array(
                    'start_date' => $data['dueDate'],
                    'end_date' => $end_date
                ));
                // echo 'numberOfMonth=>'.$numberOfMonth.'##';
            }
            // echo 'numberOfMonth=>'.$numberOfMonth.'##';
            // else
            // {
            // //$arrInvoiceSetting = $this->getInvoiceSetting($event, $component, array('soc_id'=>$data['soc_id']));//get all Unit details
            // }

            if ($arrLatePaymentdetails['interest_amount_type'] == 'fixed') {
                $singleRuleCharges = (float) round($arrLatePaymentdetails['simple_interest'] * $numberOfMonth, 4);
            } else {
                // echo $dueDate.'||'.$todaysDate;
                // $numberOfMonth = 1;//$this->getNumberOfMonth($event, $component, array('start_date' => $dueDate, 'end_date' => $todaysDate));

                $t = (float) round(($numberOfMonth / 12), 4); // exit;//convert to number of year
                $p = (float) $amount; // exit;
                $r = (float) ($arrLatePaymentdetails['simple_interest'] / 100); // exit;

                if (isset($arrLatePaymentdetails['interest_type']) && !empty($arrLatePaymentdetails['interest_type']) && in_array(strtolower($arrLatePaymentdetails['interest_type']), array(
                    'monthly',
                    'quarterly',
                    'half_yearly',
                    'yearly'
                ))) {
                    $n = 12;
                    switch (strtolower($arrLatePaymentdetails['interest_type'])) {
                        case 'quarterly':
                            $n = 4;
                            break;
                        case 'half_yearly':
                            $n = 2;
                            break;
                        case 'yearly':
                            $n = 1;
                            break;
                        case 'monthly':
                        default:
                            $n = 12;
                            break;
                    }
                    $A = (float) round(($p * pow(1 + ($r / $n), $n * $t)), 4); // compound interest formulae
                    $singleRuleCharges = (float) round($A - $p, 4);
                } else {
                    // echo '||'.$p.'*'.$r.'*'.$t.'||';
                    $singleRuleCharges = (float) round(($p * $r * $t), 4); // simple interest formulae
                }
            }
        }

        //         echo 'Rate=='.$singleRuleCharges.'Bye';
        return $singleRuleCharges;
    }

    public function getNumberOfMonth($data = array())
    {
        $begin = new \DateTime($data['start_date']);
        $end = new \DateTime($data['end_date']);
        // $end = $end->modify( '+1 month' );

        $interval = \DateInterval::createFromDateString('1 month');

        $period = new \DatePeriod($begin, $interval, $end);
        $counter = 0;
        foreach ($period as $dt) {
            $counter++;
        }
        if ($counter == 0) {
            $counter = 1;
        }
        return $counter;
    }

    // get member paid invoices
    public function getMemberPaidInvoices($data = array())
    {
        $arrMemberPaidInvoices = array();
        if (!empty($data['unpaidInvoiceDetail'])) {
            foreach ($data['unpaidInvoiceDetail'] as $eachInvoiceDetail) {
                if (isset($eachInvoiceDetail['unit_invoice_number']) && !empty($eachInvoiceDetail['unit_invoice_number'])) {
                    array_push($arrMemberPaidInvoices, $eachInvoiceDetail['unit_invoice_number']);
                    if ($eachInvoiceDetail['total_invoice_due'] >= $data['payment_amount']) {
                        break;
                    }
                }
            }
            return $arrMemberPaidInvoices;
        }
    }

    // get incident invoice unpaid bill
    public function getIncidentInvoiceUnpaidBill($data = array())
    {
        $arrBillingDetail = $arrIncidentBillDetail = array();
        if (!empty($data['unit_id']) && !empty($data['soc_id'])) {
        
            // Build the query
            $query = IncomeCommonBillingCharge::select(
                'ch.id', 'ch.soc_id', 'ch.fk_member_id', 'ch.invoice_number', 'ch.fk_unit_id', 'ch.billing_type',
                'ch.from_date', 'ch.to_date', 'ch.bill_date', 'ch.payment_mode', 'ch.advance_amount', 'ch.amount', 
                'ch.transaction_charges', 'ch.discount_amount', 'ch.payment_instrument', 'ch.payment_status', 'ch.status',
                'ch.bill_from', 'ch.created_date', 'ch.created_by', 'ch.updated_date', 'ch.updated_by', 
                'ch.due_date', 'ui.principal_amount', 'ui.interest_amount', 'ui.outstanding_principal', 
                'ui.outstanding_interest', 'ui.roundoff_amount'
            )
            ->from('income_common_billing_charges as ch')
            ->where('ch.soc_id', $data['soc_id'])
            ->where('ch.fk_unit_id', $data['unit_id'])
            ->whereIn('ch.payment_status', ["partialpaid", "unpaid"])
            ->leftJoin('income_unit_invoices as ui', 'ch.invoice_number', '=', 'ui.invoice_number')
            ->orderBy('ch.id');

            $objBillingDetail = $query->get();
        
            if (!empty($objBillingDetail)) {
                $arrBillingDetail = $objBillingDetail->toArray();

                if (!empty($arrBillingDetail)) {
                    $i = $arrIncidentBillDetail['total_unpaid_invoice_amount'] = 0;

                    foreach ($arrBillingDetail as $eachIncidentBill) {

                        $arrIncidentBillDetail[$i] = $eachIncidentBill;
                        $arrIncidentBillDetail[$i]['from_date'] = date('Y-m-d', strtotime($eachIncidentBill['from_date']));
                        $arrIncidentBillDetail[$i]['to_date'] = date('Y-m-d', strtotime($eachIncidentBill['to_date']));
                        $arrIncidentBillDetail[$i]['bill_date'] = date('Y-m-d', strtotime($eachIncidentBill['bill_date']));
                        $arrIncidentBillDetail[$i]['due_date'] = date('Y-m-d', strtotime($eachIncidentBill['due_date']));

                        // initialize due amount
                        $arrIncidentBillDetail[$i]['due_amount'] = 0;

                        //Get tax amount
                        $taxAmount = $this->getInvoiceTaxAmount(array('soc_id' => $data['soc_id'], 'invoice_number' => $eachIncidentBill['invoice_number']));
                        $arrIncidentBillDetail[$i]['due_amount'] += $taxAmount;

                        $arrIncidentBillDetail[$i]['due_amount'] += (float) round(($eachIncidentBill['amount'] + $eachIncidentBill['transaction_charges'] + $eachIncidentBill['interest_amount'] + $eachIncidentBill['roundoff_amount']) - ($eachIncidentBill['advance_amount'] + $eachIncidentBill['discount_amount']), 3);
                        if (!empty($eachIncidentBill['payment_status']) && strtolower($eachIncidentBill['payment_status']) != 'paid') {
                            $arrIncidentBillDetail[$i]['payment_detail'] = $this->getCommonBillingInvoicePayments(array('soc_id' => $data['soc_id'], 'unit_id' => $data['unit_id'], 'common_bill_id' => $eachIncidentBill['id']));
                            if (!empty($arrIncidentBillDetail[$i]['payment_detail'])) {
                                $arrIncidentBillDetail[$i]['due_amount'] = (float) round($arrIncidentBillDetail[$i]['due_amount'] - ($arrIncidentBillDetail[$i]['payment_detail']['payment_amount'] + $arrIncidentBillDetail[$i]['payment_detail']['tds_deducted']), 3);
                            }
                        } else {
                            $arrIncidentBillDetail[$i]['due_amount'] = 0;
                        }

                        if ($arrIncidentBillDetail[$i]['due_amount'] < 0) {
                            $arrIncidentBillDetail[$i]['due_amount'] = 0;
                        }
                        
                        $arrIncidentBillDetail['total_unpaid_invoice_amount'] += $arrIncidentBillDetail[$i]['due_amount'];
                        $arrIncidentBillDetail['invoice_detail'][$i] = $arrIncidentBillDetail[$i];
                        unset($arrIncidentBillDetail[$i]);
                        $i++;
                    }
                }
            }
        }
        return $arrIncidentBillDetail;
    }

    // get invoice tax amount
    public function getInvoiceTaxAmount($data = array())
    {
        $taxAmount = 0;

        $arrTaxClass = $this->getAppliedTaxDetail($data);
        if (!empty($arrTaxClass)) {
            foreach ($arrTaxClass as $eachTaxLog) {
                $taxAmount += $eachTaxLog['tax_amount'];
            }
        }

        return $taxAmount;
    }

    // get applied tax detail
    public function getAppliedTaxDetail($data = array())
    {
        $arrTaxClass = array();
        // Build the base query
        $query = ChsoneTaxLog::where('soc_id', $data['soc_id'])
        ->whereIn('invoice_number', [$data['invoice_number']]);

        // Check if `showCancelledInvoice` is set, and apply conditions accordingly
        if (isset($data['showCancelledInvoice']) && !empty($data['showCancelledInvoice'])) {
        // Do not exclude cancelled invoices
        $query = ChsoneTaxLog::where('soc_id', $data['soc_id'])
            ->whereIn('invoice_number', [$data['invoice_number']]);
        } else {
        // Exclude cancelled invoices
        $query->where('status', '!=', 'cancelled');
        }

        // Execute the query and convert the result to an array
        $arrTaxClass = $query->get();

        if (!empty($arrTaxClass)) {
            $arrTaxClass = $arrTaxClass->toArray();
        }
        return $arrTaxClass;
    }

    // get common billing invoice payments
    public function getCommonBillingInvoicePayments($data = array())
    {
        $arrCommonBillingCharges = array();
        // Use the Laravel query builder to construct the query
        $query = $this->tenantDB()->table('income_common_billing_payment as icb')
        ->select(
            DB::raw('SUM(icb.payment_amount) as payment_amount'),
            DB::raw('SUM(icb.tds_deducted) as tds_deducted'),
            DB::raw('SUM(icb.transaction_charges) as transaction_charges'),
            DB::raw('SUM(icb.discount_amount) as discount_amount')
        )
        ->where('icb.soc_id', $data['soc_id'])
        ->where('icb.fk_common_bill_id', $data['common_bill_id'])
        ->where('icb.unit_id', $data['unit_id']);

        // Execute the query and fetch the result as an array
        $arrCommonBillingCharges = $query->first();

        $arrCommonBillingCharges = json_decode(json_encode($arrCommonBillingCharges), true);

        if (!empty($arrCommonBillingCharges)) {
            // $arrCommonBillingCharges = current($arrCommonBillingCharges);
            $arrCommonBillingCharges = (array) $arrCommonBillingCharges;
        }
        return $arrCommonBillingCharges;
    }

    public function getIncidentPaymentInvoices($data = array())
    {
        $arrIncidentPaymentDetail = array();
        if (!empty($data['postData']) && !empty($data['arrUnpaidInvoices'])) {
            $arrUnpaidInvoices = $data['arrUnpaidInvoices'];
            $postData = $data['postData'];
            $paidAmount = $postData['payment_amount'];
            if (!empty($arrUnpaidInvoices['invoice_detail'])) {
                $arrTdsDetail = array();
                if (isset($postData['tds_amount']) && !empty($postData['tds_amount'])) {
                    $tds_percentage = (float) round(($postData['tds_amount'] * 100) / $postData['payment_amount'], 3);
                }
                $i = 0;
                $arrIncidentPaymentDetail['member_paid_invoice'] = '';
                foreach ($arrUnpaidInvoices['invoice_detail'] as $eachInvoiceDetail) {
                    if (!empty($paidAmount) && $paidAmount > 0) {
                        $arrIncidentPaymentDetail[$i] = $eachInvoiceDetail;
                        $arrIncidentPaymentDetail['member_paid_invoice'] .= $eachInvoiceDetail['invoice_number'] . ',';

                        if ($paidAmount > $eachInvoiceDetail['due_amount']) {
                            $arrIncidentPaymentDetail[$i]['payment_status'] = 'paid';
                            $arrIncidentPaymentDetail[$i]['payment_amount'] = $eachInvoiceDetail['due_amount'];
                            $paidAmount = (float) round($paidAmount - $eachInvoiceDetail['due_amount'], 3);
                        } elseif ($paidAmount < $eachInvoiceDetail['due_amount']) {
                            $arrIncidentPaymentDetail[$i]['payment_status'] = 'partialpaid';
                            $arrIncidentPaymentDetail[$i]['payment_amount'] = $paidAmount;
                            $paidAmount = 0;
                        } elseif ($paidAmount == $eachInvoiceDetail['due_amount']) {
                            $arrIncidentPaymentDetail[$i]['payment_status'] = 'paid';
                            $arrIncidentPaymentDetail[$i]['payment_amount'] = $eachInvoiceDetail['due_amount'];
                            $paidAmount = 0;
                        }

                        $arrIncidentPaymentDetail[$i]['tds_amount'] = 0;
                        if (!empty($tds_percentage)) {
                            $arrIncidentPaymentDetail[$i]['tds_amount'] = (float) round(($tds_percentage * $arrIncidentPaymentDetail[$i]['payment_amount']) / 100, 3);
                        }

                        $arrIncidentPaymentDetail['invoice_payment_detail'][$i] = $arrIncidentPaymentDetail[$i];

                        // Initialize paid_advance_amount if it doesn't exist
                        if (!isset($arrIncidentPaymentDetail['invoice_payment_detail'][$i]['paid_advance_amount'])) {
                            $arrIncidentPaymentDetail['invoice_payment_detail'][$i]['paid_advance_amount'] = 0;
                        }

                        //Add advance amount     
                        if (count($arrUnpaidInvoices['invoice_detail']) == ($i + 1)) {
                            $arrIncidentPaymentDetail['invoice_payment_detail'][$i]['paid_advance_amount'] += $paidAmount;
                        }

                        unset($arrIncidentPaymentDetail[$i]);
                    }
                    $i++;
                }
                $arrIncidentPaymentDetail['member_paid_invoice'] = trim($arrIncidentPaymentDetail['member_paid_invoice'], ',');
            }
        } elseif (!empty($data['postData']) && $data['postData']['total_unpaid_amount'] == 0 && !empty($data['postData']['unit_id'])) {
            $arrIncidentPaymentDetail['invoice_payment_detail'][0]['id'] = 0;
            $arrIncidentPaymentDetail['invoice_payment_detail'][0]['fk_unit_id'] = $data['postData']['unit_id'];
            $arrIncidentPaymentDetail['invoice_payment_detail'][0]['tds_amount'] = isset($data['postData']['tds_amount']) ? $data['postData']['tds_amount'] : 0;
            $arrIncidentPaymentDetail['invoice_payment_detail'][0]['paid_advance_amount'] = $data['postData']['payment_amount'];
            $arrIncidentPaymentDetail['invoice_payment_detail'][0]['payment_amount'] = 0;
        }
        return $arrIncidentPaymentDetail;
    }

    public function updateIncomePaymentTrackerStatus($data = array())
    {
        $arrResponse['status'] = 'error';
        $data['updated_date'] = date('Y-m-d H:i:s');
        $updateResponse = $this->updateIncomeInvoicePaymentTrackerStatus($data);
        if (!empty($updateResponse) && strtolower($updateResponse['status']) == 'success') {
            $arrResponse['status'] = 'success';
        }
        return $arrResponse;
    }

    public function updateIncomeInvoicePaymentTrackerStatus($data, $full_list = false)
    {
        $arrResponse = ['status' => 'success', 'message' => []];

        $updateData = [
            'updated_date' => $data['updated_date']
        ];

        if (!empty($data['status'])) {
            $updateData['status'] = $data['status'];
        }
        if (!empty($data['transaction_status'])) {
            $updateData['transaction_status'] = $data['transaction_status'];
        }
        if (!empty($data['invoice_number'])) {
            $updateData['invoice_number'] = $data['invoice_number'];
        }
        if (!empty($data['payment_date'])) {
            $updateData['payment_date'] = $data['payment_date'];
        }
        if (!empty($data['other_information'])) {
            $updateData['other_information'] = $data['other_information'];
        }
        if (!empty($data['reversal_note'])) {
            $updateData['reversal_note'] = $data['reversal_note'];
        }
        if (!empty($data['unit_id'])) {
            $updateData['unit_id'] = $data['unit_id'];
        }
        if (!empty($data['bill_type'])) {
            $updateData['bill_type'] = $data['bill_type'];
        }
        if (!empty($data['total_unpaid_amount'])) {
            $updateData['total_due_amount'] = $data['total_unpaid_amount'];
        }
        if (!empty($data['bank_and_branch_name'])) {
            $updateData['payment_instrument'] = $data['total_unpaid_amount'];
        }

        $result = IncomeInvoicePaymentTracker::where('soc_id', $data['soc_id'])
            ->where('id', $data['payment_tracker_id'])
            ->update($updateData);

        if (!$result) {
            $arrResponse = ['status' => 'error', 'message' => ['Failed to update payment tracker']];
        }

        return $arrResponse;
    }

    public function updateChequeInvoicePaymentTracker($data = array())
    {
        $arrResponse = array(
            'status' => 'error',
            'message' => array()
        );
        $arrOtherInfo = array();

        if (isset($data['arrPostData']) && !empty($data['arrPostData'])) {

            if (strtolower($data['oldTrackerData']['bill_type']) == 'member') {
                $data['oldTrackerData']['other_information']["member_detail"]["bank_ledger"] = $data['arrPostData']['bank_ledger'];
            } elseif (strtolower($data['oldTrackerData']['bill_type']) == 'common_bill') {
                $data['oldTrackerData']['other_information']['common_bill_detail']['bank_ledger'] = $data['arrPostData']['bank_ledger'];
            } elseif (strtolower($data['oldTrackerData']['bill_type']) == 'nonmember') {
                $data['oldTrackerData']['other_information']['nonmember_detail']['bank_ledger'] = $data['arrPostData']['bank_ledger'];
            } elseif (strtolower($data['oldTrackerData']['bill_type']) == 'suspense') {
                $data['oldTrackerData']['other_information']['suspense_detail']['bank_ledger'] = $data['arrPostData']['bank_ledger'];
            }
        }
        $opt = IncomeInvoicePaymentTracker::where('soc_id', $data['soc_id'])
        ->where('id', $data['oldTrackerData']['id'])
        ->first();
        
        if ($opt) {
            $opt->updated_date = date('Y-m-d H:i:s');
            $opt->updated_by = !empty($data['user_id']) ? $data['user_id'] : '';
            $opt->soc_id = !empty($data['soc_id']) ? $data['soc_id'] : '';
            $opt->transaction_reference = (isset($data['arrPostData']['transaction_reference'])) ? $data['arrPostData']['transaction_reference'] : '';
            if(isset($data['arrPostData']['payer_bank_details'])) {
                $opt->payment_instrument = (isset($data['arrPostData']['payer_bank_details']) && !empty($data['arrPostData']['payer_bank_details'])) ? $data['arrPostData']['payer_bank_details'] : '';
            } else {
                $opt->payment_instrument = (isset($data['arrPostData']['bank_and_branch_name']) && !empty($data['arrPostData']['bank_and_branch_name'])) ? $data['arrPostData']['bank_and_branch_name'] : '';
            }
            // $opt->payment_instrument = (isset($data['arrPostData']['payer_bank_details']) && !empty($data['arrPostData']['payer_bank_details'])) ? $data['arrPostData']['payer_bank_details'] : '';
            $opt->received_from = (isset($data['arrPostData']['received_from']) && !empty($data['arrPostData']['received_from'])) ? $data['arrPostData']['received_from'] : '';
            $opt->tds_deducted = isset($data['arrPostData']['tds_amount']) ? round($data['arrPostData']['tds_amount'], 2) : 0;
            $opt->payment_amount = round($data['arrPostData']['payment_amount'], 2);
            $opt->writeoff_amount = isset($data['arrPostData']['writeoff_amount']) ? round($data['arrPostData']['writeoff_amount'], 2) : 0;
            $opt->other_information = serialize($data['oldTrackerData']['other_information']);
            $opt->payment_date = ((!empty($data['arrPostData']['payment_date']) && strrchr($data['arrPostData']['payment_date'], '/')) ? $this->getDatabaseDate($data['arrPostData']['payment_date']) : ((!empty($data['arrPostData']['payment_date'])) ? $data['arrPostData']['payment_date'] : date('Y-m-d')));
            $opt->cheque_date = ((!empty($data['arrPostData']['cheque_date']) && strrchr($data['arrPostData']['cheque_date'], '/')) ? $this->getDatabaseDate($data['arrPostData']['cheque_date']) : ((!empty($data['arrPostData']['cheque_date'])) ? $data['arrPostData']['cheque_date'] : ''));

            if (!$opt->save()) {
                foreach ($opt->getMessages() as $messages) {
                    $arrMessages[] = (string) $messages;
                }
                $arrResponse['message'] = $arrMessages;
            } else {
                $arrResponse['status'] = 'success';
            }
        } else {
            $arrResponse['message'] = 'Record not found';
        }
        // print_r($arrResponse);exit;
        return $arrResponse;
    }
}
