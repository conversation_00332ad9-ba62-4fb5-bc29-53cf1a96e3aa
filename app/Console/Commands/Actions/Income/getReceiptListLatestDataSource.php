<?php

namespace App\Console\Commands\Actions\Income;

use Illuminate\Console\Command;
use App\Console\Commands\Action;
use Illuminate\Support\Facades\DB;

class getReceiptListLatestDataSource extends Action
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'datasource:getReceiptListLatest {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */
    public function apply()
    {
        $unit_id = $this->input['unit_id'];

        // check if unit id is valid or not from chsone_units_master table
        $unit = $this->tenantDB()->table('chsone_units_master')
            ->where('unit_id', $unit_id)
            ->get();
        
        if(count($unit) == 0) {
            $this->status = 'error';
            $this->message = 'Please provide a valid unit id';
            $this->statusCode = 400;
            $this->data = [];
            return;
        }

        $obj = $this->tenantDB()->table('income_invoice_payment_tracker')
        ->select('id',
            'unit_id',
            'payment_date',
            'receipt_number',
            'received_from',
            DB::raw("
                CASE 
                    WHEN payment_mode = 'cash' THEN 'Cash'
                    WHEN payment_mode = 'cheque' THEN 'Cheque'
                    WHEN payment_mode = 'cashtransfer' THEN 'Electronic Fund Transfer'
                    ELSE payment_mode
                END as payment_mode
            "),
            'payment_amount',
            'payment_note',
            'status')
        ->where('unit_id', $unit_id)
        ->where('status', 'Y')
        ->orderByDesc('id')
        ->orderByDesc('payment_date')
        ->limit(3);

        $results = $obj->get();

        $this->data = $results;
    }
}
