<?php

namespace App\Console\Commands\Actions\Income;

use Illuminate\Console\Command;
use App\Console\Commands\Action;

class IncomeAccountsListDataSource extends Action
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'datasource:IncomeAccountsList {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Get the income accounts details';

    protected $formatter = [
        'id' => '',
        'account_name' => '',
        'display_name' => '',
        'fk_income_ledger_id' => '',
        'ledger_account_name' => ''
    ];

    protected $formatterByKeys = ['id'];

    protected $mapper = [
        'id' => 'account_id',
    ];

    protected $hugeData = true;

    protected $schema = [
        "table" => [
            "tabs" => [
                "Member Income Account",
                "Non Member Income Account"
            ],
            "fields" => [
                "*"
            ],
            "bulk_update_path" => "/admin/income-tracker-setting/addIncomeAccount",
            "columns" => [
                [
                    "title" => "Income Account",
                    "key" => "account_name"
                ],
                [
                    "title" => "Invoice Print Name",
                    "key" => "display_name"
                ],
                [
                    "title" => "Ledgers",
                    "key" => "fk_income_ledger_id",
                    "editable" => true,
                    "type" => "select",
                    "api_path" => "/admin/accounts/viewLedgers?current_tab=income&per_page=300",
                ]
            ]
        ]
    ];

    protected $schema2 = [
        "table" => [
            "tabs" => [
                "Member Income Account",
                "Non Member Income Account"
            ],
            "fields" => [
                "*"
            ],
            "bulk_update_path" => "/admin/income-tracker-setting/addIncomeAccount",
            "add_row" => true,
            "columns" => [
                [
                    "title" => "Income Account",
                    "key" => "account_name",
                    "editable" => true,
                ],
                [
                    "title" => "Ledgers",
                    "key" => "fk_income_ledger_id",
                    "editable" => true,
                    "type" => "select",
                    "api_path" => "/admin/accounts/viewLedgers?current_tab=income&per_page=300",
                ],
                [
                    "title" => "Actions",
                    "key" => "actions",
                    "type" => "actions",
                    "actions" => [
                        [
                            "title" => "Delete",
                            "icon" => "ri-delete-bin-5-line",
                            "isdelete" => true,
                            "show_on" => [
                                "_action" => [
                                    "add"
                                ]
                            ]
                        ],
                    ]
                ]

            ]
        ]
    ];

    /**
     * Execute the console command.
     */
    public function apply()
    {
        $CurrentTab = 'member_income_account';
        $account_type = 'member';

        if (isset($this->input['current_tab']) && $this->input['current_tab'] != "null") {
            $CurrentTab = $this->input['current_tab'];
        }

        if (strtolower($CurrentTab) == 'member_income_account' || $CurrentTab == 'Member Income Account' || $CurrentTab == 'member income account') {

            $this->meta['schema'] = $this->schema;
        } elseif (strtolower($CurrentTab) == 'non_member_income_account' || $CurrentTab == 'Non Member Income Account' || $CurrentTab == 'non member income account') {

            $account_type = 'nonmember';
            $this->meta['schema'] = $this->schema2;
        }

        $obj = $this->tenantDB()->table('income_accounts AS IA')
            ->select('IA.account_id as id', 'IA.account_name', 'IA.display_name', 'IA.fk_income_ledger_id', 'ledger_account_name', 'ledger_account_name as title')
            ->leftJoin('chsone_grp_ledger_tree AS GLT', 'IA.fk_income_ledger_id', '=', 'GLT.ledger_account_id')
            ->where('IA.account_type', $account_type);

        $result = $obj->get();
        $this->data = $this->format($result->toArray());
    }
}
