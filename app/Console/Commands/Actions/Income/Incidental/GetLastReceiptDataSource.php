<?php

namespace App\Console\Commands\Actions\Income\Incidental;

use Illuminate\Console\Command;
use App\Console\Commands\Action;

class GetLastReceiptDataSource extends Action
{

    protected $signature = 'datasource:GetLastReceipt {flowId} {parentId} {input}';

    protected $description = 'Get Last Receipt for all the dues like incidental maintainence member non member advance';

    protected $rules = [];

    protected $rulesMessage = [];

    public function apply()
    {
        $id = $this->input['id'];

        $obj = $this->tenantDB()->table('income_invoice_payment_tracker')
        ->select('*')
        ->where('unit_id', $id)
        ->where('status', 'Y')
        ->where('created_by' , '!=', '0');

        // get the last 3 records
        $result = $obj->orderBy('id', 'desc')->limit(3)->get();

        $this->data = $result->toArray();
    }

}