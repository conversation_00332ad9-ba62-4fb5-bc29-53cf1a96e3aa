<?php

namespace App\Console\Commands\Actions\Income;

use App\Console\Commands\Action;
use App\Models\Tenants\IncomeInvoicePayment;

class Getpayments extends Action
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'action:getpayments {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Get list of payments';


    protected $formatter =  [
        'unit_id' => '',
        'invoices' => [
            'invoice_number' => '',
            'unit_invoice_id' => '',
            'payments' => [
                'payment_amount' => '',
                'writeoff_amount' => '',
                'tds_deducted' => '',
                'tax_deducted' => '',
                'transaction_charges' => '',
                'discount_amount' => '',
                'late_payment_charges' => '',
                'payment_date' => '',
            ]
        ]
    ];

    protected $formatterByKeys =  ['unit_id','invoice_number', 'receipt_number'];

    protected $smartFormatter = [
        'unit_id' => [
            'unit_id' => '',
            'invoices' => [
                'invoice_number' => [
                    'invoice_number' => '',
                    'unit_invoice_id' => '',
                    'payments' => [
                        'receipt_number' => [
                            'payment_amount' => '',
                            'writeoff_amount' => '',
                            'tds_deducted' => '',
                            'tax_deducted' => '',
                            'transaction_charges' => '',
                            'discount_amount' => '',
                            'late_payment_charges' => '',
                            'payment_date' => '',
                            'total_payment_amount' => 'adds:payment_amount,writeoff_amount,tds_deducted,late_payment_charges',
                        ]
                        ],
                        'invoice_paid_amount' => 'sum:payments.*.total_payment_amount',
                ]
            ]
        ]
    ];

    protected $mapper = [
        'unit_id' => 'fk_unit_id'];

    /**
     * Execute the console command.
     */
    public function apply()
    {
        $payments = IncomeInvoicePayment::select('fk_unit_id as unit_id', 'fk_unit_invoice_id as unit_invoice_id', 'invoice_number', 'receipt_number', 'payment_amount', 'writeoff_amount', 'tds_deducted', 'tax_deducted', 'transaction_charges', 'discount_amount', 'late_payment_charges', 'payment_date')
                ->where('status', 'success');

        $payments = $this->filter($payments);
        $payments = $payments->get();
        $this->data = $this->smartFormat($payments->toArray());
        $this->message = 'Get payments success';
        $this->statusCode = 200;
    }
}
