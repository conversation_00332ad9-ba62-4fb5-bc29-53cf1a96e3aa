<?php

namespace App\Console\Commands\Actions\Income;

use Illuminate\Console\Command;
use App\Console\Commands\Action;
use Illuminate\Support\Facades\DB;

class getUpdatePaymentTrackerDetailsDataSource extends Action
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'datasource:getUpdatePaymentTrackerDetails {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command to get update payment tracker details';

    /**
     * Execute the console command.
     */
    public function apply()
    {
        $payment_tracker_id = $this->input['payment_tracker_id'];

        // check if payment tracker id is valid or not from income_invoice_payment_tracker table
        $payments = $this->tenantDB()->table('income_invoice_payment_tracker')
        ->select(
            'id',
            'received_from',
            DB::raw('CAST(transaction_reference AS UNSIGNED) as transaction_reference'), // Ensure numeric format
            'cheque_date',
            'payment_instrument as bank_and_branch_name',
            'late_payment_charges',
            'total_due_amount',
            'payment_amount as receipt_amount',
            'writeoff_amount',
            'tds_deducted as tds_amount',
            'payment_date as receipt_date',
            'other_information'
            // DB::raw('SUBSTRING_INDEX(SUBSTRING_INDEX(other_information, \'s:11:"bank_ledger";s:3:"\', -1), \'"\', 1) AS bank_ledger')
        )
        ->where('id', $payment_tracker_id)
        ->first();
        
        
        if(empty($payments)) {
            $this->status = 'error';
            $this->message = 'Please provide a valid payment tracker id';
            $this->statusCode = 404;
            $this->data = [];
            return;
        } else {
            $payments->other_information = unserialize($payments->other_information);

            $payments->bank_ledger = 0;

            // Check each possible key within the unserialized data for bank_ledger
            if (is_array($payments->other_information)) {
                if (isset($payments->other_information['common_bill_detail']['bank_ledger'])) {
                    $payments->bank_ledger = (int) $payments->other_information['common_bill_detail']['bank_ledger'] ?? 0;
                } elseif (isset($payments->other_information['member_detail']['bank_ledger'])) {
                    $payments->bank_ledger = (int) $payments->other_information['member_detail']['bank_ledger'] ?? 0;
                } elseif (isset($payments->other_information['nonmember_detail']['bank_ledger'])) {
                    $payments->bank_ledger = (int) $payments->other_information['nonmember_detail']['bank_ledger'] ?? 0;
                } elseif (isset($payments->other_information['credit_account_detail']['bank_account'])) {
                    $payments->bank_ledger = (int) $payments->other_information['credit_account_detail']['bank_account'] ?? 0;
                }
            }

            if($payments->writeoff_amount > 0) {
                $payments->show_writeoff = true;
            } else {
                $payments->show_writeoff = false;
            }

            if($payments->tds_amount > 0) {
                $payments->receipt_amount = $payments->receipt_amount - $payments->tds_amount;
                $payments->show_tds = true;
            } else {
                $payments->show_tds = false;
            }

            unset($payments->other_information);
        }

        $this->data = $payments;
    }
}
