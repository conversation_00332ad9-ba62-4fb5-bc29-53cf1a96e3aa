<?php

namespace App\Console\Commands\Actions\Income;

use Illuminate\Console\Command;
use App\Console\Commands\Action;
use Illuminate\Support\Facades\DB;

class GetMembersDataSource extends Action
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'datasource:GetMembers {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Get list of Members';

    protected $formatter =  [];

    protected $formatterByKeys =  ['unit_id', 'invoice_number', 'receipt_number'];

    protected $smartFormatter = [];

    protected $mapper = [
        'unit_id' => 'fk_unit_id'
    ];

    /**
     * Execute the console command.
     */

    public function apply()
    {
        $arrGetData = [];

        // Get query parameters from the request
        $arrData = request()->query();
        $posted_values = $arrData['q'];

        $arrListenerdata = ['posted_values' => ['searchdata' => $posted_values[0], 'member_or_tenant' => $posted_values[1], 'date' => $posted_values[2], 'type' => $posted_values[3]]];

        $list = array();
        if (!empty($arrListenerdata['posted_values']['type'])) {

            if ($arrListenerdata['posted_values']['type'] == 'suspense') {
                $arrListenerdata['posted_values']['type'] = $arrListenerdata['posted_values']['used_for'];
            }
            $result = $this->getMembersDetails($arrListenerdata); //get all Unit details

        }
    }

    public function getMembersDetails($data)
    {
        $current_date = date('Y-m-d');
        $soc_id = request()->query()['company_id'];
        $searchdata = $data['posted_values']['searchdata'];
        $date = (isset($data['posted_values']['date']) && !empty($data['posted_values']['date'])) ? $data['posted_values']['date'] : $current_date;

        $condition = 'm.soc_id = "' . $soc_id . '" AND u.soc_id = "' . $soc_id . '" AND m.status = 1 AND u.status = 1 AND (
        CONCAT(m.member_first_name, " ", m.member_last_name) like "%' . $searchdata . '%" OR 
        CONCAT(m.member_first_name, " ", m.member_last_name, " (", u.soc_building_name, "-", u.unit_flat_number, ")") like "%' . $searchdata . '%"
    ) AND ( 
        (m.effective_date <= "' . $date . '" AND m.cancel_date >= "' . $date . '") OR 
        (m.effective_date <= "' . $date . '" AND m.cancel_date = "0000-00-00")
    ) AND ( 
        (u.effective_date <= "' . $date . '" AND u.cancel_date >= "' . $date . '") OR 
        (u.effective_date <= "' . $date . '" AND u.cancel_date = "0000-00-00")
    )';

        // clause for whom to search
        if (isset($data['posted_values']['member_or_tenant'])) {
            switch (strtolower($data['posted_values']['member_or_tenant'])) {
                case 'member':
                    $condition .= ' AND m.is_tenant = "0"';
                    break;
                case 'tenant':
                    $condition .= ' AND m.is_tenant = "1"';
                    break;
                default:
                    break;
            } // end of switch
        }
        if (isset($data['posted_values']['exclude_members']) && !empty($data['posted_values']['exclude_members'])) {
            if (is_array($data['posted_values']['exclude_members'])) {
                $condition .= ' AND m.id NOT IN(' . (implode(',', $data['posted_values']['exclude_members'])) . ')';
            } else {
                $condition .= ' AND m.id != "' . $data['posted_values']['exclude_members'] . '"';
            }
        }
        if (isset($data['posted_values']['member_types']) && !empty($data['posted_values']['member_types'])) {
            if (is_array($data['posted_values']['member_types'])) {
                $condition .= ' AND m.member_type_id IN(' . (implode(',', $data['posted_values']['member_types'])) . ')';
            } else {
                $condition .= ' AND m.member_type_id = "' . $data['posted_values']['member_types'] . '"';
            }
        }

        // query for relevant members
        $queryBuilder = $this->tenantDB()->table('chsone_members_master as m')
            ->select([
                'm.id',
                DB::raw("CONCAT(m.member_first_name, ' ', m.member_last_name) as member_name"),
                'u.soc_building_name as building_name',
                'u.unit_flat_number as unit_flat_number',
                DB::raw("CONCAT(m.member_first_name, ' ', m.member_last_name, ' (', u.soc_building_name, '-', u.unit_flat_number, ')') as member"),
                DB::raw("CONCAT(m.member_first_name, ' ', m.member_last_name) as member_name"),
                'u.soc_building_name',
                'u.unit_flat_number',
                'u.unit_id',
            ])
            ->leftJoin('chsone_units_master as u', 'u.unit_id', '=', 'm.fk_unit_id')
            ->leftJoin('chsone_member_type_master as mtype', 'mtype.member_type_id', '=', 'm.member_type_id')
            ->whereRaw($condition)
            ->where('mtype.member_type_name', '=', 'primary')
            ->get();
            
            return $this->data = $queryBuilder;
    }
}
