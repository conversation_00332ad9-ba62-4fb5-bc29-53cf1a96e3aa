<?php

namespace App\Console\Commands\Actions\Income;

use Illuminate\Console\Command;
use App\Console\Commands\Action;
use App\Models\Tenants\ChsoneCreditAccount;
use App\Models\Tenants\ChsoneGrpLedgerTree;
use App\Models\Tenants\ChsoneLedgerTransaction;
use App\Models\Tenants\ChsoneMembersMaster;
use App\Models\Tenants\ChsoneMemberTypeMaster;
use App\Models\Tenants\ChsoneTaxLog;
use App\Models\Tenants\ChsoneUnitsMaster;
use App\Models\Tenants\IncomeBillableItem;
use App\Models\Tenants\IncomeCommonAreaCharge;
use App\Models\Tenants\IncomeInvoiceParticular;
use App\Models\Tenants\IncomeInvoicePayment;
use App\Models\Tenants\IncomeInvoicePaymentTracker;
use App\Models\Tenants\IncomeInvoiceRule;
use App\Models\Tenants\IncomeInvoiceSetting;
use App\Models\Tenants\IncomeLatePaymentCharge;
use App\Models\Tenants\IncomeUnitInvoice;
use App\Models\Tenants\SocAccountFinancialYearMaster;
use Illuminate\Support\Facades\Config;
use Carbon\Carbon;

class incomePaymentTrackerConfirmationReversalDataSource extends Action
{
    protected $constants;

    public function __construct() {
        parent::__construct();
        $this->constants = Config::get("constants");
    }

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'datasource:incomePaymentTrackerConfirmationReversal {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Income Payment Tracker Confirmation Reversal Data Source';

    /**
     * Execute the console command.
     */
    public function apply()
    {
        try   {
            $soc_id = $this->input['company_id'];
            $payment_tracker_id = $this->input['payment_tracker_id'];
            $current_date = date('Y-m-d');
            $reversal_note = $this->input['reversal_note'];
    
            // check the status of payment tracker in database
            $paymentDetails = $this->tenantDB()->table('income_invoice_payment_tracker')
                ->where('id', $payment_tracker_id)
                ->where('soc_id', $soc_id)
                ->select('status')
                ->first();
    
            if(empty($paymentDetails)) {
                $this->status = 'error';
                $this->message = 'No payment tracker found or provide valid payment tracker id.';
                $this->statusCode = 400;
                return;
            }
    
            if($paymentDetails->status == 'reversed') {
                $this->status = 'error';
                $this->message = 'Payment already reversed.';
                $this->statusCode = 400;
                return;
            }
    
            $arrPaymentTrackerListener = array('soc_id' => $soc_id, 'payment_tracker_id' => $payment_tracker_id, 'status' => 'Y', 'payment_reversal' => 'y', 'bill_type' => array('member', 'creditaccount-member'));
    
            $arrPostData = $this->getInvoicePaymentTrackerDetail($arrPaymentTrackerListener);
    
            if (!empty($arrPostData)) {
                $unit_id = $arrPostData['unit_id'];
                $arrPostData['tds_amount'] = $arrPostData['tds_deducted'];
                $arrDataListener['soc_id'] = $soc_id;
                $arrDataListener['unit_id'] = $unit_id;
                $arrPostData['payment_tracker_id'] = $arrPostData['id'];
                $arrPostData['reversal_note'] = $reversal_note;
                $arrPostData['user_id'] = $user_id = $arrPostData['other_information']['user_id'] ?? 0;
                $arrPostData['user_name'] = $user_name = isset($arrPostData['other_information']['user']['first_name']) ? $arrPostData['other_information']['user']['first_name'].' '.$arrPostData['other_information']['user']['last_name'] : '';
    
                if (!empty($unit_id)) {
                    $arrIncomeInvoiceMemberDetail = $this->getMemberDetail($arrDataListener);
                    if (empty($arrIncomeInvoiceMemberDetail)) {
                        $this->status = 'error';
                        $this->message = 'Member not allotted to unit.';
                        $this->statusCode = 400;
                        return false;
                    }
                    $arrPostData['member_name'] = $arrIncomeInvoiceMemberDetail['member_first_name'] . ' ' . $arrIncomeInvoiceMemberDetail['member_last_name'];
                }
    
                // revers payment tracker
                $arrResponse = $this->paymentReversalProcess(array('soc_id' => $soc_id, 'user_id' => $user_id, 'user_name' => $user_name, 'arrPostData' => $arrPostData, 'arrMemberDetail' => $arrIncomeInvoiceMemberDetail));
                if (!empty($arrResponse) && strtolower($arrResponse['status']) == 'success') {
                    $this->status = 'success';
                    $this->message = 'Payment Reversed Successfully';
                    $this->statusCode = 200;
                    return;
                } else {
                    $this->status = 'error';
                    $this->message = 'Failed to reverse payment.';
                    $this->statusCode = 400;
                    return;
                }
    
            } else {
                $this->status = 'error';
                $this->message = 'Unable to complete transaction, No invoice found.';
                $this->statusCode = 400;
            }
        } catch (\Exception $e) {
            dd($e);
        }
    }

    // Get invoice payment tracker detail
    public function getInvoicePaymentTrackerDetail($data = array())
    {
        $arrIncomeInvoicePaymentTracker = array();
        // Start building the query for IncomeInvoicePaymentTracker model
        $query = IncomeInvoicePaymentTracker::where('soc_id', $data['soc_id'])->where('id', $data['payment_tracker_id']);

        // Add conditions for unit_id if present
        if (isset($data['unit_id']) && !empty($data['unit_id'])) {
            $query->where('unit_id', $data['unit_id']);
        }

        // Add conditions for status if present
        if (isset($data['status']) && !empty($data['status'])) {
            if (is_array($data['status'])) {
                $query->whereIn('status', $data['status']);
            } else {
                $query->where('status', $data['status']);
            }
        }

        // Add conditions for current_date if present
        if (isset($data['current_date']) && !empty($data['current_date'])) {
            $query->where('payment_date', '<=', $data['current_date']);
        }

        // Add conditions for payment reversal if applicable
        if (!empty($data['payment_reversal']) && strtolower($data['payment_reversal']) == 'y') {
            $query->where('created_date', '>=', ACTIVE_PAYMENT_REVERSAL_DATE);
        }

        // Add conditions for bill_type if present
        if (isset($data['bill_type']) && !empty($data['bill_type'])) {
            $query->whereIn('bill_type', $data['bill_type']);
        }

        // Get the first result from the query
        $objIncomeInvoicePaymentTracker = $query->first();
        if (!empty($objIncomeInvoicePaymentTracker)) {
            $arrIncomeInvoicePaymentTracker = $objIncomeInvoicePaymentTracker->toArray();

            $arrIncomeInvoicePaymentTracker['member_paid_invoice'] = $arrIncomeInvoicePaymentTracker['invoice_number'];
            $arrIncomeInvoicePaymentTracker['receipt_number'] = strtoupper($arrIncomeInvoicePaymentTracker['receipt_number']);
            $arrIncomeInvoicePaymentTracker['total_unpaid_amount'] = $arrIncomeInvoicePaymentTracker['total_due_amount'];
            unset($arrIncomeInvoicePaymentTracker['invoice_number'], $arrIncomeInvoicePaymentTracker['total_due_amount']);
            $arrIncomeInvoicePaymentTracker['other_information'] = unserialize($arrIncomeInvoicePaymentTracker['other_information']);

            // set payment for
            $arrIncomeInvoicePaymentTracker['payment_for'] = 'Maintenance Invoice';
            if (strtolower($arrIncomeInvoicePaymentTracker['bill_type']) == 'common_bill') {
                $arrIncomeInvoicePaymentTracker['payment_for'] = 'Incidental Bill';
            } elseif (strtolower($arrIncomeInvoicePaymentTracker['bill_type']) == 'nonmember') {
                $arrIncomeInvoicePaymentTracker['payment_for'] = 'Nonmember Bill';
            } elseif (strtolower($arrIncomeInvoicePaymentTracker['bill_type']) == 'suspense') {
                $arrIncomeInvoicePaymentTracker['payment_for'] = 'Suspense Reciept';
            } elseif (strtolower($arrIncomeInvoicePaymentTracker['bill_type']) == 'creditaccount-member') {
                if (
                    !empty($arrIncomeInvoicePaymentTracker['other_information']['credit_account_detail']['credit_used_type']) &&
                    strtolower($arrIncomeInvoicePaymentTracker['other_information']['credit_account_detail']['credit_used_type']) == 'refundable'
                ) {
                    $arrIncomeInvoicePaymentTracker['payment_for'] = 'Member Refundable Advance';
                } elseif (
                    !empty($arrIncomeInvoicePaymentTracker['other_information']['credit_account_detail']['credit_used_type']) &&
                    strtolower($arrIncomeInvoicePaymentTracker['other_information']['credit_account_detail']['credit_used_type']) == 'adjustable'
                ) {
                    $arrIncomeInvoicePaymentTracker['payment_for'] = 'Member Adjustable Advance';
                }
            }
            if (!empty($arrIncomeInvoicePaymentTracker['payment_mode']) && in_array($arrIncomeInvoicePaymentTracker['payment_mode'], array(YES_BANK_PG, YES_BANK_ECOLLECT, PAYTM_PG, MOBIKWIK_PG, CASHFREE_PG, MOBIKWIK_WALLET, HDFC_PG, ATOM_PG))) {
                switch ($arrIncomeInvoicePaymentTracker['payment_mode']) {
                    case YES_BANK_PG:
                        $arrIncomeInvoicePaymentTracker['online_payment_display_mode'] = DISPLAY_YES_BANK;
                        break;
                    case YES_BANK_ECOLLECT:
                        $arrIncomeInvoicePaymentTracker['online_payment_display_mode'] = DISPLAY_YES_BANK_ECOLLECT;
                        break;
                    case MOBIKWIK_WALLET:
                        $arrIncomeInvoicePaymentTracker['online_payment_display_mode'] = DISPLAY_MOBIKWIK_WALLET;
                        break;
                    case PAYTM_PG:
                        $arrIncomeInvoicePaymentTracker['online_payment_display_mode'] = DISPLAY_PAYTM;
                        break;
                    case MOBIKWIK_PG:
                        $arrIncomeInvoicePaymentTracker['online_payment_display_mode'] = DISPLAY_MOBIKWIK_PG;
                        break;
                    case CASHFREE_PG:
                        $arrIncomeInvoicePaymentTracker['online_payment_display_mode'] = DISPLAY_CASHFREE_PG;
                        break;
                    case HDFC_PG:
                        $arrIncomeInvoicePaymentTracker['online_payment_display_mode'] = DISPLAY_HDFC_PG;
                        break;
                    case ATOM_PG:
                        $arrIncomeInvoicePaymentTracker['online_payment_display_mode'] = DISPLAY_ATOM_PG;
                        break;
                }
            }

            // format date
            if (!empty($arrIncomeInvoicePaymentTracker['created_date'])) {
                $payment_date = current(explode(' ', $arrIncomeInvoicePaymentTracker['created_date']));
                if (!empty($payment_date)) {
                    $arrIncomeInvoicePaymentTracker['created_date'] = $payment_date;
                }
            }

            // setting other information field
            if (!empty($arrIncomeInvoicePaymentTracker['other_information'])) {
                // setting writeoff particular fields
                if (!empty($arrIncomeInvoicePaymentTracker['other_information']['writeoff_detail'])) {
                    $arrIncomeInvoicePaymentTracker['total_particular'] = $arrIncomeInvoicePaymentTracker['other_information']['writeoff_detail']['total_particular'];
                    for ($i = 1; $i <= $arrIncomeInvoicePaymentTracker['total_particular']; $i++) {
                        $arrIncomeInvoicePaymentTracker["rule_$i"] = $arrIncomeInvoicePaymentTracker['other_information']['writeoff_detail']["rule_$i"];
                        $arrIncomeInvoicePaymentTracker["particular_$i"] = $arrIncomeInvoicePaymentTracker['other_information']['writeoff_detail']["particular_$i"];
                        $arrIncomeInvoicePaymentTracker["writeoff_amount_$i"] = $arrIncomeInvoicePaymentTracker['other_information']['writeoff_detail']["writeoff_amount_$i"];
                    }
                }
            }
        }
        return $arrIncomeInvoicePaymentTracker;
    }

    // fetch member detail
    public function getMemberDetail($data = array())
    {
        $arrMemberMaster = [];
        $id = ChsoneMemberTypeMaster::where('soc_id', $data['soc_id'])
            ->where('member_type_name', 'Primary')
            ->value('member_type_id');

        $objMemberMaster = ChsoneMembersMaster::where('soc_id', $data['soc_id'])
            ->where('fk_unit_id', $data['unit_id'])
            ->where('status', 1)
            ->where('member_type_id', $id)
            ->first();

        if ($objMemberMaster) {
            $arrMemberMaster = $objMemberMaster->toArray();
        }

        return $arrMemberMaster;
    }

    // Payment reversal process
    public function paymentReversalProcess($data = array())
    {
        $arrResponse['status'] = 'error';
        $invoiceNumber = '';
        if (!empty($data['arrPostData'])) {
            // Check if credit_account_detail exists before accessing its properties
            $creditAccountDetail = isset($data['arrPostData']['other_information']['credit_account_detail']) 
                                ? $data['arrPostData']['other_information']['credit_account_detail'] 
                                : null;

            // Check if the bill type is 'creditaccount-member' and conditions for maintenance reversal are met
            if (strtolower($data['arrPostData']['bill_type']) == 'creditaccount-member' && 
                (strtolower($creditAccountDetail['used_for'] ?? '') != 'maintenance' && 
                strtolower($creditAccountDetail['credit_used_type'] ?? '') != 'refundable')) {
                $arrResponse['message'] = 'Only maintenance reversal payment allowed';
                $arrResponse['status'] = 'error';
                return $arrResponse;
            }

            // Check if the member paid invoice is empty and process based on the available data
            if (empty(trim($data['arrPostData']['member_paid_invoice']))) {
                if (!empty($data['arrPostData']['billids'])) {
                    $arrResponse = $this->consumedAdvanceReversalProcess($data);
                    $invoiceNumber = $arrResponse['invoice_number'];
                } elseif ($creditAccountDetail && strtolower($creditAccountDetail['credit_used_type'] ?? '') == 'refundable') {
                    $arrResponse = $this->refundablePaymentReversalProcess($data);
                } else {
                    $arrResponse = $this->creditPaymentReversalProcess($data);
                }
            } elseif (!empty(trim($data['arrPostData']['member_paid_invoice']))) {
                $arrResponse = $this->invoicePaymentReversalProcess($data);
            }


            //Payment reversal ledger entry in case of advance
            if (!empty($arrResponse['status']) && strtolower(($arrResponse['status'])) == 'success') {
                $arrResponse = $this->paymentReversalLedgerEntry($data);
                if (!empty($arrResponse) && strtolower($arrResponse['status']) == 'success') {
                    $arrResponse = $this->updateReversedPaymentLedgerTransactionStatus(array('soc_id' => $data['soc_id'], 'voucher_type' => VOUCHER_RECEIPT, 'voucher_reference_number' => $data['arrPostData']['receipt_number'], 'voucher_reference_id' => $data['arrPostData']['payment_tracker_id'], 'is_cancelled' => 1));
                }
            }
            //Update payment tracker status to REVERSED
            if (!empty($arrResponse) && strtolower($arrResponse['status']) == 'success') {
                $arrUpdatePaymentTrackerListener = array('soc_id' => $data['soc_id'], 'updated_by' => $this->input['user_id'], 'payment_tracker_id' => $data['arrPostData']['id'], 'status' => 'reversed', 'reversal_note' => $data['arrPostData']['reversal_note']);
                if ($invoiceNumber) {
                    $arrUpdatePaymentTrackerListener['invoice_number'] = $invoiceNumber;
                }
                $arrResponse = $this->updateIncomePaymentTrackerStatus($arrUpdatePaymentTrackerListener);
            }
            if (!empty($arrResponse) && strtolower($arrResponse['status']) == 'success') {
                $arrResponse['message'] = 'Payment Reversed Successfully';
                $arrResponse['status'] = 'success';
            } else {
                $arrResponse['message'] = 'Failed to reverse payment.';
                $arrResponse['status'] = 'error';
            }
        }
        return $arrResponse;
    }

    // Consumed advance reversal process
    public function consumedAdvanceReversalProcess($data = array())
    {
        $arrResponse['status'] = 'error';
        $invoice_number = "";
        $arrInvoices = $data['arrPostData']['billids'];
        if (!is_array($data['arrPostData']['billids'])) {
            $data['arrPostData']['billids'] = [$data['arrPostData']['billids']];
        }
        if (!is_array($data['arrPostData']['amounts'])) {
            $data['arrPostData']['amounts'] = [$data['arrPostData']['amounts']];
        }
        $consumedAmount = 0;
        if (!empty($arrInvoices)) {
            foreach ($data['arrPostData']['billids'] as $k => $billId) {
                if (empty((float) round($data['arrPostData']['amounts'][$k]))) {
                    continue;
                }
                $invoice_number = $invoice_number . "," . $billId;
                $arrIncomeInvoiceDetail = $this->getUnitInvoiceDetail(array('soc_id' => $data['soc_id'], 'unit_id' => $data['arrPostData']['unit_id'], 'invoice_number' => $billId, 'getSingleInvoiceDetail' => true));

                $paymentStatus = 'unpaid';
                $status = 'generated';
                $invoiceAmount = $this->getMaintenanceReversalInvoiceAmount(array('soc_id' => $data['soc_id'], 'arrIncomeInvoiceDetail' => $arrIncomeInvoiceDetail));
                $previousPaidAmount = (float) round($arrIncomeInvoiceDetail['unit_invoice_detail'][0]['invoice_amount_detail']['partialpaidAmount'], 3);
                $prevAdvance  = (float) round($arrIncomeInvoiceDetail['unit_invoice_detail'][0]['advance_amount'] - $data['arrPostData']['payment_amount'], 3);
                if ($prevAdvance > 0 || $previousPaidAmount > 0) {
                    $status = $paymentStatus = 'partialpaid';
                    if ($previousPaidAmount + $prevAdvance >= (float) round($invoiceAmount, 3)) {
                        $status = $paymentStatus = 'paid';
                    }
                }
                if ($prevAdvance < 0) {
                    $prevAdvance = 0;
                }
                $eachInvoiceDetail['payment_amount'] = $data['arrPostData']['amounts'][$k];
                $consumedAmount = $consumedAmount + $data['arrPostData']['amounts'][$k];
                $eachInvoiceDetail['tds_deducted'] = 0;
                $arrResponse = $this->getParticularReversalStatus(array('arrReceiptDetail' => $eachInvoiceDetail, 'arrIncomeInvoiceDetail' => $arrIncomeInvoiceDetail, 'invoiceAmount' => $invoiceAmount, 'status' => $status, 'invoiceStatus' => $paymentStatus, 'advance_amount' => $prevAdvance));
                if (!empty($arrResponse) && strtolower($arrResponse['status']) == 'error') {
                    return $arrResponse;
                }
            }

            $receiptAmount = (float) round($data['arrPostData']['payment_amount'] - $consumedAmount, 3);
            if ($receiptAmount > 0) {
                $data['arrPostData']['payment_amount'] = $receiptAmount;
                $arrResponse = $this->creditPaymentReversalProcess($data);
            }
        }
        $arrResponse['invoice_number'] = trim($invoice_number, ",");
        return $arrResponse;
    }

    public function getUnitInvoiceDetail($data = array())
    {
        $arrAllUnitsInvoice = array();
        $arrAllUnitsInvoiceDetail = array();
        $todaysDate = date('Y-m-d');
        $current_date = date('Y-m-d H:i:s');

        // Query setup based on input data
        $query = IncomeUnitInvoice::query();

        if (!isset($data['unit_id'])) {
            $query->where('soc_id', $data['soc_id']);
        } else {
            $query->where('soc_id', $data['soc_id'])
                  ->where('fk_unit_id', $data['unit_id']);
        }
    
        if (isset($data['order_by'])) {
            $query->orderBy('unit_invoice_id', $data['order_by']);
        } else {
            $query->orderBy('unit_invoice_id', 'desc');
        }

        if (!isset($data['show_cancelled_invoice']) || empty(trim($data['show_cancelled_invoice']))) {
            $query->where('status', '!=', 'cancelled');
        }

        //Removed paid invoice from due amount
        if (isset($data['showOnlyPendingDues']) && $data['showOnlyPendingDues'] == 1) {
            $query->whereIn('payment_status', ['partialpaid', 'unpaid']);
        }

        if (isset($data['getSingleInvoiceDetail']) && !empty($data['invoice_number'])) {
            $query->where('invoice_number', $data['invoice_number']);
        } 

        // need to pass else condition for search data this will do valmik

        $objUnitsInvoice = $query->get();

        $any_paid_invoice = 0;
        $nonCancelledInvoice = 0;
        $outstanding_principal_interest = 0;
        $advance_amount = 0;

        if (!empty($objUnitsInvoice)) {
            $arrUnitsInvoice = $objUnitsInvoice->toArray();

            // Fetch last unit invoice
            $arrLastUnitInvoice = $this->getLastUnitInvoiceByUnit(array(
                'soc_id' => $data['soc_id'],
                'unit_id' => $data['unit_id']
            ));

            if (!empty($arrUnitsInvoice)) {
                foreach ($arrUnitsInvoice as $eachUnitInvoice) {
                    $singleUnitInvoice = array();
                    // Get total count of non cancelled invoices
                    if (strtolower($eachUnitInvoice['status']) != 'cancelled') {
                        $nonCancelledInvoice++;
                    }

                    $singleUnitInvoice['soc_id'] = $eachUnitInvoice['soc_id'];
                    $singleUnitInvoice['unit_id'] = $eachUnitInvoice['fk_unit_id'];
                    $singleUnitInvoice['unit_invoice_id'] = $eachUnitInvoice['unit_invoice_id'];
                    $singleUnitInvoice['soc_building_name'] = !empty($eachUnitInvoice['soc_building_name']) ? $eachUnitInvoice['soc_building_name'] : '';
                    $singleUnitInvoice['unit_flat_number'] = $data['unit_flat_number'] ?? (!empty($eachUnitInvoice['unit_name']) ? last(explode('-', $eachUnitInvoice['unit_name'])) : '');
                    $singleUnitInvoice['parking_number'] = '';

                    if (!empty($arrLastUnitInvoice) && isset($arrLastUnitInvoice['invoice_number'])) {
                        $singleUnitInvoice['unit_first_invoice'] = $eachUnitInvoice['unit_first_invoice'] = $arrLastUnitInvoice['invoice_number'];
                    }
                    $singleUnitInvoice['unit_invoice_number'] = $eachUnitInvoice['invoice_number'];
                    $singleUnitInvoice['payment_status'] = $eachUnitInvoice['payment_status'];
                    $singleUnitInvoice['status'] = $eachUnitInvoice['status'];

                    if (strtolower($eachUnitInvoice['payment_status']) == 'paid') {
                        $any_paid_invoice++;
                    } else {
                        $advance_amount += (float) $eachUnitInvoice['advance_amount'];
                    }
                    $singleUnitInvoice['bill_to'] = $eachUnitInvoice['bill_to'];
                    $singleUnitInvoice['member_gstin'] = $eachUnitInvoice['member_gstin'];

                    $singleUnitInvoice['principal_amount'] = $eachUnitInvoice['principal_amount'];
                    $singleUnitInvoice['interest_amount'] = $singleUnitInvoice['late_payment_interest'] = $eachUnitInvoice['interest_amount'];
                    $singleUnitInvoice['advance_amount'] = $eachUnitInvoice['advance_amount'];
                    $singleUnitInvoice['roundoff_amount'] = $eachUnitInvoice['roundoff_amount'];

                    $singleUnitInvoice['outstanding_principal'] = $eachUnitInvoice['outstanding_principal'];
                    $singleUnitInvoice['outstanding_interest'] = $eachUnitInvoice['outstanding_interest'];

                    $singleUnitInvoice['from_date'] = $eachUnitInvoice['from_date'];
                    $singleUnitInvoice['to_date'] = $eachUnitInvoice['to_date'];

                    $singleUnitInvoice['invoice_date'] = (!empty($eachUnitInvoice['created_date'])) ? current(explode(' ', $eachUnitInvoice['created_date'])) : '';
                    $singleUnitInvoice['due_date'] = (!empty($eachUnitInvoice['due_date'])) ? $eachUnitInvoice['due_date'] : '';
                    $singleUnitInvoice['due_date_status'] = (!empty($singleUnitInvoice['due_date']) && $todaysDate > $singleUnitInvoice['due_date']) ? 'red' : 'green';

                    // Fetch invoice particulars
                    $objIncomeInvoiceParticular = IncomeInvoiceParticular::where('soc_id', $singleUnitInvoice['soc_id'])
                    ->where('fk_unit_invoice_id', $singleUnitInvoice['unit_invoice_id'])
                    ->where('fk_unit_id', $singleUnitInvoice['unit_id'])
                    ->where('invoice_number', $singleUnitInvoice['unit_invoice_number'])
                    ->get();
                    
                    if (!empty($objIncomeInvoiceParticular)) {
                        $singleUnitInvoice['invoice_particulars'] = $objIncomeInvoiceParticular->toArray();
                        if (!empty($singleUnitInvoice['invoice_particulars'])) {
                            $i = 0;
                            foreach ($singleUnitInvoice['invoice_particulars'] as $eachParticular) {
                                $eachParticular['particular_name'] = $eachParticular['particular'];
                                if (strtolower($eachParticular['particular']) == 'noc') {
                                    $eachParticular['particular_name'] = 'NonOccupancyCharges';
                                } elseif (strtolower($eachParticular['particular']) == 'maintenancefee') {
                                    $eachParticular['particular_name'] = 'MaintenanceCharges';
                                }
                                $eachParticular['particular_name'] = ucwords(preg_replace('/([A-Z])/', ' $1', $eachParticular['particular_name']));
                                $singleUnitInvoice['invoice_particulars'][$i] = $eachParticular;
                                $i++;
                            }
                        }

                        // Calculate the invoice amount detail
                        $singleUnitInvoice['invoice_amount_detail'] = $this->_getTotalInvoiceAmount($singleUnitInvoice['invoice_particulars'], $eachUnitInvoice);
                    }

                    if (strtolower($eachUnitInvoice['unit_first_invoice']) == strtolower($eachUnitInvoice['invoice_number']) && strtolower($eachUnitInvoice['payment_status']) != 'paid') {
                        $outstanding_principal_interest = $singleUnitInvoice['outstanding_principal_interest'] = (float) $singleUnitInvoice['principal_amount'];
                    }

                    $arrLatePaymentCharges = $this->calculateLatePaymentCharges($singleUnitInvoice);
                    $singleUnitInvoice['apply_late_payment_charges'] = $arrLatePaymentCharges['apply_late_payment_charges'];
                    $singleUnitInvoice['late_payment_charges_detail'] = (!empty($arrLatePaymentCharges['late_payment_charges_detail'])) ? $arrLatePaymentCharges['late_payment_charges_detail'] : array();
                    $singleUnitInvoice['late_payment_charges'] = (!empty($arrLatePaymentCharges['total_late_payment_charges'])) ? $arrLatePaymentCharges['total_late_payment_charges'] : 0;
                    $singleUnitInvoice['total_late_payment_chargeable_amount'] = (!empty($arrLatePaymentCharges['total_late_payment_chargeable_amount'])) ? $arrLatePaymentCharges['total_late_payment_chargeable_amount'] : 0;
                    if (!empty($singleUnitInvoice['outstanding_principal_interest'])) {
                        $singleUnitInvoice['late_payment_charges'] += $singleUnitInvoice['outstanding_principal_interest'];
                    }

                    $singleUnitInvoice['total_invoice_due'] = (float) round(($singleUnitInvoice['invoice_amount_detail']['finalInvoiceAmount'] + $eachUnitInvoice['principal_amount'] + $eachUnitInvoice['interest_amount']) - ($singleUnitInvoice['invoice_amount_detail']['partialLatePaymentCharge'] + $singleUnitInvoice['invoice_amount_detail']['partialpaidAmount'] + $eachUnitInvoice['advance_amount']), 3);
                    $singleUnitInvoice['late_charge_tax_detail'] = (!empty($singleUnitInvoice['interest_amount']) && $singleUnitInvoice['interest_amount'] > 0) ? $this->getMemberInvoiceLateChargeTaxDetail(array(
                        'soc_id' => $eachUnitInvoice['soc_id'],
                        'invoice_number' => $eachUnitInvoice['invoice_number']
                    )) : null;
                    // $singleUnitInvoice['unit_all_invoice_number'] .= $singleUnitInvoice['unit_invoice_number'] . '|';
                    $singleUnitInvoice['principal_amount'] = (float) $singleUnitInvoice['principal_amount'];
                    $singleUnitInvoice['late_payment_interest'] = (float) $singleUnitInvoice['late_payment_interest'];
                    $singleUnitInvoice['interest_amount'] = (float) $singleUnitInvoice['interest_amount'];
                    $singleUnitInvoice['advance_amount'] = (float) $singleUnitInvoice['advance_amount'];
                    $singleUnitInvoice['roundoff_amount'] = (float) $singleUnitInvoice['roundoff_amount'];
                    $singleUnitInvoice['outstanding_principal'] = (float) $singleUnitInvoice['outstanding_principal'];
                    $singleUnitInvoice['outstanding_interest'] = (float) $singleUnitInvoice['outstanding_interest'];
                    array_push($arrAllUnitsInvoice, $singleUnitInvoice);
                }

                $arrAllUnitsInvoiceDetail['total_unpaid_invoice_amount'] = 0;
                $arrAllUnitsInvoiceDetail['total_paid_invoice'] = $any_paid_invoice;
                $arrAllUnitsInvoiceDetail['total_non_cancelled_invoice'] = $nonCancelledInvoice;
                $arrAllUnitsInvoiceDetail['unit_invoice_detail'] = $arrAllUnitsInvoice;

                $totalInterestAmount = 0;
                
                foreach ($arrAllUnitsInvoice as $eachUnitInvoiceDetail) {
                    if (in_array($eachUnitInvoiceDetail['payment_status'], array(
                        'unpaid',
                        'partialpaid'
                    ))) {
                        $tot = (float) $eachUnitInvoiceDetail['invoice_amount_detail']['finalInvoiceAmount'];
                        $totalInterestAmount += $eachUnitInvoiceDetail['interest_amount'];
                        $partialPaid = 0;
                        if (!empty($eachUnitInvoiceDetail['invoice_amount_detail']['partialLatePaymentCharge'])) {
                            $totalInterestAmount = round((float) $totalInterestAmount - (float) $eachUnitInvoiceDetail['invoice_amount_detail']['partialLatePaymentCharge'], 4);
                        }
                        if (!empty($eachUnitInvoiceDetail['invoice_amount_detail']['partialpaidAmount'])) {

                            $tot = bcsub((float) $eachUnitInvoiceDetail['invoice_amount_detail']['finalInvoiceAmount'], (float) $eachUnitInvoiceDetail['invoice_amount_detail']['partialpaidAmount'], 4);
                        }
                        $tot = (!empty($eachUnitInvoiceDetail['late_charge_tax_detail']['total_tax']) && $eachUnitInvoiceDetail['late_charge_tax_detail']['total_tax'] > 0) ? $tot + $eachUnitInvoiceDetail['late_charge_tax_detail']['total_tax'] : $tot;
                        $arrAllUnitsInvoiceDetail['total_unpaid_invoice_amount'] += (float) $tot;
                    }
                }

                $arrAllUnitsInvoiceDetail['total_unpaid_invoice_amount'] += (float) ($totalInterestAmount + $outstanding_principal_interest - $advance_amount);
            }
        }

        return ($arrAllUnitsInvoiceDetail);
    }

    public function getLastUnitInvoiceByUnit($data = array()) {

        $arrUnitsInvoice = array();
        // Build query with basic conditions
        $query = IncomeUnitInvoice::where('soc_id', $data['soc_id'])
        ->where('fk_unit_id', $data['unit_id'])
        ->where('status', '!=', 'cancelled');

        // dd($query->get());
        // Add ordering condition if provided
        // if (!empty($data['order_by'])) {
        //     $query->orderBy($data['order_by']);
        // }

        // Retrieve the first matching invoice
        $objUnitsInvoice = $query->first();
        if (!empty($objUnitsInvoice)) {
            $arrUnitsInvoice = $objUnitsInvoice->toArray();
        }
        return $arrUnitsInvoice;
    }

    private function _getTotalInvoiceAmount($data, $arrUnitInvoice = array())
    {
        $arrTotal = array('totalInvoiceAmount' => 0, 'totalTaxApplicable' => 0, 'totalTaxExemption' => 0, 'partialpaidAmount' => 0, 'finalInvoiceAmount' => $arrUnitInvoice['roundoff_amount'], 'partialLatePaymentCharge' => 0);

        if (!empty($data)) {
            foreach ($data as $eachInvoiceParticular) {
                $arrTotal['totalInvoiceAmount'] += $eachInvoiceParticular['amount'];
                $applied_tax = (!empty($eachInvoiceParticular['tax_applicable']) && !is_array($eachInvoiceParticular['tax_applicable'])) ? unserialize($eachInvoiceParticular['tax_applicable']) : $eachInvoiceParticular['tax_applicable'];
                $tax_exemption = (!empty($eachInvoiceParticular['tax_exemptions']) && !is_array($eachInvoiceParticular['tax_exemptions'])) ? unserialize($eachInvoiceParticular['tax_exemptions']) : $eachInvoiceParticular['tax_exemptions'];
                $applied_tax_total = (is_array($applied_tax)) ? $applied_tax['total'] : $applied_tax;
                $tax_exemption_total = (is_array($tax_exemption)) ? $tax_exemption['total'] : $tax_exemption;
                $arrTotal['totalTaxApplicable'] += (float) $applied_tax_total;
                $arrTotal['totalTaxExemption'] += (float) $tax_exemption_total;
                $arrTotal['finalInvoiceAmount'] += (float) $eachInvoiceParticular['amount'] + ($applied_tax_total + $tax_exemption_total);
            }
        }
        // get partial pad amount
        if (!empty($arrUnitInvoice['payment_status']) && in_array($arrUnitInvoice['payment_status'], array('partialpaid','paid'))) {
            $objIncomeInvoicePayment = IncomeInvoicePayment::where('soc_id', $arrUnitInvoice['soc_id'])
            ->where('fk_unit_id', $arrUnitInvoice['fk_unit_id'])
            ->where('fk_unit_invoice_id', $arrUnitInvoice['unit_invoice_id'])
            ->where('invoice_number', $arrUnitInvoice['invoice_number'])
            ->get();

            if (!empty($objIncomeInvoicePayment)) {

                $arrIncomeInvoicePayment = $objIncomeInvoicePayment->toArray();
                foreach ($arrIncomeInvoicePayment as $eachIncomeInvoicePayment) {
                    $arrTotal['partialpaidAmount'] += (float) ($eachIncomeInvoicePayment['payment_amount'] + $eachIncomeInvoicePayment['tds_deducted'] + $eachIncomeInvoicePayment['writeoff_amount']);
                    $arrTotal['partialLatePaymentCharge'] += (float) $eachIncomeInvoicePayment['late_payment_charges'];
                }
            }
        }

        return $arrTotal;
    }

    public function getMemberInvoiceLateChargeTaxDetail($data = array())
    {
        $arrTaxClass = array(
            'tax_detail' => null,
            'total_tax' => 0
        );

        $objTaxClass = ChsoneTaxLog::where('soc_id', $data['soc_id'])
        ->whereIn('invoice_number', [$data['invoice_number']])
        ->whereNull('particular_id')
        ->where('particular', 'LateCharge')
        ->get();

        if (!empty($objTaxClass)) {
            $arrTaxClass['tax_detail'] = $objTaxClass->toArray();
            foreach ($arrTaxClass['tax_detail'] as $eachTaxLog) {
                $arrTaxClass['total_tax'] += $eachTaxLog['tax_amount'];
            }
        }
        return $arrTaxClass;
    }

    public function calculateLatePaymentCharges($data = array())
    {
        $latePaymentCharge = 0;
        $todaysDate = date('Y-m-d');
        $arrLatePaymentCharges = array();
        $arrAllLatePaymentCharges = array();
        $arrLatePaymentCharges['apply_late_payment_charges'] = 0;
        $total_late_payment_chargeable_amount = 0;

        if (!empty($data['due_date'])) {
            $dueDate = $data['due_date'];
            $lateChargeCalculationDate = new \DateTime($data['from_date']);
            $lateChargeCalculationDate = $lateChargeCalculationDate->modify("-1 day")->format('Y-m-d'); // For today/now, don't pass an arg.

            if ($todaysDate > $dueDate) {
                // set late payment charges key
                if (strtolower($data['payment_status']) != 'paid') {
                    $arrLatePaymentCharges['apply_late_payment_charges'] = 1;
                }
                if (!empty($data['invoice_particulars'])) {
                    
                    $arrData = $this->_getRulewiseTotalAmount($data, $data['payment_status']);

                    // echo '################# Late payment Interest ##############################';
                    
                    // Get general setting
                    $arrGeneralInvoiceSetting = $this->getInvoiceSetting(array('soc_id' => $data['soc_id']));

                    $arrLatePaymentdetails = $this->getLatePaymentCharges(array('soc_id' => $data['soc_id']));

                    foreach ($arrData as $rule => $amount) {

                        $objincomeinvoicerule = IncomeInvoiceRule::where('id', $rule)
                        ->where('soc_id', $data['soc_id'])
                        ->first();

                        if (!empty($objincomeinvoicerule)) {
                            $arrRuleWiseCharges = array();

                            $arrIncomeinvoicerule = $objincomeinvoicerule->toArray();

                            if (isset($arrIncomeinvoicerule['apply_late_payment_interest']) && $arrIncomeinvoicerule['apply_late_payment_interest'] == 1) {

                                if (!empty($arrLatePaymentdetails)) {
                                    $arrRuleWiseCharges['rule_id'] = $rule;
                                    $singleRuleCharges = $this->calculateMonthlyLateCharges(array(
                                        'soc_id' => $data['soc_id'],
                                        'dueDate' => $dueDate,
                                        'amount' => $amount,
                                        'arrLatePaymentdetails' => $arrLatePaymentdetails,
                                        'arrGeneralInvoiceSetting' => $arrGeneralInvoiceSetting,
                                        'lateChargeCalculationDate' => $lateChargeCalculationDate
                                    ));

                                    $arrRuleWiseCharges['late_payment_charges'] = $singleRuleCharges;
                                    $total_late_payment_chargeable_amount += $amount;
                                    $latePaymentCharge += $singleRuleCharges;
                                    array_push($arrAllLatePaymentCharges, $arrRuleWiseCharges);
                                }
                            }
                        }
                    }
                }
            }
        }
        $arrLatePaymentCharges['late_payment_charges_detail'] = $arrAllLatePaymentCharges;
        $arrLatePaymentCharges['total_late_payment_charges'] = $latePaymentCharge;
        $arrLatePaymentCharges['total_late_payment_chargeable_amount'] = $total_late_payment_chargeable_amount;
        return $arrLatePaymentCharges;
    }

    private function _getRulewiseTotalAmount($data = array(), $paymentStatus)
    {
        $arrRule = array();

        if (!empty($data)) {
            foreach ($data['invoice_particulars'] as $eachParticular) {

                $finalParticularAmount = 0;

                // Exclude noc from total amount
                $finalParticularAmount = (float) $eachParticular['amount'];

                // Add applicable tax amount
                if (!empty($eachParticular['tax_applicable'])) {
                    $arrTaxApplicable = unserialize($eachParticular['tax_applicable']);
                    $finalParticularAmount += (float) $arrTaxApplicable['total'];
                }

                // Subtract applicable tax exemption amount
                if (!empty($eachParticular['tax_exemptions'])) {
                    $arrTax_exemptions = unserialize($eachParticular['tax_exemptions']);
                    $finalParticularAmount += (float) $arrTax_exemptions['total'];
                }

                if (array_key_exists($eachParticular['fk_rule_id'], $arrRule)) {
                    if (strtolower($eachParticular['is_particular_paid']) == 'y') {
                        $arrRule[$eachParticular['fk_rule_id']] += 0;
                    } elseif (strtolower($eachParticular['is_particular_paid']) == 'p') {
                        $arrRule[$eachParticular['fk_rule_id']] += (!empty($eachParticular['particular_paid_amount'])) ? round($finalParticularAmount - $eachParticular['particular_paid_amount'], 4) : $finalParticularAmount;
                    } else {
                        $arrRule[$eachParticular['fk_rule_id']] += $finalParticularAmount;
                    }
                } else {
                    if (strtolower($eachParticular['is_particular_paid']) == 'y') {
                        $arrRule[$eachParticular['fk_rule_id']] = 0;
                    } elseif (strtolower($eachParticular['is_particular_paid']) == 'p') {
                        $arrRule[$eachParticular['fk_rule_id']] = (isset($eachParticular['particular_paid_amount'])) ? $finalParticularAmount - $eachParticular['particular_paid_amount'] : $finalParticularAmount;
                    } else {
                        $arrRule[$eachParticular['fk_rule_id']] = $finalParticularAmount;
                    }
                }
                if ($paymentStatus == 'paidafterduedate') {
                    $arrRule[$eachParticular['fk_rule_id']] += $finalParticularAmount;
                }
            }
        }
        return $arrRule;
    }

    public function getInvoiceSetting($data = array())
    {
        $arrInvoiceSetting = array();

        $objInvoiceSetting = IncomeInvoiceSetting::where('soc_id', $data['soc_id'])->first();

        if (!empty($objInvoiceSetting)) {
            $arrInvoiceSetting = $objInvoiceSetting->toArray();
        }
        return $arrInvoiceSetting;
    }

    public function getLatePaymentCharges($data = array())
    {
        $effective_date = date('Y-m-d');

        // Check if bill_date is present and format it correctly
        if (!empty($data['bill_date'])) {
            $effective_date = (strrchr($data['bill_date'], '/')) ? $data['bill_date'] : $data['bill_date'];
        }

        // Determine the type condition based on the presence of 'type'
        if (empty($data['type']) || $data['type'] == '') {
            $type = "type = 'maintenance'";
        } else {
            $type = "type = '" . $data['type'] . "'";
        }

        // Query using Eloquent with conditions and order
        $objLatePaymentdetails = IncomeLatePaymentCharge::whereRaw($type)
        ->where('soc_id', $data['soc_id'])
        ->where('effective_date', '<=', $effective_date)
        ->orderBy('effective_date', 'desc')
        ->first();
        
        // Convert result to array if exists, otherwise return empty array
        $arrLatePaymentdetails = (!empty($objLatePaymentdetails)) ? $objLatePaymentdetails->toArray() : array();
        return $arrLatePaymentdetails;
    }

    public function calculateMonthlyLateCharges($data = array())
    {
        if ($data['arrLatePaymentdetails']['calculate_for'] == 'perday') {
            // return $this->calculateDailyLateCharges($data);
        }
        $arrLatePaymentdetails = $data['arrLatePaymentdetails'];
        $arrInvoiceSetting = $data['arrGeneralInvoiceSetting'];
        $amount = $data['amount'];
        $singleRuleCharges = 0;

        if (!empty($arrLatePaymentdetails)) {
            $numberOfMonth = 1;
            if (!empty($arrInvoiceSetting)) {
                switch (strtolower($arrInvoiceSetting['invoicing_frequency'])) {
                    case 'quarterly':
                        $numberOfMonth = 3;
                        break;
                    case 'half_yearly':
                        $numberOfMonth = 6;
                        break;
                    case 'yearly':
                        $numberOfMonth = 12;
                        break;
                    case 'monthly':
                    default:
                        $numberOfMonth = 1;
                        break;
                }
            }

            if (!empty($arrLatePaymentdetails['calculate_from']) && strtolower($arrLatePaymentdetails['calculate_from']) == 'duedate' && !empty($data['dueDate'])) {
                
                $end_date = $data['lateChargeCalculationDate'];
                if (isset($data['paidDate']) && !empty($data['paidDate'])) {
                    $end_date = $data['paidDate'];
                }
                $numberOfMonth = $this->getNumberOfMonth(array(
                    'start_date' => $data['dueDate'],
                    'end_date' => $end_date
                ));
            }

            if ($arrLatePaymentdetails['interest_amount_type'] == 'fixed') {
                $singleRuleCharges = (float) round($arrLatePaymentdetails['simple_interest'] * $numberOfMonth, 4);
            } else {

                $t = (float) round(($numberOfMonth / 12), 4);
                $p = (float) $amount;
                $r = (float) ($arrLatePaymentdetails['simple_interest'] / 100);

                if (isset($arrLatePaymentdetails['interest_type']) && !empty($arrLatePaymentdetails['interest_type']) && in_array(strtolower($arrLatePaymentdetails['interest_type']), array(
                    'monthly',
                    'quarterly',
                    'half_yearly',
                    'yearly'
                ))) {
                    $n = 12;
                    switch (strtolower($arrLatePaymentdetails['interest_type'])) {
                        case 'quarterly':
                            $n = 4;
                            break;
                        case 'half_yearly':
                            $n = 2;
                            break;
                        case 'yearly':
                            $n = 1;
                            break;
                        case 'monthly':
                        default:
                            $n = 12;
                            break;
                    }
                    $A = (float) round(($p * pow(1 + ($r / $n), $n * $t)), 4); // compound interest formulae
                    $singleRuleCharges = (float) round($A - $p, 4);
                } else {
                    $singleRuleCharges = (float) round(($p * $r * $t), 4); // simple interest formulae
                }
            }
        }

        return $singleRuleCharges;
    }

    public function getNumberOfMonth($data = array())
    {
        $begin = new \DateTime($data['start_date']);
        $end = new \DateTime($data['end_date']);

        $interval = \DateInterval::createFromDateString('1 month');

        $period = new \DatePeriod($begin, $interval, $end);
        $counter = 0;
        foreach ($period as $dt) {
            $counter++;
        }
        if ($counter == 0) {
            $counter = 1;
        }
        return $counter;
    }

    public function getMaintenanceReversalInvoiceAmount($data = array())
    {
        $invoiceAmount = 0;
        if (!empty($data['arrIncomeInvoiceDetail'])) {
            $arrIncomeInvoiceDetail = $data['arrIncomeInvoiceDetail'];
            //Remove interest amount from total invoice amount
            $totalInterestAmount = (
                    $arrIncomeInvoiceDetail['unit_invoice_detail'][0]['interest_amount'] ?? 0
                ) + (
                    $arrIncomeInvoiceDetail['unit_invoice_detail'][0]['late_charge_tax_detail']['total_tax'] ?? 0
                );
                 if ($arrIncomeInvoiceDetail['unit_invoice_detail'][0]['advance_amount'] > 0 && $totalInterestAmount > 0) {
                $arrIncomeInvoiceDetail['unit_invoice_detail'][0]['advance_amount'] = ($totalInterestAmount < $arrIncomeInvoiceDetail['unit_invoice_detail'][0]['advance_amount']) ? (float) round($arrIncomeInvoiceDetail['unit_invoice_detail'][0]['advance_amount'] - $totalInterestAmount) : 0;
            }
            $invoiceAmount = $arrIncomeInvoiceDetail['unit_invoice_detail'][0]['invoice_amount_detail']['finalInvoiceAmount'];
            if (strtolower($arrIncomeInvoiceDetail['unit_invoice_detail'][0]['unit_first_invoice']) == strtolower($arrIncomeInvoiceDetail['unit_invoice_detail'][0]['unit_invoice_number'])) {
                $invoiceAmount += $arrIncomeInvoiceDetail['unit_invoice_detail'][0]['principal_amount'];
            }
            //Add previous outstanding
            $invoiceAmount = (strtolower($arrIncomeInvoiceDetail['unit_invoice_detail'][0]['unit_first_invoice']) == strtolower($arrIncomeInvoiceDetail['unit_invoice_detail'][0]['unit_invoice_number'])) ? $invoiceAmount + $arrIncomeInvoiceDetail['unit_invoice_detail'][0]['principal_amount'] : $invoiceAmount;
            //Remove advance
            if ($arrIncomeInvoiceDetail['unit_invoice_detail'][0]['advance_amount'] > 0) {
                $invoiceAmount = ($invoiceAmount < $arrIncomeInvoiceDetail['unit_invoice_detail'][0]['advance_amount']) ? (float) round($arrIncomeInvoiceDetail['unit_invoice_detail'][0]['advance_amount'] - $invoiceAmount, 3) : (float) round($invoiceAmount - $arrIncomeInvoiceDetail['unit_invoice_detail'][0]['advance_amount'], 3);
            }
            $invoiceAmount = round($invoiceAmount, 3);
        }
        return $invoiceAmount;
    }

    public function getParticularReversalStatus($data = array())
    {
        $arrResponse['status'] = 'error';
        if (!empty($data['arrIncomeInvoiceDetail']['unit_invoice_detail'][0])) {
            if ($data['arrIncomeInvoiceDetail']['unit_invoice_detail'][0]['invoice_particulars'] && !(!empty($data['arrIncomeInvoiceDetail']['unit_invoice_detail'][0]['advance_amount']) && empty($data['arrReceiptDetail']['payment_amount']))) {
                $data['arrReceiptDetail']['payment_amount'] += $data['arrReceiptDetail']['tds_deducted'];
                if ($data['arrReceiptDetail']['payment_amount'] <= $data['invoiceAmount'] && strtolower($data['invoiceStatus'] == 'partialpaid')) {
                    $arrRuleIds = $arrIncidentRuleIds = array();
                    foreach ($data['arrIncomeInvoiceDetail']['unit_invoice_detail'][0]['invoice_particulars'] as $eachInvoiceParticular) {
                        if (strtolower($eachInvoiceParticular['bill_type']) == 'maintenance') {
                            $arrRuleIds[] = $eachInvoiceParticular['fk_rule_id'];
                        } elseif (strtolower($eachInvoiceParticular['bill_type']) == 'incident') {
                            $arrIncidentRuleIds[] = $eachInvoiceParticular['fk_rule_id'];
                        }
                    }
                    $arrParticularList = $this->getParticularOrderList(array('soc_id' => array('soc_id' => $data['arrIncomeInvoiceDetail']['unit_invoice_detail'][0]['soc_id']), 'ruleIds' => $arrRuleIds, 'incidentRuleIds' => $arrIncidentRuleIds, 'account_type' => 'member', 'invoice_number' => $data['arrIncomeInvoiceDetail']['unit_invoice_detail'][0]['unit_invoice_number']));
                    $arrParticularList = array_reverse($arrParticularList);
                    if (!empty($arrParticularList)) {
                        $arrReversedInvoiceParticularList = array();
                        foreach ($arrParticularList as $key => $eachParticular) {
                            $arrReversedInvoiceParticular = $eachParticular;
                            if ($data['arrReceiptDetail']['payment_amount'] <= 0) {
                                break;
                            }
                            $particularAmount = (float) $eachParticular['amount'];
                            // Add applicable tax amount
                            if (!empty($eachParticular['tax_applicable'])) {
                                $arrTaxApplicable = unserialize($eachParticular['tax_applicable']);
                                $particularAmount += (float) $arrTaxApplicable['total'];
                            }
                            // Add applicable tax exemption amount
                            if (!empty($eachParticular['tax_exemptions'])) {
                                $arrTax_exemptions = unserialize($eachParticular['tax_exemptions']);
                                $particularAmount += (float) $arrTax_exemptions['total'];
                            }
                            if (strtolower($eachParticular['is_particular_paid']) == 'p') {
                                if ($data['arrReceiptDetail']['payment_amount'] < $eachParticular['particular_paid_amount']) {
                                    $arrReversedInvoiceParticular['is_particular_paid'] = 'P';
                                    $arrReversedInvoiceParticular['particular_paid_amount'] = $data['arrReceiptDetail']['payment_amount'];
                                    $data['arrReceiptDetail']['payment_amount'] = 0;
                                } elseif ($data['arrReceiptDetail']['payment_amount'] == $eachParticular['particular_paid_amount']) {
                                    $arrReversedInvoiceParticular['is_particular_paid'] = 'N';
                                    $arrReversedInvoiceParticular['particular_paid_amount'] = 0;
                                    $data['arrReceiptDetail']['payment_amount'] = 0;
                                } elseif ($data['arrReceiptDetail']['payment_amount'] > $eachParticular['particular_paid_amount']) {
                                    $arrReversedInvoiceParticular['is_particular_paid'] = 'N';
                                    $data['arrReceiptDetail']['payment_amount'] = (float) round($data['arrReceiptDetail']['payment_amount'] - $eachParticular['particular_paid_amount'], 3);
                                    $arrReversedInvoiceParticular['particular_paid_amount'] = 0;
                                }
                            } elseif (strtolower($eachParticular['is_particular_paid']) == 'y') {
                                if ($data['arrReceiptDetail']['payment_amount'] < $particularAmount) {
                                    $arrReversedInvoiceParticular['is_particular_paid'] = 'P';
                                    $arrReversedInvoiceParticular['particular_paid_amount'] = (float) round($particularAmount - $data['arrReceiptDetail']['payment_amount'], 3);
                                    $data['arrReceiptDetail']['payment_amount'] = 0;
                                } elseif ($data['arrReceiptDetail']['payment_amount'] == $particularAmount) {
                                    $arrReversedInvoiceParticular['is_particular_paid'] = 'N';
                                    $arrReversedInvoiceParticular['particular_paid_amount'] = 0;
                                    $data['arrReceiptDetail']['payment_amount'] = 0;
                                } elseif ($data['arrReceiptDetail']['payment_amount'] > $particularAmount) {
                                    $arrReversedInvoiceParticular['is_particular_paid'] = 'N';
                                    $data['arrReceiptDetail']['payment_amount'] = (float) round($data['arrReceiptDetail']['payment_amount'] - $particularAmount, 3);
                                    $arrReversedInvoiceParticular['particular_paid_amount'] = 0;
                                }
                            }
                            //Update unit invoice particular
                            $arrResponse = $this->updateUnitIncomeInvoiceParticular(array('soc_id' => $data['arrIncomeInvoiceDetail']['unit_invoice_detail'][0]['soc_id'], 'is_particular_paid' => $arrReversedInvoiceParticular['is_particular_paid'], 'particular_paid_amount' => $arrReversedInvoiceParticular['particular_paid_amount'], 'particularId' => $arrReversedInvoiceParticular['particularId'], 'invoice_number' => $data['arrIncomeInvoiceDetail']['unit_invoice_detail'][0]['unit_invoice_number']));
                            if (!empty($arrResponse) && strtolower($arrResponse['status']) == 'error') {
                                return $arrResponse;
                            }
                        }
                    }
                } else {
                    //Update unit invoice particular
                    $arrResponse = $this->updateUnitIncomeInvoiceParticular(array('soc_id' => $data['arrIncomeInvoiceDetail']['unit_invoice_detail'][0]['soc_id'], 'is_particular_paid' => 'N', 'particular_paid_amount' => '0', 'invoice_number' => $data['arrIncomeInvoiceDetail']['unit_invoice_detail'][0]['unit_invoice_number']));
                }
            }
            //Update unit invoice status
            if (!empty($arrResponse) && strtolower($arrResponse['status']) == 'success') {
                $arrResponse = $this->updateIncomeUnitInvoice(array('soc_id' => $data['arrIncomeInvoiceDetail']['unit_invoice_detail'][0]['soc_id'], 'status' => $data['status'], 'payment_status' => $data['invoiceStatus'], 'invoice_number' => $data['arrIncomeInvoiceDetail']['unit_invoice_detail'][0]['unit_invoice_number'], 'advance_amount' => $data['arrIncomeInvoiceDetail']['unit_invoice_detail'][0]['advance_amount']));
            }
        }
        return $arrResponse;
    }

    public function getParticularOrderList($data = array())
    {
        $arrIncomeDetail = $arrIncidentIncomeDetail = array();
        if (!empty($data['ruleIds'])) {

            $arrIncomeDetail = $this->getIncomeAccountInvoiceParticular($data);
        }
        if (!empty($data['incidentRuleIds'])) {
            $arrIncidentIncomeDetail = $this->getIncidentAccountInvoiceParticular($data);
        }
        $arrIncomeDetail = $this->getParticularsPaymentAccountOrder(array(
            'arrMaintenanceAccountDetail' => $arrIncomeDetail,
            'arrIncidentAccountDetail' => $arrIncidentIncomeDetail
        ));
        return $arrIncomeDetail;
    }

    public function getIncomeAccountInvoiceParticular($data = array())
    {
        $arrIncomeDetailData = array();
        if (is_array($data['ruleIds']) && !empty($data['ruleIds'])) {
            $data['ruleIds'] = implode(',', array_unique($data['ruleIds']));
        }
        $query = $this->tenantDB()->table('income_invoice_rules as incomerule')
        ->select([
            'invoiceparticular.id as particularId',
            'invoiceparticular.particular',
            'incomerule.id',
            'incomeaccount.account_name',
            'incomeaccount.fk_income_ledger_id',
            'incomeaccount.fk_ledger_id',
            'incomeaccount.fk_cash_ledger_id',
            'unitinvoice.advance_amount',
            'invoiceparticular.amount',
            'invoiceparticular.tax_applicable',
            'invoiceparticular.tax_exemptions',
            'incomerule.apply_late_payment_interest',
            'invoiceparticular.is_particular_paid',
            'invoiceparticular.particular_paid_amount'
        ])
        ->join('income_accounts as incomeaccount', 'incomerule.income_account_id', '=', 'incomeaccount.account_id')
        ->join('income_invoice_particular as invoiceparticular', 'incomerule.id', '=', 'invoiceparticular.fk_rule_id')
        ->leftJoin('income_unit_invoices as unitinvoice', 'invoiceparticular.invoice_number', '=', 'unitinvoice.invoice_number')
        ->where('incomerule.soc_id', $data['soc_id'])
        ->whereIn('incomerule.id', explode(',', $data['ruleIds']))
        ->where('incomeaccount.account_type', $data['account_type'])
        ->where('invoiceparticular.invoice_number', $data['invoice_number'])
        ->orderBy('incomerule.apply_late_payment_interest', 'DESC')
        ->orderBy('invoiceparticular.id', 'ASC')
        ->get();

        // Convert resultset to array
        if (!empty($query)) {
            $arrIncomeDetailData = $query->toArray();
            $arrIncomeDetailData = json_decode(json_encode($arrIncomeDetailData), true);
        }
        return $arrIncomeDetailData;
    }

    public function getIncidentAccountInvoiceParticular($data = array())
    {
        $arrIncomeDetailData = array();
        if (is_array($data['incidentRuleIds']) && !empty($data['incidentRuleIds'])) {
            $data['incidentRuleIds'] = implode(',', array_unique($data['incidentRuleIds']));
        }
        $query = $this->tenantDB()->table('income_common_area_charges as incomerule')
        ->select([
            'invoiceparticular.id as particularId',
            'invoiceparticular.particular',
            'incomerule.id',
            'invoiceparticular.particular as account_name',
            'incomerule.ledger_id as fk_income_ledger_id',
            'unitinvoice.advance_amount',
            'invoiceparticular.amount',
            'invoiceparticular.tax_applicable',
            'invoiceparticular.tax_exemptions',
            'incomerule.apply_late_payment_interest',
            'invoiceparticular.is_particular_paid',
            'invoiceparticular.particular_paid_amount'
        ])
        ->join('income_invoice_particular as invoiceparticular', 'incomerule.id', '=', 'invoiceparticular.fk_rule_id')
        ->leftJoin('income_unit_invoices as unitinvoice', 'invoiceparticular.invoice_number', '=', 'unitinvoice.invoice_number')
        ->where('incomerule.soc_id', $data['soc_id'])
        ->whereIn('incomerule.id', explode(',', $data['incidentRuleIds']))
        ->where('invoiceparticular.invoice_number', $data['invoice_number'])
        ->orderBy('incomerule.apply_late_payment_interest', 'DESC')
        ->orderBy('invoiceparticular.id', 'ASC')
        ->get();

        // Convert resultset to array
        if (!empty($query)) {
            $arrIncomeDetailData = $query->toArray();
            $arrIncomeDetailData = json_decode(json_encode($arrIncomeDetailData), true);
        }

        return $arrIncomeDetailData;
    }

    public function getParticularsPaymentAccountOrder($data = array())
    {
        $arrIncomeDetailData = array_merge($data['arrMaintenanceAccountDetail'], $data['arrIncidentAccountDetail']);
        uasort($arrIncomeDetailData, function ($item1, $item2) {
            if ($item1['apply_late_payment_interest'] == $item2['apply_late_payment_interest']) {
                return 0;
            }
            return $item1['apply_late_payment_interest'] > $item2['apply_late_payment_interest'] ? -1 : 1;
        });
        return $arrIncomeDetailData;
    }

    public function updateUnitIncomeInvoiceParticular($data = array())
    {
        $arrResponse['status'] = 'error';
        $data['updated_date'] = date('Y-m-d H:i:s');
        $updateResponse = $this->updateIncomeInvoiceParticularByField($data);
        if (!empty($updateResponse) && strtolower($updateResponse['status']) == 'success') {
            $arrResponse['status'] = 'success';
        }
        return $arrResponse;
    }

    public function updateIncomeInvoiceParticularByField($data,$full_list = false)
    {
        $arrResponse = ['status' => 'success', 'message' => []];

        $updateData = ['updated_date' => $data['updated_date']];

        if (isset($data['is_particular_paid']) && !empty($data['is_particular_paid'])) {
        $updateData['is_particular_paid'] = $data['is_particular_paid'];
        }

        if (isset($data['particular_paid_amount'])) {
            $updateData['particular_paid_amount'] = $data['particular_paid_amount'];
        }

        $query = $this->tenantDB()->table('income_invoice_particular')
        ->where('soc_id', $data['soc_id'])
        ->where('invoice_number', $data['invoice_number']);

        if (isset($data['particularId']) && !empty($data['particularId'])) {
            $query->where('id', $data['particularId']);
        }

        $result = $query->update($updateData);

        if (!$result) {
            $arrResponse = ['status' => 'error', 'message' => 'Update failed'];
        }
        return $arrResponse;
    }

    public function updateIncomeUnitInvoice($data = array())
    {
        $arrResponse['status'] = 'error';
        $data['updated_date'] = date('Y-m-d H:i:s');
        $updateResponse = $this->updateIncomeInvoice($data);
        if (!empty($updateResponse) && strtolower($updateResponse['status']) == 'success') {
            $arrResponse['status'] = 'success';
        }
        return $arrResponse;
    }

    public function updateIncomeInvoice($data,$full_list = false)
    {
        $arrResponse = ['status' => 'success', 'message' => []];

        // Updating a single column
        $updateData = ['updated_date' => $data['updated_date']];

        if (isset($data['status']) && !empty($data['status'])) {
            $updateData['status'] = $data['status'];
        }
    
        if (isset($data['payment_status']) && !empty($data['payment_status'])) {
            $updateData['payment_status'] = $data['payment_status'];
        }
    
        if (isset($data['advance_amount'])) {
            $updateData['advance_amount'] = $data['advance_amount'];
        }
    
        if (isset($data['roundoff_amount']) && !empty($data['roundoff_amount'])) {
            $updateData['roundoff_amount'] = $data['roundoff_amount'];
        }
        
        $query = $this->tenantDB()->table('income_unit_invoices')
        ->where('soc_id', $data['soc_id'])
        ->where('invoice_number', $data['invoice_number']);

        // Execute the update query
        $result = $query->update($updateData);

        if (!$result) {
            $arrResponse = ['status' => 'error', 'message' => ['Update failed']];
        }

        return $arrResponse;
    }

    public function creditPaymentReversalProcess($data = array())
    {
        $arrResponse['status'] = 'error';
        if (!empty($data['arrPostData'])) {
            if (!empty($data['arrPostData']['bill_type']) && in_array(strtolower($data['arrPostData']['bill_type']), array('member', 'creditaccount-member'))) {
                $arrCreditData['soc_id'] = $data['arrPostData']['soc_id'];
                $arrCreditData['account_id'] = $data['arrPostData']['unit_id'];
                $arrCreditData['account_context'] = 'unit';
                $arrCreditData['bill_date'] = date('Y-m-d');
                $arrCreditData['bill_type'] = 'maintenance';
                $creditRemainingAmount = $this->getCreditAmountForIndividuals(array('data' => $arrCreditData));
                //Credit available
                if (!empty($creditRemainingAmount['remaining_amount']) && $creditRemainingAmount['remaining_amount'] > 0) {
                    //Full Credit available 
                    if ($creditRemainingAmount['remaining_amount'] >= $data['arrPostData']['payment_amount']) {
                        $arrCreditData['transaction_type'] = 'dr';
                        $arrCreditData['context'] = 'system';
                        $arrCreditData['used_for'] = 'maintenance';
                        $arrCreditData['payment_date'] = date('Y-m-d');
                        $arrCreditData['narration'] = 'Amount Rs ' . $data['arrPostData']['payment_amount'] . ' has reversed from Receipt no ' . $data['arrPostData']['receipt_number'];
                        $arrCreditData['payment_amount'] = $data['arrPostData']['payment_amount'];
                        $arrCreditData['payment_tracker_id'] = $data['arrPostData']['payment_tracker_id'];
                        $arrCreditData['invoice_number'] = '';
                        $arrCreditData['account_name'] = $data['arrMemberDetail']['member_first_name'] . ' ' . $data['arrMemberDetail']['member_last_name'];

                        $creditAccountresponse = $this->saveCreditAccountResponse(array(
                            'auth' => true, 
                            'process' => 'fetch', 
                            'soc_id' => $data['soc_id'], 
                            'id' => '',
                            'data' => $arrCreditData, 
                            'user' => $this->input['user_id'], 
                            'username' => $data['user_name']
                        ));
                        if ($creditAccountresponse['error']) {
                            $arrResponse['status'] = 'error';
                            $arrResponse['message'] = 'Unable to save debit entry for ' . $data['arrPostData']['receipt_number'];
                            return $arrResponse;
                        }
                        $arrResponse['status'] = 'success';
                    }  //Partial Credit available 
                    elseif ($creditRemainingAmount['remaining_amount'] < $data['arrPostData']['payment_amount']) {
                        //Adjust partial credit
                        $data['billableItemAmount'] = (float) round($data['arrPostData']['payment_amount'] - $creditRemainingAmount['remaining_amount'], 3);
                        $arrCreditData['transaction_type'] = 'dr';
                        $arrCreditData['context'] = 'system';
                        $arrCreditData['used_for'] = 'maintenance';
                        $arrCreditData['payment_date'] = date('Y-m-d');
                        $arrCreditData['narration'] = 'Amount Rs ' . $creditRemainingAmount['remaining_amount'] . ' has reversed from Receipt no ' . $data['arrPostData']['receipt_number'];
                        $arrCreditData['payment_amount'] = $creditRemainingAmount['remaining_amount'];
                        $arrCreditData['invoice_number'] = '';
                        $arrCreditData['payment_tracker_id'] = $data['arrPostData']['payment_tracker_id'];
                        $arrCreditData['account_name'] = $data['arrMemberDetail']['member_first_name'] . ' ' . $data['arrMemberDetail']['member_last_name'];
                        $creditAccountresponse = $this->saveCreditAccountResponse(array(
                            'auth' => true, 
                            'process' => 'fetch', 
                            'soc_id' => $data['soc_id'], 
                            'id' => '',
                            'data' => $arrCreditData, 
                            'user' => $this->input['user_id'], 
                            'username' => $data['user_name']
                        ));
                        if ($creditAccountresponse['error']) {
                            $arrResponse['status'] = 'error';
                            $arrResponse['message'] = 'Unable to save debit entry for ' . $data['arrPostData']['receipt_number'];
                            return $arrResponse;
                        }
                        $arrResponse['status'] = 'success';
                        //Create Billable Item to get payment
                        $arrResponse = $this->createSystemBillableItem($data);
                    }
                } //Zero Credit available
                elseif (empty($creditRemainingAmount['remaining_amount']) || $creditRemainingAmount['remaining_amount]'] <= 0) {
                    $arrResponse = $this->createSystemBillableItem($data);
                }
            }
        }
        return $arrResponse;
    }

    public function getCreditAmountForIndividuals($data = array())
    {
        $amountDetails = [];

        $creditAmountDetails = ['credit_amount' => 0, 'debit_amount' => 0, 'remaining_amount' => 0];

        // Build the conditions for the query
        $query = $this->tenantDB()->table('chsone_credit_accounts')
            ->where('soc_id', $data['data']['soc_id'])
            ->where('account_id', $data['data']['account_id'])
            ->where('account_context', $data['data']['account_context'])
            ->where('use_credit', 'adjustable');

        // Add conditions for bill_date if present
        if (!empty($data['data']['bill_date'])) {
            $query->where(function ($q) use ($data) {
                $q->where('use_credit_after', '<=', $data['data']['bill_date'])
                    ->orWhereNull('use_credit_after');
            });
        }

        // Add conditions for bill_type if present
        if (!empty($data['data']['bill_type'])) {
            $query->where(function ($q) use ($data) {
                $q->where('use_credit_for', $data['data']['bill_type'])
                    ->orWhere('use_credit_for', 'both');
            });
        }

        // Execute the query and get results
        $amountDetails = $query->get()->toArray();
        $amountDetails = json_decode(json_encode($amountDetails), true);
        if (!empty($amountDetails)) {
            foreach ($amountDetails as $amountData) {
                if ($amountData['transaction_type'] == 'cr') {
                    $creditAmountDetails['credit_amount'] += $amountData['amount'];
                } else {
                    $creditAmountDetails['debit_amount'] += $amountData['amount'];
                }
            }
        }
        $creditAmountDetails['remaining_amount'] = $creditAmountDetails['credit_amount'] - $creditAmountDetails['debit_amount'];

        if ($creditAmountDetails['remaining_amount'] < 0) {
            $creditAmountDetails['remaining_amount'] = 0;
        }
        return $creditAmountDetails;
    }

    public function saveCreditAccountResponse($data = array())
    {
        $response = ['error' => false, 'data' => null];

        try {
            if (empty($data)) {
                $this->status = 'error';
                $this->message = 'No data values provided.';
                $this->statusCode = 400;
                return;
            }
            $user = $data['user'];
            $id = $data['id'];
            $username = (!empty($data['username']) ? $data['username'] : null);

            $data = $data['data'];
            $data['username'] = $username;
            $data['user'] = $user;

            if ($id) {
                $cr = ChsoneCreditAccount::where('credit_account_id', $id)
                    ->where('soc_id', $data['soc_id'])
                    ->first();

                $cr->updated_by = $user;
                $cr->updated_date = date('Y-m-d H:i:s');
                $cr->use_credit_after = (!empty($data['adjustable_date']) ? $data['adjustable_date'] : null);
                $cr->is_locked = 0;
                $cr->use_credit_for = (!empty($data['used_for']) ? $data['used_for'] : null);

                $cr->is_invoice_rectification = ((isset($data['is_invoice_rectification']) && !empty($data['is_invoice_rectification'])) ? $data['is_invoice_rectification'] : null);
                $cr->income_account_id = ((isset($data['income_account_ledger_id']) && !empty($data['income_account_ledger_id'])) ? $data['income_account_ledger_id'] : null);
                $cr->narration = $data['narration'];
                $cr->use_credit = (isset($data['credit_used_type']) && !empty($data['credit_used_type']) ? $data['credit_used_type'] : 'adjustable');
                // Save data
                if ($cr->save()) {
                    $response['data'] = $cr->credit_account_id;
                } else {
                    $response['error'] = true;
                    $response['data'] = ['message' => 'Failed to save record'];
                }
            } else {
                if (!empty($data['credit_used_type']) && $data['credit_used_type'] == 'both') {
                    $data['credit_used_type'] = 'adjustable';
                    $data['payment_amount'] = $data['adjustable_amount'];
                    $saveResponse = $this->saveCreditNote($data);
                    if ($saveResponse['error'] == false) {
                        $data['credit_used_type'] = 'refundable';
                        $data['payment_amount'] = $data['refundable_amount'];
                        $data['used_for'] = NULL;
                        $data['adjustable_date'] = NULL;

                        $saveResponse = $this->saveCreditNote($data);
                        if ($saveResponse['error'] == false) {
                            $response['data'] = $saveResponse['id'];
                        }
                    }
                } else {
                    $saveResponse = $this->saveCreditNote($data);
                    if ($saveResponse['error'] == false) {
                        $response['data'] = $saveResponse['id'];
                    }
                }
            }
        } catch (\Exception $e) {
            $response['error'] = true;
            $response['data'] = ['message' => $e->getMessage()];
        } finally {
            if (empty($response['data'])) {
                $response['data'] = ['message' => 'working is ok'];
                $response['error'] = false;
            }
            // return response()->json($response);
            return $response;
        }
    }

    public function saveCreditNote($data)
    {
        $cr = new ChsoneCreditAccount();
        $cr->created_by = isset($data['user']) ? $data['user'] : 0;
        $cr->created_date = $this->getCurrentDate('database');
        $cr->soc_id = $data['soc_id'];
        if (isset($data['invoice_no']) && !empty($data['invoice_no'])) {
            $cr->invoice_number = $data['invoice_no'];
        } elseif (isset($data['invoice_number']) && !empty($data['invoice_number'])) {
            $cr->invoice_number = $data['invoice_number'];
        } else {
            $cr->invoice_number = null;
        }
        $cr->is_invoice_rectification = ((isset($data['is_invoice_rectification']) && !empty($data['is_invoice_rectification'])) ? $data['is_invoice_rectification'] : null);
        $cr->income_account_id = ((isset($data['income_account_ledger_id']) && !empty($data['income_account_ledger_id'])) ? $data['income_account_ledger_id'] : null);
        $cr->payment_tracker_id = (isset($data['payment_tracker_id']) && !empty($data['payment_tracker_id']) ? $data['payment_tracker_id'] : null);
        $cr->account_id = $data['account_id'];
        $cr->account_name = $data['account_name'];
        $cr->account_context = $data['account_context'];
        $cr->amount = $data['payment_amount'];
        $cr->payment_mode = (isset($data['payment_mode']) && !empty($data['payment_mode']) ? $data['payment_mode'] : null);
        $cr->payment_date = (isset($data['payment_date']) && !empty($data['payment_date']) ? $this->getDatabaseDate($data['payment_date']) : null);
        $cr->transaction_type = $data['transaction_type'];
        $cr->narration = $data['narration'];
        $cr->use_credit = (isset($data['credit_used_type']) && !empty($data['credit_used_type']) ? $data['credit_used_type'] : 'adjustable');
        $cr->use_credit_after = (isset($data['adjustable_date']) && !empty($data['adjustable_date']) ? $data['adjustable_date'] : null);
        $cr->is_locked = (isset($data['is_locked']) && !empty($data['is_locked']) ? $data['is_locked'] : 0);
        $cr->use_credit_for = (isset($data['used_for']) && !empty($data['used_for']) ? $data['used_for'] : null);
        $cr->reference_no = (isset($data['transaction_reference']) && !empty($data['transaction_reference']) ? $data['transaction_reference'] : null);
        $cr->context = (isset($data['context']) && !empty($data['context']) ? $data['context'] : 'system');
        $cr->created_name = isset($data['username']) ? $data['username'] : '';
        // Save data
        if ($cr->save()) {
            $data['error'] = false;
            $data['id'] = $cr->credit_account_id;
            return $data;
        } else {
            $data['error'] = true;
            $data['message'] = array('message' => $cr->getMessages());
            return $data;
        }
    }

    public function createSystemBillableItem($data = array())
    {
        $arrResponse['status'] = 'error';

        //get Payment reversal incident particular
        $arrIncidentPaymentReversalCharges = $this->getBillingTypeByParticular(array('soc_id' => $data['soc_id'], 'particular' => 'payment reversal correction'));
       
        if (empty($arrIncidentPaymentReversalCharges) && count($arrIncidentPaymentReversalCharges) == 0) {
            $arrResponse['message'] = 'Unable to find payment response particular';
            return $arrResponse;
        }
        //get next invoice period
        $arrNextInvoicePeriod = $this->getNextInvoicePeriod(array('soc_id' => $data['soc_id'], 'unit_id' => $data['arrPostData']['unit_id']));
        if (empty($arrNextInvoicePeriod['start_date'])) {
            $arrResponse['message'] = 'Unbale to create billable date';
            return $arrResponse;
        }

        //Create Billable Item to get payment
        $amount = (empty($data['billableItemAmount'])) ? $data['arrPostData']['payment_amount'] : $data['billableItemAmount'];
        $arrResponse = $this->saveBillableItem(array(
            'soc_id' => $data['soc_id'],
            'arrBillableDetail' => array('unit_id' => $data['arrPostData']['unit_id'], 'member_id' => $data['arrMemberDetail']['id'], 'member_name' => $data['arrMemberDetail']['member_first_name'] . ' ' . $data['arrMemberDetail']['member_last_name'], 'income_Account_id' => $arrIncidentPaymentReversalCharges['id'], 'amount' => $amount, 'from_date' => $arrNextInvoicePeriod['start_date'], 'to_date' => $arrNextInvoicePeriod['end_date'], 'note' => $data['arrPostData']['reversal_note'], 'is_reversal' => 1)
        ));
        return $arrResponse;
    }

    public function getBillingTypeByParticular($data = array())
    {
        $arrBillingDetail = array();
        if (empty($data['soc_id']) || empty($data['particular'])) {
            return $arrBillingDetail;
        }

        $objBillingDetail = $this->tenantDB()->table('income_common_area_charges')
        ->where('particular', $data['particular'])
        ->where('soc_id', $data['soc_id'])
        ->first();

        if (!empty($objBillingDetail)) {
            $arrBillingDetail = (array) $objBillingDetail;
        } else {
            $arrBillingDetail = $this->saveCommonBillCharges(array(
                'soc_id' => $data['soc_id'],
                'arrParticularDetail' => array('particular' => $data['particular'], 'effective_date' => date('Y-m-d'))
            ));
        }
        return $arrBillingDetail;
    }

    public function saveCommonBillCharges($data = array()) 
    {
        $arrResponse = [
            'status' => 'error',
        ];

        if (isset($data['arrParticularDetail']) && !empty($data['arrParticularDetail'])) 
        {
            // Check if there's an ID to update an existing record
            if (isset($data['arrParticularDetail']['id']) && $data['arrParticularDetail']['id']) {
                $objIncomebilling = IncomeCommonAreaCharge::where('soc_id', $data['soc_id'])
                    ->where('id', $data['arrParticularDetail']['id'])
                    ->first();
            } else {
                // If no ID, create a new record
                $objIncomebilling = new IncomeCommonAreaCharge();
                $objIncomebilling->created_date = date('Y-m-d H:i:s');
                // $objIncomebilling->created_by = $this->input['user_id'];
                $objIncomebilling->created_by = $this->input['user_id'];
            }

            // Assign the rest of the fields
            $objIncomebilling->particular = $data['arrParticularDetail']['particular'];
            $objIncomebilling->rate_type = $data['arrParticularDetail']['rent_type'] ?? null;
            $objIncomebilling->rate = $data['arrParticularDetail']['rent'] ?? 0;
            $objIncomebilling->duration = $data['arrParticularDetail']['duration'] ?? null;
            $objIncomebilling->add_to_maintenance = 0;
            $objIncomebilling->ledger_id = $data['arrParticularDetail']['ledger_id'] ?? 0;
            $objIncomebilling->effective_date = $data['arrParticularDetail']['effective_date'];
            $objIncomebilling->till_date = $data['arrParticularDetail']['till_date'] ?? null;
            $objIncomebilling->tax_class_id = $data['arrParticularDetail']['tax'];
            $objIncomebilling->apply_late_payment_interest = $data['arrParticularDetail']['lateCharge'];
            $objIncomebilling->updated_date = date('Y-m-d H:i:s');
            $objIncomebilling->updated_by = $this->input['user_id'];
            $objIncomebilling->soc_id = $data['soc_id'];

            // Attempt to save and handle any errors
            if (!$objIncomebilling->save()) {
                $arrMessages = $objIncomebilling->getErrors(); // Assuming there's a getErrors method to fetch validation errors
                $arrResponse['message'] = $arrMessages;
            } else {
                $arrResponse = [
                    'status' => 'success',
                    'id' => $objIncomebilling->id,
                ];
            }
        }

        return $arrResponse;
    }

    public function getNextInvoicePeriod($data = array())
    {
        $arrInvoiceSetting = $this->getInvoiceSetting(array('soc_id' => $data['soc_id']));

        if (!empty($arrInvoiceSetting)) {
            $arrGetInvoiceData = array('soc_id' => $data['soc_id'], 'unit_id' => $data['unit_id'], 'order_by' => 'unit_invoice_id desc');
            $arrInvoiceData = $this->getLastUnitInvoiceByUnit($arrGetInvoiceData);

            if (empty($arrInvoiceData['to_date'])) {
                $arrInvoiceData['to_date'] = $arrInvoiceSetting['effective_date'];
            }

            if (!empty($arrInvoiceData['to_date'])) {
                 $nextInvoiceStartDate = Carbon::parse($arrInvoiceData['to_date'])->addDay()->format('Y-m-d');
                if (!empty($arrInvoiceSetting)) {
                    switch (strtolower($arrInvoiceSetting['invoicing_frequency'])) {
                        case 'quarterly':
                            $frequency = 3;
                            break;
                        case 'half_yearly':
                            $frequency = 6;
                            break;
                        case 'yearly':
                            $frequency = 12;
                            break;
                        case 'monthly':
                        default:
                            $frequency = 1;
                            break;
                    }
                }
                $nextInvoiceEndDate = Carbon::parse($nextInvoiceStartDate)->addMonths($frequency)->subDay()->format('Y-m-d');
                return array('start_date' => $nextInvoiceStartDate, 'end_date' => $nextInvoiceEndDate);
            }
        }
        return array();
    }

    public function saveBillableItem($data = array()) 
    {
        $arrResponse = ['status' => 'error'];

        if (isset($data['arrBillableDetail']) && !empty($data['arrBillableDetail'])) 
        {
            // Check if the billable item ID exists, to determine whether to update or create a new record
            if (!empty($data['arrBillableDetail']['billable_item_id'])) {
                $debitNote = IncomeBillableItem::where('soc_id', $data['soc_id'])
                    ->where('income_billable_item_id', $data['arrBillableDetail']['billable_item_id'])
                    ->first();
            } else {
                // Create a new record
                $debitNote = new IncomeBillableItem();
                $debitNote->created_date = now();
                $debitNote->created_by = $this->input['user_id'];
            }

            // Assign values from the data array to the debit note model
            $debitNote->soc_id = $data['soc_id'];
            $debitNote->unit_id = $data['arrBillableDetail']['unit_id'];
            $debitNote->member_id = $data['arrBillableDetail']['member_id'];
            $debitNote->income_account_id = $data['arrBillableDetail']['income_Account_id'];
            $debitNote->debit_note_type = !empty($data['arrBillableDetail']['debit_note_type']) 
                                        ? $data['arrBillableDetail']['debit_note_type'] 
                                        : 'debit_note';
            $debitNote->note = $data['arrBillableDetail']['note'];
            $debitNote->amount = $data['arrBillableDetail']['amount'];
            $debitNote->from_date = $data['arrBillableDetail']['from_date'];
            $debitNote->to_date = $data['arrBillableDetail']['to_date'];
            $debitNote->expense_id = !empty($data['arrBillableDetail']['expense_id']) 
                                    ? $data['arrBillableDetail']['expense_id'] 
                                    : '';
            $debitNote->status = 1;
            $debitNote->is_reversal = !empty($data['arrBillableDetail']['is_reversal']) 
                                    ? $data['arrBillableDetail']['is_reversal'] 
                                    : 0;
            $debitNote->updated_date = now();
            $debitNote->updated_by = $this->input['user_id'];

            // Save the record and check for validation errors
            if (!$debitNote->save()) {
                $arrMessages = $debitNote->getErrors();
                $arrResponse['message'] = $arrMessages;
            } else {
                $arrResponse = [
                    'status' => 'success',
                ];
            }
        }

        return $arrResponse;
    }

    public function refundablePaymentReversalProcess($data = array())
    {
        $arrResponse['status'] = 'error';
        if (!empty($data['arrPostData'])) {
            if (!empty($data['arrPostData']['bill_type']) && in_array(strtolower($data['arrPostData']['bill_type']), array('member', 'creditaccount-member'))) {

                $arrCreditData['soc_id'] = $data['arrPostData']['soc_id'];
                $arrCreditData['account_id'] = $data['arrPostData']['unit_id'];
                $arrCreditData['account_context'] = 'unit';

                $creditRemainingAmount = $this->getRefundableAmountForIndividuals(array('data' => $arrCreditData));
                //Credit available
                if (!empty($creditRemainingAmount['remaining_amount']) && $creditRemainingAmount['remaining_amount'] > 0) {
                    //Full Credit available 
                    if ($creditRemainingAmount['remaining_amount'] >= $data['arrPostData']['payment_amount']) {

                        $arrCreditData['transaction_type'] = 'dr';
                        $arrCreditData['context'] = 'system';
                        $arrCreditData['used_for'] = '';
                        $arrCreditData['payment_date'] = date('Y-m-d');
                        $arrCreditData['narration'] = 'Amount Rs ' . $data['arrPostData']['payment_amount'] . ' has reversed from Receipt no ' . $data['arrPostData']['receipt_number'];
                        $arrCreditData['payment_amount'] = $data['arrPostData']['payment_amount'];
                        $arrCreditData['invoice_number'] = '';
                        $arrCreditData['account_name'] = $data['arrMemberDetail']['member_first_name'] . ' ' . $data['arrMemberDetail']['member_last_name'];
                        $arrCreditData['credit_used_type'] = 'refundable';
                        $arrCreditData['payment_tracker_id'] = $data['arrPostData']['id'];
                        $arrCreditData['type'] = $data['arrPostData']['id'];
                        $arrCreditData['payment_mode'] = $data['arrPostData']['payment_mode'];
                        $arrCreditData['use_credit'] = 'refundable';
                        $arrCreditData['is_locked'] = 0;
                        $arrCreditData['income_account_id'] = $data['arrPostData']['income_account_id'];

                        $creditAccountresponse = $this->saveCreditAccountResponse(array(
                            'auth' => true, 
                            'process' => 'fetch', 
                            'soc_id' => $data['soc_id'], 
                            'id' => '',
                            'data' => $arrCreditData, 
                            'user' => $this->input['user_id'], 
                            'username' => $data['user_name']
                        ));
                        if ($creditAccountresponse['error']) {
                            $arrResponse['status'] = 'error';
                            $arrResponse['message'] = 'Unable to save debit entry for ' . $data['arrPostData']['receipt_number'];
                            return $arrResponse;
                        } else {
                            $arrResponse['status'] = 'success';
                        }
                    }  //Partial Credit available 
                    elseif ($creditRemainingAmount['remaining_amount'] < $data['arrPostData']['payment_amount']) {
                        $arrResponse['status'] = 'error';
                        $arrResponse['message'] = 'Enough balance is not available to complete the transaction, Amount partially refunded.';
                    }
                } //Zero Credit available
                elseif (empty($creditRemainingAmount['remaining_amount']) || $creditRemainingAmount['remaining_amount]'] <= 0) {
                    $arrResponse['status'] = 'error';
                    $arrResponse['message'] = 'Balance is not available to complete the transaction, Amount already refunded.';
                }
            }
        }
        return $arrResponse;
    }

    public function getRefundableAmountForIndividuals($data = [])
    {
        $amountDetails = [];
        $creditAmountDetails = [
            'credit_amount' => 0, 
            'debit_amount' => 0, 
            'remaining_amount' => 0
        ];   

        $query = ChsoneCreditAccount::where('soc_id', $data['data']['soc_id'])
        ->where('account_id', $data['data']['account_id'])
        ->where('account_context', $data['data']['account_context'])
        ->where('use_credit', 'refundable');

        // Fetch refundable credit account details
        $amountDetails = $query->get()->toArray();

        if (!empty($amountDetails)) {
            foreach ($amountDetails as $amountData) {
                if ($amountData['transaction_type'] == 'cr') {
                    $creditAmountDetails['credit_amount'] = $creditAmountDetails['credit_amount'] + $amountData['amount'];
                } else {
                    $creditAmountDetails['debit_amount'] = $creditAmountDetails['debit_amount'] + $amountData['amount'];
                }
            }
        }
        $creditAmountDetails['remaining_amount'] = $creditAmountDetails['credit_amount'] - $creditAmountDetails['debit_amount'];
        if ($creditAmountDetails['remaining_amount'] < 0) {
            $creditAmountDetails['remaining_amount'] = 0;
        }
        return $creditAmountDetails;
    }

    public function invoicePaymentReversalProcess($data = array())
    {
        $arrResponse['status'] = 'error';
        $arrInvoiceList = $this->getIncomeInvoicePaymentByReceipt(array('soc_id' => $data['soc_id'], 'receipt_number' => $data['arrPostData']['receipt_number']));
        if (!empty($arrInvoiceList)) {
            $receiptAmount = $data['arrPostData']['payment_amount'];
            foreach ($arrInvoiceList as $eachInvoiceDetail) {
                $eachInvoiceDetail['payment_amount'] += $eachInvoiceDetail['writeoff_amount'];
                $paymentStatus = 'unpaid';
                $status = 'generated';
                $arrIncomeInvoiceDetail = $this->getUnitInvoiceDetail(array('soc_id' => $data['soc_id'], 'unit_id' => $eachInvoiceDetail['fk_unit_id'], 'invoice_number' => $eachInvoiceDetail['invoice_number'], 'getSingleInvoiceDetail' => true));
                $invoiceReceiptAmount = $eachInvoiceDetail['payment_amount'] + $eachInvoiceDetail['late_payment_charges'] + $eachInvoiceDetail['tds_deducted'];
                $invoiceAmount = $this->getMaintenanceReversalInvoiceAmount(array('soc_id' => $data['soc_id'], 'arrReceiptData' => $eachInvoiceDetail, 'arrIncomeInvoiceDetail' => $arrIncomeInvoiceDetail));
                $previousPaidAmount = isset($arrIncomeInvoiceDetail['unit_invoice_detail'][0]['invoice_amount_detail'], $eachInvoiceDetail['writeoff_amount'], $invoiceReceiptAmount)
                        ? round(
                            (float) ($arrIncomeInvoiceDetail['unit_invoice_detail'][0]['invoice_amount_detail']['partialpaidAmount'] ?? 0) +
                            (float) ($arrIncomeInvoiceDetail['unit_invoice_detail'][0]['invoice_amount_detail']['partialLatePaymentCharge'] ?? 0) +
                            (float) $eachInvoiceDetail['writeoff_amount'] -
                            (float) $invoiceReceiptAmount,
                            3
                        )
                        : 0.0;

                if ((isset($arrIncomeInvoiceDetail['unit_invoice_detail'][0]['advance_amount']) && $arrIncomeInvoiceDetail['unit_invoice_detail'][0]['advance_amount'] > 0) || $previousPaidAmount > 0) {
                    $status = $paymentStatus = 'partialpaid';
                }

                if ($eachInvoiceDetail['payment_amount'] >= 0 && $eachInvoiceDetail['late_payment_charges'] > 0) {
                    //Update unit invoice status
                    $arrResponse = $this->updateIncomeUnitInvoice(['soc_id' => $data['soc_id'], 'status' => $status, 'payment_status' => $paymentStatus, 'invoice_number' => $arrIncomeInvoiceDetail['unit_invoice_detail'][0]['unit_invoice_number'] ?? null]);
                    continue;
                }
                $arrResponse = $this->getParticularReversalStatus(array('arrReceiptDetail' => $eachInvoiceDetail, 'arrIncomeInvoiceDetail' => $arrIncomeInvoiceDetail, 'invoiceAmount' => $invoiceAmount, 'status' => $status, 'invoiceStatus' => $paymentStatus));
                if (!empty($arrResponse) && strtolower($arrResponse['status']) == 'error') {
                    return $arrResponse;
                }
                $receiptAmount = (float) round($receiptAmount - $invoiceReceiptAmount, 3);
            }
            if ($receiptAmount > 0) {
                $data['arrPostData']['payment_amount'] = $receiptAmount;
                $arrResponse = $this->creditPaymentReversalProcess($data);
                //  cmnt
            }
            //Delete from payment table
            if (!empty($arrResponse) && strtolower($arrResponse['status']) == 'success') {
                $arrResponse = $this->deleteInvoicePaymentRecord(array('soc_id' => $data['arrPostData']['soc_id'], 'receipt_number' => $data['arrPostData']['receipt_number']));
            }
        }
        return $arrResponse;
    }

    public function getIncomeInvoicePaymentByReceipt($data = array())
    {
        $arrIncomeInvoicePayment = [];

        // Build the query using Laravel's Eloquent
        $objIncomeInvoicePayment = IncomeInvoicePayment::where('soc_id', $data['soc_id'])
            ->where('receipt_number', $data['receipt_number'])
            ->orderBy('payment_id', 'desc')
            ->get();

        // Check if there are any records
        if (!empty($objIncomeInvoicePayment)) {
            $arrIncomeInvoicePayment = $objIncomeInvoicePayment->toArray();
        }

        return $arrIncomeInvoicePayment;
    }

    public function deleteInvoicePaymentRecord($data = array())
    {
        $arrResponse['status'] = 'error';
        $data['updated_date'] = date('Y-m-d H:i:s');
        $updateResponse = $this->deleteIncomeInvoicePayment($data);
        if (!empty($updateResponse) && strtolower($updateResponse['status']) == 'success') {
            $arrResponse['status'] = 'success';
        }
        return $arrResponse;
    }

    public function deleteIncomeInvoicePayment($data,$full_list = false)
    {
        $arrResponse = ['status' => 'success', 'message' => []];
        try {

            $receiptNumbers = is_array($data['receipt_number']) ? $data['receipt_number'] : [$data['receipt_number']];

            // Delete payment entry in Laravel
            $deletedRows = IncomeInvoicePayment::where('soc_id', $data['soc_id'])
                            ->whereIn('receipt_number', $receiptNumbers)
                            ->delete();
    
            if ($deletedRows === 0) {
                $arrResponse = ['status' => 'error', 'message' => 'No records deleted'];
            }
        } catch (\Exception $e) {
            $arrResponse = ['status' => 'error', 'message' => $e->getMessage()];
        }
    
        return $arrResponse;
    }

    public function paymentReversalLedgerEntry($data = array())
    {
        $arrResponse['status'] = 'success';
        if (ACCOUNT_MODULE_EXIST == 1) {
            if (!empty($data['arrPostData'])) {
                //Get payment account ledger
                $data['arrIncomeDetails'] = $this->getPaymentLedgerDetailByMode($data);
                //Get Unit Ledger
                if (!empty($data['arrPostData']['other_information']['credit_account_detail']['credit_used_type']) && $data['arrPostData']['other_information']['credit_account_detail']['credit_used_type'] == 'refundable') {
                    preg_match('#\((.*?)\)#', $data['arrPostData']['other_information']['credit_account_detail']['search_account_name'], $match);
                    $buildingUnit = $match[1];
                    $data['arrUnitLedgerDetails'] = $this->checkledgerExistOrCreate(array("soc_id" => $data['soc_id'], 'ledger_name' => $buildingUnit));
                } else {
                    $arrListnerData = array('soc_id' => $data['soc_id'], 'unit_id' => $data['arrPostData']['unit_id']);
                    $arrUnitDetails = $this->getUnitDetailById($arrListnerData);
                    if (!empty($arrUnitDetails)) {
                        $data['arrUnitLedgerDetails'] = $this->checkledgerExistNew(array('soc_id' => $data['soc_id'], 'ledger_id' => $arrUnitDetails['ledger_account_id']));
                    }
                }

                $strNarration = '';
                if (!empty($data['arrPostData']['payment_mode']) && in_array(strtolower($data['arrPostData']['payment_mode']), $this->constants['payment_mode_for_clearance'])) {
                    $strNarration = ' with transaction ref. (' . $data['arrPostData']['transaction_reference'] . ', ' . $data['arrPostData']['payment_instrument'] . ')';
                } elseif (!empty($data['arrPostData']['payment_mode']) && strtolower($data['arrPostData']['payment_mode']) == 'cashtransfer') {
                    $strNarration = ' with payment ref. (' . $data['arrPostData']['transaction_reference'] . ')';
                }

                // Ledger entry for payment amount
                $arrLedgerTransactionData['soc_id'] = $data['soc_id'];
                $arrLedgerTransactionData['user_id'] = $this->input['user_id'];
                $arrLedgerTransactionData['voucher_type'] = VOUCHER_RECEIPT;
                $arrLedgerTransactionData['voucher_reference_number'] = $data['arrPostData']['receipt_number'];
                $arrLedgerTransactionData['voucher_reference_id'] = $data['arrPostData']['payment_tracker_id'];
                $arrLedgerTransactionData['to_ledger_id'] = $data['arrIncomeDetails']['ledger_id'];
                $arrLedgerTransactionData['from_ledger_id'] = $data['arrUnitLedgerDetails']['recieving_ledger_id'];
                $arrLedgerTransactionData['transaction_date'] = $data['arrPostData']['payment_date'];
                $arrLedgerTransactionData['transaction_amount'] = ($data['arrPostData']['tds_amount'] > 0) ? (float) round($data['arrPostData']['payment_amount'] - $data['arrPostData']['tds_amount'], 3) : $data['arrPostData']['payment_amount'];

                $arrLedgerTransactionData['narration'] = 'Amount reversed against Receipt ' . $data['arrPostData']['receipt_number'] . ' on ' . date('Y-m-d') . ' through ' . $data['arrPostData']['payment_mode'] . $strNarration;
                $arrLedgerTransactionData['to_ledger_name'] = $data['arrIncomeDetails']['ledger_name'];
                $arrLedgerTransactionData['from_ledger_name'] = $data['arrUnitLedgerDetails']['receiver_name'];
                $arrLedgerTransactionData['payment_reference'] = $data['arrPostData']['transaction_reference'];
                $arrLedgerTransactionData['transaction_type'] = '';
                $arrLedgerTransactionData['mode_of_payment'] = $data['arrPostData']['payment_mode'];
                $arrLedgerTransactionData['other_payment_ref'] = '';

                if (isset($data['arrPostData']['reversal_note'])) {
                    $arrLedgerTransactionData['narration'] .= ' [' . $data['arrPostData']['reversal_note'] . ']';
                }
                $arrLedgerEntry = $this->transactionLedgerEntry($arrLedgerTransactionData);
                if (isset($arrLedgerEntry['error']) && !empty($arrLedgerEntry['error'])) {
                    $arrResponse['status'] = 'error';
                    $arrResponse['message'] = 'Unable to save ledger entry for ' . $data['arrPostData']['receipt_number'];
                    return $arrResponse;
                }
                //TDS reverse ledger entry
                if (!empty($data['arrPostData']['tds_amount']) && $data['arrPostData']['tds_amount'] > 0) {
                    $arrLedgerTransactionData['transaction_amount'] = $data['arrPostData']['tds_amount'];
                    $arrListnerData['soc_id'] = $data['soc_id'];
                    $arrListnerData['ledger_name'] = 'tds receivable';
                    $arrListnerData['context'] = ''; // CURRENT_ASSETS_GROUP;
                    $arrListnerData['behaviour'] = ASSET;

                    $arrTdsLedger = $this->checkledgerExistNew($arrListnerData);
                    $arrLedgerTransactionData['to_ledger_id'] = $arrTdsLedger['recieving_ledger_id'];
                    $arrLedgerTransactionData['to_ledger_name'] = $arrTdsLedger['receiver_name'];

                    if ($data['arrPostData']['payment_mode'] == 'cashtransfer') {
                        $payment_mode_display = DISPLAY_CASH_TRANSFER;
                    } else {
                        $payment_mode_display = ucfirst($data['arrPostData']['payment_mode']);
                    }


                    $arrLedgerTransactionData['narration'] = 'Reversal of TDS amount deducted against Receipt ' . $data['arrPostData']['receipt_number'] . ' on ' . date('Y-m-d') . ' through ' . $payment_mode_display . $strNarration;
                    if (isset($data['arrPostData']['reversal_note'])) {
                        $arrLedgerTransactionData['narration'] .= ' [' . $data['arrPostData']['reversal_note'] . ']';
                    }
                    $arrLedgerEntry = $this->transactionLedgerEntry($arrLedgerTransactionData);
                    if (isset($arrLedgerEntry['error']) && !empty($arrLedgerEntry['error'])) {
                        $arrResponse['status'] = 'error';
                        $arrResponse['message'] = 'Unable to save tds ledger entry for ' . $data['arrPostData']['receipt_number'];
                        return $arrResponse;
                    }
                }
                //Ledger entry for invoice writeoff
                if (!empty($data['arrPostData']['writeoff_amount']) && $data['arrPostData']['writeoff_amount'] > 0) {
                    if ($data['arrPostData']['writeoff_amount'] >= 1000) {
                        $arrExpenseWriteOff = $this->checkledgerExistNew(array('soc_id' => $data['soc_id'], 'ledger_name' => 'Expense Bad Debt', 'context' => EXPENSE, 'behaviour' => EXPENSE));
                        if (empty($arrExpenseWriteOff['recieving_ledger_id'])) {
                            $arrParentExpense = $this->getLedgerDetail(array('soc_id' => $data['soc_id'], 'ledger_name' => 'Indirect Expense', 'context' => EXPENSE, 'behaviour' => EXPENSE));
                            if (!empty($arrParentExpense['ledger_account_id'])) {
                                $arrExpenseWriteOff['receiver_name'] = 'Expense Bad Debt';
                                $arrExpenseWriteOff['recieving_ledger_id'] = $this->createNewLedgerExit(array('soc_id' => $data['soc_id'], 'ledger_name' => $arrExpenseWriteOff['receiver_name'], 'context' => EXPENSE, 'behaviour' => EXPENSE, 'parent_id' => $arrParentExpense['ledger_account_id']));
                            }
                        }
                    } else {
                        $arrExpenseWriteOff = $this->checkledgerExistNew(array('soc_id' => $data['soc_id'], 'ledger_name' => 'Expense Write Off', 'context' => EXPENSE, 'behaviour' => EXPENSE));
                    }
                    if (empty($arrExpenseWriteOff['recieving_ledger_id'])) {
                        $arrResponse['status'] = 'error';
                        $arrResponse['message'] = 'Unable to get writeoff expense ledger for receipt ' . $data['arrPostData']['receipt_number'];
                        return $arrResponse;
                    }
                    $arrLedgerTransactionData['transaction_amount'] = $data['arrPostData']['writeoff_amount'];
                    $arrLedgerTransactionData['to_ledger_id'] = $arrExpenseWriteOff['recieving_ledger_id'];
                    $arrLedgerTransactionData['to_ledger_name'] = $arrExpenseWriteOff['receiver_name'];

                    $arrLedgerTransactionData['narration'] = 'Reversal of writeoff amount against Receipt ' . $data['arrPostData']['receipt_number'] . ' on ' . date('Y-m-d');
                    if (isset($data['arrPostData']['reversal_note'])) {
                        $arrLedgerTransactionData['narration'] .= ' [' . $data['arrPostData']['reversal_note'] . ']';
                    }
                    $arrLedgerEntry = $this->transactionLedgerEntry($arrLedgerTransactionData);
                    if (isset($arrLedgerEntry['error']) && !empty($arrLedgerEntry['error'])) {
                        $arrResponse['status'] = 'error';
                        $arrResponse['message'] = 'Unable to save writeoff ledger entry for ' . $data['arrPostData']['receipt_number'];
                        return $arrResponse;
                    }
                }
            } else {
                $arrResponse['status'] = 'error';
                $arrResponse['message'] = 'data not found.';
            }
        }
        return $arrResponse;
    }

    public function getPaymentLedgerDetailByMode($data = array())
    {
        $arrIncomeAccounts = array();
        // Getting bank/cash ledger details
        $arrAccountDetail = $this->getBankCashAccountDetail(array(
            'soc_id' => $data['soc_id']
        )); // get all Unit details
        $arrLedgerAccountDetail = $this->getLedgerAccountDetail(array(
            'account_detail' => $arrAccountDetail
        )); // get all Unit details
        if (empty($data['arrPostData']['bank_account'])) {
            $data['arrPostData']['bank_account'] = $data['arrPostData']['other_information']['member_detail']['bank_ledger'] ?? '';
        } else {
            $data['arrPostData']['bank_account'] = $data['arrPostData']['bank_account'] ?? '';
        }
        if (!empty($arrLedgerAccountDetail)) {
            $arrIncomeAccounts['ledger_id'] = $arrLedgerAccountDetail['cash']['ledger_id'];
            $arrIncomeAccounts['ledger_name'] = $arrLedgerAccountDetail['cash']['ledger_name'];
            if (strtolower($data['arrPostData']['payment_mode']) != 'cash') {
                if (!empty($data['arrPostData']['bank_account'])) {
                    $arrIncomeAccounts['ledger_id'] = $data['arrPostData']['bank_account'];
                    $arrIncomeAccounts['ledger_name'] = $arrLedgerAccountDetail['arrBank'][$data['arrPostData']['bank_account']];
                } else {
                    $arrIncomeAccounts['ledger_id'] = $arrLedgerAccountDetail['bank']['ledger_id'];
                    $arrIncomeAccounts['ledger_name'] = $arrLedgerAccountDetail['bank']['ledger_name'];
                }
            }
        }
        return $arrIncomeAccounts;
    }

    public function getBankCashAccountDetail($data = array())
    {
        $arrAccountDetail = array();

        $query = $this->tenantDB()->table('chsone_grp_ledger_tree as grpLedger')
        ->select(
            'grpLedger.ledger_account_id',
            'grpLedger.ledger_account_name',
            'grpLedger.context',
            'account.default_account',
            'account.default_bank_for_incidental',
            'account.default_bank_for_nonmember',
            'account.bank_name',
            'account.account_number',
            'account.bank_address',
            'account.bank_city',
            'account.bank_ifsc',
            'account.account_id'
        )
        ->join('chsone_accounts_master as account', 'grpLedger.ledger_account_id', '=', 'account.ledger_account_id')
        ->where('grpLedger.soc_id', $data['soc_id'])
        ->where('grpLedger.entity_type', 'ledger')
        ->where('grpLedger.status', 1);


        if (isset($data['only_bank'])) {
            $query->where('grpLedger.context', 'bank');
        } else {
            $query->whereIn('grpLedger.context', ['cash', 'bank']);
        }

        // Execute the query
        $resultset = $query->get();
        $arrAccountDetail = json_decode(json_encode($resultset), true);
        return $arrAccountDetail;
    }

    public function getLedgerAccountDetail($data = array())
    {
        $arrAccountDetail = array(
            'cash' => array(),
            'bank' => array(),
            'arrBank' => array()
        );
        if (!empty($data['account_detail'])) {
            foreach ($data['account_detail'] as $eachAccountDetail) {
                if (empty($arrAccountDetail['cash']) && strtolower($eachAccountDetail['context']) == 'cash') {
                    $arrAccountDetail['cash']['ledger_id'] = $eachAccountDetail['ledger_account_id'];
                    $arrAccountDetail['cash']['ledger_name'] = $eachAccountDetail['ledger_account_name'];
                    $arrAccountDetail['arrCash'][$eachAccountDetail['ledger_account_id']] = $eachAccountDetail['ledger_account_name'];
                } elseif (strtolower($eachAccountDetail['context']) == 'bank') {
                    if ($eachAccountDetail['default_account'] == 1) {
                        $arrAccountDetail['bank']['ledger_id'] = $eachAccountDetail['ledger_account_id'];
                        $arrAccountDetail['bank']['ledger_name'] = $eachAccountDetail['ledger_account_name'];
                    }
                    $arrAccountDetail['arrBank'][$eachAccountDetail['ledger_account_id']] = $eachAccountDetail['ledger_account_name'];
                }
                if (isset($data['default_bank_incidental']) && $data['default_bank_incidental'] && $eachAccountDetail['default_bank_for_incidental'] == 1) {
                    $arrAccountDetail['bank']['ledger_id'] = $eachAccountDetail['ledger_account_id'];
                    $arrAccountDetail['bank']['ledger_name'] = $eachAccountDetail['ledger_account_name'];
                }
                if (isset($data['default_bank_nonmember']) && $data['default_bank_nonmember'] && $eachAccountDetail['default_bank_for_nonmember'] == 1) {
                    $arrAccountDetail['bank']['ledger_id'] = $eachAccountDetail['ledger_account_id'];
                    $arrAccountDetail['bank']['ledger_name'] = $eachAccountDetail['ledger_account_name'];
                }
            }
            if (empty($arrAccountDetail['bank'])) {
                foreach ($arrAccountDetail['arrBank'] as $key => $value) {
                    $arrAccountDetail['bank']['ledger_id'] = $key;
                    $arrAccountDetail['bank']['ledger_name'] = $value;
                    break;
                }
            }
        }

        return $arrAccountDetail;
    }

    public function checkledgerExistOrCreate($data = [])
    {
        $arrClientLedgerDetails = [];

        $ledgerName = $data['ledger_name'];

        // An outsider has booked for the society. Check if exists by name; otherwise, create new.
        $objBookerLedger = $this->tenantDB()->table('chsone_grp_ledger_tree')
            ->where('soc_id', $data['soc_id'])
            ->where('context', 'security deposit')
            ->where('entity_type', $this->constants['ENTITY_TYPE_LEDGER'])
            ->where('ledger_account_name', $ledgerName)
            ->first();

        $objBookerLedger = json_decode(json_encode($objBookerLedger), true);

        if (!empty($objBookerLedger)) {
            $arrClientLedgerDetails['recieving_ledger_id'] = $objBookerLedger['ledger_account_id'];
            $arrClientLedgerDetails['receiver_name'] = $objBookerLedger['ledger_account_name'];
        } else {
            // Ledger for the outsider does not exist. Create a ledger entry based on sundry debtors group.
            $objSecurityDepositGroup = $this->tenantDB()->table('chsone_grp_ledger_tree')
                ->where('soc_id', $data['soc_id'])
                ->where('entity_type', $this->constants['ENTITY_TYPE_GROUP'])
                ->where('ledger_account_name', 'Security Deposit')
                ->first();

            if ($objSecurityDepositGroup) {

                $ledgerAccountId = $this->manipulate(
                    $ledgerName,
                    ENTITY_TYPE_LEDGER,
                    "",
                    $objSecurityDepositGroup->ledger_account_id,
                    $objSecurityDepositGroup->behaviour,
                    '',
                    0,
                    '',
                    '',
                    null,
                    $objSecurityDepositGroup->context,
                    0,
                    $data['soc_id'],
                    0,
                    $this->input['user_id']
                );

                if (gettype($ledgerAccountId) != 'boolean' && strpos('DUP', $ledgerAccountId) === false) {
                    $arrClientLedgerDetails['recieving_ledger_id'] = $ledgerAccountId;
                    $arrClientLedgerDetails['receiver_name'] = $ledgerName;
                }
            }
        }

        return $arrClientLedgerDetails;
    }

    public function manipulate($name, $entity_type = ENTITY_TYPE_GROUP, $grp_ledg_id = "", $parent_group = 0, $behaviour = "", $ledg_id = '', $opening_balance = 0, $update_led_id = '', $ledger_start_date = '', $ledger_type = '', $context = '', $txn_id = 0, $soc_id = 0, $is_reco = 0)
    {
        $config = Config::get("constants");
        if(empty($soc_id)){
            $soc_id = $this->input['company_id'];
        }
        $dup = $this->_checkGroupNameDuplication($parent_group, $name, $ledg_id, $update_led_id);
        
        $arrFYDetail = $this->getCurrentFinancialYear(array('soc_id' => $soc_id));
        $arrFYDetail = json_decode(json_encode($arrFYDetail), true);
        $ledger_start_date = $arrFYDetail['fy_start_date'];
        if ($dup == 0) {
            $behaviour = trim(strtolower($behaviour));

            $grp_ledg_tree = new ChsoneGrpLedgerTree();

            if ($ledg_id) {
                $grp_ledg_tree = $this->getLedger($ledg_id);
            }

            $grp_ledg_tree->entity_type = $entity_type;
            $grp_ledg_tree->soc_id = $soc_id;
            $grp_ledg_tree->ledger_account_name = $name;
            $grp_ledg_tree->ledger_start_date = $ledger_start_date;
            $grp_ledg_tree->context_ref_id = 0;

            if (!empty($ledger_type) && $ledger_type != "NULL" && $ledger_type != '') {
                $grp_ledg_tree->operating_type = $ledger_type;
            } else {
                $grp_ledg_tree->operating_type = '';
            }

            if (!empty($grp_ledg_id)) {
                $grp_ledg_tree->ledger_account_id = $grp_ledg_id;
            }

            if ($update_led_id != '') {
                $grp_ledg_tree->ledger_account_id = $update_led_id;
            }
            if (!empty($parent_group)) {
                $grp_ledg_tree->parent_id = $parent_group;
                $ledger_props = $this->_getLedgerProps($parent_group);
            } else {
                $grp_ledg_tree->parent_id = 0;
            }

            if (!empty($behaviour)) {
                $grp_ledg_tree->behaviour = $behaviour;
                $grp_ledg_tree->nature_of_account = $config->nature_account->$behaviour;
                $grp_ledg_tree->report_head = $config->report_head->$behaviour;
            } else {
                $grp_ledg_tree->behaviour = $ledger_props["behaviour"];
                $grp_ledg_tree->nature_of_account = $ledger_props["nature_account"];
                $grp_ledg_tree->report_head = $ledger_props["report_head"];
            }

            if (!empty($context) && $context != '') {
                $grp_ledg_tree->context = $context;
            } else {
                $grp_ledg_tree->context = $ledger_props["context"];
            }
            $grp_ledg_tree->defined_by = "user";
            $grp_ledg_tree->status = 1;
            $grp_ledg_tree->added_on = date("Y-m-d H:i:s");
            $grp_ledg_tree->created_by = ($this->input['user_id']) ? $this->input['user_id'] : 0;


            if ($grp_ledg_tree->save()) {
                //Opening balance should not go in case on Income Expense ledger
                if (!in_array(strtolower($grp_ledg_tree->behaviour), array("income", "expense"))) {
                    $txn_date = $ledger_start_date;
                    $narration = 'entry for opening balance';
                    $txn_id = $this->addTxn($grp_ledg_tree->ledger_account_id, $opening_balance, $narration, $txn_date, "", $grp_ledg_tree->nature_of_account, "", "", "", "", $name, $is_opning = 1, $is_reco);
                }

                return $grp_ledg_tree->ledger_account_id;
            } else {
                return false;
            }
        } else {
            return "DUP" . $dup;
        }
    }

    private function _checkGroupNameDuplication($parent, $name, $ledg_id = "", $update_led_id = "")
    {
        $soc_id = $this->input['company_id'];

        $bind[1] = strtolower(trim($name));
        $ledgers_count = 0;
        if ($ledg_id) {
            $ledgers_count = ChsoneGrpLedgerTree::where("soc_id", $soc_id)->where('ledger_account_name', strtolower(trim($name)))->where('ledger_account_id', '!=', $ledg_id)->count();
        }
        if ($update_led_id) {
            $ledgers_count = ChsoneGrpLedgerTree::where("soc_id", $soc_id)->where('ledger_account_name', strtolower(trim($name)))->where('ledger_account_id', '!=', $update_led_id)->count();
        }
        
        return $ledgers_count;
    }

    public function getCurrentFinancialYear($data=array()){
       
        $arrFYDetail = array();
        $arrFYDetail = SocAccountFinancialYearMaster::where("soc_id", $data['soc_id'])->where("confirmed", 0)->orderBy("account_closing_id", "asc")->first();
        
        if(!empty($arrFYDetail))
        {
            $arrFYDetail = $arrFYDetail->toArray();
        }
        return $arrFYDetail;
    }

    public function getLedger($ledger_acct_id, $return_object = 1)
    {
        //fetch ledger details from chsone_grp_ledger_tree whose ledger_account_id = $ledger_acct_id
        $ledger = ChsoneGrpLedgerTree::where("ledger_account_id", $ledger_acct_id)->first();

        if ($ledger) {
            if ($return_object) {
                return $ledger;
            } else {
                return $ledger->toArray();
            }
        } else {
            return false;
        }
    }

    public function _getLedgerProps($parent_group)
    {
        $array = array();

        if (is_numeric($parent_group)) {
            $ledger_props = ChsoneGrpLedgerTree::select('ledger_account_name', 'nature_of_account', 'behaviour', 'report_head', 'context')
            ->where('ledger_account_id', $parent_group)
            ->first();
        }

        $array = $ledger_props;
        return $array;
    }

    public function addTxn($ledger_id, $txn_amt, $narration, $txn_date, $voucher_type = VOUCHER_JOURNAL, $txn_type = "", $from_txn_id = "", $pay_mode = "", $pay_ref = "", $opening_balance = "", $ledger_name = "", $is_opning = 1, $is_reco = 0)
    {
        return $this->_addTransaction($is_reco, $ledger_id, $txn_amt, $narration, $txn_date, $voucher_type, $txn_type, $from_txn_id, $pay_mode, $pay_ref, $ledger_name, $is_opning = 1);
    }

    private function _addTransaction($is_reco = 0, $ledger_id, $txn_amt, $narration, $txn_date, $voucher_type = VOUCHER_JOURNAL, $txn_type = "", $from_txn_id = "", $pay_mode = "", $pay_ref = "", $name = "", $is_opning = "", $other_recp_ref = "", $soc_id = "", $created_by = "", $voucher_reference_id = "", $voucher_reference_number = "", $is_cancelled = 0)
    {
        $txn = new ChsoneLedgerTransaction();
        if (empty($soc_id)) {
            $soc_id = $this->input['company_id'];
        }

        if (!empty($txn_type) || $txn_type != "") {
            $type_txn = $txn_type;
        } else {
            if ($from_txn_id) {
                $mode     = "to";
                $type_txn = "cr";
            } else {
                $mode     = "from";
                $type_txn = "dr";
            }
        }

        //echo "s_opning".$is_opning;exit;
        if ($is_opning == 1) {

            $txn_entrt = ChsoneLedgerTransaction::where('soc_id', $soc_id)
            ->where('is_opening_balance', 1)
            ->where('ledger_account_id', $ledger_id)
            ->where('transaction_date', $txn_date)
            ->first();

            $txn->value_date = $txn_date;
            $txn->txn_id = (isset($txn_entrt->txn_id)  && !empty($txn_entrt->txn_id)) ? $txn_entrt->txn_id : '';
            if (!empty($txn->txn_id)) {
                $txn = ChsoneLedgerTransaction::findFirst("txn_id = " . $txn->txn_id);
            }
        }

        $txn->soc_id = $soc_id;
        $txn->transaction_date = $txn_date;
        $txn->ledger_account_id = $ledger_id;
        $txn->ledger_account_name = $name;
        $txn->voucher_type = $voucher_type;
        $txn->transaction_type = $type_txn; //$ledger_name_obj->nature_of_account;//$txn_type;
        $txn->payment_mode = $pay_mode;
        $txn->payment_reference = $pay_ref;
        $txn->transaction_amount = (is_null($txn_amt)) ? $txn->transaction_amount : $txn_amt;
        $txn->other_reference_id = $other_recp_ref;
        $txn->txn_from_id = $from_txn_id;
        $txn->memo_desc = $narration;
        $txn->is_opening_balance = $is_opning;
        $txn->is_reconciled = $is_reco;
        /*Added New Column in transaction*/
        $txn->voucher_reference_number = $voucher_reference_number;
        $txn->voucher_reference_id = $voucher_reference_id;
        $txn->is_cancelled = $is_cancelled;
        /*End New Column in transaction*/
        $txn->created_by = $this->input['user_id'];
        if (isset($created_by) && !empty($created_by)) {
            $txn->created_by = $created_by;
        }
        $txn->added_on = date("Y-m-d H:i:s");

        if ($txn->save()) {
            return $txn->txn_id;
        } else {
            print_r($txn->getMessages);
        }
        //echo "<pre>";print_r($a->getMessages);exit;
        return false;
    }

    public function getUnitDetailById(array $data = array())
    {
        $soc_id = $data['soc_id'];
        $unit_id = $data['unit_id'];
        $objUnitdetails = ChsoneUnitsMaster::where('soc_id', $soc_id)->where('unit_id', $unit_id)->first();
        if (!empty($objUnitdetails)) {
            $arrUnitDetails = $objUnitdetails->toArray();
        }
        return $arrUnitDetails;
    }

    public function checkledgerExistNew($data=array())
    {
        $arrClinetLedgerDetails = array();
        $query = ChsoneGrpLedgerTree::where('soc_id', $data['soc_id']);
        
        if (isset($data['ledger_name']) && !empty($data['ledger_name'])) {
            $query->where('context', $data['context'])
                  ->where('entity_type', "ledger")
                  ->where('ledger_account_name', $data['ledger_name']);
    
            if (isset($data['behaviour']) && !empty($data['behaviour'])) {
                $query->where('behaviour', $data['behaviour']);
            }
        } elseif (isset($data['ledger_id']) && !empty($data['ledger_id'])) {
            $query->where('ledger_account_id', $data['ledger_id']);
        }

        $objBookerLedger = $query->first();

        //an outsider has booked for the society. Check if exists by name; otherwise, create new.
        if(!empty($objBookerLedger)) {
            $arrClinetLedgerDetails['recieving_ledger_id'] = $objBookerLedger->ledger_account_id;
            $arrClinetLedgerDetails['receiver_name'] = $objBookerLedger->ledger_account_name;
        }
        elseif(!empty($data['group_name']))
        {
            $name         = $data['ledger_name'];
            $entity_type  = "ledger";
            $grp_ledg_id  = "";
            $parent       = $this->getParentGroupId($data, TRUE);
            
            if(empty($parent->ledger_account_id))
            {
                return $arrClinetLedgerDetails;
            }
            $parent_group = $parent->ledger_account_id;
            
            $ledger_id    = $this->manipulate($name, $entity_type, $grp_ledg_id , $parent_group, "", '', 0, '', '', '', '', '', 0, $data['soc_id']);
            if ($ledger_id) {
                $arrClinetLedgerDetails['recieving_ledger_id'] = $ledger_id;
                $arrClinetLedgerDetails['receiver_name'] = $data['ledger_name'];
            }
        }
        unset($objBookerLedger);
        return $arrClinetLedgerDetails;
    }

    public function getParentGroupId($arrData)
    {
        $parent = ChsoneGrpLedgerTree::where('soc_id', $arrData['soc_id'])
            ->where('ledger_account_name', $arrData['group_name'])
            ->where('entity_type', "group")
            ->where('behaviour', $arrData['behaviour'])
            ->where('context', $arrData['context'])
            ->first();
        if ($parent) {
            return $parent;
        } else {
            return false;
        }
    }

    public function getLedgerDetail($data=array())
    {
        $soc_id = $data['soc_id'];
        if (!empty($soc_id)) {
            // Build the query
            $query = $this->tenantDB()->table('chsone_grp_ledger_tree')
                ->where('soc_id', $data['soc_id']);

            if (!empty($data['ledger_id'])) {
                $query->where('ledger_account_id', $data['ledger_id']);
            }
            if (!empty($data['ledger_name'])) {
                $query->where('ledger_account_name', $data['ledger_name']);
            }
            if (!empty($data['behaviour'])) {
                $query->where('behaviour', $data['behaviour']);
            }
            if (!empty($data['context'])) {
                $query->where('context', $data['context']);
            }
            if (!empty($data['entity_type'])) {
                $query->where('entity_type', $data['entity_type']);
            }

            // Execute the query and fetch the first result
            $ledgerDetail = $query->first();

            // Return the result as an array if not empty
            if (!empty($ledgerDetail)) {
                return (array)$ledgerDetail;
            }
        }

        return false;
    }

    public function createNewLedgerExit($data=array())
    {
        $ledger_name = $data['ledger_name'];
        $parent_id = $data['parent_id'];
        $behaviour = $data['behaviour'];
        $context = $data['context'];
        
        //Check whether ledger exist or not
        $objBookerLedger = ChsoneGrpLedgerTree::where('soc_id', $data['soc_id'])
        ->where('entity_type', ENTITY_TYPE_LEDGER)
        ->where('ledger_account_name', $ledger_name)
        ->where('parent_id', $parent_id)
        ->where('behaviour', $behaviour)
        ->first();
        
        if(!empty($objBookerLedger)) {
            return $objBookerLedger->ledger_account_id;
        }
        else 
        {
            //Create new ledger if does not exist
            $ledger_account_id = $this->manipulate($ledger_name, ENTITY_TYPE_LEDGER, "", $parent_id, $behaviour, '', 0, '', '' ,null, $context);
            if (gettype($ledger_account_id) != 'boolean' && strpos($ledger_account_id, 'DUP') === false) {
                return $ledger_account_id;
            }
        }
        return false;
    }

    public function transactionLedgerEntry($data=array())
    {
        $arrResponse = array();
        if(empty(trim($data['from_ledger_id'])) || empty(trim($data['to_ledger_id'])))
        {
            $arrResponse = array('error'=>true);
            return $arrResponse;
        }
        if(empty($data['transaction_date']))
        {
            $data['transaction_date'] = $this->getCurrentDate('database');
        }

        $transaction_id = $this->executeTransactionEntry($data); 
        if($transaction_id){
            $arrResponse = array('success'=>true, 'transaction_id'=>$transaction_id);
        }else{
            $arrResponse = array('error'=>true);
        }
        
        return $arrResponse;
    }

    public function executeTransactionEntry($data)
    {
        $vouchers_array = [];
        if ((strpos($data['voucher_type'], '_') !== false) && (strpos($data['voucher_type'], '_') > 0)) {
            $vouchers_array = explode('_', $data['voucher_type']);
            $data['voucher_type'] = $vouchers_array[0];
        }

        $data['is_opening'] = 0;
        $data['transaction_from_id'] = '';
        $data['transaction_from_id'] = $this->_addTransactionEntry($data);

        if (!empty($data['transaction_from_id'])) {
            if (count($vouchers_array) >= 2) {
                $data['voucher_type'] = $vouchers_array[1];
            }
            if ($this->_addTransactionEntry($data)) {
                return $data['transaction_from_id'];
            } else {
                return false;
            }
        } else {
            return false;
        }
    }

    private function _addTransactionEntry($data)
    {
        $txn = new ChsoneLedgerTransaction();
        if (!empty($data['transaction_from_id'])) {
            $mode = "to";
            $data['transaction_type'] = "cr";
            $ledger_id = $data['to_ledger_id'];
            $ledger_name = $data['to_ledger_name'];
        } else {
            $mode = "from";
            $data['transaction_type'] = "dr";
            $ledger_id = $data['from_ledger_id'];
            $ledger_name = $data['from_ledger_name'];
        }
        //echo "s_opning".$is_opning;exit;
        if (!empty($data['is_opening']) && $data['is_opening'] == 1) {
            $txn_entrt = ChsoneLedgerTransaction::where('soc_id', $data['soc_id'])
                ->where('is_opening_balance', 1)
                ->where('ledger_account_id', $ledger_id)
                ->first();

            $txn->txn_id = (isset($txn_entrt->txn_id)  && !empty($txn_entrt->txn_id)) ? $txn_entrt->txn_id : '';
        }
        $txn->soc_id = $data['soc_id'];
        $txn->transaction_date = $data['transaction_date'];
        $txn->ledger_account_id = $ledger_id;
        $txn->ledger_account_name = $ledger_name;
        $txn->voucher_type = $data['voucher_type'];
        $txn->transaction_type = $data['transaction_type']; //$ledger_name_obj->nature_of_account;//$txn_type;
        $txn->payment_mode = $data['mode_of_payment'];
        $txn->payment_reference = $data['payment_reference'];
        $txn->transaction_amount = $data['transaction_amount'];
        $txn->other_reference_id = $data['other_payment_ref'];
        $txn->txn_from_id = $data['transaction_from_id'];
        $txn->memo_desc = $data['narration'];
        $txn->is_opening_balance = $data['is_opening'];
        $txn->is_reconciled = (!empty($data['is_reconciled'])) ? $data['is_reconciled'] : 0;;
        $txn->created_by = (!empty($this->input['user_id'])) ? $this->input['user_id'] : 0;
        $txn->added_on = date("Y-m-d H:i:s");
        $txn->voucher_reference_number = (!empty($data['voucher_reference_number'])) ? $data['voucher_reference_number'] : '';
        $txn->voucher_reference_id = (!empty($data['voucher_reference_id'])) ? $data['voucher_reference_id'] : '';
        $txn->is_cancelled = (!empty($data['is_cancelled'])) ? 1 : 0;

        if ($txn->save()) {
            return $txn->txn_id;
        } else {
            return false;
        }
    }

    public function updateReversedPaymentLedgerTransactionStatus($data,$full_list = false)
    {
        // Updating a single column
        if(!empty($data['soc_id']) && !empty($data['voucher_type']) && !empty($data['voucher_reference_number']) && !empty($data['voucher_reference_id']))
        {
            $updatedFields = [];

            if (isset($data['is_cancelled'])) {
                $updatedFields['is_cancelled'] = $data['is_cancelled'];
            }

            if (!empty($updatedFields)) {
                $result = ChsoneLedgerTransaction::where('soc_id', $data['soc_id'])
                    ->where('voucher_type', $data['voucher_type'])
                    ->where('voucher_reference_number', $data['voucher_reference_number'])
                    ->where('voucher_reference_id', $data['voucher_reference_id'])
                    ->update($updatedFields);
            }
            if ($result === 0) {
                $arrResponse = array('status'=>'error');
            } else {
                $arrResponse = array('status'=>'success');
            }
        }
        return $arrResponse;
    }

    public function updateIncomePaymentTrackerStatus($data = array())
    {
        $arrResponse['status'] = 'error';
        $data['updated_date'] = date('Y-m-d');
        $updateResponse = $this->updateIncomeInvoicePaymentTrackerStatus($data);
        if (!empty($updateResponse) && strtolower($updateResponse['status']) == 'success') {
            $arrResponse['status'] = 'success';
        }
        return $arrResponse;
    }

    public function updateIncomeInvoicePaymentTrackerStatus($data, $full_list = false)
    {
        $arrResponse = ['status' => 'success', 'message' => []];

        $updateData = [
            'updated_date' => $data['updated_date']
        ];

        if (!empty($data['status'])) {
            $updateData['status'] = $data['status'];
        }
        if (!empty($data['transaction_status'])) {
            $updateData['transaction_status'] = $data['transaction_status'];
        }
        if (!empty($data['invoice_number'])) {
            $updateData['invoice_number'] = $data['invoice_number'];
        }
        if (!empty($data['payment_date'])) {
            $updateData['payment_date'] = $data['payment_date'];
        }
        if (!empty($data['other_information'])) {
            $updateData['other_information'] = $data['other_information'];
        }
        if (!empty($data['reversal_note'])) {
            $updateData['reversal_note'] = $data['reversal_note'];
        }
        if (!empty($data['unit_id'])) {
            $updateData['unit_id'] = $data['unit_id'];
        }
        if (!empty($data['bill_type'])) {
            $updateData['bill_type'] = $data['bill_type'];
        }
        if (!empty($data['total_unpaid_amount'])) {
            $updateData['total_due_amount'] = $data['total_unpaid_amount'];
        }

        $result = IncomeInvoicePaymentTracker::where('soc_id', $data['soc_id'])
            ->where('id', $data['payment_tracker_id'])
            ->update($updateData);

        if (!$result) {
            $arrResponse = ['status' => 'error', 'message' => ['Failed to update payment tracker']];
        }

        return $arrResponse;
    }
}
