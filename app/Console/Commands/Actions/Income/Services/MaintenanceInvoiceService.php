<?php

namespace App\Console\Commands\Actions\Income\Services;

use Illuminate\Console\Command;
use App\Console\Commands\Action;

class MaintenanceInvoiceService extends Action
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'service:maintenanceInvoice {flowId} {parentId} {input}' ;

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Service for get maintenance invoice information';

    /**
     * Execute the console command.
     */
    public function apply()
    {
        $this->data = $this->action('datasource:get-primary-member', $this->pointer, $this->input);
        // dd($result);
    }
}
