<?php

namespace App\Console\Commands\Actions\Income;

use Illuminate\Console\Command;
use App\Console\Commands\Action;
use App\Models\Tenants\ChsoneCreditAccount;
use App\Models\Tenants\ChsoneGrpLedgerTree;
use App\Models\Tenants\ChsoneLedgerTransaction;
use App\Models\Tenants\ChsoneMembersMaster;
use App\Models\Tenants\ChsoneMemberTypeMaster;
use App\Models\Tenants\ChsoneTaxLog;
use App\Models\Tenants\ChsoneUnitsMaster;
use App\Models\Tenants\ExpenseInvoicePaymentTracker;
use App\Models\Tenants\IncomeCommonBillingCharge;
use App\Models\Tenants\IncomeCommonBillingPayment;
use App\Models\Tenants\IncomeInvoicePaymentTracker;
use App\Models\Tenants\IncomeInvoiceGeneralSetting;
use App\Models\Tenants\SocAccountFinancialYearMaster;
use Illuminate\Support\Facades\Config;

class postPayCommonBillDataSource extends Action
{
    protected $constants;

    public function __construct() {
        parent::__construct();
        $this->constants = Config::get("constants");
    }

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'datasource:postPayCommonBill {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Post Pay Common Bill DataSource';

    /**
     * Execute the console command.
     */
    public function apply()
    {
        $posted_values = $this->input;
        
        $arrDataListener['soc_id'] = $posted_values['soc_id'] = $this->input['company_id'];
        $arrDataListener['unit_id'] = $posted_values['unit_id'] = $this->input['unit_id'];
        $posted_values['receiptFrom'] = $this->input['receiptFrom'] ?? 'incident';
        $posted_values['payment_mode'] = $this->input['receipt_mode'];
        $posted_values['payment_date'] = $this->input['receipt_date'];
        $posted_values['bill_type'] = 'member';
        $posted_values['allowed_partial_paid'] = 'Yes';
        $posted_values['payment_type'] = 'quickpay';
        $posted_values['user_id'] = $this->input['user_id'];
        if($posted_values['payment_mode'] == 'cheque') {
            $posted_values['payment_instrument'] = $posted_values['bank_name'];
        }

        // check if unit id is valid or not from chsone_units_master table
        $unit = $this->tenantDB()->table('chsone_units_master')
            ->where('unit_id', $posted_values['unit_id'])
            ->get();
        
        if(count($unit) == 0) {
            $this->status = 'error';
            $this->message = 'Please provide a valid unit id';
            $this->statusCode = 400;
            $this->data = [];
            return;
        }

        // check if bank_account is empty then fecth default bank account id from chsone_account_master table
        if(!isset($posted_values['bank_account']) && empty($posted_values['bank_account'])) {
            $defaultBankAccount = $this->tenantDB()->table('chsone_accounts_master')->where('soc_id', $posted_values['soc_id'])->where('status', '1')->where('default_account', '1')->first();
            $posted_values['bank_account'] = $defaultBankAccount->ledger_account_id ?? '';
        } else {
            $posted_values['bank_account'] = 0;
        }

        //For TDS adjustment
        if (!empty($posted_values['tds_amount'])) {
            $posted_values['invoice_amount'] += $posted_values['tds_amount'];
        }

        if (!empty($postedValues['member_id'])) {
            $arrDataListener['member_id'] = $posted_values['member_id'];
        }

        if (empty($posted_values['member_id']) && empty($posted_values['unit_id']) && empty($posted_values['received_from'])) {
            $posted_values['bill_type'] = 'suspense';
        } else {
            $posted_values['bill_type'] = 'common_bill';
            $arrDataListener['unit_id'] = $posted_values['unit_id'];

            $unpaidInvoiceDetail = $this->getIncidentInvoiceUnpaidBill($arrDataListener);
            $arrIncidentPaymentInvoices = $this->getIncidentPaymentInvoices(array('soc_id' => $posted_values['soc_id'], 'postData' => $posted_values, 'arrUnpaidInvoices' => $unpaidInvoiceDetail));
            
            // Get member detail
            $arrIncomeInvoiceMemberDetail = $this->getMemberDetail($arrDataListener); //get all Unit details

            // Get unit details
            $arrListnerData = array('soc_id' => $posted_values['soc_id'], 'unit_id' => $posted_values['unit_id']);
            $arrUnitDetails = $this->getUnitDetailById($arrListnerData);

            if (!empty($unpaidInvoiceDetail['total_unpaid_invoice_amount'])) {
                $posted_values['total_unpaid_amount'] = $unpaidInvoiceDetail['total_unpaid_invoice_amount'];
            }

            $posted_values['total_unpaid_amount'] = $posted_values['total_due_amount'];
        }

        if (isset($posted_values['receiptFrom']) && $posted_values['receiptFrom'] == 'suspense') {
            $posted_values['payment_mode'] = $posted_values['payment_mode_hidden'];
        }

        //Do not create bill if financial years accounting closed    
        $posted_values['arrCloseAccountPayment'] = $this->getClosedAccountDetailByDate(array('soc_id' => $posted_values['soc_id'], 'bill_date' => $posted_values['receipt_date']));

        //transaction in case of faliure revert all table entries
        if (!empty($arrIncidentPaymentInvoices['member_paid_invoice'])) {
            $posted_values['member_paid_invoice'] = $posted_values['invoice_number'] = trim($arrIncidentPaymentInvoices['member_paid_invoice'], ',');
        } else {
            $posted_values['member_paid_invoice'] = $posted_values['invoice_number'] = '';
        }

        //get member detail
        if (!empty($posted_values['received_from'])) {
            $arrMemberName = explode('(', $posted_values['received_from']);
            $posted_values['member_name'] = trim($arrMemberName[0], ' ');
        } else {
            $arrMemberName = $this->getPrimaryMemberById(array('soc_id' => $posted_values['soc_id'], 'member_id' => $posted_values['id']));
            $posted_values['member_name'] = $arrMemberName['member_first_name'] . ' ' . $arrMemberName['member_last_name'];
        }

        if (empty($posted_values['payment_tracker_id'])) 
        {
            //Generate receipt number
            $posted_values['receipt_number'] = $this->generate_receipt_id($posted_values);
            $paymentToken = $posted_values['payment_token'] = $this->generatePaymentToken($posted_values);
            $arrResponseTracker = $this->saveInvoicePaymentTracker(array('soc_id' => $posted_values['soc_id'], 'unit_id' => $posted_values['unit_id'], 'postData' => $posted_values));
            $posted_values['payment_tracker_id'] = $arrResponseTracker['payment_tracker_id'];
            $isTokenValid = $this->paymentTokenVerification(array('soc_id' => $posted_values['soc_id'], 'paymentToken' => $paymentToken));
        } else {
            $paymentTracker = IncomeInvoicePaymentTracker::where('id = ' . $posted_values['payment_tracker_id'])->first()->toArray();
            $posted_values['payment_token'] = $paymentTracker['payment_token'];
            $posted_values['receipt_number'] = $paymentTracker['receipt_number'];
            $arrResponseTracker['status'] = 'success';
            $arrResponseTracker['payment_tracker_id'] = $posted_values['payment_tracker_id'];
            //Update daate for ledger reference
            if (!empty($paymentTracker['bill_type']) && $paymentTracker['bill_type'] == 'suspense') {
                $otherInformation = unserialize($paymentTracker['other_information']);
                $otherInformation['suspense_detail']['transaction_date'] = $posted_values['receipt_date'];
                $updateOtherInformation = serialize($otherInformation);
            }

            $isTokenValid = true;
        }

        if ($isTokenValid) {
            if (in_array(strtolower($posted_values['payment_mode']), $this->constants['payment_mode_for_clearance'])) {

                if ($posted_values['receiptFrom'] == 'suspense') {

                    $arrResponse = $this->saveMultiCommonBillPayments(array('soc_id' => $posted_values['soc_id'], 'postData' => $posted_values, 'arrUnpaidInvoicesDetail' => $arrIncidentPaymentInvoices['invoice_payment_detail']));

                    if (!empty($arrResponse) && strtolower($arrResponse['status']) == 'success') {
                        $arrUnitDetail = $this->getUnitDetailById(array('soc_id' => $posted_values['soc_id'], 'unit_id' => $arrDataListener['unit_id']));
                        $countLedgerEntry = $this->payCommonBillLedger(array('soc_id' => $posted_values['soc_id'], 'arrPostData' => $posted_values, 'arrCommonBillDetail' => $arrUnitDetail));                                    
                        if ($countLedgerEntry == 0) {
                            $arrResponse['status'] = 'success';
                        }
                    }
                } else {
                    $arrResponse = $arrResponseTracker;
                }
            } else {
                if (!empty($arrResponseTracker) && strtolower($arrResponseTracker['status']) == 'success') {

                    if ($posted_values['bill_type'] == 'suspense') {
                        $arrResponse = $this->suspenseLedgerEntry(array('soc_id' => $posted_values['soc_id'], 'arrData' => $posted_values));
                    } else {
                        $arrResponse = $this->saveMultiCommonBillPayments(array('soc_id' => $posted_values['soc_id'], 'postData' => $posted_values, 'arrUnpaidInvoicesDetail' => $arrIncidentPaymentInvoices['invoice_payment_detail'])); //get all Unit details

                        if (!empty($arrResponse) && strtolower($arrResponse['status']) == 'success') {
                            $arrUnitDetail = $this->getUnitDetailById(array('soc_id' => $posted_values['soc_id'], 'unit_id' => $arrDataListener['unit_id']));
                            $countLedgerEntry = $this->payCommonBillLedger(array('soc_id' => $posted_values['soc_id'], 'arrPostData' => $posted_values, 'arrCommonBillDetail' => $arrUnitDetail)); //get all Unit details                                    
                            if ($countLedgerEntry == 0) {
                                $arrResponse['status'] = 'success';
                            }
                        }
                    }
                }
            }
        } else {
            if (!empty($arrResponseTracker['payment_tracker_id'])) {
                $this->updateIncomePaymentTrackerStatus(array('soc_id' => $posted_values['soc_id'], 'updated_by' => $posted_values['user_id'], 'payment_tracker_id' => $arrResponseTracker['payment_tracker_id'], 'status' => 'N', 'transaction_status' => 'complete'));
            }
            $this->status = 'error';
            $this->message = 'Unable to complete transaction, Please try later.';
            $this->statusCode = 400;
        }

        if (!empty($arrResponse) && strtolower($arrResponse['status']) == 'success') {
            $trackertStatus = 'Y';

            if (in_array(strtolower($posted_values['payment_mode']), $this->constants['payment_mode_for_clearance'])) {
                $trackertStatus = 'P';

                if (strtolower($posted_values['payment_mode']) == 'cheque' && $this->getDatabaseDate($posted_values['payment_date']) > $this->getCurrentDate('database')) {
                    $trackertStatus = 'R';
                }
            }

            // Update payment tracker status
            if (!empty($arrResponseTracker['payment_tracker_id'])) {

                $arrUpdatePaymentTrackerDetail = array('soc_id' => $posted_values['soc_id'], 'updated_by' => $posted_values['user_id'], 'payment_tracker_id' => $arrResponseTracker['payment_tracker_id'], 'status' => $trackertStatus, 'transaction_status' => 'complete');
                if ($posted_values['receiptFrom'] == 'suspense') {
                    $arrUpdatePaymentTrackerDetail['unit_id'] = $posted_values['unit_id'];
                    $arrUpdatePaymentTrackerDetail['invoice_number'] = $posted_values['member_paid_invoice'];
                    $arrUpdatePaymentTrackerDetail['bill_type'] = $posted_values['bill_type'];
                    $arrUpdatePaymentTrackerDetail['total_unpaid_amount'] = $posted_values['total_unpaid_amount'];
                    $arrUpdatePaymentTrackerDetail['other_information'] = $updateOtherInformation;
                    unset($arrUpdatePaymentTrackerDetail['status']);
                }
                $this->updateIncomePaymentTrackerStatus($arrUpdatePaymentTrackerDetail);
            }

            if ($posted_values['bill_type'] == 'suspense') {
                $this->status = 'success';
                $this->message = 'Suspense account receipt has added successfully.';
                $this->statusCode = 200;
            } else {
                $this->status = 'success';
                $this->message = 'Payment is completed successfully.';
                $this->statusCode = 200;
            }
        } else {
            $this->status = 'error';
            $this->message = 'Unable to complete transaction, Please try later.';
            $this->statusCode = 400;
        }

        // if transaction failed then update the status of payment tracker
        if (empty($arrResponse) || (!empty($arrResponse) && strtolower($arrResponse['status']) != 'success')) {
            if (!empty($arrResponseTracker['payment_tracker_id'])) {
                $this->updateIncomePaymentTrackerStatus(array('soc_id' => $posted_values['soc_id'], 'updated_by' => $posted_values['user_id'], 'payment_tracker_id' => $arrResponseTracker['payment_tracker_id'], 'status' => 'N', 'transaction_status' => 'complete'));
            }
        }

        // send notification



    }

    public function generate_receipt_id($data) 
    {
        $settingDetails = IncomeInvoiceGeneralSetting::where('soc_id', $data['soc_id'])->where('setting_key', 'INCOME_RECEIPT_PREFIX')->first();

        // get last receipt number
        $last_receipt_number = $this->tenantDB()->table('income_invoice_payment_tracker')->orderBy('id', 'desc')->first();
        if($last_receipt_number && $last_receipt_number->receipt_number) {
            $numericPart = (int)preg_replace('/\D/', '', $last_receipt_number->receipt_number);
            // Increment the numeric part
            $newNumericPart = $numericPart + 1;
            // Format the new invoice number
            $newReceiptNumber = $settingDetails->setting_value . str_pad($newNumericPart, 5, '0', STR_PAD_LEFT);
        } else {
            $newReceiptNumber = $settingDetails->setting_value . '00001';
        }
        
        return $newReceiptNumber;

    }

    public function generatePaymentToken($data) 
    {
        $paymentToken = '';
        if (!empty($data)) {
            $arrPaymentDetail['soc_id'] = $data['soc_id'];
            $arrPaymentDetail['received_from'] = $data['received_from'];
            $arrPaymentDetail['bill_type'] = 'common_bill';
            $arrPaymentDetail['payment_amount'] = $data['invoice_amount'];
            $arrPaymentDetail['payment_mode'] = $data['receipt_mode'];
            $arrPaymentDetail['invoice_number'] = $data['invoice_number'];
            $arrPaymentDetail['payment_date'] = $data['receipt_date'];
            if (!empty($arrPaymentDetail)) {
                $paymentToken = md5(time() . serialize($arrPaymentDetail));
            }
            return $paymentToken;
        }
    }

    public function getBankCashAccountDetail($data = array())
    {
        $arrAccountDetail = array();

        $query = $this->tenantDB()->table('chsone_grp_ledger_tree as grpLedger')
        ->select(
            'grpLedger.ledger_account_id',
            'grpLedger.ledger_account_name',
            'grpLedger.context',
            'account.default_account',
            'account.default_bank_for_incidental',
            'account.default_bank_for_nonmember',
            'account.bank_name',
            'account.account_number',
            'account.bank_address',
            'account.bank_city',
            'account.bank_ifsc',
            'account.account_id'
        )
        ->join('chsone_accounts_master as account', 'grpLedger.ledger_account_id', '=', 'account.ledger_account_id')
        ->where('grpLedger.soc_id', $data['soc_id'])
        ->where('grpLedger.entity_type', 'ledger')
        ->where('grpLedger.status', 1);


        if (isset($data['only_bank'])) {
            $query->where('grpLedger.context', 'bank');
        } else {
            $query->whereIn('grpLedger.context', ['cash', 'bank']);
        }

        // Execute the query
        $resultset = $query->get();
        $arrAccountDetail = json_decode(json_encode($resultset), true);
        return $arrAccountDetail;
    }

    public function getLedgerAccountDetail($data = array())
    {
        $arrAccountDetail = array(
            'cash' => array(),
            'bank' => array(),
            'arrBank' => array()
        );
        if (!empty($data['account_detail'])) {
            foreach ($data['account_detail'] as $eachAccountDetail) {
                if (empty($arrAccountDetail['cash']) && strtolower($eachAccountDetail['context']) == 'cash') {
                    $arrAccountDetail['cash']['ledger_id'] = $eachAccountDetail['ledger_account_id'];
                    $arrAccountDetail['cash']['ledger_name'] = $eachAccountDetail['ledger_account_name'];
                    $arrAccountDetail['arrCash'][$eachAccountDetail['ledger_account_id']] = $eachAccountDetail['ledger_account_name'];
                } elseif (strtolower($eachAccountDetail['context']) == 'bank') {
                    if ($eachAccountDetail['default_account'] == 1) {
                        $arrAccountDetail['bank']['ledger_id'] = $eachAccountDetail['ledger_account_id'];
                        $arrAccountDetail['bank']['ledger_name'] = $eachAccountDetail['ledger_account_name'];
                    }
                    $arrAccountDetail['arrBank'][$eachAccountDetail['ledger_account_id']] = $eachAccountDetail['ledger_account_name'];
                }
                if (isset($data['default_bank_incidental']) && $data['default_bank_incidental'] && $eachAccountDetail['default_bank_for_incidental'] == 1) {
                    $arrAccountDetail['bank']['ledger_id'] = $eachAccountDetail['ledger_account_id'];
                    $arrAccountDetail['bank']['ledger_name'] = $eachAccountDetail['ledger_account_name'];
                }
                if (isset($data['default_bank_nonmember']) && $data['default_bank_nonmember'] && $eachAccountDetail['default_bank_for_nonmember'] == 1) {
                    $arrAccountDetail['bank']['ledger_id'] = $eachAccountDetail['ledger_account_id'];
                    $arrAccountDetail['bank']['ledger_name'] = $eachAccountDetail['ledger_account_name'];
                }
            }
            if (empty($arrAccountDetail['bank'])) {
                foreach ($arrAccountDetail['arrBank'] as $key => $value) {
                    $arrAccountDetail['bank']['ledger_id'] = $key;
                    $arrAccountDetail['bank']['ledger_name'] = $value;
                    break;
                }
            }
        }

        return $arrAccountDetail;
    }

    public function getIncidentInvoiceUnpaidBill($data = array())
    {
        $arrBillingDetail = $arrIncidentBillDetail = [];

        if (!empty($data['unit_id']) && !empty($data['soc_id'])) {
            $objBillingDetail = $this->tenantDB()->table('income_common_billing_charges as ch')
                ->select([
                    'ch.id', 'ch.soc_id', 'ch.fk_member_id', 'ch.invoice_number', 'ch.fk_unit_id', 'ch.billing_type', 
                    'ch.from_date', 'ch.to_date', 'ch.bill_date', 'ch.payment_mode', 'ch.advance_amount', 
                    'ch.amount', 'ch.transaction_charges', 'ch.discount_amount', 'ch.payment_instrument', 
                    'ch.payment_status', 'ch.status', 'ch.bill_from', 'ch.created_date', 'ch.created_by', 
                    'ch.updated_date', 'ch.updated_by', 'ch.due_date',
                    'ui.principal_amount', 'ui.interest_amount', 'ui.outstanding_principal', 
                    'ui.outstanding_interest', 'ui.roundoff_amount'
                ])
                ->leftJoin('income_unit_invoices as ui', 'ch.invoice_number', '=', 'ui.invoice_number')
                ->where('ch.soc_id', $data['soc_id'])
                ->where('ch.fk_unit_id', $data['unit_id'])
                ->whereIn('ch.payment_status', ['partialpaid', 'unpaid'])
                ->orderBy('ch.id')
                ->get();

            if (!empty($objBillingDetail)) {
                $arrBillingDetail = $objBillingDetail->toArray();

                if (!empty($arrBillingDetail)) {
                    $i = 0;
                    $arrIncidentBillDetail['total_unpaid_invoice_amount'] = 0;

                    foreach ($arrBillingDetail as $eachIncidentBill) {
                        $eachIncidentBill = (array) $eachIncidentBill;
                        $arrIncidentBillDetail[$i] = $eachIncidentBill;

                        // initialize due amount
                        $arrIncidentBillDetail[$i]['due_amount'] = 0;
                        // Get tax amount
                        $taxAmount = $this->getInvoiceTaxAmount(array('soc_id' => $data['soc_id'],'invoice_number' => $eachIncidentBill['invoice_number']));

                        $arrIncidentBillDetail[$i]['due_amount'] += $taxAmount;
                        $arrIncidentBillDetail[$i]['due_amount'] += (float) round(($eachIncidentBill['amount'] + $eachIncidentBill['transaction_charges'] + $eachIncidentBill['interest_amount'] + $eachIncidentBill['roundoff_amount']) - ($eachIncidentBill['advance_amount'] + $eachIncidentBill['discount_amount']), 3);
                        if (!empty($eachIncidentBill['payment_status']) && strtolower($eachIncidentBill['payment_status']) != 'paid') {
                            $arrIncidentBillDetail[$i]['payment_detail'] = $this->getCommonBillingInvoicePayments(array('soc_id' => $data['soc_id'], 'unit_id' => $data['unit_id'], 'common_bill_id' => $eachIncidentBill['id']));
                            if (!empty($arrIncidentBillDetail[$i]['payment_detail'])) {
                                $arrIncidentBillDetail[$i]['due_amount'] = (float) round($arrIncidentBillDetail[$i]['due_amount'] - ($arrIncidentBillDetail[$i]['payment_detail']['payment_amount'] + $arrIncidentBillDetail[$i]['payment_detail']['tds_deducted']), 3);
                            }
                        } else {
                            $arrIncidentBillDetail[$i]['due_amount'] = 0;
                        }

                        if ($arrIncidentBillDetail[$i]['due_amount'] < 0) {
                            $arrIncidentBillDetail[$i]['due_amount'] = 0;
                        }

                        $arrIncidentBillDetail['total_unpaid_invoice_amount'] += $arrIncidentBillDetail[$i]['due_amount'];

                        $arrIncidentBillDetail['invoice_detail'][$i] = $arrIncidentBillDetail[$i];
                        unset($arrIncidentBillDetail[$i]);
                        $i++;
                    }
                }
            }
        }

        return $arrIncidentBillDetail;
    }

    public function getInvoiceTaxAmount($data = array())
    {
        $taxAmount = 0;

        $arrTaxClass = $this->getAppliedTaxDetail($data);
        if (!empty($arrTaxClass)) {
            foreach ($arrTaxClass as $eachTaxLog) {
                $taxAmount += $eachTaxLog['tax_amount'];
            }
        }

        return $taxAmount;
    }

    public function getAppliedTaxDetail($data = array())
    {
        $arrTaxClass = array();
        $query = ChsoneTaxLog::where('soc_id', $data['soc_id'])
        ->whereIn('invoice_number', [$data['invoice_number']]);

        if (empty($data['showCancelledInvoice'])) {
            $query->where('status', '!=', 'cancelled');
        }

        $objTaxClass = $query->get();;

        if (!empty($objTaxClass)) {
            $arrTaxClass = $objTaxClass->toArray();
        }
        return $arrTaxClass;
    }

    public function getCommonBillingInvoicePayments($data = array())
    {
        $arrCommonBillingCharges = array();

        $result = IncomeCommonBillingPayment::where('soc_id', $data['soc_id'])
            ->where('fk_common_bill_id', $data['common_bill_id'])
            ->where('unit_id', $data['unit_id'])
            ->selectRaw('
                SUM(payment_amount) as payment_amount,
                SUM(tds_deducted) as tds_deducted,
                SUM(transaction_charges) as transaction_charges,
                SUM(discount_amount) as discount_amount
            ')
            ->first();

        $result = json_decode(json_encode($result), true);

        if (!empty($result)) {
            $arrCommonBillingCharges = $result;
        }
        return $arrCommonBillingCharges;
    }

    public function getIncidentPaymentInvoices($data = array())
    {
        $arrIncidentPaymentDetail = array();
        if (!empty($data['postData']) && !empty($data['arrUnpaidInvoices'])) {
            $arrUnpaidInvoices = $data['arrUnpaidInvoices'];
            $postData = $data['postData'];
            $paidAmount = $postData['invoice_amount'];
            if (!empty($arrUnpaidInvoices['invoice_detail'])) {
                $arrTdsDetail = array();
                if (!empty($postData['tds_amount'])) {
                    $tds_percentage = (float) round(($postData['tds_amount'] * 100) / $postData['invoice_amount'], 3);
                }
                $i = 0;
                $arrIncidentPaymentDetail['member_paid_invoice'] = '';
                foreach ($arrUnpaidInvoices['invoice_detail'] as $eachInvoiceDetail) {
                    if (!empty($paidAmount) && $paidAmount > 0) {
                        $arrIncidentPaymentDetail[$i] = $eachInvoiceDetail;
                        $arrIncidentPaymentDetail['member_paid_invoice'] .= $eachInvoiceDetail['invoice_number'] . ',';

                        if ($paidAmount > $eachInvoiceDetail['due_amount']) {
                            $arrIncidentPaymentDetail[$i]['payment_status'] = 'paid';
                            $arrIncidentPaymentDetail[$i]['payment_amount'] = $eachInvoiceDetail['due_amount'];
                            $paidAmount = (float) round($paidAmount - $eachInvoiceDetail['due_amount'], 3);
                        } elseif ($paidAmount < $eachInvoiceDetail['due_amount']) {
                            $arrIncidentPaymentDetail[$i]['payment_status'] = 'partialpaid';
                            $arrIncidentPaymentDetail[$i]['payment_amount'] = $paidAmount;
                            $paidAmount = 0;
                        } elseif ($paidAmount == $eachInvoiceDetail['due_amount']) {
                            $arrIncidentPaymentDetail[$i]['payment_status'] = 'paid';
                            $arrIncidentPaymentDetail[$i]['payment_amount'] = $eachInvoiceDetail['due_amount'];
                            $paidAmount = 0;
                        }

                        $arrIncidentPaymentDetail[$i]['tds_amount'] = 0;
                        if (!empty($tds_percentage)) {
                            $arrIncidentPaymentDetail[$i]['tds_amount'] = (float) round(($tds_percentage * $arrIncidentPaymentDetail[$i]['payment_amount']) / 100, 3);
                        }

                        $arrIncidentPaymentDetail['invoice_payment_detail'][$i] = $arrIncidentPaymentDetail[$i];
                        //Add advance amount     

                        // initialize paid_advance_amount if it doesn't exist
                        if (!isset($arrIncidentPaymentDetail['invoice_payment_detail'][$i]['paid_advance_amount'])) {
                            $arrIncidentPaymentDetail['invoice_payment_detail'][$i]['paid_advance_amount'] = 0;
                        }
                        if (count($arrUnpaidInvoices['invoice_detail']) == ($i + 1)) {
                            $arrIncidentPaymentDetail['invoice_payment_detail'][$i]['paid_advance_amount'] += $paidAmount;
                        } else {
                            $arrIncidentPaymentDetail['invoice_payment_detail'][$i]['paid_advance_amount'] = 0;
                        }

                        unset($arrIncidentPaymentDetail[$i]);
                    }
                    $i++;
                }
                $arrIncidentPaymentDetail['member_paid_invoice'] = trim($arrIncidentPaymentDetail['member_paid_invoice'], ',');
            }
        } elseif (!empty($data['postData']) && $data['postData']['total_unpaid_amount'] == 0 && !empty($data['postData']['unit_id'])) {
            $arrIncidentPaymentDetail['invoice_payment_detail'][0]['id'] = 0;
            $arrIncidentPaymentDetail['invoice_payment_detail'][0]['fk_unit_id'] = $data['postData']['unit_id'];
            $arrIncidentPaymentDetail['invoice_payment_detail'][0]['tds_amount'] = $data['postData']['tds_amount'];
            $arrIncidentPaymentDetail['invoice_payment_detail'][0]['paid_advance_amount'] = $data['postData']['invoice_amount'];
            $arrIncidentPaymentDetail['invoice_payment_detail'][0]['payment_amount'] = 0;
        }
        return $arrIncidentPaymentDetail;
    }

    public function getMemberDetail($data = array())
    {
        $arrMemberMaster = [];
        $id = ChsoneMemberTypeMaster::where('soc_id', $data['soc_id'])
            ->where('member_type_name', 'Primary')
            ->value('member_type_id');

        $objMemberMaster = ChsoneMembersMaster::where('soc_id', $data['soc_id'])
            ->where('fk_unit_id', $data['unit_id'])
            ->where('status', 1)
            ->where('member_type_id', $id)
            ->first();

        if ($objMemberMaster) {
            $arrMemberMaster = $objMemberMaster->toArray();
        }

        return $arrMemberMaster;
    }

    public function getUnitDetailById(array $data = array())
    {
        $soc_id = $data['soc_id'];
        $unit_id = $data['unit_id'];
        $objUnitdetails = ChsoneUnitsMaster::where('soc_id', $soc_id)->where('unit_id', $unit_id)->first();
        if (!empty($objUnitdetails)) {
            $arrUnitDetails = $objUnitdetails->toArray();
        }
        return $arrUnitDetails;
    }

    public function getClosedAccountDetailByDate($data=array())
    {
        $arrAccountMasterDetail = [];

        $billDate = !empty($data['bill_date']) ? $data['bill_date'] : date('Y-m-d');

        $objAccountMasterDetail = SocAccountFinancialYearMaster::where('soc_id', $data['soc_id'])
            ->where('closed', 1)
            ->where('confirmed', 1)
            // ->whereBetween($billDate, ['fy_start_date', 'fy_end_date'])
            ->where('fy_start_date', '<=', $billDate)
            ->where('fy_end_date', '>=', $billDate)
            ->first();

        if (!empty($objAccountMasterDetail)) {
            $arrAccountMasterDetail = $objAccountMasterDetail->toArray();
        }

        return $arrAccountMasterDetail;
    }

    public function getPrimaryMemberById($data = array())
    {
        $resultset = $this->tenantDB()->table('chsone_members_master as memberMaster')
        ->select([
            'memberMaster.id',
            'memberMaster.member_first_name',
            'memberMaster.member_last_name'
        ])
        ->leftJoin('chsone_member_type_master as memberType', 'memberMaster.member_type_id', '=', 'memberType.member_type_id')
        ->where('memberMaster.soc_id', $data['soc_id'])
        ->where('memberMaster.id', $data['member_id'])
        ->where('memberType.member_type_name', 'Primary')
        ->where('memberMaster.status', 1)
        ->first();

        $resultset = json_decode(json_encode($resultset), true);

        return $resultset;
    }

    public function saveInvoicePaymentTracker($data = array())
    {
        $arrResponse = array(
            'status' => 'error',
            'message' => array()
        );

        if (isset($data) && !empty($data['soc_id']) && !empty($data['postData'])) {

            $arrOtherInfo = array();
            if (isset($data['postData']) && !empty($data['postData'])) {

                if (strtolower($data['postData']['bill_type']) == 'common_bill') {
                    $arrOtherInfo['common_bill_detail']['common_bill_id'] = $data['postData']['common_bill_id'] ?? '';
                    $arrOtherInfo['common_bill_detail']['bank_ledger'] = $data['postData']['bank_account'] ?? '';
                    $arrOtherInfo['common_bill_detail']['bill_type_category'] = $data['postData']['bill_type_category'] ?? "";
                    $arrOtherInfo['common_bill_detail']['payment_type'] = $data['postData']['payment_type'];
                } elseif (strtolower($data['postData']['bill_type']) == 'suspense') {
                    $arrOtherInfo['suspense_detail']['bank_ledger'] = $data['postData']['bank_account'];
                }
            }

            $objIncomeInvoicePaymentTracker = new IncomeInvoicePaymentTracker();
            $objIncomeInvoicePaymentTracker->created_date = date('Y-m-d H:i:s');
            $objIncomeInvoicePaymentTracker->created_by = (!empty($data['postData']['user_id'])) ? $data['postData']['user_id'] : 0;
            $objIncomeInvoicePaymentTracker->soc_id = (!empty($data['soc_id'])) ? $data['soc_id'] : '';
            $objIncomeInvoicePaymentTracker->unit_id = (!empty($data['unit_id'])) ? $data['unit_id'] : '';
            $objIncomeInvoicePaymentTracker->invoice_number = $data['postData']['member_paid_invoice'];
            $objIncomeInvoicePaymentTracker->receipt_number = (!empty($data['postData']['receipt_number'])) ? $data['postData']['receipt_number'] : 0;
            $objIncomeInvoicePaymentTracker->bill_type = $data['postData']['bill_type'];
            $objIncomeInvoicePaymentTracker->payment_mode = trim($data['postData']['payment_mode']);
            if($data['postData']['payment_mode'] == "cheque") {
                $objIncomeInvoicePaymentTracker->transaction_reference = !empty($data['postData']['cheque_number']) ? $data['postData']['cheque_number'] : '';
                $objIncomeInvoicePaymentTracker->payment_instrument = !empty($data['postData']['payment_instrument']) ? $data['postData']['payment_instrument'] : '';
            } elseif ($data['postData']['payment_mode'] == "cashtransfer") {
                $objIncomeInvoicePaymentTracker->transaction_reference = !empty($data['postData']['transaction_reference']) ? $data['postData']['transaction_reference'] : '';
                $objIncomeInvoicePaymentTracker->payment_instrument = '';
            } else {
                $objIncomeInvoicePaymentTracker->transaction_reference = '';
                $objIncomeInvoicePaymentTracker->payment_instrument = '';
            }
            $objIncomeInvoicePaymentTracker->received_from = $data['postData']['received_from'];
            $objIncomeInvoicePaymentTracker->transaction_charges = !empty($data['postData']['transaction_charges']) ? $data['postData']['transaction_charges'] : 0;
            $objIncomeInvoicePaymentTracker->total_due_amount = $data['postData']['total_unpaid_amount'];
            $objIncomeInvoicePaymentTracker->late_payment_charges = !empty($data['postData']['late_payment_charges']) ? $data['postData']['late_payment_charges'] : 0;
            $objIncomeInvoicePaymentTracker->writeoff_amount = !empty($data['postData']['writeoff_amount']) ? $data['postData']['writeoff_amount'] : 0;
            $objIncomeInvoicePaymentTracker->payment_amount = $data['postData']['invoice_amount'];
            $objIncomeInvoicePaymentTracker->other_information = serialize($arrOtherInfo);
            $objIncomeInvoicePaymentTracker->tds_deducted = !empty($data['postData']['tds_amount']) ? $data['postData']['tds_amount'] : 0;
            $objIncomeInvoicePaymentTracker->status = 'N'; // (!empty($data['postData']['payment_mode']) && in_array(strtolower($data['postData']['payment_mode']), $this->constants['payment_mode_for_clearance'] )) ? 'P' : 'Y';
            $objIncomeInvoicePaymentTracker->updated_date = date('Y-m-d H:i:s');
            $objIncomeInvoicePaymentTracker->updated_by = !empty($data['postData']['user_id']) ? $data['postData']['user_id'] : 0;

            $objIncomeInvoicePaymentTracker->payment_date = ((!empty($data['postData']['receipt_date']) && strrchr($data['postData']['receipt_date'], '/')) ? $data['postData']['receipt_date'] : ((!empty($data['postData']['receipt_date'])) ? $data['postData']['receipt_date'] : $this->getCurrentDate('database')));
            $objIncomeInvoicePaymentTracker->payment_note = !empty($data['postData']['payment_note']) ? $data['postData']['payment_note'] : '';

            //Insert cheque date only in case of payment mode CHEQUE
            $objIncomeInvoicePaymentTracker->cheque_date = ((!empty($data['postData']['cheque_date']) && strrchr($data['postData']['cheque_date'], '/')) ? $data['postData']['cheque_date'] : ((!empty($data['postData']['cheque_date'])) ? $data['postData']['cheque_date'] : ''));

            $objIncomeInvoicePaymentTracker->attachment_name = !empty($data['postData']['attachment_name']) ? $data['postData']['attachment_name'] : '';

            $objIncomeInvoicePaymentTracker->payment_token = !empty($data['postData']['payment_token']) ? trim($data['postData']['payment_token']) : '';
            // Update Payment status and Transaction status for facility advance payment
            if (isset($data['postData']['booked_by']) && !empty($data['postData']['booked_by']) && isset($data['postData']['bill_from']) && strtolower($data['postData']['bill_from']) == 'facility') {
                $objIncomeInvoicePaymentTracker->transaction_status = 'complete';
                if (in_array(strtolower($data['postData']['payment_mode']), $this->constants['payment_mode_for_clearance'])) {
                    $objIncomeInvoicePaymentTracker->status = 'P';
                } else {
                    $objIncomeInvoicePaymentTracker->status = 'Y';
                }
            }

            if (!$objIncomeInvoicePaymentTracker->save()) {
                $arrMessage = [];
                foreach ($objIncomeInvoicePaymentTracker->getErrors() as $error) {
                    $arrMessage[] = $error;
                }
                $arrResponse['status'] = 'error';
                $arrResponse['message'] = $arrMessage;
            } else {
                $arrResponse['status'] = 'success';
                $arrResponse['payment_tracker_id'] = $objIncomeInvoicePaymentTracker->id;
            }

            unset($objIncomeInvoicePaymentTracker);
        }
        return $arrResponse;
    }

    public function paymentTokenVerification($data = array())
    {
        if (!empty($data['paymentToken'])) {
            $arrPaymentTrackerDetail = $this->getPaymentTrackerDetailByPaymentToken($data);
            if (!empty($arrPaymentTrackerDetail)) {
                if (in_array(strtolower($arrPaymentTrackerDetail['transaction_status']), array('in_process')) || in_array(strtolower($arrPaymentTrackerDetail['status']), array('y'))) {
                    return false;
                }
            } else {
                return false;
            }
        } else {
            return false;
        }
        return true;
    }

    public function getPaymentTrackerDetailByPaymentToken($data = array())
    {
        $arrIncomeInvoicePaymentTracker = [];

        if (!empty($data['paymentToken'])) {
            $query = [
                ['soc_id', '=', $data['soc_id']],
                ['payment_token', '=', $data['paymentToken']]
            ];

            if (!empty($data['type']) && $data['type'] == 'expense') {
                $objIncomeInvoicePaymentTracker = ExpenseInvoicePaymentTracker::where($query)->first();
            } else {
                $objIncomeInvoicePaymentTracker = IncomeInvoicePaymentTracker::where($query)->first();
            }

            if (!empty($objIncomeInvoicePaymentTracker)) {
                $arrIncomeInvoicePaymentTracker = $objIncomeInvoicePaymentTracker->toArray();
            }
        }

        return $arrIncomeInvoicePaymentTracker;
    }

    public function saveMultiCommonBillPayments($data = array())
    {

        $finalArray = array('status' => 'error');
        if (!empty($data['postData']) && !empty($data['arrUnpaidInvoicesDetail'])) {
            $arrPostData = $data['postData'];
            foreach ($data['arrUnpaidInvoicesDetail'] as $eachInvoiceDetail) {
                $objCommonBillingPayment = new IncomeCommonBillingPayment;
                $objCommonBillingPayment->created_date = date('Y-m-d');
                if (!empty($arrPostData['user_id'])) {
                    $objCommonBillingPayment->created_by = $arrPostData['user_id'];
                }

                $objCommonBillingPayment->soc_id = $data['soc_id'];
                $objCommonBillingPayment->fk_common_bill_id = $eachInvoiceDetail['id'];
                $objCommonBillingPayment->unit_id = $eachInvoiceDetail['fk_unit_id'];

                if (isset($eachInvoiceDetail['paid_advance_amount']) && !empty(isset($eachInvoiceDetail['paid_advance_amount']))) {
                    $eachInvoiceDetail['payment_amount'] += $eachInvoiceDetail['paid_advance_amount'];
                }

                if (isset($eachInvoiceDetail['tds_amount']) && !empty($eachInvoiceDetail['tds_amount']) && $eachInvoiceDetail['tds_amount'] > 0) {
                    $objCommonBillingPayment->tds_deducted = $eachInvoiceDetail['tds_amount'] ?? 0;
                    $eachInvoiceDetail['payment_amount'] = (float) round($eachInvoiceDetail['payment_amount'] - $eachInvoiceDetail['tds_amount'], 3);
                } else {
                    $objCommonBillingPayment->tds_deducted = 0;
                }

                if (isset($eachInvoiceDetail['payment_amount']) && !empty($eachInvoiceDetail['payment_amount'])) {
                    $objCommonBillingPayment->payment_amount = $eachInvoiceDetail['payment_amount'];
                }
                if (isset($arrPostData['payment_mode']) && !empty($arrPostData['payment_mode'])) {
                    $objCommonBillingPayment->payment_mode = $arrPostData['payment_mode'];
                }
                if (isset($arrPostData['transaction_reference']) && !empty($arrPostData['transaction_reference'])) {
                    $objCommonBillingPayment->transaction_reference = $arrPostData['transaction_reference'] ?? '';
                } else {
                    $objCommonBillingPayment->transaction_reference = '';
                }
                if (isset($arrPostData['transaction_charges']) && !empty($arrPostData['transaction_charges'])) {
                    $objCommonBillingPayment->transaction_charges = $arrPostData['transaction_charges'] ?? 0;
                } else {
                    $objCommonBillingPayment->transaction_charges = 0;
                }
                if (isset($arrPostData['discount_amount']) && !empty($arrPostData['discount_amount'])) {
                    $objCommonBillingPayment->discount_amount = $arrPostData['discount_amount'] ?? 0;
                } else {
                    $objCommonBillingPayment->discount_amount = 0;
                }
                if (isset($arrPostData['payment_instrument']) && !empty($arrPostData['payment_instrument'])) {
                    $objCommonBillingPayment->payment_instrument = $arrPostData['payment_instrument'] ?? '';
                } else {
                    $objCommonBillingPayment->payment_instrument = '';
                }

                $objCommonBillingPayment->payment_date = !empty($arrPostData['receipt_date']) ? $arrPostData['receipt_date'] : date('Y-m-d');

                //$finalArray = array();
                if (!$objCommonBillingPayment->save()) {
                    $arrMessage = [];
                    foreach ($objCommonBillingPayment->getMessages() as $messages) {
                        $arrMessage[] = (string) $messages;
                    }

                    $finalArray['status'] = 'error';
                    $finalArray['arrMessage'] = $arrMessage;
                } else {
                    //Update Incident bill status
                    if (isset($eachInvoiceDetail['payment_status']) && !empty($eachInvoiceDetail['payment_status']) && !empty($eachInvoiceDetail['id'])) {
                        $objCommonBillingCharges = IncomeCommonBillingCharge::where('soc_id', $data['soc_id'])
                            ->where('id', $eachInvoiceDetail['id'])
                            ->where('fk_unit_id', $eachInvoiceDetail['fk_unit_id'])
                            ->first();
                        if (!empty($objCommonBillingCharges)) {

                            $objCommonBillingCharges->payment_status = $eachInvoiceDetail['payment_status'];
                            $objCommonBillingCharges->updated_date = $this->getCurrentDate('database');
                            $objCommonBillingCharges->updated_by = $arrPostData['user_id'];
                            if (!$objCommonBillingCharges->save()) {
                                $singleMsg = '|';
                                foreach ($objCommonBillingCharges->getMessages() as $messages) {
                                    $arrMessages[] = (string) $messages;
                                    $singleMsg .= (string) $messages . '|';
                                }
                                $finalArray['status'] = 'error';
                                $finalArray['arrMessage'] .= $singleMsg;
                                return $finalArray;
                            } else {
                                $finalArray['status'] = 'success';
                                $finalArray['common_bill_id'] = $objCommonBillingPayment->payment_id;
                            }
                        }
                    }

                    //Update advance amount in unit adjustment table
                    if (isset($eachInvoiceDetail['paid_advance_amount']) && $eachInvoiceDetail['paid_advance_amount'] > 0) {
                        // subtract tds-amount only in case of full advance payment through quick pay
                        if (isset($arrPostData['tds_amount']) && !empty($arrPostData['tds_amount']) && $arrPostData['tds_amount'] > 0 && empty($eachInvoiceDetail['id']) && empty($arrPostData['total_unpaid_amount'])) {
                            $eachInvoiceDetail['paid_advance_amount'] = (float) round($eachInvoiceDetail['paid_advance_amount'] - $arrPostData['tds_amount'], 3);
                        }

                        $arrCreditData['soc_id'] = $data['soc_id'];
                        $arrCreditData['account_id'] = $eachInvoiceDetail['fk_unit_id'];
                        $arrCreditData['account_context'] = 'unit';
                        $arrCreditData['transaction_type'] = 'cr';
                        $arrCreditData['context'] = 'system';
                        $arrCreditData['credit_used_type'] = 'adjustable';
                        $arrCreditData['used_for'] = 'incidental';
                        $arrCreditData['payment_date'] = !empty($arrPostData['receipt_date']) ? $arrPostData['receipt_date'] : $this->getCurrentDate('display');
                        $arrCreditData['narration'] = 'Amount Rs ' . $eachInvoiceDetail['paid_advance_amount'] . ' has credited from Advance of ' . $eachInvoiceDetail['invoice_number'];
                        $arrCreditData['payment_amount'] = $eachInvoiceDetail['paid_advance_amount'];
                        $arrCreditData['account_name'] = $arrPostData['member_name'];
                        $arrCreditData['payment_mode'] = $arrPostData['payment_mode'];
                        $arrResponse = $this->saveCreditAccountResponse(array(
                            'process' => 'fetch',
                            'soc_id' => $arrCreditData['soc_id'],
                            'id' => '',
                            'data' => $arrCreditData,
                            'user' => $arrPostData['user_id'],
                            'username' => $arrPostData['user']['first_name'] . ' ' . $arrPostData['user']['last_name'],
                        ));

                        if ($arrResponse['error']) {
                            $finalArray['status'] = 'error';
                            $finalArray['arrMessage'] .= 'unable to create credit note';
                            return $finalArray;
                        } else {
                            $finalArray['status'] = 'success';
                            $finalArray['common_bill_id'] = $objCommonBillingPayment->payment_id;
                        }
                    }
                }
            }
        }
        return $finalArray;
    }

    public function saveCreditAccountResponse($data = array())
    {
        $response = ['error' => false, 'data' => null];

        try {
            if (empty($data)) {
                $this->status = 'error';
                $this->message = 'No data values provided.';
                $this->statusCode = 400;
                return;
            }
            $user = $data['user'];
            $id = $data['id'];
            $username = (!empty($data['username']) ? $data['username'] : null);

            $data = $data['data'];
            $data['username'] = $username;
            $data['user'] = $user;

            if ($id) {
                $cr = ChsoneCreditAccount::where('credit_account_id', $id)
                    ->where('soc_id', $data['soc_id'])
                    ->first();

                $cr->updated_by = $user;
                $cr->updated_date = date('Y-m-d H:i:s');
                $cr->use_credit_after = (!empty($data['adjustable_date']) ? $data['adjustable_date'] : null);
                $cr->is_locked = 0;
                $cr->use_credit_for = (!empty($data['used_for']) ? $data['used_for'] : null);

                $cr->is_invoice_rectification = ((isset($data['is_invoice_rectification']) && !empty($data['is_invoice_rectification'])) ? $data['is_invoice_rectification'] : null);
                $cr->income_account_id = ((isset($data['income_account_ledger_id']) && !empty($data['income_account_ledger_id'])) ? $data['income_account_ledger_id'] : null);
                $cr->narration = $data['narration'];
                $cr->use_credit = (isset($data['credit_used_type']) && !empty($data['credit_used_type']) ? $data['credit_used_type'] : 'adjustable');
                // Save data
                if ($cr->save()) {
                    $response['data'] = $cr->credit_account_id;
                } else {
                    $response['error'] = true;
                    $response['data'] = ['message' => $cr->getErrors()];
                }
            } else {
                if ($data['credit_used_type'] == 'both') {
                    $data['credit_used_type'] = 'adjustable';
                    $data['payment_amount'] = $data['adjustable_amount'];
                    $saveResponse = $this->saveCreditNote($data);
                    if ($saveResponse['error'] == false) {
                        $data['credit_used_type'] = 'refundable';
                        $data['payment_amount'] = $data['refundable_amount'];
                        $data['used_for'] = NULL;
                        $data['adjustable_date'] = NULL;

                        $saveResponse = $this->saveCreditNote($data);
                        if ($saveResponse['error'] == false) {
                            $response['data'] = $saveResponse['id'];
                        }
                    }
                } else {
                    $saveResponse = $this->saveCreditNote($data);
                    if ($saveResponse['error'] == false) {
                        $response['data'] = $saveResponse['id'];
                    }
                }
            }
        } catch (\Exception $e) {
            $response['error'] = true;
            $response['data'] = ['message' => $e->getMessage()];
        } finally {
            if (empty($response['data'])) {
                $response['data'] = ['message' => 'working is ok'];
                $response['error'] = false;
            }
            // return response()->json($response);
            return $response;
        }
    }

    public function payCommonBillLedger($data = array())
    {
        $countLedgerEntry = 0;

        if (ACCOUNT_MODULE_EXIST == 1) {
            $arrPostData = $data['arrPostData'];
            $arrCommonBillDetail = $data['arrCommonBillDetail'];

            if (isset($arrPostData['clearance_date']) && !empty($arrPostData['clearance_date'])) {
                $arrPostData['payment_date'] = $arrPostData['clearance_date'];
            }
            $arrListnerData = array('soc_id' => $data['soc_id']);
            if ($arrCommonBillDetail['ledger_account_id']) {
                $arrListnerData['ledger_id'] = $arrCommonBillDetail['ledger_account_id'];
            } else {
                $arrListnerData['ledger_name'] = 'BLDG#' . strtoupper($arrCommonBillDetail['soc_building_name']) . '-' . $arrCommonBillDetail['unit_flat_number'];
                $arrListnerData['context'] = ACCOUNT_RECEIVABLE_GROUP;
            }
            $arrUnitLedgerDetails = $this->checkledgerExistNew($arrListnerData);
                
            if (!empty($arrPostData['payment_mode']) && !in_array(strtolower($arrPostData['payment_mode']), $this->constants['payment_mode_for_receipt_reversal'])) {
                //  print_r(array(trim($arrPostData['payment_instrument']) . '_PG_LEDGER_ID'));
                $arrInvoiceGeneralSetting = $this->getInvoiceGeneralSetting(array('soc_id' => $data['soc_id'], 'setting_key' => array(trim($arrPostData['payment_instrument']) . '_PG_LEDGER_ID'))); //get all Unit details
                
                if (!empty($arrInvoiceGeneralSetting[0]['setting_value'])) {
                    $arrIncomeAccounts = $this->checkledgerExistNew(array('soc_id' => $data['soc_id'], 'ledger_id' => $arrInvoiceGeneralSetting[0]['setting_value']));
                    $arrIncomeAccounts['ledger_id'] = $arrIncomeAccounts['recieving_ledger_id'];
                    $arrIncomeAccounts['ledger_name'] = $arrIncomeAccounts['receiver_name'];
                } else {
                    if (!empty($data['to_ledger'])) {
                        $arrIncomeAccounts['ledger_id'] = $data['to_ledger']['recieving_ledger_id'];
                        $arrIncomeAccounts['ledger_name'] = $data['to_ledger']['receiver_name'];
                    } else {
                        return 1;
                    }
                }
            } else {
                if (isset($data['arrUnitLedgerDetails']['suspenseLedgerDetails']) && !empty($data['arrUnitLedgerDetails']['suspenseLedgerDetails'])) {
                    $arrIncomeAccounts['ledger_id'] = $data['arrUnitLedgerDetails']['suspenseLedgerDetails']['recieving_ledger_id'];
                    $arrIncomeAccounts['ledger_name'] = $data['arrUnitLedgerDetails']['suspenseLedgerDetails']['receiver_name'];
                } else {
                    // Getting bank/cash ledger details
                    $arrAccountDetail = $this->getBankCashAccountDetail(array('soc_id' => $data['soc_id'])); //get all Unit details
                    $arrLedgerAccountDetail = $this->getLedgerAccountDetail(array('account_detail' => $arrAccountDetail)); //get all Unit details

                    if (!empty($arrLedgerAccountDetail)) {
                        $arrIncomeAccounts['ledger_id'] = $arrLedgerAccountDetail['cash']['ledger_id'];
                        $arrIncomeAccounts['ledger_name'] = $arrLedgerAccountDetail['cash']['ledger_name'];
                        if (strtolower($arrPostData['payment_mode']) != 'cash') {
                            if (!empty($arrPostData['bank_account'])) {
                                $arrIncomeAccounts['ledger_id'] = $arrPostData['bank_account'];
                                $arrIncomeAccounts['ledger_name'] = $arrLedgerAccountDetail['arrBank'][$arrPostData['bank_account']];
                            } else {
                                $arrIncomeAccounts['ledger_id'] = $arrLedgerAccountDetail['bank']['ledger_id'];
                                $arrIncomeAccounts['ledger_name'] = $arrLedgerAccountDetail['bank']['ledger_name'];
                            }
                        }
                    }
                }
            }

            $arrPostData['payment_note'] = $arrPostData['payment_note'] ?? '';
            if ($arrPostData['payment_mode'] == 'cashtransfer') {
                $paymentModeForNarration = DISPLAY_CASH_TRANSFER;
                $strNarration = '['.$arrPostData['payment_note'].']';
            } elseif ($arrPostData['payment_mode'] == 'cheque') {
                $paymentModeForNarration = $arrPostData['payment_mode'];
                $strNarration = ' with transaction ref. (' . $arrPostData['cheque_number'] . ', ' . $arrPostData['payment_instrument'] . ')['.$arrPostData['payment_note'].']';
            } else {
                $paymentModeForNarration = $arrPostData['payment_mode'];
                $strNarration = '';
            }

            // echo $arrIncomeInvoicePaymentTracker['payment_mode'];exit;
            // Ledger entry for payment amount
            $arrLedgerTransactionData['soc_id'] = $arrPostData['soc_id'];
            $arrLedgerTransactionData['user_id'] = $arrPostData['user_id'];
            $arrLedgerTransactionData['voucher_type'] = VOUCHER_RECEIPT;
            $arrLedgerTransactionData['voucher_reference_number'] = $arrPostData['receipt_number'];
            $arrLedgerTransactionData['voucher_reference_id'] = $arrPostData['payment_tracker_id'];
            $arrLedgerTransactionData['from_ledger_id'] = $arrIncomeAccounts['ledger_id'];
            $arrLedgerTransactionData['to_ledger_id'] = $arrUnitLedgerDetails['recieving_ledger_id'];
            $arrLedgerTransactionData['transaction_date'] = !empty($arrPostData['payment_date']) ? $this->getDatabaseDate($arrPostData['payment_date']) : $this->getCurrentDate('database');
            $arrLedgerTransactionData['transaction_amount'] = $arrPostData['invoice_amount'];
            $arrLedgerTransactionData['narration'] = '(Receipt No-' . $arrPostData['receipt_number'] . ') Amount received from ' . $arrPostData['received_from'] . ' against invoice ' . $arrPostData['invoice_number'] . ' dated ' . $arrPostData['payment_date'] . ' through ' . $paymentModeForNarration . $strNarration;
            $arrLedgerTransactionData['from_ledger_name'] = $arrIncomeAccounts['ledger_name'];
            $arrLedgerTransactionData['to_ledger_name'] = $arrUnitLedgerDetails['receiver_name'];
            if($arrPostData['payment_mode'] == "cheque") {
                $arrLedgerTransactionData['payment_reference'] = $arrPostData['cheque_number'] ?? '';
            } elseif($arrPostData['payment_mode'] == "cashtransfer") {
                $arrLedgerTransactionData['payment_reference'] = $arrPostData['transaction_reference'] ?? '';
            } else {
                $arrLedgerTransactionData['payment_reference'] = '';
            }
            $arrLedgerTransactionData['transaction_type'] = '';
            $arrLedgerTransactionData['mode_of_payment'] = $arrPostData['payment_mode'];
            $arrLedgerTransactionData['other_payment_ref'] = '';
            if (!empty($arrPostData['payment_note'])) {
                $arrLedgerTransactionData['narration'] .= ' [' . $arrPostData['payment_note'] . ']';
            }

            //echo 'pay'; print_r($arrLedgerTransactionData); exit();
            if (!empty($arrPostData['tds_amount']) && $arrPostData['tds_amount'] > 0) {
                $arrLedgerTransactionData['transaction_amount'] = round($arrPostData['invoice_amount'] - $arrPostData['tds_amount'], 2);
                $arrLedgerEntry = $this->transactionLedgerEntry($arrLedgerTransactionData);
                if (isset($arrLedgerEntry['error']) && !empty($arrLedgerEntry['error'])) {
                    $countLedgerEntry++;
                } else {
                    $arrLedgerTransactionData['transaction_amount'] = $arrPostData['tds_amount'];
                    $arrListnerData['soc_id'] = $arrPostData['soc_id'];
                    $arrListnerData['ledger_name'] = 'tds receivable';
                    $arrListnerData['context'] = ''; // CURRENT_ASSETS_GROUP;
                    $arrListnerData['behaviour'] = ASSET;

                    $arrTdsLedger = $this->checkledgerExistNew($arrListnerData);
                    $arrLedgerTransactionData['from_ledger_id'] = $arrTdsLedger['recieving_ledger_id'];
                    $arrLedgerTransactionData['from_ledger_name'] = $arrTdsLedger['receiver_name'];
                    $arrLedgerTransactionData['narration'] = 'TDS amount deducted against Invoice ' . $arrPostData['member_paid_invoice'] . ' dated ' . $arrPostData['payment_date'] . ' through ' . $paymentModeForNarration . $strNarration; // 'Invoice '.$arrPostData['member_paid_invoice'].' '.$eachLedgerDetail['particular'].' Write Off';

                    $arrLedgerEntry = $this->transactionLedgerEntry($arrLedgerTransactionData);
                    if (isset($arrLedgerEntry['error']) && !empty($arrLedgerEntry['error'])) {
                        $countLedgerEntry++;
                    }
                }
            } else {
                //echo 'led';
                $arrLedgerEntry = $this->transactionLedgerEntry($arrLedgerTransactionData);
                // print_r($arrLedgerEntry); exit();
                if (isset($arrLedgerEntry['error']) && !empty($arrLedgerEntry['error'])) {
                    $countLedgerEntry++;
                }
            }
        }
        return $countLedgerEntry;
    }

    public function checkledgerExistNew($data=array())
    {
        $arrClinetLedgerDetails = array();
        $query = ChsoneGrpLedgerTree::where('soc_id', $data['soc_id']);
        
        if (isset($data['ledger_name']) && !empty($data['ledger_name'])) {
            $query->where('context', $data['context'])
                  ->where('entity_type', "ledger")
                  ->where('ledger_account_name', $data['ledger_name']);
    
            if (isset($data['behaviour']) && !empty($data['behaviour'])) {
                $query->where('behaviour', $data['behaviour']);
            }
        } elseif (isset($data['ledger_id']) && !empty($data['ledger_id'])) {
            $query->where('ledger_account_id', $data['ledger_id']);
        }

        $objBookerLedger = $query->first();

        //an outsider has booked for the society. Check if exists by name; otherwise, create new.
        if(!empty($objBookerLedger)) {
            $arrClinetLedgerDetails['recieving_ledger_id'] = $objBookerLedger->ledger_account_id;
            $arrClinetLedgerDetails['receiver_name'] = $objBookerLedger->ledger_account_name;
        }
        elseif(!empty($data['group_name']))
        {
            $name         = $data['ledger_name'];
            $entity_type  = "ledger";
            $grp_ledg_id  = "";
            $parent       = $this->getParentGroupId($data, TRUE);
            
            if(empty($parent->ledger_account_id))
            {
                return $arrClinetLedgerDetails;
            }
            $parent_group = $parent->ledger_account_id;
            
            $ledger_id    = $this->manipulate($name, $entity_type, $grp_ledg_id , $parent_group, "", '', 0, '', '', '', '', '', 0, $data['soc_id']);
            if ($ledger_id) {
                $arrClinetLedgerDetails['recieving_ledger_id'] = $ledger_id;
                $arrClinetLedgerDetails['receiver_name'] = $data['ledger_name'];
            }
        }
        unset($objBookerLedger);
        return $arrClinetLedgerDetails;
    }

    public function getParentGroupId($arrData)
    {
        $parent = ChsoneGrpLedgerTree::where('soc_id', $arrData['soc_id'])
            ->where('ledger_account_name', $arrData['group_name'])
            ->where('entity_type', "group")
            ->where('behaviour', $arrData['behaviour'])
            ->where('context', $arrData['context'])
            ->first();
        if ($parent) {
            return $parent;
        } else {
            return false;
        }
    }

    public function manipulate($name, $entity_type = ENTITY_TYPE_GROUP, $grp_ledg_id = "", $parent_group = 0, $behaviour = "", $ledg_id = '', $opening_balance = 0, $update_led_id = '', $ledger_start_date = '', $ledger_type = '', $context = '', $txn_id = 0, $soc_id = 0, $is_reco = 0)
    {
        $config = Config::get("constants");
        if(empty($soc_id)){
            $soc_id = $this->input['company_id'];
        }
        $dup = $this->_checkGroupNameDuplication($parent_group, $name, $ledg_id, $update_led_id);
        
        $arrFYDetail = $this->getCurrentFinancialYear(array('soc_id' => $soc_id));
        $arrFYDetail = json_decode(json_encode($arrFYDetail), true);
        $ledger_start_date = $arrFYDetail['fy_start_date'];
        if ($dup == 0) {
            $behaviour = trim(strtolower($behaviour));

            $grp_ledg_tree = new ChsoneGrpLedgerTree();

            if ($ledg_id) {
                $grp_ledg_tree = $this->getLedger($ledg_id);
            }

            $grp_ledg_tree->entity_type = $entity_type;
            $grp_ledg_tree->soc_id = $soc_id;
            $grp_ledg_tree->ledger_account_name = $name;
            $grp_ledg_tree->ledger_start_date = $ledger_start_date;
            $grp_ledg_tree->context_ref_id = 0;

            if (!empty($ledger_type) && $ledger_type != "NULL" && $ledger_type != '') {
                $grp_ledg_tree->operating_type = $ledger_type;
            } else {
                $grp_ledg_tree->operating_type = '';
            }

            if (!empty($grp_ledg_id)) {
                $grp_ledg_tree->ledger_account_id = $grp_ledg_id;
            }

            if ($update_led_id != '') {
                $grp_ledg_tree->ledger_account_id = $update_led_id;
            }
            if (!empty($parent_group)) {
                $grp_ledg_tree->parent_id = $parent_group;
                $ledger_props = $this->_getLedgerProps($parent_group);
            } else {
                $grp_ledg_tree->parent_id = 0;
            }

            if (!empty($behaviour)) {
                $grp_ledg_tree->behaviour = $behaviour;
                $grp_ledg_tree->nature_of_account = $config->nature_account->$behaviour;
                $grp_ledg_tree->report_head = $config->report_head->$behaviour;
            } else {
                $grp_ledg_tree->behaviour = $ledger_props["behaviour"];
                $grp_ledg_tree->nature_of_account = $ledger_props["nature_account"];
                $grp_ledg_tree->report_head = $ledger_props["report_head"];
            }

            if (!empty($context) && $context != '') {
                $grp_ledg_tree->context = $context;
            } else {
                $grp_ledg_tree->context = $ledger_props["context"];
            }
            $grp_ledg_tree->defined_by = "user";
            $grp_ledg_tree->status = 1;
            $grp_ledg_tree->added_on = date("Y-m-d H:i:s");
            $grp_ledg_tree->created_by = ($this->input['user_id']) ? $this->input['user_id'] : 0;


            if ($grp_ledg_tree->save()) {
                //Opening balance should not go in case on Income Expense ledger
                if (!in_array(strtolower($grp_ledg_tree->behaviour), array("income", "expense"))) {
                    $txn_date = $ledger_start_date;
                    $narration = 'entry for opening balance';
                    $txn_id = $this->addTxn($grp_ledg_tree->ledger_account_id, $opening_balance, $narration, $txn_date, "", $grp_ledg_tree->nature_of_account, "", "", "", "", $name, $is_opning = 1, $is_reco);
                }

                return $grp_ledg_tree->ledger_account_id;
            } else {
                return false;
            }
        } else {
            return "DUP" . $dup;
        }
    }

    private function _checkGroupNameDuplication($parent, $name, $ledg_id = "", $update_led_id = "")
    {
        $soc_id = $this->input['company_id'];

        $bind[1] = strtolower(trim($name));
        $ledgers_count = 0;
        if ($ledg_id) {
            $ledgers_count = ChsoneGrpLedgerTree::where("soc_id", $soc_id)->where('ledger_account_name', strtolower(trim($name)))->where('ledger_account_id', '!=', $ledg_id)->count();
        }
        if ($update_led_id) {
            $ledgers_count = ChsoneGrpLedgerTree::where("soc_id", $soc_id)->where('ledger_account_name', strtolower(trim($name)))->where('ledger_account_id', '!=', $update_led_id)->count();
        }
        
        return $ledgers_count;
    }

    public function getCurrentFinancialYear($data=array())
    {   
        $arrFYDetail = array();
        $arrFYDetail = SocAccountFinancialYearMaster::where("soc_id", $data['soc_id'])->where("confirmed", 0)->orderBy("account_closing_id", "asc")->first();
        
        if(!empty($arrFYDetail))
        {
            $arrFYDetail = $arrFYDetail->toArray();
        }
        return $arrFYDetail;
    }

    public function getLedger($ledger_acct_id, $return_object = 1)
    {
        //fetch ledger details from chsone_grp_ledger_tree whose ledger_account_id = $ledger_acct_id
        $ledger = ChsoneGrpLedgerTree::where("ledger_account_id", $ledger_acct_id)->first();

        if ($ledger) {
            if ($return_object) {
                return $ledger;
            } else {
                return $ledger->toArray();
            }
        } else {
            return false;
        }
    }

    public function _getLedgerProps($parent_group)
    {
        $array = array();

        if (is_numeric($parent_group)) {
            $ledger_props = ChsoneGrpLedgerTree::select('ledger_account_name', 'nature_of_account', 'behaviour', 'report_head', 'context')
            ->where('ledger_account_id', $parent_group)
            ->first();
        }

        $array = $ledger_props;
        return $array;
    }

    public function addTxn($ledger_id, $txn_amt, $narration, $txn_date, $voucher_type = VOUCHER_JOURNAL, $txn_type = "", $from_txn_id = "", $pay_mode = "", $pay_ref = "", $opening_balance = "", $ledger_name = "", $is_opning = 1, $is_reco = 0)
    {
        return $this->_addTransaction($is_reco, $ledger_id, $txn_amt, $narration, $txn_date, $voucher_type, $txn_type, $from_txn_id, $pay_mode, $pay_ref, $ledger_name, $is_opning = 1);
    }

    private function _addTransaction($is_reco = 0, $ledger_id, $txn_amt, $narration, $txn_date, $voucher_type = VOUCHER_JOURNAL, $txn_type = "", $from_txn_id = "", $pay_mode = "", $pay_ref = "", $name = "", $is_opning = "", $other_recp_ref = "", $soc_id = "", $created_by = "", $voucher_reference_id = "", $voucher_reference_number = "", $is_cancelled = 0)
    {
        $txn = new ChsoneLedgerTransaction();
        if (empty($soc_id)) {
            $soc_id = $this->input['company_id'];
        }

        if (!empty($txn_type) || $txn_type != "") {
            $type_txn = $txn_type;
        } else {
            if ($from_txn_id) {
                $mode     = "to";
                $type_txn = "cr";
            } else {
                $mode     = "from";
                $type_txn = "dr";
            }
        }

        //echo "s_opning".$is_opning;exit;
        if ($is_opning == 1) {

            $txn_entrt = ChsoneLedgerTransaction::where('soc_id', $soc_id)
            ->where('is_opening_balance', 1)
            ->where('ledger_account_id', $ledger_id)
            ->where('transaction_date', $txn_date)
            ->first();

            $txn->value_date = $txn_date;
            $txn->txn_id = (isset($txn_entrt->txn_id)  && !empty($txn_entrt->txn_id)) ? $txn_entrt->txn_id : '';
            if (!empty($txn->txn_id)) {
                $txn = ChsoneLedgerTransaction::findFirst("txn_id = " . $txn->txn_id);
            }
        }

        $txn->soc_id = $soc_id;
        $txn->transaction_date = $txn_date;
        $txn->ledger_account_id = $ledger_id;
        $txn->ledger_account_name = $name;
        $txn->voucher_type = $voucher_type;
        $txn->transaction_type = $type_txn; //$ledger_name_obj->nature_of_account;//$txn_type;
        $txn->payment_mode = $pay_mode;
        $txn->payment_reference = $pay_ref;
        $txn->transaction_amount = (is_null($txn_amt)) ? $txn->transaction_amount : $txn_amt;
        $txn->other_reference_id = $other_recp_ref;
        $txn->txn_from_id = $from_txn_id;
        $txn->memo_desc = $narration;
        $txn->is_opening_balance = $is_opning;
        $txn->is_reconciled = $is_reco;
        /*Added New Column in transaction*/
        $txn->voucher_reference_number = $voucher_reference_number;
        $txn->voucher_reference_id = $voucher_reference_id;
        $txn->is_cancelled = $is_cancelled;
        /*End New Column in transaction*/
        $txn->created_by = $this->input['user_id'];
        if (isset($created_by) && !empty($created_by)) {
            $txn->created_by = $created_by;
        }
        $txn->added_on = date("Y-m-d H:i:s");

        if ($txn->save()) {
            return $txn->txn_id;
        } else {
            print_r($txn->getMessages);
        }
        //echo "<pre>";print_r($a->getMessages);exit;
        return false;
    }

    public function getInvoiceGeneralSetting($data = array())
    {
        $arrInvoiceGeneralSetting = array();
        $objInvoiceGeneralSetting = IncomeInvoiceGeneralSetting::where('soc_id', $data['soc_id'])->whereIn('setting_key', $data['setting_key'])->get();
        if (!empty($objInvoiceGeneralSetting)) {
            $arrInvoiceGeneralSetting = $objInvoiceGeneralSetting->toArray();
        }
        return $arrInvoiceGeneralSetting;
    }

    public function transactionLedgerEntry($data=array())
    {
        $arrResponse = array();
        if(empty(trim($data['from_ledger_id'])) || empty(trim($data['to_ledger_id'])))
        {
            $arrResponse = array('error'=>true);
            return $arrResponse;
        }
        if(empty($data['transaction_date']))
        {
            $data['transaction_date'] = $this->getCurrentDate('database');
        }

        $transaction_id = $this->executeTransactionEntry($data); 
        if($transaction_id){
            $arrResponse = array('success'=>true, 'transaction_id'=>$transaction_id);
        }else{
            $arrResponse = array('error'=>true);
        }
        
        return $arrResponse;
    }

    public function executeTransactionEntry($data)
    {
        $vouchers_array = [];
        if ((strpos($data['voucher_type'], '_') !== false) && (strpos($data['voucher_type'], '_') > 0)) {
            $vouchers_array = explode('_', $data['voucher_type']);
            $data['voucher_type'] = $vouchers_array[0];
        }

        $data['is_opening'] = 0;
        $data['transaction_from_id'] = '';
        $data['transaction_from_id'] = $this->_addTransactionEntry($data);

        if (!empty($data['transaction_from_id'])) {
            if (count($vouchers_array) >= 2) {
                $data['voucher_type'] = $vouchers_array[1];
            }
            if ($this->_addTransactionEntry($data)) {
                return $data['transaction_from_id'];
            } else {
                return false;
            }
        } else {
            return false;
        }
    }

    private function _addTransactionEntry($data)
    {
        $txn = new ChsoneLedgerTransaction();
        if (!empty($data['transaction_from_id'])) {
            $mode = "to";
            $data['transaction_type'] = "cr";
            $ledger_id = $data['to_ledger_id'];
            $ledger_name = $data['to_ledger_name'];
        } else {
            $mode = "from";
            $data['transaction_type'] = "dr";
            $ledger_id = $data['from_ledger_id'];
            $ledger_name = $data['from_ledger_name'];
        }
        //echo "s_opning".$is_opning;exit;
        if (!empty($data['is_opening']) && $data['is_opening'] == 1) {
            $txn_entrt = ChsoneLedgerTransaction::where('soc_id', $data['soc_id'])
                ->where('is_opening_balance', 1)
                ->where('ledger_account_id', $ledger_id)
                ->first();

            $txn->txn_id = (isset($txn_entrt->txn_id)  && !empty($txn_entrt->txn_id)) ? $txn_entrt->txn_id : '';
        }
        $txn->soc_id = $data['soc_id'];
        $txn->transaction_date = $data['transaction_date'];
        $txn->ledger_account_id = $ledger_id;
        $txn->ledger_account_name = $ledger_name;
        $txn->voucher_type = $data['voucher_type'];
        $txn->transaction_type = $data['transaction_type']; //$ledger_name_obj->nature_of_account;//$txn_type;
        $txn->payment_mode = $data['mode_of_payment'];
        $txn->payment_reference = $data['payment_reference'];
        $txn->transaction_amount = $data['transaction_amount'];
        $txn->other_reference_id = $data['other_payment_ref'];
        $txn->txn_from_id = $data['transaction_from_id'];
        $txn->memo_desc = $data['narration'];
        $txn->is_opening_balance = $data['is_opening'];
        $txn->is_reconciled = (!empty($data['is_reconciled'])) ? $data['is_reconciled'] : 0;;
        $txn->created_by = (!empty($data['user_id'])) ? $data['user_id'] : 0;
        $txn->added_on = date("Y-m-d H:i:s");
        $txn->voucher_reference_number = (!empty($data['voucher_reference_number'])) ? $data['voucher_reference_number'] : '';
        $txn->voucher_reference_id = (!empty($data['voucher_reference_id'])) ? $data['voucher_reference_id'] : '';
        $txn->is_cancelled = (!empty($data['is_cancelled'])) ? 1 : 0;

        if ($txn->save()) {
            return $txn->txn_id;
        } else {
            return false;
        }
    }

    public function suspenseLedgerEntry(array $arrData = array())
    {
        $arrResponse = array('status' => 'error');
        $arrPostData = $arrData['arrData'];

        if (!empty($arrPostData)) {
            $arrListnerData['soc_id'] = $arrPostData['soc_id'];
            $arrListnerData['unit_id'] = $arrPostData['unit_id'];
            $arrListnerData['ledger_name'] = 'Suspense Account';
            $arrListnerData['context'] = ACCOUNT_RECEIVABLE_GROUP;
            $arrListnerData['behaviour'] = 'asset';
            $arrListnerData['group_name'] = 'Account Receivable';

            $arrUnitLedgerDetails = $this->checkledgerExistNew($arrListnerData);

            $countLedgerEntry = $this->payCommonBillLedger(array('arrPostData' => $arrPostData, 'arrUnitLedgerDetails' => $arrUnitLedgerDetails)); //get all Unit details

            $arrResponse = array('status' => 'success');
        }
        return $arrResponse;
    }

    public function updateIncomePaymentTrackerStatus($data = array())
    {
        $arrResponse['status'] = 'error';
        $data['updated_date'] = date('Y-m-d H:i:s');
        $updateResponse = $this->updateIncomeInvoicePaymentTrackerStatus($data);
        if (!empty($updateResponse) && strtolower($updateResponse['status']) == 'success') {
            $arrResponse['status'] = 'success';
        }
        return $arrResponse;
    }

    public function updateIncomeInvoicePaymentTrackerStatus($data, $full_list = false)
    {
        $arrResponse = ['status' => 'success', 'message' => []];

        $updateData = [
            'updated_date' => $data['updated_date']
        ];

        if (!empty($data['status'])) {
            $updateData['status'] = $data['status'];
        }
        if (!empty($data['transaction_status'])) {
            $updateData['transaction_status'] = $data['transaction_status'];
        }
        if (!empty($data['invoice_number'])) {
            $updateData['invoice_number'] = $data['invoice_number'];
        }
        if (!empty($data['payment_date'])) {
            $updateData['payment_date'] = $data['payment_date'];
        }
        if (!empty($data['other_information'])) {
            $updateData['other_information'] = $data['other_information'];
        }
        if (!empty($data['reversal_note'])) {
            $updateData['reversal_note'] = $data['reversal_note'];
        }
        if (!empty($data['unit_id'])) {
            $updateData['unit_id'] = $data['unit_id'];
        }
        if (!empty($data['bill_type'])) {
            $updateData['bill_type'] = $data['bill_type'];
        }
        if (!empty($data['total_unpaid_amount'])) {
            $updateData['total_due_amount'] = $data['total_unpaid_amount'];
        }

        $result = IncomeInvoicePaymentTracker::where('soc_id', $data['soc_id'])
            ->where('id', $data['payment_tracker_id'])
            ->update($updateData);

        if (!$result) {
            $arrResponse = ['status' => 'error', 'message' => ['Failed to update payment tracker']];
        }

        return $arrResponse;
    }
}
