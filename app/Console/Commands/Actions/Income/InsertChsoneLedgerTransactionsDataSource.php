<?php

namespace App\Console\Commands\Actions\Income;

use Illuminate\Console\Command;
use App\Console\Commands\Action;

class InsertChsoneLedgerTransactionsDataSource extends Action
{
    protected $signature = 'datasource:InsertChsoneLedgerTransactions {flowId} {parentId} {input}';

    protected $description = 'Insert Chsone Ledger Transactions Data Source';

    protected $rules = [];

    protected $rulesMessage = [];

    public function apply()
    {
        $company_id = $this->input['company_id'];

        $particular = $this->tenantDB()->table('income_common_area_charges')->where('id', $this->input['particular_id'])->first();
        $unit_master_ledger_account_id = $this->tenantDB()->table('chsone_units_master')->where('unit_id', $this->input['unit_id'])->first()->ledger_account_id;
        $chsone_grp_ledger_tree = $this->tenantDB()->table('chsone_grp_ledger_tree')->where('soc_id', $company_id)->where('ledger_account_id', $unit_master_ledger_account_id)->first();

        $obj = $this->tenantDB()->table('chsone_ledger_transactions')
        ->insert([
            'soc_id' => $company_id,
            'transaction_date' => $this->input['bill_date'],
            'ledger_account_id' => $chsone_grp_ledger_tree->ledger_account_id,
            'ledger_account_name' => $chsone_grp_ledger_tree->ledger_account_name,
            'voucher_type' => 'income',
            'voucher_reference_number' => $this->input['invoice_number'],
            'voucher_reference_id' => $this->input['voucher_reference_id'],
            'transaction_type' => $chsone_grp_ledger_tree->nature_of_account,
            'transaction_amount' => $this->input['particular_amount'],
            'txn_from_id' => 0,
            'memo_desc' => $particular->particular.' charges against invoice '. $this->input['invoice_number'] .' dated '. $this->input['bill_date'],
            'is_opening_balance' => 0,
            'is_reconciled' => 0,
            'is_cancelled' => 0,
            'created_by' => $this->input['user_id'] ?? 0,
            'added_on' => date('Y-m-d H:i:s'),
        ]);

        $result = $this->tenantDB()->table('chsone_ledger_transactions')->orderBy('txn_id', 'desc')->first();
        $chsone_grp_ledger_tree_second = $this->tenantDB()->table('chsone_grp_ledger_tree')->where('soc_id', $company_id)->where('ledger_account_id', $particular->ledger_id)->first();
        
        $obj2 = $this->tenantDB()->table('chsone_ledger_transactions')
        ->insert([
            'soc_id' => $company_id,
            'transaction_date' => $this->input['bill_date'],
            'ledger_account_id' => $chsone_grp_ledger_tree_second->ledger_account_id,
            'ledger_account_name' => $chsone_grp_ledger_tree_second->ledger_account_name,
            'voucher_type' => 'income',
            'voucher_reference_number' => $this->input['invoice_number'],
            'voucher_reference_id' => $this->input['voucher_reference_id'],
            'transaction_type' => $chsone_grp_ledger_tree_second->nature_of_account,
            'transaction_amount' => $this->input['particular_amount'],
            'txn_from_id' => $result->txn_id,
            'memo_desc' => $particular->particular.' charges against invoice '. $this->input['invoice_number'] .' dated '. $this->input['bill_date'],
            'is_opening_balance' => 0,
            'is_reconciled' => 0,
            'is_cancelled' => 0,
            'created_by' => $this->input['user_id'] ?? 0,
            'added_on' => date('Y-m-d H:i:s'),
        ]);


        // check if interest_amount is set then insert record in the chsone_ledger_transactions table
        if(isset($this->input['interest_amount']) && !empty($this->input['interest_amount']) && $this->input['interest_amount'] > 0) {
            $obj3 = $this->tenantDB()->table('chsone_ledger_transactions')
            ->insert([
                'soc_id' => $company_id,
                'transaction_date' => $this->input['bill_date'],
                'ledger_account_id' => $chsone_grp_ledger_tree->ledger_account_id,
                'ledger_account_name' => $chsone_grp_ledger_tree->ledger_account_name,
                'voucher_type' => 'income',
                'voucher_reference_number' => $this->input['invoice_number'],
                'voucher_reference_id' => $this->input['voucher_reference_id'],
                'transaction_type' => $chsone_grp_ledger_tree->nature_of_account,
                'transaction_amount' => $this->input['interest_amount'],
                'txn_from_id' => 0,
                'memo_desc' => 'Late payment charges against invoice '. $this->input['invoice_number'] .' dated '. $this->input['bill_date'],
                'is_opening_balance' => 0,
                'is_reconciled' => 0,
                'is_cancelled' => 0,
                'created_by' => $this->input['user_id'] ?? 0,
                'added_on' => date('Y-m-d H:i:s'),
            ]);

            $result1 = $this->tenantDB()->table('chsone_ledger_transactions')->orderBy('txn_id', 'desc')->first();
            $chsone_grp_ledger_tree_interest_id = $this->tenantDB()->table('chsone_grp_ledger_tree')->where('soc_id', $company_id)->where('ledger_account_name', 'Interest')->first();

            $obj2 = $this->tenantDB()->table('chsone_ledger_transactions')
            ->insert([
                'soc_id' => $company_id,
                'transaction_date' => $this->input['bill_date'],
                'ledger_account_id' => $chsone_grp_ledger_tree_interest_id->ledger_account_id,
                'ledger_account_name' => $chsone_grp_ledger_tree_interest_id->ledger_account_name,
                'voucher_type' => 'income',
                'voucher_reference_number' => $this->input['invoice_number'],
                'voucher_reference_id' => $this->input['voucher_reference_id'],
                'transaction_type' => $chsone_grp_ledger_tree_interest_id->nature_of_account,
                'transaction_amount' => $this->input['particular_amount'],
                'txn_from_id' => $result1->txn_id,
                'memo_desc' => 'Late payment charges against invoice '. $this->input['invoice_number'] .' dated '. $this->input['bill_date'],
                'is_opening_balance' => 0,
                'is_reconciled' => 0,
                'is_cancelled' => 0,
                'created_by' => $this->input['user_id'] ?? 0,
                'added_on' => date('Y-m-d H:i:s'),
            ]);
        }
    }
}