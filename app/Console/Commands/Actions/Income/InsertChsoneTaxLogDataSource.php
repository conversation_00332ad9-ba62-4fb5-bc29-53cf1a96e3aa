<?php

namespace App\Console\Commands\Actions\Income;

use Illuminate\Console\Command;
use App\Console\Commands\Action;

class InsertChsoneTaxLogDataSource extends Action
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'datasource:InsertChsoneTaxLog {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Insert Chsone Tax Log Data Source';

    /**
     * Execute the console command.
     */
    public function apply()
    {
        $company_id = $this->input['company_id'];
        $particularDetails = $this->getParticularName($this->input['particular_id']);

        // check if gst is applicable for the particular details
        if ($particularDetails->tax_class_id == 1) {
            // fetch tax details from chsone_tax_categories table where fk_tax_class_id is $particularDetails->tax_class_id left join with chsone_tax_class table where tax_class_id is $particularDetails->tax_class_id
            $taxDetails = $this->tenantDB()->table('chsone_tax_categories as categories')
            ->where('fk_tax_class_id', $particularDetails->tax_class_id)
            ->where('categories.status', '1')
            ->where('classes.status', '1')
            ->leftJoin('chsone_tax_classes as classes', 'categories.fk_tax_class_id', '=', 'classes.tax_class_id')
            ->select('categories.tax_categories_id','categories.tax_categories_name','categories.tax_categories_amount','categories.tax_categories_type','classes.tax_class_id','classes.tax_class_name','classes.tax_class_description','classes.tax_categories_footer')
            ->get();

            // calculate tax amount for each tax_categories_name
            foreach ($taxDetails as $taxDetail) {
                $taxAmount = ($this->input['particular_amount'] * $taxDetail->tax_categories_amount) / 100;
                
                $this->input['tax_class'] = '('.$taxDetail->tax_class_name.')';
                $this->input['tax_class_id'] = $taxDetail->tax_class_id;
                $this->input['tax_rule'] = $taxDetail->tax_categories_name;
                $this->input['tax_rule_id'] = $taxDetail->tax_categories_id;
                $this->input['tax_rate'] = $taxDetail->tax_categories_amount;
                $this->input['tax_rate_type'] = $taxDetail->tax_categories_type;

                // need to insert the tax record in chsone_tax_log table for each tax rule
                $obj = $this->tenantDB()->table('chsone_tax_log')
                ->insert([
                    'soc_id' => $company_id,
                    'invoice_number' => $this->input['invoice_number'],
                    'particular_id' => $this->input['particular_id'],
                    'particular' => $particularDetails->particular,
                    'particular_amount' => $this->input['particular_amount'],
                    'tax_class' => '('.$taxDetail->tax_class_name.')',
                    'tax_class_id' => $taxDetail->tax_class_id,
                    'tax_rule' => $taxDetail->tax_categories_name,
                    'tax_rule_id' => $taxDetail->tax_categories_id,
                    'tax_rate' => $taxDetail->tax_categories_amount,
                    'tax_rate_type' => $taxDetail->tax_categories_type,
                    'tax_amount' => $taxAmount,
                    'status' => 'generated',
                    'created_date' => date('Y-m-d H:i:s'),
                ]);
            }
        } else {
            // insert the single tax record in chsone_tax_log table
            $obj = $this->tenantDB()->table('chsone_tax_log')
            ->insert([
                'soc_id' => $company_id,
                'invoice_number' => $this->input['invoice_number'],
                'particular_id' => $this->input['particular_id'],
                'particular' => $particularDetails->particular,
                'particular_amount' => $this->input['particular_amount'],
                'tax_rate' => 0,
                'tax_amount' => 0,
                'status' => 'generated',
                'created_date' => date('Y-m-d H:i:s'),
            ]);
        }

        // get inserted record after successful insertion
        $chsone_tax_log = $this->tenantDB()->table('chsone_tax_log')
        ->where('invoice_number', $this->input['invoice_number'])
        ->get();
        
        $this->data = $chsone_tax_log;
    }

    public function getParticularName($particular_id)
    {
        $particular = $this->tenantDB()->table('income_common_area_charges')
        ->where('id', $particular_id)
        ->first();

        return $particular;
    }
}
