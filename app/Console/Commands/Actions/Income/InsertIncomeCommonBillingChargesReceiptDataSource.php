<?php

namespace App\Console\Commands\Actions\Income;

use Illuminate\Console\Command;
use App\Console\Commands\Action;
use App\Models\Tenants\IncomeInvoicePaymentTracker;
use App\Models\Tenants\IncomeInvoiceGeneralSetting;

class InsertIncomeCommonBillingChargesReceiptDataSource extends Action
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'datasource:InsertIncomeCommonBillingChargesReceipt {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Insert Income Common Billing Charges Receipt DataSource';

    /**
     * Execute the console command.
     */
    public function apply()
    {
        $data = $this->input;

        // check if unit id is valid or not from chsone_units_master table
        $unit = $this->tenantDB()->table('chsone_units_master')
            ->where('unit_id', $data['unit_id'])
            ->get();
        
        if(count($unit) == 0) {
            $this->status = 'error';
            $this->message = 'Please provide a valid unit id';
            $this->statusCode = 400;
            $this->data = [];
            return;
        }

        // check if bank_account is empty then fecth default bank account id from chsone_account_master table
        if(!isset($data['bank_account']) && empty($data['bank_account'])) {
            $defaultBankAccount = $this->tenantDB()->table('chsone_accounts_master')->where('soc_id', $data['company_id'])->where('status', '1')->where('default_account', '1')->first();
            $data['bank_account'] = $defaultBankAccount->ledger_account_id ?? '';
        } else {
            $data['bank_account'] = 0;
        }

        // Create new receipt number
        $newReceiptNumber = $this->generate_receipt_id($data);

        $incomeInvoicePaymentTracker = new IncomeInvoicePaymentTracker;

        $incomeInvoicePaymentTracker->soc_id = $data['company_id'] ?? '';
        $incomeInvoicePaymentTracker->unit_id = $data['unit_id'] ?? '';
        $incomeInvoicePaymentTracker->invoice_number = $data['invoice_number'] ?? '';
        $incomeInvoicePaymentTracker->receipt_number = $newReceiptNumber;
        $incomeInvoicePaymentTracker->bill_type = 'common_bill';
        $incomeInvoicePaymentTracker->payment_mode = $data['receipt_mode'] ?? '';
        if($data['receipt_mode'] == 'cheque') {
            $incomeInvoicePaymentTracker->transaction_reference = $data['cheque_number'] ?? '';
            $incomeInvoicePaymentTracker->payment_instrument = $data['bank_name'] ?? '';
            $incomeInvoicePaymentTracker->cheque_date = $data['cheque_date'] ?? '';
        } elseif($data['receipt_mode'] == 'cashtransfer') {
            $incomeInvoicePaymentTracker->transaction_reference = $data['transaction_reference'] ?? '';
        }
        $incomeInvoicePaymentTracker->received_from = $data['received_from'] ?? '';
        $incomeInvoicePaymentTracker->total_due_amount = $data['total_due_amount'] ?? '';
        $incomeInvoicePaymentTracker->late_payment_charges = $data['late_payment_charges'] ?? '0.000';
        $incomeInvoicePaymentTracker->writeoff_amount = $data['writeoff_amount'] ?? '0.000';
        $incomeInvoicePaymentTracker->payment_amount = $data['invoice_amount'] ?? '0.000';
        $incomeInvoicePaymentTracker->tds_deducted = $data['tds_amount'] ?? '0.000';
        if($data['receipt_mode'] == 'cash') {
            $incomeInvoicePaymentTracker->status = 'Y';
        } elseif($data['receipt_mode'] == 'cheque') {
            $incomeInvoicePaymentTracker->status = 'P';
        } elseif($data['receipt_mode'] == 'cashtransfer') {
            $incomeInvoicePaymentTracker->status = 'Y';
        }
        $incomeInvoicePaymentTracker->transaction_status = 'complete';
        $incomeInvoicePaymentTracker->payment_token = $this->generate_payment_token($data);
        $incomeInvoicePaymentTracker->payment_note = $data['receipt_note'] ?? '';
        $incomeInvoicePaymentTracker->payment_date = $data['receipt_date'] ?? '';
        $incomeInvoicePaymentTracker->updated_by = $data['updated_by'] ?? '0';
        $incomeInvoicePaymentTracker->created_by = $data['user_id'] ?? '0';
        $incomeInvoicePaymentTracker->updated_date = date("Y-m-d");
        $incomeInvoicePaymentTracker->created_date = date("Y-m-d");

        // create other information
        $arrOtherInfo = array();
        $arrOtherInfo['common_bill_detail']['common_bill_id'] = $data['invoice_id'];
        $arrOtherInfo['common_bill_detail']['bank_ledger'] = $data['bank_account'];
        $arrOtherInfo['common_bill_detail']['bill_type_category'] = $data['bill_type_category'] ?? 'N';
        $arrOtherInfo['common_bill_detail']['payment_type'] = $data['payment_type'] ?? 'N';

        $incomeInvoicePaymentTracker['other_information'] = serialize($arrOtherInfo);

        $incomeInvoicePaymentTracker->save();
        if ($incomeInvoicePaymentTracker->save()) {
            $this->message = "Receipt created successfully";
            $this->status = 'success';
            $this->statusCode = 200;
            $this->data = $incomeInvoicePaymentTracker->toArray();
        } else {
            $this->message = "Unable to create receipt";
            $this->status = 'failed';
            $this->statusCode = 400;
            $this->data = $incomeInvoicePaymentTracker->toArray();
        }
    }

    public function generate_receipt_id($data) {
        $settingDetails = IncomeInvoiceGeneralSetting::where('soc_id', $data['company_id'])->where('setting_key', 'INCOME_RECEIPT_PREFIX')->first();

        // get last receipt number
        $last_receipt_number = $this->tenantDB()->table('income_invoice_payment_tracker')->orderBy('id', 'desc')->first();
        if($last_receipt_number && $last_receipt_number->receipt_number) {
            
            $numericPart = (int)preg_replace('/\D/', '', $last_receipt_number->receipt_number);
            // Increment the numeric part
            $newNumericPart = $numericPart + 1;
            // Format the new invoice number
            $newReceiptNumber = $settingDetails->setting_value . str_pad($newNumericPart, 5, '0', STR_PAD_LEFT);
        } else {
            $newReceiptNumber = $settingDetails->setting_value . '00001';
        }
        
        return $newReceiptNumber;

    }

    public function generate_payment_token($data) {
        $paymentToken = '';
        if (!empty($data)) {
            $arrPaymentDetail['soc_id'] = $data['company_id'];
            $arrPaymentDetail['received_from'] = $data['received_from'];
            $arrPaymentDetail['bill_type'] = 'common_bill';
            $arrPaymentDetail['payment_amount'] = $data['invoice_amount'];
            $arrPaymentDetail['payment_mode'] = $data['receipt_mode'];
            $arrPaymentDetail['invoice_number'] = $data['invoice_number'];
            $arrPaymentDetail['payment_date'] = $data['receipt_date'];
            if (!empty($arrPaymentDetail)) {
                $paymentToken = md5(time() . serialize($arrPaymentDetail));
            }
            return $paymentToken;
        }
    }
}
