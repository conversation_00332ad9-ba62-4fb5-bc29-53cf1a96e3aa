<?php

namespace App\Console\Commands\Actions\Income;

use Illuminate\Support\Facades\DB;
use App\Console\Commands\Action;
use App\Models\Income\ChsoneInvoiceGeneration;
use App\Models\Tenants\ChsoneInvoiceGeneration as TenantsChsoneInvoiceGeneration;
use App\Models\Tenants\IncomeUnitInvoice;

class stopInvoicingDataSource extends Action
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'datasource:stopInvoicing {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Edit Member Name data source';

    protected $formatter =  [];

    protected $formatterByKeys =  [];

    protected $mapper = [];

    /**
     * Execute the console command.
     */
    public function apply()
    {

        // Retrieve the authenticated user's soc_id
        $socId = $this->input['company_id'];
        $status = $this->input['status'] ?? 0;

        // Find the unit by soc_id and unit_id
        $unit = $this->tenantDB()
            ->table('chsone_units_master')
            ->where('soc_id', $socId)
            ->where('unit_id', $this->input['id'])
            ->first();

        if (!$unit) {
            $this->status = "error";
            $this->message = "Unit not found";
            $this->statusCode = 404;
            return;
        }

        // Update the chargeable status using query builder
        try {
            $updated = $this->tenantDB()
                ->table('chsone_units_master')
                ->where('soc_id', $socId)
                ->where('unit_id', (int) $this->input['id'])
                ->update(['chargeable' => $status]);


            if ($updated) {
                $this->status = 'success';
                $this->message = 'status updated successfully';
                $this->statusCode = 200;
                $this->data = [];
                return;

            } else {
                $this->status = 'error';
                $this->message = 'failed to update';
                $this->statusCode = 400;
                $this->data = [];
                return;

            }
        } catch (\Exception $e) {
            $this->status = 'error';
            $this->message = 'failed to update';
            $this->statusCode = 400;
            $this->data = [];
            return;
        }


    }


}
