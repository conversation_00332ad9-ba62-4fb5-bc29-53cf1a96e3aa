<?php

namespace App\Console\Commands\Actions\Income;

use Illuminate\Console\Command;
use App\Console\Commands\Action;
use App\Models\Tenants\ChsoneCreditAccount;
use App\Models\Tenants\ChsoneGrpLedgerTree;
use App\Models\Tenants\ChsoneLedgerTransaction;
use App\Models\Tenants\ChsoneTaxLog;
use App\Models\Tenants\ChsoneUnitsMaster;
use App\Models\Tenants\FacilityBooking;
use App\Models\Tenants\FacilityBookingAccount;
use App\Models\Tenants\IncomeCommonAreaCharge;
use App\Models\Tenants\IncomeCommonBillingPayment;
use App\Models\Tenants\SocAccountFinancialYearMaster;
use Illuminate\Support\Facades\Config;

class cancelCommonBillDataSource extends Action
{
    protected $constants;

    public function __construct() {
        parent::__construct();
        $this->constants = Config::get("constants");
    }

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'datasource:cancelCommonBill {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Cancel Common Bill Data Source';

    /**
     * Execute the console command.
     */
    public function apply()
    {
        $invoice_id = $this->input['invoice_id'];
        $company_id = $this->input['company_id'];
        $user_id = !empty($this->input['user_id']) ? $this->input['user_id'] : 0;

        // check unit_invoice_id is exist or not in income_unit_invoices table if not exist then return error
        $invoiceDetails = $this->tenantDB()->table('income_common_billing_charges')
            ->where('id', $invoice_id)
            ->where('soc_id', $company_id)
            ->get();

        if(count($invoiceDetails) == 0){
            $this->status = 'error';
            $this->message = 'No invoice found for this id / provide a valid invoice id';
            $this->statusCode = 400;
            $this->data = [];
            return;
        }

        if($invoiceDetails[0]->payment_status == 'cancelled'){
            $this->status = 'error';
            $this->message = 'Invoice already cancelled';
            $this->statusCode = 403;
            $this->data = [];
            return;
        }

        if($invoiceDetails[0]->payment_status != 'unpaid'){
            $this->status = 'error';
            $this->message = 'Invoice can not be cancelled';
            $this->statusCode = 403;
            $this->data = [];
            return;
        }

        $arrInvoiceDetail = json_decode(json_encode($invoiceDetails[0]), true);

        if(!empty($arrInvoiceDetail))
        {
            // fetch voucher_reference_id fronm income_unit_invoices table whose invoice_number is equal to $arrInvoiceDetail['invoice_number'] and soc_id is equal to $company_id
            $voucher_reference_id = $this->tenantDB()->table('income_unit_invoices')
                ->where('invoice_number', $arrInvoiceDetail['invoice_number'])
                ->where('soc_id', $company_id)
                ->select('unit_invoice_id')
                ->first();
            $arrInvoiceDetail['voucher_reference_id'] = $voucher_reference_id->unit_invoice_id;

            // check tax details
            $tax_amt = 0;
            $tax_amts = ChsoneTaxLog::where('invoice_number', $arrInvoiceDetail['invoice_number'])->where('soc_id', $company_id)->get();
            if(!empty($tax_amts)){
                foreach ($tax_amts as $tax_amount) {
                    $tax_amt += $tax_amount->tax_amount;
                }
            }

            // set arrListnerData data
            $arrListnerData = array('soc_id' => $company_id, 'unit_id' => $arrInvoiceDetail['fk_unit_id']);
            $arrUnitDetails = $this->getUnitDetailById($arrListnerData);
            $arrListnerData['ledger_id'] = $arrUnitDetails['ledger_account_id'];

            // check ledger exist or not
            $arrUnitLedgerDetails = $this->checkledgerExistNew($arrListnerData);

            // check bill from is facility or incident
            if (!empty($arrInvoiceDetail['bill_from']) && strtolower($arrInvoiceDetail['bill_from']) == 'facility') {

                // fetch facility account details
                $arrFacilityAccount = $this->getFacilityAccountDetail(array('soc_id' => $company_id, 'account_type' => 'member', 'unit_id' => $arrInvoiceDetail['billing_type']));
                if (!empty($arrFacilityAccount['fk_income_ledger_id'])) {
                    $arrIncomeListnerData = array('soc_id' => $company_id, 'ledger_id' => $arrFacilityAccount['fk_income_ledger_id']);
                } else {
                    $arrCommonUnitDetails = $this->getUnitDetailById(array('soc_id' => $company_id, 'unit_id' => $arrInvoiceDetail['billing_type']));

                    $arrIncomeListnerData['ledger_id'] = $arrCommonUnitDetails['ledger_account_id']; //'BLDG#'.strtoupper($arrCommonUnitDetails['soc_building_name']).'-'.$arrCommonUnitDetails['unit_flat_number'];
                    //$arrIncomeListnerData['context'] = ACCOUNT_RECEIVABLE_GROUP;
                }

                //Get paid amount
                $arrInvoiceDetail['paid_amount'] = 0;
                if ($arrInvoiceDetail['payment_status'] == 'partialpaid') {
                    $arrInvoiceDetail['paid_amount'] = $arrInvoiceDetail['advance_amount'];
                    $arrInvoicePaymentDetail = $this->getCommonBillingInvoicePayments(array('soc_id' => $company_id, 'common_bill_id' => $invoice_id, 'unit_id' => $arrInvoiceDetail['fk_unit_id']));
                    if (!empty($arrInvoicePaymentDetail)) {
                        $arrInvoiceDetail['paid_amount'] += $arrInvoicePaymentDetail['payment_amount'];
                    }
                } elseif ($arrInvoiceDetail['payment_status'] == 'paid') {
                    $arrInvoiceDetail['paid_amount'] = $arrInvoiceDetail['amount'];
                }

                //Get cancellation amount and deduct from invoice amount and paid amount
                $arrInvoiceDetail['cancellation_amount'] = 0;
                $arrFacilityCancellationDetail = $this->getFacilityCancellationDetail(array('soc_id' => $company_id, 'unit_id' => $arrInvoiceDetail['billing_type'], 'bill_number' => $arrInvoiceDetail['invoice_number']));
                if (!empty($arrFacilityCancellationDetail)) {
                    if (!empty($arrFacilityCancellationDetail['booked_by']) && strtolower($arrFacilityCancellationDetail['booked_by']) == 'member') {
                        $arrInvoiceDetail['cancellation_amount'] = $arrFacilityCancellationDetail['cancellation_charge_member'];
                    } elseif (!empty($arrFacilityCancellationDetail['booked_by']) && strtolower($arrFacilityCancellationDetail['booked_by']) == 'tenant') {
                        $arrInvoiceDetail['cancellation_amount'] = $arrFacilityCancellationDetail['cancellation_charge_tenant'];
                    }

                    if ($arrInvoiceDetail['cancellation_amount'] > 0) {
                        $arrInvoiceDetail['amount'] = ($arrInvoiceDetail['amount'] > $arrInvoiceDetail['cancellation_amount']) ? (float) round($arrInvoiceDetail['amount'] - $arrInvoiceDetail['cancellation_amount'], 2) : 0;
                        $arrInvoiceDetail['paid_amount'] = ($arrInvoiceDetail['paid_amount'] > $arrInvoiceDetail['cancellation_amount']) ? (float) round($arrInvoiceDetail['paid_amount'] - $arrInvoiceDetail['cancellation_amount'], 2) : 0;
                    }
                }
            } else {
                if (is_numeric($arrInvoiceDetail['billing_type'])) {
                    $led_id = IncomeCommonAreaCharge::where('id', $arrInvoiceDetail['billing_type'])->value('ledger_id');

                    $arrIncomeListnerData = array('soc_id' => $company_id, 'ledger_id' => $led_id);
                } else {
                    $arrIncomeListnerData = array('soc_id' => $company_id);
                    $arrIncomeListnerData['ledger_name'] = 'repair fund';
                    $arrIncomeListnerData['context'] = 'membercontri';
                }
            }

            // check ledger exist or not
            $arrIncomeLedgerDetails = $this->checkledgerExistNew($arrIncomeListnerData);

            if (!empty($tax_amt) && $tax_amt > 0) {
                $arrInvoiceDetail['tax_amount'] = $tax_amt;
                $arrInvoiceDetail['arrTaxLogDetail'] = $tax_amts->toArray();
            }
            
            // cancel common billing ledger transaction
            $ledgerTransaction = $this->cancelCommonBillLedgerTransaction(array('soc_id' => $company_id, 'user_id' => $user_id, 'arrUnitLedgerDetails' => $arrUnitLedgerDetails, 'arrIncomeLedgerDetails' => $arrIncomeLedgerDetails, 'arrInvoiceDetail' => $arrInvoiceDetail));

            //Update the payment status of transaction
            $arrResponse['status'] = 'error';

            if (!empty($ledgerTransaction)) {
                // Update payment_status in income_common_billing_charges
                $updateStatus = $this->tenantDB()->table('income_common_billing_charges')
                ->where('invoice_number', $arrInvoiceDetail['invoice_number'])
                ->update(['payment_status' => 'cancelled']);

                if ($updateStatus === false) {
                    // If update fails, add error message to response
                    $arrResponse['message'] = 'Failed to update payment status to cancelled.';
                } else {
                    $arrResponse['status'] = 'success';
                    if (!empty($arrInvoiceDetail['bill_from']) && strtolower($arrInvoiceDetail['bill_from']) == 'facility') {

                        //Update amount Income Invoice Adjustment in case of CreditNote
                        if (!empty($arrInvoiceDetail['refund_option']) && strtolower(($arrInvoiceDetail['refund_option'])) == 'credit_note' && !empty($arrInvoiceDetail['paid_amount']) && $arrInvoiceDetail['paid_amount'] > 0) {
                            $arrCreditData['soc_id'] = $arrInvoiceDetail['soc_id'];
                            $arrCreditData['account_id'] = $arrInvoiceDetail['fk_unit_id'];
                            $arrCreditData['account_context'] = 'unit';
                            $arrCreditData['transaction_type'] = 'cr';
                            $arrCreditData['context'] = 'system';
                            $arrCreditData['credit_used_type'] = 'adjustable';
                            $arrCreditData['used_for'] = 'incidental';
                            $arrCreditData['payment_date'] = date('Y-m-d');
                            $arrCreditData['narration'] = 'Amount Rs ' . $arrInvoiceDetail['paid_amount'] . ' has credited from Advance of ' . $arrInvoiceDetail['invoice_number'];
                            $arrCreditData['payment_amount'] = $arrInvoiceDetail['paid_amount'];

                            $arrMemberDetail = $this->getPrimaryMemberByUnitId(array('soc_id' => $arrCreditData['soc_id'], 'unit_id' => $arrCreditData['account_id']));
                            $arrCreditData['account_name'] = $arrMemberDetail['member_first_name'] . ' ' . $arrMemberDetail['member_last_name'];
                            
                            $arrResponse = $this->saveCreditAccountResponse(array(
                                'auth' => true, 'process' => 'fetch', 'soc_id' => $arrCreditData['soc_id'], 'id' => '',
                                'data' => $arrCreditData, 'user_id' => $user_id, 'username' => $this->input['user']['name']
                            ));
                            if ($arrResponse['error']) {
                                $arrResponse['status'] = 'error';
                                $arrResponse['message'] = 'unable to create debit note';
                                return $arrResponse;
                            }
                        }

                        if (!empty(($arrResponse['status'])) && strtolower($arrResponse['status']) == 'success') {
                            $arrResponse = $this->updateFacilityBookingPaymentStatus(array('soc_id' => $company_id, 'bill_number' => $arrInvoiceDetail['invoice_number'], 'payment_status' => 'cancelled'));
                        }
                    }
                }
            }

            //Commit or Rollback based transation completion status
            if (!empty(($arrResponse['status'])) && strtolower($arrResponse['status']) == 'success') {
                $this->status = 'success';
                $this->message = 'Invoice has been cancelled successfully';
                $this->statusCode = 200;
                return;
            } else {
                $this->status = 'error';
                $this->message = 'Unable to cancel invoice, Please try again later';
                $this->statusCode = 400;
                return;
            }
        } else {
            $this->status = 'error';
            $this->message = 'Unable to complete transaction, No record found.';
            $this->statusCode = 400;
            return;
        }
    }

    // fecth unit details by unit_id
    public function getUnitDetailById(array $data = [])
    {
        $soc_id = $data['soc_id'];
        $unit_id = $data['unit_id'];
        $arrUnitDetails = [];

        // Find the first record matching the conditions
        $objUnitdetails = ChsoneUnitsMaster::where('soc_id', $soc_id)->where('unit_id', $unit_id)->first();

        // If record is found, convert it to array
        if ($objUnitdetails) {
            $arrUnitDetails = $objUnitdetails->toArray();
        }

        return $arrUnitDetails;
    }

    // check ledger exist or not
    public function checkledgerExistNew($data=array())
    {
        $arrClinetLedgerDetails = array();
        $query = ChsoneGrpLedgerTree::where('soc_id', $data['soc_id']);
        
        if (isset($data['ledger_name']) && !empty($data['ledger_name'])) {
            $query->where('context', $data['context'])
                  ->where('entity_type', "ledger")
                  ->where('ledger_account_name', $data['ledger_name']);
    
            if (isset($data['behaviour']) && !empty($data['behaviour'])) {
                $query->where('behaviour', $data['behaviour']);
            }
        } elseif (isset($data['ledger_id']) && !empty($data['ledger_id'])) {
            $query->where('ledger_account_id', $data['ledger_id']);
        }

        $objBookerLedger = $query->first();

        //an outsider has booked for the society. Check if exists by name; otherwise, create new.
        if(!empty($objBookerLedger)) {
            $arrClinetLedgerDetails['recieving_ledger_id'] = $objBookerLedger->ledger_account_id;
            $arrClinetLedgerDetails['receiver_name'] = $objBookerLedger->ledger_account_name;
        }
        elseif(!empty($data['group_name']))
        {
            $name         = $data['ledger_name'];
            $entity_type  = "ledger";
            $grp_ledg_id  = "";
            $parent       = $this->getParentGroupId($data, TRUE);
            
            if(empty($parent->ledger_account_id))
            {
                return $arrClinetLedgerDetails;
            }
            $parent_group = $parent->ledger_account_id;
            
            $ledger_id    = $this->manipulate($name, $entity_type, $grp_ledg_id , $parent_group, "", '', 0, '', '', '', '', '', 0, $data['soc_id']);
            if ($ledger_id) {
                $arrClinetLedgerDetails['recieving_ledger_id'] = $ledger_id;
                $arrClinetLedgerDetails['receiver_name'] = $data['ledger_name'];
            }
        }
        unset($objBookerLedger);
        return $arrClinetLedgerDetails;
    }

    // get parent group id
    public function getParentGroupId($arrData)
    {
        $parent = ChsoneGrpLedgerTree::where('soc_id', $arrData['soc_id'])
            ->where('ledger_account_name', $arrData['group_name'])
            ->where('entity_type', "group")
            ->where('behaviour', $arrData['behaviour'])
            ->where('context', $arrData['context'])
            ->first();
        if ($parent) {
            return $parent;
        } else {
            return false;
        }
    }

    // create manipulate function
    public function manipulate($name, $entity_type = ENTITY_TYPE_GROUP, $grp_ledg_id = "", $parent_group = 0, $behaviour = "", $ledg_id = '', $opening_balance = 0, $update_led_id = '', $ledger_start_date = '', $ledger_type = '', $context = '', $txn_id = 0, $soc_id = 0, $is_reco = 0)
    {
        $config = Config::get("constants");
        if(empty($soc_id)){
            $soc_id = $this->input['company_id'];
        }
        $dup = $this->_checkGroupNameDuplication($parent_group, $name, $ledg_id, $update_led_id);
        
        $arrFYDetail = $this->getCurrentFinancialYear(array('soc_id' => $soc_id));
        $arrFYDetail = json_decode(json_encode($arrFYDetail), true);
        $ledger_start_date = $arrFYDetail['fy_start_date'];
        if ($dup == 0) {
            $behaviour = trim(strtolower($behaviour));

            $grp_ledg_tree = new ChsoneGrpLedgerTree();

            if ($ledg_id) {
                $grp_ledg_tree = $this->getLedger($ledg_id);
            }

            $grp_ledg_tree->entity_type = $entity_type;
            $grp_ledg_tree->soc_id = $soc_id;
            $grp_ledg_tree->ledger_account_name = $name;
            $grp_ledg_tree->ledger_start_date = $ledger_start_date;
            $grp_ledg_tree->context_ref_id = 0;

            if (!empty($ledger_type) && $ledger_type != "NULL" && $ledger_type != '') {
                $grp_ledg_tree->operating_type = $ledger_type;
            } else {
                $grp_ledg_tree->operating_type = '';
            }

            if (!empty($grp_ledg_id)) {
                $grp_ledg_tree->ledger_account_id = $grp_ledg_id;
            }

            if ($update_led_id != '') {
                $grp_ledg_tree->ledger_account_id = $update_led_id;
            }
            if (!empty($parent_group)) {
                $grp_ledg_tree->parent_id = $parent_group;
                $ledger_props = $this->_getLedgerProps($parent_group);
            } else {
                $grp_ledg_tree->parent_id = 0;
            }

            if (!empty($behaviour)) {
                $grp_ledg_tree->behaviour = $behaviour;
                $grp_ledg_tree->nature_of_account = $config->nature_account->$behaviour;
                $grp_ledg_tree->report_head = $config->report_head->$behaviour;
            } else {
                $grp_ledg_tree->behaviour = $ledger_props["behaviour"];
                $grp_ledg_tree->nature_of_account = $ledger_props["nature_account"];
                $grp_ledg_tree->report_head = $ledger_props["report_head"];
            }

            if (!empty($context) && $context != '') {
                $grp_ledg_tree->context = $context;
            } else {
                $grp_ledg_tree->context = $ledger_props["context"];
            }
            $grp_ledg_tree->defined_by = "user";
            $grp_ledg_tree->status = 1;
            $grp_ledg_tree->added_on = date("Y-m-d H:i:s");
            $grp_ledg_tree->created_by = ($this->input['user_id']) ? $this->input['user_id'] : 0;


            if ($grp_ledg_tree->save()) {
                //Opening balance should not go in case on Income Expense ledger
                if (!in_array(strtolower($grp_ledg_tree->behaviour), array("income", "expense"))) {
                    $txn_date = $ledger_start_date;
                    $narration = 'entry for opening balance';
                    $txn_id = $this->addTxn($grp_ledg_tree->ledger_account_id, $opening_balance, $narration, $txn_date, "", $grp_ledg_tree->nature_of_account, "", "", "", "", $name, $is_opning = 1, $is_reco);
                }

                return $grp_ledg_tree->ledger_account_id;
            } else {
                return false;
            }
        } else {
            return "DUP" . $dup;
        }
    }

    private function _checkGroupNameDuplication($parent, $name, $ledg_id = "", $update_led_id = "")
    {
        $soc_id = $this->input['company_id'];

        $bind[1] = strtolower(trim($name));
        $ledgers_count = 0;
        if ($ledg_id) {
            $ledgers_count = ChsoneGrpLedgerTree::where("soc_id", $soc_id)->where('ledger_account_name', strtolower(trim($name)))->where('ledger_account_id', '!=', $ledg_id)->count();
        }
        if ($update_led_id) {
            $ledgers_count = ChsoneGrpLedgerTree::where("soc_id", $soc_id)->where('ledger_account_name', strtolower(trim($name)))->where('ledger_account_id', '!=', $update_led_id)->count();
        }
        
        return $ledgers_count;
    }

    public function getCurrentFinancialYear($data=array())
    {
        $arrFYDetail = array();
        $arrFYDetail = SocAccountFinancialYearMaster::where("soc_id", $data['soc_id'])->where("confirmed", 0)->orderBy("account_closing_id", "asc")->first();
        
        if(!empty($arrFYDetail))
        {
            $arrFYDetail = $arrFYDetail->toArray();
        }
        return $arrFYDetail;
    }

    public function getLedger($ledger_acct_id, $return_object = 1)
    {
        //fetch ledger details from chsone_grp_ledger_tree whose ledger_account_id = $ledger_acct_id
        $ledger = ChsoneGrpLedgerTree::where("ledger_account_id", $ledger_acct_id)->first();

        if ($ledger) {
            if ($return_object) {
                return $ledger;
            } else {
                return $ledger->toArray();
            }
        } else {
            return false;
        }
    }

    public function _getLedgerProps($parent_group)
    {
        $array = array();

        if (is_numeric($parent_group)) {
            $ledger_props = ChsoneGrpLedgerTree::select('ledger_account_name', 'nature_of_account', 'behaviour', 'report_head', 'context')
            ->where('ledger_account_id', $parent_group)
            ->first();
        }

        $array = $ledger_props;
        return $array;
    }

    public function addTxn($ledger_id, $txn_amt, $narration, $txn_date, $voucher_type = VOUCHER_JOURNAL, $txn_type = "", $from_txn_id = "", $pay_mode = "", $pay_ref = "", $opening_balance = "", $ledger_name = "", $is_opning = 1, $is_reco = 0)
    {
        return $this->_addTransaction($is_reco, $ledger_id, $txn_amt, $narration, $txn_date, $voucher_type, $txn_type, $from_txn_id, $pay_mode, $pay_ref, $ledger_name, $is_opning = 1);
    }

    private function _addTransaction($is_reco = 0, $ledger_id, $txn_amt, $narration, $txn_date, $voucher_type = VOUCHER_JOURNAL, $txn_type = "", $from_txn_id = "", $pay_mode = "", $pay_ref = "", $name = "", $is_opning = "", $other_recp_ref = "", $soc_id = "", $created_by = "", $voucher_reference_id = "", $voucher_reference_number = "", $is_cancelled = 0)
    {
        $txn = new ChsoneLedgerTransaction();
        if (empty($soc_id)) {
            $soc_id = $this->input['company_id'];
        }

        if (!empty($txn_type) || $txn_type != "") {
            $type_txn = $txn_type;
        } else {
            if ($from_txn_id) {
                $mode     = "to";
                $type_txn = "cr";
            } else {
                $mode     = "from";
                $type_txn = "dr";
            }
        }

        //echo "s_opning".$is_opning;exit;
        if ($is_opning == 1) {

            $txn_entrt = ChsoneLedgerTransaction::where('soc_id', $soc_id)
            ->where('is_opening_balance', 1)
            ->where('ledger_account_id', $ledger_id)
            ->where('transaction_date', $txn_date)
            ->first();

            $txn->value_date = $txn_date;
            $txn->txn_id = (isset($txn_entrt->txn_id)  && !empty($txn_entrt->txn_id)) ? $txn_entrt->txn_id : '';
            if (!empty($txn->txn_id)) {
                $txn = ChsoneLedgerTransaction::findFirst("txn_id = " . $txn->txn_id);
            }
        }

        $txn->soc_id = $soc_id;
        $txn->transaction_date = $txn_date;
        $txn->ledger_account_id = $ledger_id;
        $txn->ledger_account_name = $name;
        $txn->voucher_type = $voucher_type;
        $txn->transaction_type = $type_txn; //$ledger_name_obj->nature_of_account;//$txn_type;
        $txn->payment_mode = $pay_mode;
        $txn->payment_reference = $pay_ref;
        $txn->transaction_amount = (is_null($txn_amt)) ? $txn->transaction_amount : $txn_amt;
        $txn->other_reference_id = $other_recp_ref;
        $txn->txn_from_id = $from_txn_id;
        $txn->memo_desc = $narration;
        $txn->is_opening_balance = $is_opning;
        $txn->is_reconciled = $is_reco;
        /*Added New Column in transaction*/
        $txn->voucher_reference_number = $voucher_reference_number;
        $txn->voucher_reference_id = $voucher_reference_id;
        $txn->is_cancelled = $is_cancelled;
        /*End New Column in transaction*/
        $txn->created_by = $this->input['user_id'] ?? 0;
        if (isset($created_by) && !empty($created_by)) {
            $txn->created_by = $created_by;
        }
        $txn->added_on = date("Y-m-d H:i:s");

        if ($txn->save()) {
            return $txn->txn_id;
        } else {
            print_r($txn->getMessages);
        }
        //echo "<pre>";print_r($a->getMessages);exit;
        return false;
    }

    // get facility account details
    public function getFacilityAccountDetail($data = [])
    {
        $arrFacilityBooking = [];

        // Check if required data is present
        if (!empty($data['soc_id']) && !empty($data['account_type']) && !empty($data['unit_id'])) {
            // In case tenant set account type as member
            if (!empty($data['account_type']) && strtolower($data['account_type']) == 'tenant') {
                $data['account_type'] = 'member';
            }

            // Use Eloquent to retrieve the first matching record
            $objFacilityBooking = FacilityBookingAccount::where('soc_id', $data['soc_id'])
                ->where('account_type', $data['account_type'])
                ->where('fk_unit_id', $data['unit_id'])
                ->first();

            // If record is found, convert it to an array
            if ($objFacilityBooking) {
                $arrFacilityBooking = $objFacilityBooking->toArray();
            }
        }

        return $arrFacilityBooking;
    }

    public function getCommonBillingInvoicePayments($data = [])
    {
        $arrCommonBillingCharges = [];

        // Use Eloquent to build the query and sum up the required columns
        $arrCommonBillingCharges = IncomeCommonBillingPayment::where('soc_id', $data['soc_id'])
            ->where('fk_common_bill_id', $data['common_bill_id'])
            ->where('unit_id', $data['unit_id'])
            ->selectRaw('
                SUM(payment_amount) as payment_amount, 
                SUM(tds_deducted) as tds_deducted, 
                SUM(transaction_charges) as transaction_charges, 
                SUM(discount_amount) as discount_amount
            ')
            ->first();

        // Convert the result to array and return the first record
        if ($arrCommonBillingCharges) {
            return $arrCommonBillingCharges->toArray();
        }

        return [];
    }

    // get facility cancellation details
    public function getFacilityCancellationDetail($data = [])
    {
        $resultset = [];

        // Build the query using Eloquent with a left join
        $resultset = FacilityBooking::select([
                'facility_booking.facility_booking_id',
                'facility_booking.bill_number',
                'facility_booking.fk_unit_id',
                'facility_booking.booked_by',
                'facility_setting.cancellation_charge_member',
                'facility_setting.cancellation_charge_tenant',
                'facility_setting.cancellation_charge_nonmember'
            ])
            ->leftJoin('facility_settings as facility_setting', 'facility_setting.facility_setting_id', '=', 'facility_booking.fk_facility_setting_id')
            ->where('facility_booking.soc_id', $data['soc_id'])
            ->where('facility_booking.fk_unit_id', $data['unit_id'])
            ->where('facility_booking.bill_number', $data['bill_number'])
            ->get();

        // If record is found, convert it to array
        if ($resultset) {
            return $resultset->toArray();
        }

        return [];
    }

    // cancel common billing ledger transaction
    public function cancelCommonBillLedgerTransaction($data = array())
    {
        $countLedgerEntry = 1;
        if (ACCOUNT_MODULE_EXIST == 1) {
            $arrIncomeLedgerDetails = $data['arrIncomeLedgerDetails'];
            $arrUnitLedgerDetails = $data['arrUnitLedgerDetails'];
            $arrInvoiceDetail = $data['arrInvoiceDetail'];

            $arrLedgerTransactionData = array();
            $arrLedgerTransactionData['soc_id'] = $data['soc_id'];
            $arrLedgerTransactionData['user_id'] = $data['user_id'];
            $arrLedgerTransactionData['voucher_type'] = 'income';
            $arrLedgerTransactionData['from_ledger_id'] = $arrIncomeLedgerDetails['recieving_ledger_id'];
            $arrLedgerTransactionData['to_ledger_id'] = $arrUnitLedgerDetails['recieving_ledger_id'];
            $arrLedgerTransactionData['transaction_date'] = date('Y-m-d');
            $arrLedgerTransactionData['transaction_amount'] = $arrInvoiceDetail['amount'];
            $created_date = date('d/m/Y', strtotime($arrInvoiceDetail['created_date']));
            $arrLedgerTransactionData['narration'] = 'Cancellation of invoice number ' . $arrInvoiceDetail['invoice_number'] . ' dated on ' . $created_date;
            $arrLedgerTransactionData['from_ledger_name'] = $arrIncomeLedgerDetails['receiver_name'];
            $arrLedgerTransactionData['to_ledger_name'] = $arrUnitLedgerDetails['receiver_name'];
            $arrLedgerTransactionData['payment_reference'] = '';
            $arrLedgerTransactionData['transaction_type'] = '';
            $arrLedgerTransactionData['mode_of_payment'] = '';
            $arrLedgerTransactionData['other_payment_ref'] = '';
            $arrLedgerTransactionData['voucher_reference_number'] = $arrInvoiceDetail['invoice_number'];
            $arrLedgerTransactionData['voucher_reference_id'] = $arrInvoiceDetail['voucher_reference_id'];

            $arrLedgerEntry = $this->transactionLedgerEntry($arrLedgerTransactionData);

            if (isset($arrLedgerEntry['error']) && !empty($arrLedgerEntry['error'])) {
                $countLedgerEntry = 0;
            } elseif (!empty($arrInvoiceDetail['tax_amount']) && $arrInvoiceDetail['tax_amount'] > 0) {
                $arrLedgerTransactionData['to_ledger_id'] = $arrUnitLedgerDetails['recieving_ledger_id'];
                $arrLedgerTransactionData['to_ledger_name'] = $arrUnitLedgerDetails['receiver_name'];

                //Write code to ledger entry of GST
                if (isset($arrInvoiceDetail['arrTaxLogDetail']) && count($arrInvoiceDetail['arrTaxLogDetail']) > 0) {
                    foreach ($arrInvoiceDetail['arrTaxLogDetail'] as $eachTaxLogDetail) {
                        $arrLedgerTransactionData['transaction_amount'] = $eachTaxLogDetail['tax_amount'];
                        $arrLedgerTransactionData['narration'] = ucwords($eachTaxLogDetail['tax_rule']) . ' cancellation of invoice number ' . $arrInvoiceDetail['invoice_number'] . ' dated on ' . $this->getDisplayDate(explode(' ', $arrInvoiceDetail['created_date'])[0]);

                        $ledgerName = $this->getTaxLedgerName(array('taxClassDetail' => $eachTaxLogDetail));

                        $arrTaxLedgerDetails = $this->checkledgerExistNew(array('soc_id' => $data['soc_id'], 'ledger_name' => $ledgerName, 'context' => MEMBER_CONTRI, 'behaviour' => LIABILITY));
                        //print_r($arrIncomeLedgerDetails);exit;
                        if (!empty($arrTaxLedgerDetails)) {
                            $arrLedgerTransactionData['from_ledger_id'] = $arrTaxLedgerDetails['recieving_ledger_id'];
                            $arrLedgerTransactionData['from_ledger_name'] = $arrTaxLedgerDetails['receiver_name'];
                        } else {
                            if (empty($arrCommonTaxLedgerDetails)) {
                                $arrCommonTaxLedgerDetails = $this->checkledgerExistNew(array('soc_id' => $data['soc_id'], 'ledger_name' => 'taxes', 'context' => MEMBER_CONTRI));
                            }
                            $arrLedgerTransactionData['from_ledger_id'] = $arrCommonTaxLedgerDetails['recieving_ledger_id'];
                            $arrLedgerTransactionData['from_ledger_name'] = $arrCommonTaxLedgerDetails['receiver_name'];
                        }
                        $arrLedgerEntry = $this->transactionLedgerEntry($arrLedgerTransactionData);
                        if (isset($arrLedgerEntry['error']) && !empty($arrLedgerEntry['error'])) {
                            $countLedgerEntry = 0;
                        }
                    }
                }
            }

            //Refund amount in case of payment
            if (!empty($arrInvoiceDetail['paid_amount']) && $arrInvoiceDetail['paid_amount'] > 0 && $arrInvoiceDetail['refund_option'] != 'credit_note') {

                //Getting bank/cash ledger details
                $arrAccountDetail = $this->getBankCashAccountDetail(array('soc_id' => $data['soc_id']));
                $arrLedgerAccountDetail = $this->getLedgerAccountDetail(array('account_detail' => $arrAccountDetail));
                $strNarration = '';
                if (!empty($arrLedgerAccountDetail)) {
                    $arrIncomeLedgerDetails['ledger_id'] = $arrLedgerAccountDetail['cash']['ledger_id'];
                    $arrIncomeLedgerDetails['ledger_name'] = $arrLedgerAccountDetail['cash']['ledger_name'];
                    if (strtolower($arrInvoiceDetail['refund_option']) == 'bank') {
                        if (!empty($arrInvoiceDetail['bank_account'])) {
                            $arrIncomeLedgerDetails['ledger_id'] = $arrInvoiceDetail['bank_account'];
                            $arrIncomeLedgerDetails['ledger_name'] = $arrLedgerAccountDetail['arrBank'][$arrInvoiceDetail['bank_account']];
                        } else {
                            $arrIncomeLedgerDetails['ledger_id'] = $arrLedgerAccountDetail['bank']['ledger_id'];
                            $arrIncomeLedgerDetails['ledger_name'] = $arrLedgerAccountDetail['bank']['ledger_name'];
                        }
                    }
                }

                $arrLedgerTransactionData['from_ledger_id'] = $arrUnitLedgerDetails['recieving_ledger_id'];
                $arrLedgerTransactionData['to_ledger_id'] = $arrIncomeLedgerDetails['ledger_id'];

                $arrLedgerTransactionData['transaction_amount'] = $arrInvoiceDetail['paid_amount'];
                $arrLedgerTransactionData['narration'] = 'Refund amount of invoice number ' . $arrInvoiceDetail['invoice_number'] . ' dated on ' . $this->getDisplayDate(explode(' ', $arrInvoiceDetail['created_date'])[0]);

                $arrLedgerTransactionData['from_ledger_name'] = $arrUnitLedgerDetails['receiver_name'];
                $arrLedgerTransactionData['to_ledger_name'] = $arrIncomeLedgerDetails['ledger_name'];

                $arrLedgerEntry = $this->transactionLedgerEntry($arrLedgerTransactionData);

                if (isset($arrLedgerEntry['error']) && !empty($arrLedgerEntry['error'])) {
                    $countLedgerEntry = 0;
                }
            }
        }
        return $countLedgerEntry;
    }

    public function transactionLedgerEntry($data=array())
    {
        $arrResponse = array();

        if (empty(trim($data['from_ledger_id'])) || empty(trim($data['to_ledger_id']))) 
        {
            return ['error' => true];
        }
        
        if(empty($data['transaction_date']))
        {
            $data['transaction_date'] = date('Y-m-d');
        }

        $transaction_id = $this->executeTransactionEntry($data);

        if($transaction_id){
            $arrResponse = array('success'=>true, 'transaction_id'=>$transaction_id);
        }else{
            $arrResponse = array('error'=>true);
        }
        
        return $arrResponse;
    }

    public function executeTransactionEntry($data)
    {
        $vouchers_array = [];
        if ((strpos($data['voucher_type'], '_') !== false) && (strpos($data['voucher_type'], '_') > 0)) {
            $vouchers_array = explode('_', $data['voucher_type']);
            $data['voucher_type'] = $vouchers_array[0];
        }

        $data['is_opening'] = 0;
        $data['transaction_from_id'] = '';
        $data['transaction_from_id'] = $this->_addTransactionEntry($data);

        if (!empty($data['transaction_from_id'])) {
            if (count($vouchers_array) >= 2) {
                $data['voucher_type'] = $vouchers_array[1];
            }
            if ($this->_addTransactionEntry($data)) {
                return $data['transaction_from_id'];
            } else {
                return false;
            }
        } else {
            return false;
        }
    }

    private function _addTransactionEntry($data)
    {
        $txn = new ChsoneLedgerTransaction();
        if (!empty($data['transaction_from_id'])) {
            $mode = "to";
            $data['transaction_type'] = "cr";
            $ledger_id = $data['to_ledger_id'];
            $ledger_name = $data['to_ledger_name'];
        } else {
            $mode = "from";
            $data['transaction_type'] = "dr";
            $ledger_id = $data['from_ledger_id'];
            $ledger_name = $data['from_ledger_name'];
        }
        //echo "s_opning".$is_opning;exit;
        if (!empty($data['is_opening']) && $data['is_opening'] == 1) {
            $txn_entrt = ChsoneLedgerTransaction::where('soc_id', $data['soc_id'])
                ->where('is_opening_balance', 1)
                ->where('ledger_account_id', $ledger_id)
                ->first();

            $txn->txn_id = (isset($txn_entrt->txn_id)  && !empty($txn_entrt->txn_id)) ? $txn_entrt->txn_id : '';
        }
        $txn->soc_id = $data['soc_id'];
        $txn->transaction_date = $data['transaction_date'];
        $txn->ledger_account_id = $ledger_id;
        $txn->ledger_account_name = $ledger_name;
        $txn->voucher_type = $data['voucher_type'];
        $txn->transaction_type = $data['transaction_type']; //$ledger_name_obj->nature_of_account;//$txn_type;
        $txn->payment_mode = $data['mode_of_payment'];
        $txn->payment_reference = $data['payment_reference'];
        $txn->transaction_amount = $data['transaction_amount'];
        $txn->other_reference_id = $data['other_payment_ref'];
        $txn->txn_from_id = $data['transaction_from_id'];
        $txn->memo_desc = $data['narration'];
        $txn->is_opening_balance = $data['is_opening'];
        $txn->is_reconciled = (!empty($data['is_reconciled'])) ? $data['is_reconciled'] : 0;;
        $txn->created_by = (!empty($data['user_id'])) ? $data['user_id'] : 0;
        $txn->added_on = date("Y-m-d H:i:s");
        $txn->voucher_reference_number = (!empty($data['voucher_reference_number'])) ? $data['voucher_reference_number'] : '';
        $txn->voucher_reference_id = (!empty($data['voucher_reference_id'])) ? $data['voucher_reference_id'] : '';
        $txn->is_cancelled = (!empty($data['is_cancelled'])) ? 1 : 0;

        if ($txn->save()) {
            return $txn->txn_id;
        } else {
            return false;
        }
    }

    public function getTaxLedgerName($data = array()) {
        $ledgerName = '';
        if (!empty($data['taxClassDetail'])) {
            if (!empty($data['taxClassDetail']['tax_rule'])) {
                $ledgerName .= trim(str_replace(' ', '', ucwords($data['taxClassDetail']['tax_rule'])));
            }
            if (isset($data['taxClassDetail']['tax_rate'])) {
                $ledgerName .= trim(bcdiv($data['taxClassDetail']['tax_rate'], 1, 2)) . ucfirst(trim($data['taxClassDetail']['tax_rate_type']));
            }
        }
        return $ledgerName;
    }

    public function getBankCashAccountDetail($data = array())
    {
        $arrAccountDetail = array();

        $query = $this->tenantDB()->table('chsone_grp_ledger_tree as grpLedger')
        ->select(
            'grpLedger.ledger_account_id',
            'grpLedger.ledger_account_name',
            'grpLedger.context',
            'account.default_account',
            'account.default_bank_for_incidental',
            'account.default_bank_for_nonmember',
            'account.bank_name',
            'account.account_number',
            'account.bank_address',
            'account.bank_city',
            'account.bank_ifsc',
            'account.account_id'
        )
        ->join('chsone_accounts_master as account', 'grpLedger.ledger_account_id', '=', 'account.ledger_account_id')
        ->where('grpLedger.soc_id', $data['soc_id'])
        ->where('grpLedger.entity_type', 'ledger')
        ->where('grpLedger.status', 1);


        if (isset($data['only_bank'])) {
            $query->where('grpLedger.context', 'bank');
        } else {
            $query->whereIn('grpLedger.context', ['cash', 'bank']);
        }

        // Execute the query
        $resultset = $query->get();
        $arrAccountDetail = json_decode(json_encode($resultset), true);
        return $arrAccountDetail;
    }

    public function getLedgerAccountDetail($data = array())
    {
        $arrAccountDetail = array(
            'cash' => array(),
            'bank' => array(),
            'arrBank' => array()
        );
        if (!empty($data['account_detail'])) {
            foreach ($data['account_detail'] as $eachAccountDetail) {
                if (empty($arrAccountDetail['cash']) && strtolower($eachAccountDetail['context']) == 'cash') {
                    $arrAccountDetail['cash']['ledger_id'] = $eachAccountDetail['ledger_account_id'];
                    $arrAccountDetail['cash']['ledger_name'] = $eachAccountDetail['ledger_account_name'];
                    $arrAccountDetail['arrCash'][$eachAccountDetail['ledger_account_id']] = $eachAccountDetail['ledger_account_name'];
                } elseif (strtolower($eachAccountDetail['context']) == 'bank') {
                    if ($eachAccountDetail['default_account'] == 1) {
                        $arrAccountDetail['bank']['ledger_id'] = $eachAccountDetail['ledger_account_id'];
                        $arrAccountDetail['bank']['ledger_name'] = $eachAccountDetail['ledger_account_name'];
                    }
                    $arrAccountDetail['arrBank'][$eachAccountDetail['ledger_account_id']] = $eachAccountDetail['ledger_account_name'];
                }
                if (isset($data['default_bank_incidental']) && $data['default_bank_incidental'] && $eachAccountDetail['default_bank_for_incidental'] == 1) {
                    $arrAccountDetail['bank']['ledger_id'] = $eachAccountDetail['ledger_account_id'];
                    $arrAccountDetail['bank']['ledger_name'] = $eachAccountDetail['ledger_account_name'];
                }
                if (isset($data['default_bank_nonmember']) && $data['default_bank_nonmember'] && $eachAccountDetail['default_bank_for_nonmember'] == 1) {
                    $arrAccountDetail['bank']['ledger_id'] = $eachAccountDetail['ledger_account_id'];
                    $arrAccountDetail['bank']['ledger_name'] = $eachAccountDetail['ledger_account_name'];
                }
            }
            if (empty($arrAccountDetail['bank'])) {
                foreach ($arrAccountDetail['arrBank'] as $key => $value) {
                    $arrAccountDetail['bank']['ledger_id'] = $key;
                    $arrAccountDetail['bank']['ledger_name'] = $value;
                    break;
                }
            }
        }

        return $arrAccountDetail;
    }

    public function getPrimaryMemberByUnitId($data = array())
    {
        $resultset = $this->tenantDB()->table('chsone_member_master as memberMaster')
        ->select('memberMaster.id',
            'memberMaster.member_first_name',
            'memberMaster.member_last_name')
        ->join('chsone_units_master as units', 'units.unit_id', '=', 'memberMaster.fk_unit_id')
        ->join('chsone_member_type_master as memberType', 'memberType.member_type_id', '=', 'memberMaster.member_type_id')
        ->where('memberMaster.soc_id', $data['soc_id'])
        ->where('memberType.member_type_name', 'Primary')
        ->where('memberMaster.status', 1)
        ->where('units.is_allotted', 1)
        ->where('units.unit_id', $data['unit_id'])
        ->get();
        
        // return $resultset->toArray(); // If you need it as an array
        return $resultset;
    }

    public function saveCreditAccountResponse($data = array())
    {
        $response = ['error' => false, 'data' => null];

        try {
            if (empty($data) || (!isset($data['auth']))) {
                $this->status = 'error';
                $this->message = 'Required session values in auth';
                $this->statusCode = 400;
                return;
            }
            $user = $data['user'];
            $id = $data['id'];
            $username = (!empty($data['username']) ? $data['username'] : null);

            $data = $data['data'];
            $data['username'] = $username;
            $data['user'] = $user;

            if ($id) {
                $cr = ChsoneCreditAccount::where('credit_account_id', $id)
                    ->where('soc_id', $data['soc_id'])
                    ->first();

                $cr->updated_by = $user;
                $cr->updated_date = date('Y-m-d H:i:s');
                $cr->use_credit_after = (!empty($data['adjustable_date']) ? $data['adjustable_date'] : null);
                $cr->is_locked = 0;
                $cr->use_credit_for = (!empty($data['used_for']) ? $data['used_for'] : null);

                $cr->is_invoice_rectification = ((isset($data['is_invoice_rectification']) && !empty($data['is_invoice_rectification'])) ? $data['is_invoice_rectification'] : null);
                $cr->income_account_id = ((isset($data['income_account_ledger_id']) && !empty($data['income_account_ledger_id'])) ? $data['income_account_ledger_id'] : null);
                $cr->narration = $data['narration'];
                $cr->use_credit = (isset($data['credit_used_type']) && !empty($data['credit_used_type']) ? $data['credit_used_type'] : 'adjustable');
                // Save data
                if ($cr->save()) {
                    $response['data'] = $cr->credit_account_id;
                } else {
                    $response['error'] = true;
                    $response['data'] = ['message' => $cr->getErrors()];
                }
            } else {
                if ($data['credit_used_type'] == 'both') {
                    $data['credit_used_type'] = 'adjustable';
                    $data['payment_amount'] = $data['adjustable_amount'];
                    $saveResponse = $this->saveCreditNote($data);
                    if ($saveResponse['error'] == false) {
                        $data['credit_used_type'] = 'refundable';
                        $data['payment_amount'] = $data['refundable_amount'];
                        $data['used_for'] = NULL;
                        $data['adjustable_date'] = NULL;

                        $saveResponse = $this->saveCreditNote($data);
                        if ($saveResponse['error'] == false) {
                            $response['data'] = $saveResponse['id'];
                        }
                    }
                } else {
                    $saveResponse = $this->saveCreditNote($data);
                    if ($saveResponse['error'] == false) {
                        $response['data'] = $saveResponse['id'];
                    }
                }
            }
        } catch (Exception $e) {
            $response['error'] = true;
            $response['data'] = ['message' => $e->getMessage()];
        } finally {
            if (empty($response['data'])) {
                $response['data'] = ['message' => 'working is ok'];
                $response['error'] = false;
            }
            return response()->json($response);
        }
    }

    public function saveCreditNote($data)
    {
        $cr = new ChsoneCreditAccount();
        $cr->created_by = $data['user'] ?? 0;
        $cr->created_date = date('Y-m-d H:i:s');
        $cr->soc_id = $data['soc_id'];
        $cr->invoice_number = $data['invoice_no'] ?? $data['invoice_number'] ?? null;
        $cr->is_invoice_rectification = $data['is_invoice_rectification'] ?? null;
        $cr->income_account_id = $data['income_account_ledger_id'] ?? null;
        $cr->payment_tracker_id = $data['payment_tracker_id'] ?? null;
        $cr->account_id = $data['account_id'];
        $cr->account_name = $data['account_name'];
        $cr->account_context = $data['account_context'];
        $cr->amount = $data['payment_amount'];
        $cr->payment_mode = $data['payment_mode'] ?? null;
        $cr->payment_date = isset($data['payment_date']) ? $data['payment_date'] : null;
        $cr->transaction_type = $data['transaction_type'];
        $cr->narration = $data['narration'];
        $cr->use_credit = $data['credit_used_type'] ?? 'adjustable';
        $cr->use_credit_after = $data['adjustable_date'] ?? null;
        $cr->is_locked = $data['is_locked'] ?? 0;
        $cr->use_credit_for = $data['used_for'] ?? null;
        $cr->reference_no = $data['transaction_reference'] ?? null;
        $cr->context = $data['context'] ?? 'system';
        $cr->created_name = $data['username'] ?? null;
        // Save data
        if ($cr->save()) {
            $data['error'] = false;
            $data['id'] = $cr->credit_account_id;
            return $data;
        } else {
            $data['error'] = true;
            $data['message'] = array('message' => $cr->getMessages());
            return $data;
        }
    }
}
