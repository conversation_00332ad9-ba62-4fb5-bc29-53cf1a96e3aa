<?php

namespace App\Console\Commands\Actions\Income;

use Illuminate\Console\Command;
use App\Console\Commands\Action;
use Illuminate\Support\Facades\DB;
class getUnpaidInvoicesDataSource extends Action
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'datasource:getUnpaidInvoices {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */
    public function apply()
    {
        $unit_id = $this->input['unit_id'];

        // check if unit id is valid or not from chsone_units_master table
        $unit = $this->tenantDB()->table('chsone_units_master')
            ->where('unit_id', $unit_id)
            ->get();
        
        if(count($unit) == 0) {
            $this->status = 'error';
            $this->message = 'Please provide a valid unit id';
            $this->statusCode = 400;
            $this->data = [];
            return;
        }

        // get the list of unpaid invoices from income_unit_invoices table where unit_id = $unit_id and status in ('unpaid', 'partialpaid')
        // $obj = $this->tenantDB()->table('income_unit_invoices AS unitInvoices')
        // ->select(
        //     'unit_invoice_id as id',
        //     'unit_invoice_id',
        //     'unitInvoices.fk_unit_id as unit_id',
        //     'units.soc_building_name',
        //     'unit_flat_number',
        //     'unitInvoices.invoice_number',
        //     'unitInvoices.created_date',
        //     DB::raw('CONCAT(DATE_FORMAT(unitInvoices.created_date, "%d/%m/%Y")) AS created_date'),
        //     'due_date',
        //     DB::raw('CONCAT(DATE_FORMAT(unitInvoices.due_date, "%d/%m/%Y")) AS due_date'),
        //     'principal_amount',
        //     'interest_amount',
        //     'advance_amount',
        //     'roundoff_amount',
        //     'payment_status'
        // )
        // ->leftJoin('income_invoice_particular AS particulars', 'unitInvoices.unit_invoice_id', '=', 'particulars.fk_unit_invoice_id')
        // ->leftJoin('chsone_units_master AS units', 'unitInvoices.fk_unit_id', '=', 'units.unit_id')
        // ->where('unitInvoices.fk_unit_id', $unit_id)
        // ->where('unitInvoices.status', '!=', 'cancelled')
        // ->whereIn('unitInvoices.payment_status', ['unpaid', 'partialpaid'])
        // ->groupBy('particulars.fk_unit_invoice_id')
        // ->orderBy('unitInvoices.unit_invoice_id')
        // ->selectRaw("SUM(amount) as amount")
        // ->selectRaw("SUM(tax_applicable) as tax_applicable")
        // ->selectRaw("SUM(tax_exemptions) as tax_exemptions")
        // ->selectRaw("(SELECT SUM(payment_amount) FROM income_invoice_payment WHERE fk_unit_invoice_id = unitInvoices.unit_invoice_id) AS payment_amount")
        // ->selectRaw("(SELECT SUM(writeoff_amount) FROM income_invoice_payment WHERE fk_unit_invoice_id = unitInvoices.unit_invoice_id) AS writeoff_amount")
        // ->selectRaw("(SELECT SUM(tds_deducted) FROM income_invoice_payment WHERE fk_unit_invoice_id = unitInvoices.unit_invoice_id) AS tds_deducted")
        // ->selectRaw("(SELECT SUM(late_payment_charges) FROM income_invoice_payment WHERE fk_unit_invoice_id = unitInvoices.unit_invoice_id) AS late_payment_charges")
        // ->selectRaw("(SELECT IFNULL(SUM(tax_amount), 0) FROM chsone_tax_log WHERE invoice_number = unitInvoices.invoice_number) AS tax_amount")
        // ->selectRaw("
        //     (
        //         SUM(amount) + SUM(tax_applicable) + SUM(tax_exemptions) + IFNULL(principal_amount, 0)
        //     ) AS invoice_sub_total
        // ")
        // ->selectRaw("
        //     (
        //         SUM(amount) + SUM(tax_applicable) + SUM(tax_exemptions) + interest_amount + IFNULL(principal_amount, 0) +
        //         (SELECT IFNULL(SUM(tax_amount), 0) FROM chsone_tax_log WHERE invoice_number = unitInvoices.invoice_number)
        //     ) AS invoice_total
        // ")
        // ->selectRaw("
        //     (
        //         (SELECT IFNULL(SUM(payment_amount),0) FROM income_invoice_payment WHERE fk_unit_invoice_id = unitInvoices.unit_invoice_id) +
        //         (SELECT IFNULL(SUM(writeoff_amount),0) FROM income_invoice_payment WHERE fk_unit_invoice_id = unitInvoices.unit_invoice_id) +
        //         (SELECT IFNULL(SUM(tds_deducted),0) FROM income_invoice_payment WHERE fk_unit_invoice_id = unitInvoices.unit_invoice_id) +
        //         (SELECT IFNULL(SUM(late_payment_charges),0) FROM income_invoice_payment WHERE fk_unit_invoice_id = unitInvoices.unit_invoice_id)
        //     ) AS total_paid_amount
        // ")
        // ->selectRaw("
        //     CASE
        //         WHEN 
        //         (
        //             ROUND(
        //                 (
        //                     SUM(amount) + SUM(tax_applicable) + SUM(tax_exemptions) + interest_amount + IFNULL(principal_amount, 0) +
        //                     (SELECT IFNULL(SUM(tax_amount), 0) FROM chsone_tax_log WHERE invoice_number = unitInvoices.invoice_number)
        //                 ) + roundoff_amount 
        //                 - advance_amount 
        //                 - (
        //                     (SELECT IFNULL(SUM(payment_amount), 0) FROM income_invoice_payment WHERE fk_unit_invoice_id = unitInvoices.unit_invoice_id)
        //                     + (SELECT IFNULL(SUM(writeoff_amount), 0) FROM income_invoice_payment WHERE fk_unit_invoice_id = unitInvoices.unit_invoice_id)
        //                     + (SELECT IFNULL(SUM(tds_deducted), 0) FROM income_invoice_payment WHERE fk_unit_invoice_id = unitInvoices.unit_invoice_id)
        //                     + (SELECT IFNULL(SUM(late_payment_charges), 0) FROM income_invoice_payment WHERE fk_unit_invoice_id = unitInvoices.unit_invoice_id)
        //                 )
        //             , 2)
        //         ) < 0
        //         THEN 0
        //         ELSE 
        //         ROUND(
        //             (
        //                 SUM(amount) + SUM(tax_applicable) + SUM(tax_exemptions) + interest_amount + IFNULL(principal_amount, 0) +
        //                 (SELECT IFNULL(SUM(tax_amount), 0) FROM chsone_tax_log WHERE invoice_number = unitInvoices.invoice_number)
        //             ) + roundoff_amount 
        //             - advance_amount 
        //             - (
        //                 (SELECT IFNULL(SUM(payment_amount), 0) FROM income_invoice_payment WHERE fk_unit_invoice_id = unitInvoices.unit_invoice_id)
        //                 + (SELECT IFNULL(SUM(writeoff_amount), 0) FROM income_invoice_payment WHERE fk_unit_invoice_id = unitInvoices.unit_invoice_id)
        //                 + (SELECT IFNULL(SUM(tds_deducted), 0) FROM income_invoice_payment WHERE fk_unit_invoice_id = unitInvoices.unit_invoice_id)
        //                 + (SELECT IFNULL(SUM(late_payment_charges), 0) FROM income_invoice_payment WHERE fk_unit_invoice_id = unitInvoices.unit_invoice_id)
        //             )
        //         , 2)
        //     END AS invoice_balance_due
        // ");

        $obj = $this->tenantDB()->table('income_unit_invoices AS unitInvoices')
        ->select(
            'unit_invoice_id as id',
            'unit_invoice_id',
            'unitInvoices.fk_unit_id as unit_id',
            'units.soc_building_name',
            'unit_flat_number',
            'unitInvoices.invoice_number',
            'unitInvoices.created_date',
            DB::raw('CONCAT(DATE_FORMAT(unitInvoices.created_date, "%d/%m/%Y")) AS created_date'),
            'due_date',
            DB::raw('CONCAT(DATE_FORMAT(unitInvoices.due_date, "%d/%m/%Y")) AS due_date'),
            // Conditional principal_amount
            DB::raw("CASE
                WHEN unitInvoices.unit_invoice_id = (
                    SELECT MIN(i2.unit_invoice_id)
                    FROM income_unit_invoices i2
                    WHERE i2.fk_unit_id = unitInvoices.fk_unit_id
                    AND i2.status != 'cancelled'
                )
                THEN unitInvoices.principal_amount
                ELSE 0
            END AS principal_amount"),
            'interest_amount',
            'advance_amount',
            'roundoff_amount',
            'payment_status'
        )
        ->leftJoin('income_invoice_particular AS particulars', 'unitInvoices.unit_invoice_id', '=', 'particulars.fk_unit_invoice_id')
        ->leftJoin('chsone_units_master AS units', 'unitInvoices.fk_unit_id', '=', 'units.unit_id')
        ->where('unitInvoices.fk_unit_id', $unit_id)
        ->where('unitInvoices.status', '!=', 'cancelled')
        ->whereIn('unitInvoices.payment_status', ['unpaid', 'partialpaid'])
        ->groupBy('particulars.fk_unit_invoice_id')
        ->orderBy('unitInvoices.unit_invoice_id')
        ->selectRaw("SUM(amount) as amount")
        ->selectRaw("SUM(tax_applicable) as tax_applicable")
        ->selectRaw("SUM(tax_exemptions) as tax_exemptions")
        ->selectRaw("(SELECT SUM(payment_amount) FROM income_invoice_payment WHERE fk_unit_invoice_id = unitInvoices.unit_invoice_id) AS payment_amount")
        ->selectRaw("(SELECT SUM(writeoff_amount) FROM income_invoice_payment WHERE fk_unit_invoice_id = unitInvoices.unit_invoice_id) AS writeoff_amount")
        ->selectRaw("(SELECT SUM(tds_deducted) FROM income_invoice_payment WHERE fk_unit_invoice_id = unitInvoices.unit_invoice_id) AS tds_deducted")
        ->selectRaw("(SELECT SUM(late_payment_charges) FROM income_invoice_payment WHERE fk_unit_invoice_id = unitInvoices.unit_invoice_id) AS late_payment_charges")
        ->selectRaw("(SELECT IFNULL(SUM(tax_amount), 0) FROM chsone_tax_log WHERE invoice_number = unitInvoices.invoice_number) AS tax_amount")
        // invoice_sub_total with conditional principal_amount
        ->selectRaw("
            (
                SUM(amount) + SUM(tax_applicable) + SUM(tax_exemptions) +
                (
                    CASE
                        WHEN unitInvoices.unit_invoice_id = (
                            SELECT MIN(i2.unit_invoice_id)
                            FROM income_unit_invoices i2
                            WHERE i2.fk_unit_id = unitInvoices.fk_unit_id
                            AND i2.status != 'cancelled'
                        )
                        THEN unitInvoices.principal_amount
                        ELSE 0
                    END
                )
            ) AS invoice_sub_total
        ")
        // invoice_total with conditional principal_amount
        ->selectRaw("
            (
                SUM(amount) + SUM(tax_applicable) + SUM(tax_exemptions) + interest_amount +
                (
                    CASE
                        WHEN unitInvoices.unit_invoice_id = (
                            SELECT MIN(i2.unit_invoice_id)
                            FROM income_unit_invoices i2
                            WHERE i2.fk_unit_id = unitInvoices.fk_unit_id
                            AND i2.status != 'cancelled'
                        )
                        THEN unitInvoices.principal_amount
                        ELSE 0
                    END
                ) +
                (SELECT IFNULL(SUM(tax_amount), 0) FROM chsone_tax_log WHERE invoice_number = unitInvoices.invoice_number)
            ) AS invoice_total
        ")
        ->selectRaw("
            (
                (SELECT IFNULL(SUM(payment_amount),0) FROM income_invoice_payment WHERE fk_unit_invoice_id = unitInvoices.unit_invoice_id) +
                (SELECT IFNULL(SUM(writeoff_amount),0) FROM income_invoice_payment WHERE fk_unit_invoice_id = unitInvoices.unit_invoice_id) +
                (SELECT IFNULL(SUM(tds_deducted),0) FROM income_invoice_payment WHERE fk_unit_invoice_id = unitInvoices.unit_invoice_id) +
                (SELECT IFNULL(SUM(late_payment_charges),0) FROM income_invoice_payment WHERE fk_unit_invoice_id = unitInvoices.unit_invoice_id)
            ) AS total_paid_amount
        ")
        // invoice_balance_due with conditional principal_amount
        ->selectRaw("
            CASE
                WHEN 
                (
                    ROUND(
                        (
                            SUM(amount) + SUM(tax_applicable) + SUM(tax_exemptions) + interest_amount +
                            (
                                CASE
                                    WHEN unitInvoices.unit_invoice_id = (
                                        SELECT MIN(i2.unit_invoice_id)
                                        FROM income_unit_invoices i2
                                        WHERE i2.fk_unit_id = unitInvoices.fk_unit_id
                                        AND i2.status != 'cancelled'
                                    )
                                    THEN unitInvoices.principal_amount
                                    ELSE 0
                                END
                            ) +
                            (SELECT IFNULL(SUM(tax_amount), 0) FROM chsone_tax_log WHERE invoice_number = unitInvoices.invoice_number)
                        ) + roundoff_amount 
                        - advance_amount 
                        - (
                            (SELECT IFNULL(SUM(payment_amount), 0) FROM income_invoice_payment WHERE fk_unit_invoice_id = unitInvoices.unit_invoice_id)
                            + (SELECT IFNULL(SUM(writeoff_amount), 0) FROM income_invoice_payment WHERE fk_unit_invoice_id = unitInvoices.unit_invoice_id)
                            + (SELECT IFNULL(SUM(tds_deducted), 0) FROM income_invoice_payment WHERE fk_unit_invoice_id = unitInvoices.unit_invoice_id)
                            + (SELECT IFNULL(SUM(late_payment_charges), 0) FROM income_invoice_payment WHERE fk_unit_invoice_id = unitInvoices.unit_invoice_id)
                        )
                    , 2)
                ) < 0
                THEN 0
                ELSE 
                ROUND(
                    (
                        SUM(amount) + SUM(tax_applicable) + SUM(tax_exemptions) + interest_amount +
                        (
                            CASE
                                WHEN unitInvoices.unit_invoice_id = (
                                    SELECT MIN(i2.unit_invoice_id)
                                    FROM income_unit_invoices i2
                                    WHERE i2.fk_unit_id = unitInvoices.fk_unit_id
                                    AND i2.status != 'cancelled'
                                )
                                THEN unitInvoices.principal_amount
                                ELSE 0
                            END
                        ) +
                        (SELECT IFNULL(SUM(tax_amount), 0) FROM chsone_tax_log WHERE invoice_number = unitInvoices.invoice_number)
                    ) + roundoff_amount 
                    - advance_amount 
                    - (
                        (SELECT IFNULL(SUM(payment_amount), 0) FROM income_invoice_payment WHERE fk_unit_invoice_id = unitInvoices.unit_invoice_id)
                        + (SELECT IFNULL(SUM(writeoff_amount), 0) FROM income_invoice_payment WHERE fk_unit_invoice_id = unitInvoices.unit_invoice_id)
                        + (SELECT IFNULL(SUM(tds_deducted), 0) FROM income_invoice_payment WHERE fk_unit_invoice_id = unitInvoices.unit_invoice_id)
                        + (SELECT IFNULL(SUM(late_payment_charges), 0) FROM income_invoice_payment WHERE fk_unit_invoice_id = unitInvoices.unit_invoice_id)
                    )
                , 2)
            END AS invoice_balance_due
        ");

        $obj = $this->filter($obj);

        $result = $obj->get();

        $this->data = $result;
    }
}
