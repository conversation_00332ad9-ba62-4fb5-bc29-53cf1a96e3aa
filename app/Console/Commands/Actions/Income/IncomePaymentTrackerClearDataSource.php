<?php

namespace App\Console\Commands\Actions\Income;

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Config;
use App\Console\Commands\Action;
use App\Http\Traits\NotificationTraits;
use App\Models\Tenants\ChsoneInvoiceGeneration;
use App\Models\Tenants\ChsoneGrpLedgerTree;
use App\Models\Tenants\ChsoneInvoiceSetting;
use App\Models\Tenants\ChsoneMembersMaster;
use App\Models\Tenants\ChsoneMemberTypeMaster;
use App\Models\Tenants\ChsoneUnitsMaster;
use App\Models\Tenants\IncomeCommonBillingCharge;
use App\Models\Tenants\IncomeCommonBillingPayment;
use App\Models\Tenants\IncomeInvoiceGeneralSetting;
use App\Models\Tenants\IncomeInvoicePaymentTracker;
use App\Models\Tenants\IncomeInvoiceSetting;
use App\Models\Tenants\IncomeNonmemberBill;
use App\Models\Tenants\IncomeUnitInvoice;
use App\Models\Tenants\SocAccountFinancialYearMaster;
use Exception;

class IncomePaymentTrackerClearDataSource extends Action
{
    protected $constants;
    use NotificationTraits;

    public function __construct()
    {
        try {
            parent::__construct();
            $this->constants = Config::get("constants");
            /* (All your define() constants commented out remain unchanged) */
        } catch (\Exception $e) {
            dd("Error in __construct on line " . $e->getLine() . ": " . $e->getMessage());
        }
    }

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'datasource:IncomePaymentTrackerClearDataSource {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Payment Tracker Confirmation Clear data source';

    protected $formatter =  [];
    protected $formatterByKeys =  [];
    protected $mapper = [];

    public function checkIfPaymentTrackerIdExists($id)
    {
        try {
            $result1 = $this->tenantDB()->table('income_invoice_payment_tracker')
                ->where('id', $id)->exists();

            if ($result1) {
                // The ID exists in the table
                return "Payment Tracker ID $id exists in the table.";
            } else {
                // The ID does not exist in the table
                return false;
            }
        } catch (\Exception $e) {
            dd("Error in checkIfPaymentTrackerIdExists on line " . $e->getLine() . ": " . $e->getMessage());
        }
    }

    /**
     * Execute the console command.
     */
    public function apply()
    {
        try {
            $soc_id = $this->input['company_id'];
            $checkIfPaymentTrackerIdExists = $this->checkIfPaymentTrackerIdExists($this->input['payment_tracker_id']);
            if (!$checkIfPaymentTrackerIdExists) {
                $this->message = "Payment Tacker Id does not exists.";
                $this->status = 'error';
                $this->statusCode = 400;
                return false;
            }
            $arrGeneralSettingData = array('soc_id' => $soc_id, 'setting_key' => array('INCOME_PAYMENT_MODE'));
            $arrInvoiceGeneralSetting = $this->getInvoiceGeneralSetting($arrGeneralSettingData);
            $setting_value = (!empty($arrInvoiceGeneralSetting) && isset($arrInvoiceGeneralSetting[0]['setting_value']))
            ? $arrInvoiceGeneralSetting[0]['setting_value']
            : ''; 
    
            $payment_mode   =   explode(',', $setting_value);
    
            $arrPaymentMode =   array_combine($payment_mode, $payment_mode);
            $payment_tracker_id = $this->input['payment_tracker_id'];
            $chsoneInvoiceGeneration = new ChsoneInvoiceGeneration();
            $chsoneInvoiceGeneration->setInput($this->input);
            $creditNoteAddDataSource = new creditNoteAddDataSource();
            $incomeUnitInvoice = new IncomeUnitInvoice();
            $paymemberbillDataSource = new PaymemberbillDataSource();
            $updatePayNonMemberBillDataSource = new UpdatePayNonMemberBillDataSource();
            $action = 1;
            $succMsg = '';
            if (!empty($payment_tracker_id)) {

                $arrPaymentTrackerListener = array('soc_id' => $soc_id, 'payment_tracker_id' => $payment_tracker_id, 'status' => 'P', 'current_date' => date('Y-m-d'));
                if ($action == 3) {
                    $arrPaymentTrackerListener['status'] = 'R';
                } elseif ($action == 2) {
                    $arrPaymentTrackerListener['status'] = array('P', 'R');
                } elseif ($action == 4) {
                    $arrPaymentTrackerListener = array('soc_id' => $soc_id, 'payment_tracker_id' => $payment_tracker_id, 'status' => 'Y', 'payment_reversal' => 'y', 'bill_type' => array('member', 'creditaccount-member'));
                }
                $arrPostData = $this->getInvoicePaymentTrackerDetail($arrPaymentTrackerListener); // get all Unit details
                if (!empty($arrPostData)) {

                    $unit_id = $arrPostData['unit_id'];
                    $arrPostData['tds_amount'] = $arrPostData['tds_deducted'];
                    $arrDataListener['soc_id'] = $soc_id;
                    $arrDataListener['unit_id'] = $unit_id;
                    $arrPostData['payment_tracker_id'] = $arrPostData['id'];
                    if (!empty($unit_id)) {
                        $arrIncomeInvoiceMemberDetail = $this->getMemberDetail($arrDataListener); // get all Unit details
                        if (empty($arrIncomeInvoiceMemberDetail)) {
                            $this->message = 'Member not allotted to unit.';
                            $this->status = 'error';
                            $this->statusCode = 400;
                            return false;
                        }
                        $arrPostData['member_name'] = $arrIncomeInvoiceMemberDetail['member_first_name'] . ' ' . $arrIncomeInvoiceMemberDetail['member_last_name'];
                    }
                    $payment_date = $this->input["payment_date"] ?? $this->input["receipt_date"];
                    if (in_array($action, array(1, 3)) && !empty($payment_date)) {
                        if ($payment_date < $arrPostData['payment_date']) {
                            $this->message = 'Cheque clearance date cannot be lesser than Cheque submission date.';
                            $this->status = 'error';
                            $this->statusCode = 400;
                            return false;
                        }
                        if ($payment_date > date('Y-m-d')) {
                            $this->message = 'Date cannot be greater than todays date.';
                            $this->status = 'error';
                            $this->statusCode = 400;
                            return false;
                        }
                    }
                    
                    $succMsg = '';
                    if ($action == 1) {
                        if (!empty($arrPostData['payment_date'])) {
                            if (!empty($payment_date)) {
                                $arrGeneralInvoiceSetting = $this->getgeneralsetting(array('soc_id' => $soc_id));
                                if (!empty($arrGeneralInvoiceSetting['Late_Charges_Calculation_From']) && strtolower($arrGeneralInvoiceSetting['Late_Charges_Calculation_From']) == 'submission') {
                                    $arrPostData['clearance_date'] = $payment_date;
                                    $payment_date = $arrPostData['payment_date'];
                                }
                                if (!empty($arrGeneralInvoiceSetting['Late_Charges_Calculation_From']) && strtolower($arrGeneralInvoiceSetting['Late_Charges_Calculation_From']) == 'clearance') {
                                    $payment_date  = $payment_date;
                                    if ($payment_date >= $arrPostData['payment_date']) {
                                        $payment_date = $payment_date;
                                    }
                                }
                            } else {
                                $payment_date = $arrPostData['payment_date'];
                            }
                            $accountingDate = (isset($arrPostData['clearance_date']) && !empty(isset($arrPostData['clearance_date']))) ? $arrPostData['clearance_date'] : $payment_date;
                            $arrClosingAccountDetail = $this->getClosedAccountDetailByDate(array('soc_id' => $soc_id, 'bill_date' => $accountingDate));
                            if (!empty($arrClosingAccountDetail) && count($arrClosingAccountDetail) > 0) {
                                $this->message = 'Accounting has been closed, Cannot make payment.';
                                $this->status = 'error';
                                $this->statusCode = 400;
                                return false;
                            }
                        }
                        
                        $arrPostData['payment_date'] = $payment_date;
                        $arrDataListener['soc_id'] = $soc_id;
                        $arrDataListener['unit_id'] = $unit_id;
                        if (!empty($arrIncomeInvoiceMemberDetail)) {
                            $arrDataListener['member_id'] = $arrIncomeInvoiceMemberDetail['id'];
                        }
                        if ($arrPostData['bill_type'] == 'creditaccount-member') {
                            $data = $arrPostData['other_information'];
                            $data['payment_amount'] = $arrPostData['payment_amount'];
                            if (isset($data['credit_used_type']) && $data['credit_used_type'] == 'refundable') {
                                $arrToLedgerDetails = $this->checkledgerExistOrCreate(array('auth' => array("soc_id" => $soc_id), 'ledger_name' => $data['buildingUnit']));
                                $data['income_account_ledger_id'] = $arrToLedgerDetails['recieving_ledger_id'];
                                $narration = 'Refundable deposit recieved via ' . ucfirst($data['payment_mode']) . '(' . ucfirst($data['narration']) . ').';
                                $arrResponse = $this->createMemberLedger(array('unit_id' => $unit_id, 'arrPostData' => $arrPostData['other_information'], 'arrIncomeInvoiceMemberDetail' => $arrIncomeInvoiceMemberDetail, 'to_ledger' => $arrToLedgerDetails, 'narration' => $narration));
                            } else {
                                $arrResponse = $this->createMemberLedger(array('unit_id' => $unit_id, 'arrPostData' => $arrPostData['other_information'], 'arrIncomeInvoiceMemberDetail' => $arrIncomeInvoiceMemberDetail));
                            }
                            $data['payment_tracker_id'] = $payment_tracker_id;
                            $data['payment_date'] = $payment_date;
                            $chsoneInvoiceGeneration->saveCreditAccountResponse(array('auth' => true, 'process' => 'fetch', 'soc_id' => $soc_id, 'id' => 0, 'data' => $data, 'user' => $this->input['user_id'], 'username' => $this->input['user']['first_name'] . ' ' . $this->input['user']['last_name']));
                            $arrUpdatePaymentTrackerListener = array('soc_id' => $soc_id, 'payment_tracker_id' => $payment_tracker_id, 'status' => 'Y', 'invoice_number' => $arrPostData['member_paid_invoice']);
                            if (!empty($payment_date)) {
                                if (strrchr($payment_date, '/')) {
                                    $arrUpdatePaymentTrackerListener['payment_date'] = $payment_date;
                                } else {
                                    $arrUpdatePaymentTrackerListener['payment_date'] = $payment_date;
                                }
                            }
                            $arrResponse = $chsoneInvoiceGeneration->updateIncomePaymentTrackerStatus($arrUpdatePaymentTrackerListener);
                        } elseif ($arrPostData['bill_type'] == 'creditaccount-nonmember') {
                            $data = $arrPostData['other_information'];
                            $data['payment_amount'] = $arrPostData['payment_amount'];
                            if ($data['credit_used_type'] == 'refundable') {
                                $narration = 'Refundable deposit recieved via ' . ucfirst($data['payment_mode']) . '(' . ucfirst($data['narration']) . ').';
                                $chsoneInvoiceGeneration->saveCreditAccountResponse(array('auth' => true, 'process' => 'fetch', 'soc_id' => $soc_id, 'id' => 0, 'data' => $data, 'user' => $this->input['user_id'], 'username' => $this->input['user']['first_name'] . ' ' . $this->input['user']['last_name']));
                                $arrToLedgerDetails = $chsoneInvoiceGeneration->checkledgerExistOrCreate(array('auth' => array("soc_id" => $this->input['company_id']), 'ledger_name' => $data['account_id'] . '-' . $data['account_name']));
                                $data['income_account_ledger_id'] = $arrToLedgerDetails['recieving_ledger_id'];
                                $intBookerLedgerDetails = $creditNoteAddDataSource->payNonmemberBillLedger(array('postData' => $data, 'arrBookerLedgerDetails' => $arrToLedgerDetails, 'to_ledger' => $arrToLedgerDetails, 'narration' => $narration));
                            } else {
                                $narration = 'Advance payment received from ' . ucfirst($data['account_name']) . ' dated ' . $payment_date . ' through ' . $data['payment_mode'] . ' with transaction ref.(' . $data['transaction_reference'] . ', ' . $data['payment_instrument'] . ')';
                                $arrBookerLedgerDetails['recieving_ledger_id'] = $data['nonmember_ledger_id'];
                                $arrBookerLedgerDetails['receiver_name'] = $data['account_name'];
                                $intBookerLedgerDetails = $creditNoteAddDataSource->payNonmemberBillLedger(array('postData' => $arrPostData['other_information'], 'arrBookerLedgerDetails' => $arrBookerLedgerDetails, 'narration' => $narration));
                            }
                            if (!empty($intBookerLedgerDetails)) {
                                $arrResponse['status'] = 'success';
                            }
                            $data['payment_tracker_id'] = $payment_tracker_id;
                            $data['payment_date'] = $payment_date;
                            $chsoneInvoiceGeneration->saveCreditAccountResponse(array('auth' => true, 'process' => 'fetch', 'soc_id' => $soc_id, 'id' => 0, 'data' => $data, 'user' => $this->input['user_id'], 'username' => $this->auth['first_name'] . ' ' . $this->auth['last_name']));
                            $arrUpdatePaymentTrackerListener = array('soc_id' => $soc_id, 'payment_tracker_id' => $payment_tracker_id, 'status' => 'Y', 'invoice_number' => $arrPostData['member_paid_invoice']);
                            if (!empty($payment_date)) {
                                if (strrchr($payment_date, '/')) {
                                    $arrUpdatePaymentTrackerListener['payment_date'] = $payment_date;
                                } else {
                                    $arrUpdatePaymentTrackerListener['payment_date'] = $payment_date;
                                }
                            }
                            $arrResponse = $chsoneInvoiceGeneration->updateIncomePaymentTrackerStatus($arrUpdatePaymentTrackerListener);
                        } elseif (strtolower($arrPostData['bill_type']) == 'member') {
                            if (!empty($arrPostData['other_information']['member_detail']['bank_ledger'])) {
                                $arrPostData['bank_account'] = $arrPostData['other_information']['member_detail']['bank_ledger'];
                            }

                            if (!empty($arrPostData['writeoff_amount']) && $arrPostData['writeoff_amount'] > 0) {
                                $arrIncomeInvoiceDetail = $this->getUnitUnpaidInvoiceByInvoiceNumber(array('soc_id' => $soc_id, 'unit_id' => $unit_id, 'invoice_number' => $arrPostData['member_paid_invoice']));
                            } else {
                                $arrIncomeInvoiceDetail = $chsoneInvoiceGeneration->getUnitInvoiceUnpaidBill($arrDataListener);
                                if (!empty($arrIncomeInvoiceDetail)) {
                                    $memberPaidInvoices = $incomeUnitInvoice->getMemberPaidInvoices(array('payment_amount' => $arrPostData['payment_amount'], 'unpaidInvoiceDetail' => $arrIncomeInvoiceDetail));
                                    if (!empty($memberPaidInvoices)) {
                                        $arrPostData['member_paid_invoice'] = trim(implode(',', $memberPaidInvoices), ',');
                                    }
                                    $arrPostData['total_unpaid_amount'] = $arrIncomeInvoiceDetail['total_unpaid_invoice_amount'];
                                } else {
                                    $arrPostData['member_paid_invoice'] = '';
                                    $arrPostData['total_unpaid_amount'] = 0;
                                }
                            }
                            $arrPaymentTrackerListener = array('arrPostData' => $arrPostData, 'arrIncomeInvoiceMemberDetail' => $arrIncomeInvoiceMemberDetail, 'arrIncomeInvoiceDetail' => $arrIncomeInvoiceDetail);

                            if (!empty($arrPostData['writeoff_amount']) && $arrPostData['writeoff_amount'] > 0) {
                                $arrResponse = $paymemberbillDataSource->_invoiceWrtieoffPaymentTracker($unit_id, $arrPaymentTrackerListener);
                            } else {
                                $arrResponse = $paymemberbillDataSource->_invoicePaymentTracker($unit_id, $arrPaymentTrackerListener);
                            }
                            $arrUpdatePaymentTrackerListener = array('soc_id' => $soc_id, 'payment_tracker_id' => $payment_tracker_id, 'status' => 'Y', 'invoice_number' => $arrPostData['member_paid_invoice']);
                            if (!empty($payment_date)) {
                                if (strrchr($payment_date, '/')) {
                                    $arrUpdatePaymentTrackerListener['payment_date'] = $payment_date;
                                } else {
                                    $arrUpdatePaymentTrackerListener['payment_date'] = $payment_date;
                                }
                            }
                            $arrResponse = $chsoneInvoiceGeneration->updateIncomePaymentTrackerStatus($arrUpdatePaymentTrackerListener);
                        } elseif (strtolower($arrPostData['bill_type']) == 'common_bill') {
                            if (!empty($arrPostData['other_information'])) {
                                if (!empty($arrPostData['other_information']['common_bill_detail']) && !empty($arrPostData['other_information']['common_bill_detail']['payment_type']) && strtolower($arrPostData['other_information']['common_bill_detail']['payment_type']) == 'quickpay') {
                                    if (!empty($arrPostData['tds_amount'])) {
                                        $arrPostData['payment_amount'] += $arrPostData['tds_amount'];
                                    }

                                    $unpaidInvoiceDetail = $chsoneInvoiceGeneration->getIncidentInvoiceUnpaidBill($arrDataListener);
                                    $arrIncidentPaymentInvoices = $this->getIncidentPaymentInvoices(array('postData' => $arrPostData, 'arrUnpaidInvoices' => $unpaidInvoiceDetail));
                                    $arrCommonBillDetail = $creditNoteAddDataSource->getUnitDetailById(array('unit_id' => $unit_id));

                                    $invoicePaymentDetail = $arrIncidentPaymentInvoices['invoice_payment_detail'] ?? [];
                                    $arrResponse = $chsoneInvoiceGeneration->saveMultiCommonBillPayments(array('postData' => $arrPostData, 'arrUnpaidInvoicesDetail' => $invoicePaymentDetail, "auth" => $this->input['user']));
                                    if (!empty($arrResponse) && strtolower($arrResponse['status']) == 'success') {
                                        $arrPostData['member_paid_invoice'] = $arrPostData['invoice_number'] = $arrIncidentPaymentInvoices['member_paid_invoice'];
                                        $arrUnitDetail = $this->getUnitDetailById(array('unit_id' => $unit_id));
                                        $arrPostData['bank_account'] = $arrPostData['other_information']['common_bill_detail']['bank_ledger'];
                                        $arrPostData['bill_type_category'] = $arrPostData['other_information']['common_bill_detail']['bill_type_category'];
                                        $arrPostData['tds_amount'] = $arrPostData['tds_deducted'];
                                        $countLedgerEntry = $chsoneInvoiceGeneration->payCommonBillLedger(array('arrPostData' => $arrPostData, 'arrCommonBillDetail' => $arrUnitDetail));
                                        if ($countLedgerEntry == 0) {
                                            $arrResponse['status'] = 'success';
                                        }
                                    }
                                } elseif (!empty($arrPostData['other_information']['common_bill_detail']) && !empty($arrPostData['other_information']['common_bill_detail']['common_bill_id'])) {
                                    $arrPostData['common_bill_id'] = $arrPostData['other_information']['common_bill_detail']['common_bill_id'];
                                    $user_id = $this->input['user_id'] ?? '';
                                    $first_name = $this->input['user']['first_name'] ?? '';
                                    $last_name = $this->input['user']['first_name'] ?? '';
                                    $arrCommonListenerData = array('auth' => array('soc_id' => $this->input['company_id'], 'user_id' => $user_id, 'first_name' => $first_name, 'last_name' => $last_name), 'id' => $arrPostData['common_bill_id']);
                                    $arrCommonBillDetail = $chsoneInvoiceGeneration->getCommonBillById(array('auth' => array('soc_id' => $this->input['company_id']), 'id' => $arrPostData['common_bill_id']));
                                    if (!empty($arrCommonBillDetail)) {
                                        $arrCommonBillDetail = current($arrCommonBillDetail);
                                        $arrCommonBillPaymentDetails = $this->setCommonBillPayments(array('auth' => array('soc_id' => $this->input['company_id']), 'arrUnpaidInvoices' => $arrCommonBillDetail, 'arrPostData' => $arrPostData));
                                        if (!empty($arrCommonBillPaymentDetails['arrPostDetail'])) {
                                            $arrPostData = $arrCommonBillPaymentDetails['arrPostDetail'];
                                        }
                                        $arrCommonListenerData['PostData']['common_bill_id'] = $arrPostData['other_information']['common_bill_detail']['common_bill_id'];
                                        $arrCommonListenerData['PostData']['unit_id'] = $arrPostData['unit_id'];
                                        $arrCommonListenerData['PostData']['member_id'] = $arrDataListener['member_id'];
                                        $arrCommonListenerData['PostData']['invoice_number'] = $arrCommonBillDetail['invoice_number'];
                                        $arrCommonListenerData['PostData']['payment_mode'] = $arrPostData['payment_mode'];
                                        $arrCommonListenerData['PostData']['transaction_reference'] = $arrPostData['transaction_reference'];
                                        $arrCommonListenerData['PostData']['payment_instrument'] = $arrPostData['payment_instrument'];
                                        $arrCommonListenerData['PostData']['payment_amount'] = $arrPostData['payment_amount'];
                                        $arrCommonListenerData['PostData']['received_from'] = $arrPostData['received_from'];
                                        $arrCommonListenerData['PostData']['bank_account'] = $arrPostData['other_information']['common_bill_detail']['bank_ledger'];
                                        $arrCommonListenerData['PostData']['billing_type'] = $arrCommonBillDetail['billing_type'];
                                        $arrCommonListenerData['PostData']['payment_status'] = $arrCommonBillDetail['payment_status'];
                                        $arrCommonListenerData['PostData']['bill_type_category'] = $arrPostData['other_information']['common_bill_detail']['bill_type_category'];
                                        $arrCommonListenerData['PostData']['tds_amount'] = $arrPostData['tds_deducted'];
                                        $arrCommonListenerData['PostData']['payment_date'] = $arrPostData['payment_date'];
                                        $arrCommonListenerData['PostData']['payment_note'] = $arrPostData['payment_note'];
                                        $arrCommonListenerData['PostData']['clearance_date'] = $arrPostData['payment_date'];
                                        $arrCommonListenerData['PostData']['member_name'] = $arrPostData['member_name'];

                                        if (isset($arrPostData['paid_advance_amount'])) {
                                            $arrCommonListenerData['PostData']['paid_advance_amount'] = $arrPostData['paid_advance_amount'];
                                        }
                                        $arrCommonBillResponse = $this->saveCommonBillPayment($arrCommonListenerData);
                                        if (!empty($arrCommonBillResponse) && $arrCommonBillResponse['success'] == 1) {
                                            $countLedgerEntry = $chsoneInvoiceGeneration->payCommonBillLedger(array('auth' => array('soc_id' => $this->input['company_id']), 'defaultPostData' => $arrPostData, 'arrPostData' => $arrCommonListenerData['PostData'], 'arrCommonBillDetail' => $arrCommonBillDetail));
                                            if ($countLedgerEntry == 0) {
                                                $arrResponse['status'] = 'success';
                                                $arrPostData['member_paid_invoice'] = $arrCommonListenerData['PostData']['invoice_number'];
                                            }
                                        }
                                    }
                                }
                            }
                            
                        } elseif (strtolower($arrPostData['bill_type']) == 'nonmember') {
                            $arrNonmemberIncomeDetail = $chsoneInvoiceGeneration->getNonmemberIncomeDetail(array('soc_id' => $soc_id, 'bill_number' => $arrPostData['member_paid_invoice']));
                            if (!empty($arrPostData['other_information']['nonmember_detail']['bank_ledger'])) {
                                $arrPostData['bank_account'] = $arrPostData['other_information']['nonmember_detail']['bank_ledger'];
                            }
                            if (!empty($arrNonmemberIncomeDetail)) {
                                $arrPostData['payment_type'] = 'receipt';
                                $arrPostData['tax_deducted'] = 0;
                                if (!empty($arrNonmemberIncomeDetail)) {
                                    $arrPaymentDetail = $updatePayNonMemberBillDataSource->getNonmemberPaymentDetail(array('soc_id' => $soc_id, 'nonmember_bill_id' => $arrNonmemberIncomeDetail['nonmember_bill_id']));
                                    if (!empty($arrPaymentDetail)) {
                                        $totalPaidAmount = (float) $arrPaymentDetail['payment_amount'] + $arrPaymentDetail['tds_deducted'];
                                    }
                                }
                                $total_due_amount = (float) round(($arrNonmemberIncomeDetail['bill_amount'] + $arrNonmemberIncomeDetail['total_taxes']) - ($arrNonmemberIncomeDetail['total_deduction'] + $arrNonmemberIncomeDetail['discount_amount'] + $totalPaidAmount), 3);
                                $payment_status = 'partialpaid';
                                if ($arrPostData['payment_amount'] >= $total_due_amount) {
                                    $payment_status = 'paid';
                                }
                                $arrListnerPaymentData['invoice_details'] = $arrPostData;
                                $arrListnerPaymentData['invoice_details']['bill_number'] = $arrNonmemberIncomeDetail['bill_number'];
                                // $arrListnerPaymentData['nonmember_invoice_id'] = $arrNonmemberIncomeDetail['nonmember_bill_id'];
                                $addmemberincomepayment = $updatePayNonMemberBillDataSource->addadvancepayment(array('soc_id' => $soc_id, 'nonmember_invoice_id' => $arrNonmemberIncomeDetail['nonmember_bill_id'], 'arrListnerPaymentData' => $arrListnerPaymentData));
                                if ($addmemberincomepayment['success'] == true) {
                                    $updateIncomeStatus = $updatePayNonMemberBillDataSource->updateNonmemberIncomeStatus(array('soc_id' => $soc_id, 'status' => $payment_status, 'bill_number' => $arrNonmemberIncomeDetail['bill_number']));

                                    if (!empty($updateIncomeStatus) && strtolower($updateIncomeStatus['status'] == 'success')) {
                                        $arrNonmemberMasterDetail = $updatePayNonMemberBillDataSource->getNonmemberDetail(array('soc_id' => $soc_id, 'nonmember_id' => $arrNonmemberIncomeDetail['nonmember_id']));
                                        if (!empty($arrNonmemberMasterDetail['nonmember_ledger_id'])) {
                                            $arrBookerLedgerDetails = $chsoneInvoiceGeneration->checkledgerExistNew(array('soc_id' => $soc_id, 'ledger_id' => $arrNonmemberMasterDetail['nonmember_ledger_id']));
                                        } else {
                                            $arrBookerLedgerDetails = $updatePayNonMemberBillDataSource->createNonmemberLedgerExit(array('ledger_name' => 'Nonmember Income', 'context' => SUNDRY_DEBTORS_GROUP));
                                        }
                                        $intBookerLedgerDetails = $updatePayNonMemberBillDataSource->payNonmemberBillLedger(array('data' => $arrPostData, 'arrBookerLedgerDetails' => $arrBookerLedgerDetails));
                                        if (!empty($intBookerLedgerDetails)) {
                                            $arrResponse['status'] = 'success';
                                            $arrPostData['member_paid_invoice'] = $arrNonmemberIncomeDetail['bill_number'];
                                        }
                                    }
                                }
                            }
                        }
                        if (!empty($arrResponse) && strtolower($arrResponse['status']) == 'success') {
                            $arrUpdatePaymentTrackerListener = array('soc_id' => $soc_id, 'payment_tracker_id' => $payment_tracker_id, 'status' => 'Y', 'invoice_number' => $arrPostData['member_paid_invoice']);
                            if (!empty($payment_date)) {
                                if (strrchr($payment_date, '/')) {
                                    $arrUpdatePaymentTrackerListener['payment_date'] = $payment_date;
                                } else {
                                    $arrUpdatePaymentTrackerListener['payment_date'] = $payment_date;
                                }
                            }
                            $arrResponse = $chsoneInvoiceGeneration->updateIncomePaymentTrackerStatus($arrUpdatePaymentTrackerListener);
                        }
                        $succMsg = (strtolower($arrPostData['payment_mode']) == 'cheque') ? "Cheque payment has cleared successfully." : "Payment has cleared successfully.";
                    } elseif ($action == 2) {
                        if (strtolower($arrPostData['status']) == 'r') {
                            $arrEmailData['title'] = 'cheque_payment_not_received';
                            $arrUpdatePaymentTrackerListener = array('soc_id' => $soc_id, 'payment_tracker_id' => $payment_tracker_id, 'status' => 'not_received');
                        } else {
                            $arrEmailData['title'] = 'cheque_payment_bounced';
                            $arrUpdatePaymentTrackerListener = array('soc_id' => $soc_id, 'payment_tracker_id' => $payment_tracker_id, 'status' => 'N');
                        }
                        $arrResponse = $chsoneInvoiceGeneration->updateIncomePaymentTrackerStatus($arrUpdatePaymentTrackerListener);
                        if (!empty($arrResponse) && strtolower($arrResponse['status']) == 'success') {
                            if (strtolower($arrPostData['bill_type']) == 'member') {
                                $arrUpdateUnitInvoiceListener = array('soc_id' => $soc_id, 'invoice_number' => $arrPostData['member_paid_invoice'], 'status' => 'generated');
                                $arrResponse = $chsoneInvoiceGeneration->updateUnitInvoiceStatus($arrUpdateUnitInvoiceListener);
                            } elseif (strtolower($arrPostData['bill_type']) == 'nonmember') {
                                if (isset($arrPostData['other_information']['nonmember_detail']['advance_payment']) && !empty($arrPostData['other_information']['nonmember_detail']['advance_payment'])) {
                                    $arrUpdateUnitInvoiceListener = array('soc_id' => $soc_id, 'bill_number' => $arrPostData['member_paid_invoice'], 'status' => 'unpaid', 'advance_amount' => 0);
                                    $arrResponse = $chsoneInvoiceGeneration->updateNonmemberIncomeAdavanceStatus($arrUpdateUnitInvoiceListener);
                                }
                                $arrNonmemberIncomeDetail = $chsoneInvoiceGeneration->getNonmemberIncomeDetails(array('soc_id' => $soc_id, 'bill_number' => $arrPostData['member_paid_invoice']));
                                if (!empty($arrNonmemberIncomeDetail)) {
                                    $memberName = explode(' ', $arrNonmemberIncomeDetail['billed_name']);
                                    $arrIncomeInvoiceMemberDetail['member_first_name'] = current($memberName);
                                    $arrIncomeInvoiceMemberDetail['member_last_name'] = trim(str_replace($arrIncomeInvoiceMemberDetail['member_first_name'], '', $arrNonmemberIncomeDetail['billed_name']));
                                    $arrIncomeInvoiceMemberDetail['member_mobile_number'] = $arrNonmemberIncomeDetail['booker_mobile_number'];
                                    $arrIncomeInvoiceMemberDetail['member_email_id'] = $arrNonmemberIncomeDetail['booker_email_address'];
                                }
                            }
                        }

                        $succMsg = "Payment has not received.";
                        $arrEmailData['soc_id'] = $soc_id;
                        $arrEmailData['currency'] = 'Rs.';
                        $paymentDate = explode(' ', $arrPostData['created_date']);
                        $arrEmailData['payment_date'] = (!empty($paymentDate[0]) && strrchr($paymentDate[0], '-')) ? $paymentDate[0] : $paymentDate[0];
                        $arrEmailData['cheque_number'] = $arrPostData['transaction_reference'];
                        $arrEmailData['bank_name'] = $arrPostData['payment_instrument'];
                        $arrEmailData['total_amount'] = ($arrPostData['writeoff_amount'] > 0) ? number_format(round($arrPostData['writeoff_amount'], 2), 2, '.', '') : number_format(round($arrPostData['payment_amount'], 2), 2, '.', '');
                        $arrEmailData['member_name'] = ucwords($arrIncomeInvoiceMemberDetail['member_first_name'] . ' ' . $arrIncomeInvoiceMemberDetail['member_last_name']);
                        $arrEmailData['mobile_number'] = $arrIncomeInvoiceMemberDetail['member_mobile_number'];
                        $arrEmailData['email'] = array($arrEmailData['member_name'] => $arrIncomeInvoiceMemberDetail['member_email_id']);
                        $arrSocietyData = array('soc_id' => $soc_id, 'status' => 1);
                        $arrSocietyDetail = $chsoneInvoiceGeneration->getSocietyDetail($arrSocietyData);
                        $arrEmailData['society_name'] = ucwords($arrSocietyDetail['soc_name']);
                        $arrEmailData['chsone_site_link'] = $this->config->system->full_base_url_fe;
                        $arrEmailData['chsone_tiny_url'] = CHSONE_TINY_URL;
                        $emailResponse = $this->sendEmailFromTemplate($arrEmailData);
                        $smsResponse = $this->sendSmsFromTemplate($arrEmailData);
                    } elseif ($action == 3) {
                        $arrUpdatePaymentTrackerListener = array('soc_id' => $soc_id, 'payment_tracker_id' => $payment_tracker_id, 'status' => 'P');
                        $arrPostData['other_information'][$arrPostData['bill_type'] . '_detail']['bank_ledger'] = $arrPostData['bank_account'];
                        $arrUpdatePaymentTrackerListener['other_information'] = serialize($arrPostData['other_information']);

                        if (!empty($payment_date)) {
                            $arrUpdatePaymentTrackerListener['payment_date'] = $payment_date;
                        }
                        $arrResponse = $paymemberbillDataSource->updateIncomePaymentTrackerStatus($arrUpdatePaymentTrackerListener);
                        $succMsg = "Cheque payment has submitted successfully.";
                    } elseif ($action == 4) {
                        $arrPostData['reversal_note'] = $arrPostData["reversal_note"];
                        if (empty($arrPostData['reversal_note'])) {
                            $arrAjaxResonse = array('status' => 'error', 'message' => 'Reversal note has not given.');
                            echo json_encode($arrAjaxResonse);
                            return false;
                        }
                        $arrResponse = $chsoneInvoiceGeneration->paymentReversalProcess(array('arrPostData' => $arrPostData, 'arrMemberDetail' => $arrIncomeInvoiceMemberDetail));
                        $succMsg = $arrResponse['message'];
                    }
                }
            }
            $this->message = $succMsg;
        } catch (\Exception $e) {
            dd("Error in apply on line ". $e->getFile() . " ". $e->getLine() . ": " . $e->getMessage());
        }
    }
    public function saveCommonBillPayment($data = [])
    {
        try {
            $auth = $data['auth'] ?? '';
            $arrPostData = $data['PostData'];
            $chsoneInvoiceGeneration = new ChsoneInvoiceGeneration();
            $chsoneInvoiceGeneration->setInput($this->input);

            if (!empty($arrPostData['bill_from']) && strtolower($arrPostData['bill_from']) === 'facility' && !empty($arrPostData['member_unit_id'])) {
                $arrPostData['unit_id'] = $arrPostData['member_unit_id'];
            }

            $objCommonBillingPayment = new IncomeCommonBillingPayment();
            $objCommonBillingPayment->created_date = date('Y-m-d');
            $objCommonBillingPayment->created_by = $this->input['user_id'];
            $objCommonBillingPayment->soc_id = $this->input['company_id'];
            $objCommonBillingPayment->fk_common_bill_id = $arrPostData['common_bill_id'];
            $objCommonBillingPayment->unit_id = $arrPostData['unit_id'];

            if (!empty($arrPostData['tds_amount']) && $arrPostData['tds_amount'] > 0) {
                $objCommonBillingPayment->tds_deducted = $arrPostData['tds_amount'];
                $arrPostData['payment_amount'] = round($arrPostData['payment_amount'] - $arrPostData['tds_amount'], 3);
            }

            $objCommonBillingPayment->payment_amount = $arrPostData['payment_amount'] ?? 0;
            $objCommonBillingPayment->payment_mode = $arrPostData['payment_mode'] ?? null;
            $objCommonBillingPayment->transaction_reference = $arrPostData['transaction_reference'] ?? null;
            $objCommonBillingPayment->transaction_charges = $arrPostData['transaction_charges'] ?? 0;
            $objCommonBillingPayment->discount_amount = $arrPostData['discount_amount'] ?? 0;
            $objCommonBillingPayment->payment_instrument = $arrPostData['payment_instrument'] ?? null;
            $objCommonBillingPayment->payment_date = !empty($arrPostData['payment_date'])
                ? $arrPostData['payment_date']
                : date('Y-m-d');

            $finalArray = ['success' => false];

            try {
                if (!$objCommonBillingPayment->save()) {
                    $errors = $objCommonBillingPayment->getErrors()->all();
                    var_dump($errors);
                    return ['success' => false, 'errors' => $errors];
                }
            } catch (Exception $e) {
                dd('Caught InvalidArgumentException: ' . $e->getMessage() . ' on line ' . $e->getLine());
            }

            if (isset($arrPostData['member_unit_id']) && strtolower($arrPostData['bill_from']) === 'facility') {
                $finalArray['success'] = true;
                $finalArray['common_bill_id'] = $arrPostData['common_bill_id'];
            } elseif (!empty($arrPostData['payment_status'])) {
                $objCommonBillingCharges = IncomeCommonBillingCharge::where('soc_id', $this->input['company_id'])
                    ->where('id', $arrPostData['common_bill_id'])
                    ->where('fk_unit_id', $arrPostData['unit_id'])
                    ->first();

                if ($objCommonBillingCharges) {
                    $objCommonBillingCharges->payment_status = $arrPostData['payment_status'];
                    $objCommonBillingCharges->updated_date = date('Y-m-d');
                    $objCommonBillingCharges->updated_by = $auth['user_id'];

                    if (!$objCommonBillingCharges->save()) {
                        $finalArray['arrMessage'] = $objCommonBillingCharges->getErrors()->all();
                        return $finalArray;
                    } else {
                        $finalArray['success'] = true;
                        $finalArray['common_bill_id'] = $objCommonBillingPayment->id;
                    }
                }
            }

            if (!empty($arrPostData['paid_advance_amount'])) {
                $arrCreditData = [
                    'soc_id' => $this->input['company_id'],
                    'account_id' => $arrPostData['unit_id'],
                    'account_context' => 'unit',
                    'transaction_type' => 'cr',
                    'context' => 'system',
                    'credit_used_type' => 'adjustable',
                    'used_for' => 'incidental',
                    'payment_date' => !empty($arrPostData['payment_date'])
                        ? $arrPostData['payment_date']
                        : date('Y-m-d'),
                    'narration' => 'Amount Rs ' . $arrPostData['paid_advance_amount'] . ' has credited from Advance of ' . $arrPostData['invoice_number'],
                    'payment_amount' => $arrPostData['paid_advance_amount'],
                    'account_name' => $arrPostData['member_name'],
                ];

                $arrResponse = $chsoneInvoiceGeneration->saveCreditAccountResponse([
                    'auth' => true,
                    'process' => 'fetch',
                    'soc_id' => $arrCreditData['soc_id'],
                    'id' => '',
                    'data' => $arrCreditData,
                    'user' => $auth['user_id'],
                    'username' => $auth['first_name'] . ' ' . $auth['last_name']
                ]);

                if ($arrResponse['error']) {
                    $finalArray['success'] = false;
                    $finalArray['arrMessage'][] = 'Unable to create debit note';
                    return $finalArray;
                } else {
                    $finalArray['success'] = true;
                    $finalArray['common_bill_id'] = $objCommonBillingPayment->id;
                }
            }

            return $finalArray;
        } catch (\Exception $e) {
            dd("Error in saveCommonBillPayment on line " . $e->getLine() . ": " . $e->getMessage());
        }
    }

    /**
     * Set common bill payments
     */
    public function setCommonBillPayments($data = array())
    {
        try {
            $arrInvoiceDetail = array();
            $chsoneInvoiceGeneration = new ChsoneInvoiceGeneration();
            $chsoneInvoiceGeneration->setInput($this->input);
            if (!empty($data['arrUnpaidInvoices'])) {
                $eachInvoiceDetail = $data['arrUnpaidInvoices'];
                {
                    $eachInvoiceDetail['tax_amount'] = $chsoneInvoiceGeneration->getInvoiceTaxAmount(array('soc_id' => $this->input['company_id'], 'invoice_number' => $eachInvoiceDetail['invoice_number']));
                    $eachInvoiceDetail['due_amount'] = $eachInvoiceDetail['amount'];
                    $eachInvoiceDetail['due_amount'] += $eachInvoiceDetail['tax_amount'] + $eachInvoiceDetail['interest_amount'] + $eachInvoiceDetail['roundoff_amount'];
                    if ($eachInvoiceDetail['due_amount'] >= $eachInvoiceDetail['advance_amount']) {
                        if (empty($eachInvoiceDetail['advance_amount'])) {
                            $eachInvoiceDetail['advance_amount'] = 0;
                        }
                        $eachInvoiceDetail['due_amount'] = round((float) $eachInvoiceDetail['due_amount'] - (float) $eachInvoiceDetail['advance_amount'], 3);
                    } else {
                        $eachInvoiceDetail['due_amount'] = 0;
                    }
                    $eachInvoiceDetail['payment_detail'] = $chsoneInvoiceGeneration->getCommonBillingPayments(array('auth' => $data['auth'], 'commonBillData' => $eachInvoiceDetail, 'generateBy' => 'bill_number'));
                    if (!empty($eachInvoiceDetail['payment_detail'])) {
                        $eachInvoiceDetail['paid_amount'] = (float) round($eachInvoiceDetail['payment_detail']['payment_amount'] + $eachInvoiceDetail['payment_detail']['tds_deducted'], 3);

                        if ($eachInvoiceDetail['paid_amount'] >= $eachInvoiceDetail['due_amount']) {
                            $eachInvoiceDetail['due_amount'] = 0;
                        } else {
                            $eachInvoiceDetail['due_amount'] = round((float) $eachInvoiceDetail['due_amount'] - (float) $eachInvoiceDetail['paid_amount'], 3);
                        }
                    }

                    if (!empty($data['arrPostData'])) {
                        $arrPostData = $data['arrPostData'];
                        if (strtolower($eachInvoiceDetail['payment_status']) != 'paid' && $data['arrPostData']['payment_amount'] > 0) {
                            $arrPostData['payment_status'] = 'partialpaid';
                            if (empty($eachInvoiceDetail['paid_amount'])) {
                                $eachInvoiceDetail['paid_amount'] = 0;
                            }

                            if (($arrPostData['payment_amount'] + $eachInvoiceDetail['paid_amount'] + $eachInvoiceDetail['advance_amount']) >= ($eachInvoiceDetail['amount'] + $eachInvoiceDetail['tax_amount'])) {
                                $arrPostData['payment_status'] = 'paid';
                            }
                        }
                        if ($arrPostData['payment_amount'] > $eachInvoiceDetail['due_amount']) {
                            $arrPostData['paid_advance_amount'] = (float) round($arrPostData['payment_amount'] - $eachInvoiceDetail['due_amount'], 3);
                        }
                        $eachInvoiceDetail['arrPostDetail'] = $arrPostData;
                    }
                    $arrInvoiceDetail = $eachInvoiceDetail;
                }
            }
            return $arrInvoiceDetail;
        } catch (\Exception $e) {
            dd("Error in setCommonBillPayments on line " . $e->getLine() . ": " . $e->getMessage());
        }
    }

    public function getIncidentPaymentInvoices($data = array())
    {
        try {
            $arrIncidentPaymentDetail = array();
            if (!empty($data['postData']) && !empty($data['arrUnpaidInvoices'])) {
                $arrUnpaidInvoices = $data['arrUnpaidInvoices'];
                $postData = $data['postData'];
                $paidAmount = $postData['payment_amount'];
                if (!empty($arrUnpaidInvoices['invoice_detail'])) {
                    $arrTdsDetail = array();
                    if (!empty($postData['tds_amount'])) {
                        $tds_percentage = (float) round(($postData['tds_amount'] * 100) / $postData['payment_amount'], 3);
                    }
                    $i = 0;
                    $arrIncidentPaymentDetail['member_paid_invoice'] = '';
                    foreach ($arrUnpaidInvoices['invoice_detail'] as $eachInvoiceDetail) {
                        if (!empty($paidAmount) && $paidAmount > 0) {
                            $arrIncidentPaymentDetail[$i] = $eachInvoiceDetail;
                            $arrIncidentPaymentDetail['member_paid_invoice'] .= $eachInvoiceDetail['invoice_number'] . ',';

                            if ($paidAmount > $eachInvoiceDetail['due_amount']) {
                                $arrIncidentPaymentDetail[$i]['payment_status'] = 'paid';
                                $arrIncidentPaymentDetail[$i]['payment_amount'] = $eachInvoiceDetail['due_amount'];
                                $paidAmount = (float) round($paidAmount - $eachInvoiceDetail['due_amount'], 3);
                            } elseif ($paidAmount < $eachInvoiceDetail['due_amount']) {
                                $arrIncidentPaymentDetail[$i]['payment_status'] = 'partialpaid';
                                $arrIncidentPaymentDetail[$i]['payment_amount'] = $paidAmount;
                                $paidAmount = 0;
                            } elseif ($paidAmount == $eachInvoiceDetail['due_amount']) {
                                $arrIncidentPaymentDetail[$i]['payment_status'] = 'paid';
                                $arrIncidentPaymentDetail[$i]['payment_amount'] = $eachInvoiceDetail['due_amount'];
                                $paidAmount = 0;
                            }
                            $arrIncidentPaymentDetail[$i]['tds_amount'] = 0;
                            if (!empty($tds_percentage)) {
                                $arrIncidentPaymentDetail[$i]['tds_amount'] = (float) round(($tds_percentage * $arrIncidentPaymentDetail[$i]['payment_amount']) / 100, 3);
                            }

                            $arrIncidentPaymentDetail['invoice_payment_detail'][$i] = $arrIncidentPaymentDetail[$i];
                            if (count($arrUnpaidInvoices['invoice_detail']) == ($i + 1)) {
                                $arrIncidentPaymentDetail['invoice_payment_detail'][$i]['paid_advance_amount'] += $paidAmount;
                            }

                            unset($arrIncidentPaymentDetail[$i]);
                        }
                        $i++;
                    }
                    $arrIncidentPaymentDetail['member_paid_invoice'] = trim($arrIncidentPaymentDetail['member_paid_invoice'], ',');
                }
            } elseif (!empty($data['postData']) && $data['postData']['total_unpaid_amount'] == 0 && !empty($data['postData']['unit_id'])) {
                $arrIncidentPaymentDetail['invoice_payment_detail'][0]['id'] = 0;
                $arrIncidentPaymentDetail['invoice_payment_detail'][0]['fk_unit_id'] = $data['postData']['unit_id'];
                $arrIncidentPaymentDetail['invoice_payment_detail'][0]['tds_amount'] = $data['postData']['tds_amount'];
                $arrIncidentPaymentDetail['invoice_payment_detail'][0]['paid_advance_amount'] = $data['postData']['payment_amount'];
                $arrIncidentPaymentDetail['invoice_payment_detail'][0]['payment_amount'] = 0;
            }
            return $arrIncidentPaymentDetail;
        } catch (\Exception $e) {
            dd("Error in getIncidentPaymentInvoices on line " . $e->getLine() . ": " . $e->getMessage());
        }
    }

    public function getUnitUnpaidInvoiceByInvoiceNumber($data = array())
    {
        try {
            $arrAllUnitsInvoice = array();
            $todaysDate = date('Y-m-d');
            $invoice_number = str_replace(",", "','", $data['invoice_number']);
            $arrgetdata = array(
                "conditions" => " soc_id = '" . $data['soc_id'] . "' AND fk_unit_id = '" . $data['unit_id'] . "' AND payment_status IN ('unpaid', 'partialpaid') AND invoice_number IN ('" . $invoice_number . "')",
                "order" => "invoice_number asc"
            );

            $objUnitsInvoice = IncomeUnitInvoice::where($arrgetdata)->get();
            $chsoneInvoiceGeneration = new ChsoneInvoiceGeneration();
            $chsoneInvoiceGeneration->setInput($this->input);
            if (!empty($objUnitsInvoice)) {
                $arrUnitsInvoice = $objUnitsInvoice->toArray();
                if (!empty($arrUnitsInvoice)) {
                    $arrAllUnitsInvoice = $chsoneInvoiceGeneration->getUnpaidInvoiceDetail(array(
                        'arrUnitsInvoice' => $arrUnitsInvoice,
                        'member_id' => $data['member_id'],
                        'soc_id' => $data['soc_id'],
                        'unit_id' => $data['unit_id']
                    ));
                }
            }
            return $arrAllUnitsInvoice;
        } catch (\Exception $e) {
            dd("Error in getUnitUnpaidInvoiceByInvoiceNumber on line " . $e->getLine() . ": " . $e->getMessage());
        }
    }
    public function createMemberLedger($arrData = array())
    {
        try {
            $auth = $arrData['auth'] ?? '';
            $arrResponse = array(
                'status' => 'error'
            );
            $arrPostData = $arrData['arrPostData'];
            if (!empty($arrPostData)) {
                $arrIncomeInvoiceMemberDetail = $arrData['arrIncomeInvoiceMemberDetail'];
                if ((empty($arrPostData['total_unpaid_amount']) || $arrPostData['total_unpaid_amount'] <= 0) && empty($arrPostData['member_paid_invoice'])) {
                    if (isset($arrPostData['payment_amount']) && $arrPostData['payment_amount'] > 0) {
                        $countLedgerEntry = 0;
                        $arrPostData['soc_id'] = $this->input['company_id'];
                        if (!empty($arrIncomeInvoiceMemberDetail)) {
                            $arrPostData['unit_id'] = $arrIncomeInvoiceMemberDetail['fk_unit_id'];
                            $arrPostData['member_id'] = $arrIncomeInvoiceMemberDetail['id'];
                        }
                        if (!empty($arrPostData['payment_date']) && strrchr($arrPostData['payment_date'], '-')) {
                            $arrPostData['payment_date'] = $arrPostData['payment_date'];
                        }
                        if (ACCOUNT_MODULE_EXIST == 1) {
                            $arrListnerData = array(
                                'auth' => $auth,
                                'unit_id' => $arrData['unit_id']
                            );
                            $arrUnitDetails = $this->getUnitDetailById($arrListnerData);
                            if (!empty($arrUnitDetails)) {
                                $arrListnerData['ledger_id'] = $arrUnitDetails['ledger_account_id'];
                                $chsoneInvoiceGeneration = new ChsoneInvoiceGeneration();
                                $chsoneInvoiceGeneration->setInput($this->input);
                                $arrUnitLedgerDetails = $this->checkledgerExistNew($arrListnerData);
                                if (!empty($arrUnitLedgerDetails)) {
                                    $countLedgerEntry = $this->paymentLedgerEntry(array(
                                        'auth' => $auth,
                                        'arrPostData' => $arrPostData,
                                        'arrUnitLedgerDetails' => $arrUnitLedgerDetails,
                                        'from_ledger' => $arrData['from_ledger'],
                                        'to_ledger' => $arrData['to_ledger'],
                                        'narration' => $arrData['narration']
                                    ));
                                    if (!$countLedgerEntry) {
                                        $arrResponse['status'] = 'success';
                                    }
                                }
                            }
                        }
                    }
                    return $arrResponse;
                }
                return $arrResponse;
            }
        } catch (\Exception $e) {
            dd("Error in createMemberLedger on line " . $e->getLine() . ": " . $e->getMessage());
        }
    }

    public function paymentLedgerEntry($data = array())
    {
        try {
            $countLedgerEntry = 0;
            $chsoneInvoiceGeneration = new ChsoneInvoiceGeneration();
            $chsoneInvoiceGeneration->setInput($this->input);

            if (ACCOUNT_MODULE_EXIST == 1) {
                $auth = $data['auth'];
                $arrPostData = $data['arrPostData'];
                $arrUnitLedgerDetails = $data['arrUnitLedgerDetails'];

                if (isset($arrPostData['clearance_date']) && !empty($arrPostData['clearance_date'])) {
                    $arrPostData['payment_date'] = $arrPostData['clearance_date'];
                }

                if (empty($arrPostData['payment_date'])) {
                    $arrPostData['payment_date'] = date('Y-m-d');
                }
                if (!empty($arrPostData['payment_mode']) && !in_array(strtolower($arrPostData['payment_mode']), $this->constants['payment_mode_for_receipt_reversal'])) {
                    $arrInvoiceGeneralSetting = $this->getInvoiceGeneralSetting(array('soc_id' => $this->input['company_id'], 'setting_key' => array(trim($arrPostData['payment_instrument']) . '_PG_LEDGER_ID')));
                    if (!empty($arrInvoiceGeneralSetting[0]['setting_value'])) {
                        $arrIncomeAccounts = $chsoneInvoiceGeneration->checkledgerExistNew(array('auth' => $data['auth'] ?? '', 'ledger_id' => $arrInvoiceGeneralSetting[0]['setting_value']));
                        $arrIncomeAccounts['ledger_id'] = $arrIncomeAccounts['recieving_ledger_id'];
                        $arrIncomeAccounts['ledger_name'] = $arrIncomeAccounts['receiver_name'];
                    } else {
                        if (!empty($data['to_ledger'])) {
                            $arrIncomeAccounts['ledger_id'] = $data['to_ledger']['recieving_ledger_id'];
                            $arrIncomeAccounts['ledger_name'] = $data['to_ledger']['receiver_name'];
                        } else {
                            return 1;
                        }
                    }
                } else {
                    if (isset($data['arrUnitLedgerDetails']['suspenseLedgerDetails']) && !empty($data['arrUnitLedgerDetails']['suspenseLedgerDetails'])) {
                        $arrIncomeAccounts['ledger_id'] = $data['arrUnitLedgerDetails']['suspenseLedgerDetails']['recieving_ledger_id'];
                        $arrIncomeAccounts['ledger_name'] = $data['arrUnitLedgerDetails']['suspenseLedgerDetails']['receiver_name'];
                    } else {
                        $arrAccountDetail = $this->getBankCashAccountDetail(array(
                            'soc_id' => $this->input['company_id']
                        ));
                        $creditNoteAddDataSource = new CreditNoteAddDataSource();
                        $arrLedgerAccountDetail = $creditNoteAddDataSource->getLedgerAccountDetail(array(
                            'account_detail' => $arrAccountDetail
                        ));

                        if (!empty($arrLedgerAccountDetail)) {
                            $arrIncomeAccounts['ledger_id'] = $arrLedgerAccountDetail['cash']['ledger_id'];
                            $arrIncomeAccounts['ledger_name'] = $arrLedgerAccountDetail['cash']['ledger_name'];
                            if (strtolower($arrPostData['payment_mode']) != 'cash') {
                                if (!empty($arrPostData['bank_account'])) {
                                    $arrIncomeAccounts['ledger_id'] = $arrPostData['bank_account'];
                                    $arrIncomeAccounts['ledger_name'] = $arrLedgerAccountDetail['arrBank'][$arrPostData['bank_account']];
                                } else {
                                    $arrIncomeAccounts['ledger_id'] = $arrLedgerAccountDetail['bank']['ledger_id'];
                                    $arrIncomeAccounts['ledger_name'] = $arrLedgerAccountDetail['bank']['ledger_name'];
                                }
                            }
                        }
                    }
                }

                if ($arrPostData['payment_mode'] == 'cashtransfer') {
                    $paymentModeForNarration = DISPLAY_CASH_TRANSFER;
                } else {
                    $paymentModeForNarration = $arrPostData['payment_mode'];
                }

                $strNarration = '';
                if (!empty($arrPostData['payment_mode']) && in_array(strtolower($arrPostData['payment_mode']), $this->constants['payment_mode_for_clearance'])) {
                    $strNarration = ' with transaction ref. (' . $arrPostData['transaction_reference'] . ', ' . $arrPostData['payment_instrument'] . ')';
                } elseif (!empty($arrPostData['payment_mode']) && strtolower($arrPostData['payment_mode']) == 'cashtransfer') {
                    $strNarration = ' with payment ref. (' . $arrPostData['transaction_reference'] . ')';
                } elseif (!empty($arrPostData['payment_mode']) && !in_array(strtolower($arrPostData['payment_mode']), $this->constants['payment_mode_for_receipt_reversal'])) {
                    $strNarration = ' with transaction ref. (' . $arrPostData['transaction_reference'] . ')';
                    switch ($arrIncomeInvoicePaymentTracker['payment_mode']) {
                        case YES_BANK_PG:
                            $paymentModeForNarration = DISPLAY_YES_BANK;
                            break;
                        case YES_BANK_ECOLLECT:
                            $paymentModeForNarration = DISPLAY_YES_BANK_ECOLLECT;
                            break;
                        case MOBIKWIK_WALLET:
                            $paymentModeForNarration = DISPLAY_MOBIKWIK_WALLET;
                            break;
                        case MOBIKWIK_PG:
                            $paymentModeForNarration = DISPLAY_MOBIKWIK_PG;
                            break;
                        case CASHFREE_PG:
                            $paymentModeForNarration = DISPLAY_CASHFREE_PG;
                            break;
                        case PAYTM_PG:
                            $paymentModeForNarration = DISPLAY_PAYTM;
                            break;
                        case HDFC_PG:
                            $paymentModeForNarration = DISPLAY_HDFC_PG;
                            break;
                        case ATOM_PG:
                            $paymentModeForNarration = DISPLAY_ATOM_PG;
                            break;
                        case 'cashtransfer':
                            $paymentModeForNarration = DISPLAY_CASH_TRANSFER;
                            break;
                    }
                }
                $arrLedgerTransactionData = array(
                    'auth' => $auth
                );
                $arrLedgerTransactionData['voucher_type'] = isset($arrPostData['voucher_type_credit']) ? VOUCHER_CREDIT : VOUCHER_RECEIPT;
                $arrLedgerTransactionData['voucher_reference_number'] = $arrPostData['receipt_number'];
                $arrLedgerTransactionData['voucher_reference_id'] = $arrPostData['payment_tracker_id'];
                $arrLedgerTransactionData['from_ledger_id'] = $arrIncomeAccounts['ledger_id'];
                $arrLedgerTransactionData['to_ledger_id'] = $arrUnitLedgerDetails['recieving_ledger_id'];
                $arrLedgerTransactionData['transaction_date'] = !empty($arrPostData['payment_date']) ? $arrPostData['payment_date'] : $this->getCurrentDate('database');
                $arrLedgerTransactionData['transaction_amount'] = $arrPostData['payment_amount'];

                $arrLedgerTransactionData['narration'] = 'Amount received against Invoice ' . $arrPostData['member_paid_invoice'] . ' dated ' . $this->getDisplayDate($arrPostData['payment_date']) . ' through ' . $paymentModeForNarration . $strNarration;
                if ((empty($arrPostData['total_unpaid_amount']) || $arrPostData['total_unpaid_amount'] <= 0) && empty($arrPostData['member_paid_invoice'])) {
                    $arrLedgerTransactionData['narration'] = 'Advance payment received from ' . ucwords($arrPostData['received_from']) . ' dated ' . $this->getDisplayDate($arrPostData['payment_date']) . ' through ' . $paymentModeForNarration . $strNarration;
                }
                if ($arrPostData['bill_type'] == 'suspense') {
                    $arrLedgerTransactionData['narration'] = 'Unidenfied payment from ' . ucwords($arrPostData['received_from']) . ' dated ' . $this->getDisplayDate($arrPostData['payment_date']) . ' through ' . $paymentModeForNarration . $strNarration;
                }

                $arrLedgerTransactionData['from_ledger_name'] = $arrIncomeAccounts['ledger_name'];
                $arrLedgerTransactionData['to_ledger_name'] = $arrUnitLedgerDetails['receiver_name'];
                $arrLedgerTransactionData['payment_reference'] = $arrPostData['transaction_reference'];
                $arrLedgerTransactionData['transaction_type'] = '';
                $arrLedgerTransactionData['mode_of_payment'] = $arrPostData['payment_mode'];
                $arrLedgerTransactionData['other_payment_ref'] = '';
                if (isset($data['from_ledger'])) {
                    $arrLedgerTransactionData['from_ledger_id'] = $data['from_ledger']['recieving_ledger_id'];
                    $arrLedgerTransactionData['from_ledger_name'] = $data['from_ledger']['receiver_name'];
                }
                if (isset($data['to_ledger'])) {
                    $arrLedgerTransactionData['to_ledger_id'] = $data['to_ledger']['recieving_ledger_id'];
                    $arrLedgerTransactionData['to_ledger_name'] = $data['to_ledger']['receiver_name'];
                }
                if (isset($data['narration'])) {
                    $arrLedgerTransactionData['narration'] = $data['narration'];
                }
                $arrLedgerTransactionData['narration'] = '(Receipt No-' . $arrPostData['receipt_number'] . ') ' . $arrLedgerTransactionData['narration'];
                if (!empty($arrPostData['payment_note'])) {
                    $arrLedgerTransactionData['narration'] .= ' [' . $arrPostData['payment_note'] . ']';
                }

                if (!empty($arrPostData['tds_amount']) && $arrPostData['tds_amount'] > 0) {
                    $arrLedgerTransactionData['transaction_amount'] = round($arrPostData['payment_amount'] - $arrPostData['tds_amount'], 2);
                    $arrLedgerEntry = $chsoneInvoiceGeneration->transactionLedgerEntry($arrLedgerTransactionData);
                    if (isset($arrLedgerEntry['error']) && !empty($arrLedgerEntry['error'])) {
                        $countLedgerEntry++;
                    } else {
                        $arrLedgerTransactionData['transaction_amount'] = $arrPostData['tds_amount'];
                        $arrListnerData['auth'] = $auth;
                        $arrListnerData['ledger_name'] = 'tds receivable';
                        $arrListnerData['context'] = '';
                        $arrListnerData['behaviour'] = ASSET;

                        $arrTdsLedger = $chsoneInvoiceGeneration->checkledgerExistNew($arrListnerData);
                        $arrLedgerTransactionData['from_ledger_id'] = $arrTdsLedger['recieving_ledger_id'];
                        $arrLedgerTransactionData['from_ledger_name'] = $arrTdsLedger['receiver_name'];
                        $arrLedgerTransactionData['narration'] = 'TDS amount deducted against Invoice ' . $arrPostData['member_paid_invoice'] . ' dated ' . $this->getDisplayDate($arrPostData['payment_date']) . ' through ' . $paymentModeForNarration . $strNarration;
                        $arrLedgerEntry = $chsoneInvoiceGeneration->transactionLedgerEntry($arrLedgerTransactionData);
                        if (isset($arrLedgerEntry['error']) && !empty($arrLedgerEntry['error'])) {
                            $countLedgerEntry++;
                        }
                    }
                } else {
                    $arrLedgerEntry = $chsoneInvoiceGeneration->transactionLedgerEntry($arrLedgerTransactionData);
                    if (isset($arrLedgerEntry['error']) && !empty($arrLedgerEntry['error'])) {
                        $countLedgerEntry++;
                    }
                }
                if (!empty($arrPostData['writeoff_amount']) && $arrPostData['writeoff_amount'] > 0) {
                    if ($arrPostData['writeoff_amount'] >= 1000) {
                        $arrExpenseWriteOff = $chsoneInvoiceGeneration->checkledgerExistNew(array('auth' => $auth, 'ledger_name' => 'Expense Bad Debt', 'context' => EXPENSE, 'behaviour' => EXPENSE));
                        if (empty($arrExpenseWriteOff['recieving_ledger_id'])) {
                            $arrParentExpense = $chsoneInvoiceGeneration->getLedgerDetail(array('auth' => $auth, 'ledger_name' => 'Indirect Expense', 'context' => EXPENSE, 'behaviour' => EXPENSE));
                            if (!empty($arrParentExpense['ledger_account_id'])) {
                                $arrExpenseWriteOff['receiver_name'] = 'Expense Bad Debt';
                                $arrExpenseWriteOff['recieving_ledger_id'] = $chsoneInvoiceGeneration->createNewLedgerExit(array('auth' => $auth, 'ledger_name' => $arrExpenseWriteOff['receiver_name'], 'context' => EXPENSE, 'behaviour' => EXPENSE, 'parent_id' => $arrParentExpense['ledger_account_id']));
                            }
                        }
                    } else {
                        $arrExpenseWriteOff = $chsoneInvoiceGeneration->checkledgerExistNew(array('auth' => $auth, 'ledger_name' => 'Expense Write Off', 'context' => EXPENSE, 'behaviour' => EXPENSE));
                    }
                    if (empty($arrExpenseWriteOff['recieving_ledger_id'])) {
                        return 1;
                    }
                    $arrLedgerTransactionData['transaction_amount'] = $arrPostData['writeoff_amount'];
                    $arrLedgerTransactionData['from_ledger_id'] = $arrExpenseWriteOff['recieving_ledger_id'];
                    $arrLedgerTransactionData['from_ledger_name'] = $arrExpenseWriteOff['receiver_name'];

                    $arrLedgerTransactionData['narration'] = 'Amount writeoff against Invoice ' . $arrPostData['member_paid_invoice'] . ' dated ' . $this->getDisplayDate($arrPostData['payment_date']);
                    $arrLedgerEntry = $chsoneInvoiceGeneration->transactionLedgerEntry($arrLedgerTransactionData);
                    if (isset($arrLedgerEntry['error']) && !empty($arrLedgerEntry['error'])) {
                        $countLedgerEntry++;
                    }
                }
            }
            return $countLedgerEntry;
        } catch (\Exception $e) {
            dd("Error in paymentLedgerEntry on line " . $e->getLine() . ": " . $e->getMessage());
        }
    }

    public function getUnitDetailById($data = array())
    {
        try {
            $soc_id = $this->input['company_id'];
            $unit_id = $data['unit_id'];
            $arrUnitDetails = array();
            $objUnitDetails = ChsoneUnitsMaster::where('soc_id', $soc_id)
                ->where('unit_id', $unit_id)
                ->first();

            if (!empty($objUnitDetails)) {
                $arrUnitDetails = $objUnitDetails->toArray();
            }
            return $arrUnitDetails;
        } catch (\Exception $e) {
            dd("Error in getUnitDetailById on line " . $e->getLine() . ": " . $e->getMessage());
        }
    }

    public function checkledgerExistOrCreate($data = array())
    {
        try {
            $arrNonmemberAccounts = array();
            $auth = $data['auth'] ?? '';
            $ledger_name = $data['ledger_name'];
            $arrClinetLedgerDetails = array();
            $objBookerLedger = ChsoneGrpLedgerTree::where('soc_id', $this->input['company_id'])
                ->where('context', 'security deposit')
                ->where('entity_type', ENTITY_TYPE_LEDGER)
                ->where('ledger_account_name', $ledger_name)
                ->first();
            if (!empty($objBookerLedger)) {
                $arrClinetLedgerDetails['recieving_ledger_id'] = $objBookerLedger->ledger_account_id;
                $arrClinetLedgerDetails['receiver_name'] = $objBookerLedger->ledger_account_name;
            } else {
                $objSecurityDepositGroup = ChsoneGrpLedgerTree::where('soc_id', $this->input['company_id'])
                    ->where('entity_type', ENTITY_TYPE_GROUP)
                    ->where('ledger_account_name', 'Security Deposit')
                    ->first();
                $objGroupLedg = new ChsoneGrpLedgerTree;
                $ledger_account_id = $objGroupLedg->manipulate($ledger_name, ENTITY_TYPE_LEDGER, "", $objSecurityDepositGroup->ledger_account_id, $objSecurityDepositGroup->behaviour, '', 0, '', '', null, $objSecurityDepositGroup->context) ?? '';
                if (!is_null($ledger_account_id) && (gettype($ledger_account_id) != 'boolean') && (strpos($ledger_account_id, 'DUP') === false)) {
                    $arrClinetLedgerDetails['recieving_ledger_id'] = $ledger_account_id;
                    $arrClinetLedgerDetails['receiver_name'] = $ledger_name;
                }
            }
            unset($objBookerLedger);
            return $arrClinetLedgerDetails;
        } catch (\Exception $e) {
            dd("Error in checkledgerExistOrCreate on line " . $e->getLine() . ": " . $e->getMessage());
        }
    }

    public function getClosedAccountDetailByDate($data = array())
    {
        try {
            $arrAccountMasterDetail = array();
            if (!empty($data['bill_date'])) {
                $data['bill_date'] = $data['bill_date'];
            } else {
                $data['bill_date'] = date('Y-m-d');
            }
            $objAccountMasterDetail = SocAccountFinancialYearMaster::where('soc_id', $data['soc_id'])
                ->where('closed', 1)
                ->where('confirmed', 1)
                ->whereBetween('fy_start_date', [$data['bill_date'], 'fy_end_date'])
                ->first();
            if (!empty($objAccountMasterDetail)) {
                $arrAccountMasterDetail = $objAccountMasterDetail->toArray();
            }
            return $arrAccountMasterDetail;
        } catch (\Exception $e) {
            dd("Error in getClosedAccountDetailByDate on line " . $e->getLine() . ": " . $e->getMessage());
        }
    }

    private function _getAllSettingWithId($data)
    {
        try {
            $incomeInvoiceGeneralSetting = IncomeInvoiceGeneralSetting::where('soc_id', $data['soc_id'])->get()->toArray();
            foreach ($incomeInvoiceGeneralSetting as $key => $value) {
                $final_array[$value['setting_key']] = $value['id'];
            }
            return $final_array;
        } catch (\Exception $e) {
            dd("Error in _getAllSettingWithId on line " . $e->getLine() . ": " . $e->getMessage());
        }
    }

    public function getgeneralsetting($data = array())
    {
        try {
            $final_array = $this->_getAllSetting($data);
            $incomeInvoiceSetting = IncomeInvoiceSetting::where('soc_id', $data['soc_id'])
                ->orderBy('effective_date', 'DESC')
                ->first();

            $final_array['generalsettingid'] = $incomeInvoiceSetting->id;
            $final_array['invoicing_frequency'] = (isset($incomeInvoiceSetting->invoicing_frequency) && $incomeInvoiceSetting->invoicing_frequency != '') ? $incomeInvoiceSetting->invoicing_frequency : '';
            $final_array['effective_date'] = $incomeInvoiceSetting->effective_date;
            $final_array['general_setting_key_ids'] = $this->_getAllSettingWithId($data);
            return $final_array;
        } catch (\Exception $e) {
            dd("Error in getgeneralsetting on line " . $e->getLine() . ": " . $e->getMessage());
        }
    }

    private function _getAllSetting($data)
    {
        try {
            $incomeInvoiceSetting = IncomeInvoiceSetting::where('soc_id', $data['soc_id'])->first();
            $incomeInvoiceGeneralSetting = IncomeInvoiceGeneralSetting::where('soc_id', $data['soc_id'])->get()->toArray();

            foreach ($incomeInvoiceGeneralSetting as $key => $value) {
                $final_array[$value['setting_key']] = $value['setting_value'];
            }
            return $final_array;
        } catch (\Exception $e) {
            dd("Error in _getAllSetting on line " . $e->getLine() . ": " . $e->getMessage());
        }
    }

    public function getMemberDetail($data = array())
    {
        try {
            $arrMemberMaster = [];
            $memberType = ChsoneMemberTypeMaster::where('soc_id', $data['soc_id'])
                ->where('member_type_name', 'Primary')
                ->first();

            if ($memberType) {
                $objMemberMaster = ChsoneMembersMaster::where('soc_id', $data['soc_id'])
                    ->where('fk_unit_id', $data['unit_id'])
                    ->where('status', 1)
                    ->where('member_type_id', $memberType->member_type_id)
                    ->first();

                if ($objMemberMaster) {
                    $arrMemberMaster = $objMemberMaster->toArray();
                }
            }
            return $arrMemberMaster;
        } catch (\Exception $e) {
            dd("Error in getMemberDetail on line " . $e->getLine() . ": " . $e->getMessage());
        }
    }

    public function getInvoicePaymentTrackerDetail($data = array())
    {
        try {
            $arrIncomeInvoicePaymentTracker = array();
            $query = IncomeInvoicePaymentTracker::query();
            $query->where('soc_id', $data['soc_id'])
                ->where('id', $data['payment_tracker_id']);

            if (isset($data['unit_id']) && !empty($data['unit_id'])) {
                $query->where('unit_id', $data['unit_id']);
            }

            if (isset($data['status']) && !empty($data['status'])) {
                if (is_array($data['status'])) {
                    $query->whereIn('status', $data['status']);
                } else {
                    $query->where('status', $data['status']);
                }
            }

            if (isset($data['current_date']) && !empty($data['current_date'])) {
                $query->where('payment_date', '<=', $data['current_date']);
            }

            if (!empty($data['payment_reversal']) && strtolower($data['payment_reversal']) == 'y') {
                $query->where('created_date', '>=', ACTIVE_PAYMENT_REVERSAL_DATE);
            }

            if (isset($data['bill_type']) && !empty($data['bill_type'])) {
                $query->whereIn('bill_type', $data['bill_type']);
            }
            $objIncomeInvoicePaymentTracker = $query->first();

            if (!empty($objIncomeInvoicePaymentTracker)) {
                $arrIncomeInvoicePaymentTracker = $objIncomeInvoicePaymentTracker->toArray();
                $arrIncomeInvoicePaymentTracker['member_paid_invoice'] = $arrIncomeInvoicePaymentTracker['invoice_number'];
                $arrIncomeInvoicePaymentTracker['receipt_number'] = strtoupper($arrIncomeInvoicePaymentTracker['receipt_number']);
                $arrIncomeInvoicePaymentTracker['total_unpaid_amount'] = $arrIncomeInvoicePaymentTracker['total_due_amount'];
                unset($arrIncomeInvoicePaymentTracker['invoice_number'], $arrIncomeInvoicePaymentTracker['total_due_amount']);
                $arrIncomeInvoicePaymentTracker['other_information'] = unserialize($arrIncomeInvoicePaymentTracker['other_information']);

                $arrIncomeInvoicePaymentTracker['payment_for'] = 'Maintenance Invoice';
                if (strtolower($arrIncomeInvoicePaymentTracker['bill_type']) == 'common_bill') {
                    $arrIncomeInvoicePaymentTracker['payment_for'] = 'Incidental Bill';
                } elseif (strtolower($arrIncomeInvoicePaymentTracker['bill_type']) == 'nonmember') {
                    $arrIncomeInvoicePaymentTracker['payment_for'] = 'Nonmember Bill';
                } elseif (strtolower($arrIncomeInvoicePaymentTracker['bill_type']) == 'suspense') {
                    $arrIncomeInvoicePaymentTracker['payment_for'] = 'Suspense Reciept';
                } elseif (strtolower($arrIncomeInvoicePaymentTracker['bill_type']) == 'creditaccount-member') {
                    if (
                        !empty($arrIncomeInvoicePaymentTracker['other_information']['credit_account_detail']['credit_used_type']) &&
                        strtolower($arrIncomeInvoicePaymentTracker['other_information']['credit_account_detail']['credit_used_type']) == 'refundable'
                    ) {
                        $arrIncomeInvoicePaymentTracker['payment_for'] = 'Member Refundable Advance';
                    } elseif (
                        !empty($arrIncomeInvoicePaymentTracker['other_information']['credit_account_detail']['credit_used_type']) &&
                        strtolower($arrIncomeInvoicePaymentTracker['other_information']['credit_account_detail']['credit_used_type']) == 'adjustable'
                    ) {
                        $arrIncomeInvoicePaymentTracker['payment_for'] = 'Member Adjustable Advance';
                    }
                }
                if (!empty($arrIncomeInvoicePaymentTracker['payment_mode']) && in_array($arrIncomeInvoicePaymentTracker['payment_mode'], array(YES_BANK_PG, YES_BANK_ECOLLECT, PAYTM_PG, MOBIKWIK_PG, CASHFREE_PG, MOBIKWIK_WALLET, HDFC_PG, ATOM_PG))) {
                    switch ($arrIncomeInvoicePaymentTracker['payment_mode']) {
                        case YES_BANK_PG:
                            $arrIncomeInvoicePaymentTracker['online_payment_display_mode'] = DISPLAY_YES_BANK;
                            break;
                        case YES_BANK_ECOLLECT:
                            $arrIncomeInvoicePaymentTracker['online_payment_display_mode'] = DISPLAY_YES_BANK_ECOLLECT;
                            break;
                        case MOBIKWIK_WALLET:
                            $arrIncomeInvoicePaymentTracker['online_payment_display_mode'] = DISPLAY_MOBIKWIK_WALLET;
                            break;
                        case PAYTM_PG:
                            $arrIncomeInvoicePaymentTracker['online_payment_display_mode'] = DISPLAY_PAYTM;
                            break;
                        case MOBIKWIK_PG:
                            $arrIncomeInvoicePaymentTracker['online_payment_display_mode'] = DISPLAY_MOBIKWIK_PG;
                            break;
                        case CASHFREE_PG:
                            $arrIncomeInvoicePaymentTracker['online_payment_display_mode'] = DISPLAY_CASHFREE_PG;
                            break;
                        case HDFC_PG:
                            $arrIncomeInvoicePaymentTracker['online_payment_display_mode'] = DISPLAY_HDFC_PG;
                            break;
                        case ATOM_PG:
                            $arrIncomeInvoicePaymentTracker['online_payment_display_mode'] = DISPLAY_ATOM_PG;
                            break;
                    }
                }
                if (!empty($arrIncomeInvoicePaymentTracker['created_date'])) {
                    $payment_date = current(explode(' ', $arrIncomeInvoicePaymentTracker['created_date']));
                    if (!empty($payment_date)) {
                        $arrIncomeInvoicePaymentTracker['created_date'] = $payment_date;
                    }
                }

                if (!empty($arrIncomeInvoicePaymentTracker['other_information'])) {
                    if (!empty($arrIncomeInvoicePaymentTracker['other_information'])) {
                        if (!empty($arrIncomeInvoicePaymentTracker['other_information']['writeoff_detail'])) {
                            $arrIncomeInvoicePaymentTracker['total_particular'] = $arrIncomeInvoicePaymentTracker['other_information']['writeoff_detail']['total_particular'];
                            for ($i = 1; $i <= $arrIncomeInvoicePaymentTracker['total_particular']; $i++) {
                                $arrIncomeInvoicePaymentTracker["rule_$i"] = $arrIncomeInvoicePaymentTracker['other_information']['writeoff_detail']["rule_$i"];
                                $arrIncomeInvoicePaymentTracker["particular_$i"] = $arrIncomeInvoicePaymentTracker['other_information']['writeoff_detail']["particular_$i"];
                                $arrIncomeInvoicePaymentTracker["writeoff_amount_$i"] = $arrIncomeInvoicePaymentTracker['other_information']['writeoff_detail']["writeoff_amount_$i"];
                            }
                        }
                    }
                }
            }
            return $arrIncomeInvoicePaymentTracker;
        } catch (\Exception $e) {
            dd("Error in getInvoicePaymentTrackerDetail on line " . $e->getLine() . ": " . $e->getMessage());
        }
    }

    public function getInvoiceGeneralSetting($data = [])
    {
        try {
            $arrInvoiceGeneralSetting = array();
            $objInvoiceGeneralSetting = IncomeInvoiceGeneralSetting::where('soc_id', $data['soc_id'])
                ->whereIn('setting_key', $data['setting_key'])
                ->get();
            if (!empty($objInvoiceGeneralSetting)) {
                $arrInvoiceGeneralSetting = $objInvoiceGeneralSetting->toArray();
            }
            return $arrInvoiceGeneralSetting;
        } catch (\Exception $e) {
            dd("Error in getInvoiceGeneralSetting on line " . $e->getLine() . ": " . $e->getMessage());
        }
    }
}
