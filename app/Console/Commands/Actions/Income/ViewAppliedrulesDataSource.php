<?php

namespace App\Console\Commands\Actions\Income;

use Illuminate\Support\Facades\DB;
use App\Console\Commands\Action;
use App\Models\Income\ChsoneInvoiceGeneration;
use App\Models\Tenants\ChsoneInvoiceGeneration as TenantsChsoneInvoiceGeneration;
use App\Models\Tenants\IncomeUnitInvoice;
use Mockery\Exception;

class ViewAppliedrulesDataSource extends Action
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'datasource:viewAppliedrules {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Edit Member Name data source';

    protected $formatter =  [];

    protected $formatterByKeys =  [];

    protected $mapper = [];

    /**
     * Execute the console command.
     */
    public function apply()
    {
        try {
            $socId = $this->input['company_id'];
            $id = $this->input['id'] ?? 0;
            $unitId = $this->input['unit_id'] ?? 0;
            $date = date('Y-m-d');

            $unit = $this->tenantDB()
                ->table('income_invoice_rules as rules')
                ->select([
                    'rules.id',
                    'rules.soc_id',
                    'rules.unit_type',
                    'rules.applicable_taxes',
                    'rules.apply_late_payment_interest',
                    'rules.effective_date',
                    'rules.rule',
                    'rules.optional_rule',
                    'rules.tax_exemptions',
                    'rules.fee_carpet_area',
                    'rules.fee_open_area',
                    'rules.fee_per_unit',
                    'rules.other_income_fee',
                    'rules.other_income_fee_rate',
                    'rules.other_percent_of_income_account',
                    'accounts.account_name',
                    'units.unit_area',
                    'units.unit_open_area',
                ])
                ->distinct()  // no arguments
                ->leftJoin('income_accounts as accounts', 'rules.income_account_id', '=', 'accounts.account_id')
                ->leftJoin('chsone_units_master as units', 'rules.soc_units_type_id', '=', 'units.fk_unit_category_id')
                ->where('rules.soc_units_type_id', $id)
                ->where('rules.effective_date', '<=', $date)
                ->where('units.unit_id', $unitId)
                ->orderBy('rules.effective_date', 'desc')
                ->get();

            if ($unit->isEmpty()) {
                $this->status = "error";
                $this->message = "Unit not found";
                $this->statusCode = 404;
            } else {
                $uniqueData = [];
                $seenAccountNames = [];

                foreach ($unit as $entry) {
                    // dd($entry);
                    if (!in_array($entry->account_name, $seenAccountNames)) {
                        $uniqueData[] = $entry;
                        $seenAccountNames[] = $entry->account_name;
                    }
                    $entry->display_name = preg_replace('/([a-z])([A-Z])/', '$1 $2', $entry->account_name);
                }
                $this->status = 'success';
                $this->message = 'Unit data found successfully';
                $this->statusCode = 200;
                $this->data = $uniqueData;
            }
        }catch (Exception $e){
            dd($e);
        }


    }


}
