<?php

namespace App\Console\Commands\Actions\Income;

use App\Console\Commands\Action;
use App\Models\Tenants\IncomeInvoiceSetting;
use DateInterval;
use DatePeriod;
use DateTime;

class GetBillingPeriodsDataSource extends Action
{
    protected $signature = 'datasource:getBillingPeriods {flowId} {parentId} {input}';
    protected $description = 'Get Billing Periods Data Source';

    public function apply(): void
    {
        $socId = $this->input['company_id'] ?? null;
        if (!$socId) {
            $this->fail('No society id provided', 400);
            return;
        }

        $setting = IncomeInvoiceSetting::where('soc_id', $socId)
            ->orderByDesc('effective_date')
            ->get();


        $closings = $this->getCurrentActiveFinancialYearForBillingPeriod();

            // Determine the min start and max end
        $fyStartDate = $closings->min('fy_start_date');
        $fyEndDate   = $closings->max('fy_end_date');

        $fyDetails = [
            [
                    'fy_start_date' => $fyStartDate,
                    'fy_end_date'   => $fyEndDate,
            ],
        ];


        // format the data for the frontend
        $periods = $this->getBillingPeriods(
            $setting->toArray(),
            $fyDetails
        );

        $this->data = [];
        $id = 1;
        foreach ($periods as $period => $range) {
            $this->data[] = [
                'id'     => $id++,
                'period' => $period,
                'range'  => $range,
            ];
        }
        // $this->data = $periods;
    }

    private function getBillingPeriods(array $setting, array $fy): array
    {
        try {
            $start = (new DateTime($fy[0]['fy_start_date']))->modify('first day of this month');
            $end   = (new DateTime($fy[array_key_last($fy)]['fy_end_date']))->modify('first day of next month');

            $freq = $setting[0]['invoicing_frequency'];
            $invoice = [];

            $intervalMap = [
                'Monthly'     => '1 month',
                'Quarterly'   => '3 months',
                'Half_yearly' => '6 months',
                'Yearly'      => '1 year',
            ];

            if (!isset($intervalMap[$freq])) {
                return [];
            }

            $period = new DatePeriod($start, DateInterval::createFromDateString($intervalMap[$freq]), $end);

            foreach ($period as $dt) {
                switch ($freq) {
                    case 'Monthly':
                        $invoice[$dt->format('F-Y')] = $dt->format('F Y');
                        break;
                    case 'Yearly':
                        $startYear = $dt->format('Y');
                        $endYear = (clone $dt)->modify('+11 months')->format('Y');
                        $invoice["{$startYear}-{$endYear}"] = "{$startYear} - {$endYear}";
                        break;
                    default:
                        $startLabel = $dt->format('F Y');
                        $startLabel = $dt->format('F Y');
                        $endLabel = (clone $dt)
                            ->modify("+{$intervalMap[$freq]}")
                            ->modify('-1 day')
                            ->format('F Y');

                        $invoice[$dt->format('F-Y')] = "{$startLabel} - {$endLabel}";
                }
            }


            return $invoice;
        } catch (\Exception $e) {
            throw $e;
        }
    }

}
