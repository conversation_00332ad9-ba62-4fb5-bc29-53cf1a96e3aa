<?php

namespace App\Console\Commands\Actions\Income;

use App\Console\Commands\Action;
use App\Http\Traits\MongoTraits;
use Illuminate\Support\Facades\Config;

class EditAdvancesDataSource extends Action
{
    use MongoTraits; // Use the MongoTraits trait in this class

    protected $constants;

    public function __construct()
    {
        parent::__construct();
        $this->constants = Config::get("constants");
    }

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'datasource:editAdvances {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Get the billable note details';

    protected $formatterByKeys = ['id'];

    protected $mapper = [
        'id' => 'id',
    ];

    /**
     * Execute the console command.
     */

    public function apply()
    {
        $config = Config::get("constants");

        $data = $this->input;
        $data['soc_id'] = $this->input['company_id'] ?? null;
        $id = $data['id'] ?? null;
        $ip = $this->input['additional_data']['ip'] ?? null;
        // Edit Form

        $data['credit_account_id'] = $id;
        $response = $this->getCreditAccountResponseById(array('data' => $data));

        $response = json_decode(json_encode($response), true);
        if (is_array($response) && isset($response[0])) {
            $options['data'] = $response[0];
        } else {
            $this->message = 'Unexpected response format';
            $this->status = 'error';
            $this->statusCode = 400;
            return; // Return or handle the error gracefully
        }


        $postData = $data;

        //Refundable to Adjustable Case
        $today_date = date("Y-m-d");
        $arrCreditData['soc_id']=$data['soc_id'];
        $arrCreditData['account_id']=$options['data']['account_id'] ?? null;
        $arrCreditData['account_context']=$options['data']['account_context'] ?? null;


        $refundableBalanceData = $this->getRefundableAmountForIndividuals(array('data' => $arrCreditData));

        if ($postData['credit_used_type'] == 'adjustable' && $options['data']['use_credit'] == 'refundable' && $response[0]['amount'] > $refundableBalanceData['remaining_amount']) {
            $this->message = 'Amount already used.';
            $this->status = 'error';
            $this->statusCode = 400;
            return;
            // $this->session->set("err_msg", 'Amount already used.');
            // return $this->response->redirect($this->config->system->full_base_url_fe.trim($_SERVER['REQUEST_URI'],'/').'?page='.$postData['page'].'&tab='.$postData['tab'].'&list_page='.$postData['list_page']);
        } else {

            if (!($response[0]['context'] == 'system' || ($response[0]['use_credit'] == 'adjustable' && !empty($response[0]['use_credit_after']) && $response[0]['use_credit_after'] <= $today_date))) {

                if ($postData['credit_used_type'] == 'adjustable' && $options['data']['use_credit'] == 'refundable') {

                    //Updated Columns
                    $creditData = array_merge($options['data'], $postData);
                    $data = $this->formatData($creditData);
                    $data['action'] = 'refund-adjust';
                    $this->tenantDB()->beginTransaction();
                    try {
                        $incomeTracker = $this->tenantDB()->table('income_invoice_payment_tracker')
                            ->where('id', $response[0]['payment_tracker_id'])
                            ->where('soc_id', $data['soc_id'])
                            ->first();

                        // Ensure $incomeTracker is not empty and is an object before accessing its properties
                        if (!empty($incomeTracker) && is_object($incomeTracker)) {

                            $otherInfo = unserialize($incomeTracker->other_information);
                        } else {
                            $this->tenantDB()->rollBack();

                            $this->message = 'Income tracker not found or unexpected format';
                            $this->status = 'error';
                            $this->statusCode = 400;
                            return; // Return or handle the error gracefully
                        }

                        $otherInfo['credit_account_detail']['used_for'] = $data['used_for'];
                        $otherInfo['credit_account_detail']['credit_used_type'] = $data['credit_used_type'];
                        $incomeTrackerUpdate = $this->tenantDB()
                            ->table('income_invoice_payment_tracker')
                            ->where('id', $response[0]['payment_tracker_id'])
                            ->where('soc_id', $data['soc_id'])
                            ->update([
                                'other_information' => serialize($otherInfo),
                                'updated_date' => date("Y-m-d H:i:s"),
                            ]);

                        if (!$incomeTrackerUpdate) {

                            $this->tenantDB()->rollBack();

                            $this->message = 'Unable to complete transaction, Please try later.1';
                            $this->status = 'error';
                            $this->statusCode = 400;
                            return;
                        }

                        if ($options['data']['account_context'] == 'nonmember') {

                            $arrToLedgerDetails = $this->checkledgerExistNew(array('soc_id' => $data['soc_id'], 'ledger_name' => 'Nonmember Income', 'context' => $this->constants['SUNDRY_DEBTORS_GROUP'] ?? 'sundrydebtors'));

                            $arrFromLedgerDetails = $this->checkledgerExistNew(array('soc_id' => $data['soc_id'], 'ledger_id' => $data['income_account_id']));

                            $data['payment_amount'] = $data['amount'];
                            //$narration = 'Invoice Rectification for ' . $data['narration'];

                            $intBookerLedgerDetails = $this->payNonmemberBillLedger(array('soc_id' => $data['soc_id'], 'postData' => $data, 'arrBookerLedgerDetails' => $arrToLedgerDetails, 'to_ledger' => $arrToLedgerDetails, 'from_ledger' => $arrFromLedgerDetails));

                            if (!$intBookerLedgerDetails) {
                                $this->tenantDB()->rollBack();

                                $this->message = 'Unable to complete transaction, Please try later.';
                                $this->status = 'error';
                                $this->statusCode = 400;
                                return;
                            }
                        } else {

                            $arrDataListener['soc_id'] = $data['soc_id'];
                            $arrDataListener['unit_id'] = $options['data']['account_id'];
                            $data['payment_amount'] = $data['amount'];

                            $arrIncomeInvoiceMemberDetail = $this->getMemberDetail($arrDataListener); //get all Unit details

                            $arrFromLedgerDetails = $this->checkledgerExistNew(array("soc_id" => $data['soc_id'], 'ledger_id' => $data['income_account_id']));

                            $arrResponse = $this->createMemberLedgerCredit(array("soc_id" => $data['soc_id'], 'unit_id' => $options['data']['account_id'], 'arrIncomeInvoiceMemberDetail' => $arrIncomeInvoiceMemberDetail, 'arrPostData' => $data, 'from_ledger' => $arrFromLedgerDetails));

                            if ($arrResponse['status'] == 'error') {
                                $this->tenantDB()->rollBack();

                                $this->message = 'Unable to complete transaction, Please try later.';
                                $this->status = 'error';
                                $this->statusCode = 400;
                                return;

                                // $this->soc_db_w->rollback();
                                // $this->session->set("err_msg", 'Unable to complete transaction, Please try later.');
                                // return $this->response->redirect($redirect_url);
                                // return $this->response->redirect($this->config->system->full_base_url . "credit-accounts/memberAdvances");
                            }
                        }

                        $data['user'] = $this->input['user'] ?? [];

                        $id = $this->addCreditAmountresponse($data, $id);
                        if ($id) {
                            $this->tenantDB()->rollBack();

                            $this->message = "Unable to complete transaction, Please try later.";
                            $this->status = 'error';
                            $this->statusCode = 400;
                            return;

                            // $this->soc_db_w->rollback();
                            // $this->session->set("err_msg", $id['data']['message'][0]);
                            // return $this->response->redirect($redirect_url);
                        }
                        $this->tenantDB()->commit();

                        // $this->soc_db_w->commit();
                        // $socUsers = $this->session->get("socUsers");
                        $notification_title = 'Advanced type changed';
                        $document = [
                            'title' => $notification_title,
                            'desc' => 'Advanced type changed from ' . ucfirst($options['data']['use_credit']) . ' to ' . ucfirst($postData['credit_used_type']),
                            'scope' => 'group',
                            'module' => 'chsone_credit_accounts',
                            'company_id' => $this->input['company_id'],
                            'user_ids' => $this->input['user_id'] ?? 0,
                            'created_by' => $this->input['user_id'] ?? 0,
                            'username' => $this->input['user']['first_name'] . ' ' . $this->input['user']['last_name'],
                            'ip_address' => $ip,
                            'role' => 'admin',
                            'approve_link' => $this->input['approve_link'] ?? '',
                            'action_url' => $this->input['action_url'] ?? '',
                            'deny_link' => $this->input['deny_link'] ?? '',
                            'id' => $this->input['id'] ?? '',
                            'notified_status' => $this->input['notified_status'] ?? 0,
                            'to_be_notified' => $this->input['to_be_notified'] ?? 0,
                            'member_id' => $this->input['member_id'] ?? [],
                            'all_admin' => $this->input['all_admin'] ?? [],
                            'notification_id' => $this->input['notification_id'] ?? '',
                            'date_time' => date(format: "d-M-Y H:i:s"),

                        ];

                        $this->addNotification($document);
                        $this->data = [];
                        $this->message = 'Data has been updated successfully.';
                    } catch (\Exception $e) {
                        $this->tenantDB()->rollBack();
                        $this->message = 'Unable to complete transaction, Please try later.';
                        $this->status = 'error';
                        $this->statusCode = 400;
                        return;
                    }

                } elseif ($postData['credit_used_type'] === 'adjustable' && ($postData['adjustable_date'] != $response[0]['use_credit_after']) || $postData['used_for'] != $response[0]['use_credit_for']) {
                    // echo "<pre>";print_r("2");exit;
                    // $socUsers = $this->session->get("socUsers");
                    if ($postData['adjustable_date'] != $response[0]['use_credit_after']) {
                        $notification_title = 'Advanced adjust date changed';
                        $notification_desc = 'Advanced adjust date changed from ' . $response[0]['use_credit_after'] . ' to ' . $postData['adjustable_date'];
                    } elseif ($postData['used_for'] != $response[0]['use_credit_for']) {
                        $notification_title = 'Advanced adjust against changed';
                        $notification_desc = 'Advanced adjust against changed from ' . ucfirst($response[0]['use_credit_for']) . ' to ' . ucfirst($postData['used_for']);
                    }

                    //Updated Columns
                    $updatedData['adjustable_date'] = $postData['adjustable_date'];
                    $updatedData['is_locked'] = $postData['is_locked'] ?? 0;
                    $updatedData['used_for'] = $postData['used_for'];
                    $updatedData['credit_used_type'] = $postData['credit_used_type'];
                    $updatedData['is_invoice_rectification'] = $postData['is_invoice_rectification'] ?? '';
                    $updatedData['income_account'] = $postData['income_account'] ?? '';
                    $updatedData['nonmember_income_account'] = $postData['nonmember_income_account'] ?? '';
                    $creditData = array_merge($options['data'], $updatedData);

                    $data = $this->formatData($creditData);
                    $data['action'] = 'update';

                    $data['user'] = $this->input['user'] ?? [];
                    $id = $this->addCreditAmountresponse($data, $id);

                    if ($id) {

                        $this->tenantDB()->rollBack();

                        $this->message = "Unable to complete transaction, Please try later.";
                        $this->status = 'error';
                        $this->statusCode = 400;
                        return;
                    }

                    $this->tenantDB()->beginTransaction();
                    try {

                        //Update Payment Tracker
                        $incomeTracker = $this->tenantDB()->table('income_invoice_payment_tracker')
                            ->where('id', $response[0]['payment_tracker_id'])
                            ->where('soc_id', $data['soc_id'])
                            ->first();

                        // Ensure $incomeTracker is not empty and is an object before accessing its properties
                        if (!empty($incomeTracker) && is_object($incomeTracker)) {
                            $otherInfo = unserialize($incomeTracker->other_information);
                        } else {
                            $this->tenantDB()->rollBack();

                            $this->message = 'Income tracker not found or unexpected format';
                            $this->status = 'error';
                            $this->statusCode = 400;
                            return; // Return or handle the error gracefully
                        }

                        $otherInfo['credit_account_detail']['adjustable_date'] = $postData['adjustable_date'];
                        $incomeTrackerUpdate = $this->tenantDB()
                            ->table(table: 'income_invoice_payment_tracker')
                            ->where('id', $response[0]['payment_tracker_id'])
                            ->where('soc_id', $data['soc_id'])
                            ->update([
                                'other_information' => serialize($otherInfo),
                                'updated_date' => date("Y-m-d H:i:s"),
                            ]);

                        if (!$incomeTrackerUpdate) {
                            $this->tenantDB()->rollBack();

                            $this->message = 'Unable to complete transaction, Please try later.';
                            $this->status = 'error';
                            $this->statusCode = 400;
                            return;
                        }


                        $this->tenantDB()->commit();

                        if (isset($notification_title) && isset($notification_desc)) {
                            $document = [
                                'title' => $notification_title,
                                'desc' => $notification_desc,
                                'scope' => 'group',
                                'module' => 'chsone_credit_accounts',
                                'company_id' => $this->input['company_id'],
                                'user_ids' => $this->input['user_id'] ?? 0,
                                'created_by' => $this->input['user_id'] ?? 0,
                                'username' => $this->input['user']['first_name'] . ' ' . $this->input['user']['last_name'],
                                'ip_address' => $ip,
                                'role' => 'admin',
                                'approve_link' => $this->input['approve_link'] ?? '',
                                'action_url' => $this->input['action_url'] ?? '',
                                'deny_link' => $this->input['deny_link'] ?? '',
                                'id' => $this->input['id'] ?? '',
                                'notified_status' => $this->input['notified_status'] ?? 0,
                                'to_be_notified' => $this->input['to_be_notified'] ?? 0,
                                'member_id' => $this->input['member_id'] ?? [],
                                'all_admin' => $this->input['all_admin'] ?? [],
                                'notification_id' => $this->input['notification_id'] ?? '',
                            ];

                            $this->addNotification($document);
                            $this->data = [];
                            $this->message = 'Data has been updated successfully.';

                        }
                    } catch (\Exception $e) {
                        $this->tenantDB()->rollBack();
                        $this->message = 'Unable to complete transaction, Please try later.';
                        $this->status = 'error';
                        $this->statusCode = 400;

                    }

                }
            }
        }
    }

    public function formatData($data)
    {

        $data['adjustable_date'] = (!empty($data['adjustable_date']) && $data['adjustable_date'] !== null) ? $this->getDatabaseDate($data['adjustable_date']) : null;
        $data['transaction_type'] = 'cr';
        $data['created_name'] = $data['account_name'];
        $data['received_from'] = $data['account_name'];
        $data['bill_type'] = ($data['account_context'] == 'unit') ? 'creditaccount-member' : 'creditaccount-nonmember';
        $data['transaction_type'] = 'cr';
        $data['context'] = 'user';
        $data['is_locked'] = $data['credit_used_type'] == 'refundable' ? 1 : 0;
        $data['unit_id'] = $data['account_context'] == 'unit' ? $data['account_id'] : 0;
        $data['is_invoice_rectification'] = isset($data['is_invoice_rectification']) ? $data['is_invoice_rectification'] : 0;
        $data['income_account_ledger_id'] = ($data['credit_used_type'] == 'adjustable' && $data['is_invoice_rectification'] == '1') ? $data['account_context'] == 'unit' ? $data['income_account'] : $data['nonmember_income_account'] : null;

        return $data;
    }

    public function getCreditAccountResponseById($data = array())
    {

        if (isset($data['data']['is_pagination'])) {
            $crAcqueryResponse = $this->getCreditAccountByIdResponse($data['data']['soc_id'], $data['data']['account_id'], $data['data']['account_context']);
            return $crAcqueryResponse;
        } else {
            $accountTransactionDetails = array();
            $conditions = "soc_id = " . $data['data']['soc_id'];
            if (array_key_exists("credit_account_id", $data['data'])) {
                $conditions = $conditions . ' and credit_account_id = ' . $data['data']['credit_account_id'];
            }
            if (array_key_exists("account_id", $data['data'])) {
                $conditions = $conditions . ' and account_id = ' . $data['data']['account_id'];
            }
            if (array_key_exists("account_context", $data['data'])) {
                $conditions = $conditions . ' and account_context = "' . $data['data']['account_context'] . '"';
            }
            $objAmountdetails = $this->tenantDB()->table('chsone_credit_accounts')
                ->whereRaw($conditions)
                ->orderBy('credit_account_id', 'desc')
                ->get();

            if (!empty($objAmountdetails)) {
                $accountTransactionDetails = $objAmountdetails->toArray();
            }

            return $accountTransactionDetails;
        }
    }

    public function getRefundableAmountForIndividuals($data = [])
    {
        // Initializing an empty array for amount details and setting credit and debit amounts to zero.
        $creditAmountDetails = [
            'credit_amount' => 0,
            'debit_amount' => 0,
            'remaining_amount' => 0,
        ];

        // Query to fetch refundable amount details for the given individual.
        $amountDetails = $this->tenantDB()->table('chsone_credit_accounts')->where('soc_id', $data['data']['soc_id'])
            ->where('account_id', $data['data']['account_id'])
            ->where('account_context', $data['data']['account_context'])
            ->where('use_credit', 'refundable')
            ->get();

        // Iterating over fetched data to calculate the total credit and debit amounts.
        foreach ($amountDetails as $amountData) {
            if ($amountData->transaction_type == 'cr') {
                $creditAmountDetails['credit_amount'] += $amountData->amount;
            } else {
                $creditAmountDetails['debit_amount'] += $amountData->amount;
            }
        }

        // Calculating remaining amount.
        $creditAmountDetails['remaining_amount'] = $creditAmountDetails['credit_amount'] - $creditAmountDetails['debit_amount'];

        // Ensuring remaining amount is not negative.
        if ($creditAmountDetails['remaining_amount'] < 0) {
            $creditAmountDetails['remaining_amount'] = 0;
        }


        return $creditAmountDetails;
    }

    public function payNonmemberBillLedger($data = array())
    {
        $config = Config::get("constants");

        $success = 1;
        if ($this->constants['ACCOUNT_MODULE_EXIST'] == 1) {
            $PostData = $data['postData'];

            if (empty($PostData['bill_number'])) {
                $PostData['bill_number'] = $PostData['member_paid_invoice'] ?? '';
            }
            if (isset($PostData['clearance_date']) && !empty($PostData['clearance_date'])) {
                $PostData['payment_date'] = $PostData['clearance_date'];
            }
            $strNarration = '';

            if (empty($data['arrBookerLedgerDetails'])) {
                $arrListnerData['soc_id'] = $data['soc_id'];
                $arrListnerData['ledger_name'] = $PostData['booker_name'];
                $arrBookerLedgerDetails = $this->checkledgerExist($arrListnerData);
            } else {
                $arrBookerLedgerDetails = $data['arrBookerLedgerDetails'];
            }

            //$arrIncomeAccounts = $data['arrIncomeAccounts'];

            //Getting bank/cash ledger details
            $arrAccountDetail = $this->getBankCashAccountDetail(array('soc_id' => $data['soc_id'])); //get all Unit details

            $arrLedgerAccountDetail = $this->getLedgerAccountDetail(array('account_detail' => $arrAccountDetail)); //get all Unit details

            if (!empty($arrLedgerAccountDetail)) {

                $arrIncomeAccounts['ledger_id'] = $arrLedgerAccountDetail['cash']['ledger_id'];
                $arrIncomeAccounts['ledger_name'] = $arrLedgerAccountDetail['cash']['ledger_name'];
                if (strtolower($PostData['payment_mode']) != 'cash') {
                    if (!empty($PostData['bank_account'])) {
                        $arrIncomeAccounts['ledger_id'] = $PostData['bank_account'];
                        $arrIncomeAccounts['ledger_name'] = $arrLedgerAccountDetail['arrBank'][$PostData['bank_account']];
                    } else {
                        $arrIncomeAccounts['ledger_id'] = $arrLedgerAccountDetail['bank']['ledger_id'];
                        $arrIncomeAccounts['ledger_name'] = $arrLedgerAccountDetail['bank']['ledger_name'];
                    }

                    $strNarration = ' with transaction ref. ('
                        . (!empty($PostData['transaction_reference']) ? $PostData['transaction_reference'] : '')
                        . ')';
                    $strNarration .= !empty($PostData['payment_instrument']) ? ', ' . $PostData['payment_instrument'] : '';
                    $strNarration .= ')';
                    if (!empty($PostData['payment_mode']) && strtolower($PostData['payment_mode']) == 'cashtransfer') {
                        $strNarration = ' with payment ref. (' . $PostData['transaction_reference'] . ')';
                    }
                }
            }

            $arrListnerData = array();
            $arrListnerData['soc_id'] = $data['soc_id'];
            $arrListnerData['voucher_type'] = isset($PostData['voucher_type_credit']) ? VOUCHER_CREDIT : $this->constants['VOUCHER_RECEIPT'] ?? '';
            $arrListnerData['voucher_reference_number'] = $PostData['receipt_number'] ?? '';
            $arrListnerData['voucher_reference_id'] = $PostData['payment_tracker_id'];
            $arrListnerData['from_ledger_id'] = $arrIncomeAccounts['ledger_id'];
            $arrListnerData['to_ledger_id'] = $arrBookerLedgerDetails['recieving_ledger_id'] ?? '';

            $arrListnerData['transaction_date'] = !empty($PostData['payment_date']) ? $this->getDatabaseDate($PostData['payment_date']) : $this->getCurrentDate('database');
            $arrListnerData['transaction_amount'] = $PostData['payment_amount'];

            if ($PostData['payment_mode'] == 'cashtransfer') {
                $payment_mode_display = $this->constants['DISPLAY_CASH_TRANSFER'] ?? '';
            } else {
                $payment_mode_display = ucfirst($PostData['payment_mode']);
            }

            $arrListnerData['narration'] =
            (!empty($PostData['receipt_number']) ? '(Receipt No-' . $PostData['receipt_number'] . ') ' : '') .
            'Amount received against Invoice ' . $PostData['bill_number'] .
            ' dated ' . $this->getCurrentDate('display') .
                ' through ' . $payment_mode_display . $strNarration;
            $arrListnerData['from_ledger_name'] = $arrIncomeAccounts['ledger_name'];
            $arrListnerData['to_ledger_name'] = $arrBookerLedgerDetails['receiver_name'];
            $arrListnerData['payment_reference'] = $PostData['transaction_reference'] ?? '';
            $arrListnerData['transaction_type'] = $PostData['transaction_type'];
            $arrListnerData['mode_of_payment'] = $PostData['payment_mode'];
            $arrListnerData['other_payment_ref'] = $PostData['other_payment_ref'] ?? '';

            if (!empty($PostData['payment_note'])) {
                $arrListnerData['narration'] .= ' [' . $PostData['payment_note'] . ']';
            }
            //Code to replace from ledger id From Bank/Cash

            if (isset($data['from_ledger'])) {
                $arrListnerData['from_ledger_id'] = $data['from_ledger']['receiving_ledger_id'];
                $arrListnerData['from_ledger_name'] = $data['from_ledger']['receiver_name'];
            }
            if (isset($data['to_ledger'])) {
                $arrListnerData['to_ledger_id'] = $data['to_ledger']['receiving_ledger_id'];
                $arrListnerData['to_ledger_name'] = $data['to_ledger']['receiver_name'];
            }

            if (isset($data['narration'])) {
                $arrListnerData['narration'] = '(Receipt No-' . $PostData['receipt_number'] . ') ' . $data['narration'];
            }

            // echo "arrListnerData"; echo "<pre>"; print_r($arrListnerData); echo "</pre>"; exit;

            if (!empty($PostData['tds_amount']) && $PostData['tds_amount'] > 0) {
                $arrListnerData['transaction_amount'] = round($PostData['payment_amount'] - $PostData['tds_amount'], 2);
                //print_r($arrListnerData);
                $arrLedgerEntry = $this->transactionLedgerEntry($arrListnerData);
                if (isset($arrLedgerEntry['error']) && !empty($arrLedgerEntry['error'])) {
                    $success = 0;
                } else {
                    $arrListnerData['transaction_amount'] = $PostData['tds_amount'];
                    $arrLedgerData['sco_id'] = $data['soc_id'];
                    $arrLedgerData['ledger_name'] = 'tds receivable';
                    $arrLedgerData['context'] = ''; //CURRENT_ASSETS_GROUP;
                    $arrLedgerData['behaviour'] = ASSET;

                    $arrTdsLedger = $this->checkledgerExistNew($arrLedgerData);
                    $arrListnerData['from_ledger_id'] = $arrTdsLedger['recieving_ledger_id'] ?? '';
                    $arrListnerData['from_ledger_name'] = $arrTdsLedger['receiver_name'];

                    if ($PostData['payment_mode'] == 'cashtransfer') {
                        $payment_mode_display = DISPLAY_CASH_TRANSFER;
                    } else {
                        $payment_mode_display = ucfirst($PostData['payment_mode']);
                    }

                    $arrListnerData['narration'] = 'TDS amount deducted against Invoice ' . $PostData['bill_number'] . ' dated ' . $this->getCurrentDate('display') . ' through ' . $payment_mode_display . $strNarration; //'Invoice '.$arrPostData['member_paid_invoice'].' '.$eachLedgerDetail['particular'].' Write Off';

                    //print_r($arrListnerData);
                    $arrLedgerEntry = $this->transactionLedgerEntry($arrListnerData);
                    if (isset($arrLedgerEntry['error']) && !empty($arrLedgerEntry['error'])) {
                        $success = 0;
                    }
                }
            } else {
                $arrLedgerEntry = $this->transactionLedgerEntry($arrListnerData);
                if (isset($arrLedgerEntry['error']) && !empty($arrLedgerEntry['error'])) {
                    $success = 0;
                }
            }
        }
        //exit;
        return $success;
    }
    public function getBankCashAccountDetail($data = [])
    {
        // Define the base query with joins and column selections
        $query = $this->tenantDB()->table('chsone_grp_ledger_tree as grpLedger')
            ->select([
                'grpLedger.ledger_account_id',
                'grpLedger.ledger_account_name',
                'grpLedger.context',
                'account.default_account',
                'account.default_bank_for_incidental',
                'account.default_bank_for_nonmember',
                'account.bank_name',
                'account.account_number',
                'account.bank_address',
                'account.bank_city',
                'account.bank_ifsc',
                'account.account_id',
            ])
            ->leftJoin('chsone_accounts_master as account', 'grpLedger.ledger_account_id', '=', 'account.ledger_account_id')
            ->where('grpLedger.soc_id', $data['soc_id'])
            ->where('grpLedger.entity_type', 'ledger')
            ->where('grpLedger.status', 1);

        // Filter by context based on 'only_bank' flag
        if (!empty($data['only_bank'])) {
            $query->where('grpLedger.context', 'bank');
        } else {
            $query->whereIn('grpLedger.context', ['cash', 'bank']);
        }

        // Execute the query and get results as an array
        $arrAccountDetail = $query->get()->toArray();

        return $arrAccountDetail;
    }

    public function getGeneralSetting($data = [])
    {
        $finalArray = $this->_getAllSetting($data);

        // Assuming $auth['soc_id'] is part of the $data array or fetched via some authentication mechanism.
        $socId = $data['soc_id'] ?? null;

        if ($socId) {
            $incomeInvoiceSetting = $this->tenantDB()->table('income_invoice_settings')->where('soc_id', $socId)
                ->orderBy('effective_date', 'desc')
                ->first();

            if ($incomeInvoiceSetting) {
                $finalArray['generalsettingid'] = $incomeInvoiceSetting->id;
                $finalArray['invoicing_frequency'] = $incomeInvoiceSetting->invoicing_frequency ?? '';
                $finalArray['effective_date'] = $incomeInvoiceSetting->effective_date;
            }
        }

        $finalArray['general_setting_key_ids'] = $this->_getAllSettingWithId($data);

        return $finalArray;
    }

    private function _getAllSetting($data)
    {
        $incomeinvoicesetting = $this->tenantDB()
            ->table('income_invoice_settings')
            ->where('soc_id', $data['soc_id'])
            ->first();
        $incomeinvoicegeneralsetting = $this->tenantDB()->table('income_invoice_general_settings')->where(array('soc_id' => $data['soc_id']))->get();

        $incomeinvoicegeneralsetting = json_decode(json_encode($incomeinvoicegeneralsetting), true);
        foreach ($incomeinvoicegeneralsetting as $key => $value) {
            $final_array[$value['setting_key']] = $value['setting_value'];
        }
        return $final_array;
    }

    private function _getAllSettingWithId($data)
    {
        $incomeinvoicegeneralsetting = $this->tenantDB()->table('income_invoice_general_settings')->where(array('soc_id' => $data['soc_id']))->get();

        $incomeinvoicegeneralsetting = json_decode(json_encode($incomeinvoicegeneralsetting), true);
        foreach ($incomeinvoicegeneralsetting as $key => $value) {
            $final_array[$value['setting_key']] = $value['id'];
        }
        return $final_array;
    }

    public function getLedgerAccountDetail(array $data): array
    {
        $arrAccountDetail = [
            'cash' => [],
            'bank' => [],
            'arrBank' => [],
            'arrCash' => [],
        ];

        $data = json_decode(json_encode($data), true);
        // Check if account details are provided
        if (!empty($data['account_detail'])) {

            foreach ($data['account_detail'] as $eachAccountDetail) {

                $context = strtolower($eachAccountDetail['context']);
                $ledgerId = $eachAccountDetail['ledger_account_id'];
                $ledgerName = $eachAccountDetail['ledger_account_name'];

                // Handle cash account details
                if ($context === 'cash' && empty($arrAccountDetail['cash'])) {
                    $arrAccountDetail['cash'] = [
                        'ledger_id' => $ledgerId,
                        'ledger_name' => $ledgerName,
                    ];
                    $arrAccountDetail['arrCash'][$ledgerId] = $ledgerName;
                }

                // Handle bank account details
                if ($context === 'bank') {
                    if ($eachAccountDetail['default_account'] == 1) {
                        $arrAccountDetail['bank'] = [
                            'ledger_id' => $ledgerId,
                            'ledger_name' => $ledgerName,
                        ];
                    }
                    $arrAccountDetail['arrBank'][$ledgerId] = $ledgerName;

                    // Default bank for incidental cases
                    if (!empty($data['default_bank_incidental']) && $eachAccountDetail['default_bank_for_incidental'] == 1) {
                        $arrAccountDetail['bank'] = [
                            'ledger_id' => $ledgerId,
                            'ledger_name' => $ledgerName,
                        ];
                    }

                    // Default bank for non-member cases
                    if (!empty($data['default_bank_nonmember']) && $eachAccountDetail['default_bank_for_nonmember'] == 1) {
                        $arrAccountDetail['bank'] = [
                            'ledger_id' => $ledgerId,
                            'ledger_name' => $ledgerName,
                        ];
                    }
                }
            }

            // Set the first available bank if no default is selected
            if (empty($arrAccountDetail['bank']) && !empty($arrAccountDetail['arrBank'])) {
                $firstBank = reset($arrAccountDetail['arrBank']);
                $firstBankId = key($arrAccountDetail['arrBank']);
                $arrAccountDetail['bank'] = [
                    'ledger_id' => $firstBankId,
                    'ledger_name' => $firstBank,
                ];
            }
        }

        return $arrAccountDetail;
    }

    public function getCreditAccountByIdResponse($soc_id, $id, $context)
    {
        if (!$soc_id) {
            $this->message = 'Society id required.';
            $this->status = 'error';
            $this->statusCode = 400;
            // return response()->json(['error' => 'Society id required.'], 400);
        }

        if (!$id) {
            $this->message = 'Credit account id required.';
            $this->status = 'error';
            $this->statusCode = 400;
            // return response()->json(['error' => 'Credit account id required.'], 400);
        }

        if (empty($context)) {
            $this->message = 'Credit account context required.';
            $this->status = 'error';
            $this->statusCode = 400;
            // return response()->json(['error' => 'Credit account context required.'], 400);
        }

        $queryBuilder = $this->tenantDB()->table('chsone_credit_accounts')->where('soc_id', $soc_id)
            ->where('account_id', $id)
            ->where('account_context', $context)
            ->orderBy('credit_account_id', 'desc')
            ->get();

        return $queryBuilder;
    }

    public function createMemberLedgerCredit($arrData = [])
    {
        $arrResponse = ['status' => 'error'];
        $arrPostData = $arrData['arrPostData'];

        if (!empty($arrPostData)) {
            // Member
            $arrIncomeInvoiceMemberDetail = $arrData['arrIncomeInvoiceMemberDetail'];

            if ($arrPostData['payment_amount'] > 0) {
                $countLedgerEntry = 0;

                if (!empty($arrIncomeInvoiceMemberDetail)) {
                    $arrPostData['unit_id'] = $arrIncomeInvoiceMemberDetail['fk_unit_id'];
                    $arrPostData['member_id'] = $arrIncomeInvoiceMemberDetail['id'];
                }

                if (!empty($arrPostData['payment_date']) && str_contains($arrPostData['payment_date'], '-')) {
                    $arrPostData['payment_date'] = $this->getDisplayDate($arrPostData['payment_date']);
                }

                if ($this->constants['ACCOUNT_MODULE_EXIST'] == 1) {
                    $arrListnerData = ['soc_id' => $arrData['soc_id'], 'unit_id' => $arrPostData['unit_id']];
                    $arrUnitDetails = $this->getUnitDetailById($arrListnerData);
                    $arrUnitDetails = json_decode(json_encode($arrUnitDetails), true);
                    if (!empty($arrUnitDetails)) {
                        $arrListnerData['ledger_id'] = $arrUnitDetails['ledger_account_id'];
                        $arrUnitLedgerDetails = $this->checkledgerExistNew($arrListnerData);

                        if (!empty($arrUnitLedgerDetails)) {
                            $countLedgerEntry = $this->paymentLedgerEntry([
                                'soc_id' => $arrData['soc_id'],
                                'arrPostData' => $arrPostData,
                                'arrUnitLedgerDetails' => $arrUnitLedgerDetails,
                                'from_ledger' => $arrData['from_ledger'] ?? '',
                                'to_ledger' => $arrData['to_ledger'] ?? '',
                                'narration' => $arrData['narration'] ?? '',
                            ]);

                            if (!$countLedgerEntry) {

                                $arrResponse['status'] = 'success';
                            }
                        }
                    }

                    // Send notification
                    if ($countLedgerEntry == 0) {
                        // Send notification code
                    }
                }
            }
        }

        return $arrResponse;
    }

    public function getUnitDetailById(array $data = [])
    {
        $soc_id = $data['soc_id'];
        $unit_id = $data['unit_id'];

        $unitDetails = $this->tenantDB()->table('chsone_units_master')
            ->where('soc_id', $soc_id)
            ->where('unit_id', $unit_id)
            ->first();

        return !empty($unitDetails) ? (array) $unitDetails : [];
    }
    public function paymentLedgerEntry($data = array())
    {
        $config = Config::get("constants");

        $countLedgerEntry = 0;

        if ($this->constants['ACCOUNT_MODULE_EXIST'] == 1) {

            $soc_id = $data['soc_id'];
            $arrPostData = $data['arrPostData'];
            $arrUnitLedgerDetails = $data['arrUnitLedgerDetails'];
            $arrUnitLedgerDetails = json_decode(json_encode($arrUnitLedgerDetails), true);

            if (isset($arrPostData['clearance_date']) && !empty($arrPostData['clearance_date'])) {
                $arrPostData['payment_date'] = $arrPostData['clearance_date'];
            }

            if (empty($arrPostData['payment_date'])) {
                $arrPostData['payment_date'] = $this->getCurrentDate('display');
            }

            //$arrIncomeAccounts = $this->incometrackerevent->incometracker('accountListener:checkledgerExistNew', array('auth' => $data['auth'], 'ledger_id' => $arrPostData['bank_account']));
            // print_r($this->constants['payment_mode_for_receipt_reversal']); exit();
            if (!empty($arrPostData['payment_mode']) && !in_array(strtolower($arrPostData['payment_mode']), $this->constants['payment_mode_for_receipt_reversal'])) {
                //  print_r(array(trim($arrPostData['payment_instrument']) . '_PG_LEDGER_ID'));
                $settingKey = [];
                if (!empty($arrPostData['payment_instrument'])) {
                    $settingKey[] = trim($arrPostData['payment_instrument']) . '_PG_LEDGER_ID';
                }

                $arrInvoiceGeneralSetting = $this->getInvoiceGeneralSetting([
                    'soc_id' => $data['soc_id'],
                    'setting_key' => $settingKey,
                ]);

                if (!empty($arrInvoiceGeneralSetting[0]['setting_value'])) {
                    $arrIncomeAccounts = $this->checkledgerExistNew(array('soc_id' => $data['soc_id'], 'ledger_id' => $arrInvoiceGeneralSetting[0]['setting_value']));
                    $arrIncomeAccounts['ledger_id'] = $arrIncomeAccounts['recieving_ledger_id'];
                    $arrIncomeAccounts['ledger_name'] = $arrIncomeAccounts['receiver_name'];
                } else {

                    if (!empty($data['to_ledger'])) {
                        $arrIncomeAccounts['ledger_id'] = $data['to_ledger']['recieving_ledger_id'];
                        $arrIncomeAccounts['ledger_name'] = $data['to_ledger']['receiver_name'];
                    } else {
                        return 1;
                    }
                }
            } else {

                if (isset($data['arrUnitLedgerDetails']['suspenseLedgerDetails']) && !empty($data['arrUnitLedgerDetails']['suspenseLedgerDetails'])) {
                    $arrIncomeAccounts['ledger_id'] = $data['arrUnitLedgerDetails']['suspenseLedgerDetails']['recieving_ledger_id'];
                    $arrIncomeAccounts['ledger_name'] = $data['arrUnitLedgerDetails']['suspenseLedgerDetails']['receiver_name'];
                } else {

                    // Getting bank/cash ledger details
                    $arrAccountDetail = $this->getBankCashAccountDetail(array('soc_id' => $data['soc_id'])); // get all Unit details

                    $arrLedgerAccountDetail = $this->getLedgerAccountDetail(array(
                        'account_detail' => $arrAccountDetail,
                    )); // get all Unit details

                    if (!empty($arrLedgerAccountDetail)) {
                        $arrIncomeAccounts['ledger_id'] = $arrLedgerAccountDetail['cash']['ledger_id'];
                        $arrIncomeAccounts['ledger_name'] = $arrLedgerAccountDetail['cash']['ledger_name'];
                        if (strtolower($arrPostData['payment_mode']) != 'cash') {
                            if (!empty($arrPostData['bank_account'])) {
                                $arrIncomeAccounts['ledger_id'] = $arrPostData['bank_account'];
                                $arrIncomeAccounts['ledger_name'] = $arrLedgerAccountDetail['arrBank'][$arrPostData['bank_account']];
                            } else {
                                $arrIncomeAccounts['ledger_id'] = $arrLedgerAccountDetail['bank']['ledger_id'];
                                $arrIncomeAccounts['ledger_name'] = $arrLedgerAccountDetail['bank']['ledger_name'];
                            }
                        }
                    }
                }
            }

            if ($arrPostData['payment_mode'] == 'cashtransfer') {
                $paymentModeForNarration = $this->constants['DISPLAY_CASH_TRANSFER'] ?? 'Electronic Fund Transfer';
            } else {
                $paymentModeForNarration = $arrPostData['payment_mode'] ?? null;
            }

            // $paymentModeForNarration = $arrPostData ['payment_mode'];
            $strNarration = '';
            if (!empty($arrPostData['payment_mode']) && in_array(strtolower($arrPostData['payment_mode']), $this->constants['payment_mode_for_clearance'])) {
                $strNarration = ' with transaction ref. (' . ($arrPostData['transaction_reference'] ?? '') . ', ' . ($arrPostData['payment_instrument'] ?? '') . ')';
            } elseif (!empty($arrPostData['payment_mode']) && strtolower($arrPostData['payment_mode']) == 'cashtransfer') {
                $strNarration = ' with payment ref. (' . ($arrPostData['transaction_reference'] ?? '') . ')';
            } elseif (!empty($arrPostData['payment_mode']) && !in_array(strtolower($arrPostData['payment_mode']), $this->constants['payment_mode_for_receipt_reversal'])) {
                $strNarration = ' with transaction ref. (' . ($arrPostData['transaction_reference'] ?? '') . ')';

                switch ($arrPostData['payment_mode']) {
                    case 'YES_BANK_PG':
                        $paymentModeForNarration = DISPLAY_YES_BANK;
                        break;
                    case 'YES_BANK_ECOLLECT':
                        $paymentModeForNarration = DISPLAY_YES_BANK_ECOLLECT;
                        break;
                    case 'MOBIKWIK_WALLET':
                        $paymentModeForNarration = MOBIKWIK_WALLET;
                        break;
                    case 'MOBIKWIK_PG':
                        $paymentModeForNarration = DISPLAY_MOBIKWIK_PG;
                        break;
                    case 'CASHFREE_PG':
                        $paymentModeForNarration = DISPLAY_CASHFREE_PG;
                        break;
                    case 'PAYTM_PG':
                        $paymentModeForNarration = DISPLAY_PAYTM;
                        break;
                    case 'HDFC_PG':
                        $paymentModeForNarration = DISPLAY_HDFC_PG;
                        break;
                    case 'ATOM_PG':
                        $paymentModeForNarration = DISPLAY_ATOM_PG;
                        break;
                    case 'cashtransfer':
                        $paymentModeForNarration = DISPLAY_CASH_TRANSFER;
                        break;
                }
            }
            // echo $arrIncomeInvoicePaymentTracker['payment_mode'];exit;
            // Ledger entry for payment amount
            $arrLedgerTransactionData = array(
                'soc_id' => $soc_id,
            );

            $arrLedgerTransactionData['voucher_type'] = isset($arrPostData['voucher_type_credit']) ? $this->constants['VOUCHER_CREDIT'] : $this->constants['VOUCHER_RECEIPT'] ?? '';
            $arrLedgerTransactionData['voucher_reference_number'] = $arrPostData['receipt_number'] ?? '';
            $arrLedgerTransactionData['voucher_reference_id'] = $arrPostData['payment_tracker_id'];
            $arrLedgerTransactionData['from_ledger_id'] = $arrIncomeAccounts['ledger_id'];
            $arrLedgerTransactionData['to_ledger_id'] = $arrUnitLedgerDetails['receiving_ledger_id'] ?? '';
            $arrLedgerTransactionData['transaction_date'] = !empty($arrPostData['payment_date']) ? $this->getDatabaseDate($arrPostData['payment_date']) : $this->getCurrentDate('database');
            $arrLedgerTransactionData['transaction_amount'] = $arrPostData['payment_amount'];

            // $arrLedgerTransactionData['narration'] = 'Amount received against Invoice ' . $arrPostData['member_paid_invoice']  ?? '' . ' dated ' . $this->getDisplayDate($arrPostData['payment_date']) . ' through ' . $paymentModeForNarration . $strNarration; // 'Invoice '.$arrPostData['member_paid_invoice'].' '.$eachLedgerDetail['particular'].' Write Off';
            $arrLedgerTransactionData['narration'] = 'Amount received against Invoice ' .
            (!empty($arrPostData['member_paid_invoice']) ? $arrPostData['member_paid_invoice'] : '') .
            ' dated ' . $this->getDisplayDate($arrPostData['payment_date']) .
                ' through ' . $paymentModeForNarration .
                $strNarration;

            if ((empty($arrPostData['total_unpaid_amount']) || $arrPostData['total_unpaid_amount'] <= 0) && empty($arrPostData['member_paid_invoice'])) {
                // $arrLedgerTransactionData['narration'] = 'Advance payment received from ' . ucwords($arrPostData['received_from']) . ' dated ' . $this->getDisplayDate($arrPostData['payment_date']) . ' through ' . $paymentModeForNarration . $strNarration; // $eachInvoiceDetail['invoice_number'] . ' late payment charges';
                $arrLedgerTransactionData['narration'] = 'Amount received against Invoice ' .
                ($arrPostData['member_paid_invoice'] ?? '') .
                ' dated ' . $this->getDisplayDate($arrPostData['payment_date']) .
                    ' through ' . $paymentModeForNarration .
                    $strNarration;
            }
            if ($arrPostData['bill_type'] == 'suspense') {
                $arrLedgerTransactionData['narration'] = 'Unidenfied payment from ' . ucwords($arrPostData['received_from']) . ' dated ' . $this->getDisplayDate($arrPostData['payment_date']) . ' through ' . $paymentModeForNarration . $strNarration;
            }

            $arrLedgerTransactionData['from_ledger_name'] = $arrIncomeAccounts['ledger_name'];
            $arrLedgerTransactionData['to_ledger_name'] = $arrUnitLedgerDetails['receiver_name'];
            $arrLedgerTransactionData['payment_reference'] = !empty($arrPostData['transaction_reference'])
            ? $arrPostData['transaction_reference']
            : null;

            // $arrLedgerTransact ionData['payment_reference']=0;
            $arrLedgerTransactionData['transaction_type'] = '';
            $arrLedgerTransactionData['mode_of_payment'] = $arrPostData['payment_mode'] ?? null;
            $arrLedgerTransactionData['other_payment_ref'] = '';

            // Code to replace from ledger id From Bank/Cash
            if (!empty($data['from_ledger'])) {
                $arrLedgerTransactionData['from_ledger_id'] = $data['from_ledger']['receiving_ledger_id'];
                $arrLedgerTransactionData['from_ledger_name'] = $data['from_ledger']['receiver_name'];
            }

            if (!empty($data['to_ledger']) && !empty($data['to_ledger']['recieving_ledger_id']) && !empty($data['to_ledger']['receiver_name'])) {
                $arrLedgerTransactionData['to_ledger_id'] = $data['to_ledger']['recieving_ledger_id'];
                $arrLedgerTransactionData['to_ledger_name'] = $data['to_ledger']['receiver_name'];
            }

            if (isset($data['narration'])) {
                $arrLedgerTransactionData['narration'] = $data['narration'];
            }
            $arrLedgerTransactionData['narration'] = '(Receipt No-' . ($arrPostData['receipt_number'] ?? '') . ') ' . $arrLedgerTransactionData['narration'];

            if (!empty($arrPostData['payment_note'])) {
                $arrLedgerTransactionData['narration'] .= ' [' . $arrPostData['payment_note'] . ']';
            }

            //echo 'pay'; print_r($arrLedgerTransactionData); exit();
            if (!empty($arrPostData['tds_amount']) && $arrPostData['tds_amount'] > 0) {

                $arrLedgerTransactionData['transaction_amount'] = round($arrPostData['payment_amount'] - $arrPostData['tds_amount'], 2);
                $arrLedgerEntry = $this->transactionLedgerEntry($arrLedgerTransactionData);

                if (isset($arrLedgerEntry['error']) && !empty($arrLedgerEntry['error'])) {
                    $countLedgerEntry++;
                } else {
                    $arrLedgerTransactionData['transaction_amount'] = $arrPostData['tds_amount'];
                    $arrListnerData['soc_id'] = $soc_id;
                    $arrListnerData['ledger_name'] = 'tds receivable';
                    $arrListnerData['context'] = ''; // CURRENT_ASSETS_GROUP;
                    $arrListnerData['behaviour'] = ASSET;

                    $arrTdsLedger = $this->checkledgerExistNew($arrListnerData);
                    $arrLedgerTransactionData['from_ledger_id'] = $arrTdsLedger['recieving_ledger_id'];
                    $arrLedgerTransactionData['from_ledger_name'] = $arrTdsLedger['receiver_name'];
                    $arrLedgerTransactionData['narration'] = 'TDS amount deducted against Invoice ' . $arrPostData['member_paid_invoice'] . ' dated ' . $this->getDisplayDate($arrPostData['payment_date']) . ' through ' . $paymentModeForNarration . $strNarration; // 'Invoice '.$arrPostData['member_paid_invoice'].' '.$eachLedgerDetail['particular'].' Write Off';

                    $arrLedgerEntry = $this->transactionLedgerEntry($arrLedgerTransactionData);
                    if (isset($arrLedgerEntry['error']) && !empty($arrLedgerEntry['error'])) {
                        $countLedgerEntry++;
                    }
                }
            } else {

                //echo 'led';
                $arrLedgerEntry = $this->transactionLedgerEntry($arrLedgerTransactionData);

                // print_r($arrLedgerEntry); exit();
                if (isset($arrLedgerEntry['error']) && !empty($arrLedgerEntry['error'])) {
                    $countLedgerEntry++;
                }
            }

            //Ledger entry for invoice writeoff
            if (!empty($arrPostData['writeoff_amount']) && $arrPostData['writeoff_amount'] > 0) {
                if ($arrPostData['writeoff_amount'] >= 1000) {
                    $arrExpenseWriteOff = $this->checkledgerExistNew(array('soc_id' => $data['soc_id'], 'ledger_name' => 'Expense Bad Debt', 'context' => EXPENSE, 'behaviour' => EXPENSE));
                    if (empty($arrExpenseWriteOff['recieving_ledger_id'])) {
                        $arrParentExpense = $this->getLedgerDetail(array('soc_id' => $data['soc_id'], 'ledger_name' => 'Indirect Expense', 'context' => EXPENSE, 'behaviour' => EXPENSE));
                        if (!empty($arrParentExpense['ledger_account_id'])) {
                            $arrExpenseWriteOff['receiver_name'] = 'Expense Bad Debt';
                            $arrExpenseWriteOff['recieving_ledger_id'] = $this->createNewLedgerExit(array('soc_id' => $data['soc_id'], 'ledger_name' => $arrExpenseWriteOff['receiver_name'], 'context' => EXPENSE, 'behaviour' => EXPENSE, 'parent_id' => $arrParentExpense['ledger_account_id']));
                        }
                    }
                } else {
                    $arrExpenseWriteOff = $this->checkledgerExistNew(array('soc_id' => $data['soc_id'], 'ledger_name' => 'Expense Write Off', 'context' => EXPENSE, 'behaviour' => EXPENSE));
                }
                if (empty($arrExpenseWriteOff['recieving_ledger_id'])) {
                    return 1;
                }
                $arrLedgerTransactionData['transaction_amount'] = $arrPostData['writeoff_amount'];
                $arrLedgerTransactionData['from_ledger_id'] = $arrExpenseWriteOff['recieving_ledger_id'];
                $arrLedgerTransactionData['from_ledger_name'] = $arrExpenseWriteOff['receiver_name'];

                $arrLedgerTransactionData['narration'] = 'Amount writeoff against Invoice ' . $arrPostData['member_paid_invoice'] . ' dated ' . $this->getDisplayDate($arrPostData['payment_date']); //'Invoice '.$arrPostData['member_paid_invoice'].' '.$eachLedgerDetail['particular'].' Write Off';
                $arrLedgerEntry = $this->transactionLedgerEntry($arrLedgerTransactionData);
                if (isset($arrLedgerEntry['error']) && !empty($arrLedgerEntry['error'])) {
                    $countLedgerEntry++;
                }
            }
        }

        return $countLedgerEntry;
    }

    public function saveCreditAccountResponse($data = array())
    {
        $user = $data['user'];
        $id = $data['id'];

        $data = $data['data'];
        $data['user'] = $user;

        if ($id) {
            // Prepare the data to be updated
            $updateData = [
                'updated_by' => $user ?? 0,
                'updated_date' => date('Y-m-d H:i:s'),
                'use_credit_after' => !empty($data['adjustable_date']) ? $data['adjustable_date'] : null,
                'is_locked' => 0,
                'use_credit_for' => !empty($data['used_for']) ? $data['used_for'] : null,
                'is_invoice_rectification' => isset($data['is_invoice_rectification']) && !empty($data['is_invoice_rectification']) ? $data['is_invoice_rectification'] : null,
                'income_account_id' => isset($data['income_account_ledger_id']) && !empty($data['income_account_ledger_id']) ? $data['income_account_ledger_id'] : null,
                'narration' => $data['narration'],
                'use_credit' => isset($data['credit_used_type']) && !empty($data['credit_used_type']) ? $data['credit_used_type'] : 'adjustable',
            ];

            // Update the record in the database
            $result = $this->tenantDB()
                ->table('chsone_credit_accounts')
                ->where('credit_account_id', $id)
                ->where('soc_id', $data['soc_id'])
                ->update($updateData);

            // Save data
            if ($result) {
                return $result;
            } else {
                return false;
            }
        } else {
            if ($data['credit_used_type'] == 'both') {
                $data['credit_used_type'] = 'adjustable';
                $data['payment_amount'] = $data['adjustable_amount'];
                $saveResponse = $this->saveCreditNote($data);
                if ($saveResponse['error'] == false) {
                    $data['credit_used_type'] = 'refundable';
                    $data['payment_amount'] = $data['refundable_amount'];
                    $data['used_for'] = null;
                    $data['adjustable_date'] = null;

                    $saveResponse = $this->saveCreditNote($data);
                    if ($saveResponse['error'] == false) {
                        return $saveResponse;
                    }
                }
            } else {
                $saveResponse = $this->saveCreditNote($data);
                if ($saveResponse['error'] == false) {
                    return $saveResponse;
                }
            }
        }
    }

    public function getMemberDetail($data = [])
    {
        $arrMemberMaster = [];

        // Use parameterized queries to avoid SQL injection
        $memberType = $this->tenantDB()->table('chsone_member_type_master')
            ->where('soc_id', $data['soc_id'])
            ->where('member_type_name', 'Primary')
            ->first();

        if (!$memberType) {
            // Log or handle the case where no member type is found
            // \Log::error('Member type "Primary" not found for soc_id: ' . $data['soc_id']);
            // return $arrMemberMaster;
            $this->message = 'Member type "Primary" not found for soc_id: ' . $data['soc_id'];
            $this->status = 400;
        }

        // Fetch the member master details using the member type ID and unit ID
        $objMemberMaster = $this->tenantDB()->table('chsone_members_master')
            ->where('soc_id', $data['soc_id'])
            ->where('fk_unit_id', $data['unit_id'])
            ->where('status', 1)
            ->where('member_type_id', $memberType->member_type_id)
            ->first();
        $objMemberMaster = json_decode(json_encode($objMemberMaster), true);

        return $objMemberMaster;
    }

    public function addCreditAmountresponse($data, $id = 0)
    {

        $id = ($id) ? $id : '';
        if ($data['credit_used_type'] == 'adjustable' && empty($data['adjustable_date'])) {
            //            $data['adjustable_date'] = date("Y-m-d");  // store today date
        }

        $response = $this->saveCreditAccountResponse(array(
            'auth' => true,
            'process' => 'fetch',
            'soc_id' => $data['soc_id'],
            'id' => $id,
            'data' => $data,
            'user' => $data['user'] ?? 0,
            'username' => $data['user']['first_name'] . ' ' . $data['user']['last_name'],
        ));

        return $response;
    }

    public function getInvoiceGeneralSetting(array $data = [])
    {

        $invoiceGeneralSetting = $this->tenantDB()->table('income_invoice_general_settings')
            ->where('soc_id', $data['soc_id'])
            ->whereIn('setting_key', $data['setting_key'])
            ->get();

        return $invoiceGeneralSetting->isNotEmpty() ? $invoiceGeneralSetting->toArray() : [];
    }

    public function transactionLedgerEntry($data = [])
    {
        // Initialize response array
        $arrResponse = [];

        // Validate required fields
        if (empty(trim($data['from_ledger_id'])) || empty(trim($data['to_ledger_id']))) {
            return ['error' => true, 'message' => 'From or To ledger ID is missing'];
        }
        // Set default transaction date if not provided
        $data['transaction_date'] = $data['transaction_date'] ?? date('Y-m-d H:i:s');

        // Execute the transaction entry and capture the transaction ID
        $transaction_id = $this->executeTransactionEntry($data);

        // Check if the transaction was successful
        if ($transaction_id) {
            $arrResponse = ['success' => true, 'transaction_id' => $transaction_id];
        } else {
            $arrResponse = ['error' => true, 'message' => 'Transaction failed'];
        }

        return $arrResponse;
    }

    public function executeTransactionEntry($data)
    {
        $vouchers_array = [];

        //         // Check if voucher type contains an underscore and split if found
        if (isset($data['voucher_type']) && strpos($data['voucher_type'], '_') !== false) {
            $vouchers_array = explode('_', $data['voucher_type']);
            $data['voucher_type'] = $vouchers_array[0]; // Set the primary voucher type
        }
        // Attempt the first transaction entry
        $data['transaction_from_id'] = $this->_addTransactionEntry($data);

        // If the first transaction entry is successful
        if (!empty($data['transaction_from_id'])) {
            // Debug to confirm the first transaction ID

            // Check if a secondary voucher type exists and needs processing
            if (count($vouchers_array) >= 2) {
                $data['voucher_type'] = $vouchers_array[1]; // Set the secondary voucher type
            }

            // Attempt the second transaction entry
            $secondEntrySuccess = $this->_addTransactionEntry($data);
            // Check if the second entry was successful
            if ($secondEntrySuccess) {
                // Optional: commit transaction if using DB transactions
                // $this->soc_db_w->commit();
                return $data['transaction_from_id'];
            } else {
                // Optional: rollback transaction if the second entry fails
                // $this->soc_db_w->rollback();
                // dd('Failed to insert second transaction entry');
                // Log::error('Failed to insert second transaction entry', ['data' => $data]);
                return false;
            }
        } else {
            // Optional: rollback transaction if the first entry fails
            // $this->soc_db_w->rollback();
            // dd('Failed to insert first transaction entry');
            // Log::error('Failed to insert first transaction entry', ['data' => $data]);
            return false;
        }
    }

    public function _addTransactionEntry($data)
    {
        // Set transaction type and ledger details based on the mode
        if (!empty($data['transaction_from_id'])) {
            $data['transaction_type'] = "cr";
            $ledger_id = $data['to_ledger_id'];
            $ledger_name = $data['to_ledger_name'];
        } else {
            $data['transaction_type'] = "dr";
            $ledger_id = $data['from_ledger_id'];
            $ledger_name = $data['from_ledger_name'];
        }

        // Ensure data is an array
        $data = json_decode(json_encode($data), true);
        $soc_id = $data['soc_id'];

        // Check for opening balance transaction
        if (!empty($data['is_opening']) && $data['is_opening'] == 1) {
            $conditions = [
                ['soc_id', '=', $soc_id],
                ['is_opening_balance', '=', 1],
                ['ledger_account_id', '=', $ledger_id],
            ];

            $txn_entry = $this->tenantDB()->table('chsone_ledger_transactions')
                ->where($conditions)
                ->first();

            $data['txn_id'] = $txn_entry->txn_id ?? '';
        }

        // Prepare the data for insertion
        $transactionData = [
            'soc_id' => (string) $soc_id,
            'voucher_type' => (string) $data['voucher_type'],
            'voucher_reference_number' => (int) ($data['voucher_reference_number'] ?? 0),
            'voucher_reference_id' => (int) ($data['voucher_reference_id'] ?? 0),
            'transaction_date' => (string) $data['transaction_date'],
            'ledger_account_id' => (int) $ledger_id,
            'ledger_account_name' => (string) $ledger_name,
            'transaction_type' => (string) $data['transaction_type'],
            'payment_mode' => isset($data['payment_mode']) ? (string) $data['payment_mode'] : null,
            'transaction_amount' => (float) ($data['transaction_amount'] ?? 0),
            'other_reference_id' => (string) ($data['other_payment_ref'] ?? ''),
            'txn_from_id' => (string) ($data['transaction_from_id'] ?? ''),
            'memo_desc' => (string) $data['narration'],
            'is_opening_balance' => (int) ($data['is_opening'] ?? 0),
            'is_reconciled' => (int) ($data['is_reconciled'] ?? 0),
            'created_by' => (int) ($data['user_id'] ?? 0),
            'added_on' => date('Y-m-d H:i:s'),
            'is_cancelled' => !empty($data['is_cancelled']) ? 1 : 0,
        ];

        if (!empty($data['payment_reference'])) {
            $transactionData['payment_reference'] = $data['payment_reference'];
        }

        // Log the transaction data to ensure it's correct before insertion
        // Log::info('Attempting transaction entry', ['transactionData' => $transactionData]);

        // Attempt to insert the transaction data
        $txn = $this->tenantDB()->table('chsone_ledger_transactions')->insertGetId($transactionData);

        // Return the transaction ID if successful, otherwise false
        return $txn ? $txn : false;
    }
}
