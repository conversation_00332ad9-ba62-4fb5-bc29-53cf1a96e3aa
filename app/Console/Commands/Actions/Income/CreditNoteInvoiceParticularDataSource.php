<?php

namespace App\Console\Commands\Actions\Income;

use App\Console\Commands\Action;

class CreditNoteInvoiceParticularDataSource extends Action
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'datasource:creditNoteInvoiceParticular {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Get the credit note details';

    /**
     * Execute the console command.
     */
    public function apply()
    {

        $id = $this->input['id'];

        $inovice_number = $this->tenantDB()->table('income_unit_invoices')
            ->select('invoice_number')
            ->where('unit_invoice_id', $id)
            ->pluck('invoice_number')
            ->first();

        // ->rightJoin('income_invoice_particular', 'income_unit_invoices.invoice_number', '=', 'income_invoice_particular.invoice_number')

        $income_invoice_particular_data = $this->tenantDB()->table('income_invoice_particular')
            ->where('invoice_number',  $inovice_number)
            ->get();

        $this->data = $income_invoice_particular_data;
        $this->meta['pagination']['total'] = $income_invoice_particular_data->count();

    }
}
