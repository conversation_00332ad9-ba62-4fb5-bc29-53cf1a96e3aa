<?php

namespace App\Console\Commands\Actions\Income;

use Illuminate\Support\Facades\DB;
use App\Console\Commands\Action;

class BillableNoteDataSource extends Action
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'datasource:BillableNoteList {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Get the billable note details';

    protected $formatter =  [
        'id' => '',
        'income_billable_item_id' => '',
        'unit_id' => '',
        'member_id' => '',
        'debit_note_type' => '',
        'note' => '',
        'invoice_number' => '',
        'expense_id' => '',
        'amount' => '',
        'from_date' => '',
        'to_date' => '',
        'created_date' => '',
        'income_account_id' => '',
        'is_reversal' => '',
        'tax_class_id' => '',
        'member_first_name' => '',
        'member_last_name' => '',
        'member_type_id' => '',
        'soc_building_name' => '',
        'unit_flat_number' => '',
        'tax_class_name' => '',
        'particular' => '',
        'invoice_disable' => '',
        'is_editable' => '',
        'is_deletable' => '',
    ];

    protected $formatterByKeys =  ['id'];

    protected $mapper = [
        'id' => 'note.income_billable_item_id',
    ];

    /**
     * Execute the console command.
     */
    public function apply()
    {
        $page = $this->input['page'] ?? 1;
        $per_page = !empty($this->input['per_page']) ? $this->input['per_page']: ($this->hugeData ? 100000 : 10);

        $offset = ($page - 1) * $per_page;

        $unit_flat_number = '';
        $soc_building_name = '';
        $unit_name =  '';
        $member_name = '';
        $searchTerm = $this->input['filters']['search'] ?? '';

        if (isset($this->input['filters'])) {

            $unit_flat_number = !empty($this->input['filters']['unit_flat_number']) ? $this->input['filters']['unit_flat_number'] : '';
            unset($this->input['filters']['unit_flat_number']);


            $soc_building_name = !empty($this->input['filters']['soc_building_name']) ? $this->input['filters']['soc_building_name'] : '';
            unset($this->input['filters']['soc_building_name']);


            $unit_name = !empty($this->input['filters']['unit_name']) ? $this->input['filters']['unit_name'] : '';
            unset($this->input['filters']['unit_name']);

            $member_name = !empty($this->input['filters']['member_name']) ? $this->input['filters']['member_name'] : '';
            unset($this->input['filters']['member_name']);

        }

        $obj = $this->tenantDB()->table('income_billable_item AS note')
        ->select(
            'note.income_billable_item_id as id',
            'note.income_billable_item_id',
            'note.unit_id',
            'note.member_id',
            DB::raw("CASE 
                WHEN note.debit_note_type = 'debit_note' THEN 'Debit Note' 
                ELSE note.debit_note_type 
                END as debit_note_type"),
            'note.note',
            'note.expense_id',
            'note.amount',
            DB::raw("DATE_FORMAT(note.from_date, '%d/%m/%Y') as from_date"),
            DB::raw("DATE_FORMAT(note.to_date, '%d/%m/%Y') as to_date"),
            'note.created_date',
            'note.income_account_id',
            'note.is_reversal',
            'note.tax_class_id',
            'memmaster.member_first_name',
            'memmaster.member_last_name',
            'memmaster.member_type_id',
            'unitmaster.soc_building_name',
            'unitmaster.unit_flat_number',
            'tax.tax_class_id',
            'tax.tax_class_name',
            'icbc.particular'
        )
        ->leftJoin('chsone_members_master AS memmaster', 'note.member_id', '=', 'memmaster.id')
        ->leftJoin('chsone_units_master AS unitmaster', 'note.unit_id', '=', 'unitmaster.unit_id')
        ->leftJoin('chsone_tax_classes AS tax', 'note.tax_class_id', '=', 'tax.tax_class_id')
        ->leftJoin('income_common_area_charges AS icbc', 'note.income_account_id', '=', 'icbc.id')
        ->where('note.status', 1)
        ->where(function ($obj) {
            $obj->where('note.debit_note_type', 'debit_note')
                  ->orWhere('note.debit_note_type', 'Debit Note')
                  ->orWhere('note.debit_note_type', 'debit note');
        })
        ->where('note.unit_id', '!=', '')
        ->where('note.member_id', '!=', '')
        ->selectRaw("IFNULL(note.invoice_number, 'Next Invoice') AS invoice_number")
        ->selectRaw("IF(note.invoice_number IS NOT NULL, true, false) as invoice_disable")
        ->orderBy('note.income_billable_item_id', 'desc');


        if($unit_flat_number){
            $obj = $obj->where('unitmaster.unit_flat_number', 'like', '%' . $unit_flat_number . '%');
            }

            if($soc_building_name){
                $obj = $obj->where('unitmaster.soc_building_name', 'like', '%' . $soc_building_name . '%');
            }

            if ($unit_name) {
                $obj = $obj->where(DB::raw("CONCAT(unitmaster.soc_building_name, ' / ', unitmaster.unit_flat_number)"), 'like', '%' . $unit_name . '%');
            }


            if($member_name){
                $obj = $obj->where('memmaster.member_first_name', 'like', '%' . $member_name . '%');
            }

            // Define the columns to search
            $columns = [
                'note.income_billable_item_id',
                'note.unit_id',
                'note.member_id',
                'note.debit_note_type',
                'note.note',
                'note.expense_id',
                'note.amount',
                'note.from_date',
                'note.to_date',
                'note.created_date',
                'note.income_account_id',
                'note.is_reversal',
                'note.tax_class_id',
                'memmaster.member_first_name',
                'memmaster.member_last_name',
                'memmaster.member_type_id',
                'unitmaster.soc_building_name',
                'unitmaster.unit_flat_number',
                'tax.tax_class_id',
                'tax.tax_class_name',
                'icbc.particular'
            ];

            if ($searchTerm) {
                $searchTerms = explode(' ', $searchTerm); // Split the search term by spaces

                $obj->where(function ($q) use ($columns, $searchTerms) {
                    foreach ($searchTerms as $term) {
                        $q->where(function ($query) use ($columns, $term) {
                            foreach ($columns as $column) {
                                $query->orWhere($column, 'LIKE', '%' . $term . '%');
                            }

                            // Handle the derived fields separately
                            $query->orWhere(DB::raw("IFNULL(note.invoice_number, 'Next Invoice')"), 'LIKE', '%' . $term . '%')
                                ->orWhere(DB::raw("IF(note.invoice_number IS NOT NULL, true, false)"), 'LIKE', '%' . $term . '%');
                        });
                    }
                });
            }


        $count = $obj->count();

        $obj = $obj->offset($offset);
        $obj = $obj->limit($per_page);

        $result = $obj->get();

        $result = $obj->get()->map(function ($item) {
            $item->particular = ucwords(strtolower($item->particular));
            return $item;
        });

        foreach($result as $key => $value){

            if(empty($value->invoice_number) && empty($value->is_reversal)){
                $result[$key]->is_editable = 0;
            }else{
                $result[$key]->is_editable = 1;
            }

            if(!$value->expense_id){
                $result[$key]->is_deletable = 0;
            }
            else{
                $result[$key]->is_deletable = 1;
            }
        }
        $this->data = $this->format($result->toArray());
        $this->meta['pagination']['total'] = $count;

    }
}
