<?php

namespace App\Console\Commands\Actions\Income;

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Config;
use App\Console\Commands\Action;
use App\Http\Traits\NotificationTraits;
use App\Models\Tenants\ChsoneInvoiceGeneration;
use App\Models\Tenants\ChsoneGrpLedgerTree;
use App\Models\Tenants\ChsoneInvoiceSetting;
use App\Models\Tenants\ChsoneMembersMaster;
use App\Models\Tenants\ChsoneMemberTypeMaster;
use App\Models\Tenants\ChsoneUnitsMaster;
use App\Models\Tenants\IncomeCommonBillingCharge;
use App\Models\Tenants\IncomeCommonBillingPayment;
use App\Models\Tenants\IncomeInvoiceGeneralSetting;
use App\Models\Tenants\IncomeInvoicePaymentTracker;
use App\Models\Tenants\IncomeInvoiceSetting;
use App\Models\Tenants\IncomeNonmemberBill;
use App\Models\Tenants\IncomeUnitInvoice;
use App\Models\Tenants\SocAccountFinancialYearMaster;
use Exception;

class PaymentTrackerConfirmationBounceDataSource extends Action
{
    protected $constants;
    use NotificationTraits;

    public function __construct()
    {
        parent::__construct();
        $this->constants = Config::get("constants");
    }

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'datasource:PaymentTrackerConfirmationBounceDataSource {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Payment Tracker Confirmation Bounced data source';

    protected $formatter =  [];

    protected $formatterByKeys =  [];

    protected $mapper = [];

    /**
     * Execute the console command.
     */
    public function apply()
    {
        try     {
            $soc_id = $this->input['company_id'];
            $checkIfPaymentTrackerIdExists = $this->checkIfPaymentTrackerIdExists($this->input['payment_tracker_id']);
            if (!$checkIfPaymentTrackerIdExists) {
                $this->message = "Payment Tacker Id does not exists.";
                $this->status = 'error';
                $this->statusCode = 400;
                return false;
            }
            $arrGeneralSettingData = array('soc_id' => $soc_id, 'setting_key' => array('INCOME_PAYMENT_MODE'));
            $arrInvoiceGeneralSetting = $this->getInvoiceGeneralSetting($arrGeneralSettingData);
            $setting_value = (!empty($arrInvoiceGeneralSetting) && isset($arrInvoiceGeneralSetting[0]['setting_value']))
            ? $arrInvoiceGeneralSetting[0]['setting_value']
            : ''; 
    
            $payment_mode   =   explode(',', $setting_value);
            $arrPaymentMode =   array_combine($payment_mode, $payment_mode);
            $payment_tracker_id = $this->input['payment_tracker_id'];
            $chsoneInvoiceGeneration = new ChsoneInvoiceGeneration();
            $creditNoteAddDataSource = new creditNoteAddDataSource();
            $incomeUnitInvoice = new IncomeUnitInvoice();
            $paymemberbillDataSource = new PaymemberbillDataSource();
            $updatePayNonMemberBillDataSource = new UpdatePayNonMemberBillDataSource();
            $action = 2;
            if (!empty($payment_tracker_id)) {
                $arrPaymentTrackerListener = array('soc_id' => $soc_id, 'payment_tracker_id' => $payment_tracker_id, 'status' => 'P', 'current_date' => date('Y-m-d'));
                if ($action == 3) {
                    $arrPaymentTrackerListener['status'] = 'R';
                } elseif ($action == 2) {
                    $arrPaymentTrackerListener['status'] = array('P', 'R');
                } elseif ($action == 4) {
                    $arrPaymentTrackerListener = array('soc_id' => $soc_id, 'payment_tracker_id' => $payment_tracker_id, 'status' => 'Y', 'payment_reversal' => 'y', 'bill_type' => array('member', 'creditaccount-member'));
                }
                //            echo '<pre>xcf'; print_r($arrPaymentTrackerListener); exit;
                $arrPostData = $this->getInvoicePaymentTrackerDetail($arrPaymentTrackerListener); // get all Unit details
                if (!empty($arrPostData)) {
    
                    $unit_id = $arrPostData['unit_id'];
                    $arrPostData['tds_amount'] = $arrPostData['tds_deducted'];
                    $arrDataListener['soc_id'] = $soc_id;
                    $arrDataListener['unit_id'] = $unit_id;
                    $arrPostData['payment_tracker_id'] = $arrPostData['id'];
                    if (!empty($unit_id)) {
                        $arrIncomeInvoiceMemberDetail = $this->getMemberDetail($arrDataListener); // get all Unit details\
                        if (empty($arrIncomeInvoiceMemberDetail)) {
                            $this->message = 'Member not allotted to unit.';
                            $this->status = 'error';
                            $this->statusCode = 400;
                            return false;
                        }
                        $arrPostData['member_name'] = $arrIncomeInvoiceMemberDetail['member_first_name'] . ' ' . $arrIncomeInvoiceMemberDetail['member_last_name'];
                    }
                    $payment_date = $this->input["receipt_date"] ?? date('Y-m-d');
                    if (in_array($action, array(1, 3)) && !empty($payment_date)) {
                        if ($payment_date < $arrPostData['payment_date']) {
                            $arrAjaxResonse = array('status' => 'error', 'message' => 'Cheque clearance date cannot be lesser than Cheque submission date.');
                            //$this->session->set("err_rule", $arrAjaxResonse['message']);
                            echo json_encode($arrAjaxResonse);
                            exit();
                        }
                        if ($payment_date > date('Y-m-d')) {
                            $arrAjaxResonse = array('status' => 'error', 'message' => 'Date cannot be greater than todays date.');
                            //$this->session->set("err_rule", $arrAjaxResonse['message']);
                            echo json_encode($arrAjaxResonse);
                            exit();
                        }
                    }
                    if ($action == 2) {
                        if (strtolower($arrPostData['status']) == 'r') {
                            $arrEmailData['title'] = 'cheque_payment_not_received';
                            $arrUpdatePaymentTrackerListener = array('soc_id' => $soc_id, 'payment_tracker_id' => $payment_tracker_id, 'status' => 'not_received');
                        } else {
                            $arrEmailData['title'] = 'cheque_payment_bounced';
                            $arrUpdatePaymentTrackerListener = array('soc_id' => $soc_id, 'payment_tracker_id' => $payment_tracker_id, 'status' => 'N');
                        }
                        $arrResponse = $chsoneInvoiceGeneration->updateIncomePaymentTrackerStatus($arrUpdatePaymentTrackerListener); // get all Unit details
                        if (!empty($arrResponse) && strtolower($arrResponse['status']) == 'success') {
                            if (strtolower($arrPostData['bill_type']) == 'member') {
                                $arrUpdateUnitInvoiceListener = array('soc_id' => $soc_id, 'invoice_number' => $arrPostData['member_paid_invoice'], 'status' => 'generated');
                                $arrResponse = $chsoneInvoiceGeneration->updateUnitInvoiceStatus($arrUpdateUnitInvoiceListener); // get all Unit details
                            } elseif (strtolower($arrPostData['bill_type']) == 'nonmember') {
                                if (isset($arrPostData['other_information']['nonmember_detail']['advance_payment']) && !empty($arrPostData['other_information']['nonmember_detail']['advance_payment'])) {
                                    $arrUpdateUnitInvoiceListener = array('soc_id' => $soc_id, 'bill_number' => $arrPostData['member_paid_invoice'], 'status' => 'unpaid', 'advance_amount' => 0);
                                    $arrResponse = $chsoneInvoiceGeneration->updateNonmemberIncomeAdavanceStatus($arrUpdateUnitInvoiceListener); // get all Unit details
                                }
                                $arrNonmemberIncomeDetail = $chsoneInvoiceGeneration->getNonmemberIncomeDetails(array('soc_id' => $soc_id, 'bill_number' => $arrPostData['member_paid_invoice']));
                                if (!empty($arrNonmemberIncomeDetail)) {
                                    $memberName = explode(' ', $arrNonmemberIncomeDetail['billed_name']);
                                    $arrIncomeInvoiceMemberDetail['member_first_name'] = current($memberName);
                                    $arrIncomeInvoiceMemberDetail['member_last_name'] = trim(str_replace($arrIncomeInvoiceMemberDetail['member_first_name'], '', $arrNonmemberIncomeDetail['billed_name']));
                                    $arrIncomeInvoiceMemberDetail['member_mobile_number'] = $arrNonmemberIncomeDetail['booker_mobile_number'];
                                    $arrIncomeInvoiceMemberDetail['member_email_id'] = $arrNonmemberIncomeDetail['booker_email_address'];
                                }
                            }
                        }
    
                        $succMsg = "Payment has not received.";
                        // Send Notification on cheque bounce
                        $arrEmailData['soc_id'] = $soc_id;
                        $arrEmailData['currency'] = 'Rs.';
                        $paymentDate = explode(' ', $arrPostData['created_date']);
    
                        $arrEmailData['payment_date'] = (!empty($paymentDate[0]) && strrchr($paymentDate[0], '-')) ? $paymentDate[0] : $paymentDate[0];
                        $arrEmailData['cheque_number'] = $arrPostData['transaction_reference'];
                        $arrEmailData['bank_name'] = $arrPostData['payment_instrument'];
                        $arrEmailData['total_amount'] = ($arrPostData['writeoff_amount'] > 0) ? number_format(round($arrPostData['writeoff_amount'], 2), 2, '.', '') : number_format(round($arrPostData['payment_amount'], 2), 2, '.', '');
                        $arrEmailData['member_name'] = ucwords($arrIncomeInvoiceMemberDetail['member_first_name'] . ' ' . $arrIncomeInvoiceMemberDetail['member_last_name']);
                        $arrEmailData['mobile_number'] = $arrIncomeInvoiceMemberDetail['member_mobile_number'];
                        $arrEmailData['email'] = array($arrEmailData['member_name'] => $arrIncomeInvoiceMemberDetail['member_email_id']);
                        //$arrEmailData['priority'] = \ChsOne\Components\Email\Email::PRIORITY_MEDIUM_STORE_IN_DATABASE;
                        $arrSocietyData = array('soc_id' => $soc_id, 'status' => 1);
                        $arrSocietyDetail = $chsoneInvoiceGeneration->getSocietyDetail($arrSocietyData); // get all Unit details
                        $arrEmailData['society_name'] = ucwords($arrSocietyDetail['soc_name']);
                        //$arrEmailData['chsone_site_link'] = $this->config->system->full_base_url_fe;
                        $arrEmailData['chsone_tiny_url'] = CHSONE_TINY_URL;
                        $emailResponse = $this->sendEmailFromTemplate($arrEmailData);
                        $smsResponse = $this->sendSmsFromTemplate($arrEmailData);
                        // //print_r($emailResponse);//print_r($smsResponse);
                        // //print_r($arrEmailData);exit;
                        // exit;
                    }
                }
            }
        } catch (\Exception $e) {
            dd($e);
        }
    }

    public function checkIfPaymentTrackerIdExists($id)
    {
        $result1 = $this->tenantDB()->table('income_invoice_payment_tracker')
            ->where('id', $id)->exists();

        if ($result1) {
            // The ID exists in the table
            return "Payment Tracker ID $id exists in the table.";
        } else {
            // The ID does not exist in the table
            return false;
        }
    }

    public function getUnitUnpaidInvoiceByInvoiceNumber($data = array())
    {
        $arrAllUnitsInvoice = array();
        $todaysDate = date('Y-m-d');
        $invoice_number = str_replace(",", "','", $data['invoice_number']);
        $arrgetdata = array(
            "conditions" => " soc_id = '" . $data['soc_id'] . "' AND fk_unit_id = '" . $data['unit_id'] . "' AND payment_status IN ('unpaid', 'partialpaid') AND invoice_number IN ('" . $invoice_number . "')",
            "order" => "invoice_number asc"
        );

        $objUnitsInvoice = IncomeUnitInvoice::where($arrgetdata)->get();
        $chsoneInvoiceGeneration = new ChsoneInvoiceGeneration();
        if (!empty($objUnitsInvoice)) {
            $arrUnitsInvoice = $objUnitsInvoice->toArray();
            if (!empty($arrUnitsInvoice)) {
                $arrAllUnitsInvoice = $chsoneInvoiceGeneration->getUnpaidInvoiceDetail(array(
                    'arrUnitsInvoice' => $arrUnitsInvoice,
                    'member_id' => $data['member_id'],
                    'soc_id' => $data['soc_id'],
                    'unit_id' => $data['unit_id']
                ));
            }
        }
        return $arrAllUnitsInvoice;
    }

    public function createMemberLedger($arrData = array())
    {
        $auth = $arrData['auth'];
        $arrResponse = array(
            'status' => 'error'
        );
        $arrPostData = $arrData['arrPostData'];
        if (!empty($arrPostData)) {
            $arrIncomeInvoiceMemberDetail = $arrData['arrIncomeInvoiceMemberDetail'];
            // echo '<pre>', print_r($arrIncomeInvoiceMemberDetail),'</pre>';exit;
            // Adavnace payment if no due pending
            if ((empty($arrPostData['total_unpaid_amount']) || $arrPostData['total_unpaid_amount'] <= 0) && empty($arrPostData['member_paid_invoice'])) {
                if ($arrPostData['payment_amount'] > 0) {
                    $countLedgerEntry = 0;
                    $arrPostData['soc_id'] = $auth['soc_id'];
                    if (!empty($arrIncomeInvoiceMemberDetail)) {
                        $arrPostData['unit_id'] = $arrIncomeInvoiceMemberDetail['fk_unit_id'];
                        $arrPostData['member_id'] = $arrIncomeInvoiceMemberDetail['id'];
                    }
                    if (!empty($arrPostData['payment_date']) && strrchr($arrPostData['payment_date'], '-')) {
                        $arrPostData['payment_date'] = $arrPostData['payment_date'];
                    }
                    if (ACCOUNT_MODULE_EXIST == 1) {
                        $arrListnerData = array(
                            'auth' => $auth,
                            'unit_id' => $arrData['unit_id']
                        );
                        $arrUnitDetails = $this->getUnitDetailById($arrListnerData);
                        if (!empty($arrUnitDetails)) {
                            $arrListnerData['ledger_id'] = $arrUnitDetails['ledger_account_id'];
                            $chsoneInvoiceGeneration = new ChsoneInvoiceGeneration();
                            $arrUnitLedgerDetails = $this->checkledgerExistNew($arrListnerData);
                            if (!empty($arrUnitLedgerDetails)) {
                                $countLedgerEntry = $this->paymentLedgerEntry(array(
                                    'auth' => $auth,
                                    'arrPostData' => $arrPostData,
                                    'arrUnitLedgerDetails' => $arrUnitLedgerDetails,
                                    'from_ledger' => $arrData['from_ledger'],
                                    'to_ledger' => $arrData['to_ledger'],
                                    'narration' => $arrData['narration']
                                )); // get all Unit details
                                if (!$countLedgerEntry) {
                                    $arrResponse['status'] = 'success';
                                }
                            }
                        }
                        // Send notification
                        if ($countLedgerEntry == 0) {
                            // send notification code
                        }
                    }
                }
                // echo '2<pre>', print_r($arrResponse),'</pre>';exit;
                return $arrResponse;
            }
            return $arrResponse;
        }
    }

    /**
     * Insert payment ledger entry
     *
     * @method paymentLedgerEntry
     * @access public
     * @param object $event
     * @param object $component
     * @param array $data
     * @return array
     */
    public function paymentLedgerEntry($data = array())
    {

        $countLedgerEntry = 0;
        $chsoneInvoiceGeneration = new ChsoneInvoiceGeneration();

        if (ACCOUNT_MODULE_EXIST == 1) {
            $auth = $data['auth'];
            $arrPostData = $data['arrPostData'];
            $arrUnitLedgerDetails = $data['arrUnitLedgerDetails'];

            if (isset($arrPostData['clearance_date']) && !empty($arrPostData['clearance_date'])) {
                $arrPostData['payment_date'] = $arrPostData['clearance_date'];
            }

            if (empty($arrPostData['payment_date'])) {
                $arrPostData['payment_date'] = date('Y-m-d');
            }
            //$arrIncomeAccounts = $this->incometrackerevent->incometracker('accountListener:checkledgerExistNew', array('auth' => $data['auth'], 'ledger_id' => $arrPostData['bank_account']));
            // print_r($this->constants['payment_mode_for_receipt_reversal']); exit();
            if (!empty($arrPostData['payment_mode']) && !in_array(strtolower($arrPostData['payment_mode']), $this->constants['payment_mode_for_receipt_reversal'])) {
                //  print_r(array(trim($arrPostData['payment_instrument']) . '_PG_LEDGER_ID'));
                $arrInvoiceGeneralSetting = $this->getInvoiceGeneralSetting(array('soc_id' => $data['auth']['soc_id'], 'setting_key' => array(trim($arrPostData['payment_instrument']) . '_PG_LEDGER_ID')));

                if (!empty($arrInvoiceGeneralSetting[0]['setting_value'])) {
                    $arrIncomeAccounts = $chsoneInvoiceGeneration->checkledgerExistNew(array('auth' => $data['auth'], 'ledger_id' => $arrInvoiceGeneralSetting[0]['setting_value']));
                    $arrIncomeAccounts['ledger_id'] = $arrIncomeAccounts['recieving_ledger_id'];
                    $arrIncomeAccounts['ledger_name'] = $arrIncomeAccounts['receiver_name'];
                } else {
                    if (!empty($data['to_ledger'])) {
                        $arrIncomeAccounts['ledger_id'] = $data['to_ledger']['recieving_ledger_id'];
                        $arrIncomeAccounts['ledger_name'] = $data['to_ledger']['receiver_name'];
                    } else {
                        return 1;
                    }
                }
            } else {
                if (isset($data['arrUnitLedgerDetails']['suspenseLedgerDetails']) && !empty($data['arrUnitLedgerDetails']['suspenseLedgerDetails'])) {
                    $arrIncomeAccounts['ledger_id'] = $data['arrUnitLedgerDetails']['suspenseLedgerDetails']['recieving_ledger_id'];
                    $arrIncomeAccounts['ledger_name'] = $data['arrUnitLedgerDetails']['suspenseLedgerDetails']['receiver_name'];
                } else {
                    // Getting bank/cash ledger details
                    $arrAccountDetail = $this->getBankCashAccountDetail(array(
                        'soc_id' => $auth['soc_id']
                    )); // get all Unit details
                    $creditNoteAddDataSource = new CreditNoteAddDataSource();
                    $arrLedgerAccountDetail = $creditNoteAddDataSource->getLedgerAccountDetail(array(
                        'account_detail' => $arrAccountDetail
                    )); // get all Unit details

                    if (!empty($arrLedgerAccountDetail)) {
                        $arrIncomeAccounts['ledger_id'] = $arrLedgerAccountDetail['cash']['ledger_id'];
                        $arrIncomeAccounts['ledger_name'] = $arrLedgerAccountDetail['cash']['ledger_name'];
                        if (strtolower($arrPostData['payment_mode']) != 'cash') {
                            if (!empty($arrPostData['bank_account'])) {
                                $arrIncomeAccounts['ledger_id'] = $arrPostData['bank_account'];
                                $arrIncomeAccounts['ledger_name'] = $arrLedgerAccountDetail['arrBank'][$arrPostData['bank_account']];
                            } else {
                                $arrIncomeAccounts['ledger_id'] = $arrLedgerAccountDetail['bank']['ledger_id'];
                                $arrIncomeAccounts['ledger_name'] = $arrLedgerAccountDetail['bank']['ledger_name'];
                            }
                        }
                    }
                }
            }

            if ($arrPostData['payment_mode'] == 'cashtransfer') {
                $paymentModeForNarration = DISPLAY_CASH_TRANSFER;
            } else {
                $paymentModeForNarration = $arrPostData['payment_mode'];
            }

            // $paymentModeForNarration = $arrPostData ['payment_mode'];
            $strNarration = '';
            if (!empty($arrPostData['payment_mode']) && in_array(strtolower($arrPostData['payment_mode']), $this->constants['payment_mode_for_clearance'])) {
                $strNarration = ' with transaction ref. (' . $arrPostData['transaction_reference'] . ', ' . $arrPostData['payment_instrument'] . ')';
            } elseif (!empty($arrPostData['payment_mode']) && strtolower($arrPostData['payment_mode']) == 'cashtransfer') {
                $strNarration = ' with payment ref. (' . $arrPostData['transaction_reference'] . ')';
            } elseif (!empty($arrPostData['payment_mode']) && !in_array(strtolower($arrPostData['payment_mode']), $this->constants['payment_mode_for_receipt_reversal'])) {
                $strNarration = ' with transaction ref. (' . $arrPostData['transaction_reference'] . ')';
                switch ($arrPostData['payment_mode']) {
                    case YES_BANK_PG:
                        $paymentModeForNarration = DISPLAY_YES_BANK;
                        break;
                    case YES_BANK_ECOLLECT:
                        $paymentModeForNarration = DISPLAY_YES_BANK_ECOLLECT;
                        break;
                    case MOBIKWIK_WALLET:
                        $paymentModeForNarration = DISPLAY_MOBIKWIK_WALLET;
                        break;
                    case MOBIKWIK_PG:
                        $paymentModeForNarration = DISPLAY_MOBIKWIK_PG;
                        break;
                    case CASHFREE_PG:
                        $paymentModeForNarration = DISPLAY_CASHFREE_PG;
                        break;
                    case PAYTM_PG:
                        $paymentModeForNarration = DISPLAY_PAYTM;
                        break;
                    case HDFC_PG:
                        $paymentModeForNarration = DISPLAY_HDFC_PG;
                        break;
                    case ATOM_PG:
                        $paymentModeForNarration = DISPLAY_ATOM_PG;
                        break;
                    case 'cashtransfer':
                        $paymentModeForNarration = DISPLAY_CASH_TRANSFER;
                        break;
                }
            }
            // echo $arrIncomeInvoicePaymentTracker['payment_mode'];exit;
            // Ledger entry for payment amount
            $arrLedgerTransactionData = array(
                'auth' => $auth
            );
            $arrLedgerTransactionData['voucher_type'] = isset($arrPostData['voucher_type_credit']) ? VOUCHER_CREDIT : VOUCHER_RECEIPT;
            $arrLedgerTransactionData['voucher_reference_number'] = $arrPostData['receipt_number'];
            $arrLedgerTransactionData['voucher_reference_id'] = $arrPostData['payment_tracker_id'];
            $arrLedgerTransactionData['from_ledger_id'] = $arrIncomeAccounts['ledger_id'];
            $arrLedgerTransactionData['to_ledger_id'] = $arrUnitLedgerDetails['recieving_ledger_id'];
            $arrLedgerTransactionData['transaction_date'] = !empty($arrPostData['payment_date']) ? $arrPostData['payment_date'] : $this->getCurrentDate('database');
            $arrLedgerTransactionData['transaction_amount'] = $arrPostData['payment_amount'];

            $arrLedgerTransactionData['narration'] = 'Amount received against Invoice ' . $arrPostData['member_paid_invoice'] . ' dated ' . $this->getDisplayDate($arrPostData['payment_date']) . ' through ' . $paymentModeForNarration . $strNarration; // 'Invoice '.$arrPostData['member_paid_invoice'].' '.$eachLedgerDetail['particular'].' Write Off';
            if ((empty($arrPostData['total_unpaid_amount']) || $arrPostData['total_unpaid_amount'] <= 0) && empty($arrPostData['member_paid_invoice'])) {
                $arrLedgerTransactionData['narration'] = 'Advance payment received from ' . ucwords($arrPostData['received_from']) . ' dated ' . $this->getDisplayDate($arrPostData['payment_date']) . ' through ' . $paymentModeForNarration . $strNarration; // $eachInvoiceDetail['invoice_number'] . ' late payment charges';
            }
            if ($arrPostData['bill_type'] == 'suspense') {
                $arrLedgerTransactionData['narration'] = 'Unidenfied payment from ' . ucwords($arrPostData['received_from']) . ' dated ' . $this->getDisplayDate($arrPostData['payment_date']) . ' through ' . $paymentModeForNarration . $strNarration;
            }

            $arrLedgerTransactionData['from_ledger_name'] = $arrIncomeAccounts['ledger_name'];
            $arrLedgerTransactionData['to_ledger_name'] = $arrUnitLedgerDetails['receiver_name'];
            $arrLedgerTransactionData['payment_reference'] = $arrPostData['transaction_reference'];
            $arrLedgerTransactionData['transaction_type'] = '';
            $arrLedgerTransactionData['mode_of_payment'] = $arrPostData['payment_mode'];
            $arrLedgerTransactionData['other_payment_ref'] = '';
            // Code to replace from ledger id From Bank/Cash
            if (isset($data['from_ledger'])) {
                $arrLedgerTransactionData['from_ledger_id'] = $data['from_ledger']['recieving_ledger_id'];
                $arrLedgerTransactionData['from_ledger_name'] = $data['from_ledger']['receiver_name'];
            }
            if (isset($data['to_ledger'])) {
                $arrLedgerTransactionData['to_ledger_id'] = $data['to_ledger']['recieving_ledger_id'];
                $arrLedgerTransactionData['to_ledger_name'] = $data['to_ledger']['receiver_name'];
            }
            if (isset($data['narration'])) {
                $arrLedgerTransactionData['narration'] = $data['narration'];
            }
            $arrLedgerTransactionData['narration'] = '(Receipt No-' . $arrPostData['receipt_number'] . ') ' . $arrLedgerTransactionData['narration'];
            if (!empty($arrPostData['payment_note'])) {
                $arrLedgerTransactionData['narration'] .= ' [' . $arrPostData['payment_note'] . ']';
            }

            //echo 'pay'; print_r($arrLedgerTransactionData); exit();
            if (!empty($arrPostData['tds_amount']) && $arrPostData['tds_amount'] > 0) {
                $arrLedgerTransactionData['transaction_amount'] = round($arrPostData['payment_amount'] - $arrPostData['tds_amount'], 2);
                $arrLedgerEntry = $chsoneInvoiceGeneration->transactionLedgerEntry($arrLedgerTransactionData);
                if (isset($arrLedgerEntry['error']) && !empty($arrLedgerEntry['error'])) {
                    $countLedgerEntry++;
                } else {
                    $arrLedgerTransactionData['transaction_amount'] = $arrPostData['tds_amount'];
                    $arrListnerData['ledger_name'] = 'tds receivable';
                    $arrListnerData['context'] = ''; // CURRENT_ASSETS_GROUP;
                    $arrListnerData['behaviour'] = ASSET;

                    $arrTdsLedger = $chsoneInvoiceGeneration->checkledgerExistNew($arrListnerData);
                    $arrLedgerTransactionData['from_ledger_id'] = $arrTdsLedger['recieving_ledger_id'];
                    $arrLedgerTransactionData['from_ledger_name'] = $arrTdsLedger['receiver_name'];
                    $arrLedgerTransactionData['narration'] = 'TDS amount deducted against Invoice ' . $arrPostData['member_paid_invoice'] . ' dated ' . $this->getDisplayDate($arrPostData['payment_date']) . ' through ' . $paymentModeForNarration . $strNarration; // 'Invoice '.$arrPostData['member_paid_invoice'].' '.$eachLedgerDetail['particular'].' Write Off';

                    $arrLedgerEntry = $chsoneInvoiceGeneration->transactionLedgerEntry($arrLedgerTransactionData);
                    if (isset($arrLedgerEntry['error']) && !empty($arrLedgerEntry['error'])) {
                        $countLedgerEntry++;
                    }
                }
            } else {
                //echo 'led';
                $arrLedgerEntry = $chsoneInvoiceGeneration->transactionLedgerEntry($arrLedgerTransactionData);
                // print_r($arrLedgerEntry); exit();
                if (isset($arrLedgerEntry['error']) && !empty($arrLedgerEntry['error'])) {
                    $countLedgerEntry++;
                }
            }
            //Ledger entry for invoice writeoff
            if (!empty($arrPostData['writeoff_amount']) && $arrPostData['writeoff_amount'] > 0) {
                if ($arrPostData['writeoff_amount'] >= 1000) {
                    $arrExpenseWriteOff = $chsoneInvoiceGeneration->checkledgerExistNew(array('auth' => $auth, 'ledger_name' => 'Expense Bad Debt', 'context' => EXPENSE, 'behaviour' => EXPENSE));
                    if (empty($arrExpenseWriteOff['recieving_ledger_id'])) {
                        $arrParentExpense = $chsoneInvoiceGeneration->getLedgerDetail(array('auth' => $auth, 'ledger_name' => 'Indirect Expense', 'context' => EXPENSE, 'behaviour' => EXPENSE));
                        if (!empty($arrParentExpense['ledger_account_id'])) {
                            $arrExpenseWriteOff['receiver_name'] = 'Expense Bad Debt';
                            $arrExpenseWriteOff['recieving_ledger_id'] = $chsoneInvoiceGeneration->createNewLedgerExit(array('auth' => $auth, 'ledger_name' => $arrExpenseWriteOff['receiver_name'], 'context' => EXPENSE, 'behaviour' => EXPENSE, 'parent_id' => $arrParentExpense['ledger_account_id']));
                        }
                    }
                } else {
                    $arrExpenseWriteOff = $chsoneInvoiceGeneration->checkledgerExistNew(array('auth' => $auth, 'ledger_name' => 'Expense Write Off', 'context' => EXPENSE, 'behaviour' => EXPENSE));
                }
                if (empty($arrExpenseWriteOff['recieving_ledger_id'])) {
                    return 1;
                }
                $arrLedgerTransactionData['transaction_amount'] = $arrPostData['writeoff_amount'];
                $arrLedgerTransactionData['from_ledger_id'] = $arrExpenseWriteOff['recieving_ledger_id'];
                $arrLedgerTransactionData['from_ledger_name'] = $arrExpenseWriteOff['receiver_name'];

                $arrLedgerTransactionData['narration'] = 'Amount writeoff against Invoice ' . $arrPostData['member_paid_invoice'] . ' dated ' . $this->getDisplayDate($arrPostData['payment_date']); //'Invoice '.$arrPostData['member_paid_invoice'].' '.$eachLedgerDetail['particular'].' Write Off';
                $arrLedgerEntry = $chsoneInvoiceGeneration->transactionLedgerEntry($arrLedgerTransactionData);
                if (isset($arrLedgerEntry['error']) && !empty($arrLedgerEntry['error'])) {
                    $countLedgerEntry++;
                }
            }
        }
        return $countLedgerEntry;
    }

    /**
     * The event getUnitDetailById will fetch details of an unit w.r.t.
     * the unit id passed.
     *
     * @method getUnitDetailById
     * @access public
     * @param Phalcon\Events\Event $event
     * @param ChsOne\Components\IncomeTracker\IncometrackerEvent $component
     * @param array $data
     * @return array
     */
    public function getUnitDetailById($data = array())
    {
        $soc_id = $data['auth']['soc_id'];
        $unit_id = $data['unit_id'];
        $arrUnitDetails = array();
        $objUnitDetails = ChsoneUnitsMaster::where('soc_id', $soc_id)
            ->where('unit_id', $unit_id)
            ->first();

        if (!empty($objUnitdetails)) {
            $arrUnitDetails = $objUnitdetails->toArray();
        }
        return $arrUnitDetails;
    }

    public function checkledgerExistOrCreate($data = array())
    {
        $arrNonmemberAccounts = array();
        $auth = $data['auth'];
        $ledger_name = $data['ledger_name'];
        $arrClinetLedgerDetails = array();
        //an outsider has booked for the society. Check if exists by name; otherwise, create new.
        $objBookerLedger = ChsoneGrpLedgerTree::where('soc_id', $auth['soc_id'])
            ->where('context', 'security deposit')
            ->where('entity_type', ENTITY_TYPE_LEDGER)
            ->where('ledger_account_name', $ledger_name)
            ->first();
        if (!empty($objBookerLedger)) {
            $arrClinetLedgerDetails['recieving_ledger_id'] = $objBookerLedger->ledger_account_id;
            $arrClinetLedgerDetails['receiver_name'] = $objBookerLedger->ledger_account_name;
        } else {
            //Ledger for the outsider does not exist. Create a ledger entry based on sundry debtors group.
            $objSecurityDepositGroup = ChsoneGrpLedgerTree::where('soc_id', $auth['soc_id'])
                ->where('entity_type', ENTITY_TYPE_GROUP)
                ->where('ledger_account_name', 'Security Deposit')
                ->first();
            //            echo '<pre>',print_r($objSecurityDepositGroup->toArray()),'</pre>';exit;
            $objGroupLedg = new ChsoneGrpLedgerTree;
            $ledger_account_id = $objGroupLedg->manipulate($ledger_name, ENTITY_TYPE_LEDGER, "", $objSecurityDepositGroup->ledger_account_id, $objSecurityDepositGroup->behaviour, '', 0, '', '', null, $objSecurityDepositGroup->context) ?? '';
            // dd($ledger_account_id);
            if (!is_null($ledger_account_id) && (gettype($ledger_account_id) != 'boolean') && (strpos($ledger_account_id, 'DUP') === false)) {
                $arrClinetLedgerDetails['recieving_ledger_id'] = $ledger_account_id;
                $arrClinetLedgerDetails['receiver_name'] = $ledger_name;
            }
        }
        unset($objBookerLedger);
        //        echo '<pre>',print_r($arrClinetLedgerDetails),'</pre>';exit;
        return $arrClinetLedgerDetails;
    }

    public function getClosedAccountDetailByDate($data = array())
    {
        $arrAccountMasterDetail = array();
        if (!empty($data['bill_date'])) {
            $data['bill_date'] = $data['bill_date'];
        } else {
            $data['bill_date'] = date('Y-m-d');
        }
        $objAccountMasterDetail = SocAccountFinancialYearMaster::where('soc_id', $data['soc_id'])
            ->where('closed', 1)
            ->where('confirmed', 1)
            ->whereBetween('fy_start_date', [$data['bill_date'], 'fy_end_date'])
            ->first();
        if (!empty($objAccountMasterDetail)) {
            $arrAccountMasterDetail = $objAccountMasterDetail->toArray();
        }
        return $arrAccountMasterDetail;
    }

    private function _getAllSettingWithId($data)
    {
        $incomeInvoiceGeneralSetting = IncomeInvoiceGeneralSetting::where('soc_id', $data['soc_id'])->get()->toArray();
        foreach ($incomeInvoiceGeneralSetting as $key => $value) {
            $final_array[$value['setting_key']] = $value['id'];
        }
        return $final_array;
    }

    public function getgeneralsetting($data = array())
    {
        $final_array = $this->_getAllSetting($data);
        $incomeInvoiceSetting = IncomeInvoiceSetting::where('soc_id', $data['soc_id'])
            ->orderBy('effective_date', 'DESC')
            ->first();

        $final_array['generalsettingid'] = $incomeInvoiceSetting->id;
        $final_array['invoicing_frequency'] = (isset($incomeInvoiceSetting->invoicing_frequency) && $incomeInvoiceSetting->invoicing_frequency != '') ? $incomeInvoiceSetting->invoicing_frequency : '';
        $final_array['effective_date'] = $incomeInvoiceSetting->effective_date;
        $final_array['general_setting_key_ids'] = $this->_getAllSettingWithId($data);
        return $final_array;
    }

    private function _getAllSetting($data)
    {
        // Fetch the first record from IncomeInvoiceSetting where soc_id matches
        $incomeInvoiceSetting = IncomeInvoiceSetting::where('soc_id', $data['soc_id'])->first();

        // Fetch all records from IncomeInvoiceGeneralSetting where soc_id matches and convert to array
        $incomeInvoiceGeneralSetting = IncomeInvoiceGeneralSetting::where('soc_id', $data['soc_id'])->get()->toArray();

        foreach ($incomeInvoiceGeneralSetting as $key => $value) {
            $final_array[$value['setting_key']] = $value['setting_value'];
        }
        return $final_array;
    }

    /**
     * Get member detail
     *
     * @method getMemberDetail
     * @access public
     * @param object $event
     * @param object $component
     * @param array $data
     * @return array
     */
    public function getMemberDetail($data = array())
    {
        // Initialize an empty array for the member details
        $arrMemberMaster = [];

        // Fetch the member type ID for 'Primary'
        $memberType = ChsoneMemberTypeMaster::where('soc_id', $data['soc_id'])
            ->where('member_type_name', 'Primary')
            ->first();

        if ($memberType) {
            // Fetch the member details for the provided society and unit IDs, and for active status
            $objMemberMaster = ChsoneMembersMaster::where('soc_id', $data['soc_id'])
                ->where('fk_unit_id', $data['unit_id'])
                ->where('status', 1)
                ->where('member_type_id', $memberType->member_type_id)
                ->first();

            // If a member record is found, convert it to an array
            if ($objMemberMaster) {
                $arrMemberMaster = $objMemberMaster->toArray();
            }
        }

        // Return the member details array
        return $arrMemberMaster;
    }


    /**
     * Get in process payment invoices detail
     *
     * @method getInvoicePaymentTrackerDetail
     * @access public
     * @param object $event
     * @param object $component
     * @param array $data
     * @return array
     */
    public function getInvoicePaymentTrackerDetail($data = array())
    {

        $arrIncomeInvoicePaymentTracker = array();
        $query = IncomeInvoicePaymentTracker::query();
        // Add the basic conditions
        $query->where('soc_id', $data['soc_id'])
            ->where('id', $data['payment_tracker_id']);

        // Add the unit_id condition if present
        if (isset($data['unit_id']) && !empty($data['unit_id'])) {
            $query->where('unit_id', $data['unit_id']);
        }

        // Add the status condition if present
        if (isset($data['status']) && !empty($data['status'])) {
            if (is_array($data['status'])) {
                $query->whereIn('status', $data['status']);
            } else {
                $query->where('status', $data['status']);
            }
        }

        // Add the current_date condition if present
        if (isset($data['current_date']) && !empty($data['current_date'])) {
            $query->where('payment_date', '<=', $data['current_date']);
        }

        // Add the payment_reversal condition if present and set to 'y'
        if (!empty($data['payment_reversal']) && strtolower($data['payment_reversal']) == 'y') {
            $query->where('created_date', '>=', ACTIVE_PAYMENT_REVERSAL_DATE);
        }

        // Add the bill_type condition if present
        if (isset($data['bill_type']) && !empty($data['bill_type'])) {
            $query->whereIn('bill_type', $data['bill_type']);
        }
        // Execute the query and get the first result
        $objIncomeInvoicePaymentTracker = $query->first();
        if (!empty($objIncomeInvoicePaymentTracker)) {
            $arrIncomeInvoicePaymentTracker = $objIncomeInvoicePaymentTracker->toArray();
            // echo '<pre>';print_r($arrIncomeInvoicePaymentTracker);exit;
            $arrIncomeInvoicePaymentTracker['member_paid_invoice'] = $arrIncomeInvoicePaymentTracker['invoice_number'];
            $arrIncomeInvoicePaymentTracker['receipt_number'] = strtoupper($arrIncomeInvoicePaymentTracker['receipt_number']);
            $arrIncomeInvoicePaymentTracker['total_unpaid_amount'] = $arrIncomeInvoicePaymentTracker['total_due_amount'];
            unset($arrIncomeInvoicePaymentTracker['invoice_number'], $arrIncomeInvoicePaymentTracker['total_due_amount']);
            $arrIncomeInvoicePaymentTracker['other_information'] = unserialize($arrIncomeInvoicePaymentTracker['other_information']);

            // set payment for
            $arrIncomeInvoicePaymentTracker['payment_for'] = 'Maintenance Invoice';
            if (strtolower($arrIncomeInvoicePaymentTracker['bill_type']) == 'common_bill') {
                $arrIncomeInvoicePaymentTracker['payment_for'] = 'Incidental Bill';
            } elseif (strtolower($arrIncomeInvoicePaymentTracker['bill_type']) == 'nonmember') {
                $arrIncomeInvoicePaymentTracker['payment_for'] = 'Nonmember Bill';
            } elseif (strtolower($arrIncomeInvoicePaymentTracker['bill_type']) == 'suspense') {
                $arrIncomeInvoicePaymentTracker['payment_for'] = 'Suspense Reciept';
            } elseif (strtolower($arrIncomeInvoicePaymentTracker['bill_type']) == 'creditaccount-member') {
                if (
                    !empty($arrIncomeInvoicePaymentTracker['other_information']['credit_account_detail']['credit_used_type']) &&
                    strtolower($arrIncomeInvoicePaymentTracker['other_information']['credit_account_detail']['credit_used_type']) == 'refundable'
                ) {
                    $arrIncomeInvoicePaymentTracker['payment_for'] = 'Member Refundable Advance';
                } elseif (
                    !empty($arrIncomeInvoicePaymentTracker['other_information']['credit_account_detail']['credit_used_type']) &&
                    strtolower($arrIncomeInvoicePaymentTracker['other_information']['credit_account_detail']['credit_used_type']) == 'adjustable'
                ) {
                    $arrIncomeInvoicePaymentTracker['payment_for'] = 'Member Adjustable Advance';
                }
            }
            if (!empty($arrIncomeInvoicePaymentTracker['payment_mode']) && in_array($arrIncomeInvoicePaymentTracker['payment_mode'], array(YES_BANK_PG, YES_BANK_ECOLLECT, PAYTM_PG, MOBIKWIK_PG, CASHFREE_PG, MOBIKWIK_WALLET, HDFC_PG, ATOM_PG))) {
                switch ($arrIncomeInvoicePaymentTracker['payment_mode']) {
                    case YES_BANK_PG:
                        $arrIncomeInvoicePaymentTracker['online_payment_display_mode'] = DISPLAY_YES_BANK;
                        break;
                    case YES_BANK_ECOLLECT:
                        $arrIncomeInvoicePaymentTracker['online_payment_display_mode'] = DISPLAY_YES_BANK_ECOLLECT;
                        break;
                    case MOBIKWIK_WALLET:
                        $arrIncomeInvoicePaymentTracker['online_payment_display_mode'] = DISPLAY_MOBIKWIK_WALLET;
                        break;
                    case PAYTM_PG:
                        $arrIncomeInvoicePaymentTracker['online_payment_display_mode'] = DISPLAY_PAYTM;
                        break;
                    case MOBIKWIK_PG:
                        $arrIncomeInvoicePaymentTracker['online_payment_display_mode'] = DISPLAY_MOBIKWIK_PG;
                        break;
                    case CASHFREE_PG:
                        $arrIncomeInvoicePaymentTracker['online_payment_display_mode'] = DISPLAY_CASHFREE_PG;
                        break;
                    case HDFC_PG:
                        $arrIncomeInvoicePaymentTracker['online_payment_display_mode'] = DISPLAY_HDFC_PG;
                        break;
                    case ATOM_PG:
                        $arrIncomeInvoicePaymentTracker['online_payment_display_mode'] = DISPLAY_ATOM_PG;
                        break;
                }
            }
            // if(!empty($arrIncomeInvoicePaymentTracker['member_paid_invoice']))
            // {
            // $arrIncomeInvoicePaymentTracker['payment_for'].= ' : '.strtoupper($arrIncomeInvoicePaymentTracker['member_paid_invoice']);
            // }
            // format date
            if (!empty($arrIncomeInvoicePaymentTracker['created_date'])) {
                $payment_date = current(explode(' ', $arrIncomeInvoicePaymentTracker['created_date']));
                if (!empty($payment_date)) {
                    $arrIncomeInvoicePaymentTracker['created_date'] = $payment_date;
                }
            }

            // setting other information field
            if (!empty($arrIncomeInvoicePaymentTracker['other_information'])) {
                if (!empty($arrIncomeInvoicePaymentTracker['other_information'])) {
                    // setting writeoff particular fields
                    if (!empty($arrIncomeInvoicePaymentTracker['other_information']['writeoff_detail'])) {
                        $arrIncomeInvoicePaymentTracker['total_particular'] = $arrIncomeInvoicePaymentTracker['other_information']['writeoff_detail']['total_particular'];
                        for ($i = 1; $i <= $arrIncomeInvoicePaymentTracker['total_particular']; $i++) {
                            $arrIncomeInvoicePaymentTracker["rule_$i"] = $arrIncomeInvoicePaymentTracker['other_information']['writeoff_detail']["rule_$i"];
                            $arrIncomeInvoicePaymentTracker["particular_$i"] = $arrIncomeInvoicePaymentTracker['other_information']['writeoff_detail']["particular_$i"];
                            $arrIncomeInvoicePaymentTracker["writeoff_amount_$i"] = $arrIncomeInvoicePaymentTracker['other_information']['writeoff_detail']["writeoff_amount_$i"];
                        }
                    }
                }
            }
        }
        return $arrIncomeInvoicePaymentTracker;
    }

    public function getInvoiceGeneralSetting($data = [])
    {
        $arrInvoiceGeneralSetting = array();
        $objInvoiceGeneralSetting = IncomeInvoiceGeneralSetting::where('soc_id', $data['soc_id'])
            ->whereIn('setting_key', $data['setting_key'])
            ->get();
        if (!empty($objInvoiceGeneralSetting)) {
            $arrInvoiceGeneralSetting = $objInvoiceGeneralSetting->toArray();
        }
        return $arrInvoiceGeneralSetting;
    }
}
