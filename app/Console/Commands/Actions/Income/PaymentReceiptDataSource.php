<?php

namespace App\Console\Commands\Actions\Income;

use Illuminate\Console\Command;
use App\Console\Commands\Action;

class PaymentReceiptDataSource extends Action
{
    protected $signature = 'datasource:PaymentReceipt {flowId} {parentId} {input}';

    protected $description = 'Get the payment receipt details';

    protected $formatter =  [
        'id' => '',
        'unit_id' => '',
        'invoice_number' => '',
        'receipt_number' => '',
        'bill_type' => '',
        'payment_mode' => '',
        'received_from' => '',
        'payment_amount' => '',
        'payment_amount_in_words' => '',
        'status' => '',
        'transaction_status' => '',
        'payment_date' => '',
        'payment_instrument' => '',
        'total_due_amount' => '',
        'transaction_reference' => '',
        'society_unit_name' => '',
    ];

    protected $formatterByKeys =  ['id'];

    protected $mapper = [
        'id' => 'id',
    ];

    public function apply()
    {
        $id = $this->input['id'];
        // check if payment tracker id is valid or not from income_invoice_payment_tracker table
        $payments = $this->tenantDB()->table('income_invoice_payment_tracker')
            ->where('id', $id)
            ->get();
        
        if(count($payments) == 0) {
            $this->status = 'error';
            $this->message = 'Please provide a valid payment tracker id';
            $this->statusCode = 400;
            $this->data = [];
            return;
        } else{
            $obj = $this->tenantDB()->table('income_invoice_payment_tracker AS ipt')
                ->select('ipt.id', 'ipt.unit_id', 'ipt.receipt_number', 'ipt.bill_type', 'ipt.payment_mode', 'ipt.received_from', 'ipt.payment_amount', 'ipt.status', 'ipt.transaction_status', 'ipt.payment_date', 'ipt.payment_instrument','ipt.total_due_amount')
                ->leftJoin('chsone_units_master AS cum', 'ipt.unit_id', '=', 'cum.unit_id')
                ->where('ipt.id', $id)
                ->whereNotIn('ipt.status', ['N', 'reversed'])
                ->orderBy('ipt.receipt_number', 'desc')
                ->orderBy('ipt.payment_date', 'desc')
                ->selectRaw("IF(ipt.invoice_number IS NULL or ipt.invoice_number = '', 'NA', ipt.invoice_number) as invoice_number")
                ->selectRaw("IF(ipt.transaction_reference IS NULL or ipt.transaction_reference = '', 'NA', ipt.transaction_reference) as transaction_reference")
                ->selectRaw("IF(ipt.unit_id IS NULL or ipt.unit_id = '0', 'NA', CONCAT(cum.soc_building_name, ' / ', cum.unit_flat_number)) as society_unit_name");

            $results = $obj->get();

            // check if $result is not empty
            if (!$results->isEmpty()) {
                $result = $results->first();
                $result->payment_amount_in_words = $this->convertNumberToWords($result->payment_amount). " rupees";
            }

            $this->data = $this->format($results->toArray());
        }
    }
}