<?php

namespace App\Console\Commands\Actions\Income;

use Illuminate\Console\Command;
use App\Console\Commands\Action;
use Illuminate\Support\Facades\DB;

class GetBankCashAccountDetailDataSource extends Action
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'datasource:GetBankCashAccountDetail {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Get list of Bank and Cash accounts';

    protected $formatter =  [];

    protected $formatterByKeys =  ['unit_id', 'invoice_number', 'receipt_number'];

    protected $smartFormatter = [];

    protected $mapper = [
        'unit_id' => 'fk_unit_id'
    ];

    /**
     * Execute the console command.
     */

     public function apply()
     {
         $id = $this->input['id'];
         $soc_id = $this->input['company_id'];
         // Fetch the required account details with a left join on the ledger tree table
         $this->data = $this->tenantDB()
             ->table('chsone_accounts_master as accounts')
             ->select(
                 'accounts.ledger_account_id as id',
                 'accounts.account_id',
                 'accounts.bank_name',
                 'accounts.branch',
                 DB::raw('CAST(accounts.account_number AS UNSIGNED) as account_number'),
                 'accounts.bank_address',
                 'accounts.bank_city',
                 'accounts.bank_ifsc',
                 'accounts.default_account',
                 'accounts.status',
                 'accounts.group_id',
                 'accounts.active_for_payments',
                 'accounts.default_bank_for_incidental',
                 'accounts.default_bank_for_nonmember',
                 'ledger_tree.ledger_account_name',
                 'accounts.account_name'
             )
             ->leftJoin('chsone_grp_ledger_tree as ledger_tree', 'ledger_tree.ledger_account_id', '=', 'accounts.ledger_account_id')
             ->where([
                 ['accounts.status', '=', 1],
                 ['accounts.soc_id', '=', $soc_id],
                 ['accounts.account_id', '=', $id]
             ])
             ->first();
     }

}
