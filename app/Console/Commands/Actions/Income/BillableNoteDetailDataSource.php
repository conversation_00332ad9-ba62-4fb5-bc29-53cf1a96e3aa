<?php

namespace App\Console\Commands\Actions\Income;

use App\Console\Commands\Action;
use Illuminate\Support\Facades\DB;

class BillableNoteDetailDataSource extends Action
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'datasource:billableNoteDetail {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Get the billable note details';
    /**
     * Execute the console command.
     */
    public function apply()
    {

        $id = $this->input['id'];
        $obj = $this->tenantDB()->table('income_billable_item AS note')
            ->select(
                'note.income_billable_item_id as id',
                'note.income_billable_item_id',
                'note.unit_id',
                'note.member_id',
                'note.debit_note_type',
                'note.note',
                'note.expense_id',
                'note.amount',
                'note.from_date',
                'note.to_date',
                'note.created_date',
                'note.income_account_id',
                'note.is_reversal',
                'note.tax_class_id',
                'memmaster.member_first_name',
                'memmaster.member_last_name',
                'memmaster.member_type_id',
                'unitmaster.soc_building_name',
                'unitmaster.unit_flat_number',
                'note.apply_late_payment_interest',
                'note.due_date',
                'tax.tax_class_id',
                'tax.tax_class_name',
                'icbc.particular',
                DB::raw("CONCAT(memmaster.member_first_name, ' ', memmaster.member_last_name) AS member_name"), // Concatenate member fields
                DB::raw("CONCAT(unitmaster.soc_building_name, ' / ', unitmaster.unit_flat_number) AS unit_name"), // Concatenate columns
                DB::raw("CONCAT(note.from_date, ' - ', note.to_date) AS billing_period") // Concatenate columns
                )
            ->selectRaw("IFNULL(note.invoice_number, 'Next Invoice') AS invoice_number")
            ->selectRaw("IF(note.invoice_number IS NOT NULL, true, false) as invoice_disable")
            ->leftJoin('chsone_members_master AS memmaster', 'note.member_id', '=', 'memmaster.id')
            ->leftJoin('chsone_units_master AS unitmaster', 'note.unit_id', '=', 'unitmaster.unit_id')
            ->leftJoin('chsone_tax_classes AS tax', 'note.tax_class_id', '=', 'tax.tax_class_id')
            ->leftJoin('income_common_area_charges AS icbc', 'note.income_account_id', '=', 'icbc.id')
            ->where('note.status', 1)
            // commenting out below line since this condition was not required
            // ->where('note.debit_note_type', 'debit_note')
            ->where('note.unit_id', '!=', '')
            ->where('note.member_id', '!=', '')
            ->where('note.income_billable_item_id',$id);


        $result = $obj->first();

        if ($result) {
            $debit_note_type = $result->debit_note_type;
            $result->apply_late_payment_interest = $result->apply_late_payment_interest ? true : false;
            $debit_note_type_parts = explode('_', $debit_note_type);

            // Capitalize each part and join with a space
            $formatted_parts = array_map(function ($part) {
                return ucfirst(strtolower($part)); // Make sure to lowercase before capitalizing to avoid issues with mixed case
            }, $debit_note_type_parts);

            $result->debit_note_type = implode(' ', $formatted_parts);
            $this->data = $result;
            $this->message = 'Billable note details fetched successfully';
            $this->statusCode = 200;
            return;

        }else{
            $this->message = 'Billable note details not found';
            $this->statusCode = 404;
            return;
        }  

    }
}
