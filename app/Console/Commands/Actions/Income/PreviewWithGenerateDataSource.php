<?php

namespace App\Console\Commands\Actions\Income;

use Illuminate\Support\Facades\DB;
use App\Console\Commands\Action;
use App\Models\Income\ChsoneInvoiceGeneration;
use App\Models\Tenants\ChsoneInvoiceGeneration as TenantsChsoneInvoiceGeneration;

class PreviewWithGenerateDataSource extends Action
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'datasource:PreviewWithGenerateDataSource {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Add outstanding data source';

    protected $formatter =  [];

    protected $formatterByKeys =  [];


    protected $mapper = [];

    /**
     * Execute the console command.
     */
    public function apply()
    {
        $objPreviewWithGenerate = new TenantsChsoneInvoiceGeneration();
        $objPreviewWithGenerate->setInput($this->input);
        $previewWithGenerate = $objPreviewWithGenerate->previewWithGenerate($this->input);
        $this->data = $previewWithGenerate;

    }
}
