<?php

namespace App\Console\Commands\Actions\Income;

use Illuminate\Console\Command;
use App\Console\Commands\Action;
use App\Models\Tenants\ChsoneCreditAccount;
use App\Models\Tenants\ChsoneTaxLog;
use App\Models\Tenants\IncomeCommonAreaCharge;
use App\Models\Tenants\IncomeCommonBillingCharge;
use App\Models\Tenants\IncomeInvoiceGeneralSetting;
use App\Models\Tenants\IncomeInvoicePaymentTracker;
use App\Models\Tenants\IncomeInvoiceSetting;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class fetchUnitInvoiceDetailsDataSource extends Action
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'datasource:fetchUnitInvoiceDetails {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Fetch unit invoice details data source.';

    /**
     * Execute the console command.
     */
    public function apply()
    {
        $queryBuilder = $this->tenantDB()->table('income_common_billing_charges as icb')
            ->select([
                'icb.id',
                'icb.fk_unit_id as unit_id',
                'icb.fk_member_id as member_id',
                'icb.invoice_number',
                'icb.billing_type',
                'icb.advance_amount',
                'icb.amount',
                'icb.payment_status',
                'icb.status',
                'icb.bill_date',
                'icb.from_date',
                'icb.to_date',
                'icb.due_date',
                'icb.bill_from',
                'icb.payment_mode',
                'icb.transaction_charges',
                'icb.discount_amount',
                'icb.payment_instrument',
                'unitInvoice.member_gstin',
                'unitInvoice.principal_amount',
                'unitInvoice.interest_amount',
                'unitInvoice.outstanding_principal',
                'unitInvoice.outstanding_interest',
                'unitInvoice.roundoff_amount'
            ])
            ->leftJoin('income_unit_invoices as unitInvoice', 'icb.invoice_number', '=', 'unitInvoice.invoice_number')
            ->where('icb.soc_id', '=', $this->input['company_id'])
            ->where('icb.fk_unit_id', '=', $this->input['unit_id']);
        // ->where('icb.invoice_number', '=', $this->input['invoice_number']);

        $result = $queryBuilder->get();

        $result = json_decode(json_encode($result), true);

        // prepare the final data
        // get last bill details from income_common_billing_charges table
        $final_data = end($result);

        // $final_data = $result[0];

        // check if the $final_data['payment_status'] is cancelled then return $final_data['from_date'] and $final_data['to_date'] as  it is
        if ($final_data['payment_status'] == 'cancelled') {
            $final_data['from_date'] = date('Y-m-d', strtotime($final_data['from_date']));
            $final_data['to_date'] = date('Y-m-d', strtotime($final_data['to_date']));
            $final_data['bill_date'] = date('Y-m-d', strtotime($final_data['bill_date']));
            $final_data['due_date'] = date('Y-m-d', strtotime($final_data['due_date']));
        } else {
            // need to add 1 month to the from_date
            $final_data['from_date'] = date('Y-m-d', strtotime($final_data['from_date'] . ' + 1 month'));

            // need to add 1 month to the to_date
            $final_data['to_date'] = $this->getStartAndEndDate($final_data['from_date']);

            // need to add 1 month to the bill_date
            $final_data['bill_date'] = date('Y-m-d', strtotime($final_data['bill_date'] . ' + 1 month'));

            // need to add 1 month to the due_date
            $final_data['due_date'] = date('Y-m-d', strtotime($final_data['due_date'] . ' + 1 month'));
        }

        // get late charges details
        $arrLateChargesDetail = $this->getLateChargesRule(array('soc_id' => $this->input['company_id']));
        if (!empty($arrLateChargesDetail)) {
            $arrLateChargesDetail = $arrLateChargesDetail[0];
        } else {
            $arrLateChargesDetail = array('simple_interest' => 0, 'grace_period' => 0);
        }

        // get one time particular details
        $arrOnetimeParticular = $this->getOnetimeParticulars(array('soc_id' => $this->input['company_id']));

        // find perticular details
        $arrOnetimeParticularAll = IncomeCommonAreaCharge::where('soc_id', $this->input['company_id'])->orderBy('id')->get()->toArray();
        $perticularHistory = [];
        foreach ($arrOnetimeParticularAll as $hele) {
            $hName = str_replace(' ', '_', strtolower(trim($hele['particular'])));
            if (!empty($perticularHistory[$hName])) {
                array_push($perticularHistory[$hName], $hele['id']);
            } else {
                $perticularHistory[$hName] = [];
                array_push($perticularHistory[$hName], $hele['id']);
            }
        }

        // fetch perticular details using invoice number
        $perticularDetails = $this->getParticularDetails(array('invoice_number' => $this->input['invoice_number']), 'soc_id = ' . $this->input['company_id']);
        $currPerticular = $arrOnetimeParticularAll[array_search($perticularDetails['id'], array_column($arrOnetimeParticularAll, 'id'))];
        $billintTypeIds = [];
        if (!empty($currPerticular)) {
            $perKey = str_replace(' ', '_', strtolower(trim($currPerticular['particular'])));
            $billintTypeIds = $perticularHistory[$perKey];
        }

        // get unitcommon bill list details
        $arrCommonBillAll = $this->getUnitsCommonBillListDetail(array('soc_id' => $this->input['company_id'], 'commonBillData' => $result));

        $temp_per = [];
        foreach ($arrOnetimeParticular as $per) {
            $temp_per[$per['id']] = $per['particular'];
        }

        $outstandingPrincipalAmount = 0;
        $interestAr = 0;
        if (!empty($arrCommonBillAll)) {
            foreach ($arrCommonBillAll as $bill) {

                if (in_array($bill['payment_status'], array('unpaid', 'partialpaid'))) {

                    if (strcmp(trim(strtolower(str_replace(' ', '', $bill['billing_type']))), trim(strtolower(str_replace(' ', '', $temp_per[$this->input['billing_type']])))) == 0) {
                        // $outstandingPrincipalAmount += $bill['total_due'];
                        if($bill['payment_status'] == 'unpaid') {
                            $outstandingPrincipalAmount += $bill['bill_amount'];
                        }
                        if($bill['payment_status'] == 'partialpaid') {
                            // check $bill['advance_amount'] is empty or not
                            $advance_amount = !empty($bill['advance_amount']) ? $bill['advance_amount'] : 0;
                            $bill['bill_amount'] = $bill['bill_amount'] - $advance_amount;
                            $bill['total_due'] = $bill['total_due'] - $advance_amount;

                            // $bill['bill_amount'] is 1581.22, $bill['interest_amount'] is 37.43, $bill['payment_detail']['payment_amount'] is 1315.21, $bill['total_due'] is 1618.65
                            // $bill['total_due'] - $bill['payment_detail']['payment_amount'] = 303.44, from this 37.43 is interest amount and 266.01 is principal amount
                            // so 266.01 + 37.43 = 303.44. so $remaining_amount is 1581.22 - 1315.21 = 266.01 and pass 266.01 to $outstandingPrincipalAmount and 37.43 pass to interestAr
                            $remaining_amount = $bill['bill_amount'] - $bill['payment_detail']['payment_amount'];
                            $outstandingPrincipalAmount += $remaining_amount;
                            $crossCheck = $bill['total_due'] - $bill['payment_detail']['payment_amount'];
                            //$interestAr = $crossCheck - $remaining_amount;
                            // if interest_amount is not empty then add the interest amount to interestAr
                            if($bill['interest_amount'] != 0) {
                                $interestAr = $crossCheck - $remaining_amount;
                            } else {
                                $interestAr = 0;
                            }
                        }
                    }
                }
            }
        }

        // fetch credit remaining amount
        $arrCreditData['soc_id'] = $this->input['company_id'];
        $arrCreditData['account_id'] = $this->input['unit_id'];
        $arrCreditData['account_context'] = 'unit';
        $arrCreditData['bill_date'] = date('Y-m-d');
        $arrCreditData['bill_type'] = 'incidental';
        $creditRemainingAmount = $this->getCreditAmountForIndividuals(array('data' => $arrCreditData));
        $final_data['advance_amount'] = round($creditRemainingAmount['remaining_amount'], 2);

        // get incident invoice unpaid bill
        $unpaidBills = $this->getIncidentInvoiceUnpaidBill(array('soc_id' => $this->input['company_id'], 'unit_id' => $this->input['unit_id']));

        $pricipalAr = 0;
        // $interestAr = 0;
        $latePayCharges = 0;
        $past_fr_date = null;
        $amountOnDpcApply = 0;

        if (!empty($unpaidBills['invoice_detail'])) {
            $unpaidBillsArr = $unpaidBills['invoice_detail'];
            usort($unpaidBillsArr, function ($a, $b) {
                if (strtotime($a['from_date']) < strtotime($b['from_date']))
                    return 1;
                else if (strtotime($a['from_date']) > strtotime($b['from_date']))
                    return -1;
                else
                    return 0;
            });
            $i = 0;

            foreach ($unpaidBillsArr as $pastInv) {
                $InvDueAmount = 0;

                if (in_array($pastInv['billing_type'], $billintTypeIds)) {
                    if ($pastInv['payment_status'] == 'unpaid') {
                        $InvDueAmount = $pastInv['amount'];
                        $interestAr += $pastInv['interest_amount'];
                    } elseif ($pastInv['payment_status'] == 'partialpaid') {
                        if (!empty($pastInv['advance_amount']) && $pastInv['advance_amount'] > 0) {
                            $InvDueAmount = $pastInv['amount'] - $pastInv['advance_amount'] - $pastInv['payment_detail']['payment_amount'] -
                                $pastInv['payment_detail']['tds_deducted'] - $pastInv['payment_detail']['transaction_charges'] - $pastInv['payment_detail']['discount_amount'];
                        } else {
                            $InvDueAmount = $pastInv['amount'] - $pastInv['payment_detail']['payment_amount'] -
                                $pastInv['payment_detail']['tds_deducted'] - $pastInv['payment_detail']['transaction_charges'] - $pastInv['payment_detail']['discount_amount'];
                        }
                    } else {
                        $InvDueAmount = 0;
                    }
                }

                $validEffectivePeriod = FALSE;
                if (!empty($arrLateChargesDetail['effective_date']) && !empty($pastInv['bill_date'])) {
                    $effectiveDate = new \DateTime($arrLateChargesDetail['effective_date']);
                    $billDate = new \DateTime($pastInv['bill_date']);
                    if ($billDate >= $effectiveDate) {
                        $validEffectivePeriod = TRUE;
                    }
                }

                // echo $arrLateChargesDetail['effective_date'] . ' ' . $pastInv['bill_date'];
                $incidentalRule = $arrOnetimeParticular[array_search($pastInv['billing_type'], array_column($arrOnetimeParticular, 'id'))];
                if (in_array($pastInv['billing_type'], $billintTypeIds) && $incidentalRule['apply_late_payment_interest'] == 1 && $validEffectivePeriod) {
                    $dpc = 0;
                    $arrLateChargeCal = [];
                    $arrLateChargeCal['start_date'] = $pastInv['bill_date'];
                    $arrLateChargeCal['dueDate'] = $pastInv['due_date'];
                    $arrLateChargeCal['invoice_number'] = $pastInv['invoice_number'];
                    $arrLateChargeCal['arrLatePaymentdetails'] = $arrLateChargesDetail;
                    if ($i == 0) {
                        $arrLateChargeCal['lateChargeCalculationDate'] = $final_data['from_date'];
                    } else {
                        $arrLateChargeCal['lateChargeCalculationDate'] = $past_fr_date;
                    }

                    //$arrLateChargeCal['start_date'] = date('Y-m-d', strtotime($arrLateChargeCal['start_date']. ' + 1 days'));
                    $arrLateChargeCal['dueDate'] = date('Y-m-d', strtotime($arrLateChargeCal['dueDate'] . ' + 1 days'));
                    $arrLateChargeCal['lateChargeCalculationDate'] = date('Y-m-d', strtotime($arrLateChargeCal['lateChargeCalculationDate'] . ' 0 days'));

                    if ($pastInv['payment_status'] == 'unpaid') {
                        $arrLateChargeCal['amount'] = $InvDueAmount;
                        if (empty($pastInv['due_date'])) {
                            $dpc = 0;
                        } else {

                            //$dpc =  $this->incometrackerevent->incometracker('MemberIncomeDetail:calculateDailyLateCharges', $arrLateChargeCal);
                        }
                    } elseif ($pastInv['payment_status'] == 'partialpaid') {
                        $arrLateChargeCal['amount'] = $InvDueAmount;
                        if (empty($pastInv['due_date'])) {
                            $dpc = 0;
                        } else {
                            // $dpc =  $this->incometrackerevent->incometracker('MemberIncomeDetail:calculateDailyLateCharges', $arrLateChargeCal);
                        }
                    } else {
                        $dpc = 0;
                    }

                    if ($i == 0) {
                        $lateCharge = $dpc;
                        $arrLateChargeCalBackup = $arrLateChargeCal;
                    } else {
                        //$interestAr += $dpc;
                    }
                    $past_fr_date = $pastInv['bill_date'];
                    $i++;
                } else {
                    // $arrLateChargeCal['arrLatePaymentdetails'] = [];
                    // $arrLateChargeCal['dueDate'] = '';
                    // $arrLateChargeCal['lateChargeCalculationDate'] = '';
                    // $arrLateChargeCalBackup = $arrLateChargeCal;
                }

                $pricipalAr += $InvDueAmount;

                if (!empty($pastInv['due_date'])) {
                    $dueDate = $pastInv['due_date'];
                    $billDate = $final_data['from_date'];

                    if ($billDate >= $dueDate) {
                        $validEffectivePeriod = TRUE;
                        $amountOnDpcApply += $InvDueAmount;
                    } else {
                        $amountOnDpcApply += $InvDueAmount;
                    }
                } else {
                }
            }
        } else {
            $arrLateChargeCal['arrLatePaymentdetails'] = [];
            $arrLateChargeCal['dueDate'] = '';
            $arrLateChargeCal['lateChargeCalculationDate'] = '';
            $arrLateChargeCalBackup = $arrLateChargeCal;
        }

        $arrLateChargeCalBackup['amount'] = $amountOnDpcApply;
        $lateCharge = $this->calculateDailyLateCharges($arrLateChargeCalBackup);
        $interestAr = round($interestAr, 2);

        $arrListenerRoundoffData = [];
        $arrListenerRoundoffData['invoiceAmount'] = $outstandingPrincipalAmount;
        $arrListenerRoundoffData['interestAmount'] = $interestAr;
        $arrListenerRoundoffData['adjustedAdvanceAmount'] = 0;
        $arrListenerRoundoffData['soc_id'] = $this->input['company_id'];
        $roundOffData = $this->getInvoiceRoundoffAdjustmentAmount($arrListenerRoundoffData);

        $outstandingPrincipalAmount += round($roundOffData['roundoff_amount'], 2);
        $outstandingPrincipalAmount = round($outstandingPrincipalAmount, 2);

        $final_data['amountOnDpcApply'] = round($amountOnDpcApply, 2);
        $final_data['lateCharge'] = round($lateCharge, 2);
        $final_data['invoice_amount'] = round($this->input['particular_amount'] + $lateCharge - $final_data['advance_amount'], 2);

        $settingData = $this->getgeneralsetting(array('soc_id' => $this->input['company_id']));
        if (!empty($settingData['INVOICE_AMOUNT_ROUNDOFF']) && strtolower($settingData['INVOICE_AMOUNT_ROUNDOFF']) == 'yes') {
            if (!empty($settingData['INVOICE_AMOUNT_ROUNDOFF_TYPE']) && strtolower($settingData['INVOICE_AMOUNT_ROUNDOFF_TYPE']) == 'upper') {
                $grand_total = ceil((float)$final_data['invoice_amount']);
            } elseif (!empty($settingData['INVOICE_AMOUNT_ROUNDOFF_TYPE']) && strtolower($settingData['INVOICE_AMOUNT_ROUNDOFF_TYPE']) == 'lower') {
                $grand_total = floor((float)$final_data['invoice_amount']);
            } elseif (!empty($settingData['INVOICE_AMOUNT_ROUNDOFF_TYPE']) && strtolower($settingData['INVOICE_AMOUNT_ROUNDOFF_TYPE']) == 'round') {
                $grand_total = round((float)$final_data['invoice_amount']);
            }
        } else {
            $grand_total = round($final_data['invoice_amount'], 2);
        }
        $final_data['grand_total'] = $grand_total;
        $final_data['principal_arrears'] = $outstandingPrincipalAmount;
        $final_data['interest_arrears'] = $interestAr;
        $final_data['balance_due'] = round($final_data['principal_arrears'] + $final_data['grand_total'] + $final_data['interest_arrears'], 2);
        $this->data = $final_data;
    }

    // get unit common bill list details
    public function getUnitsCommonBillListDetail($data = array())
    {
        $arrCommonBill = array();
        foreach ($data['commonBillData'] as $eachCommonBill) {
            // if (!empty($eachCommonBill['bill_date'])) {
            //     $eachCommonBill['bill_date'] = $this->getDisplayDate($eachCommonBill['bill_date']);
            // }
            // if (!empty($eachCommonBill['due_date'])) {
            //     $eachCommonBill['due_date'] = $this->getDisplayDate($eachCommonBill['due_date']);
            // }
            // if (!empty($eachCommonBill['from_date'])) {
            //     $eachCommonBill['from_date'] = $this->getDisplayDate($eachCommonBill['from_date']);
            // }
            // if (!empty($eachCommonBill['to_date'])) {
            //     $eachCommonBill['to_date'] = $this->getDisplayDate($eachCommonBill['to_date']);
            // }

            if (empty($arrCommonBillDetail['advance_amount'])) {
                $arrCommonBillDetail['advance_amount'] = 0;
            }
            // $eachCommonBill['amount'] += $eachCommonBill['interest_amount'] + $eachCommonBill['roundoff_amount'];
            // if($data['auth']['soc_id'] != '38' && !empty($eachCommonBill['interest_amount']) && $eachCommonBill['interest_amount'] > 0)
            // {
            //     $eachCommonBill['amount'] = $eachCommonBill['amount'] + $eachCommonBill['interest_amount'] + $eachCommonBill['roundoff_amount'];
            // }

            $taxes = ChsoneTaxLog::where("invoice_number", $eachCommonBill['invoice_number'])->get();
            $eachCommonBill['tax_amount'] = 0;

            $eachCommonBill['total_due'] = $eachCommonBill['amount'];
            foreach ($taxes as $tax) {
                $eachCommonBill['tax_amount'] += $tax->tax_amount;
            }
            //Get total due
            $eachCommonBill['total_due'] += $eachCommonBill['tax_amount'];

            if (!empty($eachCommonBill['advance_amount']) && $eachCommonBill['advance_amount'] > 0) {
                $eachCommonBill['total_due'] = round((float)$eachCommonBill['total_due'] - (float)$eachCommonBill['advance_amount'], 3);

                if ($eachCommonBill['total_due'] < 0) {
                    //$eachCommonBill['total_due'] = 0;
                }
            }
            // if($eachCommonBill['advance_amount'] < $eachCommonBill['amount'])
            // {
            // $eachCommonBill['total_due'] += round((float)$eachCommonBill['amount']-(float)$eachCommonBill['advance_amount'],3);
            // }
            $eachCommonBill['bill_amount'] = $eachCommonBill['amount'];
            // $eachCommonBill['end_date'] = $eachCommonBill['to_date'];

            //Get payment detail
            $eachCommonBill['payment_detail'] = $this->getCommonBillingPayments(array('soc_id' => $data['soc_id'], 'commonBillData' => $eachCommonBill, 'generateBy' => 'bill_number', 'id' => $eachCommonBill['id']));
            $eachCommonBill['payment_detail']['partial_paid'] = ($eachCommonBill['payment_detail']['payment_amount'] + $eachCommonBill['payment_detail']['tds_deducted']) - $eachCommonBill['advance_amount'];
            $eachCommonBill['payment_detail']['partial_paid'] = ($eachCommonBill['payment_detail']['partial_paid'] < 0) ? 0 : $eachCommonBill['payment_detail']['partial_paid'];

            //Replace empty integer variables with 0
            $this->replaceEmptyNumbers($eachCommonBill['payment_detail']);

            if (is_numeric($eachCommonBill['billing_type'])) {
                if (isset($eachCommonBill['payment_detail']['str_billing_type']) && !empty($eachCommonBill['payment_detail']['str_billing_type'])) {
                    $eachCommonBill['billing_type'] = ucwords($eachCommonBill['payment_detail']['str_billing_type']);
                } else {
                    $str_billing_type = IncomeCommonAreaCharge::where('id', $eachCommonBill['billing_type'])->first();
                    if (!empty($str_billing_type)) {
                        $eachCommonBill['billing_type'] = ucwords($str_billing_type->particular);
                    } else {
                        $eachCommonBill['billing_type'] = ucwords(str_replace('_', ' ', $eachCommonBill['billing_type']));
                    }
                }
            } else {
                $eachCommonBill['billing_type'] = ucwords(str_replace('_', ' ', $eachCommonBill['billing_type']));
            }

            //Calculate total due after payment
            if (!empty($eachCommonBill['payment_detail']) && isset($eachCommonBill['payment_detail']['payment_amount'])) {
                $eachCommonBill['total_due'] = round($eachCommonBill['total_due'] - (float)($eachCommonBill['payment_detail']['payment_amount'] + $eachCommonBill['payment_detail']['tds_deducted']), 2);
            }
            $eachCommonBill['balance_due'] = $eachCommonBill['total_due'];

            $arrCommonBill[] = $eachCommonBill;
        }

        $arrCommonBill[0]['advance_amount'] = (float)$arrCommonBill[0]['advance_amount'];
        $arrCommonBill[0]['amount'] = (float)$arrCommonBill[0]['amount'];
        $arrCommonBill[0]['transaction_charges'] = (float)$arrCommonBill[0]['transaction_charges'];
        $arrCommonBill[0]['discount_amount'] = (float)$arrCommonBill[0]['discount_amount'];
        $arrCommonBill[0]['tax_amount'] = (float)$arrCommonBill[0]['tax_amount'];
        $arrCommonBill[0]['total_due'] = $arrCommonBill[0]['total_due'] > 0 ? (float)$arrCommonBill[0]['total_due'] : 0;
        $arrCommonBill[0]['bill_amount'] = (float)$arrCommonBill[0]['bill_amount'];
        $arrCommonBill[0]['balance_due'] = (float)$arrCommonBill[0]['balance_due'];
        return $arrCommonBill;
    }

    // get common billing payments details
    public function getCommonBillingPayments($data = array())
    {
        $arrCommonBillingCharges = array();

        $queryBuilder = $this->tenantDB()->table('income_common_billing_payment as icb')
            ->select([
                DB::raw('SUM(icb.payment_amount) as payment_amount'),
                DB::raw('SUM(icb.tds_deducted) as tds_deducted'),
                DB::raw('SUM(icb.transaction_charges) as transaction_charges'),
                'common_area_charges.particular as str_billing_type'
            ])
            ->leftJoin('income_common_billing_charges as cbiiling', 'icb.fk_common_bill_id', '=', 'cbiiling.id')
            ->leftJoin('income_common_area_charges as common_area_charges', 'common_area_charges.id', '=', 'cbiiling.billing_type')
            ->where('icb.soc_id', '=', $data['soc_id'])
            ->where('cbiiling.billing_type', '=', $data['commonBillData']['billing_type'])
            ->where('cbiiling.fk_unit_id', '=', $data['commonBillData']['unit_id']);

        // Conditional billing type check
        if (!empty($data['commonBillData']['billing_type']) && strtolower($data['commonBillData']['bill_from']) != 'facility') {
            $queryBuilder->where('cbiiling.billing_type', '=', $data['commonBillData']['billing_type']);
        }

        // Additional condition for 'bill_number'
        if (isset($data['generateBy']) && $data['generateBy'] == 'bill_number') {
            $queryBuilder->where('icb.fk_common_bill_id', '=', $data['commonBillData']['id']);
        }

        // Condition for specific id
        if (!empty($data['id'])) {
            $queryBuilder->where('cbiiling.id', '=', $data['id']);
        }

        // Execute the query and fetch the result
        $arrCommonBillingCharges = $queryBuilder->get()->toArray();
        $arrCommonBillingCharges = json_decode(json_encode($arrCommonBillingCharges), true);
        // Post-process the result if it's not empty
        if (!empty($arrCommonBillingCharges)) {
            $arrCommonBillingCharges[0]['payment_amount'] = (float)$arrCommonBillingCharges[0]['payment_amount'];
            $arrCommonBillingCharges[0]['tds_deducted'] = (float)$arrCommonBillingCharges[0]['tds_deducted'];
            $arrCommonBillingCharges[0]['transaction_charges'] = (float)$arrCommonBillingCharges[0]['transaction_charges'];
            $arrCommonBillingCharges = current($arrCommonBillingCharges);
        }
        return $arrCommonBillingCharges;
    }

    public function replaceEmptyNumbers(&$array)
    {
        $array = collect($array)->map(function ($value) {
            return (empty($value) || is_null($value)) ? 0 : $value;
        })->toArray();
    }

    // get credit amount for individuals
    public function getCreditAmountForIndividuals($data = array())
    {
        $amountDetails = array();
        $creditAmountDetails = array('credit_amount' => 0, 'debit_amount' => 0, 'remaining_amount' => 0);

        // Start building the query
        $query = ChsoneCreditAccount::where('soc_id', $data['data']['soc_id'])
            ->where('account_id', $data['data']['account_id'])
            ->where('account_context', $data['data']['account_context'])
            ->where('use_credit', 'adjustable');

        // Add condition for bill_date if present
        if (isset($data['data']['bill_date']) && !empty($data['data']['bill_date'])) {
            $query->where(function ($q) use ($data) {
                $q->where('use_credit_after', '<=', $data['data']['bill_date'])
                    ->orWhereNull('use_credit_after');
            });
        }

        // Add condition for bill_type if present
        if (isset($data['data']['bill_type']) && !empty($data['data']['bill_type'])) {
            $query->where(function ($q) use ($data) {
                $q->where('use_credit_for', $data['data']['bill_type'])
                    ->orWhere('use_credit_for', 'both');
            });
        }

        // Execute the query
        $amountDetails = $query->get()->toArray();

        if (!empty($amountDetails)) {
            foreach ($amountDetails as $amountData) {
                if ($amountData['transaction_type'] == 'cr') {
                    $creditAmountDetails['credit_amount'] = $creditAmountDetails['credit_amount'] + $amountData['amount'];
                } else {
                    $creditAmountDetails['debit_amount'] = $creditAmountDetails['debit_amount'] + $amountData['amount'];
                }
            }
        }
        $creditAmountDetails['remaining_amount'] = $creditAmountDetails['credit_amount'] - $creditAmountDetails['debit_amount'];

        if ($creditAmountDetails['remaining_amount'] < 0) {
            $creditAmountDetails['remaining_amount'] = 0;
        }
        return $creditAmountDetails;
    }

    // get incident invoice unpaid bill
    public function getIncidentInvoiceUnpaidBill($data = array())
    {
        $arrBillingDetail = $arrIncidentBillDetail = array();
        if (!empty($data['unit_id']) && !empty($data['soc_id'])) {

            // Build the query
            $query = IncomeCommonBillingCharge::select(
                'ch.id', 'ch.soc_id', 'ch.fk_member_id', 'ch.invoice_number', 'ch.fk_unit_id', 'ch.billing_type',
                'ch.from_date', 'ch.to_date', 'ch.bill_date', 'ch.payment_mode', 'ch.advance_amount', 'ch.amount',
                'ch.transaction_charges', 'ch.discount_amount', 'ch.payment_instrument', 'ch.payment_status', 'ch.status',
                'ch.bill_from', 'ch.created_date', 'ch.created_by', 'ch.updated_date', 'ch.updated_by',
                'ch.due_date', 'ui.principal_amount', 'ui.interest_amount', 'ui.outstanding_principal',
                'ui.outstanding_interest', 'ui.roundoff_amount'
            )
                ->from('income_common_billing_charges as ch')
                ->where('ch.soc_id', $data['soc_id'])
                ->where('ch.fk_unit_id', $data['unit_id'])
                ->whereIn('ch.payment_status', ["partialpaid", "unpaid"])
                ->leftJoin('income_unit_invoices as ui', 'ch.invoice_number', '=', 'ui.invoice_number')
                ->orderBy('ch.id');

            $objBillingDetail = $query->get();

            if (!empty($objBillingDetail)) {
                $arrBillingDetail = $objBillingDetail->toArray();

                if (!empty($arrBillingDetail)) {
                    $i = $arrIncidentBillDetail['total_unpaid_invoice_amount'] = 0;

                    foreach ($arrBillingDetail as $eachIncidentBill) {

                        $arrIncidentBillDetail[$i] = $eachIncidentBill;
                        $arrIncidentBillDetail[$i]['from_date'] = date('Y-m-d', strtotime($eachIncidentBill['from_date']));
                        $arrIncidentBillDetail[$i]['to_date'] = date('Y-m-d', strtotime($eachIncidentBill['to_date']));
                        $arrIncidentBillDetail[$i]['bill_date'] = date('Y-m-d', strtotime($eachIncidentBill['bill_date']));
                        $arrIncidentBillDetail[$i]['due_date'] = date('Y-m-d', strtotime($eachIncidentBill['due_date']));

                        // initialize due amount
                        $arrIncidentBillDetail[$i]['due_amount'] = 0;

                        //Get tax amount
                        $taxAmount = $this->getInvoiceTaxAmount(array('soc_id' => $data['soc_id'], 'invoice_number' => $eachIncidentBill['invoice_number']));
                        $arrIncidentBillDetail[$i]['due_amount'] += $taxAmount;

                        $arrIncidentBillDetail[$i]['due_amount'] += (float)round(($eachIncidentBill['amount'] + $eachIncidentBill['transaction_charges'] + $eachIncidentBill['interest_amount'] + $eachIncidentBill['roundoff_amount']) - ($eachIncidentBill['advance_amount'] + $eachIncidentBill['discount_amount']), 3);
                        if (!empty($eachIncidentBill['payment_status']) && strtolower($eachIncidentBill['payment_status']) != 'paid') {
                            $arrIncidentBillDetail[$i]['payment_detail'] = $this->getCommonBillingInvoicePayments(array('soc_id' => $data['soc_id'], 'unit_id' => $data['unit_id'], 'common_bill_id' => $eachIncidentBill['id']));
                            if (!empty($arrIncidentBillDetail[$i]['payment_detail'])) {
                                $arrIncidentBillDetail[$i]['due_amount'] = (float)round($arrIncidentBillDetail[$i]['due_amount'] - ($arrIncidentBillDetail[$i]['payment_detail']['payment_amount'] + $arrIncidentBillDetail[$i]['payment_detail']['tds_deducted']), 3);
                            }
                        } else {
                            $arrIncidentBillDetail[$i]['due_amount'] = 0;
                        }

                        if ($arrIncidentBillDetail[$i]['due_amount'] < 0) {
                            $arrIncidentBillDetail[$i]['due_amount'] = 0;
                        }

                        $arrIncidentBillDetail['total_unpaid_invoice_amount'] += $arrIncidentBillDetail[$i]['due_amount'];
                        $arrIncidentBillDetail['invoice_detail'][$i] = $arrIncidentBillDetail[$i];
                        unset($arrIncidentBillDetail[$i]);
                        $i++;
                    }
                }
            }
        }
        return $arrIncidentBillDetail;
    }

    // get invoice tax amount
    public function getInvoiceTaxAmount($data = array())
    {
        $taxAmount = 0;

        $arrTaxClass = $this->getAppliedTaxDetail($data);
        if (!empty($arrTaxClass)) {
            foreach ($arrTaxClass as $eachTaxLog) {
                $taxAmount += $eachTaxLog['tax_amount'];
            }
        }

        return $taxAmount;
    }

    // get applied tax detail
    public function getAppliedTaxDetail($data = array())
    {
        $arrTaxClass = array();
        // Build the base query
        $query = ChsoneTaxLog::where('soc_id', $data['soc_id'])
            ->whereIn('invoice_number', [$data['invoice_number']]);

        // Check if `showCancelledInvoice` is set, and apply conditions accordingly
        if (isset($data['showCancelledInvoice']) && !empty($data['showCancelledInvoice'])) {
            // Do not exclude cancelled invoices
            $query = ChsoneTaxLog::where('soc_id', $data['soc_id'])
                ->whereIn('invoice_number', [$data['invoice_number']]);
        } else {
            // Exclude cancelled invoices
            $query->where('status', '!=', 'cancelled');
        }

        // Execute the query and convert the result to an array
        $arrTaxClass = $query->get();

        if (!empty($objTaxClass)) {
            $arrTaxClass = $objTaxClass->toArray();
        }
        return $arrTaxClass;
    }

    // get common billing invoice payments
    public function getCommonBillingInvoicePayments($data = array())
    {
        $arrCommonBillingCharges = array();
        // Use the Laravel query builder to construct the query
        $query = $this->tenantDB()->table('income_common_billing_payment as icb')
            ->select(
                DB::raw('SUM(icb.payment_amount) as payment_amount'),
                DB::raw('SUM(icb.tds_deducted) as tds_deducted'),
                DB::raw('SUM(icb.transaction_charges) as transaction_charges'),
                DB::raw('SUM(icb.discount_amount) as discount_amount')
            )
            ->where('icb.soc_id', $data['soc_id'])
            ->where('icb.fk_common_bill_id', $data['common_bill_id'])
            ->where('icb.unit_id', $data['unit_id']);

        // Execute the query and fetch the result as an array
        $arrCommonBillingCharges = $query->first();

        $arrCommonBillingCharges = json_decode(json_encode($arrCommonBillingCharges), true);

        if (!empty($arrCommonBillingCharges)) {
            // $arrCommonBillingCharges = current($arrCommonBillingCharges);
            $arrCommonBillingCharges = (array)$arrCommonBillingCharges;
        }
        return $arrCommonBillingCharges;
    }

    // get particular details
    public function getParticularDetails($data = array())
    {
        // fetch perticular details using invoice number from income_common_billing_charges table joined with income_common_area_charges table on billing_type column
        $queryBuilder = $this->tenantDB()->table('income_common_area_charges as icac')
            ->leftJoin('income_common_billing_charges as icbc', 'icac.id', '=', 'icbc.billing_type')
            ->select([
                'icac.id',
                'icac.soc_id',
                'icac.particular',
                'icac.rate',
                'icac.rate_type',
                'icac.duration',
                'icac.add_to_maintenance',
                'icac.effective_date',
                'icac.till_date',
                'icac.ledger_id',
                'icac.tax_class_id',
                'icac.apply_late_payment_interest'
            ])
            ->where('icbc.invoice_number', $data['invoice_number']);

        $result = $queryBuilder->first();

        $result = json_decode(json_encode($result), true);

        return $result;
    }

    // get late charges rule
    public function getLateChargesRule($data = array())
    {
        // Use Laravel's query builder to construct the query
        $query = $this->tenantDB()->table('income_late_payment_charges as rule')
            ->select(
                'rule.id',
                'rule.simple_interest',
                'rule.grace_period',
                'rule.calculate_from',
                'rule.interest_amount_type',
                'rule.interest_type',
                'rule.effective_date',
                'rule.applicable_taxes'
            )
            ->where('rule.soc_id', $data['soc_id'])
            ->where('rule.type', 'incidental')
            ->where('rule.effective_date', '<=', date('Y-m-d'))
            ->orderBy('rule.effective_date', 'desc')
            ->limit(1);

        // Execute the query and get the result
        $resultset = $query->get();

        $resultset = json_decode(json_encode($resultset), true);

        // Convert the result to array and return
        return $resultset;
    }

    // get onetime particulars
    public function getOnetimeParticulars($data = array())
    {
        $soc_id = $data['soc_id'];
        $effective_date = date("Y-m-d");
        $duration = 'onetime';

        $recordesOnetime = array();
        // Prepare the query for currently applicable records
        $currently_applicable_records = $this->tenantDB()->table('income_common_area_charges')
            ->select(DB::raw('MAX(effective_date) as effective_date, particular'))
            ->where('soc_id', $soc_id)
            ->where('effective_date', '<=', $effective_date)
            ->groupBy('particular')
            ->orderBy('updated_date', 'desc')
            ->get();

        $selected_records = [];

        if ($currently_applicable_records->count() > 0) {
            foreach ($currently_applicable_records as $record) {
                $selected_records[] = [
                    'effective_date' => $record->effective_date,
                    'particular' => $record->particular,
                ];
            }

            // Prepare the final query based on the selected records
            $query = $this->tenantDB()->table('income_common_area_charges')
                ->where('soc_id', $soc_id);

            foreach ($selected_records as $record) {
                $query->orWhere(function ($q) use ($record) {
                    $q->where('effective_date', $record['effective_date'])
                        ->where('particular', $record['particular']);
                });
            }

            // Get the final records ordered by updated_date
            $records = $query->orderBy('updated_date', 'desc')->get();
            $records = json_decode(json_encode($records), true);
            $recordesOnetime = $records;
        }

        return $recordesOnetime;
    }

    // get start and end date
    public function getStartAndEndDate($date)
    {
        // Create a Carbon instance from the given date
        $carbonDate = Carbon::parse($date);

        // Get the start of the month
        $startDate = $carbonDate->startOfMonth()->format('Y-m-d');

        // Get the end of the month
        $endDate = $carbonDate->endOfMonth()->format('Y-m-d');

        // Return end date
        return $endDate;
    }

    public function calculateDailyLateCharges($data = array())
    {
        if (isset($data['arrLatePaymentdetails'])) {
            $arrLatePaymentdetails = $data['arrLatePaymentdetails'];
            // $arrInvoiceSetting = $data['arrGeneralInvoiceSetting'];
            $amount = $data['amount'];
            $todaysDate = date('Y-m-d');

            $earlier = !empty($arrLatePaymentdetails['calculate_from']) && $arrLatePaymentdetails['calculate_from'] == 'billdate' ? $data['start_date'] : $data['dueDate'];
            $earlier = new \DateTime($earlier);

            if (!empty($data['lateChargeCalculationDate'])) {
                $later = $data['lateChargeCalculationDate'];
            } else {
                $later = $todaysDate;
            }

            if (isset($data['Late_Charges_Calculation_From']) && $data['Late_Charges_Calculation_From'] == 'submission') {
                // Retrieve submitted payments based on invoice number and soc_id
                $submitted = IncomeInvoicePaymentTracker::where('invoice_number', 'LIKE', '%' . $data['invoice_number'] . '%')
                    ->where('soc_id', $data['soc_id'])
                    ->whereIn('status', ['Y', 'P'])
                    ->get();

                if (!empty($submitted)) {
                    foreach ($submitted as $all) {
                        $submitted_date = $all->cheque_date;
                    }

                    $differ = (new \DateTime($later))->diff(new \DateTime($submitted_date))->format("%a");

                    if ($differ > 0) {
                        $later = $submitted_date;
                    }
                }
            } elseif (isset($data['paidDate']) && !empty($data['paidDate'])) {
                $later = $data['paidDate'];
            }

            $later = new \DateTime($later);
            if ($earlier > $later) {
                $later = new \DateTime($data['lateChargeCalculationDate']);
            }

            if ($earlier > $later) {
                $diff = 0;
            } else {
                $diff = $later->diff($earlier)->format("%a");
            }

            if (!empty($arrLatePaymentdetails['interest_amount_type']) && $arrLatePaymentdetails['interest_amount_type'] == 'fixed') {
                $singleRuleCharges = (float)round($arrLatePaymentdetails['simple_interest'], 2);
            } else {
                $t = (float)round($diff); //convert to number of year
                $p = (float)$amount;
                $r = !empty($arrLatePaymentdetails['simple_interest']) ? (float)($arrLatePaymentdetails['simple_interest'] / 100) : 0;
                if (isset($arrLatePaymentdetails['interest_type']) && !empty($arrLatePaymentdetails['interest_type']) && in_array(strtolower($arrLatePaymentdetails['interest_type']), array(
                        'monthly',
                        'quarterly',
                        'half_yearly',
                        'yearly'
                    ))) {

                    /**
                     * Daily coumpound interest
                     * Principle * (1 + (Rate/365)) ^ days - Principle
                     */
                    $singleRuleCharges = $p * pow((1 + ($r / 365)), $t) - $p;
                } else {
                    /**
                     * Daily simple interest
                     * (Rate/365) * Principle
                     */
                    $singleRuleCharges = ($r / 365) * $p * $t;
                }
            }
            return $singleRuleCharges;
        }
    }

    // get invoice roundoff adjustment amount
    public function getInvoiceRoundoffAdjustmentAmount($data = array())
    {
        $arrInvoiceAdjustedAmount = array('roundoff_amount' => 0, 'invoice_amount' => 0);

        //Check if invoice setting exist or not
        if (empty($data['arrGeneralInvoiceSetting'])) {
            $data['arrGeneralInvoiceSetting'] = $this->getgeneralsetting(array('soc_id' => $data['soc_id']));
        }
        //if(!empty($data['arrGeneralInvoiceSetting']['INVOICE_AMOUNT_ROUNDOFF']) && strtolower($data['arrGeneralInvoiceSetting']['INVOICE_AMOUNT_ROUNDOFF']) == 'yes')
        {
            //Get ttotal invoice amount
            $finalInvoiceAmount = $data['invoiceAmount'] + $data['interestAmount'];

            //In case of partial paid
            $actualFinalInvoiceAmount = $finalInvoiceAmount;
            if ($finalInvoiceAmount > $data['adjustedAdvanceAmount']) {
                $finalInvoiceAmount = (float)round($finalInvoiceAmount - $data['adjustedAdvanceAmount'], 3);
            }

            //Invoice round off based on settting
            $roundedInvoiceAmount = $this->getInvoiceRoundCharges(array('soc_id' => $data['soc_id'], 'invoiceAmount' => $finalInvoiceAmount, 'arrGeneralInvoiceSetting' => $data['arrGeneralInvoiceSetting']));

            //Get invoice difference amount after rounding
            $arrInvoiceAdjustedAmount['roundoff_amount'] = (float)round($roundedInvoiceAmount - $finalInvoiceAmount, 3);
            if ($data['adjustedAdvanceAmount'] >= $actualFinalInvoiceAmount) {
                $arrInvoiceAdjustedAmount['invoice_amount'] = $roundedInvoiceAmount;
            }
        }

        return $arrInvoiceAdjustedAmount;
    }

    // get general setting
    public function getgeneralsetting($data = array())
    {
        $final_array = $this->_getAllSetting($data);
        $incomeinvoicesetting = IncomeInvoiceSetting::where('soc_id', $data['soc_id'])->orderBy('effective_date', 'desc')->first();
        $final_array['generalsettingid'] = $incomeinvoicesetting->id;
        $final_array['invoicing_frequency'] = (isset($incomeinvoicesetting->invoicing_frequency) && $incomeinvoicesetting->invoicing_frequency != '') ? $incomeinvoicesetting->invoicing_frequency : '';
        $final_array['effective_date'] = $incomeinvoicesetting->effective_date;
        $final_array['general_setting_key_ids'] = $this->_getAllSettingWithId($data);
        return $final_array;
    }

    // get all setting
    private function _getAllSetting($data)
    {
        $incomeinvoicesetting = IncomeInvoiceSetting::where('soc_id', $data['soc_id'])->first();
        $incomeinvoicegeneralsetting = IncomeInvoiceGeneralSetting::where('soc_id', $data['soc_id'])->get()->toArray();
        foreach ($incomeinvoicegeneralsetting as $key => $value) {
            $final_array[$value['setting_key']] = $value['setting_value'];
        }
        return $final_array;
    }

    // get all setting with id
    private function _getAllSettingWithId($data)
    {
        $incomeinvoicegeneralsetting = IncomeInvoiceGeneralSetting::where('soc_id', $data['soc_id'])->get()->toArray();
        foreach ($incomeinvoicegeneralsetting as $value) {
            $final_array[$value['setting_key']] = $value['id'];
        }
        return $final_array;
    }

    // get invoice round charges
    public function getInvoiceRoundCharges($data = array())
    {
        $particularRoundedAmount = $data['invoiceAmount'] = number_format($data['invoiceAmount'], 4, '.', '');

        if ($data['invoiceAmount'] > 0 && !empty($data['arrGeneralInvoiceSetting']['INVOICE_AMOUNT_ROUNDOFF']) && strtolower($data['arrGeneralInvoiceSetting']['INVOICE_AMOUNT_ROUNDOFF']) == 'yes') {
            if (!empty($data['arrGeneralInvoiceSetting']['INVOICE_AMOUNT_ROUNDOFF_TYPE']) && strtolower($data['arrGeneralInvoiceSetting']['INVOICE_AMOUNT_ROUNDOFF_TYPE']) == 'upper') {
                $particularRoundedAmount = ceil((float)$data['invoiceAmount']);
            } elseif (!empty($data['arrGeneralInvoiceSetting']['INVOICE_AMOUNT_ROUNDOFF_TYPE']) && strtolower($data['arrGeneralInvoiceSetting']['INVOICE_AMOUNT_ROUNDOFF_TYPE']) == 'lower') {
                $particularRoundedAmount = floor((float)$data['invoiceAmount']);
            } elseif (!empty($data['arrGeneralInvoiceSetting']['INVOICE_AMOUNT_ROUNDOFF_TYPE']) && strtolower($data['arrGeneralInvoiceSetting']['INVOICE_AMOUNT_ROUNDOFF_TYPE']) == 'round') {
                $particularRoundedAmount = round((float)$data['invoiceAmount']);
            }
        } else {
            $particularRoundedAmount = round($data['invoiceAmount'], 2);
        }
        return $particularRoundedAmount;
    }
}
