<?php

namespace App\Console\Commands\Actions\Income;

use Illuminate\Console\Command;
use App\Console\Commands\Action;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Route;

class MemberCommonBillListDataSource extends Action
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'datasource:MemberCommonBillList {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Get Member Common Bill List by Id';
    protected $mapper = [
        'id' => 'id',
    ];

    protected $hugeData = true;

    /**
     * Execute the console command.
     */
    public function apply()
    {
        $invoice_number = '';
        $payment_status = '';

        $page = $this->input['page'] ?? 1;
        $per_page = !empty($this->input['per_page']) ? $this->input['per_page']: ($this->hugeData ? 100000 : 10);
        $offset = ($page - 1) * $per_page;
        $searchTerm = $this->input['filters']['search'] ?? '';

        if (isset($this->input['filters'])) {
            $invoice_number =
            !empty($this->input['filters']['invoice_number']) ? $this->input['filters']['invoice_number'] : '';
        unset($this->input['filters']['invoice_number']);

        $payment_status = isset($this->input['filters']['payment_status']) ? explode(',', $this->input['filters']['payment_status']) : [];

        }

        $unitId = $this->input['unit_id'];
        $obj = $this->tenantDB()->table('income_common_billing_charges as icb')
        ->select(
            'icb.id',
            'icb.fk_unit_id AS unit_id',
            'icb.invoice_number',
            'icb.billing_type',
            'icac.particular',
            'icb.advance_amount',
            'icb.amount',
            'icb.payment_status',
            'icb.status',
            'icb.bill_date',
            'icb.from_date',
            'icb.to_date',
            'icb.due_date',
            'icb.bill_from',
            'icb.payment_mode',
            'icb.transaction_charges',
            'icb.discount_amount',
            'icb.payment_instrument',
            'unit.unit_id',
            'unit.unit_flat_number',
            'unit.soc_building_name',
            'memmaster.member_first_name',
            'memmaster.gstin',
            'memmaster.member_last_name',
            'memmaster.member_mobile_number',
            'unitInvoice.member_gstin',
            'unitInvoice.principal_amount',
            'unitInvoice.interest_amount',
            'unitInvoice.outstanding_principal',
            'unitInvoice.outstanding_interest',
            'unitInvoice.roundoff_amount',
            DB::raw('ROUND(
                icb.amount +
                IFNULL((SELECT IFNULL(SUM(ctl.tax_amount), 0) FROM chsone_tax_log AS ctl WHERE ctl.invoice_number = unitInvoice.invoice_number), 0)
                - IFNULL(icb.advance_amount, 0)
                - IFNULL(SUM(icbp.payment_amount), 0)
            ) AS total_due')
            )
            ->selectRaw('CONCAT(DATE_FORMAT(icb.from_date, "%d/%m/%Y"), " To ", DATE_FORMAT(icb.to_date, "%d/%m/%Y")) AS period')
            ->selectRaw('CONCAT(unit.soc_building_name, " / ", unit.unit_flat_number) AS unit_building_name')
            ->selectRaw('IF(SUM(icbp.payment_amount) = 0, 0, SUM(icbp.payment_amount)) AS payment_amount')
            ->selectRaw('CONCAT(memmaster.member_first_name, " ", memmaster.member_last_name) AS member_name')
            ->selectRaw('(SELECT IF(SUM(ctl.tax_amount) = 0, 0, SUM(ctl.tax_amount)) FROM chsone_tax_log AS ctl WHERE ctl.invoice_number = unitInvoice.invoice_number) AS tax_amount')
            ->leftJoin('chsone_members_master as memmaster', 'icb.fk_member_id', '=', 'memmaster.id')
        ->leftJoin('chsone_units_master as unit', 'icb.fk_unit_id', '=', 'unit.unit_id')
        ->leftJoin('income_unit_invoices as unitInvoice', 'icb.invoice_number', '=', 'unitInvoice.invoice_number')
        ->leftJoin('income_common_area_charges as icac', 'icb.billing_type', '=', 'icac.id')
        ->leftJoin('income_common_billing_payment as icbp', 'icb.id', '=', 'icbp.fk_common_bill_id')
        ->where('icb.payment_status', '!=', 'cancelled')
        ->where('icb.fk_unit_id', $unitId)
        ->groupBy([
            'icb.id',
            'icb.fk_unit_id',
            'icb.invoice_number',
            'icb.billing_type',
            'icac.particular',
            'icb.advance_amount',
            'icb.amount',
            'icb.payment_status',
            'icb.status',
            'icb.bill_date',
            'icb.from_date',
            'icb.to_date',
            'icb.due_date',
            'icb.bill_from',
            'icb.payment_mode',
            'icb.transaction_charges',
            'icb.discount_amount',
            'icb.payment_instrument',
            'unit.unit_id',
            'unit.unit_flat_number',
            'unit.soc_building_name',
            'memmaster.member_first_name',
            'memmaster.gstin',
            'memmaster.member_last_name',
            'memmaster.member_mobile_number',
            'unitInvoice.member_gstin',
            'unitInvoice.principal_amount',
            'unitInvoice.interest_amount',
            'unitInvoice.outstanding_principal',
            'unitInvoice.outstanding_interest',
            'unitInvoice.roundoff_amount',
            'unitInvoice.invoice_number'
        ])
        ->orderBy('icb.id', 'DESC');

        if ($invoice_number) {
            $obj = $obj->where('icb.invoice_number', 'like', '%' . $invoice_number . '%');
        }

        if ($payment_status) {
            $obj = $obj->whereIn('icb.payment_status', $payment_status);
        }


        if ($searchTerm) {
            // Apply WHERE filters
            $whereQuery = clone $obj;
            $whereQuery = $whereQuery->where(function ($query) use ($searchTerm) {
                $query->where('icb.bill_date', 'LIKE', '%' . $searchTerm . '%')
                    ->orWhere('icb.due_date', 'LIKE', '%' . $searchTerm . '%')
                    ->orWhere('icb.invoice_number', 'LIKE', '%' . $searchTerm . '%')
                    ->orWhere('icb.amount', 'LIKE', '%' . $searchTerm . '%')
                    ->orWhere('icac.particular', 'LIKE', '%' . $searchTerm . '%')
                    ->orWhere('icb.advance_amount', 'LIKE', '%' . $searchTerm . '%')
                    ->orWhere(DB::raw('DATE_FORMAT(icb.from_date, "%d/%m/%Y")'), 'LIKE', '%' . $searchTerm . '%')
                    ->orWhere(DB::raw('DATE_FORMAT(icb.to_date, "%d/%m/%Y")'), 'LIKE', '%' . $searchTerm . '%');
            });

            $checkResult = $whereQuery->get();

            if ($checkResult->isEmpty()) {
                // Apply HAVING filters if WHERE filters returned no results
                $obj = $obj->having(function ($query) use ($searchTerm) {
                    $query->orHaving(DB::raw('ROUND(
                        icb.amount
                        + IFNULL((SELECT IFNULL(SUM(ctl.tax_amount), 0) FROM chsone_tax_log AS ctl WHERE ctl.invoice_number = icb.invoice_number), 0)
                        - IFNULL(icb.advance_amount, 0)
                        - IFNULL(SUM(icbp.payment_amount), 0)
                    )'), 'LIKE', '%' . $searchTerm . '%');
                });
            } else {
                // If WHERE filters have results, use that query
                $obj = $whereQuery;
            }
        }

        $currentRoute = Route::current();
        $routeUri = $currentRoute->uri();

        if ($routeUri == 'api/admin/common-billing/unpaidInvoices/{id}') {
            $obj->where('icb.payment_status', 'unpaid');
        }

        // Continue with the rest of your query...

        // Continue with the rest of your query...


        $count = $obj->get()->count();

        $obj = $obj->offset($offset);
        $obj = $obj->limit($per_page);

        $result = $obj->get();

        $this->data = $result;
        $this->meta['pagination']['total']=$count;

    }

}
