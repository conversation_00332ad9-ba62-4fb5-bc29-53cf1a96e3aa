<?php

namespace App\Console\Commands\Actions\Income;

use Illuminate\Console\Command;
use App\Console\Commands\Action;
use App\Models\Tenants\ChsoneCreditAccount;
use App\Models\Tenants\ChsoneGrpLedgerTree;
use App\Models\Tenants\ChsoneLedgerTransaction;
use App\Models\Tenants\ChsoneMembersMaster;
use App\Models\Tenants\ChsoneTaxLog;
use App\Models\Tenants\IncomeInvoiceAdjustment;
use App\Models\Tenants\IncomeUnitInvoice;
use Illuminate\Http\Request;
use Exception;

class confirmInvoiceCancellationDataSource extends Action
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'datasource:confirmInvoiceCancellation {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Member Invoice Cancellation Data Source';

    /**
     * Execute the console command.
     */
    public function apply()
    {
        $unit_invoice_id = $this->input['id'];

        // check unit_invoice_id is exist or not in income_unit_invoices table if not exist then return error
        $invoiceDetails = $this->tenantDB()->table('income_unit_invoices')
            ->where('unit_invoice_id', $unit_invoice_id)
            ->get();

        if(count($invoiceDetails) == 0){
            $this->status = 'error';
            $this->message = 'No invoice found for this id / provide a valid invoice id';
            $this->statusCode = 400;
            $this->data = [];
            return;
        }

        if($invoiceDetails[0]->status == 'cancelled'){
            $this->status = 'error';
            $this->message = 'Invoice already cancelled';
            $this->statusCode = 403;
            $this->data = [];
            return;
        }

        // dd($invoiceDetails);

        // get ledger_account_id from chsone_units_master table whose unit_id is equal to $invoiceDetails[0]->fk_unit_id and soc_id is equal to $invoiceDetails[0]->fk_soc_id
        $ledgerDetails = $this->tenantDB()->table('chsone_units_master')
            ->where('unit_id', $invoiceDetails[0]->fk_unit_id)
            ->where('soc_id', $invoiceDetails[0]->soc_id)
            ->select('ledger_account_id')
            ->first();

        // check ledger_account_id is exist or not in chsone_grp_ledger_tree table if not exist then return error
        $arrBookerLedgerDetails = $this->checkLedger(array(
            'auth' => $invoiceDetails[0]->soc_id,
            'ledger_id' => $ledgerDetails->ledger_account_id
        ));

        // get Maintenance Invoice Particular Detail
        $arrMaintenanceInvoiceParticular = $this->getMaintenanceInvoiceParticularDetail(array(
            'arrInvoiceData' => $invoiceDetails[0],
            'bill_type' => 'maintenance'
        ));

        $arrInvoiceParticular = json_decode(json_encode($arrMaintenanceInvoiceParticular), true);
        // $arrInvoiceParticular = $arrMaintenanceInvoiceParticular;
        // insert particular cancel entry in ledger
        if (!empty($arrInvoiceParticular)) {
            foreach ($arrInvoiceParticular as $eachParticular) {
                if (!empty($eachParticular['ledger_id'])) {
                    // bill ledger entry
                    $arrListnerData = array();
                    $arrListnerData['auth']['soc_id'] = $invoiceDetails[0]->soc_id;
                    $arrListnerData['auth']['user_id'] = $this->input['user_id'] ?? 0;
                    $arrListnerData['voucher_type'] = "income";
                    $arrListnerData['voucher_reference_number'] = $invoiceDetails[0]->invoice_number;
                    $arrListnerData['voucher_reference_id'] = $invoiceDetails[0]->unit_invoice_id;
                    $arrListnerData['is_cancelled'] = 1;
                    $arrListnerData['to_ledger_id'] = $arrBookerLedgerDetails['recieving_ledger_id'];
                    $arrListnerData['from_ledger_id'] = $eachParticular['ledger_id'];
                    $arrListnerData['transaction_date'] = date('Y-m-d');
                    $arrListnerData['transaction_amount'] = $eachParticular['amount'];
                    $arrListnerData['narration'] = 'Cancellation of ' . $eachParticular['particular'] . ' charges on invoice number ' . $invoiceDetails[0]->invoice_number . ' dated on ' . date('d-m-Y');
                    $arrListnerData['to_ledger_name'] = $arrBookerLedgerDetails['receiver_name'];
                    $arrListnerData['from_ledger_name'] = $eachParticular['account_name'];
                    $arrListnerData['payment_reference'] = '';
                    $arrListnerData['transaction_type'] = '';
                    $arrListnerData['mode_of_payment'] = '';
                    $arrListnerData['other_payment_ref'] = '';
                    $arrLedgerEntry = $this->transactionLedgerEntry($arrListnerData);
                    if (!isset($arrLedgerEntry['success'])) {
                        $this->status = 'error';
                        $this->message = 'Due to some technical issue unable to save record.';
                        $this->statusCode = 400;
                        return;
                    }
                }
            }
        } else {
            $this->status = 'error';
            $this->message = 'No particular found for this invoice';
            $this->statusCode = 400;
            return;
        }

        // get interest Invoice Particular Detail
        if (!empty($invoiceDetails[0]->interest_amount) && $invoiceDetails[0]->interest_amount > 0) {
            // Cancellation Ledger entry in case of late charges
            $arrInterestLedgerDetails = $this->checkLedger(array(
                'auth' => $invoiceDetails[0]->soc_id,
                'ledger_name' => 'Interest',
                'context' => "membercontri"
            ));

            $arrLedgerData['auth']['soc_id'] = $invoiceDetails[0]->soc_id;
            $arrListnerData['auth']['user_id'] = $this->input['user_id'] ?? 0;
            $arrLedgerData['voucher_type'] = "income";
            $arrLedgerData['voucher_reference_number'] = $invoiceDetails[0]->invoice_number;
            $arrLedgerData['voucher_reference_id'] = $invoiceDetails[0]->unit_invoice_id;
            $arrLedgerData['is_cancelled'] = 1;
            $arrLedgerData['to_ledger_id'] = $arrBookerLedgerDetails['recieving_ledger_id'];
            $arrLedgerData['from_ledger_id'] = $arrInterestLedgerDetails['recieving_ledger_id'];
            $arrLedgerData['transaction_date'] = date('Y-m-d');
            $arrLedgerData['to_ledger_name'] = $arrBookerLedgerDetails['receiver_name'];
            $arrLedgerData['from_ledger_name'] = $arrInterestLedgerDetails['receiver_name'];
            $arrLedgerData['payment_reference'] = '';
            $arrLedgerData['transaction_type'] = '';
            $arrLedgerData['mode_of_payment'] = '';
            $arrLedgerData['other_payment_ref'] = '';
            $arrLedgerData['transaction_amount'] = $invoiceDetails[0]->interest_amount;
            $arrLedgerData['narration'] = 'Cancellation of Late payment charges against invoice ' . $invoiceDetails[0]->invoice_number . ' dated ' . date('d-m-Y');
            $resInterestLedgerEntry = $this->transactionLedgerEntry($arrLedgerData);
            if (!isset($resInterestLedgerEntry['success'])) {
                $this->status = 'error';
                $this->message = 'Due to some technical issue unable to save record.';
                $this->statusCode = 400;
                return;
            }
        } else {
            $this->status = 'error';
            $this->message = 'No interest found for this invoice';
            $this->statusCode = 400;
            return;
        }

        // check tax details and insert tax ledger entry
        $arrTaxDetail = $this->getAppliedTaxDetail(array(
            'soc_id' => $invoiceDetails[0]->soc_id,
            'invoice_number' => $invoiceDetails[0]->invoice_number,
        ));

        if (!empty($arrTaxDetail)) {
            $arrTaxLogLedgerDetail = $this->getTaxLedgerDetailByTaxLog(array(
                'arrTaxLogDetail' => $arrTaxDetail
            ));
            $arrTaxLogLedgerDetail = $this->taxNewLedgerExit(array(
                'soc_id' => $invoiceDetails[0]->soc_id,
                'arrTaxLedgerName' => $arrTaxLogLedgerDetail
            ));
            
            // Update tax status to cancelled
            $updateTaxLogResponse = $this->updateTaxLogStatus(array(
                'soc_id' => $invoiceDetails[0]->soc_id,
                'invoice_number' => $invoiceDetails[0]->invoice_number,
                'status' => 'cancelled'
            ));

            foreach ($arrTaxDetail as $eachTaxDetail) {
                $arrListnerData = array();
                $arrListnerData['auth']['soc_id'] = $invoiceDetails[0]->soc_id;
                $arrListnerData['auth']['user_id'] = $this->input['user_id'] ?? 0;
                $arrListnerData['voucher_type'] = "income";
                $arrListnerData['voucher_reference_number'] = $invoiceDetails[0]->invoice_number;
                $arrListnerData['voucher_reference_id'] = $invoiceDetails[0]->unit_invoice_id;
                $arrListnerData['is_cancelled'] = 1;
                $arrListnerData['to_ledger_id'] = $arrBookerLedgerDetails['recieving_ledger_id'];
                $arrListnerData['transaction_date'] = date('Y-m-d');
                $arrListnerData['transaction_amount'] = $eachTaxDetail['tax_amount'];
                $arrListnerData['narration'] = 'Cancellation of ' . ucwords($eachTaxDetail['tax_rule']) . ' tax charges on ' . $eachTaxDetail['particular'] . ' against invoice number ' . $invoiceDetails[0]->invoice_number . ' dated on ' . date('d-m-Y');
                $arrListnerData['to_ledger_name'] = $arrBookerLedgerDetails['receiver_name'];
                $arrListnerData['payment_reference'] = '';
                $arrListnerData['transaction_type'] = '';
                $arrListnerData['mode_of_payment'] = '';
                $arrListnerData['other_payment_ref'] = '';

                $ledgerName = $this->getTaxLedgerName(array(
                    'taxClassDetail' => $eachTaxDetail
                ));
                if (isset($arrTaxLogLedgerDetail[$ledgerName]) && !empty($arrTaxLogLedgerDetail[$ledgerName])) {
                    $arrListnerData['from_ledger_id'] = $arrTaxLogLedgerDetail[$ledgerName]['ledger_account_id'];
                    $arrListnerData['from_ledger_name'] = $arrTaxLogLedgerDetail[$ledgerName]['ledger_account_name'];
                } else {
                    if (empty($arrCommonTaxesLedger)) {
                        $arrCommonTaxesLedger = $this->checkLedger(array(
                            'auth' => $invoiceDetails[0]->soc_id,
                            'ledger_name' => 'taxes',
                            'context' => "membercontri"
                        ));
                    }
                    if (!empty($arrCommonTaxesLedger)) {
                        $arrListnerData['from_ledger_id'] = $arrCommonTaxesLedger['recieving_ledger_id'];
                        $arrListnerData['from_ledger_name'] = $arrCommonTaxesLedger['receiver_name'];
                    }
                }

                $arrLedgerEntry = $this->transactionLedgerEntry($arrListnerData);
                if (!isset($arrLedgerEntry['success'])) {
                    $arrResponse['status'] = 'error';
                    $arrResponse['message'] = 'Due to some technical issue unable to save record.';
                    return $arrResponse;
                }
            }
        }

        //Update ledger transaction cancelled status
        $arrResponse = $this->updateReversedPaymentLedgerTransactionStatus(array('soc_id' => $invoiceDetails[0]->soc_id, 'voucher_type' => "income", 'voucher_reference_number' => $invoiceDetails[0]->invoice_number, 'voucher_reference_id' => $invoiceDetails[0]->unit_invoice_id, 'is_cancelled' => 1));
        if (!empty($arrResponse) && strtolower($arrResponse['status']) == 'error') {
            $this->status = 'error';
            $this->message = 'Unable to cancel ledger entry on Invoice.';
            $this->statusCode = 400;
            return;
        }

        // need to get all invoice number to check whether this is the first invoice or not
        $arrTotalGeneratedInvoice = $this->getAllInvoiceNumber(array(
            'soc_id' => $invoiceDetails[0]->soc_id,
            'unit_id' => $invoiceDetails[0]->fk_unit_id
        ));

        if (count($arrTotalGeneratedInvoice) == 1) {
            // Adjust invoice outstanding in case of cancellatioon of first invoice
            if ($invoiceDetails[0]->principal_amount > 0 || $invoiceDetails[0]->principal_amount > 0) {
                $arrPendingOutstanding = $this->getPendingOutstanding(array('soc_id' => $invoiceDetails[0]->soc_id, 'unit_id' => $invoiceDetails[0]->fk_unit_id, 'bill_type' => 'maintenance'));
                $arrAdjustmentData = array(
                    'soc_id' => $invoiceDetails[0]->soc_id,
                    'unit_id' => $invoiceDetails[0]->fk_unit_id,
                    'principal_amount' => (float) $arrPendingOutstanding['principal_amount'],
                    'interest_amount' => (float) $arrPendingOutstanding['interest_amount'],
                    'delayed_payment_charges' => (float) $arrPendingOutstanding['delayed_payment_charges'],
                    'adjustment_type' => 'outstanding',
                    'bill_type' => 'maintenance',
                    'is_generated' => 0
                );
                $arrResponse = $this->updateInvoiceAdjustment($arrAdjustmentData);
            } elseif (!empty($invoiceDetails[0]->advance_amount) && $invoiceDetails[0]->advance_amount > 0) {
                $arrAdjustmentData = array(
                    'soc_id' => $invoiceDetails[0]->soc_id,
                    'unit_id' => $invoiceDetails[0]->fk_unit_id,
                    'principal_amount' => (float) $invoiceDetails[0]->advance_amount,
                    'interest_amount' => 0,
                    'adjustment_type' => 'advance',
                    'bill_type' => 'maintenance',
                    'is_generated' => 0
                );
                $arrResponse = $this->updateInvoiceAdjustment($arrAdjustmentData);
            }
        } elseif (count($arrTotalGeneratedInvoice) > 1) {
            if (!empty($invoiceDetails[0]->advance_amount) && $invoiceDetails[0]->advance_amount > 0) {

                $arrDebitDetail = $this->getCreditDrByInvoiceNumber(array(
                    'soc_id' => $invoiceDetails[0]->soc_id,
                    'unit_id' => $invoiceDetails[0]->fk_unit_id,
                    'invoice_number' => $invoiceDetails[0]->invoice_number
                ));

                $arrCreditData['soc_id'] = $invoiceDetails[0]->soc_id;
                $arrCreditData['account_id'] = $invoiceDetails[0]->fk_unit_id;
                $arrCreditData['account_context'] = 'unit';
                $arrCreditData['transaction_type'] = 'cr';
                $arrCreditData['context'] = 'system';
                $arrCreditData['credit_used_type'] = 'adjustable';
                $arrCreditData['used_for'] = 'maintenance';
                $arrCreditData['payment_date'] = date('Y-m-d');
                $arrCreditData['narration'] = 'Amount Rs ' . $arrDebitDetail['amount'] . ' has credited from cancellation against invoice ' . $invoiceDetails[0]->invoice_number;
                $arrCreditData['payment_amount'] = $arrDebitDetail['amount'];

                if (!empty($invoiceDetails[0]->fk_unit_id)) {
                    $arrMemberDetail = $this->getPrimaryMemberByUnitId(array(
                        'soc_id' => $arrCreditData['soc_id'],
                        'unit_id' => $invoiceDetails[0]->fk_unit_id
                    ));
                }
                $arrCreditData['account_name'] = $arrMemberDetail['member_first_name'] . ' ' . $arrMemberDetail['member_last_name'];

                $creditAccountresponse = $this->saveCreditAccountResponse(array(
                    'auth' => true,
                    'process' => 'fetch',
                    'soc_id' => $arrCreditData['soc_id'],
                    'id' => '',
                    'data' => $arrCreditData,
                    'user' => $this->input['user_id'] ?? 0,
                    'username' => $this->input['user']['name']
                ));
                if ($creditAccountresponse['error']) {
                    $arrResponse['status'] = 'error';
                    $arrResponse['message'] = 'unable to create debit note';
                    return $arrResponse;
                }
            }
        }

        // update status in income_unit_invoices table
        $arrResponse = $this->updateUnitIncomeInvoice(array(
            'soc_id' => $invoiceDetails[0]->soc_id,
            'updated_by' => $this->input['user_id'] ?? 0,
            'status' => 'cancelled',
            'invoice_number' => $invoiceDetails[0]->invoice_number,
            'cancel_date' => date('Y-m-d H:i:s')
        ));

        if (!empty($arrResponse) && strtolower($arrResponse['status']) == 'success') {
            $this->status = 'success';
            $this->message = 'Invoice has been cancelled successfully';
            $this->statusCode = 200;
            return;
        } else {
            $this->status = 'error';
            $this->message = 'Unable to cancel invoice';
            $this->statusCode = 400;
            return;
        }
    }

    public function checkLedger($data)
    {
        $arrClinetLedgerDetails = array();
        if(isset($data['ledger_name']) && !empty($data['ledger_name']))
        {
            $condition = 'soc_id="'.$data['auth'].'" AND context="'.$data['context'].'" AND entity_type="ledger" AND ledger_account_name = "'.$data['ledger_name'].'"';
            if(isset($data['behaviour']) && !empty($data['behaviour']) && !empty($condition))
            {
                $condition .= ' AND behaviour="'.$data['behaviour'].'"';
            }
        }
        elseif(isset($data['ledger_id']) && !empty($data['ledger_id']))
        {
            $condition = 'soc_id="'.$data['auth'].'" AND ledger_account_id="'.$data['ledger_id'].'"';
        }

        $objBookerLedger = ChsoneGrpLedgerTree::whereRaw($condition)->first();

        if(!empty($objBookerLedger)) {
            $arrClinetLedgerDetails['recieving_ledger_id'] = $objBookerLedger->ledger_account_id;
            $arrClinetLedgerDetails['receiver_name'] = $objBookerLedger->ledger_account_name;
        }
        unset($objBookerLedger);
        return $arrClinetLedgerDetails;
    }

    public function getMaintenanceInvoiceParticularDetail($data)
    {
        $queryBuilder = $this->tenantDB()->table('income_invoice_particular as InvoiceParticular')
        ->select([
            'InvoiceParticular.particular',
            'InvoiceParticular.amount',
            'IncomeAccounts.fk_income_ledger_id as ledger_id',
            'IncomeAccounts.account_name'
        ])
        ->join('income_invoice_rules as InvoiceRule', 'InvoiceParticular.fk_rule_id', '=', 'InvoiceRule.id')
        ->leftJoin('income_accounts as IncomeAccounts', 'InvoiceRule.income_account_id', '=', 'IncomeAccounts.account_id')
        ->where('InvoiceParticular.soc_id', $data['arrInvoiceData']->soc_id)
        ->where('InvoiceParticular.fk_unit_invoice_id', $data['arrInvoiceData']->unit_invoice_id)
        ->where('InvoiceParticular.fk_unit_id', $data['arrInvoiceData']->fk_unit_id)
        ->where('InvoiceParticular.invoice_number', $data['arrInvoiceData']->invoice_number)
        ->where('InvoiceParticular.bill_type', $data['bill_type'])
        ->orderBy('InvoiceParticular.created_date', 'DESC');

        $resultset = $queryBuilder->get();
        return $resultset;
    }

    public function transactionLedgerEntry($data = array())
    {
        $arrResponse = array();
        if(empty(trim($data['from_ledger_id'])) || empty(trim($data['to_ledger_id'])))
        {
            $this->status = 'error';
            $this->message = 'Ledger Account Id is required.';
            $this->statusCode = 400;
            return;
        }
        if(empty($data['transaction_date']))
        {
            $data['transaction_date'] = date('Y-m-d');
        }

        $transaction_id = $this->executeTransactionEntry($data); 
        if($transaction_id){
            $arrResponse = array('success'=>true, 'transaction_id'=>$transaction_id);
        }else{
            $arrResponse = array('error'=>true);
        }
        
        return $arrResponse;
    }

    public function executeTransactionEntry($data)
    {
        $vouchers_array = [];
        if ((strpos($data['voucher_type'], '_') !== false) && (strpos($data['voucher_type'], '_') > 0)) {
            $vouchers_array = explode('_', $data['voucher_type']);
            $data['voucher_type'] = $vouchers_array[0];
        }

        $data['is_opening'] = 0;
        $data['transaction_from_id'] = '';
        $data['transaction_from_id'] = $this->_addTransactionEntry($data);

        if (!empty($data['transaction_from_id'])) {
            if (count($vouchers_array) >= 2) {
                $data['voucher_type'] = $vouchers_array[1];
            }
            if ($this->_addTransactionEntry($data)) {
                return $data['transaction_from_id'];
            } else {
                return false;
            }
        } else {
            return false;
        }
    }

    private function _addTransactionEntry($data)
    {
        $txn = new ChsoneLedgerTransaction();
        if (!empty($data['transaction_from_id'])) {
            $mode = "to";
            $data['transaction_type'] = "cr";
            $ledger_id = $data['to_ledger_id'];
            $ledger_name = $data['to_ledger_name'];
        } else {
            $mode = "from";
            $data['transaction_type'] = "dr";
            $ledger_id = $data['from_ledger_id'];
            $ledger_name = $data['from_ledger_name'];
        }
        //echo "s_opning".$is_opning;exit;
        if (!empty($data['is_opening']) && $data['is_opening'] == 1) {
            $txn_entrt = ChsoneLedgerTransaction::where('soc_id', $data['auth']['soc_id'])
                ->where('is_opening_balance', 1)
                ->where('ledger_account_id', $ledger_id)
                ->first();

            $txn->txn_id = (isset($txn_entrt->txn_id)  && !empty($txn_entrt->txn_id)) ? $txn_entrt->txn_id : '';
        }
        $txn->soc_id = $data['auth']['soc_id'];
        $txn->transaction_date = $data['transaction_date'];
        $txn->ledger_account_id = $ledger_id;
        $txn->ledger_account_name = $ledger_name;
        $txn->voucher_type = $data['voucher_type'];
        $txn->transaction_type = $data['transaction_type']; //$ledger_name_obj->nature_of_account;//$txn_type;
        $txn->payment_mode = $data['mode_of_payment'];
        $txn->payment_reference = $data['payment_reference'];
        $txn->transaction_amount = $data['transaction_amount'];
        $txn->other_reference_id = $data['other_payment_ref'];
        $txn->txn_from_id = $data['transaction_from_id'];
        $txn->memo_desc = $data['narration'];
        $txn->is_opening_balance = $data['is_opening'];
        $txn->is_reconciled = (!empty($data['is_reconciled'])) ? $data['is_reconciled'] : 0;;
        $txn->created_by = (!empty($data['auth']['user_id'])) ? $data['auth']['user_id'] : 0;
        $txn->added_on = date("Y-m-d H:i:s");
        $txn->voucher_reference_number = (!empty($data['voucher_reference_number'])) ? $data['voucher_reference_number'] : '';
        $txn->voucher_reference_id = (!empty($data['voucher_reference_id'])) ? $data['voucher_reference_id'] : '';
        $txn->is_cancelled = (!empty($data['is_cancelled'])) ? 1 : 0;

        if ($txn->save()) {
            return $txn->txn_id;
        } else {
            return false;
        }
    }

    public function getAppliedTaxDetail($data = array())
    {
        $arrTaxClass = array();
        $condition = ChsoneTaxLog::where('soc_id', $data['soc_id'])
            ->where('status', '!=', 'cancelled')
            ->whereIn('invoice_number', [$data['invoice_number']]);

        if (isset($data['showCancelledInvoice']) && !empty($data['showCancelledInvoice'])) {
            $condition = ChsoneTaxLog::where('soc_id', $data['soc_id'])
                ->whereIn('invoice_number', [$data['invoice_number']]);
        }

        $objTaxClass = $condition->get();

        if (!$objTaxClass->isEmpty()) {
            $arrTaxClass = $objTaxClass->toArray();
        }
        return $arrTaxClass;
    }

    public function getTaxLedgerDetailByTaxLog($data = array()) {
        $arrTaxLedgerName = array();
        if (!empty($data['arrTaxLogDetail']) && !empty($data['arrTaxLogDetail'])) {
            foreach ($data['arrTaxLogDetail'] as $eachTaxLogDetail) {
                $ledgerName = $this->getTaxLedgerName(array('taxClassDetail' => $eachTaxLogDetail));
                //if key already exist then dont include in array
                if (!in_array($ledgerName, $arrTaxLedgerName)) {
                    array_push($arrTaxLedgerName, $ledgerName);
                }
            }
        }
        return $arrTaxLedgerName;
    }

    public function getTaxLedgerName($data = array()) {
        $ledgerName = '';
        if (!empty($data['taxClassDetail'])) {
            if (!empty($data['taxClassDetail']['tax_rule'])) {
                $ledgerName .= trim(str_replace(' ', '', ucwords($data['taxClassDetail']['tax_rule'])));
            }
            if (isset($data['taxClassDetail']['tax_rate'])) {
                // $ledgerName .= trim(bcdiv($data['taxClassDetail']['tax_rate'], 1, 2)) . ucfirst(trim($data['taxClassDetail']['tax_rate_type']));
                $taxRate = (float) $data['taxClassDetail']['tax_rate'];
                $taxRateType = ucfirst(trim($data['taxClassDetail']['tax_rate_type']));

                $ledgerName .= trim(number_format($taxRate / 1, 2)) . $taxRateType;
            }
        }
        return $ledgerName;
    }

    public function taxNewLedgerExit($data = array()) {
        $arrTaxLedgerDetail = array();
        $behaviour = "liability";
        $context = "membercontri";
        //Remove ledger name iof already listed
        if (isset($data['arrTaxLedgerDetail']) && is_array($data['arrTaxLedgerDetail']) && !empty($data['arrTaxLedgerDetail'])) {
            $arrTaxLedgerDetail = $data['arrTaxLedgerDetail'];
            foreach ($data['arrTaxLedgerName'] as $key => $eachLedgerName) {
                if (array_key_exists($eachLedgerName, $data['arrTaxLedgerDetail'])) {
                    unset($data['arrTaxLedgerName'][$key]);
                }
            }
        }
        //Run query only if you have ledger name
        if (isset($data['arrTaxLedgerName']) && count($data['arrTaxLedgerName']) > 0) {
            $ledger_name = implode('","', $data['arrTaxLedgerName']);
            if (!empty(trim($ledger_name))) {
                //Check whether ledger exist or not
                $objTaxLedgerDetail = ChsoneGrpLedgerTree::where('soc_id', $data['soc_id'])
                    ->where('entity_type', "ledger")
                    ->whereIn('ledger_account_name', (array) $ledger_name)
                    ->where('behaviour', $behaviour)
                    ->get();
                $objTaxLedgerDetail = json_decode(json_encode($objTaxLedgerDetail), true);
                if (!empty($objTaxLedgerDetail)) {
                    $arrTaxLedger = $objTaxLedgerDetail->toArray();
                    foreach ($arrTaxLedger as $eachTaxDetail) {
                        if (in_array($eachTaxDetail['ledger_account_name'], $data['arrTaxLedgerName'])) {
                            $arrTaxLedgerDetail[$eachTaxDetail['ledger_account_name']] = $eachTaxDetail;
                        }
                    }
                }
            }
        }
        return $arrTaxLedgerDetail;
    }

    public function updateTaxLogStatus($data = array())
    {
        $arrResponse['status'] = 'error';
        $data['updated_date'] = date('Y-m-d H:i:s');
        $updateResponse = $this->updateTaxLogSts($data);
        if (!empty($updateResponse) && strtolower($updateResponse['status']) == 'success') {
            $arrResponse['status'] = 'success';
        }
        return $arrResponse;
    }

    public function updateTaxLogSts($data, $full_list = false)
    {
        $arrResponse = ['status' => 'success', 'message' => []];

        if (!empty($data['status'])) {
            $affectedRows = ChsoneTaxLog::where('soc_id', $data['soc_id'])
                ->where('invoice_number', $data['invoice_number'])
                ->update(['status' => $data['status']]);

            if ($affectedRows === 0) {
                $arrResponse = ['status' => 'error', 'message' => ['No rows affected']];
            }
        } else {
            $arrResponse = ['status' => 'error', 'message' => ['No status provided']];
        }

        return $arrResponse;
    }

    public function updateReversedPaymentLedgerTransactionStatus($data,$full_list = false)
    {
        // Updating a single column
        if(!empty($data['soc_id']) && !empty($data['voucher_type']) && !empty($data['voucher_reference_number']) && !empty($data['voucher_reference_id']))
        {
            $updatedFields = [];

            if (isset($data['is_cancelled'])) {
                $updatedFields['is_cancelled'] = $data['is_cancelled'];
            }

            if (!empty($updatedFields)) {
                $result = ChsoneLedgerTransaction::where('soc_id', $data['soc_id'])
                    ->where('voucher_type', $data['voucher_type'])
                    ->where('voucher_reference_number', $data['voucher_reference_number'])
                    ->where('voucher_reference_id', $data['voucher_reference_id'])
                    ->update($updatedFields);
            }
            if ($result === 0) {
                $arrResponse = array('status'=>'error');
            } else {
                $arrResponse = array('status'=>'success');
            }
        }

        return $arrResponse;
    }

    public function getAllInvoiceNumber($data = array())
    {
        $arrUnitsInvoice = IncomeUnitInvoice::where('soc_id', $data['soc_id'])
            ->where('fk_unit_id', $data['unit_id'])
            ->where('status', '!=', 'cancelled')
            ->orderBy('invoice_number', 'desc')
            ->get()
            ->toArray();

        return $arrUnitsInvoice;
    }

    public function getPendingOutstanding($data = array()) {

        $arrUnitsInvoiceOutstanding = [];

        $objUnitsInvoiceOutstanding = IncomeInvoiceAdjustment::where('soc_id', $data['soc_id'])
            ->where('fk_unit_id', $data['unit_id'])
            ->where('bill_type', $data['bill_type'])
            ->first();

        if ($objUnitsInvoiceOutstanding) {
            $arrUnitsInvoiceOutstanding = $objUnitsInvoiceOutstanding->toArray();
        }

        return $arrUnitsInvoiceOutstanding;
    }

    public function updateInvoiceAdjustment($data = array())
    {
        $arrResponse = array(
            'status' => 'error'
        );
        // print_r($data);//exit;
        if (!empty($data['soc_id']) && !empty($data['unit_id'])) {
            $objIncomeInvoiceAdjustment = IncomeInvoiceAdjustment::where('soc_id', $data['soc_id'])
            ->where('fk_unit_id', $data['unit_id'])
            ->where('bill_type', $data['bill_type'])
            ->first();

            if (empty($objIncomeInvoiceAdjustment)) {
                $objIncomeInvoiceAdjustment = new IncomeInvoiceAdjustment();
                $objIncomeInvoiceAdjustment->created_date = $this->getCurrentDate('database');
                $objIncomeInvoiceAdjustment->created_by = 0;
            }
            $objIncomeInvoiceAdjustment->soc_id = $data['soc_id'];
            $objIncomeInvoiceAdjustment->fk_unit_id = $data['unit_id'];
            if (!empty($objIncomeInvoiceAdjustment->adjustment_type) && strtolower($objIncomeInvoiceAdjustment->adjustment_type) == 'advance') {
                $objIncomeInvoiceAdjustment->principal_amount += $data['principal_amount'];
            } else {
                $objIncomeInvoiceAdjustment->principal_amount = $data['principal_amount'];
            }
            $objIncomeInvoiceAdjustment->adjustment_type = $data['adjustment_type'];
            $objIncomeInvoiceAdjustment->interest_amount = $data['interest_amount'];
            $objIncomeInvoiceAdjustment->bill_type = $data['bill_type'];
            $objIncomeInvoiceAdjustment->is_generated = isset($data['is_generated']) ? $data['is_generated'] : 1;
            $objIncomeInvoiceAdjustment->updated_date = $this->getCurrentDate('database');
            $objIncomeInvoiceAdjustment->updated_by = 0;

            if (!$objIncomeInvoiceAdjustment->save()) {
                $arrMessages = [];
                $singleMsg = '';

                foreach ($objIncomeInvoiceAdjustment->getMessages() as $messages) {
                    $arrMessages[] = (string) $messages;
                    $singleMsg .= (string) $messages . '|';
                }
                $arrResponse['message'] = $singleMsg;
            } else {
                $arrResponse['status'] = 'success';
            }
        }
        return $arrResponse;
    }

    public function getCreditDrByInvoiceNumber($data = array())
    {
        $arrCreditAccountDr = [];
        $condition = [];

        if ($data['use_credit']) {
            $condition['use_credit'] = $data['use_credit'];
            $condition['use_credit_for'] = $data['use_credit_for'];
        }

        $condition['soc_id'] = $data['soc_id'];
        $condition['account_id'] = $data['unit_id'];
        $condition['transaction_type'] = 'dr';
        $condition['invoice_number'] = $data['invoice_number'];

        $objCreditAccountDr = ChsoneCreditAccount::where($condition)->first();

        if ($objCreditAccountDr) {
            $arrCreditAccountDr = $objCreditAccountDr->toArray();
        }

        return $arrCreditAccountDr;
    }

    public function getPrimaryMemberByUnitId($data = array())
    {
        $resultset = $this->tenantDB()->table('chsone_member_master as memberMaster')
        ->select('memberMaster.id',
            'memberMaster.member_first_name',
            'memberMaster.member_last_name')
        ->join('chsone_units_master as units', 'units.unit_id', '=', 'memberMaster.fk_unit_id')
        ->join('chsone_member_type_master as memberType', 'memberType.member_type_id', '=', 'memberMaster.member_type_id')
        ->where('memberMaster.soc_id', $data['soc_id'])
        ->where('memberType.member_type_name', 'Primary')
        ->where('memberMaster.status', 1)
        ->where('units.is_allotted', 1)
        ->where('units.unit_id', $data['unit_id'])
        ->get();
        
        // return $resultset->toArray(); // If you need it as an array
        return $resultset;
    }

    public function saveCreditAccountResponse($data = array())
    {
        $response = ['error' => false, 'data' => null];

        try {
            if (empty($data) || (!isset($data['auth']))) {
                $this->status = 'error';
                $this->message = 'Required session values in auth';
                $this->statusCode = 400;
                return;
            }
            $user = $data['user'];
            $id = $data['id'];
            $username = (!empty($data['username']) ? $data['username'] : null);

            $data = $data['data'];
            $data['username'] = $username;
            $data['user'] = $user;

            if ($id) {
                $cr = ChsoneCreditAccount::where('credit_account_id', $id)
                    ->where('soc_id', $data['soc_id'])
                    ->first();

                $cr->updated_by = $user;
                $cr->updated_date = date('Y-m-d H:i:s');
                $cr->use_credit_after = (!empty($data['adjustable_date']) ? $data['adjustable_date'] : null);
                $cr->is_locked = 0;
                $cr->use_credit_for = (!empty($data['used_for']) ? $data['used_for'] : null);

                $cr->is_invoice_rectification = ((isset($data['is_invoice_rectification']) && !empty($data['is_invoice_rectification'])) ? $data['is_invoice_rectification'] : null);
                $cr->income_account_id = ((isset($data['income_account_ledger_id']) && !empty($data['income_account_ledger_id'])) ? $data['income_account_ledger_id'] : null);
                $cr->narration = $data['narration'];
                $cr->use_credit = (isset($data['credit_used_type']) && !empty($data['credit_used_type']) ? $data['credit_used_type'] : 'adjustable');
                // Save data
                if ($cr->save()) {
                    $response['data'] = $cr->credit_account_id;
                } else {
                    $response['error'] = true;
                    $response['data'] = ['message' => $cr->getErrors()];
                }
            } else {
                if ($data['credit_used_type'] == 'both') {
                    $data['credit_used_type'] = 'adjustable';
                    $data['payment_amount'] = $data['adjustable_amount'];
                    $saveResponse = $this->saveCreditNote($data);
                    if ($saveResponse['error'] == false) {
                        $data['credit_used_type'] = 'refundable';
                        $data['payment_amount'] = $data['refundable_amount'];
                        $data['used_for'] = NULL;
                        $data['adjustable_date'] = NULL;

                        $saveResponse = $this->saveCreditNote($data);
                        if ($saveResponse['error'] == false) {
                            $response['data'] = $saveResponse['id'];
                        }
                    }
                } else {
                    $saveResponse = $this->saveCreditNote($data);
                    if ($saveResponse['error'] == false) {
                        $response['data'] = $saveResponse['id'];
                    }
                }
            }
        } catch (Exception $e) {
            $response['error'] = true;
            $response['data'] = ['message' => $e->getMessage()];
        } finally {
            if (empty($response['data'])) {
                $response['data'] = ['message' => 'working is ok'];
                $response['error'] = false;
            }
            return response()->json($response);
        }
    }

    public function saveCreditNote($data)
    {
        $cr = new ChsoneCreditAccount();
        $cr->created_by = $data['user'] ?? 0;
        $cr->created_date = date('Y-m-d H:i:s');
        $cr->soc_id = $data['soc_id'];
        $cr->invoice_number = $data['invoice_no'] ?? $data['invoice_number'] ?? null;
        $cr->is_invoice_rectification = $data['is_invoice_rectification'] ?? null;
        $cr->income_account_id = $data['income_account_ledger_id'] ?? null;
        $cr->payment_tracker_id = $data['payment_tracker_id'] ?? null;
        $cr->account_id = $data['account_id'];
        $cr->account_name = $data['account_name'];
        $cr->account_context = $data['account_context'];
        $cr->amount = $data['payment_amount'];
        $cr->payment_mode = $data['payment_mode'] ?? null;
        $cr->payment_date = isset($data['payment_date']) ? $this->getDatabaseDate($data['payment_date']) : null;
        $cr->transaction_type = $data['transaction_type'];
        $cr->narration = $data['narration'];
        $cr->use_credit = $data['credit_used_type'] ?? 'adjustable';
        $cr->use_credit_after = $data['adjustable_date'] ?? null;
        $cr->is_locked = $data['is_locked'] ?? 0;
        $cr->use_credit_for = $data['used_for'] ?? null;
        $cr->reference_no = $data['transaction_reference'] ?? null;
        $cr->context = $data['context'] ?? 'system';
        $cr->created_name = $data['username'] ?? null;
        // Save data
        if ($cr->save()) {
            $data['error'] = false;
            $data['id'] = $cr->credit_account_id;
            return $data;
        } else {
            $data['error'] = true;
            $data['message'] = array('message' => $cr->getMessages());
            return $data;
        }
    }

    public function updateUnitIncomeInvoice($data = array())
    {
        $arrResponse = [
            'status' => 'error'
        ];
    
        $objUnitsInvoice = IncomeUnitInvoice::where('soc_id', $data['soc_id'])
            ->where('invoice_number', $data['invoice_number'])
            ->first();
    
        if ($objUnitsInvoice) {
            $objUnitsInvoice->status = $data['status'];
            $objUnitsInvoice->updated_by = $data['updated_by'];
            $objUnitsInvoice->cancel_date = $data['cancel_date'];
            $objUnitsInvoice->updated_date = now();
    
            if (!$objUnitsInvoice->save()) {
                $arrMessages = $objUnitsInvoice->getErrors();
                $arrResponse['message'] = $arrMessages;
            } else {
                $arrResponse = [
                    'status' => 'success'
                ];
            }
        }
    
        return $arrResponse;
    }
    
}
