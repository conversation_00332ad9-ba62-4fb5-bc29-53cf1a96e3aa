<?php

namespace App\Console\Commands\Actions\Income;

use Illuminate\Console\Command;
use App\Console\Commands\Action;
use App\Models\Tenants\IncomeNonmemberBill;
use App\Models\Tenants\ChsoneNonmemberMaster;
use App\Models\Tenants\ChsoneGrpLedgerTree;
use App\Models\Tenants\SocAccountFinancialYearMaster;
use App\Models\Tenants\IncomeInvoiceGeneralSetting;
use App\Models\Tenants\IncomeUnitInvoice;
use App\Models\Tenants\ChsoneTaxLog;
use App\Models\Tenants\IncomeAccount;
use App\Models\Tenants\ChsoneTaxCategory;

class generateNonMemberInvoiceDataSource extends Action
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'datasource:generateNonMemberInvoice {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Generate a new non-member income invoice';

    /**
     * Execute the console command.
     */
    public function apply()
    {
        $data = $this->input;
        $data['nonmember_id'] = $this->input['booker_name'];
        $data['booking_charge'] = $this->input['total_due_amount'];
        //query to fetch first name and last name from booker_name from chsone_nonmember_master table
        $nonmemberMaster = ChsoneNonmemberMaster::where('nonmember_id', $data['nonmember_id'])->first();
        $data['booker_name'] = $nonmemberMaster->first_name . ' ' . $nonmemberMaster->last_name;

        // need to check if nonmember_id exist in input data
        // if not exist then need to create new record in chsone_nonmember_master table
        // if exist then directly insert the record in income_nonmember_bill table
        if (empty($data['nonmember_id'])) {
            $createNonmemberMaster = $this->createNonmemberMaster($data);
            if ($createNonmemberMaster) {

                if (isset($data['nonmem_ledger']) && $data['nonmem_ledger'] == 1) {
                    $ledgerName = $data['booker_name'];
                    $context = 'sundrydebtors';
                    $arrBookerLedgerDetails = $this->createNonmemberLedger($data, $ledgerName, $context);
                    $data['nonmember_ledger_id'] = $arrBookerLedgerDetails->ledger_account_id;
                    $data['is_default_ledger'] = 0;
                    // update chsone_nonmember_master table with nonmember_ledger_id and is_default_ledger
                    $updateNonmemberMaster = ChsoneNonmemberMaster::where('nonmember_id', $createNonmemberMaster->nonmember_id)->update(['nonmember_ledger_id' => $data['nonmember_ledger_id'], 'is_default_ledger' => $data['is_default_ledger']]);
                } else {
                    $ledgerName = 'Nonmember Income';
                    $context = 'sundrydebtors';
                    // fetch ledger_account_id from chsone_grp_ledger_tree table whose ledger_account_name is 'Nonmember Income' and context is 'sundrydebtors'
                    $arrBookerLedgerDetails = ChsoneGrpLedgerTree::where('soc_id', $data['company_id'])->where('ledger_account_name', $ledgerName)->where('context', $context)->first();
                    // $arrBookerLedgerDetails = $this->createNonmemberLedger($data, $ledgerName, $context);
                    $data['nonmember_ledger_id'] = $arrBookerLedgerDetails->ledger_account_id;
                    $data['is_default_ledger'] = 1;
                    // update chsone_nonmember_master table with nonmember_ledger_id and is_default_ledger
                    $updateNonmemberMaster = ChsoneNonmemberMaster::where('nonmember_id', $createNonmemberMaster->nonmember_id)->update(['nonmember_ledger_id' => $data['nonmember_ledger_id'], 'is_default_ledger' => $data['is_default_ledger']]);
                }

                // now insert the record in income_nonmember_bill table
                $data['nonmember_id'] = $createNonmemberMaster->nonmember_id;
                $data['booker_email_address'] = $createNonmemberMaster->email_id;
                $data['booker_mobile_number'] = $createNonmemberMaster->mobile_number;
                $data['booker_name'] = $createNonmemberMaster->first_name . ' ' . $createNonmemberMaster->last_name;
                $data['hsn'] = $createNonmemberMaster->hsn;
                $data['gstin'] = $createNonmemberMaster->gstin;

                $newNonmemberBill = $this->createNonmemberBill($data);

                if ($newNonmemberBill) {

                    // need to insert the record in income_unit_invoices table
                    $insertUnitInvoice = $this->insertUnitInvoice($data, $newNonmemberBill->bill_number);

                    if ($insertUnitInvoice) {
                        // check tax_class_id is set or not
                        if(isset($data['tax_class_id']) && !empty($data['tax_class_id']) && $data['tax_class_id'] == 0) {
                            // insert the record in chsone_tax_log table
                            $insertTaxLog = $this->insertTaxLog($data, $newNonmemberBill->bill_number);

                            if (!$insertTaxLog) {
                                $this->message = "Unable to insert record in chsone_tax_log table";
                                $this->status = 'error';
                                $this->statusCode = 400;
                            }
                        }
                    } else {
                        $this->message = "Unable to insert record in income_unit_invoices table";
                        $this->status = 'error';
                        $this->statusCode = 400;
                    }

                    // need to insert the record in chsone_ledger_transactions table
                    $chsone_ledger_transactions = $this->insertLedgerTransactions($newNonmemberBill);

                    if(!$chsone_ledger_transactions) {
                        $this->message = "Unable to insert record in chsone_ledger_transactions table";
                        $this->status = 'error';
                        $this->statusCode = 400;
                    }

                    $this->message = "Non member invoice created successfully";
                    $this->status = 'success';
                    $this->statusCode = 200;
                    $this->data = $newNonmemberBill->toArray();
                } else {
                    $this->message = "Unable to create non member invoice";
                    $this->status = 'error';
                    $this->statusCode = 400;
                }
            } else {
                $this->message = "Unable to create non member invoice";
                $this->status = 'error';
                $this->statusCode = 400;
                $this->data = [];
            }
        } else {
            $newNonmemberBill = $this->createNonmemberBill($data);

            if ($newNonmemberBill) {

                // need to insert the record in income_unit_invoices table
                $insertUnitInvoice = $this->insertUnitInvoice($data, $newNonmemberBill->bill_number);

                if ($insertUnitInvoice) {
                    // check tac_class_id is set or not
                    if(isset($data['tax_class_id']) && !empty($data['tax_class_id']) && $data['tax_class_id'] == 0) {
                        // insert the record in chsone_tax_log table
                        $insertTaxLog = $this->insertTaxLog($data, $newNonmemberBill->bill_number);

                        if (!$insertTaxLog) {
                            $this->message = "Unable to insert record in chsone_tax_log table";
                            $this->status = 'error';
                            $this->statusCode = 400;
                        }
                    }
                } else {
                    $this->message = "Unable to insert record in income_unit_invoices table";
                    $this->status = 'error';
                    $this->statusCode = 400;
                }

                // need to insert the record in chsone_ledger_transactions table
                $chsone_ledger_transactions = $this->insertLedgerTransactions($newNonmemberBill);

                if(!$chsone_ledger_transactions) {
                    $this->message = "Unable to insert record in chsone_ledger_transactions table";
                    $this->status = 'error';
                    $this->statusCode = 400;
                }

                $this->message = "Non member invoice created successfully";
                $this->status = 'success';
                $this->statusCode = 200;
                $this->data = $newNonmemberBill->toArray();
            } else {
                $this->message = "Unable to create non member invoice";
                $this->status = 'error';
                $this->statusCode = 400;
            }
        }
    }

    /**
     * Insert the record in chsone_nonmember_master table
     */
    public function createNonmemberMaster($data)
    {
        $nonmemberMaster = new ChsoneNonmemberMaster;
        $nonmemberMaster->soc_id=$data['company_id'] ?? '';
        $nonmemberMaster->nonmember_ledger_id=$data['nonmember_ledger_id'] ?? '';
        $nonmemberMaster->is_default_ledger=$data['is_default_ledger'] ?? '0';
        // split the booker_name into first_name and last_name
        $bookerName = explode(' ', $data['booker_name']);
        $nonmemberMaster->first_name=$bookerName[0] ?? '';
        $nonmemberMaster->last_name=$bookerName[1] ?? '';
        $nonmemberMaster->email_id=$data['booker_email_address'] ?? '';
        $nonmemberMaster->mobile_number=$data['booker_mobile_number'] ?? '';
        $nonmemberMaster->gstin=$data['gstin'] ?? '';
        $nonmemberMaster->hsn=$data['hsn'] ?? '';
        $nonmemberMaster->created_date=date("Y-m-d");
        $nonmemberMaster->created_by=$data['user_id'] ?? '';
        $nonmemberMaster->updated_date=date("Y-m-d");
        $nonmemberMaster->updated_by=$data['user_id'] ?? '';

        $nonmemberMaster->save();

        if($nonmemberMaster->save()) {
            return $nonmemberMaster;
        } else {
            return false;
        }
    }

    /**
     * Insert the record in income_nonmember_bill table
     */
    public function createNonmemberBill($data)
    {
         // Create new invoice number
         $newInvoiceNumber = $this->generate_invoice_id($data);

        $nonmemberBill = new IncomeNonmemberBill;
        $nonmemberBill->soc_id=$data['company_id'] ?? '';
        $nonmemberBill->nonmember_id=$data['nonmember_id'] ?? '';
        $nonmemberBill->billed_name=$data['booker_name'] ?? '';
        $nonmemberBill->booker_email_address=$data['booker_email_address'] ?? '';
        $nonmemberBill->booker_mobile_number=$data['booker_mobile_number'] ?? '';
        $nonmemberBill->fk_income_account_id=$data['nonmemberincomeaccount'] ?? '';
        $nonmemberBill->bill_number=$newInvoiceNumber ?? '';
        $nonmemberBill->bill_for=$data['narration'] ?? '';
        $nonmemberBill->bill_amount=$data['booking_charge'] ?? '0.000';
        $nonmemberBill->advance_amount=$data['advance_amount'] ?? '0.000';
        $nonmemberBill->total_taxes=$data['tax_amount'] ?? '0.000';
        $nonmemberBill->tax_class_id=$data['tax_class_id'] ?? '0';
        $nonmemberBill->total_deduction=$data['total_deduction'] ?? '0.000';
        $nonmemberBill->discount_amount=$data['discount_amount'] ?? '0.000';
        $nonmemberBill->due_amount=$data['due_amount'] ?? '0.000';
        $nonmemberBill->from_date=isset($data['from_date']) ? date('Y-m-d', strtotime($data['from_date'])) : '';
        $nonmemberBill->end_date=isset($data['end_date']) ? date('Y-m-d', strtotime($data['end_date'])) : '';
        $nonmemberBill->bill_status='unapproved';
        $nonmemberBill->payment_status=$data['payment_status'] ?? 'unpaid';
        $nonmemberBill->bill_from='nonmember';
        $nonmemberBill->bill_date=isset($data['bill_date']) ? date('Y-m-d', strtotime($data['bill_date'])) : '';
        $nonmemberBill->created_date=date("Y-m-d");
        $nonmemberBill->created_by=$data['user_id'] ?? '';
        $nonmemberBill->updated_date=date("Y-m-d");
        $nonmemberBill->updated_by=$data['user_id'] ?? '';
        $nonmemberBill->booker_address=$data['booker_address'] ?? '';
        $nonmemberBill->hsn=$data['hsn'] ?? '';
        $nonmemberBill->gstin=$data['gstin'] ?? '';

        $nonmemberBill->save();

        if($nonmemberBill->save()) {
            return $nonmemberBill;
        } else {
            return false;
        }
    }

    /**
     * Create nonmember ledger
     */
    public function createNonmemberLedger($data, $ledgerName, $context)
    {
        // fecth current financial year from soc_account_financial_year_master table
        $socAccountFinancialYearMaster = SocAccountFinancialYearMaster::where('soc_id', $data['company_id'])->where('closed', 0)->where('confirmed', 0)->first();
        $fy_start_date = $socAccountFinancialYearMaster->fy_start_date;

        // firstly fetch parent_id from chsone_grp_ledger_tree table whose ledger_account_name is 'Sundry Debtors'
        $parentLedger = ChsoneGrpLedgerTree::where('soc_id', $data['company_id'])->where('ledger_account_name', 'Sundry Debtors')->first();
        if (!empty($parentLedger)) {
            $parentLedgerId = $parentLedger->ledger_account_id;
        } else {
            $parentLedgerId = 0;
        }

        $chsoneGrpLedgerTree = new ChsoneGrpLedgerTree;
        $chsoneGrpLedgerTree->soc_id=$data['company_id'] ?? '';
        $chsoneGrpLedgerTree->ledger_account_name=$ledgerName ?? '';
        $chsoneGrpLedgerTree->nature_of_account='dr';
        $chsoneGrpLedgerTree->parent_id=$parentLedgerId ?? '';
        $chsoneGrpLedgerTree->report_head='Balance Sheet';
        $chsoneGrpLedgerTree->context_ref_id=$data['context_ref_id'] ?? '';
        $chsoneGrpLedgerTree->context=$context ?? '';
        $chsoneGrpLedgerTree->ledger_start_date=$fy_start_date ?? '';
        $chsoneGrpLedgerTree->added_on=date("Y-m-d H:i:s");
        $chsoneGrpLedgerTree->status='1';
        $chsoneGrpLedgerTree->created_by=$data['user_id'] ?? '';
        $chsoneGrpLedgerTree->entity_type='ledger';
        $chsoneGrpLedgerTree->behaviour='asset';
        $chsoneGrpLedgerTree->defined_by='user';

        $chsoneGrpLedgerTree->save();

        if($chsoneGrpLedgerTree->save()) {
            return $chsoneGrpLedgerTree;
        } else {
            return false;
        }
    }

    /**
     * Generate new invoice number
     */
    public function generate_invoice_id($data) 
    {
        $settingDetails = IncomeInvoiceGeneralSetting::where('soc_id', $data['company_id'])->where('setting_key', 'INCOME_NONMEMBER_INVOICE_PREFIX')->first();

        // get last invoice number
        $last_invoice_number = $this->tenantDB()->table('income_nonmember_bills')->orderBy('nonmember_bill_id', 'desc')->first();
        if($last_invoice_number && $last_invoice_number->bill_number) {
            
            $numericPart = (int)preg_replace('/\D/', '', $last_invoice_number->bill_number);
            // Increment the numeric part
            $newNumericPart = $numericPart + 1;
            // Format the new invoice number
            $newinvoiceNumber = $settingDetails->setting_value . str_pad($newNumericPart, 5, '0', STR_PAD_LEFT);
        } else {
            $newinvoiceNumber = $settingDetails->setting_value . '00001';
        }

        return $newinvoiceNumber;
    }

    /**
     * Insert the record in income_unit_invoices table
     */
    public function insertUnitInvoice($data, $bill_number) {
        $unitInvoice = new IncomeUnitInvoice;
        $unitInvoice->soc_id=$data['company_id'] ?? '';
        $unitInvoice->invoice_number=$bill_number ?? '';
        $unitInvoice->fk_unit_id=$data['unit_id'] ?? '0';
        $unitInvoice->fk_unit_ledger_id=$data['fk_unit_ledger_id'] ?? '0';
        $unitInvoice->soc_building_id=$data['soc_building_id'] ?? '0';
        $unitInvoice->soc_building_floor=$data['soc_building_floor'] ?? '0';
        // convert from date in the format of Y-m-d
        $from_date = date('Y-m-d', strtotime($data['from_date']));
        $unitInvoice->from_date=$from_date ?? '';
        // convert from date in the format of Y-m-d
        $to_date = date('Y-m-d', strtotime($data['end_date']));
        $unitInvoice->to_date=$to_date ?? '';
        $unitInvoice->status='generated';
        $unitInvoice->payment_status='unpaid';
        $unitInvoice->principal_amount='0.000';
        $unitInvoice->interest_amount='0.000';
        $unitInvoice->advance_amount='0.000';
        $unitInvoice->outstanding_principal='0.000';
        $unitInvoice->outstanding_interest='0.000';
        $unitInvoice->roundoff_amount='0.000';
        $unitInvoice->created_date=date("Y-m-d");
        $unitInvoice->created_by=$data['user_id'] ?? '';
        $unitInvoice->updated_date=date("Y-m-d");
        $unitInvoice->updated_by=$data['user_id'] ?? '';
        $unitInvoice->member_id=$data['member_id'] ?? '0';

        $unitInvoice->save();

        if($unitInvoice->save()) {
            return $unitInvoice;
        } else {
            return false;
        }
    }

    /**
     * Insert the record in chsone_tax_log table
     */
    public function insertTaxLog($data, $bill_number)
    {
        // first find out account_name from income_accounts table using nonmemberincomeaccount
        $incomeAccount = IncomeAccount::where('soc_id', $data['company_id'])->where('account_id', $data['nonmemberincomeaccount'])->first();
        $accountName = $incomeAccount->account_name;

        // if tax_class_id is not empty then fetch tax_rate from chsone_tax_categories table
        if (!empty($data['tax_class_id'])) {
            $taxCategory = ChsoneTaxCategory::where('soc_id', $data['company_id'])->where('tax_class_id', $data['tax_class_id'])->first();
            $taxCategory = $taxCategory->toArray();
            // create a for loop for taxCategory and insert the record in chsone_tax_log table
            foreach ($taxCategory as $key => $value) {
                $taxLog = new ChsoneTaxLog;
                $taxLog->soc_id=$data['company_id'] ?? '';
                $taxLog->invoice_number=$bill_number ?? '';
                $taxLog->particular=$accountName ?? '';
                $taxLog->particular_id=$data['nonmemberincomeaccount'] ?? '';
                $taxLog->particular_amount=$data['booking_charge'] ?? '0.000';
                $taxLog->tax_class='GST';
                $taxLog->tax_class_id=$data['tax_class_id'] ?? '';
                $taxLog->tax_rule=$taxCategory[$key]['tax_categories_name'] ?? '';
                $taxLog->tax_rule_id=$taxCategory[$key]['tax_categories_id'] ?? '';
                $taxLog->tax_rate=$taxCategory[$key]['tax_categories_amount'] ?? '0.000';
                $taxLog->tax_rate_type=$taxCategory[$key]['tax_categories_type'] ?? '';
                // calculate tax amount using tax_rate in percentage
                $taxRate = $taxCategory[$key]['tax_categories_amount'] / 100;
                $taxLog->status='generated';
                $taxLog->created_date=date("Y-m-d");

                $taxLog->save();

                if($taxLog->save()) {
                    return $taxLog;
                } else {
                    return false;
                }
            }
        } else {
            $taxLog = new ChsoneTaxLog;
            $taxLog->soc_id=$data['company_id'] ?? '';
            $taxLog->invoice_number=$bill_number ?? '';
            $taxLog->particular=$accountName ?? '';
            $taxLog->particular_id=$data['nonmemberincomeaccount'] ?? '';
            $taxLog->particular_amount=$data['booking_charge'] ?? '0.000';
            $taxLog->tax_rate='0.000';
            $taxLog->tax_amount=$data['tax_amount'] ?? '0.000';
            $taxLog->status='generated';
            $taxLog->created_date=date("Y-m-d");

            $taxLog->save();

            if($taxLog->save()) {
                return $taxLog;
            } else {
                return false;
            }
        }
    }

    /**
     * Insert the record in chsone_ledger_transactions table
     */
    public function insertLedgerTransactions($newNonmemberBill) {

        // fetch ledger_account_id from chsone_nonmember_master table whose nonmember_id is $newNonmemberBill->nonmember_id
        $nonmemberMaster = ChsoneNonmemberMaster::where('nonmember_id', $newNonmemberBill->nonmember_id)->first();
        $nonmemberLedgerId = $nonmemberMaster->nonmember_ledger_id;

        // fetch nature_of_account from chsone_grp_ledger_tree table whose ledger_account_id is $nonmemberLedgerId
        $chsone_grp_ledger_tree = ChsoneGrpLedgerTree::where('soc_id', $newNonmemberBill->soc_id)->where('ledger_account_id', $nonmemberLedgerId)->first();

        // fecth unit_invoice_id from income_unit_invoices table whose invoice_number is $newNonmemberBill->bill_number
        $unitInvoice = IncomeUnitInvoice::where('invoice_number', $newNonmemberBill->bill_number)->first();
        $unitInvoiceId = $unitInvoice->unit_invoice_id;

        // fetch ledger_account_id and income_account_name from income_accounts table whose account_id is $newNonmemberBill->fk_income_account_id
        $incomeAccount = IncomeAccount::where('account_id', $newNonmemberBill->fk_income_account_id)->first();
        $incomeAccountName = $incomeAccount->account_name;
        $incomeLedgerId = $incomeAccount->fk_income_ledger_id;
        // fecth nature of account from chsone_grp_ledger_tree table whose ledger_account_id is $incomeLedgerId
        $chsone_grp_ledger_tree_second = ChsoneGrpLedgerTree::where('soc_id', $newNonmemberBill->soc_id)->where('ledger_account_id', $incomeLedgerId)->first();

        $obj = $this->tenantDB()->table('chsone_ledger_transactions')
        ->insert([
            'soc_id' => $newNonmemberBill->soc_id,
            'transaction_date' => $newNonmemberBill->bill_date,
            'ledger_account_id' => $nonmemberLedgerId,
            'ledger_account_name' => $newNonmemberBill->billed_name,
            'voucher_type' => 'income',
            'voucher_reference_number' => $newNonmemberBill->bill_number,
            'voucher_reference_id' => $unitInvoiceId,
            'transaction_type' => $chsone_grp_ledger_tree['nature_of_account'],
            'transaction_amount' => $newNonmemberBill->bill_amount,
            'txn_from_id' => 0,
            'memo_desc' => $newNonmemberBill->bill_for.' charges against invoice '. $newNonmemberBill->bill_number .' dated '. $newNonmemberBill->bill_date . ' for ' . $newNonmemberBill->from_date . ' to ' . $newNonmemberBill->end_date,
            'is_opening_balance' => 0,
            'is_reconciled' => 0,
            'is_cancelled' => 0,
            'created_by' => $newNonmemberBill->created_by,
            'added_on' => date('Y-m-d H:i:s'),
        ]);

        $result = $this->tenantDB()->table('chsone_ledger_transactions')->orderBy('txn_id', 'desc')->first();

        $obj2 = $this->tenantDB()->table('chsone_ledger_transactions')
        ->insert([
            'soc_id' => $newNonmemberBill->soc_id,
            'transaction_date' => $newNonmemberBill->bill_date,
            'ledger_account_id' => $incomeLedgerId,
            'ledger_account_name' => $incomeAccountName,
            'voucher_type' => 'income',
            'voucher_reference_number' => $newNonmemberBill->bill_number,
            'voucher_reference_id' => $unitInvoiceId,
            'transaction_type' => $chsone_grp_ledger_tree_second['nature_of_account'],
            'transaction_amount' => $newNonmemberBill->bill_amount,
            'txn_from_id' => $result->txn_id,
            'memo_desc' => $newNonmemberBill->bill_for.' charges against invoice '. $newNonmemberBill->bill_number .' dated '. $newNonmemberBill->bill_date . ' for ' . $newNonmemberBill->from_date . ' to ' . $newNonmemberBill->end_date,
            'is_opening_balance' => 0,
            'is_reconciled' => 0,
            'is_cancelled' => 0,
            'created_by' => $newNonmemberBill->created_by,
            'added_on' => date('Y-m-d H:i:s'),
        ]);

        if($obj && $obj2) {
            return true;
        } else {
            return false;
        }
    }
}
