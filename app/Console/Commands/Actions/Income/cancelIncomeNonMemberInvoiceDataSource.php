<?php

namespace App\Console\Commands\Actions\Income;

use Illuminate\Console\Command;
use App\Console\Commands\Action;

class cancelIncomeNonMemberInvoiceDataSource extends Action
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'datasource:cancelIncomeNonMemberInvoice {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Cancel a non-member income invoice';

    /**
     * Execute the console command.
     */
    public function apply()
    {
        $invoice_number = $this->input['invoice_number'];

        // check invoice is exist or not if not exist then return error
        $invoice = $this->tenantDB()->table('income_nonmember_bills')
            ->where('bill_number', $invoice_number)
            ->get();

        if(count($invoice) == 0){
            $this->status = 'error';
            $this->message = 'Invoice not found';
            $this->statusCode = 400;
            $this->data = [];
            return;
        }
        
        $result = $this->tenantDB()->table('income_nonmember_bills')
        ->where('bill_number', $invoice_number)
        ->update([
            'payment_status' => 'cancelled'
        ]);
        
        if($result){
            $this->status = 'success';
            $this->message = 'Invoice has been cancelled successfully';
            $this->statusCode = 200;
            $this->data = [];
        }else{
            $this->status = 'error';
            $this->message = 'Invoice not deleted';
            $this->statusCode = 400;
            $this->data = [];
        }
    }
}
