<?php

namespace App\Console\Commands\Actions\Income;

use App\Console\Commands\Action;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Config;

class creditNoteAddDataSource extends Action
{
    protected $constants;

    public function __construct()
    {
        parent::__construct();
        $this->constants = Config::get("constants");
    }

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'datasource:creditNoteAdd {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Add a credit note to a member and non-member.';

    /**
     * Execute the console command.
     */

     //test change
    public function apply()
    {

        // Handle POST request
        try {
            $data = $this->input;

            $payment_amount = 0;
            $data['account_context'] = 'unit';
            foreach ($data['particular_data'] as $item) {
                $rectified_amount = number_format(round($item['rectified_amount'], 2), 2, '.', '');
                $rectified_value = number_format(round($item['rectified_amount'], 2), 2, '.', '');
                number_format(round($item['rectified_amount'], 2), 2, '.', '');
                if ($rectified_amount < $rectified_value) {
                    // $this->session->set("err_msg", 'Rectification amount should be less than particular amount!');
                    // return $this->response->redirect($this->config->system->full_base_url . "credit-accounts/creditNoteAdd");
                }
                $payment_amount += $item['rectified_amount'];
            }

            // $account_id = $data['account_id_member'] ?? $data['account_id_nonmember'] ?? null;
            // Prepare data for saving
            $data['soc_id'] = $this->input['company_id']; // Assuming user is authenticated
            $data['transaction_type'] = 'cr';
            $data['credit_used_type'] = 'adjustable';
            $data['is_invoice_rectification'] = 1;
            $data['payment_mode'] = '';
            $data['narration'] = 'Invoice Rectification -' . $data['narration'];
            $data['action'] = 'insert';
            $data['unit_id'] = $data['account_id_member'] ?? $data['account_id_nonmember'];
            $data['used_for'] = $data['bill_type'] ?? '';
            $data['payment_amount'] = $payment_amount;
            $data['is_invoice_rectification'] = 1;
            $data['save'] = 'save';
            $data['income_account_ledger_id'] = $data['income_account_ledger_id'] ?? 0;
            $data['rectified_name'] = $item['particular'];

            $creditAccountResponse = $this->saveCreditAccountResponse($data);

            // $memberData = $this->tenantDB()->table('chsone_members_master')->where('fk_unit_id', $data['account_id_member'] ?? $data['account_id_nonmember'])->first();
            // $data['user_name'] = $memberData->member_first_name . ' ' . $memberData->member_last_name;

            $data['user_name'] = $this->input['user']['first_name'] . ' ' . $this->input['user']['last_name'];

            $invoiceData = $this->tenantDB()->table('income_invoice_particular')->where('id', $item['id'])->first();
            // $data['user_name'] = $memberData->member_first_name
            $data['invoice_number'] = $invoiceData->invoice_number;
            $invoice_number = $data['invoice_number']; // save and remove invocie number coz it's cr and  while creating advance we dont insert invoice number table.
            $data['invoice_number'] = $invoice_number; // again assign invoice number for futher processing
            if (!$creditAccountResponse) {

                // $this->session->set("err_msg", 'Unable to complete transaction, Please try later.');
                $this->message = 'Unable to complete transaction, Please try later.';
                $this->status = 400;
            } else {
                $data['payment_tracker_id'] = $creditAccountResponse;
                $data['credit_account_id'] = $creditAccountResponse;

                if ($this->saveRectificationLedger($data)) {
                    $creditNoteRes = $this->saveCreditNoteResponse($data);


                    if ($creditNoteRes['error'] == true) {
                        $this->message = "Unable to complete transaction, Please try later.";
                        // $this->session->set("err_msg", $creditNoteRes['error_msg']);
                    } else {
                        $this->message = 'Credit note added successfully.';
                        // $this->session->set('succ_cr_add', 'Credit note added successfully.');
                    }
                    $this->message = 'Credit note added successfully.';
                    // $this->session->set('succ_cr_add', 'Credit note added successfully.');
                } else {
                    $this->message = 'Unable to create ledger for this transaction.';
                    // $this->session->set("err_msg", 'Unable to create ledger for this transaction.');
                }
            }
        }
        catch (\Exception $e) {
            $this->message = $e->getMessage();
            $this->status = 400;
        }
    }

    public function saveCreditAccountResponse($data = array())
    {
        $id = $data['id'] ?? null;

        if ($id) {
            // Prepare the data to be updated
            $updateData = [
                'updated_by' => $data['user_id'],
                'updated_date' => date('Y-m-d H:i:s'),
                'use_credit_after' => !empty($data['adjustable_date']) ? $data['adjustable_date'] : null,
                'is_locked' => 0,
                'use_credit_for' => !empty($data['used_for']) ? $data['used_for'] : null,
                'is_invoice_rectification' => isset($data['is_invoice_rectification']) && !empty($data['is_invoice_rectification']) ? $data['is_invoice_rectification'] : null,
                'income_account_id' => isset($data['income_account_ledger_id']) && !empty($data['income_account_ledger_id']) ? $data['income_account_ledger_id'] : null,
                'narration' => $data['narration'],
                'use_credit' => isset($data['credit_used_type']) && !empty($data['credit_used_type']) ? $data['credit_used_type'] : 'adjustable',
            ];

            // Update the record in the database
            $result = $this->tenantDB()
                ->table('chsone_credit_accounts')
                ->where('credit_account_id', $id)
                ->where('soc_id', $data['soc_id'])
                ->update($updateData);

            // Save data
            if ($result) {
                return $result;
            } else {
                return false;
            }
        } else {
            if ($data['credit_used_type'] == 'both') {
                $data['credit_used_type'] = 'adjustable';
                $data['payment_amount'] = $data['adjustable_amount'];
                $saveResponse = $this->saveCreditNote($data);
                if ($saveResponse['error'] == false) {
                    $data['credit_used_type'] = 'refundable';
                    $data['payment_amount'] = $data['refundable_amount'];
                    $data['used_for'] = null;
                    $data['adjustable_date'] = null;

                    $saveResponse = $this->saveCreditNote($data);

                    if ($saveResponse['error'] == false) {
                        return $saveResponse['id'];
                    }
                }
            } else {
                $saveResponse = $this->saveCreditNote($data);
                if ($saveResponse['error'] == false) {
                    return $saveResponse['id'];
                }
            }
        }

    }

    public function saveCreditNoteResponse($data)
    {
        foreach ($data['particular_data'] as $item) {
            // Prepare data to be saved using an associative array
            $creditData = [
                'soc_id' => $data['soc_id'],
                'account_id' => $data['unit_id'] ?? $data['account_id_member'] ?? $data['account_id_nonmember'],
                'credit_id' => $data['credit_account_id'],
                'credit_type' => $data['bill_type'] ?? '',
                'rectified_invoice_id' => $data['invoice_id'],
                'rectified_invoice_number' => $data['invoice_number'],
                'rectified_particular_id' => $item['rectified_amount'],
                'rectified_particular_name' => $item['rectified_name'] ?? preg_replace('/([a-z])([A-Z])/', '$1 $2', $item['particular']),
                'amount' => $item['rectified_amount'],
                'status' => 1,
                'created_by' => $data['user_id'] ?? 0,
                'updated_by' => $data['user_id'] ?? 0,
                'created_date' => date('Y-m-d H:i:s'),
                'updated_date' => date('Y-m-d H:i:s'),
                'payment_date' => $data['payment_date'],
            ];

            $result = $this->tenantDB()->table('chsone_credit_note')->insertGetId($creditData);

        }

        // Save the model and handle the response
        if ($result) {
            return [
                'error' => false,
                'id' => $result,
            ];
        } else {
            return [
                'error' => true,
            ];
        }
    }

    public function saveRectificationLedger($data)
    {
        try {
            $payment_amount = 0;
            $data['account_context'] = 'unit';
            $response = false;
            foreach ($data['particular_data'] as $item) {
                $data['payment_amount'] = $item['rectified_amount'];
                //get ledger account_id from chsone_grp_ledger_tree where ledger_account_name is $item['particular']
                $item['particular'] = strpos($item['particular'], '-') !== false ? explode('-', $item['particular'])[0] : $item['particular'];
                $ledgerData = $this->tenantDB()->table('chsone_grp_ledger_tree')->where('ledger_account_name', $item['particular'])->first();

                if ($ledgerData !== null) {
                    $data['et_ledger_id'] = isset($data['et_ledger_id']) ? $data['et_ledger_id'] : $ledgerData->ledger_account_id;
                    $item['et_ledger_id'] = isset($item['et_ledger_id']) ? $item['et_ledger_id'] : $ledgerData->ledger_account_id;
                    $item['ledger_account_id'] = isset($item['ledger_account_id']) ? $item['ledger_account_id'] : $ledgerData->ledger_account_id;
                    $data['ledger_account_id'] = isset($data['ledger_account_id']) ? $data['ledger_account_id'] : $ledgerData->ledger_account_id;
                } else {
                    dd("check line no ". __LINE__);
                }



                if ($data['account_context'] == 'unit') {
                    $arrDataListener['soc_id'] = $data['soc_id'];
                    $arrDataListener['unit_id'] = $data['unit_id'];
                    //only for members
                    $arrIncomeInvoiceMemberDetail = $this->getMemberDetail($arrDataListener); //get all Unit details

                    if (!empty($arrIncomeInvoiceMemberDetail)) {
                        $arrDataListener['member_id'] = $arrIncomeInvoiceMemberDetail['id'];
                    }
                    $arrFromLedgerDetails = $this->checkledgerExistNew(array("soc_id" => $data['soc_id'], 'ledger_id' => $item['et_ledger_id']));

                    $narration = $data['narration'];
                    $data['transaction_reference'] = 'rectification';
                    $data['receipt_number'] = $data['credit_account_id'];
                    $data['voucher_type_credit'] = 1;
                    // echo "<pre>";print_r($data);exit;
                    $arrResponse = $this->createMemberLedgerCredit(array('soc_id' => $data['soc_id'], 'unit_id' => $data['unit_id'], 'arrPostData' => $data, 'arrIncomeInvoiceMemberDetail' => $arrIncomeInvoiceMemberDetail, 'from_ledger' => $arrFromLedgerDetails, 'narration' => $narration));

                    if ($arrResponse['status'] = 'success') {
                        $response = true;
                    } else {
                        $response = false;
                    }
                } else {

                    // $nonmemberData = \ChsOne\Models\Nonmember::findFirst("nonmember_id = " . $data['account_id_member'] ?? $data['account_id_nonmember'])->toArray();

                    $nonmemberData = $this->tenantDB()->table('chsone_nonmember_master')->where('nonmember_id', $data['account_id_member'] ?? $data['account_id_nonmember'])->first();
                    $nonmemberData = json_decode(json_encode($nonmemberData), true);

                    $data['nonmember_ledger_id'] = $nonmemberData['nonmember_ledger_id'];

                    $arrToLedgerDetails['recieving_ledger_id'] = $nonmemberData['nonmember_ledger_id'];
                    $arrToLedgerDetails['receiver_name'] = 'suraj data';
                    $arrFromLedgerDetails = $this->checkledgerExistNew(array("soc_id" => $data['soc_id'], 'ledger_id' => $data['et_ledger_id']));
                    // $narration = 'Invoice Rectification for ' . $data['narration'];
                    $narration = $data['narration'];
                    $data['transaction_reference'] = 'rectification';
                    $data['receipt_number'] = $data['credit_account_id'];
                    $data['voucher_type_credit'] = 1;
                    $intBookerLedgerDetails = $this->payNonmemberBillLedger(array('soc_id' => $data['soc_id'], 'postData' => $data, 'arrBookerLedgerDetails' => $arrToLedgerDetails, 'to_ledger' => $arrToLedgerDetails, 'from_ledger' => $arrFromLedgerDetails, 'narration' => $narration));

                    if (!$intBookerLedgerDetails) {

                        $response = false;
                    } else {
                        $response = true;
                    }
                }
            }
            return $response;
        } catch (\Exception $e) {
            dd($e);
            $this->message = $e->getMessage();
            $this->status = 400;
        }
    }

/**
 * Process rectification for units
 */
    public function processUnitRectification($data)
    {
        $arrDataListener = [
            'soc_id' => $data['soc_id'],
            'unit_id' => $data['unit_id'],
        ];

        // Fetch member details if available
        $arrIncomeInvoiceMemberDetail = $this->getMemberDetail($arrDataListener);

        if (!empty($arrIncomeInvoiceMemberDetail)) {
            $arrDataListener['member_id'] = $arrIncomeInvoiceMemberDetail['id'];
        }

        // Get from ledger details
        $arrFromLedgerDetails = $this->checkledgerExistNew(
            [
                'soc_id' => $data['soc_id'],
                'ledger_id' => $data['et_ledger_id'],
            ]
        );

        // Prepare data for ledger credit
        $narration = $data['narration'];
        $data['transaction_reference'] = 'rectification';
        $data['receipt_number'] = $data['account_id_member'] ?? $data['account_id_nonmember'];
        $data['voucher_type_credit'] = 1;

        $data['arrIncomeInvoiceMemberDetail'] = $arrIncomeInvoiceMemberDetail;
        $data['from_ledger'] = $arrFromLedgerDetails;
        $data['narration'] = $narration;
        $data['arrPostData'] = $data;

        // Create ledger credit entry

        $arrResponse = $this->createMemberLedgerCredit(
            $data
        );

        // "success" => true
        //"transaction_id" => 36712
        //]

        return strtolower($arrResponse['status']) === 'success';
    }

/**
 * Process rectification for non-members
 */
    public function processNonMemberRectification($data)
    {

        // Fetch non-member data
        $nonmemberData = $this->tenantDB()->table('chsone_nonmember_master')
            ->where('nonmember_id', $data['account_id_member'] ?? $data['account_id_nonmember'])
            ->first();

        // Check if non-member data is found
        if (empty($nonmemberData)) {
            return false; // Return false if non-member data is not found
        }

        $data['nonmember_ledger_id'] = $nonmemberData->nonmember_ledger_id;

        // Set ledger details for the transaction
        $arrToLedgerDetails = [
            'recieving_ledger_id' => $nonmemberData->nonmember_ledger_id,
            'receiver_name' => 'suraj data',
        ];

        // Get from ledger details
        $arrFromLedgerDetails = $this->checkledgerExistNew([
            'soc_id' => $data['soc_id'],
            'ledger_id' => $data['et_ledger_id'],
        ]);

        // Check if from ledger details were found
        if (empty($arrFromLedgerDetails)) {
            return false; // Return false if from ledger details are not found
        }

        // Prepare additional data for ledger payment
        $narration = $data['narration'];
        $data['transaction_reference'] = 'rectification';
        $data['receipt_number'] = $data['credit_account_id'];
        $data['voucher_type_credit'] = 1;

        // Pay non-member bill ledger
        $intBookerLedgerDetails = $this->payNonmemberBillLedger([
            'soc_id' => $data['soc_id'],
            'postData' => $data,
            'arrBookerLedgerDetails' => $arrToLedgerDetails,
            'to_ledger' => $arrToLedgerDetails,
            'from_ledger' => $arrFromLedgerDetails,
            'narration' => $narration,
        ]);

        // Return true if ledger details were processed successfully, false otherwise
        return !empty($intBookerLedgerDetails);
    }

    public function processUnitLedger($data)
    {
        $arrDataListener['soc_id'] = $data['company_id'];
        $arrDataListener['unit_id'] = $data['unit_id'];

        // Get member details
        $arrIncomeInvoiceMemberDetail = $this->getMemberDetail($arrDataListener);

        if (!empty($arrIncomeInvoiceMemberDetail)) {
            $arrDataListener['member_id'] = $arrIncomeInvoiceMemberDetail['id'];
        }

        // Check ledger existence
        $arrFromLedgerDetails = $this->checkLedgerExistNew($data);

        // Process member ledger credit
        $arrResponse = $this->createMemberLedgerCredit([
            'unit_id' => $data['unit_id'],
            'arrPostData' => $data,
            'arrIncomeInvoiceMemberDetail' => $arrIncomeInvoiceMemberDetail,
            'from_ledger' => $arrFromLedgerDetails,
            'narration' => $data['narration'],
        ]);

        // Return success status
        return $arrResponse['status'] == 'success';
    }
    public function getMemberDetail($data = [])
    {
        $arrMemberMaster = [];

        // Use parameterized queries to avoid SQL injection
        $memberType = $this->tenantDB()->table('chsone_member_type_master')
            ->where('soc_id', $data['soc_id'])
            ->where('member_type_name', 'Primary')
            ->first();

        if (!$memberType) {
            // Log or handle the case where no member type is found
            // \Log::error('Member type "Primary" not found for soc_id: ' . $data['soc_id']);
            // return $arrMemberMaster;
            $this->message = 'Member type "Primary" not found for soc_id: ' . $data['soc_id'];
            $this->status = 400;
        }

        // Fetch the member master details using the member type ID and unit ID
        $objMemberMaster = $this->tenantDB()->table('chsone_members_master')
            ->where('soc_id', $data['soc_id'])
            ->where('fk_unit_id', $data['unit_id'])
            ->where('status', 1)
            ->where('member_type_id', $memberType->member_type_id)
            ->first();
        $objMemberMaster = json_decode(json_encode($objMemberMaster), true);

        return $objMemberMaster;
    }

    public function checkLedgerExistNew($data)
    {
        try {
            $arrClinetLedgerDetails = [];

            // Build the query with conditions based on `ledger_name` or `ledger_id`
            $query = $this->tenantDB()->table('chsone_grp_ledger_tree');

            if (isset($data['ledger_name']) && !empty($data['ledger_name'])) {
                $query->where('soc_id', $data['soc_id'])
                    ->where('context', $data['context'])
                    ->where('entity_type', ENTITY_TYPE_LEDGER)
                    ->where('ledger_account_name', $data['ledger_name']);

                if (isset($data['behaviour']) && !empty($data['behaviour'])) {
                    $query->where('behaviour', $data['behaviour']);
                }
            } elseif (isset($data['ledger_id']) && !empty($data['ledger_id'])) {
                $query->where('soc_id', $data['soc_id'])
                    ->where('ledger_account_id', $data['ledger_id']);
            }

            // Execute the query to get the first result
            $objBookerLedger = $query->first();

            if ($objBookerLedger) {
                $arrClinetLedgerDetails['recieving_ledger_id'] = $objBookerLedger->ledger_account_id;
                $arrClinetLedgerDetails['receiver_name'] = $objBookerLedger->ledger_account_name;
            } // Ledger not found, check if group_name is provided and create a new ledger
            elseif (!empty($data['group_name'])) {
                $name = $data['ledger_name'];
                $entity_type = ENTITY_TYPE_LEDGER;
                $grp_ledg_id = '';

                // Directly use the logic that would otherwise be handled by the `getParentGroupId` function
                $parent = $this->tenantDB()->table('chsone_grp_ledger_tree')
                    ->where('soc_id', $data['soc_id'])
                    ->where('ledger_account_name', $data['group_name']) // Assuming `group_name` is correct context
                    ->where('entity_type', ENTITY_TYPE_GROUP)
                    ->where('behaviour', $data['behaviour'])
                    ->where('context', $data['context'])
                    ->first();

                // Check if parent group was found
                if (empty($parent) || empty($parent->ledger_account_id)) {
                    return $arrClinetLedgerDetails; // Return empty if parent is not found
                }

                $parent_group = $parent->ledger_account_id;

                // Implementing the equivalent of the `manipulate` logic directly
                $ledger_id = $this->tenantDB()->table('chsone_grp_ledger_tree')->insertGetId([
                    'ledger_account_name' => $name,
                    'entity_type' => $entity_type,
                    'parent_group_id' => $parent_group,
                    'soc_id' => $data['soc_id'],
                    'context' => $data['context'],
                    'additional_fields' => '', // Add all required fields as per your actual schema
                    // Additional fields filled as per your requirements, replace with actual columns and logic
                ]);

                if ($ledger_id) {
                    $arrClinetLedgerDetails['recieving_ledger_id'] = $ledger_id;
                    $arrClinetLedgerDetails['receiver_name'] = $data['ledger_name'];
                }
            }
            return $arrClinetLedgerDetails;
        } catch(\Exception $e) {
            dd($e);
        }
    }
    public function createMemberLedgerCredit($arrData = array())
    {
        try {
            $soc_id = $arrData['soc_id'];
            $arrResponse = array('status' => 'error');
            $arrPostData = $arrData['arrPostData'];

            //print_r($arrData['to_ledger']); exit();
            if (!empty($arrPostData)) {
                //Member
                $arrIncomeInvoiceMemberDetail = $arrData['arrIncomeInvoiceMemberDetail'];
                if ($arrPostData['payment_amount'] > 0) {
                    $countLedgerEntry = 0;
                    $arrPostData['soc_id'] = $soc_id;
                    if (!empty($arrIncomeInvoiceMemberDetail)) {
                        $arrPostData['unit_id'] = $arrIncomeInvoiceMemberDetail['fk_unit_id'];
                        $arrPostData['member_id'] = $arrIncomeInvoiceMemberDetail['id'];
                    }
                    if (!empty($arrPostData['payment_date']) && strrchr($arrPostData['payment_date'], '-')) {
                        $arrPostData['payment_date'] = $this->getDisplayDate($arrPostData['payment_date']);
                    }
                    if (ACCOUNT_MODULE_EXIST == 1) {
                        $arrListnerData = array('soc_id' => $soc_id, 'unit_id' => $arrPostData['unit_id']);
                        $arrUnitDetails = $this->getUnitDetailById($arrListnerData);
                        $arrUnitDetails = json_decode(json_encode($arrUnitDetails), true);

                        if (!empty($arrUnitDetails)) {

                            $arrListnerData['ledger_id'] = $arrUnitDetails['ledger_account_id'];
                            $arrUnitLedgerDetails = $this->checkledgerExistNew($arrListnerData);

                            if (!empty($arrUnitLedgerDetails)) {

                                $countLedgerEntry = $this->paymentLedgerEntry(array('soc_id' => $soc_id, 'arrPostData' => $arrPostData, 'arrUnitLedgerDetails' => $arrUnitLedgerDetails, 'from_ledger' => $arrData['from_ledger'], 'to_ledger' => $arrData['to_ledger'] ?? [], 'narration' => $arrData['narration'])); //get all Unit details

                                // print_r($countLedgerEntry); exit();
                                if (!$countLedgerEntry) {
                                    $arrResponse['status'] = 'success';
                                }
                            }
                        }
                        //Send notification
                        if ($countLedgerEntry == 0) {
                            //send notification code
                        }
                    }
                }
                return $arrResponse;
            }
        }catch(\Exception $e) {
            dd($e);
        }
    }

/**
 * Get unit details by unit ID
 */
    public function getUnitDetails($data)
    {
        $arrListnerData = ['soc_id' => $data['soc_id'], 'unit_id' => $data['unit_id']];
        return $this->getUnitDetailById($arrListnerData);
    }

/**
 * Make the ledger entry and handle narration and other details
 */
    public function makeLedgerEntry($arrPostData, $arrUnitLedgerDetails, $arrData)
    {

        // Prepare data for ledger entry
        $ledgerEntryData = [
            'soc_id' => $arrPostData['soc_id'],
            'arrPostData' => $arrPostData,
            'arrUnitLedgerDetails' => $arrUnitLedgerDetails,
            'from_ledger' => $arrData['from_ledger'],
            'to_ledger' => $arrUnitLedgerDetails,
            'narration' => $arrData['narration'],
        ];

        // Call the payment ledger entry function
        return $this->paymentLedgerEntry($ledgerEntryData);
    }

    public function getParentGroupId($arrData)
    {
        // Perform a query using the Eloquent model
        $parent = $this->tenantDB()->table('chsone_grp_ledger_tree')->where('soc_id', $arrData['soc_id'])
            ->where('ledger_account_name', $arrData['group_name'])
            ->where('entity_type', ENTITY_TYPE_GROUP)
            ->where('behaviour', $arrData['behaviour'])
            ->where('context', $arrData['context'])
            ->first();

        // Return the parent if found, otherwise return false
        return $parent ?: false;
    }

    public function getUnitDetailById($data = [])
    {
        // Validate required data
        $soc_id = $data['soc_id'] ?? null;
        $unit_id = $data['unit_id'] ?? null;

        // Return empty if required data is missing
        if (empty($soc_id) || empty($unit_id)) {
            return [];
        }

        // Fetch unit details using Eloquent or your preferred ORM
        $unitDetails = $this->tenantDB()->table('chsone_units_master')->where('soc_id', $soc_id)
            ->where('unit_id', $unit_id)
            ->first();

        // Convert the result to an array if found, otherwise return empty array
        return $unitDetails ? $unitDetails : [];
    }

    public function paymentLedgerEntry($data = array())
    {
        try {
            $countLedgerEntry = 0;

            if (ACCOUNT_MODULE_EXIST == 1) {

                $soc_id = $data['soc_id'];
                $arrPostData = $data['arrPostData'];
                $arrUnitLedgerDetails = $data['arrUnitLedgerDetails'];

                if (isset($arrPostData['clearance_date']) && !empty($arrPostData['clearance_date'])) {
                    $arrPostData['payment_date'] = $arrPostData['clearance_date'];
                }

                if (empty($arrPostData['payment_date'])) {
                    $arrPostData['payment_date'] = $this->getCurrentDate('display');
                }

                //$arrIncomeAccounts = $this->incometrackerevent->incometracker('accountListener:checkledgerExistNew', array('auth' => $data['auth'], 'ledger_id' => $arrPostData['bank_account']));
                // print_r($this->constants['payment_mode_for_receipt_reversal']); exit();
                if (!empty($arrPostData['payment_mode']) && !in_array(strtolower($arrPostData['payment_mode']), $this->constants['payment_mode_for_receipt_reversal'])) {
                    //  print_r(array(trim($arrPostData['payment_instrument']) . '_PG_LEDGER_ID'));
                    $arrInvoiceGeneralSetting = $this->getInvoiceGeneralSetting(array('soc_id' => $data['soc_id'], 'setting_key' => array(trim($arrPostData['payment_instrument']) . '_PG_LEDGER_ID')));

                    if (!empty($arrInvoiceGeneralSetting[0]['setting_value'])) {
                        $arrIncomeAccounts = $this->checkledgerExistNew(array('soc_id' => $data['soc_id'], 'ledger_id' => $arrInvoiceGeneralSetting[0]['setting_value']));
                        $arrIncomeAccounts['ledger_id'] = $arrIncomeAccounts['recieving_ledger_id'];
                        $arrIncomeAccounts['ledger_name'] = $arrIncomeAccounts['receiver_name'];
                    } else {
                        if (!empty($data['to_ledger'])) {
                            $arrIncomeAccounts['ledger_id'] = $data['to_ledger']['recieving_ledger_id'];
                            $arrIncomeAccounts['ledger_name'] = $data['to_ledger']['receiver_name'];
                        } else {
                            return 1;
                        }
                    }
                } else {
                    if (isset($data['arrUnitLedgerDetails']['suspenseLedgerDetails']) && !empty($data['arrUnitLedgerDetails']['suspenseLedgerDetails'])) {
                        $arrIncomeAccounts['ledger_id'] = $data['arrUnitLedgerDetails']['suspenseLedgerDetails']['recieving_ledger_id'];
                        $arrIncomeAccounts['ledger_name'] = $data['arrUnitLedgerDetails']['suspenseLedgerDetails']['receiver_name'];
                    } else {
                        // Getting bank/cash ledger details
                        $arrAccountDetail = $this->getBankCashAccountDetail(array(
                            'soc_id' => $data['soc_id'],
                        )); // get all Unit details
                        $arrLedgerAccountDetail = $this->getLedgerAccountDetail(array(
                            'account_detail' => $arrAccountDetail,
                        )); // get all Unit details

                        if (!empty($arrLedgerAccountDetail)) {
                            $arrIncomeAccounts['ledger_id'] = $arrLedgerAccountDetail['cash']['ledger_id'];
                            $arrIncomeAccounts['ledger_name'] = $arrLedgerAccountDetail['cash']['ledger_name'];
                            if (strtolower($arrPostData['payment_mode']) != 'cash') {
                                if (!empty($arrPostData['bank_account'])) {
                                    $arrIncomeAccounts['ledger_id'] = $arrPostData['bank_account'];
                                    $arrIncomeAccounts['ledger_name'] = $arrLedgerAccountDetail['arrBank'][$arrPostData['bank_account']];
                                } else {
                                    $arrIncomeAccounts['ledger_id'] = $arrLedgerAccountDetail['bank']['ledger_id'];
                                    $arrIncomeAccounts['ledger_name'] = $arrLedgerAccountDetail['bank']['ledger_name'];
                                }
                            }
                        }
                    }
                }

                if ($arrPostData['payment_mode'] == 'cashtransfer') {
                    $paymentModeForNarration = DISPLAY_CASH_TRANSFER;
                } else {
                    $paymentModeForNarration = $arrPostData['payment_mode'] ?? null;
                }

                // $paymentModeForNarration = $arrPostData ['payment_mode'];
                $strNarration = '';
                if (!empty($arrPostData['payment_mode']) && in_array(strtolower($arrPostData['payment_mode']), $this->constants['payment_mode_for_clearance'])) {
                    $strNarration = ' with transaction ref. (' . $arrPostData['transaction_reference'] . ', ' . $arrPostData['payment_instrument'] . ')';
                } elseif (!empty($arrPostData['payment_mode']) && strtolower($arrPostData['payment_mode']) == 'cashtransfer') {
                    $strNarration = ' with payment ref. (' . $arrPostData['transaction_reference'] . ')';
                } elseif (!empty($arrPostData['payment_mode']) && !in_array(strtolower($arrPostData['payment_mode']), $this->constants['payment_mode_for_receipt_reversal'])) {
                    $strNarration = ' with transaction ref. (' . $arrPostData['transaction_reference'] . ')';
                    switch ($arrPostData['payment_mode']) {
                        case YES_BANK_PG:
                            $paymentModeForNarration = DISPLAY_YES_BANK;
                            break;
                        case YES_BANK_ECOLLECT:
                            $paymentModeForNarration = DISPLAY_YES_BANK_ECOLLECT;
                            break;
                        case MOBIKWIK_WALLET:
                            $paymentModeForNarration = DISPLAY_MOBIKWIK_WALLET;
                            break;
                        case MOBIKWIK_PG:
                            $paymentModeForNarration = DISPLAY_MOBIKWIK_PG;
                            break;
                        case CASHFREE_PG:
                            $paymentModeForNarration = DISPLAY_CASHFREE_PG;
                            break;
                        case PAYTM_PG:
                            $paymentModeForNarration = DISPLAY_PAYTM;
                            break;
                        case HDFC_PG:
                            $paymentModeForNarration = DISPLAY_HDFC_PG;
                            break;
                        case ATOM_PG:
                            $paymentModeForNarration = DISPLAY_ATOM_PG;
                            break;
                        case 'cashtransfer':
                            $paymentModeForNarration = DISPLAY_CASH_TRANSFER;
                            break;
                    }
                }
                // echo $arrIncomeInvoicePaymentTracker['payment_mode'];exit;
                // Ledger entry for payment amount
                $arrLedgerTransactionData = array(
                    'soc_id' => $soc_id,
                );
                $arrLedgerTransactionData['voucher_type'] = isset($arrPostData['voucher_type_credit']) ? VOUCHER_CREDIT : VOUCHER_RECEIPT;
                $arrLedgerTransactionData['voucher_reference_number'] = $arrPostData['receipt_number'];
                $arrLedgerTransactionData['voucher_reference_id'] = $arrPostData['payment_tracker_id'];
                $arrLedgerTransactionData['from_ledger_id'] = $arrIncomeAccounts['ledger_id'];
                $arrLedgerTransactionData['to_ledger_id'] = $arrUnitLedgerDetails['recieving_ledger_id'];
                $arrLedgerTransactionData['transaction_date'] = !empty($arrPostData['payment_date']) ? $this->getDatabaseDate($arrPostData['payment_date']) : $this->getCurrentDate('database');
                $arrLedgerTransactionData['transaction_amount'] = $arrPostData['payment_amount'];

                // $arrLedgerTransactionData['narration'] = 'Amount received against Invoice ' . $arrPostData['member_paid_invoice']  ?? '' . ' dated ' . $this->getDisplayDate($arrPostData['payment_date']) . ' through ' . $paymentModeForNarration . $strNarration; // 'Invoice '.$arrPostData['member_paid_invoice'].' '.$eachLedgerDetail['particular'].' Write Off';
                $arrLedgerTransactionData['narration'] = 'Amount received against Invoice ' .
                    (!empty($arrPostData['member_paid_invoice']) ? $arrPostData['member_paid_invoice'] : '') .
                    ' dated ' . $this->getDisplayDate($arrPostData['payment_date']) .
                    ' through ' . $paymentModeForNarration .
                    $strNarration;

                if ((empty($arrPostData['total_unpaid_amount']) || $arrPostData['total_unpaid_amount'] <= 0) && empty($arrPostData['member_paid_invoice'])) {
                    // $arrLedgerTransactionData['narration'] = 'Advance payment received from ' . ucwords($arrPostData['received_from']) . ' dated ' . $this->getDisplayDate($arrPostData['payment_date']) . ' through ' . $paymentModeForNarration . $strNarration; // $eachInvoiceDetail['invoice_number'] . ' late payment charges';
                    $arrLedgerTransactionData['narration'] = 'Amount received against Invoice ' .
                        ($arrPostData['member_paid_invoice'] ?? '') .
                        ' dated ' . $this->getDisplayDate($arrPostData['payment_date']) .
                        ' through ' . $paymentModeForNarration .
                        $strNarration;

                }
                if ($arrPostData['bill_type'] == 'suspense') {
                    $arrLedgerTransactionData['narration'] = 'Unidenfied payment from ' . ucwords($arrPostData['received_from']) . ' dated ' . $this->getDisplayDate($arrPostData['payment_date']) . ' through ' . $paymentModeForNarration . $strNarration;
                }

                $arrLedgerTransactionData['from_ledger_name'] = $arrIncomeAccounts['ledger_name'];
                $arrLedgerTransactionData['to_ledger_name'] = $arrUnitLedgerDetails['receiver_name'];
                $arrLedgerTransactionData['payment_reference'] = $arrPostData['transaction_reference'];
                $arrLedgerTransactionData['transaction_type'] = '';
                $arrLedgerTransactionData['mode_of_payment'] = $arrPostData['payment_mode'] ?? null;
                $arrLedgerTransactionData['other_payment_ref'] = '';

                // Code to replace from ledger id From Bank/Cash
                if (isset($data['from_ledger'])) {
                    $arrLedgerTransactionData['from_ledger_id'] = $data['from_ledger']['recieving_ledger_id'];
                    $arrLedgerTransactionData['from_ledger_name'] = $data['from_ledger']['receiver_name'];
                }
                if (!empty($data['to_ledger']) && !empty($data['to_ledger']['recieving_ledger_id']) && !empty($data['to_ledger']['receiver_name'])) {
                    $arrLedgerTransactionData['to_ledger_id'] = $data['to_ledger']['recieving_ledger_id'];
                    $arrLedgerTransactionData['to_ledger_name'] = $data['to_ledger']['receiver_name'];
                }

                if (isset($data['narration'])) {
                    $arrLedgerTransactionData['narration'] = $data['narration'];
                }
                $arrLedgerTransactionData['narration'] = '(Receipt No-' . $arrPostData['receipt_number'] . ') ' . $arrLedgerTransactionData['narration'];

                if (!empty($arrPostData['payment_note'])) {
                    $arrLedgerTransactionData['narration'] .= ' [' . $arrPostData['payment_note'] . ']';
                }

                //echo 'pay'; print_r($arrLedgerTransactionData); exit();
                if (!empty($arrPostData['tds_amount']) && $arrPostData['tds_amount'] > 0) {
                    $arrLedgerTransactionData['transaction_amount'] = round($arrPostData['payment_amount'] - $arrPostData['tds_amount'], 2);
                    $arrLedgerEntry = $this->transactionLedgerEntry($arrLedgerTransactionData);
                    if (isset($arrLedgerEntry['error']) && !empty($arrLedgerEntry['error'])) {
                        $countLedgerEntry++;
                    } else {
                        $arrLedgerTransactionData['transaction_amount'] = $arrPostData['tds_amount'];
                        $arrListnerData['soc_id'] = $soc_id;
                        $arrListnerData['ledger_name'] = 'tds receivable';
                        $arrListnerData['context'] = ''; // CURRENT_ASSETS_GROUP;
                        $arrListnerData['behaviour'] = ASSET;

                        $arrTdsLedger = $this->checkledgerExistNew($arrListnerData);
                        $arrLedgerTransactionData['from_ledger_id'] = $arrTdsLedger['recieving_ledger_id'];
                        $arrLedgerTransactionData['from_ledger_name'] = $arrTdsLedger['receiver_name'];
                        $arrLedgerTransactionData['narration'] = 'TDS amount deducted against Invoice ' . $arrPostData['member_paid_invoice'] . ' dated ' . $this->getDisplayDate($arrPostData['payment_date']) . ' through ' . $paymentModeForNarration . $strNarration; // 'Invoice '.$arrPostData['member_paid_invoice'].' '.$eachLedgerDetail['particular'].' Write Off';

                        $arrLedgerEntry = $this->transactionLedgerEntry($arrLedgerTransactionData);
                        if (isset($arrLedgerEntry['error']) && !empty($arrLedgerEntry['error'])) {
                            $countLedgerEntry++;
                        }
                    }
                } else {
                    //echo 'led';
                    $arrLedgerEntry = $this->transactionLedgerEntry($arrLedgerTransactionData);
                    // print_r($arrLedgerEntry); exit();
                    if (isset($arrLedgerEntry['error']) && !empty($arrLedgerEntry['error'])) {
                        $countLedgerEntry++;
                    }
                }
                //Ledger entry for invoice writeoff
                if (!empty($arrPostData['writeoff_amount']) && $arrPostData['writeoff_amount'] > 0) {
                    if ($arrPostData['writeoff_amount'] >= 1000) {
                        $arrExpenseWriteOff = $this->checkledgerExistNew(array('soc_id' => $data['soc_id'], 'ledger_name' => 'Expense Bad Debt', 'context' => EXPENSE, 'behaviour' => EXPENSE));
                        if (empty($arrExpenseWriteOff['recieving_ledger_id'])) {
                            $arrParentExpense = $this->getLedgerDetail(array('soc_id' => $data['soc_id'], 'ledger_name' => 'Indirect Expense', 'context' => EXPENSE, 'behaviour' => EXPENSE));
                            if (!empty($arrParentExpense['ledger_account_id'])) {
                                $arrExpenseWriteOff['receiver_name'] = 'Expense Bad Debt';
                                $arrExpenseWriteOff['recieving_ledger_id'] = $this->createNewLedgerExit(array('soc_id' => $data['soc_id'], 'ledger_name' => $arrExpenseWriteOff['receiver_name'], 'context' => EXPENSE, 'behaviour' => EXPENSE, 'parent_id' => $arrParentExpense['ledger_account_id']));
                            }
                        }
                    } else {
                        $arrExpenseWriteOff = $this->checkledgerExistNew(array('soc_id' => $data['soc_id'], 'ledger_name' => 'Expense Write Off', 'context' => EXPENSE, 'behaviour' => EXPENSE));
                    }
                    if (empty($arrExpenseWriteOff['recieving_ledger_id'])) {
                        return 1;
                    }
                    $arrLedgerTransactionData['transaction_amount'] = $arrPostData['writeoff_amount'];
                    $arrLedgerTransactionData['from_ledger_id'] = $arrExpenseWriteOff['recieving_ledger_id'];
                    $arrLedgerTransactionData['from_ledger_name'] = $arrExpenseWriteOff['receiver_name'];

                    $arrLedgerTransactionData['narration'] = 'Amount writeoff against Invoice ' . $arrPostData['member_paid_invoice'] . ' dated ' . $this->getDisplayDate($arrPostData['payment_date']); //'Invoice '.$arrPostData['member_paid_invoice'].' '.$eachLedgerDetail['particular'].' Write Off';
                    $arrLedgerEntry = $this->transactionLedgerEntry($arrLedgerTransactionData);
                    if (isset($arrLedgerEntry['error']) && !empty($arrLedgerEntry['error'])) {
                        $countLedgerEntry++;
                    }
                }
            }

            return $countLedgerEntry;
        }catch(\Exception $e) {
            dd($e);
        }
    }
    /**
     * Set payment date based on clearance date or current date.
     */
    public function getPaymentDate($arrPostData)
    {
        return $arrPostData['clearance_date'] ?? $arrPostData['payment_date'] ?? $this->getCurrentDate('display');
    }

    /**
     * Determine the appropriate ledger based on payment mode and settings.
     */
    public function getIncomeAccounts($arrPostData, $data)
    {
        // Handling various ledger conditions based on payment mode
        if (!empty($arrPostData['payment_mode']) && !in_array(strtolower($arrPostData['payment_mode']), $this->constants['payment_mode_for_receipt_reversal'])) {
            $arrInvoiceGeneralSetting = $this->getInvoiceGeneralSetting([
                'soc_id' => $data['soc_id'],
                'setting_key' => [trim($arrPostData['payment_instrument'] ?? '') . '_PG_LEDGER_ID'],
            ]);

            if (!empty($arrInvoiceGeneralSetting[0]['setting_value'])) {
                return $this->fetchLedgerById($arrInvoiceGeneralSetting[0]['setting_value'], $data);
            } elseif (!empty($data['to_ledger'])) {
                return [
                    'ledger_id' => $data['to_ledger']['recieving_ledger_id'],
                    'ledger_name' => $data['to_ledger']['receiver_name'],
                ];
            }
        }

        // Default to suspense ledger or bank/cash details if necessary
        if (isset($data['arrUnitLedgerDetails']['suspenseLedgerDetails'])) {
            return [
                'ledger_id' => $data['arrUnitLedgerDetails']['suspenseLedgerDetails']['recieving_ledger_id'],
                'ledger_name' => $data['arrUnitLedgerDetails']['suspenseLedgerDetails']['receiver_name'],
            ];
        } else {
            return $this->fetchDefaultBankCashLedger($data);
        }

        return null;
    }

    /**
     * Prepare transaction narration based on payment details.
     */
    public function prepareNarration($arrPostData, $arrIncomeAccounts, $arrUnitLedgerDetails)
    {
        $paymentModeForNarration = ($arrPostData['payment_mode'] == 'cashtransfer') ? DISPLAY_CASH_TRANSFER : $arrPostData['payment_mode'];
        $strNarration = '';

        if (!empty($arrPostData['payment_mode']) && in_array(strtolower($arrPostData['payment_mode']), $this->constants['payment_mode_for_clearance'])) {
            $strNarration = ' with transaction ref. (' . $arrPostData['transaction_reference'] . ', ' . $arrPostData['payment_instrument'] . ')';
        } elseif (!empty($arrPostData['payment_mode']) && strtolower($arrPostData['payment_mode']) == 'cashtransfer') {
            $strNarration = ' with payment ref. (' . $arrPostData['transaction_reference'] . ')';
        } elseif (!empty($arrPostData['payment_mode']) && !in_array(strtolower($arrPostData['payment_mode']), $this->constants['payment_mode_for_receipt_reversal'])) {
            $strNarration = ' with transaction ref. (' . $arrPostData['transaction_reference'] . ')';
        }

        return '(Receipt No-' . $arrPostData['receipt_number'] . ') Amount received against Invoice ' . $arrPostData['invoice_number'] .
        ' dated ' . $this->getDisplayDate($arrPostData['payment_date']) . ' through ' . $paymentModeForNarration . $strNarration;
    }

    /**
     * Prepare the ledger transaction data array.
     */
    public function prepareLedgerTransactionData($arrPostData, $arrIncomeAccounts, $arrUnitLedgerDetails, $narration, $soc_id)
    {
        return [
            'soc_id' => $soc_id,
            'voucher_type' => isset($arrPostData['voucher_type_credit']) ? VOUCHER_CREDIT : VOUCHER_RECEIPT,
            'voucher_reference_number' => $arrPostData['receipt_number'],
            'voucher_reference_id' => $arrPostData['payment_tracker_id'],
            'from_ledger_id' => $arrIncomeAccounts['ledger_id'],
            'to_ledger_id' => $arrUnitLedgerDetails['recieving_ledger_id'],
            'transaction_date' => !empty($arrPostData['payment_date']) ? $this->getDatabaseDate($arrPostData['payment_date']) : $this->getCurrentDate('database'),
            'transaction_amount' => $arrPostData['payment_amount'],
            'narration' => $narration,
            'from_ledger_name' => $arrIncomeAccounts['ledger_name'],
            'to_ledger_name' => $arrUnitLedgerDetails['receiver_name'],
            'payment_reference' => $arrPostData['transaction_reference'],
            'transaction_type' => '',
            'mode_of_payment' => $arrPostData['payment_mode'],
            'other_payment_ref' => '',
        ];
    }

    /**
     * Execute the ledger entry transaction.
     */
    public function executeLedgerEntry($arrLedgerTransactionData)
    {
        $arrLedgerEntry = $this->transactionLedgerEntry($arrLedgerTransactionData);
        return isset($arrLedgerEntry['error']) && !empty($arrLedgerEntry['error']) ? 1 : 0;
    }

    /**
     * Handle invoice write-off scenario.
     */
    public function handleInvoiceWriteoff($arrLedgerTransactionData, $arrPostData, $soc_id, $event, $component)
    {
        $countLedgerEntry = 0;
        $arrExpenseWriteOff = $this->fetchLedgerByName('Expense Write Off', $soc_id);

        if (empty($arrExpenseWriteOff['recieving_ledger_id'])) {
            return 1; // Ledger not found, return error count
        }

        $arrLedgerTransactionData['transaction_amount'] = $arrPostData['writeoff_amount'];
        $arrLedgerTransactionData['from_ledger_id'] = $arrExpenseWriteOff['recieving_ledger_id'];
        $arrLedgerTransactionData['from_ledger_name'] = $arrExpenseWriteOff['receiver_name'];
        $arrLedgerTransactionData['narration'] = 'Amount writeoff against Invoice ' . $arrPostData['invoice_number'] .
        ' dated ' . $this->getDisplayDate($arrPostData['payment_date']);

        $arrLedgerEntry = $this->executeLedgerEntry($arrLedgerTransactionData);
        if (isset($arrLedgerEntry['error']) && !empty($arrLedgerEntry['error'])) {
            $countLedgerEntry++;
        }

        return $countLedgerEntry;
    }

    //TEST

/**
 * Determine the appropriate ledger based on payment mode.
 */
    public function determineLedger($arrPostData, $data)
    {

        // Check specific conditions for different ledger accounts
        // Replace or expand logic as per requirements
        if (!empty($arrPostData['payment_mode'])) {
            $arrInvoiceGeneralSetting = $this->getInvoiceGeneralSetting([
                'soc_id' => $arrPostData['soc_id'],
                'setting_key' => [trim($arrPostData['payment_instrument']) . '_PG_LEDGER_ID'],
            ]);

            if (!empty($arrInvoiceGeneralSetting[0]['setting_value'])) {
                return $this->fetchLedgerById($arrInvoiceGeneralSetting[0]['setting_value'], $data['soc_id']);
            } elseif (!empty($data['to_ledger'])) {
                return [
                    'ledger_id' => $data['to_ledger']['recieving_ledger_id'],
                    'ledger_name' => $data['to_ledger']['receiver_name'],
                ];
            }
        }

        // Default to suspense ledger or bank/cash details if necessary
        if (isset($data['arrUnitLedgerDetails']['suspenseLedgerDetails'])) {
            return [
                'ledger_id' => $data['arrUnitLedgerDetails']['suspenseLedgerDetails']['recieving_ledger_id'],
                'ledger_name' => $data['arrUnitLedgerDetails']['suspenseLedgerDetails']['receiver_name'],
            ];
        } else {
            return $this->fetchDefaultBankCashLedger($data);
        }
    }

    /**
     * Fetch default bank or cash ledger details based on the provided context.
     */
    public function fetchDefaultBankCashLedger($soc_id)
    {
        // Fetch bank/cash account details
        $arrAccountDetail = $this->getBankCashAccountDetail([
            'soc_id' => $soc_id,
        ]);

        // Get ledger account details based on account type (cash or bank)
        $arrLedgerAccountDetail = $this->getLedgerAccountDetail([
            'account_detail' => $arrAccountDetail,
        ]);

        // Check and return ledger details for cash
        if (!empty($arrLedgerAccountDetail)) {
            $ledgerData = [
                'ledger_id' => $arrLedgerAccountDetail['cash']['ledger_id'],
                'ledger_name' => $arrLedgerAccountDetail['cash']['ledger_name'],
            ];

            // Check if the payment mode is not cash and bank details are available
            if (!empty($arrAccountDetail['payment_mode']) && strtolower($arrAccountDetail['payment_mode']) !== 'cash') {
                if (!empty($arrAccountDetail['bank_account'])) {
                    $ledgerData['ledger_id'] = $arrAccountDetail['bank_account'];
                    $ledgerData['ledger_name'] = $arrLedgerAccountDetail['arrBank'][$arrAccountDetail['bank_account']];
                } else {
                    $ledgerData['ledger_id'] = $arrLedgerAccountDetail['bank']['ledger_id'];
                    $ledgerData['ledger_name'] = $arrLedgerAccountDetail['bank']['ledger_name'];
                }
            }

            return $ledgerData;
        }

        // Return null if no ledger details found
        return null;
    }

/**
 * Fetch bank or cash account details for the specified society.
 *
 * @param array $data The data array containing the 'soc_id' (society ID) and optional 'only_bank' flag.
 * @return array Returns an array with bank and cash account details.
 */
    public function getBankCashAccountDetail($data = [])
    {
        // Define the base query with joins and column selections
        $query = $this->tenantDB()->table('chsone_grp_ledger_tree as grpLedger')
            ->select([
                'grpLedger.ledger_account_id',
                'grpLedger.ledger_account_name',
                'grpLedger.context',
                'account.default_account',
                'account.default_bank_for_incidental',
                'account.default_bank_for_nonmember',
                'account.bank_name',
                'account.account_number',
                'account.bank_address',
                'account.bank_city',
                'account.bank_ifsc',
                'account.account_id',
            ])
            ->leftJoin('chsone_accounts_master as account', 'grpLedger.ledger_account_id', '=', 'account.ledger_account_id')
            ->where('grpLedger.soc_id', $data['soc_id'])
            ->where('grpLedger.entity_type', 'ledger')
            ->where('grpLedger.status', 1);

        // Filter by context based on 'only_bank' flag
        if (!empty($data['only_bank'])) {
            $query->where('grpLedger.context', 'bank');
        } else {
            $query->whereIn('grpLedger.context', ['cash', 'bank']);
        }

        // Execute the query and get results as an array
        $arrAccountDetail = $query->get()->toArray();

        return $arrAccountDetail;
    }
    public function getInvoiceGeneralSetting($data = [])
    {
        // Initialize the response array
        $arrInvoiceGeneralSetting = [];

        // Validate the input data to ensure necessary keys are present and valid
        if (empty($data['soc_id']) || empty($data['setting_key']) || !is_array($data['setting_key'])) {
            return $arrInvoiceGeneralSetting;
        }

        // Fetch the general invoice settings from the database using Laravel's query builder
        $objInvoiceGeneralSetting = $this->tenantDB()
            ->table('income_invoice_general_settings')
            ->where('soc_id', $data['soc_id'])
            ->whereIn('setting_key', $data['setting_key'])
            ->get();

        // Convert the results to an array if any settings are found
        if (!$objInvoiceGeneralSetting->isEmpty()) {
            $arrInvoiceGeneralSetting = $objInvoiceGeneralSetting->toArray();
        }

        return $arrInvoiceGeneralSetting;
    }

    public function getLedgerAccountDetail(array $data): array
    {
        try {
            $arrAccountDetail = [
                'cash' => [],
                'bank' => [],
                'arrBank' => [],
                'arrCash' => [],
            ];
            $data = json_decode(json_encode($data), true);
            // Check if account details are provided
            if (!empty($data['account_detail'])) {

                foreach ($data['account_detail'] as $eachAccountDetail) {
                    $context = strtolower($eachAccountDetail['context']);
                    $ledgerId = $eachAccountDetail['ledger_account_id'];
                    $ledgerName = $eachAccountDetail['ledger_account_name'];

                    // Handle cash account details
                    if ($context === 'cash' && empty($arrAccountDetail['cash'])) {
                        $arrAccountDetail['cash'] = [
                            'ledger_id' => $ledgerId,
                            'ledger_name' => $ledgerName,
                        ];
                        $arrAccountDetail['arrCash'][$ledgerId] = $ledgerName;
                    }

                    // Handle bank account details
                    if ($context === 'bank') {
                        if ($eachAccountDetail['default_account'] == 1) {
                            $arrAccountDetail['bank'] = [
                                'ledger_id' => $ledgerId,
                                'ledger_name' => $ledgerName,
                            ];
                        }
                        $arrAccountDetail['arrBank'][$ledgerId] = $ledgerName;

                        // Default bank for incidental cases
                        if (!empty($data['default_bank_incidental']) && $eachAccountDetail['default_bank_for_incidental'] == 1) {
                            $arrAccountDetail['bank'] = [
                                'ledger_id' => $ledgerId,
                                'ledger_name' => $ledgerName,
                            ];
                        }

                        // Default bank for non-member cases
                        if (!empty($data['default_bank_nonmember']) && $eachAccountDetail['default_bank_for_nonmember'] == 1) {
                            $arrAccountDetail['bank'] = [
                                'ledger_id' => $ledgerId,
                                'ledger_name' => $ledgerName,
                            ];
                        }
                    }
                }

                // Set the first available bank if no default is selected
                if (empty($arrAccountDetail['bank']) && !empty($arrAccountDetail['arrBank'])) {
                    $firstBank = reset($arrAccountDetail['arrBank']);
                    $firstBankId = key($arrAccountDetail['arrBank']);
                    $arrAccountDetail['bank'] = [
                        'ledger_id' => $firstBankId,
                        'ledger_name' => $firstBank,
                    ];
                }
            }

            return $arrAccountDetail;
        }catch(\Exception $e) {
            dd($e);
        }
    }

    public function transactionLedgerEntry($data = [])
    {
        // Initialize response array
        $arrResponse = [];

        // Validate required fields
        if (empty(trim($data['from_ledger_id'])) || empty(trim($data['to_ledger_id']))) {
            return ['error' => true, 'message' => 'From or To ledger ID is missing'];
        }
        // Set default transaction date if not provided
        $data['transaction_date'] = $data['transaction_date'] ?? date('Y-m-d H:i:s');
        // Execute the transaction entry and capture the transaction ID
        $transaction_id = $this->executeTransactionEntry($data);

        // Check if the transaction was successful
        if ($transaction_id) {
            $arrResponse = ['success' => true, 'transaction_id' => $transaction_id];
        } else {
            $arrResponse = ['error' => true, 'message' => 'Transaction failed'];
        }

        return $arrResponse;
    }

    public function executeTransactionEntry($data)
    {
        $vouchers_array = [];

        //         // Check if voucher type contains an underscore and split if found
        if (isset($data['voucher_type']) && strpos($data['voucher_type'], '_') !== false) {
            $vouchers_array = explode('_', $data['voucher_type']);
            $data['voucher_type'] = $vouchers_array[0]; // Set the primary voucher type
        }
        // Attempt the first transaction entry
        $data['transaction_from_id'] = $this->_addTransactionEntry($data);
        // If the first transaction entry is successful
        if (!empty($data['transaction_from_id'])) {
            // Debug to confirm the first transaction ID

            // Check if a secondary voucher type exists and needs processing
            if (count($vouchers_array) >= 2) {
                $data['voucher_type'] = $vouchers_array[1]; // Set the secondary voucher type
            }

            // Attempt the second transaction entry
            $secondEntrySuccess = $this->_addTransactionEntry($data);
            // Check if the second entry was successful
            if ($secondEntrySuccess) {
                // Optional: commit transaction if using DB transactions
                // $this->soc_db_w->commit();
                return $data['transaction_from_id'];
            } else {
                // Optional: rollback transaction if the second entry fails
                // $this->soc_db_w->rollback();
                // dd('Failed to insert second transaction entry');
                // Log::error('Failed to insert second transaction entry', ['data' => $data]);
                return false;
            }
        } else {
            // Optional: rollback transaction if the first entry fails
            // $this->soc_db_w->rollback();
            // dd('Failed to insert first transaction entry');
            // Log::error('Failed to insert first transaction entry', ['data' => $data]);
            return false;
        }
    }

    public function _addTransactionEntry($data)
    {
        // Set transaction type and ledger details based on the mode
        if (!empty($data['transaction_from_id'])) {
            $data['transaction_type'] = "cr";
            $ledger_id = $data['to_ledger_id'];
            $ledger_name = $data['to_ledger_name'];
        } else {
            $data['transaction_type'] = "dr";
            $ledger_id = $data['from_ledger_id'];
            $ledger_name = $data['from_ledger_name'];
        }

        // Ensure data is an array
        $data = json_decode(json_encode($data), true);
        $soc_id = $data['soc_id'];

        // Check for opening balance transaction
        if (!empty($data['is_opening']) && $data['is_opening'] == 1) {
            $conditions = [
                ['soc_id', '=', $soc_id],
                ['is_opening_balance', '=', 1],
                ['ledger_account_id', '=', $ledger_id],
            ];

            $txn_entry = $this->tenantDB()->table('chsone_ledger_transactions')
                ->where($conditions)
                ->first();

            $data['txn_id'] = $txn_entry->txn_id ?? '';
        }

        // Prepare the data for insertion
        $transactionData = [
            'soc_id' => (string) $soc_id,
            'voucher_type' => isset($data['voucher_type']) ? (string) $data['voucher_type'] : '',
            'voucher_reference_number' => (int) ($data['voucher_reference_number'] ?? 0),
            'voucher_reference_id' => (int) ($data['voucher_reference_id'] ?? 0),
            'transaction_date' => (string) $data['transaction_date'],
            'ledger_account_id' => (int) $ledger_id,
            'ledger_account_name' => (string) $ledger_name,
            'transaction_type' => (string) $data['transaction_type'],
            'payment_mode' => isset($data['payment_mode']) ? (string) $data['payment_mode'] : null,
            'payment_reference' => (string) $data['payment_reference'],
            'transaction_amount' => (float) ($data['transaction_amount'] ?? 0),
            'other_reference_id' => (string) ($data['other_payment_ref'] ?? ''),
            'txn_from_id' => (string) ($data['transaction_from_id'] ?? ''),
            'memo_desc' => (string) $data['narration'],
            'is_opening_balance' => (int) ($data['is_opening'] ?? 0),
            'is_reconciled' => (int) ($data['is_reconciled'] ?? 0),
            'created_by' => (int) ($data['user_id'] ?? 0),
            'added_on' => date('Y-m-d H:i:s'),
            'is_cancelled' => !empty($data['is_cancelled']) ? 1 : 0,
        ];

        // Log the transaction data to ensure it's correct before insertion
        // Log::info('Attempting transaction entry', ['transactionData' => $transactionData]);

        // Attempt to insert the transaction data
        $txn = $this->tenantDB()->table('chsone_ledger_transactions')->insertGetId($transactionData);

        // Return the transaction ID if successful, otherwise false
        return $txn ? $txn : false;
    }

    public function createNewLedgerExit($data = [])
    {
        // Extract necessary variables from the data array
        $soc_id = $data['soc_id'];
        $ledger_name = $data['ledger_name'];
        $parent_id = $data['parent_id'];
        $behaviour = $data['behaviour'];
        $context = $data['context'];

        // Check whether the ledger already exists
        $objBookerLedger = $this->tenantDB()->table('chsone_grp_ledger_tree')
            ->where('soc_id', $data['soc_id'])
            ->where('entity_type', ENTITY_TYPE_LEDGER)
            ->where('ledger_account_name', $ledger_name)
            ->where('parent_id', $parent_id)
            ->where('behaviour', $behaviour)
            ->first();

        // If the ledger exists, return its account ID
        if (!empty($objBookerLedger)) {
            return $objBookerLedger->ledger_account_id;
        }

        // Create a new ledger if it does not exist
        $ledger_account_id = $this->manipulate($ledger_name, ENTITY_TYPE_LEDGER, $parent_id, $behaviour, $context);

        // Return the new ledger ID if creation is successful
        if ($ledger_account_id && strpos($ledger_account_id, 'DUP') === false) {
            return $ledger_account_id;
        }

        // Return false if the ledger creation fails
        return false;
    }

/**
 * Handles the manipulation of the ledger, creating a new entry if necessary.
 */
    public function manipulate($name, $entity_type = ENTITY_TYPE_GROUP, $grp_ledg_id = "", $parent_group = 0, $behaviour = "", $ledg_id = '', $opening_balance = 0, $update_led_id = '', $ledger_start_date = '', $ledger_type = '', $context = '', $txn_id = 0, $soc_id = 0, $is_reco = 0)
    {
        try {
            global $config, $di;
            $dup = $this->_checkGroupNameDuplication($parent_group, $name, $ledg_id, $update_led_id, $soc_id);
            $arrFYDetail = $this->getCurrentFinancialYear(['soc_id' => $soc_id]);
            $ledger_start_date = $arrFYDetail['fy_start_date'];

            if ($dup == 0) {
                $behaviour = trim(strtolower($behaviour));

                // Check if we need to update or create a new ledger
                $grp_ledg_tree = $this->tenantDB()->table('chsone_grp_ledger_tree');
                if ($ledg_id) {
                    $grp_ledg_tree = $this->getLedger($ledg_id);
                }
                // Prepare data for insertion or update
                $grpLedgData = [
                    'entity_type' => $entity_type,
                    'soc_id' => $soc_id,
                    'ledger_account_name' => $name,
                    'ledger_start_date' => $ledger_start_date,
                    'context_ref_id' => 0,
                    'operating_type' => (!empty($ledger_type) && $ledger_type != "NULL" && $ledger_type != '') ? $ledger_type : '',
                    'parent_id' => !empty($parent_group) ? $parent_group : HEAD_GROUP_VAL,
                    'behaviour' => !empty($behaviour) ? $behaviour : $this->_getLedgerProps($parent_group)['behaviour'],
                    'nature_of_account' => $config->nature_account->$behaviour ?? $this->_getLedgerProps($parent_group)['nature_account'],
                    'report_head' => $config->report_head->$behaviour ?? $this->_getLedgerProps($parent_group)['report_head'],
                    'context' => !empty($context) ? $context : $this->_getLedgerProps($parent_group)['context'],
                    'defined_by' => USER,
                    'status' => ACTIVE,
                    'added_on' => date("Y-m-d H:i:s"),
                    'created_by' => 0,
                ];

                // Set ledger_account_id if provided
                if (!empty($grp_ledg_id)) {
                    $grpLedgData['ledger_account_id'] = $grp_ledg_id;
                }
                if ($update_led_id != '') {
                    $grpLedgData['ledger_account_id'] = $update_led_id;
                }

                // Insert or update ledger in the database
                $result = $grp_ledg_tree->insertOrIgnore($grpLedgData);

                if ($result) {
                    // Handle transaction for opening balance if applicable
                    if (!in_array(strtolower($grpLedgData['behaviour']), [INCOME, EXPENSE])) {
                        $txn_date = $ledger_start_date;
                        $narration = 'entry for opening balance';
                        if ($txn_id = $this->addTxn($grpLedgData['ledger_account_id'], $opening_balance, $narration, $txn_date, "", $grpLedgData['nature_of_account'], "", "", "", "", $name, $is_opning = 1, $is_reco, $soc_id)) {
                            // Transaction successful
                        } else {
                            // Handle failure
                        }
                    }
                    return $grpLedgData['ledger_account_id'];
                } else {
                    // Handle save failure
                    return false;
                }
            } else {
                return "DUP" . $dup;
            }
        }catch(\Exception $e) {
            dd($e);
        }
    }

    // public function payNonmemberBillLedger($data = [])
    // {
    //     $success = 1;

    //     // Check if the account module exists
    //     if (ACCOUNT_MODULE_EXIST != 1) {
    //         return $success;
    //     }

    //     $postData = $data['postData'];

    //     // Handle empty bill number
    //     $postData['bill_number'] = $postData['bill_number'] ?? $postData['invoice_number'];

    //     // Set payment date to clearance date if available
    //     $postData['payment_date'] = $postData['clearance_date'] ?? $postData['payment_date'];

    //     $strNarration = '';

    //     // Fetch or set Booker Ledger Details
    //     $arrBookerLedgerDetails = $data['arrBookerLedgerDetails'] ?? $this->getBookerLedgerDetails($data['soc_id'], $postData);

    //     dd($arrBookerLedgerDetails);
    //     // Get bank/cash ledger details
    //     $arrAccountDetail = $this->getBankCashAccountDetail(['soc_id' => $data['soc_id']]);

    //     $arrLedgerAccountDetail = $this->getLedgerAccountDetail(['account_detail' => $arrAccountDetail]);

    //     // Prepare income account details
    //     $arrIncomeAccounts = $this->prepareIncomeAccountDetails($postData, $arrLedgerAccountDetail);

    //     // Prepare listener data for transaction entry
    //     $arrListnerData = $this->prepareListenerData($data['soc_id'], $postData, $arrIncomeAccounts, $arrBookerLedgerDetails, $strNarration);

    //     // Handle TDS deduction if applicable
    //     if (!empty($postData['tds_amount']) && $postData['tds_amount'] > 0) {
    //         $success = $this->handleTdsDeduction($postData, $data['soc_id'], $arrListnerData, $strNarration);
    //     } else {
    //         // Perform ledger transaction entry
    //         $success = $this->performLedgerTransactionEntry($arrListnerData);
    //     }

    //     return $success;
    // }


    public function payNonmemberBillLedger($data = array()){
        $success = 1;
        if(ACCOUNT_MODULE_EXIST == 1)
        {
            $PostData = $data['postData'];
            if(empty($PostData['bill_number']))
            {
                $PostData['bill_number'] = $PostData['member_paid_invoice'] ?? null;
            }
            if(isset($PostData ['clearance_date']) && !empty($PostData ['clearance_date'])) {
                $PostData ['payment_date'] = $PostData ['clearance_date'];
            }
            $strNarration = '';

            if(empty($data['arrBookerLedgerDetails']))
            {
                $arrListnerData['soc_id'] = $data['soc_id'];
                $arrListnerData['ledger_name'] = $PostData['booker_name'];
                $arrBookerLedgerDetails = $this->checkledgerExist($arrListnerData);
            }
            else
            {
                $arrBookerLedgerDetails = $data['arrBookerLedgerDetails'];
            }

            //$arrIncomeAccounts = $data['arrIncomeAccounts'];

            //Getting bank/cash ledger details
            $arrAccountDetail = $this->getBankCashAccountDetail( array('soc_id'=>$data['soc_id'])); //get all Unit details

            $arrLedgerAccountDetail = $this->getLedgerAccountDetail(array('account_detail'=>$arrAccountDetail)); //get all Unit details

            if(!empty($arrLedgerAccountDetail))
            {

                $arrIncomeAccounts['ledger_id'] = $arrLedgerAccountDetail['cash']['ledger_id'];
                $arrIncomeAccounts['ledger_name'] = $arrLedgerAccountDetail['cash']['ledger_name'];
                if(strtolower($PostData['payment_mode']) != 'cash')
                {
                    if(!empty($PostData['bank_account']))
                    {
                        $arrIncomeAccounts['ledger_id'] = $PostData['bank_account'];
                        $arrIncomeAccounts['ledger_name'] = $arrLedgerAccountDetail['arrBank'][$PostData['bank_account']];
                    }
                    else
                    {
                        $arrIncomeAccounts['ledger_id'] = $arrLedgerAccountDetail['bank']['ledger_id'];
                        $arrIncomeAccounts['ledger_name'] = $arrLedgerAccountDetail['bank']['ledger_name'];
                    }

                    $strNarration = ' with transaction ref. (' . $PostData['transaction_reference'] .
                    (isset($PostData['payment_instrument']) ? ', ' . $PostData['payment_instrument'] : '') . ')';
                                    if(!empty($PostData['payment_mode']) && strtolower($PostData['payment_mode']) == 'cashtransfer')
                    {
                        $strNarration = ' with payment ref. ('.$PostData['transaction_reference'].')';
                    }
                }
            }

            $arrListnerData = [];
            $arrListnerData['soc_id'] = $data['soc_id'];
            $arrListnerData['voucher_type'] =isset($PostData['voucher_type_credit'])?VOUCHER_CREDIT:VOUCHER_RECEIPT;
            $arrListnerData['voucher_reference_number'] = $PostData['receipt_number'];
            $arrListnerData['voucher_reference_id'] = $PostData['payment_tracker_id'];
            $arrListnerData['from_ledger_id'] = $arrIncomeAccounts['ledger_id'];
            $arrListnerData['to_ledger_id'] = $arrBookerLedgerDetails['recieving_ledger_id'];

            $arrListnerData['transaction_date'] = !empty($PostData['payment_date']) ? $this->getDatabaseDate($PostData['payment_date']) : $this->getCurrentDate('database');
            $arrListnerData['transaction_amount'] = $PostData['payment_amount'];

            if($PostData['payment_mode']=='cashtransfer'){$payment_mode_display=DISPLAY_CASH_TRANSFER;}else{$payment_mode_display=ucfirst($PostData['payment_mode']);}


            $arrListnerData['narration'] = '(Receipt No-'.$PostData['receipt_number'].') Amount received against Invoice '.$PostData['bill_number'].' dated '.$this->getCurrentDate('display').' through '.$payment_mode_display.$strNarration;//$key . ' bill pay';//$PostData['narration'];
            $arrListnerData['from_ledger_name'] = $arrIncomeAccounts['ledger_name'];
            $arrListnerData['to_ledger_name'] = $arrBookerLedgerDetails['receiver_name'];
            $arrListnerData['payment_reference'] = $PostData['transaction_reference'];
            $arrListnerData['transaction_type'] = $PostData['transaction_type'];
            $arrListnerData['mode_of_payment'] = $PostData['payment_mode'];

            $arrListnerData['other_payment_ref'] = $PostData['other_payment_ref'] ?? null;

            if (!empty($PostData ['payment_note'])) {
                $arrListnerData ['narration'] .= ' ['.$PostData ['payment_note'].']';
            }
            //Code to replace from ledger id From Bank/Cash
            if(isset($data['from_ledger'])){
                $arrListnerData['from_ledger_id'] = $data['from_ledger']['recieving_ledger_id'];
                $arrListnerData['from_ledger_name'] = $data['from_ledger']['receiver_name'];
            }
            if(isset($data['to_ledger'])){
                $arrListnerData['to_ledger_id'] = $data['to_ledger']['recieving_ledger_id'];
                $arrListnerData['to_ledger_name'] = $data['to_ledger']['receiver_name'];
            }


            if(isset($data['narration'])){
                $arrListnerData['narration'] = '(Receipt No-'.$PostData['receipt_number'].') '.$data['narration'];
            }

            if(!empty($PostData['tds_amount']) && $PostData['tds_amount']>0)
            {

                $arrListnerData['transaction_amount'] = round($PostData['payment_amount']-$PostData['tds_amount'],2);
                //print_r($arrListnerData);
                $arrLedgerEntry = $this->transactionLedgerEntry( $arrListnerData);
                if (isset($arrLedgerEntry['error']) && !empty($arrLedgerEntry['error'])) {
                    $success = 0;
                }
                else
                {
                    $arrListnerData['transaction_amount'] = $PostData['tds_amount'];
                    $arrLedgerData['soc_id'] = $data['soc_id'];
                    $arrLedgerData['ledger_name'] = 'tds receivable';
                    $arrLedgerData['context'] = '';//CURRENT_ASSETS_GROUP;
                    $arrLedgerData['behaviour'] = ASSET;

                    $arrTdsLedger = $this->checkledgerExistNew($arrLedgerData);
                    $arrListnerData['from_ledger_id'] = $arrTdsLedger['recieving_ledger_id'];
                    $arrListnerData['from_ledger_name'] = $arrTdsLedger['receiver_name'];

                    if($PostData['payment_mode']=='cashtransfer'){$payment_mode_display=DISPLAY_CASH_TRANSFER;}else{$payment_mode_display=ucfirst($PostData['payment_mode']);}


                    $arrListnerData['narration'] = 'TDS amount deducted against Invoice '.(isset($PostData['bill_number']) ? ', ' . $PostData['bill_number'] : '') .' dated '.$this->getCurrentDate('display').' through '.$payment_mode_display.$strNarration;//'Invoice '.$arrPostData['member_paid_invoice'].' '.$eachLedgerDetail['particular'].' Write Off';

                    //print_r($arrListnerData);
                    $arrLedgerEntry = $this->transactionLedgerEntry( $arrListnerData);
                    if (isset($arrLedgerEntry['error']) && !empty($arrLedgerEntry['error'])) {
                        $success = 0;
                    }
                }
            }
            else
            {
                $arrLedgerEntry = $this->transactionLedgerEntry( $arrListnerData);
                if (isset($arrLedgerEntry['error']) && !empty($arrLedgerEntry['error'])) {
                    $success = 0;
                }
            }
        }
        //exit;
        return $success;
    }

    public function getBookerLedgerDetails($soc_id, $postData)
    {
        $arrListnerData = [
            'soc_id' => $soc_id,
            'ledger_name' => $postData['booker_name'],
        ];
        return $this->checkledgerExist($arrListnerData);
    }

    public function prepareIncomeAccountDetails($postData, $arrLedgerAccountDetail)
    {
        $arrIncomeAccounts['ledger_id'] = $arrLedgerAccountDetail['cash']['ledger_id'];
        $arrIncomeAccounts['ledger_name'] = $arrLedgerAccountDetail['cash']['ledger_name'];

        if (strtolower($postData['payment_mode']) !== 'cash') {
            if (!empty($postData['bank_account'])) {
                $arrIncomeAccounts['ledger_id'] = $postData['bank_account'];
                $arrIncomeAccounts['ledger_name'] = $arrLedgerAccountDetail['arrBank'][$postData['bank_account']];
            } else {
                $arrIncomeAccounts['ledger_id'] = $arrLedgerAccountDetail['bank']['ledger_id'];
                $arrIncomeAccounts['ledger_name'] = $arrLedgerAccountDetail['bank']['ledger_name'];
            }
        }

        return $arrIncomeAccounts;
    }

    public function prepareListenerData($soc_id, $postData, $arrIncomeAccounts, $arrBookerLedgerDetails, &$strNarration)
    {

        $strNarration = ' with transaction ref. (' . $postData['transaction_reference'] . ', ' . $postData['payment_instrument'] ?? null . ')';
        if (strtolower($postData['payment_mode']) === 'cashtransfer') {
            $strNarration = ' with payment ref. (' . $postData['transaction_reference'] . ')';
        }

        return [
            'soc_id' => $soc_id,
            'voucher_type' => isset($postData['voucher_type_credit']) ? VOUCHER_CREDIT : VOUCHER_RECEIPT,
            'voucher_reference_number' => $postData['receipt_number'],
            'voucher_reference_id' => $postData['payment_tracker_id'],
            'from_ledger_id' => $arrIncomeAccounts['ledger_id'],
            'to_ledger_id' => $arrBookerLedgerDetails['recieving_ledger_id'],
            'transaction_date' => !empty($postData['payment_date']) ? $this->getDatabaseDate($postData['payment_date']) : $this->getCurrentDate('database'),
            'transaction_amount' => $postData['payment_amount'],
            'narration' => '(Receipt No-' . $postData['receipt_number'] . ') Amount received against Invoice ' . $postData['bill_number'] . ' dated ' . $this->getCurrentDate('display') . ' through ' . ucfirst($postData['payment_mode']) . $strNarration,
            'from_ledger_name' => $arrIncomeAccounts['ledger_name'],
            'to_ledger_name' => $arrBookerLedgerDetails['receiver_name'],
            'payment_reference' => $postData['transaction_reference'],
            'transaction_type' => $postData['transaction_type'],
            'mode_of_payment' => $postData['payment_mode'],
            'other_payment_ref' => $postData['other_payment_ref'] ?? null,
        ];
    }

    public function handleTdsDeduction($postData, $soc_id, $arrListnerData, $strNarration)
    {
        $arrListnerData['transaction_amount'] = round($postData['payment_amount'] - $postData['tds_amount'], 2);
        $arrLedgerEntry = $this->transactionLedgerEntry($arrListnerData);
        if (isset($arrLedgerEntry['error']) && !empty($arrLedgerEntry['error'])) {
            return 0;
        }

        $arrListnerData['transaction_amount'] = $postData['tds_amount'];
        $arrLedgerData = [
            'soc_id' => $soc_id,
            'ledger_name' => 'tds receivable',
            'context' => '',
            'behaviour' => ASSET,
        ];

        $arrTdsLedger = $this->checkledgerExistNew($arrLedgerData);
        $arrListnerData['from_ledger_id'] = $arrTdsLedger['recieving_ledger_id'];
        $arrListnerData['from_ledger_name'] = $arrTdsLedger['receiver_name'];
        $arrListnerData['narration'] = 'TDS amount deducted against Invoice ' . $postData['bill_number'] . ' dated ' . $this->getCurrentDate('display') . ' through ' . ucfirst($postData['payment_mode']) . $strNarration;

        $arrLedgerEntry = $this->transactionLedgerEntry($arrListnerData);
        return (isset($arrLedgerEntry['error']) && !empty($arrLedgerEntry['error'])) ? 0 : 1;
    }

    public function performLedgerTransactionEntry($arrListnerData)
    {
        $arrLedgerEntry = $this->transactionLedgerEntry($arrListnerData);
        return (isset($arrLedgerEntry['error']) && !empty($arrLedgerEntry['error'])) ? 0 : 1;
    }

    public function checkledgerExist($data = [])
    {
        $arrClientLedgerDetails = [];
        $soc_id = $data['soc_id'];
        $ledgerName = $data['ledger_name'];

        // Check if the ledger already exists for the outsider based on provided criteria
        $existingLedger = $this->tenantDB()->table('chsone_grp_ledger_tree')
            ->where('soc_id', $data['soc_id'])
            ->where('context', 'sundrydebtors')
            ->where('entity_type', ENTITY_TYPE_LEDGER)
            ->where('ledger_account_name', $ledgerName)
            ->first();

        if (!empty($existingLedger)) {
            // Ledger exists, so set the receiving ledger details
            $arrClientLedgerDetails['recieving_ledger_id'] = $existingLedger->ledger_account_id;
            $arrClientLedgerDetails['receiver_name'] = $existingLedger->ledger_account_name;
        } else {
            // Ledger does not exist, create a new ledger under the Sundry Debtors group
            $sundryDebtorGroup = $this->tenantDB()->table('chsone_grp_ledger_tree')
                ->where('soc_id', $data['soc_id'])
                ->where('entity_type', ENTITY_TYPE_GROUP)
                ->where('ledger_account_name', SUNDRY_DR)
                ->first();

            if ($sundryDebtorGroup) {
                // Create a new ledger entry under the Sundry Debtors group
                $ledgerAccountId = $this->manipulate(
                    $ledgerName,
                    ENTITY_TYPE_LEDGER,
                    "",
                    $sundryDebtorGroup->ledger_account_id,
                    $sundryDebtorGroup->behaviour,
                    '',
                    0,
                    '',
                    '',
                    null,
                    $sundryDebtorGroup->context
                );

                // Check if the new ledger was created successfully
                if (is_string($ledgerAccountId) && strpos($ledgerAccountId, 'DUP') === false) {
                    $arrClientLedgerDetails['recieving_ledger_id'] = $ledgerAccountId;
                    $arrClientLedgerDetails['receiver_name'] = $ledgerName;
                }
            }
        }

        return $arrClientLedgerDetails;
    }

    public function saveCreditNote($data)
{
    // Initialize the response array
    $response = [
        'error' => true,
        'message' => '',
    ];

    try {
        // Create an array to hold the credit account data
        $creditAccount = [];

        // Set the properties from the request data
        $creditAccount['created_by'] = $data['user_id'] ?? 0; // Use authenticated user or passed user
        $creditAccount['updated_by'] = $data['user_id'] ?? 0; // Use authenticated user or passed user
        $creditAccount['created_date'] = date('Y-m-d H:i:s'); // Use current date-time
        $creditAccount['updated_date'] = date('Y-m-d H:i:s'); // Use current date-time
        $creditAccount['soc_id'] = $data['soc_id'] ?? null; // Ensure soc_id is present

        $creditAccount['invoice_number'] = $data['invoice_no'] ?? $data['invoice_number'] ?? null; // Use invoice_no if present, otherwise invoice_number
        $creditAccount['is_invoice_rectification'] = $data['is_invoice_rectification'] ?? null; // Default to null if not set
        $creditAccount['income_account_id'] = $data['income_account_ledger_id'] ?? null; // Default to null if not set
        $creditAccount['payment_tracker_id'] = $data['payment_tracker_id'] ?? null; // Default to null if not set
        $creditAccount['account_id'] = $data['account_id_member'] ?? $data['account_id_nonmember'] ?? null; // Ensure account_id is present
        $creditAccount['account_name'] = 'suraj data' ?? null; // Ensure account_name is present
        $creditAccount['account_context'] = $data['account_context'] ?? null; // Default to null if not set
        $creditAccount['amount'] = $data['payment_amount'] ?? 0; // Default to 0 if not set
        $creditAccount['payment_mode'] = $data['payment_mode'] ?? null; // Default to null if not set
        $creditAccount['payment_date'] = !empty($data['payment_date']) ? $this->getDatabaseDate($data['payment_date']) : null; // Use a method to format the date
        $creditAccount['transaction_type'] = $data['transaction_type'] ?? null; // Ensure transaction_type is present
        $creditAccount['narration'] = $data['narration'] ?? null; // Default to null if not set
        $creditAccount['use_credit'] = $data['credit_used_type'] ?? 'adjustable'; // Default to 'adjustable' if not set
        $creditAccount['use_credit_after'] = $data['adjustable_date'] ?? null; // Default to null if not set
        $creditAccount['is_locked'] = $data['is_locked'] ?? 0; // Default to 0 if not set
        $creditAccount['use_credit_for'] = $data['used_for'] ?? null; // Default to null if not set
        $creditAccount['reference_no'] = $data['transaction_reference'] ?? null; // Default to null if not set
        $creditAccount['context'] = $data['context'] ?? 'system'; // Default to 'system' if not set

        // Insert the data and get the inserted ID using insertGetId()
        $creditAccountId = $this->tenantDB()->table('chsone_credit_accounts')->insertGetId($creditAccount);

        // Save the CreditAccount and return a successful response
        if ($creditAccountId) {
            $response['error'] = false;
            $response['id'] = $creditAccountId;
        }

    } catch (\Exception $e) {
        // Handle exceptions and return an error response
        $response['message'] = $e->getMessage();
    }

    return $response;
}

}
