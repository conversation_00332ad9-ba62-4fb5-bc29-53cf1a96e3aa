<?php

namespace App\Console\Commands\Actions\Income;

use Illuminate\Console\Command;
use App\Console\Commands\Action;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Route;

class IncidentalBillListNewDataSource extends Action
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'datasource:IncidentalBillListNew {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Get the incidental bill list';
    protected $mapper = [
        'id' => 'units.id',
    ];

    /**
     * Execute the console command.
     */
    public function apply()
    {
        $companyId = $this->input['company_id'];
        $page = $this->input['page'] ?? 1;

        $currentRoute = Route::current();

            // Get the route URI pattern (e.g., "member/register/{id}")
        $routeUri = $currentRoute->uri();

        if ($routeUri == 'api/admin/admindashboard/index/balance_dues') {
                $this->hugeData = true;
        }

        $per_page = !empty($this->input['per_page']) ? $this->input['per_page'] : ($this->hugeData ? 100000 : 10);
        $offset = ($page - 1) * $per_page;

        $unit_flat_number = '';
        $soc_building_name = '';
        $unit_name =  '';
        $member_name = '';
        $invoice_number = '';
        $building_unit_number = '';
        $searchTerm = $this->input['filters']['search'] ?? '';

        if (isset($this->input['filters'])) {

            $invoice_number =
                !empty($this->input['filters']['invoice_number']) ? $this->input['filters']['invoice_number'] : '';
            unset($this->input['filters']['invoice_number']);

            $unit_flat_number = !empty($this->input['filters']['unit_flat_number']) ? $this->input['filters']['unit_flat_number'] : '';
            unset($this->input['filters']['unit_flat_number']);


            $soc_building_name = !empty($this->input['filters']['soc_building_name']) ? $this->input['filters']['soc_building_name'] : '';
            unset($this->input['filters']['soc_building_name']);


            $unit_name = !empty($this->input['filters']['unit_name']) ? $this->input['filters']['unit_name'] : '';
            unset($this->input['filters']['unit_name']);

            $member_name = !empty($this->input['filters']['member_name']) ? $this->input['filters']['member_name'] : '';
            unset($this->input['filters']['member_name']);


            $building_unit_number = !empty($this->input['filters']['building_unit_number']) ? $this->input['filters']['building_unit_number'] : '';
            unset($this->input['filters']['building_unit_number']);
        }

        $obj = $this->tenantDB()->table('chsone_units_master as units')
            ->select(
                'units.unit_id as id',
                'units.soc_id',
                'units.unit_flat_number',
                'units.soc_building_id',
                'units.soc_building_name',
                'memmaster.id as member_id',
                'memmaster.member_first_name',
                'memmaster.member_last_name',
                'memmaster.status',
                'memmaster.member_mobile_number',
            )
            ->selectRaw('MAX(icb.bill_date) AS last_bill_date')
            ->selectRaw('MAX(icb.id) AS icb_id')
            ->selectRaw("CONCAT(units.soc_building_name, ' / ', units.unit_flat_number) as unit_building_name")
            ->selectRaw("CONCAT(memmaster.member_first_name, ' ', memmaster.member_last_name) as member_name")
            ->selectRaw("CASE
                WHEN (
                    ROUND(
                        COALESCE(
                            (
                                SELECT SUM(
                                    CASE
                                        WHEN icb_inner.payment_status IN ('partialpaid', 'unpaid') THEN
                                            (COALESCE(icb_inner.amount, 0) + COALESCE(tax.total_tax_amount, 0))
                                            - COALESCE(payment.total_payment_amount, 0)
                                            - COALESCE(icb_inner.advance_amount, 0)
                                        ELSE 0
                                    END
                                )
                                FROM income_common_billing_charges as icb_inner
                                LEFT JOIN (
                                    SELECT invoice_number, SUM(tax_amount) AS total_tax_amount
                                    FROM chsone_tax_log
                                    GROUP BY invoice_number
                                ) as tax ON tax.invoice_number = icb_inner.invoice_number
                                LEFT JOIN (
                                    SELECT fk_common_bill_id, SUM(payment_amount) AS total_payment_amount
                                    FROM income_common_billing_payment
                                    GROUP BY fk_common_bill_id
                                ) as payment ON payment.fk_common_bill_id = icb_inner.id
                                WHERE icb_inner.fk_unit_id = units.unit_id
                                AND icb_inner.payment_status != 'cancelled'
                            ), 0
                        )
                    , 2)
                ) < 0
                THEN 0
                ELSE
                    ROUND(
                        COALESCE(
                            (
                                SELECT SUM(
                                    CASE
                                        WHEN icb_inner.payment_status IN ('partialpaid', 'unpaid') THEN
                                            (COALESCE(icb_inner.amount, 0) + COALESCE(tax.total_tax_amount, 0))
                                            - COALESCE(payment.total_payment_amount, 0)
                                            - COALESCE(icb_inner.advance_amount, 0)
                                        ELSE 0
                                    END
                                )
                                FROM income_common_billing_charges as icb_inner
                                LEFT JOIN (
                                    SELECT invoice_number, SUM(tax_amount) AS total_tax_amount
                                    FROM chsone_tax_log
                                    GROUP BY invoice_number
                                ) as tax ON tax.invoice_number = icb_inner.invoice_number
                                LEFT JOIN (
                                    SELECT fk_common_bill_id, SUM(payment_amount) AS total_payment_amount
                                    FROM income_common_billing_payment
                                    GROUP BY fk_common_bill_id
                                ) as payment ON payment.fk_common_bill_id = icb_inner.id
                                WHERE icb_inner.fk_unit_id = units.unit_id
                                AND icb_inner.payment_status != 'cancelled'
                            ), 0
                        )
                    , 2)
            END AS total_due_amount")
            ->selectRaw("CASE
                WHEN (
                    ROUND(
                        COALESCE(
                            (
                                SELECT SUM(
                                    CASE
                                        WHEN icb_inner.payment_status IN ('partialpaid', 'unpaid') THEN
                                            CASE
                                                WHEN ((COALESCE(icb_inner.amount, 0) + COALESCE(tax.total_tax_amount, 0))
                                                    - COALESCE(payment.total_payment_amount, 0)
                                                    - COALESCE(icb_inner.advance_amount, 0)) < 0
                                                THEN COALESCE(icb_inner.advance_amount, 0)
                                                ELSE 0
                                            END
                                        ELSE 0
                                    END
                                )
                                FROM income_common_billing_charges as icb_inner
                                LEFT JOIN (
                                    SELECT invoice_number, SUM(tax_amount) AS total_tax_amount
                                    FROM chsone_tax_log
                                    GROUP BY invoice_number
                                ) as tax ON tax.invoice_number = icb_inner.invoice_number
                                LEFT JOIN (
                                    SELECT fk_common_bill_id, SUM(payment_amount) AS total_payment_amount
                                    FROM income_common_billing_payment
                                    GROUP BY fk_common_bill_id
                                ) as payment ON payment.fk_common_bill_id = icb_inner.id
                                WHERE icb_inner.fk_unit_id = units.unit_id
                                AND icb_inner.payment_status != 'cancelled'
                            ), 0
                        )
                    , 2)
                ) < 0
                THEN 0
                ELSE
                    ROUND(
                        COALESCE(
                            (
                                SELECT SUM(
                                    CASE
                                        WHEN icb_inner.payment_status IN ('partialpaid', 'unpaid') THEN
                                            CASE
                                                WHEN ((COALESCE(icb_inner.amount, 0) + COALESCE(tax.total_tax_amount, 0))
                                                    - COALESCE(payment.total_payment_amount, 0)
                                                    - COALESCE(icb_inner.advance_amount, 0)) < 0
                                                THEN COALESCE(icb_inner.advance_amount, 0)
                                                ELSE 0
                                            END
                                        ELSE 0
                                    END
                                )
                                FROM income_common_billing_charges as icb_inner
                                LEFT JOIN (
                                    SELECT invoice_number, SUM(tax_amount) AS total_tax_amount
                                    FROM chsone_tax_log
                                    GROUP BY invoice_number
                                ) as tax ON tax.invoice_number = icb_inner.invoice_number
                                LEFT JOIN (
                                    SELECT fk_common_bill_id, SUM(payment_amount) AS total_payment_amount
                                    FROM income_common_billing_payment
                                    GROUP BY fk_common_bill_id
                                ) as payment ON payment.fk_common_bill_id = icb_inner.id
                                WHERE icb_inner.fk_unit_id = units.unit_id
                                AND icb_inner.payment_status != 'cancelled'
                            ), 0
                        )
                    , 2)
            END AS advance_amount")
            ->selectSub(function ($query) {
                $query->select('invoice_number')
                    ->from('income_common_billing_charges as icb_inner')
                    ->whereColumn('icb_inner.fk_unit_id', 'units.unit_id')
                    ->where('icb_inner.payment_status', '!=', 'cancelled')
                    ->whereColumn('icb_inner.id', DB::raw('MAX(icb.id)'));
            }, 'invoice_number')
            ->selectSub(function ($query) {
                $query->select('amount')
                    ->from('income_common_billing_charges as icb_inner')
                    ->whereColumn('icb_inner.fk_unit_id', 'units.unit_id')
                    ->where('icb_inner.payment_status', '!=', 'cancelled')
                    ->whereColumn('icb_inner.id', DB::raw('MAX(icb.id)'));
            }, 'last_invoice_amount')
            ->selectSub(function ($query) {
                $query->select('icb_inner.id')
                    ->from('income_common_billing_charges as icb_inner')
                    ->whereColumn('icb_inner.fk_unit_id', 'units.unit_id')
                    ->whereIn('icb_inner.payment_status', ['partialpaid', 'unpaid'])
                    ->orderBy('icb_inner.id', 'ASC')  // Ordering by id to get the oldest one
                    ->limit(1); // Limit to one id (if more than one match exists)
            }, 'payment_id')
            ->selectSub(function ($query) {
                $query->select('invoice_number')
                    ->from('income_common_billing_charges as icb_inner')
                    ->whereColumn('icb_inner.fk_unit_id', 'units.unit_id')
                    ->where('icb_inner.payment_status', '!=', 'cancelled')
                    ->whereColumn('icb_inner.id', DB::raw('MIN(icb.id)'));
            }, 'first_invoice_number')
            ->selectSub(function ($query) {
                $query->select('id')
                    ->from('income_common_billing_charges as icb_inner')
                    ->whereColumn('icb_inner.fk_unit_id', 'units.unit_id')
                    ->where('icb_inner.payment_status', '!=', 'cancelled')
                    ->whereColumn('icb_inner.id', DB::raw('MIN(icb.id)'));
            }, 'first_invoice_number_id')
            ->selectSub(function ($query) {
                $query->select('billing_type')
                    ->from('income_common_billing_charges as icb_inner')
                    ->whereColumn('icb_inner.fk_unit_id', 'units.unit_id')
                    ->where('icb_inner.payment_status', '!=', 'cancelled')
                    ->whereColumn('icb_inner.id', DB::raw('MIN(icb.id)'));
            }, 'billing_type')
            ->selectSub(function ($query) {
                $query->select('payment_status')
                    ->from('income_common_billing_charges as icb_inner')
                    ->whereColumn('icb_inner.fk_unit_id', 'units.unit_id')
                    ->where('icb_inner.payment_status', '!=', 'cancelled')
                    ->whereColumn('icb_inner.id', 'icb.id');
            }, 'payment_status')

            ->leftJoin('chsone_members_master as memmaster', 'units.unit_id', '=', 'memmaster.fk_unit_id')
            ->leftJoin('chsone_member_type_master as membertype', 'membertype.member_type_id', '=', 'memmaster.member_type_id')
            ->leftJoin('income_common_billing_charges as icb', 'icb.fk_unit_id', '=', 'units.unit_id')
            ->where('units.soc_id', $companyId)
            ->where('units.status', 1)
            ->where('units.is_allotted', 1)
            ->where('membertype.member_type_name', 'Primary')
            ->where('memmaster.status', 1)
            ->groupBy('units.unit_id');

        if ($unit_flat_number) {
            $obj = $obj->where('units.unit_flat_number', 'like', '%' . $unit_flat_number . '%');
        }

        if ($invoice_number) {
            $obj = $obj->where('icb.invoice_number', 'like', '%' . $invoice_number . '%');
        }

        if ($soc_building_name) {
            $obj = $obj->where('units.soc_building_name', 'like', '%' . $soc_building_name . '%');
        }


        if ($unit_name) {
            $obj = $obj->where(function ($query) use ($unit_name, $searchTerm) {
                $query->where(DB::raw("CONCAT(units.soc_building_name, ' / ', units.unit_flat_number)"), 'like', '%' . $unit_name . '%');
                    // ->orWhere('units.unit_flat_number', 'LIKE', '%' . $searchTerm . '%')
                    // ->orWhere('units.soc_building_id', 'LIKE', '%' . $searchTerm . '%')
                    // ->orWhere('units.soc_building_name', 'LIKE', '%' . $searchTerm . '%');
            });
        }

        if ($member_name) {
            $obj = $obj->where(DB::raw("CONCAT(memmaster.member_first_name, ' ', memmaster.member_last_name)"), 'like', '%' . $member_name . '%')
                ->orWhere('memmaster.member_first_name', 'LIKE', '%' . $member_name . '%')
                ->orWhere('memmaster.member_last_name', 'LIKE', '%' . $member_name . '%');
        }

        if ($building_unit_number) {
            $obj = $obj->where(DB::raw("CONCAT(units.soc_building_name, '/', units.unit_flat_number)"), 'like', '%' . $building_unit_number . '%');
        }

        if ($searchTerm) {
            $obj = $obj->where(function ($q) use ($searchTerm) {
                $q->orWhere('units.unit_id', 'LIKE', '%' . $searchTerm . '%')
                    ->orWhere('units.unit_flat_number', 'LIKE', '%' . $searchTerm . '%')
                    ->orWhere('units.soc_building_id', 'LIKE', '%' . $searchTerm . '%')
                    ->orWhere('units.soc_building_name', 'LIKE', '%' . $searchTerm . '%')
                    ->orWhere('memmaster.id', 'LIKE', '%' . $searchTerm . '%')

                    ->orWhere('memmaster.status', 'LIKE', '%' . $searchTerm . '%')
                    ->orWhere(DB::raw("CONCAT(units.soc_building_name, ' / ', units.unit_flat_number)"), 'like', '%' . $searchTerm . '%')
                    ->orWhere(DB::raw("CONCAT(memmaster.member_first_name, ' ', memmaster.member_last_name)"), 'like', '%' . $searchTerm . '%')
                    ->orWhere('memmaster.member_mobile_number', 'LIKE', '%' . $searchTerm . '%')
                    ->orWhere(DB::raw('COALESCE(
                        (
                            SELECT SUM(
                                CASE
                                    WHEN icb_inner.payment_status IN ("partialpaid", "unpaid") THEN
                                        (COALESCE(icb_inner.amount, 0) + COALESCE(tax.total_tax_amount, 0)) - COALESCE(payment.total_payment_amount, 0) - COALESCE(icb_inner.advance_amount, 0)
                                    ELSE 0
                                END
                            )
                            FROM income_common_billing_charges as icb_inner
                            LEFT JOIN (
                                SELECT invoice_number, SUM(tax_amount) AS total_tax_amount
                                FROM chsone_tax_log
                                GROUP BY invoice_number
                            ) as tax ON tax.invoice_number = icb_inner.invoice_number
                            LEFT JOIN (
                                SELECT fk_common_bill_id, SUM(payment_amount) AS total_payment_amount
                                FROM income_common_billing_payment
                                GROUP BY fk_common_bill_id
                            ) as payment ON payment.fk_common_bill_id = icb_inner.id
                            WHERE icb_inner.fk_unit_id = units.unit_id
                            AND icb_inner.payment_status != "cancelled"
                        ), 0
                    )'), 'LIKE', '%' . $searchTerm . '%')  // Search by total_due_amount
                    ->orWhere(DB::raw('COALESCE(
                        (
                            SELECT SUM(
                                CASE
                                    WHEN icb_inner.payment_status IN ("partialpaid", "unpaid") THEN
                                        CASE
                                            WHEN ((COALESCE(icb_inner.amount, 0) + COALESCE(tax.total_tax_amount, 0)) - COALESCE(payment.total_payment_amount, 0) - COALESCE(icb_inner.advance_amount, 0)) < 0 THEN COALESCE(icb_inner.advance_amount, 0)
                                            ELSE 0
                                        END
                                    ELSE 0
                                END
                            )
                            FROM income_common_billing_charges as icb_inner
                            LEFT JOIN (
                                SELECT invoice_number, SUM(tax_amount) AS total_tax_amount
                                FROM chsone_tax_log
                                GROUP BY invoice_number
                            ) as tax ON tax.invoice_number = icb_inner.invoice_number
                            LEFT JOIN (
                                SELECT fk_common_bill_id, SUM(payment_amount) AS total_payment_amount
                                FROM income_common_billing_payment
                                GROUP BY fk_common_bill_id
                            ) as payment ON payment.fk_common_bill_id = icb_inner.id
                            WHERE icb_inner.fk_unit_id = units.unit_id
                            AND icb_inner.payment_status != "cancelled"
                        ), 0
                    )'), 'LIKE', '%' . $searchTerm . '%');  // Search by advance_amount
            });
        }

        $count = $obj->get()->count();

        $obj = $obj->offset($offset);
        $obj = $obj->limit($per_page);
        $result = $obj->get();
        // // now need to calculate the total due amount according to the unit id
        $this->data = $result;
        $this->meta['pagination']['total'] = $count;
    }
}
