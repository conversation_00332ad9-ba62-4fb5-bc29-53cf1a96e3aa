<?php

namespace App\Console\Commands\Actions\Income;

use Illuminate\Console\Command;
use App\Console\Commands\Action;
use App\Models\Tenants\IncomeInvoicePaymentTracker;
use App\Models\Tenants\IncomeInvoiceSetting;
use Illuminate\Support\Facades\Config;

class IncidentalLastPeriodPaymentTransactionDataSource extends Action
{
    protected $constants;

    public function __construct()
    {
        parent::__construct();
        $this->constants = Config::get("constants");
    }

    protected $signature = 'datasource:IncidentalLastPeriodPaymentTransaction {flowId} {parentId} {input}';

    protected $description = 'Get the incidental bill details';

    public function apply()
    {
        $data = $this->input;

        $arrPaymentTransactionDetail = array();
        $receipt_from_req = !empty($data['receipt_form']) ? $data['receipt_form'] : 'maintenance';

        if(!empty($data)) {
            $numberOfMonth = $this->getMonthByInvoiceSetting(array('soc_id' => $data['company_id']));

            if (!empty($numberOfMonth)) {
                $invoiceStartDate = $this->getInvoicePeriod(array(
                    'soc_id' => $data['company_id'],
                    'date' => $data['from_date'],
                    'type' => 'subtract',
                    'frequency' => $numberOfMonth
                ));

                if (!empty($invoiceStartDate)) {
                    if (in_array($data['company_id'], array('5215', '7220', '7350', '30')) && $receipt_from_req != 'incidental') {
                        if ($data['company_id'] == 7220 && $data['from_date'] < '2021-04-30') {
                            $invoiceStartDate = $data['from_date'];
                            // $tempDate = new \DateTime($data['from_date']);
                            // $endDate = new \DateTime($data['from_date']);
                            $tempDate = $data['from_date'];
                            $endDate = $data['from_date'];
                            $invoiceStartDate = date('Y-m-d', strtotime("-3 months", strtotime($tempDate)));
                            $endDate = date('Y-m-d', strtotime("-1 day", strtotime($endDate)));

                        } else if($data['company_id'] == 30) {
                            if($numberOfMonth == 1 || $numberOfMonth == 3){
                                $invoiceStartDate = $data['from_date'];
                                // $tempDate = new \DateTime($data['from_date']);
                                // $endDate = new \DateTime($data['from_date']);
                                $tempDate = $data['from_date'];
                                $endDate = $data['from_date'];
                                $invoiceStartDate = date('Y-m-d', strtotime("-3 months", strtotime($tempDate)));
                                $endDate = date('Y-m-d', strtotime("-1 day", strtotime($endDate)));
                            }
                        } else {
                            if ($data['company_id'] == 5215 || $data['company_id'] == 7350) {
                                $endDate = date('Y-m-d');
                            } else {
                                // $endDate = new \DateTime($data['from_date']);
                                $endDate = $data['from_date'];
                            }
    
                            $endDate = date('Y-m-d', strtotime("-1 day", strtotime($endDate)));
                        }
                    } else {
                        $endDate = $data['from_date'];
                        $endDate = date('Y-m-d', strtotime("-1 day", strtotime($endDate)));
                    }
    
                    // For today/now, don't pass an arg.
                    // Invoice period detail
                    $arrPaymentTransactionDetail['invoice_period_detail'] = array(
                        'soc_id' => $data['company_id'],
                        'from_date' => $invoiceStartDate,
                        'to_date' => $endDate,
                        'unit_id' => $data['unit_id'],
                        'payment_status' => 'y'
                    );
    
                    // Get successful transaction detail
                    $arrPaymentTransactionDetail['payment_transaction_detail'] = $this->getPaymentTransactionByPeriod($arrPaymentTransactionDetail['invoice_period_detail']);
                    $arrPaymentTransactionDetail['total'] = 0;

                    // Sum of total paid amount
                    if (!empty($arrPaymentTransactionDetail['payment_transaction_detail'])) {
                        foreach ($arrPaymentTransactionDetail['payment_transaction_detail'] as $key => $eachTransactionDetail) {
                            $receipt_form = '';
                            if (!empty($eachTransactionDetail['bill_type'])) {
                                switch (strtolower($eachTransactionDetail['bill_type'])) {
                                    case 'member':
                                        $receipt_form  = 'Maintenance';
                                        break;
                                    case 'nonmember':
                                        $receipt_form  = 'Non Member';
                                        break;
                                    case 'common_bill':
                                    case 'common_bill_quickpay':
                                        $receipt_form  = 'Incidental';
                                        break;
                                    case 'creditaccount-nonmember':
                                        $receipt_form  = 'Non Member Advance';
                                        break;
                                    case 'creditaccount-member':
                                        $receipt_form  = 'Member Advance';
                                        break;
                                    case 'member_common_bill':
                                        $receipt_form  = 'Maintenance / Incidental';
                                        break;
                                    case 'suspense':
                                        $receipt_form  = 'Suspense';
                                        break;
                                }
                            }
    
                            if (($receipt_from_req  == 'incidental' && $receipt_form == 'Incidental') || ($receipt_from_req  == 'maintenance' && ($receipt_form != 'Incidental' || !empty($data['setting']['INCOME_MAINTENANCE_SHOW_INCIDENTAL_RECEIPT'])))) {
    
                                // $eachTransactionDetail ['payment_instrument']
                                if (in_array(strtolower($eachTransactionDetail['payment_instrument']), array(strtolower(YES_BANK_PG), strtolower(YES_BANK_ECOLLECT), strtolower(PAYTM_PG), strtolower(MOBIKWIK_PG), strtolower(CASHFREE_PG), strtolower(HDFC_PG), strtolower(ATOM_PG)))) {
    
                                    if (strtolower($eachTransactionDetail['payment_instrument']) == strtolower(YES_BANK_PG)) {
                                        $payment_instrument_display = DISPLAY_YES_BANK;
                                    } elseif (strtolower($eachTransactionDetail['payment_instrument']) == strtolower(YES_BANK_ECOLLECT)) {
                                        $payment_instrument_display = DISPLAY_YES_BANK_ECOLLECT;
                                    } elseif (strtolower($eachTransactionDetail['payment_instrument']) == strtolower(PAYTM_PG)) {
                                        $payment_instrument_display = DISPLAY_PAYTM;
                                    } elseif (strtolower($eachTransactionDetail['payment_instrument']) == strtolower(MOBIKWIK_PG)) {
                                        $payment_instrument_display = DISPLAY_MOBIKWIK_PG;
                                    } elseif (strtolower($eachTransactionDetail['payment_instrument']) == strtolower(CASHFREE_PG)) {
                                        $payment_instrument_display = DISPLAY_CASHFREE_PG;
                                    } elseif (strtolower($eachTransactionDetail['payment_instrument']) == strtolower(HDFC_PG)) {
                                        $payment_instrument_display = DISPLAY_HDFC_PG;
                                    } elseif (strtolower($eachTransactionDetail['payment_instrument']) == strtolower(ATOM_PG)) {
                                        $payment_instrument_display = DISPLAY_ATOM_PG;
                                    }
                                } else {
                                    $payment_instrument_display = $eachTransactionDetail['payment_instrument'];
                                }
                                // echo "<pre>";print_r($eachTransactionDetail['payment_mode']);exit;
                                /* payment mode */
                                if (in_array(strtolower($eachTransactionDetail['payment_mode']), array(strtolower(YES_BANK_PG), strtolower(YES_BANK_ECOLLECT), strtolower(PAYTM_PG), strtolower(MOBIKWIK_PG), strtolower(CASHFREE_PG), strtolower(HDFC_PG), strtolower(ATOM_PG)))) {
                                    if (strtolower($eachTransactionDetail['payment_mode']) == strtolower(YES_BANK_PG)) {
                                        $displayPayment_mode = DISPLAY_YES_BANK;
                                    } elseif (strtolower($eachTransactionDetail['payment_mode']) == strtolower(YES_BANK_ECOLLECT)) {
                                        $displayPayment_mode = DISPLAY_YES_BANK_ECOLLECT;
                                    } elseif (strtolower($eachTransactionDetail['payment_mode']) == strtolower(PAYTM_PG)) {
                                        $displayPayment_mode = DISPLAY_PAYTM;
                                    } elseif (strtolower($eachTransactionDetail['payment_mode']) == strtolower(MOBIKWIK_PG)) {
                                        $displayPayment_mode = DISPLAY_MOBIKWIK_PG;
                                    } elseif (strtolower($eachTransactionDetail['payment_mode']) == strtolower(CASHFREE_PG)) {
                                        $displayPayment_mode = DISPLAY_CASHFREE_PG;
                                    } elseif (strtolower($eachTransactionDetail['payment_mode']) == strtolower(HDFC_PG)) {
                                        $displayPayment_mode = DISPLAY_HDFC_PG;
                                    } elseif (strtolower($eachTransactionDetail['payment_mode']) == strtolower(ATOM_PG)) {
                                        $displayPayment_mode = DISPLAY_ATOM_PG;
                                    }
                                } else {
                                    $displayPayment_mode = $eachTransactionDetail['payment_mode'];
                                }
    
                                $arrPaymentTransactionDetail['payment_transaction_detail'][$key]['total_amount'] = number_format(round($eachTransactionDetail['payment_amount'], 2), 2, '.', '');
                                $arrPaymentTransactionDetail['payment_transaction_detail'][$key]['transaction_reference'] = (empty($eachTransactionDetail['transaction_reference'])) ? '-' : $eachTransactionDetail['transaction_reference'];
                                $arrPaymentTransactionDetail['payment_transaction_detail'][$key]['invoice_number'] = (empty($eachTransactionDetail['invoice_number'])) ? '-' : $eachTransactionDetail['invoice_number'];
                                $arrPaymentTransactionDetail['payment_transaction_detail'][$key]['payment_instrument'] = (empty($eachTransactionDetail['payment_instrument'])) ? '-' : $eachTransactionDetail['payment_instrument'];
                                if (empty($displayPayment_mode)) {
                                    $arrPaymentTransactionDetail['payment_transaction_detail'][$key]['payment_mode'] = '-';
                                } else {
                                    if (strtolower($displayPayment_mode) != 'cashtransfer') {
                                        $arrPaymentTransactionDetail['payment_transaction_detail'][$key]['payment_mode'] = ucfirst($displayPayment_mode);
                                    } else {
                                        $arrPaymentTransactionDetail['payment_transaction_detail'][$key]['payment_mode'] = DISPLAY_CASH_TRANSFER;
                                    }
                                }
                                $arrPaymentTransactionDetail['payment_transaction_detail'][$key]['payment_reference'] = (!empty($eachTransactionDetail['payment_mode']) && strtolower($eachTransactionDetail['payment_mode']) == 'cash') ? '-' : ucwords($eachTransactionDetail['transaction_reference'] . ' / ' . $payment_instrument_display);
    
                                $arrPaymentTransactionDetail['total'] += $arrPaymentTransactionDetail['payment_transaction_detail'][$key]['total_amount'];
                            } else {
    
                                unset($arrPaymentTransactionDetail['payment_transaction_detail'][$key]);
                            }
                        }
                    }
    
                    // Get total amount in words
                    if (!empty($arrPaymentTransactionDetail['total']) ) {
                        $arrPaymentTransactionDetail['total'] = number_format((float) $arrPaymentTransactionDetail['total'], 2, '.', '');
                        $arrPaymentTransactionDetail['rupeesInWords'] = $this->numberToWordRupees(array(
                            'number' => $arrPaymentTransactionDetail['total']
                        ));
                    }
                }
            }
        }

        $this->data = $arrPaymentTransactionDetail;
    }

    public function getMonthByInvoiceSetting($data)
    {
        $numberOfMonth = 0;
        if (empty($data['arrIncomeInvoiceSetting'])) {
            $arrIncomeInvoiceSetting = $this->getInvoiceSetting(array('soc_id' => $data['soc_id']));
        }

        // Get number of months
        if (!empty($arrIncomeInvoiceSetting)) {
            switch (strtolower($arrIncomeInvoiceSetting['invoicing_frequency'])) {
                case 'quarterly':
                    $numberOfMonth = 3;
                    break;
                case 'half_yearly':
                    $numberOfMonth = 6;
                    break;
                case 'yearly':
                    $numberOfMonth = 12;
                    break;
                case 'monthly':
                default:
                    $numberOfMonth = 1;
                    break;
            }
        }

        return $numberOfMonth;
    }

    public function getInvoiceSetting($data = array())
    {
        $arrInvoiceSetting = array();
        $objInvoiceSetting = IncomeInvoiceSetting::where('soc_id', $data['soc_id'])->first();
        
        if (!empty($objInvoiceSetting)) {
            $arrInvoiceSetting = $objInvoiceSetting->toArray();
        }
        return $arrInvoiceSetting;
    }

    public function getInvoicePeriod($data = array())
    {
        if (!empty($data['date'])) {
            if (strtolower($data['type']) == 'add') {
                // return $date->modify('+' . $data['frequency'] . ' month')->format('Y-m-d');
                return date('Y-m-d', strtotime('+' . $data['frequency'] . ' month', strtotime($data['date'])));
            } elseif (strtolower($data['type']) == 'subtract') {
                // return $date->modify('-' . $data['frequency'] . ' month')->format('Y-m-d');
                return date('Y-m-d', strtotime('-' . $data['frequency'] . ' month', strtotime($data['date'])));
            }
        }
        return false;
    }

    public function getPaymentTransactionByPeriod($data = array())
    {
        $arrIncomeInvoicePaymentTracker = array();

        $query = IncomeInvoicePaymentTracker::where('soc_id', $data['soc_id'])
        ->where('payment_date', '>=', $data['from_date'])
        ->where('payment_date', '<=', $data['to_date'])
        ->where('status', $data['payment_status']);

        // Add condition for unit_id if present
        if (!empty($data['unit_id'])) {
            $query->where('unit_id', $data['unit_id']);
        }

        $objIncomeInvoicePaymentTracker = $query->get();

        // Execute the query and return the result as an array
        if (!empty($objIncomeInvoicePaymentTracker)) {
            $arrIncomeInvoicePaymentTracker = $objIncomeInvoicePaymentTracker->toArray();
        }
        return $arrIncomeInvoicePaymentTracker;
    }

    public function numberToWordRupees($data = array())
    {
        $number = $data['number'];
        $decimal = round($number - ($no = floor($number)), 2) * 100;
        $hundred = null;
        $digits_length = strlen($no);
        $i = 0;
        $str = array();
        $words = array(
            0 => '', 1 => 'one', 2 => 'two',
            3 => 'three', 4 => 'four', 5 => 'five', 6 => 'six',
            7 => 'seven', 8 => 'eight', 9 => 'nine',
            10 => 'ten', 11 => 'eleven', 12 => 'twelve',
            13 => 'thirteen', 14 => 'fourteen', 15 => 'fifteen',
            16 => 'sixteen', 17 => 'seventeen', 18 => 'eighteen',
            19 => 'nineteen', 20 => 'twenty', 30 => 'thirty',
            40 => 'forty', 50 => 'fifty', 60 => 'sixty',
            70 => 'seventy', 80 => 'eighty', 90 => 'ninety'
        );
        $digits = array('', 'hundred', 'thousand', 'lakh', 'crore');
        while ($i < $digits_length) {
            $divider = ($i == 2) ? 10 : 100;
            $number = floor($no % $divider);
            $decNumber = floor($decimal % $divider);

            $no = floor($no / $divider);
            $decimal = floor($decimal / $divider);

            $i += $divider == 10 ? 1 : 2;
            //Rupees
            if ($number) {
                $plural = (($counter = count($str)) && $number > 9) ? '' : null;
                $hundred = ($counter == 1 && $str[0]) ? '' : null;
                $str[] = ($number < 21) ? $words[$number] . ' ' . $digits[$counter] . $plural . ' ' . $hundred : $words[floor($number / 10) * 10] . ' ' . $words[$number % 10] . ' ' . $digits[$counter] . $plural . ' ' . $hundred;
            } else
                $str[] = null;
            //Paise
            $decStr = array();
            if ($decNumber) {
                $plural = (($counter = count($decStr)) && $decNumber > 9) ? '' : null;
                $hundred = ($counter == 1 && $decStr[0]) ? '' : null;
                $decStr[] = ($decNumber < 21) ? $words[$decNumber] . ' ' . $digits[$counter] . $plural . ' ' . $hundred : $words[floor($decNumber / 10) * 10] . ' ' . $words[$decNumber % 10] . ' ' . $digits[$counter] . $plural . ' ' . $hundred;
            } else
                $decStr[] = null;
        }
        $Rupees = trim(implode('', array_reverse($str)), ' ');
        $Paise = trim(implode('', array_reverse($decStr)), ' ');

        $amountInWOrds = $Rupees ? $Rupees . ' Rupees ' : '';
        $amountInWOrds .= $Paise ? 'and ' . $Paise . ' Paise ' : '';
        //$paise = ($decimal) ? "and " . ($words[$decimal / 10] . " " . $words[$decimal % 10]) . ' Paise' : '';
        if (empty($amountInWOrds)) {
            $amountInWOrds = 'Zero Rupees';
        }
        return ucwords($amountInWOrds);
    }
}
