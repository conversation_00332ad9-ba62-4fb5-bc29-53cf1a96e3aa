<?php

namespace App\Console\Commands\Actions\Income;

use Illuminate\Console\Command;
use App\Console\Commands\Action;
use App\Models\Tenants\ChsoneCreditAccount;
use App\Models\Tenants\ChsoneNonmemberMaster;
use Illuminate\Support\Facades\DB;

class NonMemberMasterDataSource extends Action
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'datasource:NonMemberMaster {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Get Non Member Master Data Source List';

    protected $formatter = [
        "id" => "",
        "soc_id" => "",
        "nonmember_ledger_id" => "",
        "is_default_ledger" => "",
        "first_name" => "",
        "last_name" => "",
        "email_id" => "",
        "mobile_number" => "",
        "gender" => "",
        "gstin" => "",
        "place_of_supply" => "",
        "pan_number" => "",
        "created_by" => "",
        "created_date" => "",
        "updated_by" => "",
        "updated_date" => "",
        "hsn" => "",
        "address" => "",
        "name" => "",
    ];

    protected $formatterByKeys =  ['nonmember_id'];

    /**
     * Execute the console command.
     */
    public function apply()
    {
        $obj = $this->tenantDB()->table('chsone_nonmember_master')
        ->select('nonmember_id as id', 'soc_id', 'nonmember_ledger_id', 'is_default_ledger', 'first_name', 'last_name', 'email_id', 'mobile_number', 'gender', 'gstin', 'place_of_supply', 'pan_number', 'created_by', 'created_date', 'updated_by', 'updated_date', 'hsn', 'address')
        ->selectRaw('CONCAT_WS(" ", first_name, last_name) as name')
        ->orderBy('nonmember_id', 'desc')
        ->get();

        $this->data = $obj;
    }

    public function concat($a, $b)
    {
        return $a . " " . $b;
    }
}
