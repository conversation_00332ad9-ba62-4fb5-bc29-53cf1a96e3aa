<?php

namespace App\Console\Commands\Actions\Income;

use Illuminate\Support\Facades\DB;
use App\Console\Commands\Action;
use App\Models\Income\ChsoneInvoiceGeneration;
use App\Models\Tenants\ChsoneInvoiceGeneration as TenantsChsoneInvoiceGeneration;

class AddoutstandingDataSource extends Action
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'datasource:AddoutstandingDataSource {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Add outstanding data source';

    protected $formatter =  [];

    protected $formatterByKeys =  [];

    protected $mapper = [];

    /**
     * Execute the console command.
     */
    public function apply()
    {
        
        $addOutstandingDataSource = new TenantsChsoneInvoiceGeneration();
        $addOutstandingDataSource->setInput($this->input);
        $postedValues = request()->post();
        if (
            isset($postedValues['adjustment_type'], $postedValues['advance_amount']) &&
            $postedValues['adjustment_type'] === 'advance'
        ) {
            $postedValues['outstanding_amt'] = $postedValues['advance_amount'];
            unset($postedValues['advance_amount']);
        }
        
        $response = $addOutstandingDataSource->addOutstanding($this->input, $postedValues);

    }
}