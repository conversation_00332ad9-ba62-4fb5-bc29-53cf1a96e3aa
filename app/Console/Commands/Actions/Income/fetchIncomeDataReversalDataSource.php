<?php

namespace App\Console\Commands\Actions\Income;

use Illuminate\Console\Command;
use App\Console\Commands\Action;

class fetchIncomeDataReversalDataSource extends Action
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'datasource:fetchIncomeDataReversal {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Fetch Reversal Data Source';

    /**
     * Execute the console command.
     */
    public function apply()
    {
        $payment_tracker_id = $this->input['payment_tracker_id'];

        // check if unit id is valid or not from chsone_units_master table
        $paymentDetails = $this->tenantDB()->table('income_invoice_payment_tracker')
            ->where('id', $payment_tracker_id)
            ->get();
        
        if(count($paymentDetails) == 0) {
            $this->status = 'error';
            $this->message = 'Please provide a valid payment tracker id';
            $this->statusCode = 404;
            $this->data = [];
            return;
        }

        $obj = $this->tenantDB()->table('income_invoice_payment_tracker')
            ->selectRaw(
                'id,
                receipt_number,
                IFNULL(invoice_number, "N/A") as invoice_number,
                payment_mode,
                payment_amount',
            )
            ->where('id', $payment_tracker_id)
            ->first();

        $this->data = $obj;
    }
}
