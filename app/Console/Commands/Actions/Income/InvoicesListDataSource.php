<?php

namespace App\Console\Commands\Actions\Income;

use Illuminate\Console\Command;
use App\Console\Commands\Action;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Route;

class InvoicesListDataSource extends Action
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'datasource:InvoicesList {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Get the list of invoices by unit id';

    /**
     * Execute the console command.
     */
    public function apply()
    {
        $unitId = $this->input['unit_id'];
        $page = $this->input['page'] ?? 1;
        $per_page = empty($this->input['per_page']) ? ($this->hugeData ? 100000 : 10) : ($this->input['per_page']);
        $offset = ($page - 1) * $per_page;
        $currentRoute = Route::current();

            // Get the route URI pattern (e.g., "member/register/{id}")
        $routeUri = $currentRoute->uri();

        if ($routeUri == 'api/admin/society/income-details/memberInvoicelist') {
                $this->hugeData = true;
        }
        $invoice_number = '';
        $payment_status = '';

        if (isset($this->input['filters'])) {
            $invoice_number =
            !empty($this->input['filters']['invoice_number']) ? $this->input['filters']['invoice_number'] : '';
            unset($this->input['filters']['invoice_number']);

            $payment_status = isset($this->input['filters']['payment_status']) ? explode(',', $this->input['filters']['payment_status']) : [];
        }

        // check if unit id is valid or not from chsone_units_master table
        $unit = $this->tenantDB()->table('chsone_units_master')
            ->where('unit_id', $unitId)
            ->get();

        if(count($unit) == 0) {
            $this->status = 'error';
            $this->message = 'Please provide a valid unit id';
            $this->statusCode = 400;
            $this->data = [];
            return;
        } else {
            $obj = $this->tenantDB()->table('income_unit_invoices AS unitInvoices')
            ->select(
                'unitInvoices.unit_invoice_id as id',
                'unitInvoices.unit_invoice_id',
                'unitInvoices.fk_unit_id as unit_id',
                'units.soc_building_name',
                'units.unit_flat_number',
                DB::raw("CONCAT(units.soc_building_name, ' / ', units.unit_flat_number) as building_unit_number"),
                'unitInvoices.invoice_number',
                'unitInvoices.created_date',
                DB::raw('CONCAT(DATE_FORMAT(unitInvoices.created_date, "%d/%m/%Y")) AS created_date'),
                'unitInvoices.due_date',
                DB::raw('CONCAT(DATE_FORMAT(unitInvoices.due_date, "%d/%m/%Y")) AS due_date'),
                'unitInvoices.interest_amount',
                DB::raw('CAST(ROUND(unitInvoices.interest_amount, 3) AS DOUBLE) AS interest_amount'),
                'unitInvoices.advance_amount',
                'unitInvoices.roundoff_amount',
                'unitInvoices.payment_status',
                DB::raw("CASE
                    WHEN unitInvoices.unit_invoice_id = (
                        SELECT MIN(i2.unit_invoice_id)
                        FROM income_unit_invoices i2
                        WHERE i2.fk_unit_id = unitInvoices.fk_unit_id
                        AND i2.status != 'cancelled'
                    )
                    THEN unitInvoices.principal_amount
                    ELSE 0
                END AS principal_amount"),
                DB::raw('CONCAT(DATE_FORMAT(unitInvoices.from_date, "%d/%m/%Y"), " To ", DATE_FORMAT(unitInvoices.to_date, "%d/%m/%Y")) AS period'),
                DB::raw("SUM(particulars.amount) as amount"),
                DB::raw("SUM(particulars.tax_applicable) as tax_applicable"),
                DB::raw("SUM(particulars.tax_exemptions) as tax_exemptions"),
                DB::raw("(SELECT SUM(payment_amount) FROM income_invoice_payment WHERE fk_unit_invoice_id = unitInvoices.unit_invoice_id) AS payment_amount"),
                DB::raw("(SELECT SUM(writeoff_amount) FROM income_invoice_payment WHERE fk_unit_invoice_id = unitInvoices.unit_invoice_id) AS writeoff_amount"),
                DB::raw("(SELECT SUM(tds_deducted) FROM income_invoice_payment WHERE fk_unit_invoice_id = unitInvoices.unit_invoice_id) AS tds_deducted"),
                DB::raw("(SELECT SUM(late_payment_charges) FROM income_invoice_payment WHERE fk_unit_invoice_id = unitInvoices.unit_invoice_id) AS late_payment_charges"),
                DB::raw("(SELECT SUM(tax_amount) FROM chsone_tax_log WHERE invoice_number = unitInvoices.invoice_number) AS tax_amount"),
                DB::raw("
                    (
                        SUM(particulars.amount) + SUM(particulars.tax_applicable) + SUM(particulars.tax_exemptions) +
                        (
                            CASE
                                WHEN unitInvoices.unit_invoice_id = (
                                    SELECT MIN(i2.unit_invoice_id)
                                    FROM income_unit_invoices i2
                                    WHERE i2.fk_unit_id = unitInvoices.fk_unit_id
                                    AND i2.status != 'cancelled'
                                )
                                THEN unitInvoices.principal_amount
                                ELSE 0
                            END
                        )
                    ) AS invoice_sub_total
                "),
                DB::raw("
                    (
                        SUM(particulars.amount) + SUM(particulars.tax_applicable) + SUM(particulars.tax_exemptions) + unitInvoices.interest_amount +
                        (
                            CASE
                                WHEN unitInvoices.unit_invoice_id = (
                                    SELECT MIN(i2.unit_invoice_id)
                                    FROM income_unit_invoices i2
                                    WHERE i2.fk_unit_id = unitInvoices.fk_unit_id
                                    AND i2.status != 'cancelled'
                                )
                                THEN unitInvoices.principal_amount
                                ELSE 0
                            END
                        ) +
                        (SELECT IFNULL(SUM(tax_amount), 0) FROM chsone_tax_log WHERE invoice_number = unitInvoices.invoice_number)
                    ) AS invoice_total
                "),
                DB::raw("
                    CASE
                        WHEN
                        (
                            ROUND(
                                (
                                    SUM(particulars.amount) + SUM(particulars.tax_applicable) + SUM(particulars.tax_exemptions) + unitInvoices.interest_amount +
                                    (
                                        CASE
                                            WHEN unitInvoices.unit_invoice_id = (
                                                SELECT MIN(i2.unit_invoice_id)
                                                FROM income_unit_invoices i2
                                                WHERE i2.fk_unit_id = unitInvoices.fk_unit_id
                                                AND i2.status != 'cancelled'
                                            )
                                            THEN unitInvoices.principal_amount
                                            ELSE 0
                                        END
                                    ) +
                                    (SELECT IFNULL(SUM(tax_amount), 0) FROM chsone_tax_log WHERE invoice_number = unitInvoices.invoice_number)
                                ) + unitInvoices.roundoff_amount
                                - unitInvoices.advance_amount
                                - (
                                    (SELECT IFNULL(SUM(payment_amount), 0) FROM income_invoice_payment WHERE fk_unit_invoice_id = unitInvoices.unit_invoice_id)
                                    + (SELECT IFNULL(SUM(writeoff_amount), 0) FROM income_invoice_payment WHERE fk_unit_invoice_id = unitInvoices.unit_invoice_id)
                                    + (SELECT IFNULL(SUM(tds_deducted), 0) FROM income_invoice_payment WHERE fk_unit_invoice_id = unitInvoices.unit_invoice_id)
                                    + (SELECT IFNULL(SUM(late_payment_charges), 0) FROM income_invoice_payment WHERE fk_unit_invoice_id = unitInvoices.unit_invoice_id)
                                )
                            , 2)
                        ) < 0
                        THEN 0
                        ELSE
                        ROUND(
                            (
                                SUM(particulars.amount) + SUM(particulars.tax_applicable) + SUM(particulars.tax_exemptions) + unitInvoices.interest_amount +
                                (
                                    CASE
                                        WHEN unitInvoices.unit_invoice_id = (
                                            SELECT MIN(i2.unit_invoice_id)
                                            FROM income_unit_invoices i2
                                            WHERE i2.fk_unit_id = unitInvoices.fk_unit_id
                                            AND i2.status != 'cancelled'
                                        )
                                        THEN unitInvoices.principal_amount
                                        ELSE 0
                                    END
                                ) +
                                (SELECT IFNULL(SUM(tax_amount), 0) FROM chsone_tax_log WHERE invoice_number = unitInvoices.invoice_number)
                            ) + unitInvoices.roundoff_amount
                            - unitInvoices.advance_amount
                            - (
                                (SELECT IFNULL(SUM(payment_amount), 0) FROM income_invoice_payment WHERE fk_unit_invoice_id = unitInvoices.unit_invoice_id)
                                + (SELECT IFNULL(SUM(writeoff_amount), 0) FROM income_invoice_payment WHERE fk_unit_invoice_id = unitInvoices.unit_invoice_id)
                                + (SELECT IFNULL(SUM(tds_deducted), 0) FROM income_invoice_payment WHERE fk_unit_invoice_id = unitInvoices.unit_invoice_id)
                                + (SELECT IFNULL(SUM(late_payment_charges), 0) FROM income_invoice_payment WHERE fk_unit_invoice_id = unitInvoices.unit_invoice_id)
                            )
                        , 2)
                    END AS invoice_balance_due
                ")
            )
            ->join('income_invoice_particular AS particulars', 'unitInvoices.unit_invoice_id', '=', 'particulars.fk_unit_invoice_id')
            ->join('chsone_units_master AS units', 'unitInvoices.fk_unit_id', '=', 'units.unit_id')
            ->where('unitInvoices.fk_unit_id', $unitId)
            ->where('unitInvoices.status', '!=', 'cancelled')
            ->groupBy('particulars.fk_unit_invoice_id')
            ->orderByDesc('unitInvoices.created_date');

            if ($invoice_number) {
                $obj = $obj->where('unitInvoices.invoice_number', 'like', '%' . $invoice_number . '%');
            }

            if ($payment_status) {
                $obj = $obj->whereIn('unitInvoices.payment_status', $payment_status);
            }

            
            // Apply pagination
            //$obj = $obj->offset($offset)->limit($per_page);
            $result = $obj->get();
            // Get total count for pagination
            $count = $obj->count();
            $this->data = $result;

            $credit = $this->tenantDB()->table('chsone_credit_accounts')
                            ->selectRaw("SUM(amount) AS cr_amount")
                            ->where('transaction_type', 'cr')
                            ->where('is_locked', '!=', 1)
                            ->where('account_context', 'unit')
                            ->where('account_id', $unitId)
                            ->get();

            $debit = $this->tenantDB()->table('chsone_credit_accounts')
                            ->selectRaw("SUM(amount) AS dr_amount")
                            ->where('transaction_type', 'dr')
                            ->where('is_locked', '!=', 1)
                            ->where('account_context', 'unit')
                            ->where('account_id', $unitId)
                            ->get();

            // calculate credit balance
            $this->meta['info'] = [
                'title' => 'Credit Balance',
                'credit_balance' => '₹ '. number_format($credit[0]->cr_amount - $debit[0]->dr_amount, 2)
            ];

            // Add pagination metadata
            $this->meta['pagination'] = [
                'total' => $count,
                'per_page' => $per_page,
                'current_page' => $page,
                'last_page' => ceil($count / $per_page)
            ];
        }
    }
}


            //fetch credit balance of unit
            // $creditQuery = "SELECT
            //     SUM(amount) AS cr_amount
            // FROM
            // chsone_credit_accounts
            // WHERE
            //     transaction_type = 'cr'
            //         AND is_locked != 1
            //         AND account_context = 'unit'
            //         AND account_id = $unitId;";

            // $credit = $this->tenantDB()->select($creditQuery);

 // $debitQuery = "SELECT
            //     SUM(amount) AS dr_amount
            // FROM
            // chsone_credit_accounts
            // WHERE
            //     transaction_type = 'dr'
            //         AND is_locked != 1
            //         AND account_context = 'unit'
            //         AND account_id = $unitId;";

            // $debit = $this->tenantDB()->select($debitQuery);
