<?php

namespace App\Console\Commands\Actions\Income;

use Illuminate\Console\Command;
use App\Console\Commands\Action;
use App\Models\Tenants\ChsoneMembersMaster;
use App\Models\Tenants\ChsoneMemberTypeMaster;
use App\Models\Tenants\IncomeInvoicePaymentTracker;
use Illuminate\Support\Facades\Config;

class paymentTrackerConfirmationSubmitDataSource extends Action
{
    protected $constants;

    public function __construct() {
        parent::__construct();
        $this->constants = Config::get("constants");
    }

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'datasource:paymentTrackerConfirmationSubmit {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Datasource for income payment tracker confirmation submit';

    /**
     * Execute the console command.
     */
    public function apply()
    {
        $soc_id = $this->input['company_id'];
        $payment_tracker_id = $this->input['payment_tracker_id'];
        $payment_date = $this->input['receipt_date'];
        $current_date = date('Y-m-d');
        $bank_account = $this->input['bank_account'];

        // check the status of payment tracker in database
        $paymentDetails = $this->tenantDB()->table('income_invoice_payment_tracker')
            ->where('id', $payment_tracker_id)
            ->where('soc_id', $soc_id)
            ->select('status')
            ->first();

        if(empty($paymentDetails)) {
            $this->status = 'error';
            $this->message = 'No payment tracker found or provide valid payment tracker id.';
            $this->statusCode = 400;
            return;
        }
        
        if($paymentDetails->status == 'P') {
            $this->status = 'error';
            $this->message = 'Cheque payment already submitted';
            $this->statusCode = 400;
            return;
        }

        $arrPaymentTrackerListener = array('soc_id' => $soc_id, 'payment_tracker_id' => $payment_tracker_id, 'status' => 'R', 'current_date' => date('Y-m-d'));

        $arrPostData = $this->getInvoicePaymentTrackerDetail($arrPaymentTrackerListener);

        if (!empty($arrPostData)) {
            $unit_id = $arrPostData['unit_id'];
            $arrPostData['tds_amount'] = $arrPostData['tds_deducted'];
            $arrDataListener['soc_id'] = $soc_id;
            $arrDataListener['unit_id'] = $unit_id;
            $arrPostData['payment_tracker_id'] = $arrPostData['id'];
        } else {
            $this->status = 'error';
            $this->message = 'No payment tracker found or provide valid payment tracker id.';
            $this->statusCode = 400;
            return;
        }

        if (!empty($unit_id)) {
            $arrIncomeInvoiceMemberDetail = $this->getMemberDetail($arrDataListener);
            if (empty($arrIncomeInvoiceMemberDetail)) {
                $this->status = 'error';
                $this->message = 'Member not allotted to unit.';
                $this->statusCode = 400;
                return false;
            }
            $arrPostData['member_name'] = $arrIncomeInvoiceMemberDetail['member_first_name'] . ' ' . $arrIncomeInvoiceMemberDetail['member_last_name'];
        }

        if (!empty($payment_date)) {
            if ($payment_date < $arrPostData['payment_date']) {
                $this->status = 'error';
                $this->message = 'Cheque clearance date cannot be lesser than Cheque submission date.';
                $this->statusCode = 400;
                return;
            }
            if ($payment_date > $current_date) {
                $this->status = 'error';
                $this->message = 'Date cannot be greater than todays date.';
                $this->statusCode = 400;
                return;
            }
        }

        // Update pdc to submitted state
        $arrUpdatePaymentTrackerListener = array('soc_id' => $soc_id, 'payment_tracker_id' => $payment_tracker_id, 'status' => 'P');

        // Add/Update bank account details
        $arrPostData['other_information'][$arrPostData['bill_type'] . '_detail']['bank_ledger'] = $bank_account;
        $arrUpdatePaymentTrackerListener['other_information'] = serialize($arrPostData['other_information']);

        if (!empty($payment_date)) {
            $arrUpdatePaymentTrackerListener['payment_date'] = $payment_date;

            // Update payment tracker status
            $arrResponse = $this->updateIncomePaymentTrackerStatus($arrUpdatePaymentTrackerListener);

            if (!empty($arrResponse) && strtolower($arrResponse['status']) == 'success') {
                $this->status = 'success';
                $this->message = 'Cheque payment has submitted successfully.';
                $this->statusCode = 200;
                return;
            } else {
                $this->status = 'error';
                $this->message = 'Failed to submit cheque payment or cheque payment already submitted.';
                $this->statusCode = 400;
                return;
            }
        }
    }

    // Get invoice payment tracker detail
    public function getInvoicePaymentTrackerDetail($data = array())
    {
        $arrIncomeInvoicePaymentTracker = array();
        // Start building the query for IncomeInvoicePaymentTracker model
        $query = IncomeInvoicePaymentTracker::where('soc_id', $data['soc_id'])->where('id', $data['payment_tracker_id']);

        // Add conditions for unit_id if present
        if (isset($data['unit_id']) && !empty($data['unit_id'])) {
            $query->where('unit_id', $data['unit_id']);
        }

        // Add conditions for status if present
        if (isset($data['status']) && !empty($data['status'])) {
            if (is_array($data['status'])) {
                $query->whereIn('status', $data['status']);
            } else {
                $query->where('status', $data['status']);
            }
        }

        // Add conditions for current_date if present
        if (isset($data['current_date']) && !empty($data['current_date'])) {
            $query->where('payment_date', '<=', $data['current_date']);
        }

        // Add conditions for payment reversal if applicable
        if (!empty($data['payment_reversal']) && strtolower($data['payment_reversal']) == 'y') {
            $query->where('created_date', '>=', ACTIVE_PAYMENT_REVERSAL_DATE);
        }

        // Add conditions for bill_type if present
        if (isset($data['bill_type']) && !empty($data['bill_type'])) {
            $query->whereIn('bill_type', $data['bill_type']);
        }

        // Get the first result from the query
        $objIncomeInvoicePaymentTracker = $query->first();
        if (!empty($objIncomeInvoicePaymentTracker)) {
            $arrIncomeInvoicePaymentTracker = $objIncomeInvoicePaymentTracker->toArray();

            $arrIncomeInvoicePaymentTracker['member_paid_invoice'] = $arrIncomeInvoicePaymentTracker['invoice_number'];
            $arrIncomeInvoicePaymentTracker['receipt_number'] = strtoupper($arrIncomeInvoicePaymentTracker['receipt_number']);
            $arrIncomeInvoicePaymentTracker['total_unpaid_amount'] = $arrIncomeInvoicePaymentTracker['total_due_amount'];
            unset($arrIncomeInvoicePaymentTracker['invoice_number'], $arrIncomeInvoicePaymentTracker['total_due_amount']);
            $arrIncomeInvoicePaymentTracker['other_information'] = unserialize($arrIncomeInvoicePaymentTracker['other_information']);

            // set payment for
            $arrIncomeInvoicePaymentTracker['payment_for'] = 'Maintenance Invoice';
            if (strtolower($arrIncomeInvoicePaymentTracker['bill_type']) == 'common_bill') {
                $arrIncomeInvoicePaymentTracker['payment_for'] = 'Incidental Bill';
            } elseif (strtolower($arrIncomeInvoicePaymentTracker['bill_type']) == 'nonmember') {
                $arrIncomeInvoicePaymentTracker['payment_for'] = 'Nonmember Bill';
            } elseif (strtolower($arrIncomeInvoicePaymentTracker['bill_type']) == 'suspense') {
                $arrIncomeInvoicePaymentTracker['payment_for'] = 'Suspense Reciept';
            } elseif (strtolower($arrIncomeInvoicePaymentTracker['bill_type']) == 'creditaccount-member') {
                if (
                    !empty($arrIncomeInvoicePaymentTracker['other_information']['credit_account_detail']['credit_used_type']) &&
                    strtolower($arrIncomeInvoicePaymentTracker['other_information']['credit_account_detail']['credit_used_type']) == 'refundable'
                ) {
                    $arrIncomeInvoicePaymentTracker['payment_for'] = 'Member Refundable Advance';
                } elseif (
                    !empty($arrIncomeInvoicePaymentTracker['other_information']['credit_account_detail']['credit_used_type']) &&
                    strtolower($arrIncomeInvoicePaymentTracker['other_information']['credit_account_detail']['credit_used_type']) == 'adjustable'
                ) {
                    $arrIncomeInvoicePaymentTracker['payment_for'] = 'Member Adjustable Advance';
                }
            }
            if (!empty($arrIncomeInvoicePaymentTracker['payment_mode']) && in_array($arrIncomeInvoicePaymentTracker['payment_mode'], array(YES_BANK_PG, YES_BANK_ECOLLECT, PAYTM_PG, MOBIKWIK_PG, CASHFREE_PG, MOBIKWIK_WALLET, HDFC_PG, ATOM_PG))) {
                switch ($arrIncomeInvoicePaymentTracker['payment_mode']) {
                    case YES_BANK_PG:
                        $arrIncomeInvoicePaymentTracker['online_payment_display_mode'] = DISPLAY_YES_BANK;
                        break;
                    case YES_BANK_ECOLLECT:
                        $arrIncomeInvoicePaymentTracker['online_payment_display_mode'] = DISPLAY_YES_BANK_ECOLLECT;
                        break;
                    case MOBIKWIK_WALLET:
                        $arrIncomeInvoicePaymentTracker['online_payment_display_mode'] = DISPLAY_MOBIKWIK_WALLET;
                        break;
                    case PAYTM_PG:
                        $arrIncomeInvoicePaymentTracker['online_payment_display_mode'] = DISPLAY_PAYTM;
                        break;
                    case MOBIKWIK_PG:
                        $arrIncomeInvoicePaymentTracker['online_payment_display_mode'] = DISPLAY_MOBIKWIK_PG;
                        break;
                    case CASHFREE_PG:
                        $arrIncomeInvoicePaymentTracker['online_payment_display_mode'] = DISPLAY_CASHFREE_PG;
                        break;
                    case HDFC_PG:
                        $arrIncomeInvoicePaymentTracker['online_payment_display_mode'] = DISPLAY_HDFC_PG;
                        break;
                    case ATOM_PG:
                        $arrIncomeInvoicePaymentTracker['online_payment_display_mode'] = DISPLAY_ATOM_PG;
                        break;
                }
            }

            // format date
            if (!empty($arrIncomeInvoicePaymentTracker['created_date'])) {
                $payment_date = current(explode(' ', $arrIncomeInvoicePaymentTracker['created_date']));
                if (!empty($payment_date)) {
                    $arrIncomeInvoicePaymentTracker['created_date'] = $payment_date;
                }
            }

            // setting other information field
            if (!empty($arrIncomeInvoicePaymentTracker['other_information'])) {
                // setting writeoff particular fields
                if (!empty($arrIncomeInvoicePaymentTracker['other_information']['writeoff_detail'])) {
                    $arrIncomeInvoicePaymentTracker['total_particular'] = $arrIncomeInvoicePaymentTracker['other_information']['writeoff_detail']['total_particular'];
                    for ($i = 1; $i <= $arrIncomeInvoicePaymentTracker['total_particular']; $i++) {
                        $arrIncomeInvoicePaymentTracker["rule_$i"] = $arrIncomeInvoicePaymentTracker['other_information']['writeoff_detail']["rule_$i"];
                        $arrIncomeInvoicePaymentTracker["particular_$i"] = $arrIncomeInvoicePaymentTracker['other_information']['writeoff_detail']["particular_$i"];
                        $arrIncomeInvoicePaymentTracker["writeoff_amount_$i"] = $arrIncomeInvoicePaymentTracker['other_information']['writeoff_detail']["writeoff_amount_$i"];
                    }
                }
            }
        }
        return $arrIncomeInvoicePaymentTracker;
    }

    // fetch member detail
    public function getMemberDetail($data = array())
    {
        $arrMemberMaster = [];
        $id = ChsoneMemberTypeMaster::where('soc_id', $data['soc_id'])
            ->where('member_type_name', 'Primary')
            ->value('member_type_id');

        $objMemberMaster = ChsoneMembersMaster::where('soc_id', $data['soc_id'])
            ->where('fk_unit_id', $data['unit_id'])
            ->where('status', 1)
            ->where('member_type_id', $id)
            ->first();

        if ($objMemberMaster) {
            $arrMemberMaster = $objMemberMaster->toArray();
        }

        return $arrMemberMaster;
    }

    // Update income payment tracker status
    public function updateIncomePaymentTrackerStatus($data = array())
    {
        $arrResponse['status'] = 'error';
        $data['updated_date'] = date('Y-m-d');
        $updateResponse = $this->updateIncomeInvoicePaymentTrackerStatus($data);
        if (!empty($updateResponse) && strtolower($updateResponse['status']) == 'success') {
            $arrResponse['status'] = 'success';
        }
        return $arrResponse;
    }

    // Update income invoice payment tracker status
    public function updateIncomeInvoicePaymentTrackerStatus($data, $full_list = false)
    {
        $arrResponse = ['status' => 'success', 'message' => []];

        $updateData = [
            'updated_date' => $data['updated_date']
        ];

        if (!empty($data['status'])) {
            $updateData['status'] = $data['status'];
        }
        if (!empty($data['transaction_status'])) {
            $updateData['transaction_status'] = $data['transaction_status'];
        }
        if (!empty($data['invoice_number'])) {
            $updateData['invoice_number'] = $data['invoice_number'];
        }
        if (!empty($data['payment_date'])) {
            $updateData['payment_date'] = $data['payment_date'];
        }
        if (!empty($data['other_information'])) {
            $updateData['other_information'] = $data['other_information'];
        }
        if (!empty($data['reversal_note'])) {
            $updateData['reversal_note'] = $data['reversal_note'];
        }
        if (!empty($data['unit_id'])) {
            $updateData['unit_id'] = $data['unit_id'];
        }
        if (!empty($data['bill_type'])) {
            $updateData['bill_type'] = $data['bill_type'];
        }
        if (!empty($data['total_unpaid_amount'])) {
            $updateData['total_due_amount'] = $data['total_unpaid_amount'];
        }

        $result = IncomeInvoicePaymentTracker::where('soc_id', $data['soc_id'])
            ->where('id', $data['payment_tracker_id'])
            ->update($updateData);

        if (!$result) {
            $arrResponse = ['status' => 'error', 'message' => ['Failed to update payment tracker']];
        }

        return $arrResponse;
    }
}
