<?php

namespace App\Console\Commands\Actions\Income;

use Illuminate\Support\Facades\DB;
use App\Console\Commands\Action;
use App\Models\Income\ChsoneInvoiceGeneration;
use App\Models\Tenants\ChsoneInvoiceGeneration as TenantsChsoneInvoiceGeneration;

class GenerateBulkBillDataSource extends Action
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'datasource:GenerateBulkBillDataSource {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Generate Bulk Bill data source';

    protected $formatter =  [];

    protected $formatterByKeys =  [];

    protected $mapper = [];

    /**
     * Execute the console command.
     */
    public function apply()
    {
        $objTenantInvoiceGeneration = new TenantsChsoneInvoiceGeneration();
        $objTenantInvoiceGeneration->generateBulkBill();
        
        dd($this->input);
        $response = $addOutstandingDataSource->addOutstanding($this->input, $postedValues);
    }
}
