<?php

namespace App\Console\Commands\Actions\Income;

use App\Console\Commands\Action;

class MembersUnitStatementReportDownloadDataSource extends Action
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'datasource:membersUnitStatementReportDownload {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Get Members Unit Statement Report Data for Download';

    protected $formatter = [
        'date' => '',
        'type' => '',
        'reference_id' => '',
        'payment_reference' => '',
        'debit' => '',
        'credit' => '',
        'balance' => ''
    ];

    protected $formatterByKeys = [];

    protected $mapper = [];

    /**
     * Execute the console command.
     */
    public function apply()
    {
        // Call the existing members unit statement report datasource
        $membersUnitStatementData = $this->action('datasource:membersUnitStatementReport', $this->pointer, $this->input);
        
        // The membersUnitStatementReport returns an array with two elements:
        // [0] = transaction details array
        // [1] = summary array
        // We only need the transaction details for the download
        $transactionDetails = [];
        if (is_array($membersUnitStatementData) && count($membersUnitStatementData) > 0) {
            $transactionDetails = $membersUnitStatementData[0] ?? [];
        }
        
        // Format the data for download
        $this->data = $this->format($transactionDetails);
    }
}
