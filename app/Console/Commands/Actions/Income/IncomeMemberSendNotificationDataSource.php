<?php

namespace App\Console\Commands\Actions\Income;

use App\Console\Commands\Action;
use App\Http\Traits\NotificationTraits;
class IncomeMemberSendNotificationDataSource extends Action
{

    use NotificationTraits;
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'datasource:incomeMemberSendNotification {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */
    public function apply()
    {

        $id= $this->input['id'];
        $invoice_number= $this->input['invoice_number'];
        $company_id = $this->input['company_id'];

        // check if unit id is valid or not from chsone_units_master table
        $unit = $this->tenantDB()->table('chsone_units_master')
            ->where('unit_id', $id)
            ->get();

        if(count($unit) == 0) {
            $this->status = 'error';
            $this->message = 'Please provide a valid unit id';
            $this->statusCode = 404;
            $this->data = [];
            return;
        }

        $unitInvoice = $this->tenantDB()->table('income_unit_invoices')->where('soc_id',$company_id)
        ->where('invoice_number', $invoice_number)
        ->whereNotIn('status', ['cancelled'])
        ->first();

        $unitInvoice = json_decode(json_encode($unitInvoice), true);

        // check if unitInvoice is empty or null then return error
        if (empty($unitInvoice)) {
            $this->status = false;
            $this->statusCode = 400;
            $this->message = "Provide valid unit invoice id";
            return;
        }

        $unitInvoice['title'] = "invoice_generation_manual";
        $unitInvoice['type'] = "invoice";
        $unitInvoice['start_date'] = $unitInvoice['from_date'];
        $unitInvoice['end_date'] = $unitInvoice['to_date'];

        // pass invoice_number for fetching invoice details in notification
        $unitInvoice['invoice_number'] = $unitInvoice['invoice_number'];
        $unitInvoice['with_dpc'] = 1;
        $unitInvoice['bill_date'] = $unitInvoice['created_date'];

        //pass fk_unit_id from the which you getting as unit_id
        $unitInvoice['unit_id']=$unitInvoice['fk_unit_id'];


        $this->addEmailSmsNotificationData($this->input,$unitInvoice);
        $this->data= $unitInvoice;
        $this->message = "Notification sent successfully";


        $this->data = $unitInvoice;
    }
}
