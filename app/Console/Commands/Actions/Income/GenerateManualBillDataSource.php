<?php

namespace App\Console\Commands\Actions\Income;

use Illuminate\Support\Facades\DB;
use App\Console\Commands\Action;
use App\Http\Traits\NotificationTraits as TraitsNotificationTraits;
use App\Models\Income\ChsoneInvoiceGeneration;
use App\Models\Tenants\ChsoneInvoiceGeneration as TenantsChsoneInvoiceGeneration;
use App\Models\Tenants\IncomeUnitInvoice;
use App\Models\Tenants\IncomeUnitInvoiceSetting;

class GenerateManualBillDataSource extends Action
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'datasource:GenerateManualBillDataSource {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Generate Manual Bill data source';

    protected $formatter =  [];

    protected $formatterByKeys =  [];

    protected $mapper = [];

    /**
     * Execute the console command.
     */
    public function apply()
    {
        try     {
        $date = date('Y-m-d');
        $soc_id = $this->input['company_id'];
        $unit_id = $this->input['unit_id'];
        $previewUnitId = array();
        $previewUnitId = (is_array($unit_id)) ? $unit_id : array($unit_id);
        $arrGetInvoiceData = array('soc_id' => $soc_id, 'unit_id' => $unit_id, 'order_by' => 'unit_invoice_id desc');
        $arrListenerdata = array("unit_id" => $unit_id);
        $arrDataListener = array('soc_id' => $soc_id);
        $objUnitsInvoice = new TenantsChsoneInvoiceGeneration();
        $arrInvoiceSetting = $objUnitsInvoice->getInvoiceSetting($arrDataListener); //get all Unit details
        $arrInvoiceData = $this->getLastUnitInvoiceByUnit($arrGetInvoiceData);
        $arrInvoiceData = json_decode(json_encode($arrInvoiceData), true);
        $arrUnitData = array();

        if (!empty($arrInvoiceData)) {
            $date = (new \DateTime($arrInvoiceData['to_date']))->modify('first day of next month');
            $date = $date->format('Y-m-d');
            //$date = $this->getDisplayDate($date);

            if (!empty($arrInvoiceData['from_date'])) {
                $arrInvoiceData['from_date'] = $arrInvoiceData['from_date'];
            }
            if (!empty($arrInvoiceData['to_date'])) {
                $nextInvoiceStartDate = (new \DateTime($arrInvoiceData['to_date']))->modify('+1 day');
                $nextInvoiceStartDate = $nextInvoiceStartDate->format('Y-m-d');
                $nextInvoiceStartDate = $nextInvoiceStartDate;

                $arrInvoiceData['to_date'] = $arrInvoiceData['to_date'];
            }
        } elseif (!empty($arrInvoiceSetting['effective_date'])) {
            $nextInvoiceStartDate = $arrInvoiceSetting['effective_date'];
        } else {
            $nextInvoiceStartDate = $date;
        }
        if (count($previewUnitId) > 1) {
            $arrUnitData['unit_name'] = 'unit/preview';
        } else {
            if (empty($arrInvoiceData)) {
                $arrUnitData = $this->getUnitDetailById(array('soc_id' => $soc_id, 'unit_id' => $unit_id));
                $arrUnitData = json_decode(json_encode($arrUnitData), true);
                if (!empty($arrUnitData)) {
                    $arrUnitData['unit_name'] = ucwords($arrUnitData['soc_building_name'] . '/' . $arrUnitData['unit_flat_number']);
                }
            } else {
                $arrInvoiceData['unit_name'] = str_replace($arrInvoiceData['soc_building_name'] . '-', '', $arrInvoiceData['unit_name']);
                $arrInvoiceData['unit_name'] = ucwords($arrInvoiceData['soc_building_name'] . '/' . $arrInvoiceData['unit_name']);
            }
        }
        $startDate = $nextInvoiceStartDate;
        $startDate = (new \DateTime($startDate))->modify('+1 day');
        $startDate = $startDate->format('Y-m-d');
        // $dueDate = (new \DateTime($arrInvoiceData['due_date']))->modify('+1 month +1 day');
        // $dueDate = $dueDate->format('Y-m-d');

        $baseDate = $nextInvoiceStartDate;

        $latePaymentRule = $this->tenantDB()->table('income_late_payment_charges')
            ->where('type', 'maintenance')
            ->where('soc_id', $soc_id)
            ->where('effective_date', '<=', $baseDate)
            ->orderBy('effective_date', 'desc')
            ->first();


        $gracePeriod = 0;

        if ($latePaymentRule && isset($latePaymentRule->grace_period)) {
            // Subtract 1 from the grace period per your business logic.
            $gracePeriod = $latePaymentRule->grace_period - 1;
        }

        $dueDate = (new \DateTime($baseDate))
                    ->modify("+{$gracePeriod} days")
                    ->modify("+1 day")
                    ->format('Y-m-d');




        $start = new \DateTime($startDate);
        switch (strtolower($arrInvoiceSetting['invoicing_frequency'])) {
            case 'monthly':
                $end = $start->modify('last day of this month');
                break;

            case 'quarterly':
                $end = $start->modify('last day of +2 months'); // adds 2 to include the start month = 3 months total
                break;

            case 'half-yearly':
                $end = $start->modify('last day of +5 months');
                break;

            case 'yearly':
                $end = $start->modify('last day of +11 months');
                break;

            default:
                // fallback = single month
                $end = $start->modify('last day of this month');
        }

        // $endDate->modify('last day of this month');
        $endDate = $end->format('Y-m-d');

        $date1 = new \DateTime($startDate);
        $date2 = new \DateTime($dueDate);

        $interval = $date1->diff($date2);
        $interval = $interval->days + 1;

        // $lastInvoicePeriodStartDate = new \DateTime($arrInvoiceData['to_date']);
        // $lastInvoicePeriodStartDate->modify('first day of this month');
        // $lastInvoicePeriodStartDate = $lastInvoicePeriodStartDate->format('Y-m-d');

        // $lastInvoicePeriodEndDate = new \DateTime($arrInvoiceData['to_date']);
        // $lastInvoicePeriodEndDate->modify('last day of this month');
        // $lastInvoicePeriodEndDate = $lastInvoicePeriodEndDate->format('Y-m-d');
        if (!empty($arrInvoiceData) && !empty($arrInvoiceData['to_date'])) {
            $lastInvoicePeriodStartDate = (new \DateTime($arrInvoiceData['created_date']))->modify('+1 day')->modify('first day of this month')->format('Y-m-d');
            $lastInvoicePeriodEndDate = (new \DateTime($arrInvoiceData['to_date']))->modify('last day of this month')->format('Y-m-d');
        } else {
            $lastInvoicePeriodStartDate = null;
            $lastInvoicePeriodEndDate = null;
        }


        $this->data = [
            'invoice_date' => $startDate,
            'start_date' => $startDate,
            'end_date' => $endDate,
            'due_date' => $dueDate,
            'last_invoice_period_start_date' => $lastInvoicePeriodStartDate,
            'last_invoice_period_end_date' => $lastInvoicePeriodEndDate,
            'interval' => $interval,
            'unit_name' => isset($arrInvoiceData['unit_name']) ? $arrInvoiceData['unit_name'] : (isset($arrUnitData['unit_name']) ? $arrUnitData['unit_name'] : ''),

        ];

    } catch (\Exception $e) {
        dd($e);
        // $this->status = 'error';
        // $this->message = 'Error generating manual bill data source: ' . $e->getMessage();
        // $this->statusCode = 500;
        // return;
    }
}

    public function getLastUnitInvoiceByUnit($data = array())
    {
        $arrUnitsInvoice = array();

        $query = IncomeUnitInvoice::query()
            ->where('soc_id', $data['soc_id'])
            ->where('fk_unit_id', $data['unit_id'])
            ->where('status', '!=', 'cancelled');

        if (isset($data['order_by']) && !empty($data['order_by'])) {
            $query->orderByRaw($data['order_by']);
        }
        $objUnitsInvoice = $query->first();

        if (!empty($objUnitsInvoice)) {
            $arrUnitsInvoice = $objUnitsInvoice->toArray();
        }

        return $arrUnitsInvoice;
    }

    public function getUnitDetailById(array $data = [])
    {
        $soc_id = $data['soc_id'];
        $unit_id = $data['unit_id'];

        $arrUnitDetails = $this->tenantDB()->table('chsone_units_master')->where('soc_id', $soc_id)
            ->where('unit_id', $unit_id)
            ->first();

        return !empty($arrUnitDetails) ? $arrUnitDetails : [];
    }
}
