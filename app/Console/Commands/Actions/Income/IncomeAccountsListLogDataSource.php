<?php

namespace App\Console\Commands\Actions\Income;

use Illuminate\Console\Command;
use App\Console\Commands\Action;

class IncomeAccountsListLogDataSource extends Action
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'datasource:incomeAccountsListLog {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Get the income accounts details';



    protected $formatterByKeys = ['id'];

    protected $mapper = [
        'id' => 'account_id',
    ];

    protected $hugeData = true;

    protected $schema = [
        "table" => [
            "tabs" => [
                "Member Income Account",
                "Non Member Income Account"
            ],
            "fields" => [
                "*"
            ],
            "columns" => [
                [
                    "title" => "Income Account",
                    "key" => "account_name"
                ],
                [
                    "title" => "Invoice Print Name",
                    "key" => "display_name"
                ],
                [
                    "title" => "Ledgers",
                    "key" => "ledger_account_name",
                    "editable" => "true",
                    "type" => "select",
                    "api_path" => "/admin/accounts/viewLedgers"
                ]
            ]
        ]
    ];

    protected $schema2 = [
        "table" => [
            "tabs" => [
                "Member Income Account",
                "Non Member Income Account"
            ],
            "fields" => [
                "*"
            ],
            "columns" => [
                [
                    "title" => "Income Account",
                    "key" => "account_name"
                ],
                [
                    "title" => "Ledgers",
                    "key" => "ledger_account_name",
                    "editable" => "true",
                    "type" => "select",
                    "api_path" => "/admin/accounts/viewLedgers"
                ]
            ],
            "actions" => [
                [
                    "title" => "Delete Income Account",
                    "icon" => "ri-delete-back-2-line",
                    "color" => "error",
                    "variant" => "contained",
                    "delete_row" => true,
                    "hide_on" => [
                        "_action" => [
                            "add",
                        ]
                    ]
                ],
            ],
        ]
    ];

    /**
     * Execute the console command.
     */
    public function apply()
    {
        $CurrentTab = 'member_income_account';
        $account_type = 'member';

        if (isset($this->input['current_tab']) && $this->input['current_tab'] != "null") {
            $CurrentTab = $this->input['current_tab'];
        }

        if (strtolower($CurrentTab) == 'member_income_account' || $CurrentTab == 'Member Income Account') {

            $this->meta['schema'] = $this->schema;
        } elseif (strtolower($CurrentTab) == 'non_member_income_account' || $CurrentTab == 'Non Member Income Account') {

            $account_type = 'nonmember';
            $this->meta['schema'] = $this->schema2;
        }

        $obj = $this->tenantDB()->table('income_accounts AS IA')
            ->select('IA.account_id as id', 'IA.account_name', 'IA.display_name', 'IA.fk_income_ledger_id', 'ledger_account_name', 'ledger_account_name as title', 'IA.created_date', 'IA.created_by', 'IA.updated_date', 'IA.updated_by')
            ->leftJoin('chsone_grp_ledger_tree AS GLT', 'IA.fk_income_ledger_id', '=', 'GLT.ledger_account_id')
            ->where('IA.account_type', $account_type);

        $result = $obj->first();
        $result = json_decode(json_encode($result), true);

        // dd($result);
        $created_by =        $this->getUserData($result['created_by']);
        $created_date =      $result['created_date'];
        $updated_by =        $this->getUserData($result['updated_by']);
        $updated_date =      $result['updated_date'];

        $result['created_by'] = $created_by;
        $result['created_date'] = $created_date;
        $result['updated_by'] = $updated_by;
        $result['updated_date'] = $updated_date;


        $this->data = $result;
    }

    public function getUserData($id)
    {

        $obj = $this->MasterDB()->table('chsone_users_master as user')
            ->select('user.user_id as id', 'user.user_name', 'user.user_first_name', 'user.user_last_name', 'user.user_email_id', 'user.user_mobile_number', 'user.user_type', 'user.user_source', 'user.user_lang_iso_639_3', 'user.user_gmt_time_zone', 'user.added_on', 'user.modified_on', 'user.role', 'user.status')
            ->where('user.user_id', $id)
            ->first();

        if ($id && $obj) {
            return  $obj->user_first_name . ' ' . $obj->user_last_name;
        } else {
            return '-';
        }
    }
}
