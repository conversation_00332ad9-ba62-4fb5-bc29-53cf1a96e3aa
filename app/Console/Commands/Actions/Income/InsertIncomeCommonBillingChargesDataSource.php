<?php

namespace App\Console\Commands\Actions\Income;

use Illuminate\Console\Command;
use App\Console\Commands\Action;
use App\Models\Tenants\IncomeInvoiceGeneralSetting;

class InsertIncomeCommonBillingChargesDataSource extends Action
{
    protected $signature = 'datasource:InsertIncomeCommonBillingCharges {flowId} {parentId} {input}';

    protected $description = 'Insert Income Common Billing Charges Data Source';

    protected $rules = [];

    protected $rulesMessage = [];

    public function apply()
    {

        $newInvoiceNumber = $this->getNewInvoiceNumber();
        $company_id = $this->input['company_id'];

        // check if unit id is valid or not from chsone_units_master table
        $unit = $this->tenantDB()->table('chsone_units_master')
            ->where('unit_id', $this->input['unit_id'])
            ->where('soc_id', $company_id)
            ->get();
        
        if(count($unit) == 0) {
            $this->status = 'error';
            $this->message = 'Please provide a valid unit id';
            $this->statusCode = 400;
            $this->data = [];
            return;
        }

        // check if member id is valid or not from chsone_members table
        $member = $this->tenantDB()->table('chsone_members_master')
            ->where('id', $this->input['member_id'])
            ->where('fk_unit_id', $this->input['unit_id']) 
            ->where('soc_id', $company_id)
            ->get();

        if(count($member) == 0) {
            $this->status = 'error';
            $this->message = 'Please provide a valid member id';
            $this->statusCode = 400;
            $this->data = [];
            return;
        }

        // check if advance amount is greater than 0 then set the payment_status to 'paid' or 'partialpaid' else 'unpaid' according to the particular amount
        if (isset($this->input['advance_amount']) && !empty($this->input['advance_amount']) && $this->input['advance_amount'] > 0) {
            // check if advance amount is equal to the invoice amount then set the payment_status to 'paid' else 'partialpaid'
            if ($this->input['advance_amount'] == $this->input['particular_amount']) {
                $payment_status = 'paid';
            } else {
                $payment_status = 'partialpaid';
            }
        } else {
            $payment_status = 'unpaid';
        }

        $obj = $this->tenantDB()->table('income_common_billing_charges')
        ->insert([
            'soc_id' => $company_id,
            'fk_member_id' => $this->input['member_id'],
            // get the last invoice number from the income_common_billing_charges table and increment it by 1 to get the new invoice number
            'invoice_number' => $newInvoiceNumber,
            'fk_unit_id' => $this->input['unit_id'],
            'billing_type' => $this->input['particular_id'],
            'from_date' => $this->input['from_date'],
            'to_date' => $this->input['to_date'],
            'bill_date' => $this->input['bill_date'],
            'due_date' => $this->input['due_date'],
            // 'payment_mode' => '',
            'advance_amount' => $this->input['advance_amount'] ?? 0,
            'amount' => $this->input['particular_amount'],
            'transaction_charges' => 0,
            'discount_amount' => 0,
            // 'payment_instrument' => '',
            'payment_status' => $payment_status,
            'status' => 'unsettled',
            'bill_from' => 'incident',
            'created_by' => $this->input['user_id'] ?? 0,
            'updated_by' => $this->input['user_id'] ?? 0,
            'created_date' => date('Y-m-d H:i:s'),
            'updated_date' => date('Y-m-d H:i:s')
        ]);

        // get inserted record after successful insertion
        $income_common_billing_charges = $this->tenantDB()->table('income_common_billing_charges')
        ->where('invoice_number', $newInvoiceNumber)
        ->first();

        $this->data = $income_common_billing_charges;
    }

    public function getNewInvoiceNumber()
    {
        $settingDetails = IncomeInvoiceGeneralSetting::where('soc_id', $this->input['company_id'])->where('setting_key', 'INCOME_INCIDENTAL_INVOICE_PREFIX')->first();

        $last_invoice_number = $this->tenantDB()->table('income_common_billing_charges')->orderBy('id', 'desc')->first();
        if($last_invoice_number && $last_invoice_number->invoice_number) {
            
            // Extract the numeric part (remove non-numeric characters
            $numericPart = (int)preg_replace('/\D/', '', $last_invoice_number->invoice_number);
            // Increment the numeric part
            $newNumericPart = $numericPart + 1;
            // Format the new invoice number
            $newInvoiceNumber = $settingDetails->setting_value . str_pad($newNumericPart, 5, '0', STR_PAD_LEFT);
        } else {
            $newInvoiceNumber = $settingDetails->setting_value . '00001';
        }

        return $newInvoiceNumber;
    }
}