<?php

namespace App\Console\Commands\Actions\Income;

use App\Console\Commands\Action;
use Carbon\Carbon;
use Illuminate\Support\Facades\Config;

class DetailAdvancesDataSource extends Action
{

    protected $constants;
    protected $schema = [];

    public function __construct()
    {
        parent::__construct();
        $this->constants = Config::get("constants");
    }

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'datasource:detailAdvances {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Get the billable note details';

    /**
     * Execute the console command.
     */
    public function apply()
    {
        $id = $this->input['id'];

        $result = $this->tenantDB()->table('chsone_credit_accounts as cr_acc')
            ->selectRaw(
                'credit_account_id as id,
                account_id,
                account_context,
                credit_account_id,
                soc_id,
                payment_date,
                account_name,
                amount,
                type,
                COALESCE(payment_mode, "NA") as payment_mode,
                transaction_type,
                narration,
                use_credit,
                COALESCE(use_credit_for, "NA") as use_credit_for,
                is_invoice_rectification,
                context,
                created_date,
                updated_date,
                created_by,
                updated_by,
                CASE
                    WHEN context = "system"
                    OR transaction_type = "dr"
                    OR (use_credit = "adjustable" AND use_credit_after IS NOT NULL AND use_credit_after <= ?)
                    THEN true
                    ELSE false
                END AS disable', [
                    Carbon::now()->format('Y-m-d H:i:s'),
                ]
            )
            ->where('cr_acc.credit_account_id', $id)
            ->orderByDesc('cr_acc.credit_account_id')
            ->first();

        $this->data = $result;
        $this->status = 'success';
        $this->statusCode = 200;

    }
    public function getAmountPaid($payment_amount)
    {
        $payment_amount = (float) $payment_amount;
        return '₹ ' . number_format($payment_amount, 2);
    }
}
