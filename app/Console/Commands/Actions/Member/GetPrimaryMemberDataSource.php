<?php

namespace App\Console\Commands\Actions\Member;

use App\Http\Traits\NotificationTraits;
use Illuminate\Console\Command;
use App\Console\Commands\Action;


// STEP 8 : Create a new action class whitch extends Action class. Keep signature  same as you specified in workflow.

class GetPrimaryMemberDataSource extends Action
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'datasource:GetPrimaryMember {flowId} {parentId} {input}';

    use NotificationTraits;
    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Get Primery Member List';

    // STEP 9: Specified formatter for your action. This will format your data as per your requirement.
    // Formatter will 3 level depth array. 1st level for outer array and 2nd level for inner array and so on.
    protected $formatter =  [

    ];

    // STEP 10: Specified formatterByKeys for your action. This will format your data as per specified keys.
    // 1st key for outer array and 2nd key for inner array and so on.
    protected $formatterByKeys =  ['unit_id as id'];

    // STEP 11: Mapper helps to map the keys of your database query and formatter.
    // If you have filters, please specified thoes keys in mapper.
    protected $mapper = [

    ];

    /**
     * Execute the console command.
     */

    public function apply()
    {
        $member_mobile_number = $this->input['mobile'];
        $company_id = $this->input['company_id'];

        $getPrimaryDetail = $this->tenantDB()
            ->table("chsone_members_master")
            ->where('member_mobile_number', $member_mobile_number)
            ->where('member_type_id', 1)
            ->where('soc_id', $company_id)
            ->first();

        $this->data = $getPrimaryDetail;
    }

}
