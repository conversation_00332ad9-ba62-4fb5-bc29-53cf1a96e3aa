<?php

namespace App\Console\Commands\Actions\Member;

use App\Console\Commands\Action;
use App\Models\Tenants\ChsoneCreditAccount as CreditAccounts;
use Illuminate\Support\Facades\DB;


// STEP 8 : Create a new action class whitch extends Action class. Keep signature  same as you specified in workflow.
class IncomeMemberDownloadDataSource extends Action
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'datasource:incomeMemberDownload {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Get Primery Member List';

    // STEP 9: Specified formatter for your action. This will format your data as per your requirement.
    // Formatter will 3 level depth array. 1st level for outer array and 2nd level for inner array and so on.
    protected $formatter =  [
        'id' => '',
        'unit_category_id' => '',
        'unit_category' => '',
        'soc_building_name' => '',
        'unit_flat_number' => '',
        'soc_building_floor' => '',
        'member_id' => '',
        'member_first_name' => '',
        'member_last_name' => '',
        'member_mobile_number' => '',
        'gstin' => '',
        'total_advance' => '',
        'total_balance_due' => '',
        'member_name' => 'concat:member_first_name,member_last_name', // for customization of value her you specifeid the function name and pass the key of your data. Then define function in curren class.


    ];

    // STEP 10: Specified formatterByKeys for your action. This will format your data as per specified keys.
    // 1st key for outer array and 2nd key for inner array and so on.
    protected $formatterByKeys =  ['unit_id as id'];

    // STEP 11: Mapper helps to map the keys of your database query and formatter.
    // If you have filters, please specified thoes keys in mapper.
    protected $mapper = [
        'unit_number' => 'units.unit_flat_number',
        'building' => 'units.soc_building_name',
        'unit_id' => 'unit_id',
    ];


    /**
     * Execute the console command.
     */
    public function apply()
    {

        $obj = $this->tenantDB()->table('chsone_members_master AS memberMaster')
            ->select(
                'units.unit_id as id',
                DB::raw("CONCAT(units.soc_building_name, ' / ', units.unit_flat_number) as unit_name"),
                'units.fk_unit_category_id as unit_category_id',
                'units.unit_category',
                'units.soc_building_floor',
                DB::raw("CONCAT(memberMaster.member_first_name,' ',memberMaster.member_last_name) as member_name"),
                'memberMaster.member_mobile_number',
                DB::raw("1 as start_invoicing")
            )
            ->leftJoin('chsone_units_master AS units', 'units.unit_id', '=', 'memberMaster.fk_unit_id')
            ->leftJoin('chsone_member_type_master AS memberType', 'memberType.member_type_id', '=', 'memberMaster.member_type_id')
            ->where('memberType.member_type_name', 'Primary')
            ->where('memberMaster.status', 1)
            ->where('units.is_allotted', '1');

        $obj = $obj->orderBy('unit_id', 'asc');
        $result = $obj->get();
        $result = json_decode(json_encode($result), true);
        foreach ($result as $key => $value) {
            // Get the invoice details
            $result[$key]['invoice_detail'] = $this->invoiceParticular($value);
           //$result[$key]['total_advance'] = $this->advanceDetails($value);

            // Check if invoice_detail is a collection and sum the balance_due_amount
            if (isset($result[$key]['invoice_detail']) && $result[$key]['invoice_detail'] instanceof \Illuminate\Support\Collection) {
                $total_balance_due = $result[$key]['invoice_detail']->sum('balance_due_amount');
                $result[$key]['due_amount'] = number_format($total_balance_due, 2, '.', '');
                //$result[$key]['advances'] = number_format($result[$key]['total_advance']->sum('total_advance'), 2, '.', '');
                $result[$key]['last_invoice_date'] = date('d/m/Y', strtotime($result[$key]['invoice_detail']->max('created_date')));
                unset($result[$key]['invoice_detail']);
                //unset($result[$key]['total_advance']);
            }

            // Fetch unit invoice details using the refactored method
            $is_generatedOutstanding = $this->isGeneratedOutstanding($result[$key]['id']);
            //$result[ $key ][ 'is_generatedOutstanding' ] = $is_generatedOutstanding;

            $result[$key]['is_generatedBill'] = ($is_generatedOutstanding['is_already_generated'] == 0) ? 1 : 0;
            $result[$key]['rupees_disable'] = $result[$key]['is_generatedBill'] != 0;

            $singleMemberDetail = [];
            if (!empty($is_generatedOutstanding['is_generated'])) {
                $singleMemberDetail['credit_detail'] = $this->getCreditAmountForIndividuals(array('data' => array('soc_id' => $this->input['company_id'], 'account_id' => $value['id'], 'account_context' => 'unit', 'bill_date' => $this->getCurrentDate('database'), 'bill_type' => 'maintenance'))); //get advance details

                //$singleMemberDetail['credit_detail'] = $creditAllUnits[$value->unit_id];

            } else {
                $singleMemberDetail['credit_detail'] = array('credit_amount' => 0, 'debit_amount' => 0, 'remaining_amount' => $is_generatedOutstanding['advance_amount']);
                $singleMemberDetail['outstanding_amount'] = $is_generatedOutstanding['outstanding_amount'];
            }
            $result[$key]['advances'] = $singleMemberDetail['credit_detail']['remaining_amount'];
            if (!empty($result[$key]['invoice_detail']) && isset($result[$key]['invoice_detail'][0]->invoice_number)) {
                $downloadFlag = 1;
            } else {
                $downloadFlag = 0;
            }
            //dd($result[$key]['invoice_detail']);

            if (isset($result[$key]['due_amount']) && $result[$key]['due_amount'] > 0) {
                $viewAllInvoiceFlag = 1;
            } else {
                $viewAllInvoiceFlag = 0;
            }
            if (isset($result[$key]['due_amount']) && $result[$key]['due_amount'] > 0) {
                $viewReceiptFlag = 1;
            } else {
                $viewReceiptFlag = 0;
            }
            $result[$key]['downloadFlag'] = $downloadFlag;
            $result[$key]['viewReceiptFlag'] = $viewReceiptFlag;
            $result[$key]['viewAllInvoiceFlag'] = $viewAllInvoiceFlag;
            unset($result[$key]['invoice_detail']);
            //unset($result[$key]['total_advance']);
        }
        //dd($result);
        // STEP 13: Format your data as per your requirement.
        $this->data = $result;

    }

    public function getCreditAmountForIndividuals($data = [])
    {
        // Initialize the amounts.
        $creditAmountDetails = [
            'credit_amount'    => 0,
            'debit_amount'     => 0,
            'remaining_amount' => 0,
        ];

        // Build the query using Eloquent
        $query = CreditAccounts::query()
            ->where('soc_id', $data['data']['soc_id'])
            ->where('account_id', $data['data']['account_id'])
            ->where('account_context', $data['data']['account_context'])
            ->where('use_credit', 'adjustable');

        // Add condition for bill_date if provided:
        if (!empty($data['data']['bill_date'])) {
            $query->where(function($q) use ($data) {
                $q->where('use_credit_after', '<=', $data['data']['bill_date'])
                    ->orWhereNull('use_credit_after');
            });
        }

        // Add condition for bill_type if provided:
        if (!empty($data['data']['bill_type'])) {
            $query->where(function($q) use ($data) {
                $q->where('use_credit_for', $data['data']['bill_type'])
                    ->orWhere('use_credit_for', 'both');
            });
        }

        // Retrieve matching records
        $objAmountDetails = $query->get();

        // Loop through each record to sum up amounts based on transaction type.
        if ($objAmountDetails->isNotEmpty()) {
            foreach ($objAmountDetails as $amountData) {
                if ($amountData->transaction_type == 'cr') {
                    $creditAmountDetails['credit_amount'] += $amountData->amount;
                } else {
                    $creditAmountDetails['debit_amount'] += $amountData->amount;
                }
            }
        }

        // Calculate the remaining amount.
        $creditAmountDetails['remaining_amount'] = $creditAmountDetails['credit_amount'] - $creditAmountDetails['debit_amount'];

        // Make sure the remaining amount is not negative.
        if ($creditAmountDetails['remaining_amount'] < 0) {
            $creditAmountDetails['remaining_amount'] = 0;
        }

        return $creditAmountDetails;
    }

    public function isGeneratedOutstanding($unitId)
    {
        $socId = $this->input['company_id'];
        // Retrieve `soc_id` from session

        // Fetch the first matching record for IncomeInvoiceAdjustment
        $outstandingAmt = $this->tenantDB()->table('income_invoice_adjustment')->where('soc_id', $socId)
            ->where('fk_unit_id', $unitId)
            ->first();

        // Count the number of non-cancelled invoices for the unit
        $totalInvoiceAlreadyGenerated = $this->tenantDB()->table('income_unit_invoices')->where('soc_id', $socId)
            ->where('fk_unit_id', $unitId)
            ->where('status', '!=', 'cancelled')
            ->count();

        $isAlreadyGenerated = $totalInvoiceAlreadyGenerated > 0 ? 1 : 0;
        $return = [
            'count' => 0,
            'is_generated' => null,
            'advance_amount' => 0,
            'outstanding_amount' => 0,
            'is_already_generated' => $isAlreadyGenerated,
        ];

        if (!empty($outstandingAmt) && is_object($outstandingAmt)) {
            $outstandingAmtArray = json_decode(json_encode($outstandingAmt), true);
            $return['count'] = count($outstandingAmtArray);
            $return['is_generated'] = $outstandingAmtArray['is_generated'];

            // Calculate advance amount if conditions are met
            if (
                empty($outstandingAmtArray['is_generated']) &&
                strtolower($outstandingAmtArray['adjustment_type']) === 'advance' &&
                strtolower($outstandingAmtArray['bill_type']) === 'maintenance'
            ) {
                $return['advance_amount'] = $outstandingAmtArray['principal_amount'];
            }

            // Calculate outstanding amount if conditions are met
            if (
                empty($outstandingAmtArray['is_generated']) &&
                strtolower($outstandingAmtArray['adjustment_type']) === 'outstanding' &&
                strtolower($outstandingAmtArray['bill_type']) === 'maintenance'
            ) {
                $return['outstanding_amount'] = $outstandingAmtArray['principal_amount'] +
                    $outstandingAmtArray['interest_amount'] +
                    $outstandingAmtArray['delayed_payment_charges'];
            }
        }

        return $return;
    }

    // STEP 14: Define your custom function here. This will call while format function call.
    public function concat($a, $b)
    {
        return $a . ' ' . $b;
    }

    public function advanceDetails($data = [])
    {
        $obj = $this->tenantDB()->table('chsone_credit_accounts')
        ->selectRaw('
        IFNULL(
            SUM(CASE WHEN transaction_type = "cr" THEN amount ELSE 0 END) -
            SUM(CASE WHEN transaction_type = "dr" THEN amount ELSE 0 END),
        0.0) AS total_advance')
    ->where('account_context', 'unit')
    ->where('account_id', $data['id']);

        $result = $obj->get();
        return $result;
    }

    public function invoiceParticular($data = [])
    {
        $obj = $this->tenantDB()->table('income_unit_invoices AS unitInvoices')
            ->select(
                'unit_invoice_id as id',
                'unit_invoice_id',
                'unitInvoices.fk_unit_id as unit_id',
                //'units.soc_building_name',
                //'unit_flat_number',
                'unitInvoices.invoice_number',
                'unitInvoices.created_date',
                //'due_date',
                //'interest_amount',
                'advance_amount',
                //'roundoff_amount',
                //'payment_status'
            )
            ->leftJoin('income_invoice_particular AS particulars', 'unitInvoices.unit_invoice_id', '=', 'particulars.fk_unit_invoice_id')
            ->leftJoin('chsone_units_master AS units', 'unitInvoices.fk_unit_id', '=', 'units.unit_id')
            ->where('unitInvoices.fk_unit_id', $data['id'])
            ->whereIn('payment_status', ['unpaid', 'partialpaid'])
            ->where('unitInvoices.status', '!=', 'cancelled')
            ->groupBy('particulars.fk_unit_invoice_id')
            ->orderByDesc('created_date')
            //->selectRaw("SUM(amount) as amount")
            //->selectRaw("SUM(tax_applicable) as tax_applicable")
            //->selectRaw("SUM(tax_exemptions) as tax_exemptions")
            //->selectRaw("(SELECT SUM(payment_amount) FROM income_invoice_payment WHERE fk_unit_invoice_id = unitInvoices.unit_invoice_id) AS payment_amount")
            //->selectRaw("(SELECT SUM(writeoff_amount) FROM income_invoice_payment WHERE fk_unit_invoice_id = unitInvoices.unit_invoice_id) AS writeoff_amount")
            //->selectRaw("(SELECT SUM(tds_deducted) FROM income_invoice_payment WHERE fk_unit_invoice_id = unitInvoices.unit_invoice_id) AS tds_deducted")
            //->selectRaw("(SELECT SUM(late_payment_charges) FROM income_invoice_payment WHERE fk_unit_invoice_id = unitInvoices.unit_invoice_id) AS late_payment_charges")
            //->selectRaw("(SELECT SUM(late_payment_charges) FROM income_invoice_payment WHERE fk_unit_invoice_id = unitInvoices.unit_invoice_id) AS late_payment_charges")
            //->selectRaw("(SELECT SUM(tax_amount) FROM chsone_tax_log WHERE invoice_number = unitInvoices.invoice_number) AS gst_amount")
            ->selectRaw("SUM(amount)+tax_applicable+tax_exemptions+interest_amount+roundoff_amount AS invoice_total")
            ->selectRaw("IFNULL((SELECT SUM(payment_amount) FROM income_invoice_payment WHERE fk_unit_invoice_id = unitInvoices.unit_invoice_id),'0.0')+IFNULL((SELECT SUM(writeoff_amount) FROM income_invoice_payment WHERE fk_unit_invoice_id = unitInvoices.unit_invoice_id),'0.0')+IFNULL((SELECT SUM(tds_deducted) FROM income_invoice_payment WHERE fk_unit_invoice_id = unitInvoices.unit_invoice_id),'0.0')+IFNULL((SELECT SUM(late_payment_charges) FROM income_invoice_payment WHERE fk_unit_invoice_id = unitInvoices.unit_invoice_id),'0.0') + IFNULL((SELECT SUM(tax_amount) FROM chsone_tax_log WHERE invoice_number = unitInvoices.invoice_number),0.0) AS total_paid_amount")
            ->selectRaw("(SUM(amount)+tax_applicable+tax_exemptions+interest_amount+roundoff_amount) - advance_amount - (IFNULL((SELECT SUM(payment_amount) FROM income_invoice_payment WHERE fk_unit_invoice_id = unitInvoices.unit_invoice_id),'0.0')+IFNULL((SELECT SUM(writeoff_amount) FROM income_invoice_payment WHERE fk_unit_invoice_id = unitInvoices.unit_invoice_id),'0.0')+IFNULL((SELECT SUM(tds_deducted) FROM income_invoice_payment WHERE fk_unit_invoice_id = unitInvoices.unit_invoice_id),'0.0')+IFNULL((SELECT SUM(late_payment_charges) FROM income_invoice_payment WHERE fk_unit_invoice_id = unitInvoices.unit_invoice_id),'0.0')) + IFNULL((SELECT SUM(tax_amount) FROM chsone_tax_log WHERE invoice_number = unitInvoices.invoice_number),0.0) AS balance_due_amount");
        //->selectRaw("SUM(amount)+SUM(tax_applicable)+SUM(tax_exemptions)+SUM(interest_amount)+SUM(roundoff_amount) AS suraj");
        $result = $obj->get();
        //dd($result);
        return $result;
    }
}
