<?php

namespace App\Console\Commands\Actions\Website\Profile;

use Illuminate\Console\Command;
use App\Console\Commands\Action;
use Illuminate\Support\Facades\Config;

class UpdateProfileDetailsAction extends Action
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'datasource:UpdateSocProfileDataSource {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Update Society Profile Details in Form';

    protected $formatter = [
        'id' => '',
        'soc_name' => '',
        'soc_reg_num' => '',
        'soc_gst_number' => '',
        'soc_subdomain_name' => '',
        'place_of_supply' => '',
        'soc_reg_num' => '',
        'soc_num_of_units' => '',
        'soc_address_1' => '',
        'soc_address_2' => '',
        'soc_landmark' => '',
        'soc_city_or_town' => '',
        'soc_pincode' => '',
        'firm_address' => '',
        'firm_name' => '',
        'soc_signature' => '',
        // 'soc_signature_path_f'=> '',
    ];
    
    protected $formatterByKeys = ['id'];

    /**
     * Execute the console command.
     */
    public function apply()
    {
        $request = $this->input;
        $id = $this->input['company_id'] ?? null;

        if (!empty(isset($request['data'][0]))) {            
            $profile = $request['data'][0]['profile'][0] ?? null;
            $gallery = $request['data'][0]['gallery'][0] ?? null;
            
            $profileData = [
                'soc_id' => $id ?? null,
                'soc_name' => $profile['soc_name'] ?? null,
                'soc_reg_num' => $profile['soc_reg_num'] ?? null,
                'soc_gst_number' => $profile['soc_gst_number'] ?? null,
                'soc_subdomain_name' => $profile['soc_subdomain_name'] ?? null,
                'place_of_supply' => $profile['place_of_supply'] ?? null,
                'soc_num_of_units' => $profile['soc_num_of_units'] ?? null,
                'soc_address_1' => $profile['soc_address_1'] ?? null,
                'soc_address_2' => $profile['soc_address_2'] ?? null,
                'soc_landmark' => $profile['soc_landmark'] ?? null,
                'soc_city_or_town' => $profile['soc_city_or_town'] ?? null,
                'soc_pincode' => $profile['soc_pincode'] ?? null,
                'firm_address' => $profile['firm_address'] ?? null,
                'firm_name' => $profile['firm_name'] ?? null,
                'soc_signature' => $profile['soc_signature'] ?? null,
            ];

            $obj = $this->masterDB()->table('chsone_societies_master')
            ->where('soc_id', $id)
            ->update($profileData);

            return $obj;
        
            // $galleryData = [
            //     'soc_id' => $id ?? null,
            //     // 'soc_photo_gallery_id' => $gallery['soc_photo_gallery_id'] ?? null,
            //     'image_name' => $gallery['image_name'] ?? null,
            //     'image_sequence' => $gallery['image_sequence'] ?? null,
            //     'added_on' => $gallery['added_on'] ?? null,
            //     'modified_on' => $gallery['modified_on'] ?? null,
            //     'status' => $gallery['status'] ?? null,
            // ];
            // $this->masterDB()->table('chsone_website_photo_gallery')
            // ->where('soc_id', $id)
            // ->update($galleryData);

            // $this->info('Profile Updated Successfully');
        }
    }
}
