<?php

namespace App\Console\Commands\Actions\Website\Profile;

use Illuminate\Console\Command;
use App\Console\Commands\Action;
use Illuminate\Support\Facades\Config;
use App\Models\ChsoneSocietiesMaster;
use App\Models\ChsoneWebsiteWelcome;
use App\Models\ChsoneWebsitePhotoGallery;

class GetSocWebsiteDetailsAction extends Action
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'datasource:GetSocWebsiteDetailsDataSource {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Get Society Profile Details in Form';

    protected $mapper = [
        'id' => 'master.soc_id'
    ];

    /**
     * Execute the console command.
     */        
    public function apply()
    {
        $id = $this->input['company_id'];
        $obj = $this->masterDB()->table('chsone_societies_master as master')
        ->leftJoin('chsone_website_welcome as welcome', 'master.soc_id', '=', 'welcome.soc_id')
        ->leftJoin('chsone_website_photo_gallery as gallery', 'master.soc_id', '=', 'gallery.soc_id')
        ->select(
            // profile
            'master.soc_id as id',
            'master.soc_name',
            'master.soc_subdomain_name',
            'master.soc_reg_num',
            'master.soc_gst_number',
            'master.place_of_supply',
            'master.soc_num_of_units',
            'master.soc_address_1',
            'master.soc_address_2',
            'master.soc_landmark',
            'master.soc_city_or_town',
            'master.soc_pincode',
            'master.firm_address',
            'master.firm_name',
            'master.soc_signature',

            // home
            'welcome.soc_web_id',
            'welcome.soc_welcome_msg',
            'welcome.soc_web_banner_1',
            'welcome.soc_web_banner_2',
            'welcome.soc_web_banner_3',

            // gallery
            'gallery.soc_photo_gallery_id',
            'gallery.image_name',
            'gallery.image_sequence',
            'gallery.added_on',
            'gallery.modified_on',
            'gallery.status',

            // developer
            'welcome.soc_developer_logo',
            'welcome.soc_developer',

            // contributors
            'welcome.soc_contributor_architect_logo',
            'welcome.soc_contributor_architect',
            'welcome.soc_contributor_facility_management_logo',
            'welcome.soc_contributor_facility_management',
            
            // committee
            'welcome.soc_committee',
            
            // amenities
            'welcome.soc_amenties_serialized',

            // websiteLayout 
            'welcome.soc_web_layout',
            'welcome.soc_header_logo',
            'welcome.soc_skin_color',

            // locationMap
            'welcome.soc_web_latitude',
            'welcome.soc_web_longitude',
            
            // contact
            'welcome.soc_contact_us_address',            
        )
        ->where('master.soc_id', $id)
        ->get();

        $result = $obj;
        $this->data = $result;
    }
}
