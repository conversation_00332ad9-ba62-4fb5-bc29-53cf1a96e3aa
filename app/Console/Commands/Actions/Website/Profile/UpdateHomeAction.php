<?php

namespace App\Console\Commands\Actions\Website\Profile;

use Illuminate\Console\Command;
use App\Console\Commands\Action;
use Illuminate\Support\Facades\Config;

class UpdateHomeAction extends Action
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'datasource:UpdateHomeDataSource {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Update Society Home Details in Form';

    protected $formatter = [
        'id' => '',
        'soc_welcome_msg' => '',
        'soc_web_banner_1' => '',
        'soc_web_banner_2' => '',
        'soc_web_banner_3' => '',
        'soc_developer_logo' => '',
        'soc_developer' => '',
        'soc_contributor_architect_logo' => '',
        'soc_contributor_architect' => '',
        'soc_contributor_facility_management_logo' => '',
        'soc_contributor_facility_management' => '',
        'soc_committee' => '',
        'soc_amenties_serialized' => '',
        'soc_web_layout' => '',
        'soc_header_logo' => '',
        'soc_skin_color' => '',
        'soc_web_latitude' => '',
        'soc_web_longitude' => '',
        'soc_contact_us_address' => '',
    ];
    
    protected $formatterByKeys = ['id'];

    /**
     * Execute the console command.
     */
    public function apply()
    {
        $request = $this->input;
        $id = $this->input['company_id'] ?? null;

        if (!empty(isset($request['data'][0]))) {            
            $home = $request['data'][0]['home'][0] ?? null;
            $developer = $request['data'][0]['developer'][0] ?? null;
            $contributors = $request['data'][0]['contributors'][0] ?? null;
            $committee = $request['data'][0]['committee'][0] ?? null;
            $amenities = $request['data'][0]['amenities'][0] ?? null;
            $websiteLayout = $request['data'][0]['websiteLayout'][0] ?? null;
            $locationMap = $request['data'][0]['locationMap'][0] ?? null;
            $contact = $request['data'][0]['contact'][0] ?? null;

            $data = [
                'soc_id' => $id ?? null,
                'soc_web_id' => $home['soc_web_id'] ?? null,
                'soc_welcome_msg' => $home['soc_welcome_msg'] ?? null,
                'soc_web_banner_1' => $home['soc_web_banner_1'] ?? null,
                'soc_web_banner_2' => $home['soc_web_banner_2'] ?? null,
                'soc_web_banner_3' => $home['soc_web_banner_3'] ?? null,
                'soc_developer_logo' => $developer['soc_developer_logo'] ?? null,
                'soc_developer' => $developer['soc_developer'] ?? null,
                'soc_contributor_architect_logo' => $contributors['soc_contributor_architect_logo'] ?? null,
                'soc_contributor_architect' => $contributors['soc_contributor_architect'] ?? null,
                'soc_contributor_facility_management_logo' => $contributors['soc_contributor_facility_management_logo'] ?? null,
                'soc_contributor_facility_management' => $contributors['soc_contributor_facility_management'] ?? null,
                'soc_committee' => $committee['soc_committee'] ?? null,
                'soc_amenties_serialized' => $amenities['soc_amenties_serialized'] ?? null,
                'soc_web_layout' => $websiteLayout['soc_web_layout'] ?? null,
                'soc_header_logo' => $websiteLayout['soc_header_logo'] ?? null,
                'soc_skin_color' => $websiteLayout['soc_skin_color'] ?? null,
                'soc_web_latitude' => $locationMap['soc_web_latitude'] ?? null,
                'soc_web_longitude' => $locationMap['soc_web_longitude'] ?? null,
                'soc_contact_us_address' => $contact['soc_contact_us_address'] ?? null,
            ];

            // dd($data);
            
            $obj = $this->masterDB()->table('chsone_website_welcome')
            ->where('soc_id', $id)
            ->update($data);

            return $obj;
        }
    }
}
