<?php

namespace App\Console\Commands\Actions\Website\Profile;

use Illuminate\Console\Command;
use App\Console\Commands\Action;
use Illuminate\Support\Facades\Config;

class UpdateSocWebsiteAction extends Action
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'datasource:UpdateSocWebsiteDataSource {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Update Society Profile Details in Form';

    protected $formatter = [
        'id' => '',
        'soc_name' => '',
        'soc_reg_num' => '',
        'soc_gst_number' => '',
        'soc_subdomain_name' => '',
        'place_of_supply' => '',
        'soc_reg_num' => '',
        'soc_num_of_units' => '',
        'soc_address_1' => '',
        'soc_address_2' => '',
        'soc_landmark' => '',
        'soc_city_or_town' => '',
        'soc_pincode' => '',
        'firm_address' => '',
        'firm_name' => '',
        'soc_signature' => '',
        // 'soc_signature_path_f'=> '',
    ];

    protected $formatterByKeys = ['id'];

    /**
     * Execute the console command.
     */
    public function apply()
    {
        try     {
        $request = $this->input;
        $id = $this->input['company_id'] ?? null;

        if (!empty(isset($request['data']))) {
            // dd($request['data']);
            $profile = $request['data']['profile'] ?? null;
            // $gallery = $request['data']['gallery'] ?? null;
            $home = $request['data']['home'] ?? null;
            $developer = $request['data']['developer'] ?? null;
            $contributors = $request['data']['contributors'] ?? null;
            $committee = $request['data']['committee'] ?? null;
            $amenities = $request['data']['amenities'] ?? null;
            $websiteLayout = $request['data']['websiteLayout'] ?? null;
            $locationMap = $request['data']['locationMap'] ?? null;
            $contact = $request['data']['contact'] ?? null;

            $profileData = [
                'soc_id' => $id ?? null,
                'soc_name' => $profile['soc_name'] ?? null,
                'soc_reg_num' => $profile['soc_reg_num'] ?? null,
                'soc_gst_number' => $profile['soc_gst_number'] ?? null,
                'soc_subdomain_name' => $profile['soc_subdomain_name'] ?? null,
                'place_of_supply' => $profile['place_of_supply'] ?? null,
                'soc_num_of_units' => $profile['soc_num_of_units'] ?? null,
                'soc_address_1' => $profile['soc_address_1'] ?? null,
                'soc_address_2' => $profile['soc_address_2'] ?? null,
                'soc_landmark' => $profile['soc_landmark'] ?? '',
                'soc_city_or_town' => $profile['soc_city_or_town'] ?? null,
                'soc_pincode' => $profile['soc_pincode'] ?? null,
                'firm_address' => $profile['firm_address'] ?? null,
                'firm_name' => $profile['firm_name'] ?? null,
                'soc_signature' => $profile['soc_signature'] ?? null,
            ];

            $homedata = [
                'soc_id' => $id ?? null,
                'soc_web_id' => $id ?? null,
                'soc_welcome_msg' => $home['soc_welcome_msg'] ?? null,
                'soc_web_banner_1' => $home['soc_web_banner_1'] ?? null,
                'soc_web_banner_2' => $home['soc_web_banner_2'] ?? null,
                'soc_web_banner_3' => $home['soc_web_banner_3'] ?? null,
                'soc_developer_logo' => $developer['soc_developer_logo'] ?? null,
                'soc_developer' => $developer['soc_developer'] ?? null,
                'soc_contributor_architect_logo' => $contributors['soc_contributor_architect_logo'] ?? null,
                'soc_contributor_architect' => $contributors['soc_contributor_architect'] ?? null,
                'soc_contributor_facility_management_logo' => $contributors['soc_contributor_facility_management_logo'] ?? null,
                'soc_contributor_facility_management' => $contributors['soc_contributor_facility_management'] ?? null,
                'soc_committee' => $committee['soc_committee'] ?? null,
                'soc_amenties_serialized' => $amenities['soc_amenties_serialized'] ?? null,
                'soc_web_layout' => $websiteLayout['soc_web_layout'] ?? null,
                'soc_header_logo' => $websiteLayout['soc_header_logo'] ?? null,
                'soc_skin_color' => $websiteLayout['soc_skin_color'] ?? null,
                'soc_web_latitude' => $locationMap['soc_web_latitude'] ?? null,
                'soc_web_longitude' => $locationMap['soc_web_longitude'] ?? null,
                'soc_contact_us_address' => $contact['soc_contact_us_address'] ?? null,
            ];

            $this->masterDB()->table('chsone_societies_master')
            ->where('soc_id', $id)
            ->update($profileData);

            $this->masterDB()->table('chsone_website_welcome')
            ->where('soc_id', $id)
            ->update($homedata);

            return ['message' => 'Tables updated successfully'];
        }
    } catch (\Exception $e) {
        dd($e);
        }
    }
}
