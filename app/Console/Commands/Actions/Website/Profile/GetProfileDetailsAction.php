<?php

namespace App\Console\Commands\Actions\Website\Profile;

use Illuminate\Console\Command;
use App\Console\Commands\Action;
use Illuminate\Support\Facades\Config;
use App\Models\ChsoneSocietiesMaster;
use App\Models\ChsoneWebsiteWelcome;
use App\Models\ChsoneWebsitePhotoGallery;

class GetProfileDetailsAction extends Action
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'datasource:getSocProfileDataSource {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Get Society Profile Details in Form';

    protected $formatter = [
        'id' => '',
        'soc_name' => '',
        'soc_reg_num' => '',
        'soc_gst_number' => '',
        'soc_subdomain_name' => '',
        'place_of_supply' => '',
        'soc_reg_num' => '',
        'soc_num_of_units' => '',
        'soc_address_1' => '',
        'soc_address_2' => '',
        'soc_landmark' => '',
        'soc_city_or_town' => '',
        'soc_pincode' => '',
        'firm_address' => '',
        'firm_name' => '',
        'soc_signature' => '',
        // 'soc_signature_path_f'=> '',
        'soc_web_id' => '',
        'soc_welcome_msg' => '',
        'soc_web_banner_1' => '',
        'soc_web_banner_2' => '',
        'soc_web_banner_3' => '',
        'soc_photo_gallery_id' => '',
        'image_name' => '',
        'image_sequence' => '',
        'added_on' => '',
        'modified_on' => '',
        'status' => '',        
    ];
    
    protected $formatterByKeys = ['id'];

    /**
     * Execute the console command.
     */        
    public function apply()
    {
        // dd(config('database.connections.mysql'));
        $id = $this->input['company_id'];
        // print_r($id);
        $obj = $this->masterDB()->table('chsone_societies_master as master')
        ->leftJoin('chsone_website_welcome as welcome', 'master.soc_id', '=', 'welcome.soc_id')
        ->leftJoin('chsone_website_photo_gallery as gallery', 'master.soc_id', '=', 'gallery.soc_id')
        ->select(
            // profile
            'master.soc_id as id',
            'master.soc_name',
            'master.soc_subdomain_name',
            'master.soc_reg_num',
            'master.soc_gst_number',
            'master.place_of_supply',
            'master.soc_num_of_units',
            'master.soc_address_1',
            'master.soc_address_2',
            'master.soc_landmark',
            'master.soc_city_or_town',
            'master.soc_pincode',
            'master.firm_address',
            'master.firm_name',
            'master.soc_signature',

            // home
            'welcome.soc_web_id',
            'welcome.soc_welcome_msg',
            'welcome.soc_web_banner_1',
            'welcome.soc_web_banner_2',
            'welcome.soc_web_banner_3',

            // gallery
            'gallery.soc_photo_gallery_id',
            'gallery.image_name',
            'gallery.image_sequence',
            'gallery.added_on',
            'gallery.modified_on',
            'gallery.status',

            // developer
            'welcome.soc_developer_logo',
            'welcome.soc_developer',

            // contributors
            'welcome.soc_contributor_architect_logo',
            'welcome.soc_contributor_architect',
            'welcome.soc_contributor_facility_management_logo',
            'welcome.soc_contributor_facility_management',
            
            // committee
            'welcome.soc_committee',
            
            // amenities
            'welcome.soc_amenties_serialized',

            // websiteLayout 
            'welcome.soc_web_layout',
            'welcome.soc_header_logo',
            'welcome.soc_skin_color',

            // locationMap
            'welcome.soc_web_latitude',
            'welcome.soc_web_longitude',
            
            // contact
            'welcome.soc_contact_us_address',            
        )
        ->where('master.soc_id', $id)
        ->first();

        $result = $obj;

        if ($result) {
            $profileData = [
                'id' => $result->id,
                'soc_name' => $result->soc_name,
                'soc_subdomain_name' => $result->soc_subdomain_name,
                'soc_reg_num' => $result->soc_reg_num,
                'soc_gst_number' => $result->soc_gst_number,
                'place_of_supply' => $result->place_of_supply,
                'soc_num_of_units' => $result->soc_num_of_units,
                'soc_address_1' => $result->soc_address_1,
                'soc_address_2' => $result->soc_address_2,
                'soc_landmark' => $result->soc_landmark,
                'soc_city_or_town' => $result->soc_city_or_town,
                'soc_pincode' => $result->soc_pincode,
                'firm_address' => $result->firm_address,
                'firm_name' => $result->firm_name,
                'soc_signature' => $result->soc_signature,
            ];
    
            $homeData = [
                'soc_web_id' => $result->soc_web_id,
                'soc_welcome_msg' => $result->soc_welcome_msg,
                'soc_web_banner_1' => $result->soc_web_banner_1,
                'soc_web_banner_2' => $result->soc_web_banner_2,
                'soc_web_banner_3' => $result->soc_web_banner_3,
            ];
    
            $galleryData = [
                'soc_photo_gallery_id' => $result->soc_photo_gallery_id,
                'image_name' => $result->image_name,
                'image_sequence' => $result->image_sequence,
                'added_on' => $result->added_on,
                'modified_on' => $result->modified_on,
                'status' => $result->status,
            ];
    
            $developerData = [
                'soc_developer_logo' => $result->soc_developer_logo,
                'soc_developer' => $result->soc_developer,
            ];
    
            $contributorsData = [
                'soc_contributor_architect_logo' => $result->soc_contributor_architect_logo,
                'soc_contributor_architect' => $result->soc_contributor_architect,
                'soc_contributor_facility_management_logo' => $result->soc_contributor_facility_management_logo,
                'soc_contributor_facility_management' => $result->soc_contributor_facility_management,
            ];
    
            $committeeData = [
                'soc_committee' => $result->soc_committee,
            ];
    
            $amenitiesData = [
                'soc_amenties_serialized' => $result->soc_amenties_serialized,
            ];
    
            $websiteLayoutData = [
                'soc_header_logo' => $result->soc_header_logo,
                'soc_skin_color' => $result->soc_skin_color,
            ];
    
            $locationMapData = [
                'soc_web_latitude' => $result->soc_web_latitude,
                'soc_web_longitude' => $result->soc_web_longitude,
            ];
    
            $contactData = [
                'soc_contact_us_address' => $result->soc_contact_us_address,
            ];
    
            $data = [
                'profile' => (array) ($profileData ?: []),
                'home' => (array) ($homeData ?: []),
                'gallery' => (array) ($galleryData ?: []),
                'developer' => (array) ($developerData ?: []),
                'contributors' => (array) ($contributorsData ?: []),
                'committee' => (array) ($committeeData ?: []),
                'amenities' => (array) ($amenitiesData ?: []),
                'websiteLayout' => (array) ($websiteLayoutData ?: []),
                'locationMap' => (array) ($locationMapData ?: []),
                'contact' => (array) ($contactData ?: []),
            ];
    
            $this->data = [$data];
        } else {
            // Handle the case when no record is found with the given soc_id
            // You can set an appropriate message or return an error response here.
            $this->data = [];
        }
    }
}
