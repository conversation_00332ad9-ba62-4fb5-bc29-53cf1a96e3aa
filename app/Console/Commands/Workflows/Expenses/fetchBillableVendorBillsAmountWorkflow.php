<?php

namespace App\Console\Commands\Workflows\Expenses;

use Illuminate\Console\Command;
use App\Console\Commands\Workflow;
use App\Models\Tenants\ChsoneUnitsMaster;

class fetchBillableVendorBillsAmountWorkflow extends Workflow
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'workflow:fetchBillableVendorBillsAmount {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Workflow for fetching billable vendor bills amount';

    /**
     * Execute the console command.
     */
    public function apply()
    {
        $fetchBillableVendorBillsAmount = $this->action('datasource:fetchBillableVendorBillsAmount', $this->pointer, $this->request);

        // need to calculate billable_amount from fetchBillableVendorBillsAmount data and set it to $this->data["billable_amount"] and no need to return fetchBillableVendorBillsAmount to $this->data
        $billableAmount = 0;
        $this->data = [];
        foreach ($fetchBillableVendorBillsAmount as $bill) {
            $billableAmount += $bill["vendor_bill_amount_final"];
        }

        $this->data["billable_amount"] = $billableAmount;

        // need to findout unit count from chsone_units_master table
        $unitCount = ChsoneUnitsMaster::where('soc_id', $this->input['company_id'])->count();
        $this->data['total_unit'] = $unitCount;

        return $this->data;

    }
}
