<?php

namespace App\Console\Commands\Workflows\Expenses;

use Illuminate\Console\Command;
use App\Console\Commands\Workflow;

class fetchpurchaseFormListWorkflow extends Workflow
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'workflow:fetchpurchaseFormList {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Purchase Form List Workflow';

    protected $formatter = [
        'id' => '',
        'purchase_form_title' => '',
        'purchase_form_amount' => '',
        'purchase_form_currency' => '',
        'purchase_form_po_number' => '',
        'purchase_form_vendor_name' => '',
        'status' => 0,
        'reject_by_name'=> '',
        'approval_pending_by_name'=> '',
        'approved_by'=> '',
        'refused_by_name'=> '',
        'reviewed_pending_by_name'=> '',
        'reviewed_by'=> '',
        'disable' => ''

    ];

    protected $formatterByKeys = ['id'];

    protected $mapper =[
        'id' => 'purchase_form_id',
    ];

    protected $hugeData = true;

    /**
     * Execute the console command.
     */
    public function apply()
    {
        $PurchaseList = $this->action('datasource:fetchpurchaseFormList', $this->pointer, $this->request);
        // if(empty($PurchaseList)){
        //     $this->status = 'error';
        //     $this->statusCode = 404;
        //     $this->message = 'No data found';
        //     return;
        // }
        $this->data = $PurchaseList;
        $this->data = $this->format((array)$this->data);
    }
}
