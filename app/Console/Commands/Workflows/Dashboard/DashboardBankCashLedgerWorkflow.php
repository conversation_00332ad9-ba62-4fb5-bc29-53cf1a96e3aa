<?php

namespace App\Console\Commands\Workflows\Dashboard;

use App\Console\Commands\Workflow;

class DashboardBankCashLedgerWorkflow extends Workflow
{
    protected $signature = 'workflow:dashboardBankCashLedger {input}';

    protected $description = 'Dashboard Bank Cash Ledger Workflow';

    public function apply()
    {
        $this->action('datasource:dashboardBankCashLedger', $this->pointer, $this->request);
    }
}