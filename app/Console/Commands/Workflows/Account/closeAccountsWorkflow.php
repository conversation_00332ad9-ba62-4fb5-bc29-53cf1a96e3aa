<?php

namespace App\Console\Commands\Workflows\Account;

use App\Console\Commands\Workflow;

class closeAccountsWorkflow extends Workflow {
    /**
    * The name and signature of the console command.
    *
    * @var string
    */
    protected $signature = 'workflow:closeAccounts {input}';

    /**
    * The console command description.
    *
    * @var string
    */
    protected $description = 'View all cash accounts';

    /**
    * Execute the console command.
    */

    protected $rules = [
        'account_closing_id' => 'required|integer|min:1|max:**********',
    ];
    
    protected $rulesMessages = [
        'account_closing_id.required' => 'Account closing id is required',
        'account_closing_id.integer' => 'Account closing id must be an integer',
        'account_closing_id.min' => 'Account closing id must be at least 1',
        'account_closing_id.max' => 'Account closing id must be at most **********',
    ];
    

    public function apply() {
        $this->request[ 'ledger' ] = 'cash';
        $vendorDetails = $this->action( 'datasource:closeAccounts', $this->pointer, $this->request );
        $this->data = $vendorDetails;
    }
}
