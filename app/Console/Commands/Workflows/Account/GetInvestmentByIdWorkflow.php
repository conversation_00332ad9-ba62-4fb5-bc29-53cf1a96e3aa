<?php

namespace App\Console\Commands\Workflows\Account;

use App\Console\Commands\Workflow;
use Illuminate\Console\Command;

class getInvestmentByIdWorkflow extends Workflow
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'workflow:getInvestmentById {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Fetch investment details.';

    /**
     * Execute the console command.
     */
    public function apply()
    {
        $investment_id = $this->input['investment_id'];

        if (!$investment_id || $investment_id == '' || $investment_id == ':investment_id') {
            $this->status = 'error';
            $this->message = 'Please provide a valid investment id';
            $this->statusCode = 400;
            $this->data = [];
        } else {
            $investmentDetails = $this->action('datasource:GetInvestMent', $this->pointer, $this->request);
            $this->data = $investmentDetails;
        }
    }
}
