<?php

namespace App\Console\Commands\Workflows\Account;

use App\Console\Commands\Workflow;
use Illuminate\Console\Command;

class assetDetailsWorkflow extends Workflow
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'workflow:assetDetails {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Fetch asset details.';

    /**
     * Execute the console command.
     */
    public function apply()
    {
        $assets_id = $this->input['assets_id'];

        if (!$assets_id || $assets_id == '' || $assets_id == ':assets_id') {
            $this->status = 'error';
            $this->message = 'Please provide a valid assets id';
            $this->statusCode = 400;
            $this->data = [];
        } else {
            $assetDetails = $this->action('datasource:assetDetails', $this->pointer, $this->request);
            $this->data = $assetDetails;
        }
    }
}
