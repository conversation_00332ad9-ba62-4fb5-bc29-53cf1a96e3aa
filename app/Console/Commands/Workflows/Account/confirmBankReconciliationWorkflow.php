<?php

namespace App\Console\Commands\Workflows\Account;

use Illuminate\Console\Command;
use App\Console\Commands\Workflow;

class confirmBankReconciliationWorkflow extends Workflow
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'workflow:confirmBankReconciliation {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Confirm Bank Reconciliation Workflow';

    /**
     * Execute the console command.
     */
    public function apply()
    {
        $soc_id = $this->input['company_id'];
        $month_no = $this->input['month'] ?? null;
        $yearSelected = $this->input['year'] ?? null;
        $bank_ledger_id = $this->input['bank_ledger_id'] ?? null;
        $bankClosingAmount = $this->input['bankClosingAmount'] ?? 0;

        // Validate that if any one of the inputs is provided, all three are required
        if ($month_no || $yearSelected || $bank_ledger_id || $bankClosingAmount) {
            if (empty($month_no) || empty($yearSelected) || empty($bank_ledger_id)) {
                $this->message = "All fields (month, year, bank_ledger_id, bankClosingAmount) are required if any one is provided.";
                $this->status = "error";
                $this->statusCode = 400;
                return;
            }
        }

        // Validation for yearSelected
        if ($yearSelected) {
            $yearData = explode('-', $yearSelected);

            // Validate if the input has exactly two parts after splitting
            if (count($yearData) !== 2) {
                $this->message = "Invalid Year Format";
                $this->status = "error";
                $this->statusCode = 400;
                return;
            }

            $start_year = intval($yearData[0]);
            $end_year = intval($yearData[1]);
            $currentYear = intval(date('Y'));

            // Validate that start_year and end_year are four-digit numbers
            if (strlen($yearData[0]) !== 4 || strlen($yearData[1]) !== 4) {
                $this->message = "Year should be in YYYY format";
                $this->status = "error";
                $this->statusCode = 400;
                return;
            }

            // Check if start_year is exactly one year less than end_year
            if ($end_year !== $start_year + 1) {
                $this->message = "End year must be exactly one year after the start year";
                $this->status = "error";
                $this->statusCode = 400;
                return;
            }

            // Check if start_year is not greater than the current year
            if ($start_year > $currentYear) {
                $this->message = "Start year cannot be greater than the current year";
                $this->status = "error";
                $this->statusCode = 400;
                return;
            }

            // Fetch the fy_start_date from the database
            $financialYearRecord = $this->tenantDB()->table('soc_account_financial_year_master')
                ->where('soc_id', $soc_id)
                ->orderBy('fy_start_date', 'asc')
                ->first();

            if (!$financialYearRecord) {
                $this->message = "Financial year master data not found";
                $this->status = "error";
                $this->statusCode = 400;
                return;
            }

            $fy_start_date = $financialYearRecord->fy_start_date;
            $fy_start_year = intval(date('Y', strtotime($fy_start_date)));

            if ($start_year < $fy_start_year) {
                $this->message = "Start year cannot be less than the financial year start date ($fy_start_year)";
                $this->status = "error";
                $this->statusCode = 400;
                return;
            }
        }

        // Validation for month_no
        if ($month_no) {
            $monthData = explode('-', $month_no);

            // Validate format: should be in "MM-YYYY" format
            if (count($monthData) !== 2) {
                $this->message = "Invalid Month Format";
                $this->status = "error";
                $this->statusCode = 400;
                return;
            }

            $month = intval($monthData[0]);
            $year = intval($monthData[1]);

            // Validate the extracted month and year
            if ($month < 1 || $month > 12) {
                $this->message = "Month should be between 1 and 12";
                $this->status = "error";
                $this->statusCode = 400;
                return;
            }

            if ($yearSelected) {
                $yearData = explode('-', $yearSelected);
                $start_year = intval($yearData[0]);
                $end_year = intval($yearData[1]);

                // Check if the extracted year is either the start_year or end_year
                if ($year !== $start_year && $year !== $end_year) {
                    $this->message = "In Month, Year must match either the start or end year of the financial year range";
                    $this->status = "error";
                    $this->statusCode = 400;
                    return;
                }

                // Additional validation based on the financial year
                if ($year === $start_year && ($month < 4 || $month > 12)) {
                    $this->message = "For the start year $start_year, the month should be between April (4) and December (12)";
                    $this->status = "error";
                    $this->statusCode = 400;
                    return;
                }

                if ($year === $end_year && ($month < 1 || $month > 3)) {
                    $this->message = "For the end year $end_year, the month should be between January (1) and March (3)";
                    $this->status = "error";
                    $this->statusCode = 400;
                    return;
                }
            }
        }

        // Validation for bank_ledger_id
        if ($bank_ledger_id) {
            if (!is_numeric($bank_ledger_id)) {
                $this->message = "Invalid Bank Ledger ID format. It should be a number.";
                $this->status = "error";
                $this->statusCode = 400;
                return;
            }

            $ledgerExists = $this->tenantDB()->table('chsone_grp_ledger_tree')
                ->where('ledger_account_id', $bank_ledger_id)
                ->exists();

            if (!$ledgerExists) {
                $this->message = "Bank Ledger ID not found in the system.";
                $this->status = "error";
                $this->statusCode = 404;
                return;
            }
        }

        // $month = $this->input['month'];
        // $month = explode('-', $month);
        // $month = date('F', mktime(0, 0, 0, $month[0], 10)).' '.$month[1];
        $bankReco = $this->action('datasource:confirmBankReconciliation', $this->pointer, $this->request);
        $this->data = $bankReco;
        // $this->meta['schema']['table']['tableTitle'] = "Bank Reconciliation for financial Year (".$this->input['year'].") / ".$month;
    }
}
