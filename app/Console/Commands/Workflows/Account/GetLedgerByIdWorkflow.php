<?php

namespace App\Console\Commands\Workflows\Account;

use App\Console\Commands\Workflow;
use Illuminate\Console\Command;

class getLedgerByIdWorkflow extends Workflow
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'workflow:getLedgerById {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Fetch ledger details.';

    /**
     * Execute the console command.
     */
    public function apply()
    {
        $ledger_account_id = $this->input['ledger_account_id'];

        if (!$ledger_account_id || $ledger_account_id == '' || $ledger_account_id == ':ledger_account_id') {
            $this->status = 'error';
            $this->message = 'Please provide a valid ledger account id';
            $this->statusCode = 400;
            $this->data = [];
        } else {
            $ledgerDetails = $this->action('datasource:getLedgerById', $this->pointer, $this->request);
            $this->data = $ledgerDetails;
        }
    }
}
