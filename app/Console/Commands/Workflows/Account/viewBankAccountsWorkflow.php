<?php

namespace App\Console\Commands\Workflows\Account;

use Illuminate\Console\Command;
use App\Console\Commands\Workflow;

class viewBankAccountsWorkflow extends Workflow
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'workflow:viewBankAccounts {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Get Bank Accounts Workflow';

    protected $hugeData = true;

    /**
     * Execute the console command.
     */
    public function apply()
    {
        $bankAccountsList = $this->action('datasource:viewBankAccountsList', $this->pointer, $this->request);
        $this->data = $bankAccountsList;
        }
}
