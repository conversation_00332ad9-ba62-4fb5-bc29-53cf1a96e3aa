<?php

namespace App\Console\Commands\Workflows\Account;

use Illuminate\Console\Command;
use App\Console\Commands\Workflow;

class viewCashAccountsWorkflow extends Workflow
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'workflow:viewCashAccounts {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'View all cash accounts';

    /**
     * Execute the console command.
     */
    public function apply()
    {
        $this->request['ledger'] = 'cash';
        $vendorDetails = $this->action('datasource:fetchDataAddVendorPayment', $this->pointer, $this->request);
        $this->data = $vendorDetails;
    }
}
