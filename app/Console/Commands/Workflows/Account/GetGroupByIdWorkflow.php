<?php

namespace App\Console\Commands\Workflows\Account;

use App\Console\Commands\Workflow;
use Illuminate\Console\Command;

class getGroupByIdWorkflow extends Workflow
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'workflow:getGroupById {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Fetch group details.';

    /**
     * Execute the console command.
     */
    public function apply()
    {
        $group_account_id = $this->input['group_account_id'];

        if (!$group_account_id || $group_account_id == '' || $group_account_id == ':group_account_id') {
            $this->status = 'error';
            $this->message = 'Please provide a valid group account id';
            $this->statusCode = 400;
            $this->data = [];
        } else {
            $groupDetails = $this->action('datasource:getGroupById', $this->pointer, $this->request);
            $this->data = $groupDetails;
        }
    }
}
