<?php

namespace App\Console\Commands\Workflows\Account;

use Illuminate\Console\Command;
use App\Console\Commands\Workflow;

class CreateLedgerWorkflow extends Workflow
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'workflow:CreateLedger {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Create Ledger Workflow';

    protected $formatter = [
    ];

    protected $rules = [
        'nature'   => 'required|in:dr,cr',
        'lstParentAccount'     => 'required|numeric',
        'ledger_account_name'     => 'required',
        // 'opening_balance'        => 'required'
    ];

    protected $rulesMessage = [
        'nature.required' => 'Nature not selected',
        'nature.in' => 'Nature should be dr or cr',
        'lstParentAccount.required' => 'Group not selected',
        'lstParentAccount.numeric' => 'Group should be numeric',
        'ledger_account_name.required' => 'Ledger Account Name not provided',
        // 'opening_balance.required' => 'Opening Balance not provided'
    ];

    protected $formatterKey = '';

    /**
     * Get table schema.
     */
    protected $schema = [];

    /**
     * Execute the console command.
     */
    public function apply()
    {
        return $AccountSetTracker = $this->action('datasource:CreateLedger', $this->pointer, $this->request);
    }

    public function add($a, $b)
    {
        return $a . ' - ' . $b;
    }

}
