<?php

namespace App\Console\Commands\Workflows\Account;

use Illuminate\Console\Command;
use App\Console\Commands\Workflow;

class GetAllLedgersWorkflow extends Workflow
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'workflow:getAllLedgers {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Get Ledgers List';


    protected $formatterKey = ['id'];

    /**
     * Execute the console command.
     */

    protected $rules = [
        'filters' => 'array',
        'filters.search' => 'string',
        'filters.name' => 'string',
        'filters.behaviour' => 'string',
        // 'filters.nature' => 'string|in:cr,dr',
        'filters.status' => 'string|in:1,0',
        'page' => 'integer',
        'per_page' => 'integer',
    ];
    public function apply()
    {
        $getAllLedgers = $this->action('datasource:getAllLedgers', $this->pointer, $this->request);
        $this->data = $getAllLedgers;
        $this->meta['schema'] = $this->schema();
    }

    public function schema()
    {
        return $meta = [
            "table" => [
                "tableTitle" => "Accounts",
                "select_by" => [
                    "name" => "Ledger Account Name",
                    "behaviour" => "Behaviour"
                ],
                "is_searchable"=>true,
                "filter_by" => [
                    "nature" => [
                        "title" => "Nature of Account",
                        "options" => [
                            "cr" => "Credit",
                            "dr" => "Debit",
                        ]
                    ],
                    "status" => [
                        "title" => "Status",
                        "options" => [
                            "1" => "Active",
                            "0" => "Inactive",
                        ]
                    ],
                ],
                "actions" => [
                    [
                        "title" => "Download Ledger",
                        "color" => "primary",
                        "variant" => "contained",
                        "form" => "downloadLedger",
                    ],
                    [
                        "title" => "New Ledger",
                        "icon" => "ri-add-circle-line",
                        "color" => "primary",
                        "redirect" => "/admin/accounts/createLedger",
                        "variant" => "contained"
                    ]
                ],
                "fields" => [
                    "*"
                ],
                "columns" => [
                    [
                        "title" => "Name",
                        "key" => "name"
                    ],
                    [
                        "title" => "Nature",
                        "key" => "nature",
                    ],
                    [
                        "title" => "Behaviour",
                        "key" => "behaviour"
                    ],
                    [
                        "title" => "Purpose",
                        "key" => "entity_type"
                    ],
                    [
                        "title" => "Status",
                        "key" => "status",
                        "type" => "chip",
                        "options" => [
                            "1" => [
                                "title" => "Active",
                                "color" => "success"
                            ],
                            "0" => [
                                "title" => "Inactive",
                                "color" => "error"
                            ],
                        ]
                    ],
                    [
                        "title" => "Actions",
                        "type" => "actions",
                        "key" => "actions",
                        "actions" => [
                            [
                                "title" => "Edit",
                                "icon" => "ri-edit-box-line",
                                "href" => "/admin/accounts/editLedger/:id",
                                "color" => "#0a4f92",
                            ],
                            [
                                "title" => "List Transaction",
                                "icon" => "ri-bank-card-2-line",
                                "href" => "/admin/transaction/listTransactionMonthly/:id",
                                "color" => "#0a4f92",
                            ]
                        ]
                    ]
                ]
            ]
        ];
    }
}
