<?php

namespace App\Console\Commands\Workflows\Account;

use Illuminate\Console\Command;
use App\Console\Commands\Workflow;

class MembersUnitLedgerReportWorkflow extends Workflow
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'workflow:MembersUnitLedgerReportWorkflow {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Member Unit Ledger Report Workflow';

    protected $schema = [
        'table' => [
            'tableTitle' => [
                'Member Unit Ledger Statement',
                'Summary'
            ],
            // "filters" => [
            //     "unit_number" => [
            //         "title" => "Unit :",
            //         "type" => "autocomplete",
            //     ],
            //     "transaction_date_from" => [
            //         "title" => "From Date",
            //         "type" => "date",
            //     ],
            //     "transaction_date_to" => [
            //         "title" => "To Date",
            //         "type" => "date",
            //     ],
            // ],
            "extraFilters" => [
                "unit_id" => [
                    "title" => "Unit :",
                    "type" => "autocomplete",
                ],
                "transaction_date" => [
                    "title" => "Select Date Range",
                    "type" => "daterange",
                    "minDateTime" => "financialYearStart",
                    "maxDateTime" => "financialYearEnd",
                ],
                // "type" => [
                //     "title" => "Type",
                //     "options" => [
                //         "both" => "Both",
                //         "maintenance" => "Maintenance",
                //         "incidental" => "Incidental",
                //     ],
                // ],
                // "refundable" => [
                //     "title" => "Refunable",
                //     "options" => [
                //         "yes" => "Yes",
                //         "no" => "No",
                //     ],
                // ]
            ],
            'actions' => [
                [
                    "title" => "Print/Export",
                    "icon" => "ri-printer-line",
                    "options" => [
                        [
                            "title" => "PDF",
                            "icon" => "ri-file-pdf-2-line",
                            "api" => [
                                "method" => "GET",
                                "url" => "/admin/accountsreporting/membersUnitAccountStatementReport/download/pdf",
                                "type" => "download"
                            ]
                        ],
                        [
                            "title" => "Excel",
                            "icon" => "ri-file-excel-2-line",
                            "api" => [
                                "method" => "GET",
                                "url" => "/admin/accountsreporting/membersUnitAccountStatementReport/download/excel",
                                "type" => "download"
                            ]
                        ]
                    ]
                ]
            ],
            'fields' => [
                '*'
            ],
            'columns' => [
                [
                    // [
                    //     'title' => 'ID',
                    //     'key' => 'id'
                    // ],
                    [
                        'title' => 'Date',
                        'key' => 'transaction_date'
                    ],
                    [
                        'title' => 'Particulars',
                        'key' => 'particulars'
                    ],
                    [
                        'title' => 'Vch Type',
                        'key' => 'voucher_type'
                    ],
                    [
                        'title' => 'Vch No.',
                        'key' => 'voucher_reference_number'
                    ],
                    [
                        'title' => 'Debit',
                        'key' => 'debit'
                    ],
                    [
                        'title' => 'Credit',
                        'key' => 'credit'
                    ],
                ],
                [
                    [
                        "title" => "Total Debit",
                        "key" => "total_debit",
                    ],
                    [
                        "title" => "Total Credit",
                        "key" => "total_credit",
                    ],
                    [
                        "title" => "Total (Debit-Credit)",
                        "key" => "total_amount",
                    ]
                ]
            ]
        ],
        // "summary_table" => [
        //     "tableTitle" => "Summary",
        //     "fields" => [
        //         "total_amount",
        //     ],
        //     "columns" => [
        //         [
        //             "title" => "Total",
        //             "key" => "total_amount"
        //         ],
        //     ]
        // ]
    ];

    protected $rules = [];

    protected $rulesMessage = [];

    protected $formatter = [
        'id' => '',
        'transaction_date' => '',
        'particulars' => '',
        'voucher_type' => '',
        'voucher_reference_number' => '',
        'debit' => '',
        'credit' => '',
    ];

    protected $formatterByKeys = [
        'id'
    ];

    /**
     * Execute the console command.
     */
    public function apply()
    {
        $data = $this->action('datasource:MembersUnitLedgerReport', $this->pointer, $this->request);
        $this->meta['schema'] = $this->schema;
    }
}
