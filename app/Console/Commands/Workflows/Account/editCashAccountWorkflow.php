<?php

namespace App\Console\Commands\Workflows\Account;

use Illuminate\Console\Command;
use App\Console\Commands\Workflow;

class editCashAccountWorkflow extends Workflow
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'workflow:editCashAccount {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Edit a cash account';

    protected $rules = [
        'bank_name' => 'required',
    ];

    protected $rulesMessages = [
        'bank_name.required' => 'Cash name is required',
    ];

    /**
     * Execute the console command.
     */
    public function apply()
    {
        $editCashAccountDetails = $this->action('datasource:editCashAccount', $this->pointer, $this->request);
        $this->data = $editCashAccountDetails;
    }
}
