<?php

namespace App\Console\Commands\Workflows\Account;

use Illuminate\Console\Command;
use App\Console\Commands\Workflow;

class assetsDownloadWorkflow extends Workflow
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'workflow:assetsDownload {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'List all assets for an account.';

    /**
     * Get table schema.
     */

     protected $schema = [
        "table" => [
            "tableTitle" => "Assets",
            "actions" => [
                [
                    "title" => "New Assets",
                    "icon" => "ri-add-circle-line",
                    "color" => "primary",
                    "redirect" => "/admin/assets/addAsset",
                ],
                [
                    "title" => "Asset Categories",
                    "icon" => "ri-list-check",
                    "color" => "secondary",
                    "redirect" => "/admin/assets/settings",
                ],
                [
                    "title" => "Print/Export",
                    "icon" => "ri-printer-line",
                    "options" => [
                        [
                            "title" => "Excel",
                            "icon" => "ri-file-excel-2-line",
                            "api" => [
                                "url" => "/admin/assets/downloadAssets/excel",
                                "method" => "POST",
                                "type" => "download"
                            ]
                        ],
                        [
                            "title" => "PDF",
                            "icon" => "ri-file-pdf-2-line",
                            "api" => [
                                "method" => "POST",
                                "url" => "/admin/assets/downloadAssets/pdf",
                                "type" => "download"
                            ]
                        ]
                    ]
                ]
            ],
            "fields" => [
                "*"
            ],
            "columns" => [
                [
                    "title" => "Name",
                    "key" => "assets_name"
                ],
                [
                    "title" => "Tag No",
                    "key" => "assets_tag_number"
                ],
                [
                    "title" => "Vendor Name",
                    "key" => "vendor_name"
                ],
                [
                    "title" => "Category",
                    "key" => "assets_categories_name"
                ],
                [
                    "title" => "Location",
                    "key" => "assets_location"
                ],
                [
                    "title" => "Purchase Date",
                    "key" => "assets_purchase_date"
                ],
                [
                    "title" => "Asset Cost",
                    "key" => "assets_cost"
                ],
                [
                    "title" => "Actions",
                    "type" => "actions",
                    "key" => "actions",
                    "actions" => [
                        [
                            "title" => "Edit Asset",
                            "icon" => "ri-edit-box-line",
                            "href" => "/admin/assets/editAsset/:id",
                        ],
                        [
                            "title" => "View Asset",
                            "icon" => "ri-eye-line",
                            "href" => "/admin/assets/assetDetails/:id",
                        ]
                    ]
                ]
            ]
        ]
    ];

    protected $headings = [
        'assets_name'=>'Name',
        'assets_tag_number'=>'Tag No',
        'vendor_name'=>'Vendor Name',
        'assets_categories_name'=>'Category',
        'assets_location'=>'Location',
        'assets_purchase_date'=>'Purchase Date',
        'assets_cost'=>'Asset Cost',
        'status'=>'Status'
    ];
    protected $rules = [
        'type' => 'required|string|in:pdf,excel',
    ];

    protected $rulesMessage = [
        'type.required' => 'Type is required',
        'type.string' => 'Type must be a string',
        'type.in' => 'Type must be either pdf or excel',
    ];

    /**
     * Execute the console command.
     */

    public function apply()
    {

        $type = $this->input['type'];

        if(!$type || $type == '' || $type == ':type')
        {
            $this->status = 'error';
            $this->message = 'Please provide a valid type either excel or pdf';
            $this->statusCode = 400;
            $this->data = [];
        }
        else{
            $assetsList = $this->action('datasource:assetsList', $this->pointer, $this->request);

            // dd($assetsList);
            $this->data = [];


            $this->meta['schema'] = $this->schema;

            $newData = array_map(function($item){
                $obj =[

                    'assets_name' => $item['assets_name'],
                    'assets_tag_number' => $item['assets_tag_number'],
                    'vendor_name' => $item['vendor_name'],
                    'assets_categories_name' => $item['assets_categories_name'],
                    'assets_location' => $item['assets_location'],
                    'assets_purchase_date' => $item['assets_purchase_date'],
                    'assets_cost' => $item['assets_cost'],
                    'status' => $item['status'] == 1 ? 'Active' : ($item['status'] == 3 ? 'Inactive' : $item['status'])
                ];
                return $obj;
            }, $assetsList);

            if($type == 'excel')
            {

                $data = $this->hitCURLForGenerateCSV($newData, $this->headings, 'assetList_');
                $this->data['url'] = $data['data'];
            }
            else{
                $data = $this->hitCURLForGeneratePDF($newData, $this->headings, 'assetList');
                $this->data['url'] = $data['data'];
            }
        }
    }
}
