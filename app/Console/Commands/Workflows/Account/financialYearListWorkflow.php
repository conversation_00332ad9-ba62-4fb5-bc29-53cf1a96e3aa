<?php

namespace App\Console\Commands\Workflows\Account;

use Illuminate\Console\Command;
use App\Console\Commands\Workflow;

class financialYearListWorkflow extends Workflow
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'workflow:financialYearList {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Get Financial Year Workflow';

    /**
     * Execute the console command.
     */
    public function apply()
    {
        $financialYearListTracker = $this->action('datasource:financialYearList', $this->pointer, $this->request);
        $this->data = $financialYearListTracker;
    }


}
