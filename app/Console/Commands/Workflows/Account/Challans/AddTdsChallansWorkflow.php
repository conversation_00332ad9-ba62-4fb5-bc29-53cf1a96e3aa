<?php

namespace App\Console\Commands\Workflows\Account\Challans;

use Illuminate\Console\Command;
use App\Console\Commands\Workflow;

class AddTdsChallansWorkflow extends Workflow
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'workflow:addTdsChallans {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Add TDS Challan';

    protected $rules = [
        "challan_no" => "required|string|max:255",
        "assessment_year" => "required|string|regex:/^\d{4}-\d{4}$/",
        "tax_applicable" => "required|in:(0020)Company Deductees,(0021)Non-Company Deductees",
        "tan_no" => "required|string|max:255",
        "type_of_payment" => "required|string|max:255",
        "bsr_code" => "required|string|max:7",
        "on_account_of" => "required|string|max:255",
        "challan_serial_no" => "required|string|max:255",
        "payment_mode" => "required|in:cash,cheque,online,cashtransfer",
        "bank_account" => "required_if:payment_mode,cheque,online,cashtransfer|nullable|numeric",
        "transaction_reference" => "required_if:payment_mode,cheque|digits:6",
        "payment_date" => "required|date_format:Y-m-d|before_or_equal:today",
        "submitted_bank" => "required|string|max:255",
        "income_tax" => "nullable|numeric|min:0",
        "surcharges" => "nullable|numeric|min:0",
        "education_cess" => "nullable|numeric|min:0",
        "interest" => "nullable|numeric|min:0",
        "penalty_code" => "nullable|numeric|min:0",
        "penalty" => "nullable|numeric|min:0",
        "others" => "nullable|numeric|min:0",
        "fee_under_sec_234e" => "nullable|numeric|min:0",
        "total" => "nullable|numeric|min:1",
    ];

    protected $rulesMessage = [
        "challan_no.required" => "Challan number is required",
        "assessment_year.required" => "Assessment year is required",
        "assessment_year.regex" => "Invalid year format",
        "tax_applicable.required" => "Tax applicable is required",
        "tan_no.required" => "TAN number is required",
        "type_of_payment.required" => "Type of payment is required",
        "bsr_code.required" => "BSR code is required",
        "bsr_code.max" => "BSR code should be 7 characters",
        "on_account_of.required" => "On account of is required",
        "challan_serial_no.required" => "Challan serial number is required",
        "payment_mode.required" => "Payment mode is required",
        "bank_account.required_if" => "Bank account is required",
        "transaction_reference.required_if" => "Transaction reference number is required",
        "payment_date.required" => "Payment date is required",
        "payment_date.date_format" => "Invalid date format",
        "payment_date.before_or_equal" => "Payment date cannot be a future date",
        "submitted_bank.required" => "Submitted bank is required",
        "income_tax.numeric" => "Income tax should be a number",
        "income_tax.min" => "Income tax should be greater than or equal to 0",
        "surcharges.numeric" => "Surcharges should be a number",
        "surcharges.min" => "Surcharges should be greater than or equal to 0",
        "education_cess.numeric" => "Education cess should be a number",
        "education_cess.min" => "Education cess should be greater than or equal to 0",
        "interest.numeric" => "Interest should be a number",
        "interest.min" => "Interest should be greater than or equal to 0",
        "penalty_code.numeric" => "Penalty code should be a number",
        "penalty_code.min" => "Penalty code should be greater than or equal to 0",
        "penalty.numeric" => "Penalty should be a number",
        "penalty.min" => "Penalty should be greater than or equal to 0",
        "others.numeric" => "Others should be a number",
        "others.min" => "Others should be greater than or equal to 0",
        "fee_under_sec_234e.numeric" => "Fee under section 234E should be a number",
        "fee_under_sec_234e.min" => "Fee under section 234E should be greater than or equal to 0",
        "total.numeric" => "Total should be a number",
        "total.min" => "Total should be greater than 0",
    ];

    /**
     * Execute the console command.
     */
    public function apply()
    {

        $soc_id = $this->input['company_id'];
        $yearSelected = $this->input['assessment_year'];
        $total = $this->input['total'];

        // Validation for yearSelected
        if ($yearSelected) {
            $yearData = explode('-', $yearSelected);

            // Validate if the input has exactly two parts after splitting
            if (count($yearData) !== 2) {
                $this->message = "Invalid Year Format";
                $this->status = "error";
                $this->statusCode = 400;
                return;
            }

            $start_year = intval($yearData[0]);
            $end_year = intval($yearData[1]);
            $currentYear = intval(date('Y'));

            // Validate that start_year and end_year are four-digit numbers
            if (strlen($yearData[0]) !== 4 || strlen($yearData[1]) !== 4) {
                $this->message = "Year should be in YYYY format";
                $this->status = "error";
                $this->statusCode = 400;
                return;
            }

            // Check if start_year is exactly one year less than end_year
            if ($end_year !== $start_year + 1) {
                $this->message = "End year must be exactly one year after the start year";
                $this->status = "error";
                $this->statusCode = 400;
                return;
            }

            // // Check if start_year is not greater than the current year
            // if ($start_year > $currentYear) {
            //     $this->message = "Start year cannot be greater than the current year";
            //     $this->status = "error";
            //     $this->statusCode = 400;
            //     return;
            // }

            // Fetch the fy_start_date from the database
            $financialYearRecord = $this->tenantDB()->table('soc_account_financial_year_master')
                ->where('soc_id', $soc_id)
                ->orderBy('fy_start_date', 'asc')
                ->first();

            if (!$financialYearRecord) {
                $this->message = "Financial year master data not found";
                $this->status = "error";
                $this->statusCode = 400;
                return;
            }

            $fy_start_date = $financialYearRecord->fy_start_date;
            $fy_start_year = intval(date('Y', strtotime($fy_start_date)));

            if ($start_year < $fy_start_year) {
                $this->message = "Start year cannot be less than the financial year start date ($fy_start_year)";
                $this->status = "error";
                $this->statusCode = 400;
                return;
            }
        }

        if($total){
            $inputTotal = $this->input['income_tax']+ $this->input['surcharges']+ $this->input['education_cess']+ $this->input['interest']+ $this->input['penalty']+ $this->input['others']+ $this->input['fee_under_sec_234e']+ $this->input['penalty_code'];

            if($inputTotal != $total){
                $this->message = "Total amount is not equal to sum of all fields";
                $this->status = "error";
                $this->statusCode = 400;
                return;
            }
        }
        $response = $this->action('action:addTdsChallans', $this->pointer, $this->request);
        $this->data = $response;
    }
}
