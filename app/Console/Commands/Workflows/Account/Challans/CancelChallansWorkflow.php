<?php

namespace App\Console\Commands\Workflows\Account\Challans;

use Illuminate\Console\Command;
use App\Console\Commands\Workflow;

class CancelChallansWorkflow extends Workflow
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'workflow:cancelChallans {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Cancel TDS Challan';

    protected $rules =[
        "id"=>"required|numeric|min:1|max:********",
        "cancel_reason"=>"required|string",
    ];

    protected $messages = [
        "id.required" => "Challan ID is required",
        "id.numeric" => "Challan ID must be numeric",
        "id.min" => "Challan ID must be at least 1",
        "id.max" => "Challan ID may not be greater than ********",
        "cancel_reason.required" => "Cancel Reason is required",
        "cancel_reason.string" => "Cancel Reason must be a string",
    ];

    /**
     * Execute the console command.
     */
    public function apply()
    {
        $response = $this->action('action:cancelChallans', $this->pointer, $this->request);
        $this->data = $response;
        $this->data = $this->format((array)$this->data);
    }
}
