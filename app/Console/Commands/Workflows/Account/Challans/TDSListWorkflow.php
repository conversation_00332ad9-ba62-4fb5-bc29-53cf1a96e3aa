<?php

namespace App\Console\Commands\Workflows\Account\Challans;

use Illuminate\Console\Command;
use App\Console\Commands\Workflow;

class TDSListWorkflow extends Workflow
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'workflow:tdsList {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Get TDS Challans List';

    protected $formatterByKeys = ['id'];

    /**
     * Execute the console command.
     */

    protected $rules = [
        'page'=>'integer',
        'per_page'=>'integer'
    ];


    protected $rulesMessage = [
        'page.integer'=>'Page must be an integer',
        'per_page.integer'=>'Per page must be an integer'
    ];

    public function apply()
    {
        $tdsList = $this->action('datasource:tdsList', $this->pointer, $this->request);
        $this->data = $tdsList;
        $this->meta['schema'] = $this->schema();
    }

    public function schema()
    {
        return $meta = [
            "table" => [
                "tableTitle" => "TDS Challans",
                "actions" => [
                    [
                        "title" => "New Challan",
                        "icon" => "ri-add-line",
                        "color" => "primary",
                        "variant" => "contained",
                        "redirect" => "/admin/tds-challans/add"
                    ]
                ],
                "fields" => [
                    "*"
                ],
                "columns" => [
                    [
                        "title" => "Challan No.",
                        "key" => "challan_no"
                    ],
                    [
                        "title" => "Assessment Year",
                        "key" => "assessment_year",
                    ],
                    [
                        "title" => "BSR Code",
                        "key" => "bsr_code"
                    ],
                    [
                        "title" => "Tender Date",
                        "key" => "tender_date"
                    ],
                    [
                        "title" => "Total Amount",
                        "key" => "total_sum"
                    ],
                    [
                        "title" => "Actions",
                        "type" => "actions",
                        "key" => "actions",
                        "actions" => [
                            [
                                "title" => "View",
                                "icon" => "ri-eye-line",
                                "href" => "/admin/tds-challans/view/:id",
                            ],
                            [
                                "title" => "Cancel Challan",
                                "icon" => "ri-close-line",
                                "form" => "cancelChallan",
                            ]
                        ]
                    ]
                ]
            ]
        ];
    }

    public function add($it, $srchrg, $edCess, $penaltyCode, $int, $penalty, $others, $fee)
    {
        return (float)$it + (float)$srchrg + (float)$edCess + (float)$penaltyCode + (float)$int + (float)$penalty + (float)$others + (float)$fee;
    }
}
