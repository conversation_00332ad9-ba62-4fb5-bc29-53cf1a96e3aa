<?php

namespace App\Console\Commands\Workflows\Account\Challans;

use Illuminate\Console\Command;
use App\Console\Commands\Workflow;

class GetTdsDetailsWorkflow extends Workflow
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'workflow:getTdsDetails {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Get TDS Challan details';

    /**
     * Execute the console command.
     */

    protected $rules = [
        'id' => 'required|integer'
    ];

    protected $rulesMessage = [
        'id.required' => 'Challan id is required',
        'id.integer' => 'Challan id must be an integer'
    ];
    public function apply()
    {
        $challanDetails = $this->action('action:getTdsDetails', $this->pointer, $this->request);
        $this->data = $challanDetails;
        $this->meta['schema'] = $this->schema();
    }

    public function schema()
    {
        return $meta = [
            "table" => [
                "tableTitle" => "View TDS Challan",
                "actions" => [
                    [
                        "title" => "Back",
                        "icon" => "ri-arrow-go-back-line",
                        "color" => "primary",
                        "variant" => "contained",
                        "redirect" => "/admin/tds-challans/list"
                    ]
                ],
                "fields" => [
                    "*"
                ],
                "columns" => [
                    [
                        "title" => "Challan No.",
                        "key" => "challan_no"
                    ],
                    [
                        "title" => "Assessment Year",
                        "key" => "assessment_year",
                    ],
                    [
                        "title" => "BSR Code",
                        "key" => "bsr_code"
                    ],
                    [
                        "title" => "Tender Date",
                        "key" => "tender_date"
                    ],
                    [
                        "title" => "TAN No",
                        "key" => "tan_no"
                    ],
                    [
                        "title" => "Type of Payment",
                        "key" => "type_of_payment"
                    ],
                    [
                        "title" => "On Account of",
                        "key" => "on_account_of",
                    ],
                    [
                        "title" => "Challan Serial No",
                        "key" => "challan_serial_no"
                    ],
                    [
                        "title" => "Payment Mode",
                        "key" => "payment_mode"
                    ],
                    [
                        "title" => "Bank",
                        "key" => "submitted_bank"
                    ],
                    [
                        "title" => "Total Amount",
                        "key" => "total_amt"
                    ],
                    [
                        "title" => "Tax Applicable",
                        "key" => "tax_applicable"
                    ]
                ]
            ]
        ];
    }
}
