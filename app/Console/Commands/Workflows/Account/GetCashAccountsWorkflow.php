<?php

namespace App\Console\Commands\Workflows\Account;

use Illuminate\Console\Command;
use App\Console\Commands\Workflow;
use App\Models\Tenants\ChsoneGrpLedgerTree; 


class GetCashAccountsWorkflow extends Workflow
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'workflow:GetCashAccounts {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Get Cash Accounts Workflow';

    protected $formatterKey = 'id';

    /**
     * Execute the console command.
     */
    public function apply()
    {
        $ledgerObj = new ChsoneGrpLedgerTree();
        $whereConditions = [
            'context' => 'cash'
        ];
        $cashLedgers = $ledgerObj->fetchLedgerTree($whereConditions);
        $this->data = $cashLedgers;
        $this->message = "Cash Accounts fetched successfully";
    }


}
