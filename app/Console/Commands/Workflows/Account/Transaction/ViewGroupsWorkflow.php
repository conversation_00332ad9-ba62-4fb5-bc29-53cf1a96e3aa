<?php

namespace App\Console\Commands\Workflows\Account\Transaction;

use Illuminate\Console\Command;
use App\Console\Commands\workflow;

class ViewGroupsWorkflow extends workflow
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'workflow:ViewGroups {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Get Ledger Group Tree Workflow';

    protected $schema = [
        "table" => [
            "tableTitle" => "Groups",
            "actions" => [
                [
                    "title" => "New Groups",
                    "icon" => "ri-add-circle-line",
                    "color" => "primary",
                    "redirect" => "/admin/accounts/createGroup",
                    "variant" => "contained"
                ]
            ],
            "fields" => [
                "*"
            ],
            "columns" => [
                [
                    "title" => "Group/Ledger",
                    "key" => "ledger_account_name"
                ],
                [
                    "title" => "Nature of Group",
                    "key" => "nature_of_account",
                    "options" => [
                        "dr" => "Debit",
                        "cr" => "Credit"
                    ]
                ],
                [
                    "title" => "Behaviour",
                    "key" => "behaviour"
                ],
                [
                    "title" => "Purpose",
                    "key" => "context"
                ],
                [
                    "title" => "Status",
                    "key" => "status",
                    "type" => "chip",
                    "options" => [
                        "0"=>[
                            "title" => "Inactive",
                            "color" => "error"
                        ],
                        "1"=>[
                            "title" => "Active",
                            "color" => "success"
                        ]
                    ]
                ],
                [
                    "title" => "Actions",
                    "type" => "actions",
                    "key" => "actions",
                    "actions" => [
                        [
                            "title" => "Edit Group",
                            "icon" => "ri-edit-2-line",
                            "href" => "/admin/accounts/editGroup/:id",
                            "color" => "primary",
                            "hide_on" => [
                                "hide" => 1
                            ]
                        ],
                        [
                            "title" => "Change Status",
                            "icon" => "ri-close-line",
                            // "href" => "/admin/accounts/changeGroupStatus/:id",
                            "api" => [
                                "method" => "put",
                                "url" => "/admin/accounts/changeGroupStatus/:id?status=0",
                            ],
                            "color" => "primary",
                            "hide_on" => [
                                "hide" => 1
                            ]
                        ]
                    ]
                ]
            ]
        ]
    ];

    /**
     * Execute the console command.
     */
    public function apply()
    {
        $listVoucherTracker = $this->action('datasource:ViewGroups', $this->pointer, $this->request);
        $this->meta['schema'] = $this->schema;
    }
}
