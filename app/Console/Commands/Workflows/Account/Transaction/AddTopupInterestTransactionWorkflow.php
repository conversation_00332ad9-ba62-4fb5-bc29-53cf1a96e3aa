<?php

namespace App\Console\Commands\Workflows\Account\Transaction;

use Illuminate\Console\Command;
use App\Console\Commands\workflow;

class AddTopupInterestTransactionWorkflow extends workflow
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
     
    protected $signature = 'workflow:AddTopupInterestTransaction {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Interest and Topup Transaction';

    protected $rules = [
        'transaction_date' => 'required|date',
        'amount' => 'required|numeric',
    ];

    protected $rulesMessage = [
        'transaction_date.required' => 'Transaction Date is required',
        'transaction_date.date' => 'Transaction Date must be a date',
        'amount.required' => 'Amount is required',
        'amount.numeric' => 'Amount must be a number'
    ];

    protected $schema = [];

    /**
     * Execute the console command.
     */
    public function apply()
    {
        $PosNewLedger = $this->action('datasource:AddTopupInterestTransaction', $this->pointer, $this->request);
    }
}
