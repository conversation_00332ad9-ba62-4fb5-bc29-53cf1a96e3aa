<?php

namespace App\Console\Commands\Workflows\Account\Transaction;

use App\Console\Commands\Workflow;

class GeneralSettingsPDFWorkflow extends Workflow
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'workflow:generalSettingsPDF {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Delete Transaction Workflow';

    protected $schema = [];
    protected $formatterByKeys = [];
    /**
     * Execute the console command.
     */

   
     public $headings = [
        "highlightfield" => "Highlight Field",
    ];

    public function apply()
    {
        $filename = $this->input['filename'];
        $label = $this->input['label'];

        $obj = [
            "highlightfield"=>$label
        ];
            $data = $this->hitCURLForGeneratePDF($obj, $this->headings, $filename);
            $this->data['url'] = $data['data'];
        
    }
}
