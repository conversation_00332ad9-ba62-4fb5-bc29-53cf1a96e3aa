<?php

namespace App\Console\Commands\Workflows\Account\Transaction;

use Illuminate\Console\Command;
use App\Console\Commands\Workflow;

class deleteAssetSettingsWorkflow extends Workflow
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'workflow:deleteAssetSettings {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Delete Asset Settings Workflow';

    protected $rules = [
        "assets_category_id" => "required",
    ];

    protected $rulesMessage = [
        "assets_category_id.required" => "The asset category id is required.",
    ];

    /**
     * Execute the console command.
     */
    public function apply()
    {
        $deleteAssetsCategory = $this->action('datasource:deleteAssetSettings', $this->pointer, $this->request);
    }
}
