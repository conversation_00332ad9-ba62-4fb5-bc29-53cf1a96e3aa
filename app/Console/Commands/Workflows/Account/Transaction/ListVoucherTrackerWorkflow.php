<?php

namespace App\Console\Commands\Workflows\Account\Transaction;

use Illuminate\Console\Command;
use App\Console\Commands\Workflow;

class ListVoucherTrackerWorkflow extends Workflow
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'workflow:ListVoucherTracker {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Workflow for list voucher tracker';

    protected $hugeData = true;

    protected $schema = [
        'table' => [
            'tableTitle' => 'Voucher Tracker',
            "select_by" => [
                "voucher_id" => "Vloucher Id",
                "type" => "Voucher Type",
                "transaction_date" => "Transaction Date",
                "from_ledger_account_name" => "From",
                "to_ledger_account_name" => "To",
                "amount" => "Amount",
                "narration" => "Narration"
            ],
            "filter_by" => [
                "status" => [
                    "title" => "Status",
                    "options" => [
                        "1" => "Active",
                        "0" => "Deleted"
                    ]
                ]
            ],
            'fields' => [
                '*'
            ],
            'columns' => [
                [
                    'title' => 'Id',
                    'key' => 'id'
                ],
                [
                    'title' => 'Voucher Id',
                    'key' => 'voucher_id'
                ],
                [
                    'title' => 'Voucher Type',
                    'key' => 'type'
                ],
                [
                    'title' => 'Transaction Date',
                    'key' => 'transaction_date'
                ],
                [
                    'title' => 'From / By',
                    'key' => 'from_ledger_account_name'
                ],
                [
                    'title' => 'To',
                    'key' => 'to_ledger_account_name'
                ],
                [
                    'title' => 'Amount',
                    'key' => 'amount'
                ],
                [
                    'title' => 'Reference',
                    'key' => 'reference'
                ],
                [
                    'title' => 'Narration',
                    'key' => 'narration'
                ],
                [
                    'title' => 'Status',
                    'key' => 'status',
                    "type" => "chip",
                    "options" => [
                        "0"=>[
                            "title" => "Deleted",
                            "color" => "error"
                        ],
                        "1"=>[
                            "title" => "Active",
                            "color" => "success"
                        ]
                    ]
                ],
                [
                    'title' => 'Actions',
                    'type' => 'actions',
                    'key' => 'actions',
                    'actions' => [
                        [
                            'title' => 'View',
                            'icon' => 'ri-eye-line',
                            'api' => [
                                'method' => 'GET',
                                'url' => '/admin/transaction/viewVoucherReciept/:id',
                                'type' => "view"
                            ],
                        ],
                        [
                            'title' => 'Download',
                            'icon' => 'ri-download-2-fill',
                            'api' => [
                                'method' => 'GET',
                                'url' => '/admin/transaction/viewVoucherReciept/download/:id',
                                'type' => "download"
                            ],
                        ],
                        [
                            'title' => 'Delete',
                            'icon' => 'ri-delete-bin-5-fill',
                            'api' => [
                                'method' => 'delete',
                                'url' => '/admin/transaction/deleteVoucher/:id'
                            ],
                        ]
                    ]
                ]
            ]
        ]
    ];

    protected $rules = [
        'voucher_id' => 'numeric|min:1|max:100000000',
        'transaction_date' => 'date|date_format:Y-m-d',
        'amount' => 'numeric|min:0',
        'page' => 'numeric|min:1',
        'per_page' => 'numeric|min:1',
    ];

    protected $rulesMessage = [
      "voucher_id.numeric" => "Voucher Id must be a number",
        "voucher_id.min" => "Voucher Id must be greater than 0",
        "voucher_id.max" => "Voucher Id must be less than 100000000",
        "transaction_date.date" => "Transaction Date must be a date",
        "transaction_date.date_format" => "Transaction Date must be in Y-m-d format",
        "amount.numeric" => "Amount must be a number",
        "amount.min" => "Amount must be greater than 0",
        "page.numeric" => "Page must be a number",
        "page.min" => "Page must be greater than 0",
        "per_page.numeric" => "Per Page must be a number",
        "per_page.min" => "Per Page must be greater than 0",


    ];



    protected $formatterByKeys = ['voucher_id'];

    /**
     * Execute the console command.
     */
    public function apply()
    {
        $listVoucherTracker = $this->action('datasource:ListVoucherTracker', $this->pointer, $this->request);
        $this->data = $listVoucherTracker;
        $this->meta['schema'] = $this->schema;
    }
}
