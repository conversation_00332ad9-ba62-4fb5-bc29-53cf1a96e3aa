<?php

namespace App\Console\Commands\Workflows\Account\Transaction;

use Illuminate\Console\Command;
use App\Console\Commands\Workflow;
use App\Models\Tenants\ChsoneGrpLedgerTree;

class ListTransactionYearlyWorkflow extends Workflow
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'workflow:listTransactionYearly {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'List Monthly Transaction';

    protected $formatterByKeys = ['id'];

    /**
     * Execute the console command.
     */
    public function apply()
    {
        // $listTransactionMonthly = $this->action('datasource:listTransactionMonthly', $this->pointer, $this->request);
        $listTransactionYearly = $this->action('datasource:listTransactionYearly', $this->pointer, $this->request);
        $this->data = $listTransactionYearly;
        // fetch ledger name to display table title in schema  
        $ledgerId = $this->input['id'];
        $ledger_name = ChsoneGrpLedgerTree::select('ledger_account_name')->where('ledger_account_id', $ledgerId)->first();
        $ledger_name = $ledger_name->ledger_account_name;

        $this->meta['schema'] = $this->schema();
        $this->meta['schema']['table']['tableTitle'] = 'Account: '.$ledger_name;
    }

    public function schema()
    {
        return $meta = [
            "table" => [
                "tableTitle" => "Account: ",
                "actions" => [
                    [
                        "title" => "Print/Export",
                        "icon" => "ri-export-line",
                        "options" => [
                            [
                                "title" => "Excel",
                                "icon" => "ri-file-excel-2-line"
                            ]
                        ]
                    ]
                ],
                "fields" => [
                    "*"
                ],
                "columns" => [
                    [
                        "title" => "Date",
                        "key" => "transaction_date"
                    ],
                    [
                        "title" => "Ledger Account",
                        "key" => "counter_ledger_account_name",
                    ],
                    [
                        "title" => "Narration",
                        "key" => "memo_desc"
                    ],
                    [
                        "title" => "Debit",
                        "key" => "transaction_amount"
                    ],
                    [
                        "title" => "Credit",
                        "key" => "transaction_amount",
                    ],
                    [
                        "title" => "Balance",
                        "key" => "balance",
                    ],
                    [
                        "title" => "Actions",
                        "type" => "actions",
                        "key" => "actions",
                        "actions" => [
                            [
                                "title" => "Edit",
                                "icon" => "ri-edit-box-line"
                            ],
                            [
                                "title" => "Delete",
                                "icon" => "ri-delete-bin-5-line"
                            ]
                        ]
                    ]
                ]
            ]
        ];
    }
}
