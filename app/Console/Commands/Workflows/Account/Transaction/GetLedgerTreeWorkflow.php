<?php

namespace App\Console\Commands\Workflows\Account\Transaction;

use App\Console\Commands\workflow;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class GetLedgerTreeWorkflow extends workflow
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'workflow:GetLedgerTree {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Get Ledger Tree Workflow';

    // protected $rules = [
    //     'current_tab' => 'required',
    // ];

    // protected $rulesMessage = [
    //     'current_tab.required' => 'Current tab name is required',
    // ];

    protected $schema = [
        "table" => [
            "tableTitle" => "Charts of Accounts",
            "tabs" => [
                "Asset",
                "Liability",
                "Income",
                "Expense",
            ],
            "actions" => [
                [
                    "title" => "Download Ledger",
                    "color" => "primary",
                    "redirect" => "/admin/accounts/downloadLedgers",
                    "variant" => "contained",
                ],
                [
                    "title" => "New Ledger",
                    "icon" => "ri-add-circle-line",
                    "color" => "primary",
                    "redirect" => "/admin/accounts/createLedger",
                    "variant" => "contained",
                ],
            ],
            "fields" => [
                "*",
            ],
            "columns" => [
                [
                    "title" => "Name",
                    "key" => "ledger_account_name",
                    "filterable" => true,
                ],
                [
                    "title" => "Group/Ledger",
                    "key" => "entity_type",
                ],
                [
                    "title" => "Nature",
                    "key" => "nature_of_account",
                    "options" => [
                        "dr" => "Debit",
                        "cr" => "Credit",
                    ],
                ],
                [
                    "title" => "Behaviour",
                    "key" => "behaviour",
                    "filterable" => true,
                ],
                [
                    "title" => "Purpose",
                    "key" => "context",
                ],
                [
                    "title" => "Status",
                    "key" => "status",
                    "type" => "chip",
                    "options" => [
                        "0" => [
                            "title" => "Inactive",
                            "color" => "error",
                        ],
                        "1" => [
                            "title" => "Active",
                            "color" => "success",
                        ],
                    ],
                ],
                [
                    "title" => "Actions",
                    "type" => "actions",
                    "key" => "actions",
                    "actions" => [
                        [
                            "title" => "Edit Ledger",
                            "icon" => "ri-edit-box-line",
                            "href" => "/admin/accounts/editLedger/:id",
                            "hide_on" => [
                                "hide" => [
                                    1
                                ],
                            ]
                        ],
                        [
                            "title" => "View Ledger",
                            "icon" => "ri-eye-line",
                            "href" => "/admin/transaction/listTransactionMonthly/:id",
                            "hide_on" => [
                                "hide" => [
                                    1
                                ],
                            ]
                        ],
                        [
                            "title" => "Change Status",
                            "icon" => "ri-check-line",
                            "color" => "error",
                            "api" => [
                                "method" => "put",
                                "url" => "/admin/accounts/deleteLedger/:id?status=1",
                            ],
                            "rerender" => true,
                            "hide_on" => [
                                "status" => [
                                    1,
                                ],
                                "_action" => [
                                    "add"
                                ],
                                "hide" => [
                                    1
                                ]
                            ]
                        ],
                        [
                            "title" => "Change Status",
                            "icon" => "ri-close-line",
                            "color" => "error",
                            "api" => [
                                "method" => "put",
                                "url" => "/admin/accounts/deleteLedger/:id?status=0",
                            ],
                            "rerender" => true,
                            "hide_on" => [
                                "status" => [
                                    0,
                                ],
                                "_action" => [
                                    "add"
                                ],
                                "hide" => [
                                   1
                                ]
                            ]
                        ],
                    ],
                ],
            ],
        ],
    ];

    public function tenantDB()
    {
        return DB::connection('tenant');
    }

    /**
     * Execute the console command.
     */
    public function apply()
    {
        $soc_id = $this->input['company_id'];
        $dropdown = $this->input['dropdown'] ?? null;
        if ($dropdown && $dropdown == 'journal') {
            $rawData = $this->action('datasource:GetLedgerTreeJournal', $this->pointer, $this->request);
        } else {
            $rawData = $this->action('datasource:GetLedgerTree', $this->pointer, $this->request);
        }
        // dd($rawData);
        if ($dropdown && $dropdown == 'journal') {
            $this->data = $this->addHideRecursively1($rawData);
        } elseif ($dropdown && $dropdown == 'contra') {
            $this->data = $this->addHideRecursively2($rawData);
        } else {
            $this->data = $this->addHideRecursively($rawData);
        }
        $this->meta['schema'] = $this->schema;

        $result = $this->tenantDB()
            ->table('chsone_grp_ledger_tree')
            ->whereIn('nature_of_account', ['dr', 'cr', 'Credit'])
            ->where('soc_id', $soc_id)
            ->where(function ($query) {
                $query->where('parent_id', 0)
                      ->orWhereNull('parent_id');
            })
            ->pluck('ledger_account_name');


        $this->meta['schema']['table']['tabs'] = $result;
    }
    
    private function addHideRecursively(array $items, int $depth = 0): array
    {
        foreach ($items as &$item) {
            $item['rows'] = $item['rows'] ?? [];

            // If it's nested (i.e. depth > 0), hide = 0, else hide = 1
            $item['hide'] = $depth > 0 ? 0 : 1;

            // Recursively apply to children, increasing depth
            if (!empty($item['rows'])) {
                $item['rows'] = $this->addHideRecursively($item['rows'], $depth + 1);
            }
        }

        return $items;
    }

    private function addHideRecursively1(array $items, int $depth = 0): array
    {
        foreach ($items as &$item) {
            // Default rows to empty array if not set
            $item['rows'] = $item['rows'] ?? [];
    
            // Set hide based on depth
            $item['hide'] = $depth > 0 ? 0 : 1;
    
            // Recursively handle children
            if (!empty($item['rows'])) {
                $item['rows'] = $this->addHideRecursively1($item['rows'], $depth + 1);
            }

            // 🧹 Remove rows ONLY if it's a leaf and empty
            if (isset($item['entity_type']) && $item['entity_type'] === 'ledger' && empty($item['rows'])) {
                unset($item['rows']);
            }
            
        }
    
        return $items;
    }

    private function addHideRecursively2(array $items, int $depth = 0): array
    {
        $result = [];

        foreach ($items as $item) {
            $item['rows'] = $item['rows'] ?? [];

            // Recursively filter and process children
            if (!empty($item['rows'])) {
                $item['rows'] = $this->addHideRecursively2($item['rows'], $depth + 1);
            }

            // Set hide flag
            $item['hide'] = $depth > 0 ? 0 : 1;

            // 🧹 Remove rows if it's a leaf and empty
            if (
                $item['entity_type'] === 'ledger' &&
                empty($item['rows'])
            ) {
                unset($item['rows']);
            }

            // ✅ Filter: keep only items with context 'bank' or 'cash'
            if (
                isset($item['context']) &&
                in_array($item['context'], ['bank', 'cash'])
            ) {
                $result[] = $item;
            }
            // ✅ Or if it has children matching the filter, include it too
            elseif (!empty($item['rows'])) {
                $result[] = $item;
            }
        }

        return $result;
    }

    
}
