<?php

namespace App\Console\Commands\Workflows\Account\Transaction;

use Illuminate\Console\Command;
use App\Console\Commands\workflow;

class AddBankCashTransactionWorkflow extends workflow
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
     
    protected $signature = 'workflow:AddBankCashTransaction {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Insert Bank Cash Transaction';

    protected $rules = [
        'from_ledger'   => 'required',
        'to_ledger'     => 'required',
        'memo_desc'     => 'required',
        'amount'        => 'required|numeric|min:1',
        'transaction_date' => 'required|date',
    ];

    protected $rulesMessage = [
        'from_ledger.required' => 'From Account not selected',
        'to_ledger.required' => 'To Account not selected',
        'transaction_date.required' => 'Transaction Date not provided',
        'memo_desc.required' => 'Memo Description not provided',
        'amount.required' => 'Transaction amount not provided',
        'amount.numeric' => 'Transaction amount should be numeric',
        'transaction_date.date' => 'Transaction Date should be a date',
        'amount.min' => 'Transaction amount should be greater than 0',
    ];

    protected $schema = [];

    /**
     * Execute the console command.
     */
    public function apply()
    {
        $PosNewLedger = $this->action('datasource:AddBankCashTransaction', $this->pointer, $this->request);
    }
}
