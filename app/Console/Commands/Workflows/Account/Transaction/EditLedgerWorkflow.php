<?php

namespace App\Console\Commands\Workflows\Account\Transaction;

use Illuminate\Console\Command;
use App\Console\Commands\Workflow;

class EditLedgerWorkflow extends Workflow
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'workflow:EditLedger {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Edit Ledger';

    protected $formatterByKeys = ['id'];

    /**
     * Execute the console command.
     */
    public function apply()
    {
        $editLdger = $this->action('datasource:EditLedger', $this->pointer, $this->request);
    }

}
