<?php

namespace App\Console\Commands\Workflows\Account\Transaction;

use Illuminate\Console\Command;
use App\Console\Commands\Workflow;

class EditAssetWorkflow extends Workflow
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'workflow:EditAsset {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Edit Asset Workflow';

    protected $rules = [
        "assets_name" => "required",
        "assets_tag_number" => "required",
        "assets_categories_id" => "required",
        "vendor_bill_type_purchase" => "required|in:cash,bank,credit|string",
        "cash_ledger" => "required_if:vendor_bill_type_purchase,cash",
        "card_ledger" => "required_if:vendor_bill_type_purchase,bank",
        "assets_purchase_date" => 'required|date|date_format:Y-m-d',
        "assets_cost" => "required|numeric",
        "assets_categories_id" => "required",
        "vendor_bill_type_purchase" => "required",
        "assets_appreciation" => "numeric",
        "assets_depreciation" => "numeric",
    ];

    protected $rulesMessage = [
        "assets_name.required" => "Asset name is required",
        "assets_tag_number.required" => "Asset tag number is required",
        "assets_categories_id.required" => "Asset category is required",
        "vendor_bill_type_purchase.required" => "Purchase type is required",
        "vendor_bill_type_purchase.in" => "Purchase type must be cash, bank or credit",
        "vendor_bill_type_purchase.string" => "Purchase type must be a string",
        "cash_ledger.required_if" => "Cash ledger is required if purchase type is cash",
        "card_ledger.required_if" => "Card ledger is required if purchase type is bank",
        "assets_purchase_date.required" => "Asset purchase date is required",
        "assets_purchase_date.date" => "Asset purchase date must be a date",
        "assets_purchase_date.date_format" => "Asset purchase date must be in Y-m-d format",
        "assets_cost.required" => "Asset cost is required",
        "assets_cost.numeric" => "Asset cost must be a number",
        "assets_categories_id.required" => "Asset category is required",
        "assets_appreciation.numeric" => "Asset appreciation must be a number",
        "assets_depreciation.numeric" => "Asset depreciation must be a number",
    ];

    /**
     * Execute the console command.
     */
    public function apply()
    {
        $editLdger = $this->action('datasource:EditAsset', $this->pointer, $this->request);
    }

}
