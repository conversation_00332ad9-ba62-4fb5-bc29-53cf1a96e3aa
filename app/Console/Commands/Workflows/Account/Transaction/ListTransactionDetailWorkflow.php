<?php

namespace App\Console\Commands\Workflows\Account\Transaction;

use App\Console\Commands\Workflow;

class ListTransactionDetailWorkflow extends Workflow
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'workflow:listTransactionDetail {input?}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Delete Transaction Workflow';

    protected $schema = [];
    protected $formatterByKeys = [];
    /**
     * Execute the console command.
     */

    protected $rules = [
        "id" => "required|numeric|min:1",
    ];

    protected $rulesMessage = [
        "id.required" => "Ledger Id is required",
        "id.numeric" => "Ledger Id must be numeric",
        "id.min" => "Ledger Id must be greater than 0",
    ];

    public function apply()
    {
        $data = $this->action('datasource:listTransactionDetail', $this->pointer, $this->request);
        $this->data = $data;
    }
}
