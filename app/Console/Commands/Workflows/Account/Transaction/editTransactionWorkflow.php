<?php

namespace App\Console\Commands\Workflows\Account\Transaction;

use Illuminate\Console\Command;
use App\Console\Commands\Workflow;

class editTransactionWorkflow extends Workflow
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'workflow:editTransaction {input?}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Edit Transaction Workflow';

    protected $rules = [
        "id" => "required|numeric|min:1",
        "transaction_date" => "required|date|date_format:Y-m-d",
        // "from_ledger_account_name" => "required|string",
        "from_transaction_amount" => "required|numeric|min:0",
        "from_transaction_type" => "required|string|in:dr,cr",
        "from_memo_desc" => "required|string",
        "to_ledger_id" => "required|numeric|min:1",
        // "to_ledger_account_name" => "required|string",
        "to_transaction_amount" => "required|numeric|min:0",
        "to_transaction_type" => "required|string|in:dr,cr",
        "to_memo_desc" => "required|string",
    ];

    protected $rulesMessage = [
        "id.required" => "Ledger Id is required",
        "id.numeric" => "Ledger Id must be numeric",
        "id.min" => "Ledger Id must be greater than 0",
        "transaction_date.required" => "Transaction Date is required",
        "transaction_date.date" => "Transaction Date must be a date",
        "transaction_date.date_format" => "Transaction Date must be in the format Y-m-d",
        // "from_ledger_account_name.required" => "From Ledger Account Name is required",
        // "from_ledger_account_name.string" => "From Ledger Account Name must be a string",
        // "from_transaction_amount.required" => "Transaction Amount is required",
        "from_transaction_amount.numeric" => "Transaction Amount must be numeric",
        "from_transaction_amount.min" => "Transaction Amount min value is 0",
        "from_transaction_type.required" => "From Transaction Type is required",
        "from_transaction_type.string" => "From Transaction Type must be a string",
        "from_transaction_type.in" => "From Transaction Type must be either dr or cr",
        "from_memo_desc.required" => "Memo Description is required",
        "from_memo_desc.string" => "Memo Description must be a string",
        "to_ledger_id.required" => "To Ledger is required",
        "to_ledger_id.numeric" => "To Ledger must be numeric",
        "to_ledger_id.min" => "To Ledger must be greater than 0",
        // "to_ledger_account_name.required" => "To Ledger Account Name is required",
        // "to_ledger_account_name.string" => "To Ledger Account Name must be a string",
        "to_transaction_amount.required" => "Transaction Amount is required",
        "to_transaction_amount.numeric" => "Transaction Amount must be numeric",
        "to_transaction_amount.min" => "Transaction Amount min value is 0",
        "to_transaction_type.required" => "To Transaction Type is required",
        "to_transaction_type.string" => "To Transaction Type must be a string",
        "to_transaction_type.in" => "To Transaction Type must be either dr or cr",
        "to_memo_desc.required" => "To Memo Description is required",
        "to_memo_desc.string" => "To Memo Description must be a string",
    ];

    /**
     * Execute the console command.
     */
    public function apply()
    {
        $data = $this->action('datasource:editTransaction', $this->pointer, $this->request);
        $this->data = $data;
    }
}
