<?php

namespace App\Console\Commands\Workflows\Account\Transaction;

use Illuminate\Console\Command;
use App\Console\Commands\workflow;

class PostNewGroupWorkflow extends workflow
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
     
    protected $signature = 'workflow:PostNewGroup {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Insert Ledger';

    // protected $rules = [
    //     'current_tab' => 'required',
    // ];

    // protected $rulesMessage = [
    //     'current_tab.required' => 'Current tab name is required',
    // ];

    protected $schema = [];

    /**
     * Execute the console command.
     */
    public function apply()
    {
        $PosNewLedger = $this->action('datasource:PostNewGroup', $this->pointer, $this->request);
    }
}
