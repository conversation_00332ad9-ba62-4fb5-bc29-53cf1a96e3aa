<?php

namespace App\Console\Commands\Workflows\Account\Transaction;

use Illuminate\Console\Command;
use App\Console\Commands\Workflow;

class UpdateAssetSettingWorkflow extends Workflow
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'workflow:updateAssetSetting {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Update a single asset setting by ID';

    /**
     * The validation rules for the workflow.
     *
     * @var array
     */
    protected $rules = [
        // 'assets_category_id' => 'required|numeric',
        // 'assets_categories_name' => 'required|string',
        // 'assets_categories_type' => 'required|string|in:Movable,Immovable',
    ];

    /**
     * Custom error messages for validation.
     *
     * @var array
     */
    protected $rulesMessage = [
        // 'assets_category_id.required' => 'The asset category ID is required.',
        // 'assets_category_id.numeric' => 'The asset category ID must be a number.',
        // 'assets_categories_name.required' => 'The asset category name is required.',
        // 'assets_categories_name.string' => 'The asset category name must be a string.',
        // 'assets_categories_type.required' => 'The asset category type is required.',
        // 'assets_categories_type.string' => 'The asset category type must be a string.',
        // 'assets_categories_type.in' => 'The asset category type must be either Movable or Immovable.',
    ];

    /**
     * Execute the console command.
     */
    public function apply()
    {
        $this->data = $this->action('datasource:updateAssetSetting', $this->pointer, $this->input);
    }
}
