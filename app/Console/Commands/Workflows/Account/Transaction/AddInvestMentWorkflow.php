<?php

namespace App\Console\Commands\Workflows\Account\Transaction;

use Illuminate\Console\Command;
use App\Console\Commands\workflow;

class AddInvestMentWorkflow extends workflow
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
     
    protected $signature = 'workflow:AddInvestMent {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'AddInvestMent Workflow';

    protected $rules = [
        "type" => "required",
        "year" => "required",
        "investment_account_number" => "required|numeric",
        "investment_bank_name" => "required",
        "investment_bank_branch" => "required",
        "investment_bank_address" => "required",
        "investment_bank_city" => "required",
        'investment_bank_ifsc' => 'required|regex:/^[A-Z]{4}0[A-Z0-9]{6}$/',
        "amount" => "required|numeric",
        "payment_mode" => "required|in:cash,bank|string",
        // if payment mode is bank then bank_ledger is required
        "bank_ledger" => "required_if:payment_mode,bank",
        'start_date' => 'required|date|date_format:Y-m-d',
        'maturity_date' => 'required|date|date_format:Y-m-d',
    ];

    protected $rulesMessage = [
        "type.required" => "Type of investment is required",
        "year.required" => "Year of investment is required",
        "investment_account_number.required" => "Investment account number is required",
        "investment_account_number.numeric" => "Investment account number must be a number",
        "investment_bank_name.required" => "Investment bank name is required",
        "investment_bank_branch.required" => "Investment bank branch is required",
        "investment_bank_address.required" => "Investment bank address is required",
        "investment_bank_city.required" => "Investment bank city is required",
        "investment_bank_ifsc.required" => "Investment bank ifsc is required",
        "investment_bank_ifsc.regex" => "Investment bank ifsc must be in correct format",
        "amount.required" => "Amount is required",
        "amount.numeric" => "Amount must be a number",
        "payment_mode.required" => "Payment mode is required",
        "payment_mode.in" => "Payment mode must be cash or bank",
        "payment_mode.string" => "Payment mode must be a string",
        "bank_ledger.required_if" => "Bank ledger is required if payment mode is bank",
        "start_date.required" => "Start date is required",
        "start_date.date" => "Start date must be a date",
        "start_date.date_format" => "Start date must be in Y-m-d format",
        "maturity_date.required" => "Maturity date is required",
        "maturity_date.date" => "Maturity date must be a date",
        "maturity_date.date_format" => "Maturity date must be in Y-m-d format",
    ];

    protected $schema = [];

    /**
     * Execute the console command.
     */
    public function apply()
    {
        $PosNewLedger = $this->action('datasource:AddInvestMent', $this->pointer, $this->request);
    }
}
