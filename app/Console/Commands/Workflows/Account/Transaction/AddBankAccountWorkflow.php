<?php

namespace App\Console\Commands\Workflows\Account\Transaction;

use Illuminate\Console\Command;
use App\Console\Commands\workflow;

class AddBankAccountWorkflow extends workflow
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */

    protected $signature = 'workflow:AddBankAccount {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Insert Ledger';

    protected $rules = [
        "bank_name" => "required",
        "account_number" => "required|numeric",
        "bank_address" => "required",
        "bank_ifsc" => "required|regex:/^(?=.*[A-Z])(?=.*\d)[A-Z0-9]+$/",
        "opening_balance" => "required|numeric"
    ];
    
    protected $rulesMessage = [
        "bank_name.required" => "Bank Name is required.",
        "account_number.required" => "Account Number is required.",
        "account_number.numeric" => "Account Number must be a numeric value.",
        "bank_address.required" => "Bank Address is required.",
        "bank_ifsc.required" => "IFSC Code is required.",
        "bank_ifsc.regex" => "IFSC Code must contain only uppercase alphabets and numbers.",
        "opening_balance.required" => "Opening Balance is required.",
        "opening_balance.numeric" => "Opening Balance must be a numeric value."
    ];

    protected $schema = [];

    /**
     * Execute the console command.
     */
    public function apply()
    {
        $PosNewLedger = $this->action('datasource:AddBankAccount', $this->pointer, $this->request);
    }
}
