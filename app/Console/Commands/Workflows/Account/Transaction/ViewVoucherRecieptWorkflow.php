<?php

namespace App\Console\Commands\Workflows\Account\Transaction;

use Illuminate\Console\Command;
use App\Console\Commands\Workflow;

class ViewVoucherRecieptWorkflow extends Workflow
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'workflow:ViewVoucherReciept {input?}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'View Voucher Reciept Workflow';

    protected $schema = [];
    protected $formatterByKeys = ['voucher_id'];

    protected $rules = [
        'id' => 'required|integer|min:1',
    ];

    protected $rulesMessage = [
        'id.required' => 'Voucher ID is required',
        'id.integer' => 'Voucher ID must be an integer',
        'id.min' => 'Voucher ID must be greater than 0',
    ];

    public function apply()
    {
        $voucherReciept = $this->action('datasource:ViewVoucherReciept', $this->pointer, $this->request);
        // $this->data = $voucherReciept;
        $companyDeatils = $this->action('datasource:company', $this->pointer, $this->request);
        $mydata = $voucherReciept ? $voucherReciept : null;
        $companyData = $companyDeatils ? $companyDeatils[0] : null;

        $mydata['company_details'] = $companyData;
        $mydata['transaction_date'] = date('d M Y', strtotime($mydata['transaction_date']));

        $response = $this->hitCURLForGeneratePDF($mydata, [], 'viewVoucherReceipt');

        if ($response != null) {
            $this->data['status'] = 'success';
            $this->data['message'] = 'Payment Receipt Downloaded Successfully';
            $this->data['data'] = $response['data'];
        }
    }
}
