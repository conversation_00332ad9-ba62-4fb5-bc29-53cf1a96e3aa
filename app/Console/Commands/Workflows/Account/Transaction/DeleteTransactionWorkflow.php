<?php

namespace App\Console\Commands\Workflows\Account\Transaction;

use App\Console\Commands\Workflow;

class DeleteTransactionWorkflow extends Workflow
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'workflow:deleteTransaction {input?}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Delete Transaction Workflow';

    protected $schema = [];
    protected $formatterByKeys = [];
    /**
     * Execute the console command.
     */

    protected $rules= [
    ];

    protected $rulesMessage = [
    ];

    public function apply()
    {
        $deleteTransaction = $this->action('datasource:deleteTransaction', $this->pointer, $this->request);
        //$this->data = $voucherReciept;
    }
}
