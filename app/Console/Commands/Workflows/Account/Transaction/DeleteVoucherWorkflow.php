<?php

namespace App\Console\Commands\Workflows\Account\Transaction;

use App\Console\Commands\Workflow;

class DeleteVoucherWorkflow extends Workflow
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'workflow:deleteVoucher {input?}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'View Voucher Reciept Workflow';

    protected $schema = [];
    protected $formatterByKeys = ['voucher_id'];
    /**
     * Execute the console command.
     */

    protected $rules= [
        'id' => 'required|integer|min:1',
    ];

    protected $rulesMessage = [
        'id.required' => 'Voucher ID is required',
        'id.integer' => 'Voucher ID must be an integer',
        'id.min' => 'Voucher ID must be greater than 0',
    ];

    public function apply()
    {
        $voucherReciept = $this->action('datasource:deleteVoucher', $this->pointer, $this->request);
        $this->data = $voucherReciept;
    }
}
