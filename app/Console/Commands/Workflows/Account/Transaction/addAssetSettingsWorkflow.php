<?php

namespace App\Console\Commands\Workflows\Account\Transaction;

use Illuminate\Console\Command;
use App\Console\Commands\Workflow;

class addAssetSettingsWorkflow extends Workflow
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'workflow:addAssetSettings {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Add Asset Settings Workflow';

    protected $rules = [
        "assetCategoryArray" => "required|array",
        "assetCategoryArray.*.assets_categories_name" => "required|string",
        "assetCategoryArray.*.opening_balance" => "required|numeric|min:0",
        // here assets_categories_type is an trsing and it must be movable or immovable
        "assetCategoryArray.*.assets_categories_type" => "required|string|in:Movable,Immovable",
    ];

    protected $rulesMessage = [
        "assetCategoryArray.required" => "The asset category array is required.",
        "assetCategoryArray.array" => "The asset category must be an array.",
        "assetCategoryArray.*.assets_categories_name.required" => "The name of each asset category is required.",
        "assetCategoryArray.*.assets_categories_name.string" => "The name of each asset category must be a string.",
        "assetCategoryArray.*.opening_balance.required" => "The opening balance for each asset category is required.",
        "assetCategoryArray.*.opening_balance.numeric" => "The opening balance for each asset category must be a number.",
        "assetCategoryArray.*.opening_balance.min" => "The opening balance for each asset category must be at least 0.",
        "assetCategoryArray.*.assets_categories_type.required" => "The type of each asset category is required.",
        "assetCategoryArray.*.assets_categories_type.string" => "The type of each asset category must be a string.",
        "assetCategoryArray.*.assets_categories_type.in" => "The type of each asset category must be either Movable or Immovable.",
    ];

    /**
     * Execute the console command.
     */
    public function apply()
    {
        $addAsset = $this->action('datasource:addAssetSettings', $this->pointer, $this->request);
    }
}
