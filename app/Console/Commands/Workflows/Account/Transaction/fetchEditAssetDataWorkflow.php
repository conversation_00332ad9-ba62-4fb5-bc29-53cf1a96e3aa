<?php

namespace App\Console\Commands\Workflows\Account\Transaction;

use Illuminate\Console\Command;
use App\Console\Commands\Workflow;

class fetchEditAssetDataWorkflow extends Workflow
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'workflow:fetchEditAssetData {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Fetch Edit Asset Data Workflow';

    /**
     * Execute the console command.
     */
    public function apply()
    {
        $this->action('datasource:fetchEditAssetData', $this->pointer, $this->request);
    }
}
