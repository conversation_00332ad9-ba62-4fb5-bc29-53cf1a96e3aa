<?php

namespace App\Console\Commands\Workflows\Account\Vouchers;

use Illuminate\Console\Command;
use App\Console\Commands\Workflow;

class AddMultiplePaymentVoucherWorkflow extends Workflow
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'workflow:addMultiplePaymentVoucher {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Add Multiple Payment Voucher';

    protected $rules = [
        "params.*.from_ledger" => "required|integer",
        "params.*.to_ledger" => "required|integer",
        "params.*.transaction_amount" => "required|numeric|min:1",
        "params.*.transaction_date" => "required|date",
        "params.*.payment_reference" => "required",
        "params.*.memo_desc" => "required"
    ];
    
    
    protected $rulesMessage = [
        "params.*.from_ledger.required" => "From Ledger is required.",
        "params.*.to_ledger.required" => "To Ledger is required.",
        "params.*.transaction_amount.required" => "Transaction Amount is required.",
        "params.*.transaction_amount.numeric" => "Transaction Amount must be a numeric value.",
        "params.*.transaction_amount.min" => "Transaction Amount must be at least 1.",
        "params.*.transaction_date.required" => "Transaction Date is required.",
        "params.*.transaction_date.date" => "Transaction Date must be a valid date.",
        "params.*.payment_reference.required" => "Payment Reference is required.",
        "params.*.memo_desc.required" => "Memo Description is required."
    ];
    
    
    /**
     * Execute the console command.
     */
    public function apply()
    {
        $multiplePaymentVoucher = $this->action('action:addMultiplePaymentVoucher', $this->pointer, $this->request);
        $this->data = $multiplePaymentVoucher;
        $this->data = $this->format((array)$this->data);
    }
}
