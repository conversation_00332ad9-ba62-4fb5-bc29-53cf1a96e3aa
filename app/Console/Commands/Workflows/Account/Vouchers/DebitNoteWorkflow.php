<?php

namespace App\Console\Commands\Workflows\Account\Vouchers;

use Illuminate\Console\Command;
use App\Console\Commands\Workflow;

class DebitNoteWorkflow extends Workflow
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'workflow:addDebitNote {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Add Debit Note';

    protected $rules = [
        "salestype" => 'required',
        "from_ledger" => "required",
        "to_ledger" => "required",
        "transaction_amount" => "required|numeric|min:1",
        "transaction_date" => "required|date",
        "payment_reference" => "required",
        "memo_desc" => "required"
    ];
    protected $rulesMessage = [
        "salestype.required" => "Sales Type is required",
        "from_ledger.required" => "From Ledger is required",
        "to_ledger.required" => "To Ledger is required",
        "transaction_amount.required" => "Transaction Amount is required",
        "transaction_date.required" => "Transaction Date is required",
        "transaction_date.date" => "Transaction Date must be a date",
        "payment_reference.required" => "Payment Reference is required",
        "memo_desc.required" => "Memo Description is required",
        "transaction_amount.numeric" => "Transaction Amount must be a number",
        "transaction_amount.min" => "Transaction Amount must be greater than 0"
    ];
    /**
     * Execute the console command.
     */
    public function apply()
    {
        $addDebitNote = $this->action('action:addDebitNote', $this->pointer, $this->request);
        $this->data = $addDebitNote;
        $this->data = $this->format((array)$this->data);
    }
}
