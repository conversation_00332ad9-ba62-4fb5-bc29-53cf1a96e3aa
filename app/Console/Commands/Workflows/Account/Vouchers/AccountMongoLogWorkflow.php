<?php

namespace App\Console\Commands\Workflows\Account\Vouchers;
use App\Console\Commands\Workflow;
class AccountMongoLogWorkflow extends Workflow
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'workflow:accountMongoLog {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Account Mongo Log Workflow';

    /**
     * Execute the console command.
     */
    protected $rules = [
        "company_id" => "required",
        "module"=>  "required",
    ];

    protected $rulesMessage = [
        "company_id.required" => "Company ID is required",
        "module.required" => "Module is required",
    ];
    public function apply()
    {
            $this->data= $this->action('datasource:accountMongoLog', $this->pointer, $this->request);
    }
}
