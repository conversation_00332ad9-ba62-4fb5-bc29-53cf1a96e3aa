<?php

namespace App\Console\Commands\Workflows\Account\Vouchers;

use Illuminate\Console\Command;
use App\Console\Commands\Workflow;

class AddContraEntryWorkflow extends Workflow
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'workflow:addContraEntry {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Add Contra Entry';

    protected $rules = [
        "from_ledger" => "required",
        "to_ledger" => "required",
        "transaction_amount" => "required|numeric|min:1",
        "transaction_date" => "required|date",
        "payment_reference" => "required",
        "memo_desc" => "required"
    ];
    protected $rulesMessage = [
        "from_ledger.required" => "From Ledger is required",
        "to_ledger.required" => "To Ledger is required",
        "transaction_amount.required" => "Transaction Amount is required",
        "transaction_date.required" => "Transaction Date is required",
        "transaction_date.date" => "Transaction Date must be a date",
        "payment_reference.required" => "Payment Reference is required",
        "memo_desc.required" => "Memo Description is required",
        "transaction_amount.numeric" => "Transaction Amount must be a number",
        "transaction_amount.min" => "Transaction Amount must be greater than 0"
    ];
    /**
     * Execute the console command.
     */
    public function apply()
    {
        $paymentVoucher = $this->action('action:addContraEntry', $this->pointer, $this->request);
        $this->data = $paymentVoucher;
        $this->data = $this->format((array)$this->data);
    }
}
