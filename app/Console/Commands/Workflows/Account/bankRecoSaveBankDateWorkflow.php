<?php

namespace App\Console\Commands\Workflows\Account;

use Illuminate\Console\Command;
use App\Console\Commands\Workflow;

class bankRecoSaveBankDateWorkflow extends Workflow
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'workflow:bankRecoSaveBankDate {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Bank Reconciliation Save Bank Date Workflow';

    /**
     * Execute the console command.
     */
    public function apply()
    {
        $soc_id = $this->input['company_id'];
        $txn_id = $this->input['txn_id'] ?? null;
        $bank_date = $this->input['bank_date'] ?? null;

        // Validate that if any one of the inputs is provided, all three are required
        // if ($txn_id || $bank_date) {
        //     if (empty($txn_id) || empty($bank_date)) {
        //         $this->message = "All fields (txn_id, bank_date) are required.";
        //         $this->status = "error";
        //         $this->statusCode = 400;
        //         return;
        //     }
        // }

        // validate txn_id should be a number
        if ($txn_id && !is_numeric($txn_id)) {
            $this->message = "txn_id should be a number.";
            $this->status = "error";
            $this->statusCode = 400;
            return;
        }

        // validate bank_date format is Y-m-d
        if ($bank_date) {
            $date = \DateTime::createFromFormat('Y-m-d', $bank_date);
            if (!$date || $date->format('Y-m-d') !== $bank_date) {
                $this->message = "Invalid bank_date format. Expected format is Y-m-d.";
                $this->status = "error";
                $this->statusCode = 400;
                return;
            }
        }

        // Check txn_id from chsone_ledger_transactions table
        $transactionDetails = $this->tenantDB()->table('chsone_ledger_transactions')
            ->where('soc_id', $soc_id)
            ->where('txn_id', $txn_id)
            ->first();

        if (!$transactionDetails) {
            $this->message = "No transaction found with the given txn_id or provide a valid txn_id.";
            $this->status = "error";
            $this->statusCode = 400;
            return;
        }

        $bankRecoSaveBankDate = $this->action('datasource:bankRecoSaveBankDate', $this->pointer, $this->request);
        $this->data = $bankRecoSaveBankDate;
    }
}
