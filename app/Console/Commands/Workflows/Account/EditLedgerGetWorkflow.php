<?php

namespace App\Console\Commands\Workflows\Account;

use Illuminate\Console\Command;
use App\Console\Commands\Workflow;

class EditLedgerGetWorkflow extends Workflow
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'workflow:EditLedgerGet {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Edit Ledger Information';

    protected $formatterByKeys = ['id'];

    protected $rules = [
        'id' => 'required|integer|min:1',
    ];

    protected $messages = [
        'id.required' => 'Ledger ID is required',
        'id.integer' => 'Ledger ID must be an integer',
        'id.min' => 'Ledger ID must be greater than 0',
    ];

    /**
     * Execute the console command.
     */
    public function apply()
    {
        $editLdger = $this->action('datasource:EditLedgerGet', $this->pointer, $this->request);
        $this->data = $editLdger;

    }

}
