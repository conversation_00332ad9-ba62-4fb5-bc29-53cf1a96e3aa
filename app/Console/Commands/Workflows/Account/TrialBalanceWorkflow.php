<?php

namespace App\Console\Commands\Workflows\Account;

use Illuminate\Console\Command;
use App\Console\Commands\workflow;

class TrialBalanceWorkflow extends workflow
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'workflow:trialBalance {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */

    protected $schema = [
        "table" => [
            "tableTitle" => "Trial Balance",
            "fields" => [
                "*"
            ],
            "tableDirection" => "row",
            "extraFilters" => [
                "acc_date" => [
                    "title" => "Accounting Date",
                    "type" => "account_date"
                ]
            ],
            "actions" => [
                [
                    "title" => "Print/Export",
                    "icon" => "ri-printer-line",
                    "options" => [
                        [
                            "title" => "Excel",
                            "icon" => "ri-file-excel-2-line",
                            "api" => [
                                "url" => "/admin/accountsreporting/trial_balance/download/excel",
                                "method" => "GET",
                                "type" => "download"
                            ]
                        ],
                        
                        [
                            "title" => "PDF",
                            "icon" => "ri-file-excel-2-line",
                            "api" => [
                                "url" => "/admin/accountsreporting/trial_balance/download/pdf",
                                "method" => "GET",
                                "type" => "download"
                            ]
                        ],
                    ]
                ]
            ],

            "columns" => [
                [
                    [
                        "title" => "Particulars",
                        "key" => "ledger_account_name",
                        "footer" => "SUM"
                    ],
                    [
                        "title" => "Opening(Debit)",
                        "key" => "opening_balance",
                        "type" => "number",
                        "aggregation" => true
                    ],
                    [
                        "title" => "Closing(Debit)",
                        "key" => "transaction_amount",
                        "type" => "number",
                        "aggregation" => true
                    ]
                ],
                [
                    [
                        "title" => "Particulars",
                        "key" => "ledger_account_name",
                        "footer" => "SUM"
                    ],
                    [
                        "title" => "Opening(Credit)",
                        "key" => "opening_balance",
                        "type" => "number",
                        "aggregation" => true
                    ],
                    [
                        "title" => "Closing(Credit)",
                        "key" => "transaction_amount",
                        "type" => "number",
                        "aggregation" => true
                    ]
                ]
            ]
        ]
    ];
    public function apply()
    {
        $data = $this->action('service:ledgersTransaction', $this->pointer, $this->request);
        $this->data = $data;
        $this->meta['schema'] = $this->schema;

    }
}
