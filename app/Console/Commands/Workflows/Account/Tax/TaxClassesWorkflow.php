<?php

namespace App\Console\Commands\Workflows\Account\Tax;

use Illuminate\Console\Command;
use App\Console\Commands\Workflow;

class TaxClassesWorkflow extends Workflow
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'workflow:taxClasses {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Get Tax List';

    protected $formatter = [
        "id" => "",
        "tax_class_name" => "",
        "tax_class_description" => "",
        "tax_categories_footer" => "",
        "tax_categories_amount" => "",
        "tax_categories_type" => "",
    ];

    protected $formatterByKeys = ['id'];

    protected $hugeData = true;

    /**
     * Execute the console command.
     */
    public function apply()
    {
        $taxClasses = $this->action('datasource:taxClasses', $this->pointer, $this->request);
        $this->data = $taxClasses;
        $this->data = $this->format((array)$this->data);
        $this->meta['schema'] = $this->schema();
    }

    public function schema()
    {
        return $meta = [
            "table" => [
                "tableTitle" => "Tax Classes",
                "actions" => [
                    [
                        "title" => "New Tax",
                        "icon" => "ri-add-line",
                        "color" => "primary",
                        "variant" => "contained",
                        "redirect" => "/admin/tax/addTax"
                    ]
                ],
                "fields" => [
                    "*"
                ],
                "columns" => [
                    [
                        "title" => "Sr No.",
                        "key" => "id"
                    ],
                    [
                        "title" => "Tax Class",
                        "key" => "tax_class_name",
                    ],
                    [
                        "title" => "Description",
                        "key" => "tax_class_description"
                    ],
                    [
                        "title" => "Footer Note",
                        "key" => "tax_categories_footer"
                    ],
                    [
                        "title" => "Actions",
                        "type" => "actions",
                        "key" => "actions",
                        "actions" => [
                            [
                                "title" => "View",
                                "icon" => "ri-eye-line",
                                "href" => "/admin/tax/viewTaxRule/:id",
                            ],
                            [
                                "title" => "Edit",
                                "icon" => "ri-edit-box-line",
                                "href" => "/admin/tax/editTax/:id",
                            ]
                        ]
                    ]
                ]
            ]
        ];
    }
}
