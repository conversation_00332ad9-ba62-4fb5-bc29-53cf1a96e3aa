<?php

namespace App\Console\Commands\Workflows\Account\Tax;

use Illuminate\Console\Command;
use App\Console\Commands\Workflow;

class AddtaxexemptionWorkflow extends Workflow
{
    protected $signature = 'workflow:addtaxexemption {input}';

    protected $description = 'Add Tax Exemption Workflow';

    protected $rules = [
        'lower_limit' => 'required|numeric|min:1',
        'upper_limit' => 'required|numeric|min:1|gt:lower_limit',
        'rate' => 'required|numeric|min:1',
        'type' => 'required|in:fixed,percentage',
        'effective_date' => 'required|date',
        'tax_class' => 'required',
        'tax_exemption_description' => 'required'
    ];

    protected $rulesMessage = [
        'lower_limit.required' => 'Lower Limit is required',
        'upper_limit.required' => 'Upper Limit is required',
        'upper_limit.gt' => 'Upper Limit must be greater than Lower Limit',
        'rate.required' => 'Rate is required',
        'type.required' => 'Type is required',
        'effective_date.required' => 'Effective Date is required',
        'tax_class.required' => 'Tax Class is required',
        'lower_bound.numeric' => 'Lower Bound must be a numeric value',
        'upper_bound.numeric' => 'Upper Bound must be a numeric value',
        'rate.numeric' => 'Rate must be a numeric value',
        'lower_bound.min' => 'Lower Bound must be greater than 0',
        'upper_bound.min' => 'Upper Bound must be greater than 0',
        'rate.min' => 'Rate must be greater than 0',
        'type.in' => 'Type must be either fixed or percentage',
        'effective_date.date' => 'Effective Date must be a valid date',
        'tax_exemption_description.required' => 'Tax Exemption Description is required'
    ];


    public function apply()
    {
        $this->action('datasource:addTaxExemption', $this->pointer, $this->request);
    }

}