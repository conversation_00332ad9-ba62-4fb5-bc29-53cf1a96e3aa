<?php

namespace App\Console\Commands\Workflows\Account\Tax;

use Illuminate\Console\Command;
use App\Console\Commands\Workflow;

class ViewTaxRuleWorkflow extends Workflow
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'workflow:viewTaxRule {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'View Tax Rule by Id';

    protected $formatter = [
        "id" => "",
        "tax_class_id" => "",
        "tax_categories_name" => "",
        "tax_categories_amount" => "",
        "tax_categories_type" => "",
        "rate" => "",
        "from_date" => "",
        "to_date" => "",
        "tax_code" => "",
        "status" => ""
    ];

    protected $formatterKey = ['id'];

    /**
     * Execute the console command.
     */
    public function apply()
    {
        $infoAPI = $this->input['info'] ?? '';
        if ($infoAPI) {
            $this->formatter = [
                "id" => "",
                "tax_class_id" => "",
                "tax_class_name" => "",
                "tax_class_description" => "",
                "tax_categories_footer" => ""
            ];
            $viewTaxRuleInfo = $this->action('datasource:viewTaxRuleInfo', $this->pointer, $this->request);
            $this->data = $viewTaxRuleInfo;
        } else {
            $this->formatter = [
                "id" => "",
                "tax_class_id" => "",
                "tax_categories_name" => "",
                "tax_categories_amount" => "",
                "tax_categories_type" => "",
                "rate" => "",
                "from_date" => "",
                "to_date" => "",
                "tax_code" => "",
                "status" => ""
            ];
            $viewTaxRule = $this->action('datasource:viewTaxRule', $this->pointer, $this->request);
            $this->data = $viewTaxRule;
        }
    }
}
