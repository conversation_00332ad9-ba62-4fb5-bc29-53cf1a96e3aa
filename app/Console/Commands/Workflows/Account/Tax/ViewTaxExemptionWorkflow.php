<?php

namespace App\Console\Commands\Workflows\Account\Tax;

use Illuminate\Console\Command;
use App\Console\Commands\Workflow;

class ViewTaxExemptionWorkflow extends Workflow
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'workflow:viewTaxExemption {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'View Tax Rule by Id';

    protected $formatter = [
        "id" => "",
        "lower_bound" => "",
        "upper_bound" => "",
        "gender" => "",
        "description" => "",
        "effective_date" => "",
        "tax_code" => "",
        "rate_limit" => ""
    ];

    protected $formatterByKeys = ['id'];

    /**
     * Execute the console command.
     */
    public function apply()
    {
        $viewTaxExemption = $this->action('datasource:viewTaxExemption', $this->pointer, $this->request);
        $this->data = $viewTaxExemption;
        $this->data = $this->format((array)$this->data);
        $this->meta['schema'] = $this->schema();
    }

    public function schema()
    {
        return $meta = [
            "table" => [
                "tableTitle" => "Tax Exemption",
                "actions" => [
                    [
                        "title" => "New Tax Exemption",
                        "icon" => "ri-add-line",
                        "color" => "primary",
                        "redirect" => "/admin/taxexemption/addtaxexemption"
                    ]
                ],
                "fields" => [
                    "*"
                ],
                "columns" => [
                    [
                        "title" => "Name",
                        "key" => "tax_code",
                    ],
                    [
                        "title" => "Lower Limit",
                        "key" => "lower_bound"
                    ],
                    [
                        "title" => "Upper Limit",
                        "key" => "upper_bound"
                    ],
                    [
                        "title" => "Rate",
                        "key" => "rate_limit"
                    ],
                    [
                        "title" => "Gender",
                        "key" => "gender"
                    ],
                    [
                        "title" => "Effective Date",
                        "key" => "effective_date"
                    ]
                ]
            ]
        ];
    }
}
