<?php

namespace App\Console\Commands\Workflows\Account\Tax;

use Illuminate\Console\Command;
use App\Console\Commands\Workflow;

class GetTaxDetailsWorkflow extends Workflow
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'workflow:GetTaxDetails {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Get Tax Class details with associated Tax Categories';

    protected $formatter = [
        "tax_class_id" => "",
        "tax_class_name" => "",
        "tax_class_description" => "",
        "tax_categories_footer" => "",
        "tax_categories" => []
    ];

    protected $formatterByKeys = ['tax_class_id'];

    /**
     * Execute the console command.
     */
    public function apply()
    {

        try {
            $taxDetails = $this->action('datasource:GetTaxDetails', $this->pointer, $this->request);
            $this->data = $taxDetails;
        } catch (\Exception $e) {
            $this->data = [
                'error' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine()
            ];
        }
    }
}
