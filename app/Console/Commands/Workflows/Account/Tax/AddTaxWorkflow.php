<?php

namespace App\Console\Commands\Workflows\Account\Tax;

use Illuminate\Console\Command;
use App\Console\Commands\Workflow;

class AddTaxWorkflow extends Workflow
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'workflow:AddTax {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'save Tax Data';

    protected $rules = [
        "tax_class_id"                                => ["nullable"],
        "tax_class_name"                              => ["required"],
        "tax_class_description"                  => ["required"],
        "tax_categories_footer"                       => ["required"],
        "taxclassruledetails"                         => ["required","array","min:1"],
    
        "taxclassruledetails.*.tax_categories_name"   => ["required"],
        "taxclassruledetails.*.tax_categories_id"     => ["nullable"],
        "taxclassruledetails.*.tax_categories_amount" => ["required","numeric","min:1"],
        "taxclassruledetails.*.tax_categories_type"   => ["required","in:percentage,fixed"],
    
        // <-- new array rule for your dual‐date field: must be an array of 2
        "taxclassruledetails.*.tax_categories_from_date"       => ["required","array","size:2"],
    
        // each element in that array must be a Y-m-d date
        "taxclassruledetails.*.tax_categories_from_date.*"     => ["required","date_format:Y-m-d"],
    
        // and element 1 (the “to” date) must be >= element 0 (the “from” date)
        "taxclassruledetails.*.tax_categories_from_date.1"     => [
            "required",
            "date_format:Y-m-d",
            "after_or_equal:taxclassruledetails.*.tax_categories_from_date.0"
        ],
    
        "taxclassruledetails.*.tax_categories_code"           => ["required"],
    ];

    protected $rulesMessage = [
        "tax_class_name.required" => "The tax class name is required.",
        "tax_class_description.required" => "The tax class description is required.",
        "tax_categories_footer.required" => "The tax categories footer is required.",
        "tax_categories_type.required" => "The tax categories type is required.",
        "taxclassruledetails.required" => "At least one tax class rule detail is required.",
        "taxclassruledetails.array" => "The tax class rule details must be an array.",
        "taxclassruledetails.*.tax_categories_name.required" => "The tax category name is required.",
        "taxclassruledetails.*.tax_categories_amount.required" => "The tax category amount is required.",
        "taxclassruledetails.*.tax_categories_amount.numeric" => "The tax category amount must be a numeric value.",
        "taxclassruledetails.*.tax_categories_amount.min" => "The tax category amount must be at least 1.",
        "taxclassruledetails.*.tax_categories_type.required" => "The tax category type is required.",
        "taxclassruledetails.*.tax_categories_type.in" => "The tax categories type must be either 'percentage' or 'fixed'.",
        
        // Updated date‐range messages
        "taxclassruledetails.*.tax_categories_from_date.required" => "The date range is required.",
        "taxclassruledetails.*.tax_categories_from_date.array"    => "The date range must be an array of two dates.",
        "taxclassruledetails.*.tax_categories_from_date.size"     => "The date range must contain exactly two dates.",
        "taxclassruledetails.*.tax_categories_from_date.*.required"    => "Both start and end dates are required.",
        "taxclassruledetails.*.tax_categories_from_date.*.date_format" => "Each date must be in the format YYYY-MM-DD.",
        "taxclassruledetails.*.tax_categories_from_date.1.after_or_equal" => "The end date must be on or after the start date.",
        
        "taxclassruledetails.*.tax_categories_code.required" => "The tax category code is required.",
    ];



    protected $formatter = [];

    protected $formatterByKeys = ['id'];

    /**
     * Execute the console command.
     */
    public function apply()
    {
        $taxClasses = $this->action('datasource:AddTax', $this->pointer, $this->request);
        $this->data = $taxClasses;
        $this->data = $this->format((array)$this->data);
    }
}
