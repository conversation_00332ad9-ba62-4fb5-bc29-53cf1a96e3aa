<?php

namespace App\Console\Commands\Workflows\Account;

use Illuminate\Console\Command;
use App\Console\Commands\Workflow;

class bankRecoSaveReferenceNumberWorkflow extends Workflow
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'workflow:bankRecoSaveReferenceNumber {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Bank Reconciliation Save Reference Number Workflow';

    /**
     * Execute the console command.
     */
    public function apply()
    {
        $soc_id = $this->input['company_id'];
        $txn_id = $this->input['txn_id'] ?? null;
        $ref_no = $this->input['ref_no'] ?? null;

        // Validate that if any one of the inputs is provided, all three are required
        // if ($txn_id || $ref_no) {
        //     if (empty($txn_id) || empty($ref_no)) {
        //         $this->message = "All fields (txn_id, ref_no) are required.";
        //         $this->status = "error";
        //         $this->statusCode = 400;
        //         return;
        //     }
        // }

        // validate txn_id should be a number
        if ($txn_id && !is_numeric($txn_id)) {
            $this->message = "txn_id should be a number.";
            $this->status = "error";
            $this->statusCode = 400;
            return;
        }

        // Check txn_id from chsone_ledger_transactions table
        $transactionDetails = $this->tenantDB()->table('chsone_ledger_transactions')
            ->where('soc_id', $soc_id)
            ->where('txn_id', $txn_id)
            ->first();

        if (!$transactionDetails) {
            $this->message = "No transaction found with the given txn_id or provide a valid txn_id.";
            $this->status = "error";
            $this->statusCode = 400;
            return;
        }

        $bankRecoSaveRefNo = $this->action('datasource:bankRecoSaveReferenceNumber', $this->pointer, $this->request);
        $this->data = $bankRecoSaveRefNo;
    }
}
