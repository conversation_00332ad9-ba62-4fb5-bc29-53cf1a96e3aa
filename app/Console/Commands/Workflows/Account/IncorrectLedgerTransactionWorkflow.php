<?php

namespace App\Console\Commands\Workflows\Account;

use Illuminate\Console\Command;
use App\Console\Commands\workflow;

class IncorrectLedgerTransactionWorkflow extends Workflow
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'workflow:incorrectledgertransaction {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Workflow for Incorrect Ledger Transaction';

    protected $schema = [
        'table' => [
            'tableTitle' => 'Incrorect Ledger Entries',
            'fields' => [
                '*'
            ],
            'columns' => [
                [
                    'title' => 'Txn Id',
                    'key' => 'id'
                ],
                [
                    'title' => 'Transaction Type',
                    'key' => 'transaction_type'
                ],
                [
                    'title' => 'Transaction Date',
                    'key' => 'transaction_date'
                ],
                [
                    'title' => 'Ledger',
                    'key' => 'ledger_account_name'
                ],
                [
                    'title' => 'Voucher Type',
                    'key' => 'voucher_type'
                ],
                [
                    'title' => 'Reference',
                    'key' => 'voucher_reference_number'
                ],
                [
                    'title' => 'Amount ₹',
                    'key' => 'transaction_amount'
                ],
                [
                    'title' => 'Narration',
                    'key' => 'narration'
                ],
                [
                    'title' => 'Reconciled',
                    'key' => 'reconciliation_status'
                ],
                [
                    'title' => 'Cancelled',
                    'key' => 'cancellation_status'
                ],
            ]
        ]
    ];

    protected $rules = [];

    protected $rulesMessage = [];

    protected $formatter = [
        'id' => '',
        'transaction_type' => '',
        'transaction_date' => '',
        'ledger_account_name' => '',
        'voucher_type' => '',
        'voucher_reference_number' => '',
        'transaction_amount' => '',
        'narration' => '',
        'is_reconciled' => '',
        'is_cancelled' => ''
    ];

    // protected $formatterByKeys = ['txn_id'];

    /**
     * Execute the console command.
     */
    public function apply()
    {
        $this->action('datasource:IncorrectLedgerTransactionDataSource', $this->pointer, $this->request);
        $this->meta['schema'] = $this->schema;
    }
}
