<?php

namespace App\Console\Commands\Workflows\Account;

use App\Console\Commands\Workflow;
use Illuminate\Console\Command;

class investmentsListWorkflow extends Workflow
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'workflow:investmentsList {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Get Investments List Workflow';

    /**
     * Get table schema.
     */
    protected $schema = [
        "table" => [
            "tableTitle" => "Investments",
            "actions" => [
                [
                    "title" => "New Investments",
                    "icon" => "ri-add-circle-line",
                    "color" => "primary",
                    "redirect" => "/admin/assets/addInvestment",
                ]
            ],
            "fields" => [
                "*"
            ],
            "columns" => [
                [
                    "title" => "Account Number",
                    "key" => "account_number"
                ],
                [
                    "title" => "Account Type",
                    "key" => "type",
                    "options" => [
                        "rd" => [
                            "title" => "Recurring"
                        ],
                        "fd" => [
                            "title" => "Fixed"
                        ]
                    ]
                ],
                [
                    "title" => "Bank",
                    "key" => "bank_name"
                ],
                [
                    "title" => "Branch",
                    "key" => "branch"
                ],
                [
                    "title" => "IFSC Code",
                    "key" => "bank_ifsc"
                ],
                [
                    "title" => "Start Date",
                    "key" => "start_date"
                ],
                [
                    "title" => "Maturity Date",
                    "key" => "maturity_date"
                ],
                [
                    "title" => "Status",
                    "key" => "status",
                    "type" => "chip",
                    "options" => [
                        "1" => [
                            "title" => "Active",
                            "color" => "success"
                        ],
                        "0" => [
                            "title" => "Closed",
                            "color" => "error"
                        ]
                    ]
                ],
                [
                    "title" => "Actions",
                    "type" => "actions",
                    "key" => "actions",
                    "actions" => [
                        [
                            "title" => "View Transactions",
                            "icon" => "ri-eye-line",
                            "href" => "/admin/transaction/listTransactionMonthly/:ledger_account_id",
                        ],
                        [
                            "title" => "Add Interest",
                            "icon" => "ri-percent-line",
                            "form" => "addInterest?ledger_account_id=:ledger_account_id",
                            // "href" => "/admin/transaction/topUpEntryPopup/i/:ledger_account_id/:start_date",
                        ],
                        [
                            "title" => "Topup",
                            "icon" => "ri-arrow-up-circle-line",
                            "form" => "addTopUp?ledger_account_id=:ledger_account_id",
                            // "href" => "/admin/transaction/topUpEntryPopup/i/:ledger_account_id/:start_date",
                            "disable_on" => [
                                "type" => [
                                    "fd"
                                ]
                            ]
                        ]
                    ]
                ]
            ]
        ]
    ];

    /**
     * Execute the console command.
     */
    public function apply()
    {
        $investmentsList = $this->action('datasource:investmentsList', $this->pointer, $this->request);
        $this->data = $investmentsList;
        $this->meta['schema'] = $this->schema;
    }
}
