<?php

namespace App\Console\Commands\Workflows\Account;

use Illuminate\Console\Command;
use App\Console\Commands\Workflow;
use Illuminate\Support\Facades\DB;

class BankReconciliationFormTableWorkflow extends Workflow
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'workflow:bankReconciliationFormTable {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Bank Reconcilation Form Table list';

    protected $formatterByKeys = ['id'];

    /**
     * Execute the console command.
     */
    public function tenantDB()
    {
        return DB::connection('tenant');
    }

    protected $schema = [
        "table" => [
            "tableTitle" => "Bank Reconciliation Form",
            "fields" => [
                "*"
            ],
            "columns" => [
                [
                    "title" => "Bank Account",
                    "key" => "bank_account",
                ],
                [
                    "title" => "Apr",
                    "key" => "Apr",
                    "type" => "checkbox",
                ],
                [
                    "title" => "May",
                    "key" => "May",
                    "type" => "checkbox",
                ],
                [
                    "title" => "Jun",
                    "key" => "Jun",
                    "type" => "checkbox",
                ],
                [
                    "title" => "Jul",
                    "key" => "Jul",
                    "type" => "checkbox",
                ],
                [
                    "title" => "Aug",
                    "key" => "Aug",
                    "type" => "checkbox",
                ],
                [
                    "title" => "Sep",
                    "key" => "Sep",
                    "type" => "checkbox",
                ],
                [
                    "title" => "Oct",
                    "key" => "Oct",
                    "type" => "checkbox",
                ],
                [
                    "title" => "Nov",
                    "key" => "Nov",
                    "type" => "checkbox",
                ],
                [
                    "title" => "Dec",
                    "key" => "Dec",
                    "type" => "checkbox",
                ],
                [
                    "title" => "Jan",
                    "key" => "Jan",
                    "type" => "checkbox",
                ],
                [
                    "title" => "Feb",
                    "key" => "Feb",
                    "type" => "checkbox",
                ],
                [
                    "title" => "Mar",
                    "key" => "Mar",
                    "type" => "checkbox",
                ],
            ]
        ]
    ];

    public function apply()
    {
        $soc_id = $this->input['company_id'];
        $yearSelected = $this->input['yearSelected'] ?? null;

        // Validate that if any one of the inputs is provided, all three are required
        if ($yearSelected) {
            if (empty($yearSelected)) {
                $this->message = "Field yearSelected is required";
                $this->status = "error";
                $this->statusCode = 400;
                return;
            }
        }

        // Validation for yearSelected
        if ($yearSelected) {
            $yearData = explode('-', $yearSelected);

            // Validate if the input has exactly two parts after splitting
            if (count($yearData) !== 2) {
                $this->message = "Invalid Year Format";
                $this->status = "error";
                $this->statusCode = 400;
                return;
            }

            $start_year = intval($yearData[0]);
            $end_year = intval($yearData[1]);
            $currentYear = intval(date('Y'));

            // Validate that start_year and end_year are four-digit numbers
            if (strlen($yearData[0]) !== 4 || strlen($yearData[1]) !== 4) {
                $this->message = "Year should be in YYYY format";
                $this->status = "error";
                $this->statusCode = 400;
                return;
            }

            // Check if start_year is exactly one year less than end_year
            if ($end_year !== $start_year + 1) {
                $this->message = "End year must be exactly one year after the start year";
                $this->status = "error";
                $this->statusCode = 400;
                return;
            }

            // Check if start_year is not greater than the current year
            if ($start_year > $currentYear) {
                $this->message = "Start year cannot be greater than the current year";
                $this->status = "error";
                $this->statusCode = 400;
                return;
            }

            // Fetch the fy_start_date from the database
            $financialYearRecord = $this->tenantDB()->table('soc_account_financial_year_master')
                ->where('soc_id', $soc_id)
                ->orderBy('fy_start_date', 'asc')
                ->first();

            if (!$financialYearRecord) {
                $this->message = "Financial year master data not found";
                $this->status = "error";
                $this->statusCode = 400;
                return;
            }

            $fy_start_date = $financialYearRecord->fy_start_date;
            $fy_start_year = intval(date('Y', strtotime($fy_start_date)));

            if ($start_year < $fy_start_year) {
                $this->message = "Start year cannot be less than the financial year start date ($fy_start_year)";
                $this->status = "error";
                $this->statusCode = 400;
                return;
            }
        }

        // If all validations pass, proceed to fetch the data
        $bankRecoForm = $this->action('datasource:bankReconciliationFormTable', $this->pointer, $this->request);
        $this->data = $bankRecoForm;
        $this->meta['schema'] = $this->schema;
    }
}
