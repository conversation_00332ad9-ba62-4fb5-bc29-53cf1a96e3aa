<?php

namespace App\Console\Commands\Workflows\Account;

use App\Console\Commands\workflow;
use Illuminate\Console\Command;
use App\Http\Traits\CommonFunctionTraits;

class DownloadTrialBalanceWorkflow extends workflow
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    use CommonFunctionTraits;

    protected $signature = 'workflow:downloadTrialBalance {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */

    protected $schema = [
        "table" => [
            "fields" => [
                "*",
            ],
            "columns" => [
                [
                    "title" => "Particulars",
                    "key" => "ledger_account_name",
                ],
                [
                    "title" => "Opening(Debit)",
                    "key" => "opening_balance",
                ],
                [
                    "title" => "Closing(Debit)",
                    "key" => "transaction_amount",
                ],
            ],
        ],
    ];

    protected $headings = [
        'particulars',
        'opening_balance',
        'transaction_amount',
        '',
        'particulars',
        'opening_balance',
        'transaction_amount',
    ];
    protected $rules=[
        'method'=>'required|string|in:excel,pdf'
    ];

    protected $rulesMessage=[
        "method.required"=>"Please provide a valid type: either excel or pdf",
        'method.in'=>'Please provide a valid type: either excel or pdf'
    ];
    public function apply()
    {
       
        $year = $this->input['lstYear'] ?? null;
        $method = $this->input[ 'method' ];


         // Validate the input type
         if ( !$method || $method == '' || $method == ':type' ) {
            $this->status = 'error';
            $this->message = 'Please provide a valid type: either excel or pdf';
            $this->statusCode = 400;
            $this->data = [];
            return;
        }

        $financial_year = $this->input['lstYear'] ?? date('Y').'-'.(date('Y')+1);
        $years = explode('-',$financial_year);
        $type = $this->input['type'] ?? 'yearly';
        $month = $this->input['lstMonth'] ?? 3;
        $asondateval = $this->input['asondateval'] ?? date('Y-m-d');
        $soc_id = $this->input['company_id'];
        $inputValidation = $this->validateInput(array(
            "financial_year"=>$financial_year,
            "type"=>$type,
            "month"=>$month,
            "asondateval"=>$asondateval,
            "soc_id"=>$soc_id
        ));

        if ($inputValidation["status"] == "error") {
            $this->message = $inputValidation["message"];
            $this->status = $inputValidation["status"];
            $this->statusCode = 400;
            return;
        }

        // Fetch data from the action
        $data = $this->action('service:ledgersTransaction', $this->pointer, $this->request);

        // Validate fetched data
        if (empty($data)) {
            $this->setErrorResponse('No data received from the service.', 404);
            return;
        }

        // Flatten the data
        $data1 = $this->flattenNestedArray($data[0] ?? []);
        $data2 = $this->flattenNestedArray($data[1] ?? []);

        // Define headings
        $headings1 = ['particulars', 'opening_balance', 'transaction_amount'];
        $headings2 = ['particulars', 'opening_balance', 'transaction_amount'];
        $headings3 = ['debit_particular', 'opening_debit', 'closing_debit'];
        $headings4 = ['credit_particular', 'opening_credit1', 'closing_credit1'];

        // Merge tables for Excel and PDF generation
        $mergedDataForExcel = $this->mergeTablesWithBlankColumn($headings1, $data1, $headings2, $data2);
        $mergedDataForPDF = $this->mergeTablesWithBlankColumn($headings3, $data1, $headings4, $data2);
        $newData = [];
        foreach ($mergedDataForPDF['data'] as $row) {
            $newData[] = [
                'debit_particular' => $this->formatWriteOffAmount($row[0] ?? '-'),  // First column
                'opening_debit' => $this->formatWriteOffAmount($row[1] ?? 0),      // Second column
                'closing_debit' => $this->formatWriteOffAmount($row[2] ?? 0),      // Third column
                'credit_particular' => $this->formatWriteOffAmount($row[4] ?? '-'), // Fifth column
                'opening_credit' => $this->formatWriteOffAmount($row[5] ?? 0),     // Sixth column
                'closing_credit' => $this->formatWriteOffAmount($row[6] ?? 0)      // Seventh column
            ];
        }

        $companyDeatils = $this->action('datasource:company', $this->pointer, $this->request);

        $companyDeatils[0]['year'] = 'Trial Balance for '.$year;

        // Debug output for validation
        // Log $mergedDataForExcel and $mergedDataForPDF to ensure correctness
        if (empty($mergedDataForExcel['data']) || empty($mergedDataForPDF['data'])) {
            $this->setErrorResponse('Merged data is empty. Please check the source data.', 500);
            return;
        }

        // Set schema and merged data for the response
        $this->meta['schema'] = $this->schema;
        $this->data = $mergedDataForExcel['data'];

        // Generate output based on type
        try {
            if ($method === 'excel') {
                $result = $this->hitCURLForGenerateCSV(
                    $mergedDataForExcel['data'],
                    $mergedDataForExcel['headings'],
                    'Trial_Balance_'
                );
            } else {
                $result = $this->hitCURLForGeneratePDF(
                    $newData,
                    $mergedDataForPDF['headings'],
                    'trialBalance',
                    [],
                    [],
                    $companyDeatils
                );
            }
            // Set the result URL
            $this->data['url'] = $result['data'] ?? '';
        } catch (\Exception $e) {
            $this->setErrorResponse('Error generating the file: ' . $e->getMessage(), 500);
        }
    }


    private function flattenNestedArray(array $nestedArray, &$flatArray = [])
    {
        foreach ($nestedArray as $item) {
            // Add valid rows to the flat array
            if (isset($item['ledger_account_name'])) {
                $flatArray[] = [
                    $item['ledger_account_name'] ?? '',
                    $this->formatWriteOffAmount($item['opening_balance']) ?? '0',
                    $this->formatWriteOffAmount($item['transaction_amount']) ?? '0',
                ];
            }

            // Process nested rows recursively
            if (!empty($item['rows']) && is_array($item['rows'])) {
                $this->flattenNestedArray($item['rows'], $flatArray);
            }
        }

        return $flatArray;
    }

    public function mergeTablesWithBlankColumn($headings1, $data1, $headings2, $data2)
    {
        // Combine headings with a blank column in between
        $mergedHeadings = array_merge($headings1, [''], $headings2);

        // Determine the maximum number of rows between the two datasets
        $maxRows = max(count($data1), count($data2));
        $mergedData = [];

        for ($i = 0; $i < $maxRows; $i++) {
            // Retrieve data from dataset 1 or fill with empty cells
            $row1 = $i < count($data1) ? $data1[$i] : array_fill(0, count($headings1), '');

            // Retrieve data from dataset 2 or fill with empty cells
            $row2 = $i < count($data2) ? $data2[$i] : array_fill(0, count($headings2), '');

            // Combine rows with a blank column in between
            $mergedData[] = array_merge($row1, [''], $row2);
        }

        return [
            'headings' => $mergedHeadings,
            'data' => $mergedData
        ];
    }


    private function setErrorResponse(string $message, int $statusCode)
    {
        $this->status = 'error';
        $this->message = $message;
        $this->statusCode = $statusCode;
        $this->data = [];
    }
    
    /**
     * Format write-off amount to ensure zero values are displayed as blank in Excel
     */
    private function formatWriteOffAmount($writeoff_amount)
    {
        if($writeoff_amount == 0)
        {
            return '0'; // Return empty string for zero values to show as blank in Excel
        }
        return $writeoff_amount;
    }
}
