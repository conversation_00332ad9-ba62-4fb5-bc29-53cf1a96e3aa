<?php

namespace App\Console\Commands\Workflows\Account;

use Illuminate\Console\Command;
use App\Console\Commands\Workflow;

class assetSettingsWorkflow extends Workflow
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'workflow:assetSettings {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'List all assets for an account.';

    /**
     * Get table schema.
     */
    protected $schema = [
        "table" => [
            "tableTitle" => "Assets",
            "edit_mode" => "row",
            "fields" => [
                "*"
            ],
            "select_all" => true,
            "select_actions" => [
                [
                    "title" => "Update Assets",
                    "icon" => "ri-edit-box-line",
                    "color" => "success",
                    "api" => [
                        'method' => 'put',
                        'url' =>  "/admin/assets/editAsset/:id",

                    ],
                    "rerender" => true,
                    "variant" => "contained"
                ],

            ],
            "columns" => [
                [
                    "title" => "Sr No",
                    "key" => "number"
                ],
                [
                    "title" => "Name of Category",
                    "key" => "assets_categories_name",
                    "editable" => true
                ],
                [
                    "title" => "Movable/Immovable",
                    "key" => "assets_categories_type",
                    "type" => "select",
                    "editable" => true,
                    "options" => [
                        [
                            "title" => "Movable",
                            "value" => "Movable",
                        ],
                        [
                            "title" => "Immovable",
                            "value" => "Immovable",
                        ]
                    ]
                ],
                [
                    "title" => "Actions",
                    "type" => "actions",
                    "key" => "actions",
                    "size" => "120px",
                    "actions" => [
                        [
                            "title" => "Edit Asset",
                            "icon" => "ri-edit-box-line",
                            "isedit" => true,
                            "_action" => [
                                "add"   
                            ]
                        ],
                        [
                            "title" => "Delete",
                            "icon" => "ri-prohibited-2-line",
                            "api" => [
                                "method" => "DELETE",
                                "url" => "/admin/assets/settings/:id",
                            ],
                            "rerender" => true,
                            "disable_on" => [
                                "status" => [
                                    "0"
                                ],
                                // "_action" => [
                                //     "add"
                                // ]
                            ]
                        ],
                        // [
                        //     "title" => "Save",
                        //     "icon" => "ri-prohibited-2-line",
                        //     "href" => "/admin/assets/editAsset/:id",
                        // ]
                    ]
                ]
            ]
        ]
    ];

    protected $rules = [
        'page' => 'integer',
        'per_page' => 'integer',
    ];

    protected $messages = [
        'page.integer' => 'Page must be an integer.',
        'per_page.integer' => 'Per page must be an integer.',
    ];

    /**
     * Execute the console command.
     */
    public function apply()
    {
        $assetsList = $this->action('datasource:assetSettings', $this->pointer, $this->request);
        $this->data = $assetsList;
        $this->meta['schema'] = $this->schema;
    }
}
