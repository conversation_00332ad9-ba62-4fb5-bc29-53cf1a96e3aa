<?php

namespace App\Console\Commands\Workflows\Account;

use Illuminate\Console\Command;
use App\Console\Commands\Workflow;

class changeGroupStatusWorkflow extends Workflow
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'workflow:changeGroupStatus {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Change Group Status Workflow';

    protected $rules = [
        'status' => 'required|in:1,0',
        'id' => 'required|integer',
    ];

    protected $rulesMessage = [
        'status.required' => 'Status is required',
        'status.in' => 'Status must be 1 or 0',
        'id.required' => 'Id is required',
        'id.integer' => 'Id must be an integer',
    ];

    public function apply()
    {
        $this->data = $this->action('datasource:changeGroupStatus', $this->pointer, $this->input);
    }
}
