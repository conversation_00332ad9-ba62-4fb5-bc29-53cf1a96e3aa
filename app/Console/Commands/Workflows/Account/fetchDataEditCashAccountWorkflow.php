<?php

namespace App\Console\Commands\Workflows\Account;

use Illuminate\Console\Command;
use App\Console\Commands\Workflow;

class fetchDataEditCashAccountWorkflow extends Workflow
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'workflow:fetchDataEditCashAccount {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Fetch data for editing cash account';

    /**
     * Execute the console command.
     */
    public function apply()
    {
        $editCashAccountData = $this->action('datasource:fetchDataEditCashAccount', $this->pointer, $this->request);
        $this->data = $editCashAccountData;
    }
}
