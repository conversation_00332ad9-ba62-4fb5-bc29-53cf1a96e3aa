<?php

namespace App\Console\Commands\Workflows\Helpdesk\Complaints\IssueDetail\AssignJobCard;

use App\Console\Commands\Workflow;

class IssueDetailAssignJobCardDownloadWorkflow extends Workflow
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'workflow:issueDetailAssignJobCardDownload {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Download Job Card Detail workflow';

    protected $headings = [
        'Handler Name',
        'Job Details',
        'Visiting Date',
        'Created Date',
        'Created By',
        'Updated Date',
        'Updated By',
        'Job ID',
        'Job Options',
        'Attempted Options',
        'Job Card Scan File'
    ];

    /**
     * Execute the console command.
     */
    public function apply()
    {
        $issue_id = $this->input['issue_id'];
        $type = $this->input['type'];

        if ($issue_id == null || $issue_id == '' || $issue_id == ':issue_id') {
            $this->status = 'error';
            $this->message = 'Please provide a valid issue_id';
            $this->statusCode = 400;
            $this->data = [];
        } else if (!$type || $type == '' || $type == ':type') {
            $this->status = 'error';
            $this->message = 'Please provide a valid type either excel or pdf';
            $this->statusCode = 400;
            $this->data = [];
        } else {
            // Get the job card data using the list datasource to get all job cards for the issue
            $jobCardData = $this->action('datasource:issueDetailAssignJobCardDetail', $this->pointer, $this->input);
            $this->data = [];
            if ($type == 'excel') {
                $data = $this->hitCURLForGenerateCSV($jobCardData, $this->headings, 'job_card_list_');
                $this->data['url'] = $data['data'];
            } else {
                $data = $this->hitCURLForGeneratePDF($jobCardData, $this->headings, 'job_card_detail');
                $this->data['url'] = $data['data'];
            }
        }
    }
}
