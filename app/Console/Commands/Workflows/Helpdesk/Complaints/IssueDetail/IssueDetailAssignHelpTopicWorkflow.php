<?php

namespace App\Console\Commands\Workflows\Helpdesk\Complaints\IssueDetail;

use App\Console\Commands\Workflow;

class IssueDetailAssignHelpTopicWorkflow extends Workflow
{

    // signature 
    protected $signature = 'workflow:issueDetailAssignHelpTopic {input}';

    // description
    protected $description = 'Issue Detail Assign Help Topic Workflow';

    public function apply()
    {
        $this->data = $this->action('datasource:issueDetailAssignHelpTopic', $this->pointer, $this->input);
    }
}
