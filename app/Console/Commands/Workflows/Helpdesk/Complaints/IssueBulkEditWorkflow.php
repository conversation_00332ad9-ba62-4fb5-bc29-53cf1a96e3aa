<?php

namespace App\Console\Commands\Workflows\Helpdesk\Complaints;

use App\Console\Commands\Workflow;

class IssueBulkEditWorkflow extends Workflow
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'workflow:issueBulkEdit {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Get the helpdesk issues edit workflow';

    /**
     * Execute the console command.
     */

    //  protected $rules = [
    //     'issue_ids' => "required",
    //     'assignee' => 'required',
    //     'title' => 'required',
    //     'body' => 'required',
    //     'file_name'=>'sometimes', // Make file_name optional
    //     'file_path'=>'required_with:file_name',
    //     'mime_type'=>'required_with:file_name',
    //     'updated_by' => 'required',
    // ];
    
    // protected $rulesMessage = [
    //     'issue_ids.required' => 'Please provide a valid issue_ids',
    //     'assignee.required' => 'Please provide a valid assignee',
    //     'title.required' => 'Please provide a valid title',
    //     'body.required' => 'Please provide a valid body',
    //     'file_path.required_with' => 'Please provide a valid file_path when file_name is present',
    //     'mime_type.required_with' => 'Please provide a valid mime_type when file_name is present',
    //     'updated_by.required' => 'Please provide a valid updated_by',
    // ];
    
    public function apply()
    {
        $issue_ids = $this->input['ids'];

        $status = $this->input['status'];
        if($issue_ids == null || $issue_ids == [] || $issue_ids == ':ids')
        {
            $this->status = 'error';
            $this->message = 'Please provide a valid ids';
            $this->statusCode = 400;
            $this->data = [];
        }else{

            $this->data = $this->action('datasource:issueBulkEdit', $this->pointer, $this->input);
        }

    }
}
