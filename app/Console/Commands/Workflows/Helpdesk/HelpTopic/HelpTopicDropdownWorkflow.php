<?php
namespace App\Console\Commands\Workflows\Helpdesk\HelpTopic;

use App\Console\Commands\Workflow;

class HelpTopicDropdownWorkflow extends Workflow
{
    protected $signature = 'workflow:helpTopicDropdown {input}';
    protected $description = 'Help Topic Dropdown';

    protected $schema = [
        "table" => [
            "tableTitle" => "Help Topic Dropdown",
            "actions" => [],
        ],
    ];

    public function apply()
    {
        $this->data = $this->action('datasource:helpTopicDropdown', $this->pointer, $this->input);
    }
}
