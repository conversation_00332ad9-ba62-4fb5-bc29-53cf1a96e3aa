<?php

namespace App\Console\Commands\Workflows\Income\Advances;

use App\Console\Commands\Workflow;
use Illuminate\Console\Command;

class RefundMoneyAmountWorkflow extends Workflow
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'workflow:refundMoneyAmount {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Refund Money';

    protected $rules = [
        "account_id" => "required|numeric|min:1",
        // add type is either member, nonmember or vendor
        "type" => "required|in:member,nonmember,vendor"
    ];

    protected $rulesMessage = [
        "account_id.required" => "Account id is required.",
        "account_id.numeric" => "Account id must be a number.",
        "account_id.min" => "Account id must be at least 1.",
        "type.required" => "Type is required.",
        "type.in" => "Type must be either member, nonmember or vendor.",
    ];

    public function apply()
    {
        $this->data = $this->action('datasource:refundMoneyAmount', $this->pointer, $this->request);
    }
}
