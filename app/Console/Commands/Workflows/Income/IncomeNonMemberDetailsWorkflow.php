<?php

namespace App\Console\Commands\Workflows\Income;

use Illuminate\Console\Command;
use App\Console\Commands\Workflow;
use DateTime;
use Illuminate\Support\Facades\DB;

class IncomeNonMemberDetailsWorkflow extends Workflow
{
    protected $signature = 'workflow:IncomeNonMemberDetails {input}';

    protected $description = 'Income Non Member Details Workflow';

    protected $rules = [
        'id' => 'required',
        'invoice_number' => 'required',
    ];

    protected $rulesMessage = [
        'id.required' => 'Unit id is required',
        'invoice_number.required' => 'Invoice number is required',
    ];

    protected $headings = [];

    public function apply()
    {
        $NonMemberBillDataSource = $this->action('datasource:IncomeNonMemberDetails', $this->pointer, $this->request);
        if(empty($NonMemberBillDataSource)){
            $this->status = 'error';
            $this->message = 'No data found for this details';
            $this->statusCode = 400;
            $this->data = [];
            return;
        }

        $NonMemberBillDetails = $NonMemberBillDataSource;
        // Initialize accumulator array with the same keys

        // Initialize total_array with keys for totals.
        $total_array = [
            'total_interest_amount' => 0,
            'total_amount' => 0,
        ];
        // Loop through each element within the "data" key of $NonMemberBillDetails
        $NonMemberBillDetails['roundoff_interest_amount'] = round($NonMemberBillDetails['interest_amount'], 2);
        $NonMemberBillDetails['total_due'] = round($NonMemberBillDetails['bill_amount'] + round($NonMemberBillDetails['roundoff_amount'], 2), 2); //  - $NonMemberBillDetails['advance_amount']
        $NonMemberBillDetails['grand_total'] = round($NonMemberBillDetails['total_due'] - $NonMemberBillDetails['advance_amount'], 2);
        $NonMemberBillDetails['balance_due'] = round($NonMemberBillDetails['grand_total'] + $NonMemberBillDetails['principal_amount'] + round($NonMemberBillDetails['interest_amount'], 2), 2);
        $NonMemberBillDetails['total_due_in_words'] = $this->convertNumberToWords($NonMemberBillDetails['balance_due']);

        $bill_date = new DateTime($NonMemberBillDetails['bill_date']);
        $NonMemberBillDetails['bill_date'] = $bill_date->format('M d, Y');

        $fromDate = new DateTime($NonMemberBillDetails['from_date']);
        $NonMemberBillDetails['from_date'] = $fromDate->format('M d, Y');

        $endDate = new DateTime($NonMemberBillDetails['end_date']);
        $NonMemberBillDetails['end_date'] = $endDate->format('M d, Y');

        $this->request['from_date'] = $NonMemberBillDetails['from_date'];
        $this->request['to_date'] = $NonMemberBillDetails['end_date'];
        $this->request['receipt_form'] = 'Non Member';

        /*$LastPeriodPaymentTransaction = $this->action('datasource:IncidentalLastPeriodPaymentTransaction', $this->pointer, $this->request);

        $NonMemberBillDetails['Last_payment_receipt_detail'] = $LastPeriodPaymentTransaction;*/
        $arrSocietyDetail = $this->getSocietyDetail([]); //get all Unit details
        $NonMemberBillDetails['arrSocietyDetail'] =  $arrSocietyDetail;
        $companyDetails = [];

        $data = $this->hitCURLForGeneratePDF(
            $NonMemberBillDetails,
            $this->headings,
            'NonMemberBill',
            "",
            "",
            $companyDetails
        );

        $this->data['url'] = $data['data'];
        //$this->data = $NonMemberBillDetails;
    }

    public function getSocietyDetail($data = array())
    {
        $arrSocietyMaster = array();
        $arrSocietyMaster = DB::connection('master')->table('chsone_societies_master')->where('soc_id', $this->input['company_id'])
            ->first();
        $arrSocietyMaster = (array) $arrSocietyMaster;
        if (!empty($arrSocietyMaster)) {
            $arrSocietyMaster['soc_name'] = ((trim($arrSocietyMaster['soc_name'])));
            $arrSocietyMaster['soc_name_short'] = $data['soc_name_short'] ?? 'suraj';
            $arrSocietyMaster['soc_address_1'] = ucwords(strtolower(trim($arrSocietyMaster['soc_address_1'])));
            $arrSocietyMaster['soc_address_2'] = ucwords(strtolower(trim($arrSocietyMaster['soc_address_2'])));
            $arrSocietyMaster['soc_landmark'] = ucwords(strtolower(trim($arrSocietyMaster['soc_landmark'])));
            $arrSocietyMaster['soc_city_or_town'] = ucwords(strtolower(trim($arrSocietyMaster['soc_city_or_town'])));
            $arrSocietyMaster['soc_state'] = ucwords(strtolower(trim($arrSocietyMaster['soc_state'])));
            $arrSocietyMaster['soc_pincode'] = 4546454; //ucwords(strtolower(($arrSocietyMaster['soc_pincode'])));
        }
        return $arrSocietyMaster;
    }

    public function getSocietyName($soc_building_name, $unit_flat_number)
    {
        return $soc_building_name .' / '. $unit_flat_number;
    }

    public function getMemberName($member_first_name, $member_last_name)
    {
        return $member_first_name .' '. $member_last_name;
    }

    public function getTotalDue($amount, $tax_amount, $advance_amount, $payment_amount)
    {
        $tax_amount = empty($tax_amount) ? 0 : $tax_amount;
        $advance_amount = empty($advance_amount) ? 0 : $advance_amount;
        $payment_amount = empty($payment_amount) ? 0 : $payment_amount;
        return round($amount + $tax_amount - $advance_amount - $payment_amount);
    }

    public function getPeriod($from_date, $to_date)
    {
        return date('d/m/Y', strtotime($from_date)) .' To '. date('d/m/Y', strtotime($to_date));
    }
}
