<?php

namespace App\Console\Commands\Workflows\Income;

use Illuminate\Console\Command;
use App\Console\Commands\Workflow;
use DateTime;
use Illuminate\Support\Facades\DB;

class IncomeDetailsWorkflow extends Workflow
{
    protected $signature = 'workflow:IncomeDetails {input}';

    protected $description = 'Income Details Workflow';

    protected $rules = [
        'id' => 'required',
        'invoice_number' => 'required',
    ];

    protected $rulesMessage = [
        'id.required' => 'Unit id is required',
        'invoice_number.required' => 'Invoice number is required',
    ];

    protected $headings = [];

    public function apply()
    {
        $incidentalBillDataSource = $this->action('datasource:IncomeDetails', $this->pointer, $this->request);

        if (empty($incidentalBillDataSource)) {
            $this->status = 'error';
            $this->message = 'No data found for this details';
            $this->statusCode = 400;
            $this->data = [];
            return;
        }

        $incidentalBillDetails = $incidentalBillDataSource;

        // Initialize total_array with keys for totals
        $total_array = [
            'total_interest_amount' => 0,
            'total_amount' => 0,
        ];

        // Loop through each element within the "data" key of $incidentalBillDetails
        foreach ($incidentalBillDetails['invoice_particular'] as $eachItem) {
            // Sum the interest_amount from each item
            // $total_array['total_interest_amount'] += round($eachItem['interest_amount'], 2);

            // Calculate total amount for the current item: amount + principal_amount
            // Then add it to the cumulative total
            $total_array['total_amount'] += round((float)$eachItem['amount'], 2);
        }

        $total_amount = $total_array['total_amount'] + $incidentalBillDetails['creditNoteAdjustment']['interest_amount'];

        $grand_total = $total_amount
            + $incidentalBillDetails['creditNoteAdjustment']['outstanding_principal']
            + $incidentalBillDetails['creditNoteAdjustment']['outstanding_interest']
            - $incidentalBillDetails['creditNoteAdjustment']['advance_amount'];

        // Format the final amounts to ensure 2 decimal places are always shown
        $total_array['total_amount'] = number_format((float)$total_amount, 2, '.', '');
        $total_array['grand_total'] = number_format((float)$grand_total, 2, '.', '');

        $total_array['grand_total_in_words'] = $this->convertNumberToWords($total_array['grand_total']);

        // At this point, $total_array contains the sum for interest_amount and total_amount
        $incidentalBillDetails['total_array'] = $total_array;

        // Format creditNoteAdjustment values after calculations are complete
        if (isset($incidentalBillDetails['creditNoteAdjustment']) && $incidentalBillDetails['creditNoteAdjustment']) {
            $incidentalBillDetails['creditNoteAdjustment']['principal_amount'] = number_format((float)$incidentalBillDetails['creditNoteAdjustment']['principal_amount'], 2, '.', '');
            $incidentalBillDetails['creditNoteAdjustment']['interest_amount'] = number_format((float)$incidentalBillDetails['creditNoteAdjustment']['interest_amount'], 2, '.', '');
            $incidentalBillDetails['creditNoteAdjustment']['advance_amount'] = number_format((float)$incidentalBillDetails['creditNoteAdjustment']['advance_amount'], 2, '.', '');
            $incidentalBillDetails['creditNoteAdjustment']['outstanding_principal'] = number_format((float)$incidentalBillDetails['creditNoteAdjustment']['outstanding_principal'], 2, '.', '');
            $incidentalBillDetails['creditNoteAdjustment']['outstanding_interest'] = number_format((float)$incidentalBillDetails['creditNoteAdjustment']['outstanding_interest'], 2, '.', '');
            $incidentalBillDetails['creditNoteAdjustment']['roundoff_amount'] = number_format((float)$incidentalBillDetails['creditNoteAdjustment']['roundoff_amount'], 2, '.', '');
        }

        // Need to pass unit id from incidentalBillDetails in IncidentalLastPeriodPaymentTransaction as input
        $this->request['unit_id'] = $incidentalBillDetails['creditNoteAdjustment']['fk_unit_id'];

        $fromDate = new DateTime($incidentalBillDetails['creditNoteAdjustment']['from_date']);
        $incidentalBillDetails['creditNoteAdjustment']['from_date'] = $fromDate->modify('+1 day')->format('M d, Y');

        // For 'due_date'
        $dueDate = new DateTime($incidentalBillDetails['creditNoteAdjustment']['due_date']);
        $incidentalBillDetails['creditNoteAdjustment']['due_date'] = $dueDate->modify('+1 day')->format('M d, Y');

        // For 'to_date'
        $toDate = new DateTime($incidentalBillDetails['creditNoteAdjustment']['to_date']);
        $incidentalBillDetails['creditNoteAdjustment']['to_date'] = $toDate->modify('+1 day')->format('M d, Y');

        $this->request['from_date'] = $incidentalBillDetails['creditNoteAdjustment']['from_date'];
        $this->request['to_date'] = $incidentalBillDetails['creditNoteAdjustment']['due_date'];
        $this->request['due_date'] = $incidentalBillDetails['creditNoteAdjustment']['to_date'];
        $this->request['receipt_form'] = 'maintenance';

        $LastPeriodPaymentTransaction = $this->action('datasource:IncidentalLastPeriodPaymentTransaction', $this->pointer, $this->request);

        $incidentalBillDetails['Last_payment_receipt_detail'] = $LastPeriodPaymentTransaction;
        $arrSocietyDetail = $this->getSocietyDetail([]); // Get all Unit details
        $incidentalBillDetails['arrSocietyDetail'] = $arrSocietyDetail;
        $companyDetails = [];
        $data = $this->hitCURLForGeneratePDF(
            $incidentalBillDetails,
            $this->headings,
            'viewMaintainance-invoice',
            "",
            "",
            $companyDetails
        );

        $this->data['url'] = $data['data'];
        // $this->data = $incidentalBillDetails;
    }

    public function getSocietyDetail($data = array())
    {
        $arrSocietyMaster = array();
        $arrSocietyMaster = DB::connection('master')->table('chsone_societies_master')
            ->where('soc_id', $this->input['company_id'])
            ->first();
        $arrSocietyMaster = (array) $arrSocietyMaster;

        if (!empty($arrSocietyMaster)) {
            $arrSocietyMaster['soc_name'] = trim($arrSocietyMaster['soc_name']);
            $arrSocietyMaster['soc_name_short'] = $data['soc_name_short'] ?? 'suraj';
            $arrSocietyMaster['soc_address_1'] = ucwords(strtolower(trim($arrSocietyMaster['soc_address_1'])));
            $arrSocietyMaster['soc_address_2'] = ucwords(strtolower(trim($arrSocietyMaster['soc_address_2'])));
            $arrSocietyMaster['soc_landmark'] = ucwords(strtolower(trim($arrSocietyMaster['soc_landmark'])));
            $arrSocietyMaster['soc_city_or_town'] = ucwords(strtolower(trim($arrSocietyMaster['soc_city_or_town'])));
            $arrSocietyMaster['soc_state'] = ucwords(strtolower(trim($arrSocietyMaster['soc_state'])));
            $arrSocietyMaster['soc_pincode'] = 4546454; // ucwords(strtolower(($arrSocietyMaster['soc_pincode'])));
        }

        return $arrSocietyMaster;
    }

    public function getSocietyName($soc_building_name, $unit_flat_number)
    {
        return $soc_building_name . ' / ' . $unit_flat_number;
    }

    public function getMemberName($member_first_name, $member_last_name)
    {
        return $member_first_name . ' ' . $member_last_name;
    }

    public function getTotalDue($amount, $tax_amount, $advance_amount, $payment_amount)
    {
        $tax_amount = empty($tax_amount) ? 0 : $tax_amount;
        $advance_amount = empty($advance_amount) ? 0 : $advance_amount;
        $payment_amount = empty($payment_amount) ? 0 : $payment_amount;

        return round((float)$amount + (float)$tax_amount - (float)$advance_amount - (float)$payment_amount);
    }

    public function getPeriod($from_date, $to_date)
    {
        return date('d/m/Y', strtotime($from_date)) . ' To ' . date('d/m/Y', strtotime($to_date));
    }
}
