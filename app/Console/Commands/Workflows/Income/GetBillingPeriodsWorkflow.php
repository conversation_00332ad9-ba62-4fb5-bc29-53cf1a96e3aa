<?php

namespace App\Console\Commands\Workflows\Income;
use App\Console\Commands\Workflow;

class GetBillingPeriodsWorkflow extends Workflow
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'workflow:getBillingPeriods {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Workflow for getting billing periods';

    public function apply()
    {
        // Call the datasource action to get the billing periods data
        $billingPeriodsResult = $this->action('datasource:getBillingPeriods', $this->pointer, $this->request);

        // Pass the datasource data into the workflow's data property
        $this->data = $billingPeriodsResult;
        // $this->status = $billingPeriodsResult->status;
        // $this->message = $billingPeriodsResult->message;
        // $this->statusCode = $billingPeriodsResult->statusCode;
    }


}
