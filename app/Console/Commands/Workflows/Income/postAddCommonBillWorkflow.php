<?php

namespace App\Console\Commands\Workflows\Income;

use Illuminate\Console\Command;
use App\Console\Commands\Workflow;

class postAddCommonBillWorkflow extends Workflow
{
    protected $signature = 'workflow:postAddCommonBill {input}';

    protected $description = 'Add Common Bill Workflow';

    protected $rules = [
        'unit_id' => 'required',
        'member_id' => 'required',
        'flat_number' => 'required',
        'member_name' => 'required',
        'particular_id' => 'required',
        'bill_date' => 'required|date_format:Y-m-d',
        'due_date' => 'required|date_format:Y-m-d',
        'from_date' => 'required|date_format:Y-m-d',
        'to_date' => 'required|date_format:Y-m-d',
        'particular_amount' => 'required|numeric',
        // 'tax_amount' => 'required',
        'invoice_amount' => 'required',
    ];

    protected $rulesMessage = [
        'unit_id.required' => 'Unit Id is required',
        'member_id.required' => 'Member Id is required',
        'flat_number.required' => 'Flat Number is required',
        'member_name.required' => 'Member Name is required',
        'particular_id.required' => 'Particular Id is required',
        'bill_date.required' => 'Bill Date is required',
        'bill_date.date_format' => 'Bill Date format must be Y-m-d',
        'due_date.required' => 'Due Date is required',
        'due_date.date_format' => 'Due Date format must be Y-m-d',
        'from_date.required' => 'From Date is required',
        'from_date.date_format' => 'From Date format must be Y-m-d',
        'to_date.required' => 'To Date is required',
        'to_date.date_format' => 'To Date format must be Y-m-d',
        'particular_amount.required' => 'Particular Amount is required',
        'particular_amount.numeric' => 'Particular Amount must be a number',
        // 'tax_amount.required' => 'Tax Amount is required',
        'invoice_amount.required' => 'Invoice Amount is required',
    ];

    protected $formatter = [];

    protected $formatterByKeys = [];

    public function apply()
    {
        // insert the record in income_common_billing_charges table
        $income_common_billing_charges = $this->action('datasource:InsertIncomeCommonBillingCharges', $this->pointer, $this->request);
        $newInvoiceNumber = $income_common_billing_charges['invoice_number'];
        $this->request['invoice_number'] = $newInvoiceNumber;
        $this->request['payment_status'] = $income_common_billing_charges['payment_status'];
        // insert the record in income_unit_invoices table
        $income_unit_invoices = $this->action('datasource:InsertIncomeUnitInvoices', $this->pointer, $this->request);
        $voucher_reference_id = $income_unit_invoices['unit_invoice_id'];
        $this->request['voucher_reference_id'] = $voucher_reference_id;
        $this->request['interest_amount'] = $income_unit_invoices['interest_amount'] ?? 0;

        // insert the record in chsone_ledger_transactions table
        $chsone_ledger_transactions = $this->action('datasource:InsertChsoneLedgerTransactions', $this->pointer, $this->request);

        // Add chsone tax log entry
        $chsone_tax_log = $this->action('datasource:InsertChsoneTaxLog', $this->pointer, $this->request);

        $this->status = 'success';
        $this->statusCode = 200;
        $this->message = 'Common Bill Added Successfully';
        $this->data = $income_common_billing_charges;
    }
}