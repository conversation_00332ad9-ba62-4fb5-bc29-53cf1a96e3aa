<?php

namespace App\Console\Commands\Workflows\Income;

use Illuminate\Console\Command;
use App\Console\Commands\Workflow;

class postPayCommonBillWorkflow extends Workflow
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'workflow:postPayCommonBill {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Workflow for postPayCommonBill';

    protected $rules = [
        'unit_id' => 'required',
        // 'invoice_number' => 'required',
        // 'common_bill_id' => 'required',
        'member_id' => 'required',
        'received_from' => 'required',
        // 'particular_id' => 'required',
        'total_due_amount' => 'required|numeric|min:0',
        'invoice_amount' => 'required|numeric|min:1',
        'receipt_mode' => 'required|in:cash,cheque,cashtransfer',
        'receipt_date' => 'required|date_format:Y-m-d',
        // 'user_id' => 'required',
        // check if the receipt mode is cheque or cashtransfer then the following fields are required
        'transaction_reference' => 'required_if:receipt_mode,cashtransfer',
        'bank_name' => 'required_if:receipt_mode,cheque',
        'cheque_date' => 'required_if:receipt_mode,cheque|date_format:Y-m-d',
        "cheque_number" => "required_if:payment_mode,cheque",
        'bank_account' => 'required_if:receipt_mode,cheque,cashtransfer',
    ];

    protected $rulesMessage = [
        'unit_id.required' => 'Unit Id is required',
        // 'invoice_number.required' => 'Invoice Number is required',
        // 'common_bill_id.required' => 'Common Bill Id is required',
        'member_id.required' => 'Member Id is required',
        'received_from.required' => 'Member Name is required',
        // 'particular_id.required' => 'Particular Id is required',
        'total_due_amount.required' => 'Total Due Amount is required',
        'total_due_amount.numeric' => 'Total Due Amount must be a number and greater than 0',
        'invoice_amount.required' => 'Invoice Amount is required',
        'invoice_amount.numeric' => 'Invoice Amount must be a number',
        'invoice_amount.min' => 'Invoice Amount must be greater than 0',
        'receipt_mode.required' => 'Receipt Mode is required',
        'receipt_mode.in' => 'Receipt Mode must be a cash, cheque or cashtransfer only',
        'receipt_date.required' => 'Receipt Date is required',
        'receipt_date.date_format' => 'Receipt Date format must be Y-m-d',
        // 'user_id.required' => 'User Id is required',
        'transaction_reference.required_if' => 'Transaction Reference is required',
        'bank_name.required_if' => 'Bank Name is required',
        'cheque_date.required_if' => 'Cheque Date is required',
        'cheque_date.date_format' => 'Cheque Date format must be Y-m-d',
        "cheque_number.required_if" => "Cheque Number is required",
        'bank_account.required_if' => 'Bank Account is required',
    ];

    /**
     * Execute the console command.
     */
    public function apply()
    {
        $income_common_billing_charges_receipt = $this->action('datasource:postPayCommonBill', $this->pointer, $this->request);
    }
}
