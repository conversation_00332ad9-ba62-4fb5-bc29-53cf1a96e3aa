<?php

namespace App\Console\Commands\Workflows\Reports;

use Illuminate\Console\Command;
use App\Console\Commands\Workflow;

class NonMembersReceivableReportWorkflow extends Workflow
{
    protected $signature = 'workflow:nonMembersReceivableReport {input}';

    protected $description = 'Get Non-Member Receivable Report';

    public function apply()
    {
        // Prepare request data with filters
        $requestData = $this->request;
        
        // If we have filters in the request, pass them through
        if (isset($this->request['filters'])) {
            $requestData = array_merge($requestData, [
                'filters' => $this->request['filters']
            ]);
        }
        
        // Pass the prepared request data to the action
        $data = $this->action('datasource:nonMembersReceivableReport', $this->pointer, $requestData);

        // Format numbers in data array
        foreach ($data as &$record) {
            $record['due_amount'] = (float) round($record['due_amount'], 2);
            $record['cr_bal'] = (float) round($record['cr_bal'], 2);
            $record['ledger_bal'] = (float) round($record['ledger_bal'], 2);        
        }

        // Calculate and format totals
        $totals = [
            'total_payment_dues' => round(array_sum(array_column($data, 'due_amount')), 2),
            'total_credit_balance' => round(array_sum(array_column($data, 'cr_bal')), 2),
            'total_ledger_balance' => round(array_sum(array_column($data, 'ledger_bal')), 2),        
        ];

        $this->data = [
             $data,
            [$totals]
        ];

        $this->meta['schema'] = $this->schema();
    }
    
    public function schema()
    {
        $filterType = $this->input['filters']['type'] ?? null;
    
        $columns = [];
        $summary = [];
    
        // Determine which columns to show based on filter
        if ($filterType === 'payment_due') {
            $columns[] = [
                ['title' => 'Name', 'key' => 'full_name'],
                ['title' => 'Payment Dues', 'key' => 'due_amount', 'type' => 'amount'],
            ];
            $summary[] = [
                ['title' => 'Payment Dues', 'key' => 'total_payment_dues', 'type' => 'amount'],
                ['title' => 'Credit Balance', 'key' => 'total_credit_balance', 'type' => 'amount'],
                ['title' => 'Ledger Balance', 'key' => 'total_ledger_balance', 'type' => 'amount'],
            ];
        } elseif ($filterType === 'ledger') {
            $columns[] = [
                ['title' => 'Name', 'key' => 'full_name'],
                ['title' => 'Ledger Balance', 'key' => 'ledger_bal', 'type' => 'amount'],
            ];
            $summary[] = [
                ['title' => 'Payment Dues', 'key' => 'total_payment_dues', 'type' => 'amount'],
                ['title' => 'Credit Balance', 'key' => 'total_credit_balance', 'type' => 'amount'],
                ['title' => 'Ledger Balance', 'key' => 'total_ledger_balance', 'type' => 'amount'],
            ];
        } else {
            $columns[] = [
                ['title' => 'Name', 'key' => 'full_name'],
                ['title' => 'Payment Dues', 'key' => 'due_amount', 'type' => 'amount'],
                ['title' => 'Credit Balance', 'key' => 'cr_bal', 'type' => 'amount'],
                ['title' => 'Ledger Balance', 'key' => 'ledger_bal', 'type' => 'amount'],
            ];
            $summary[] = [
                ['title' => 'Payment Dues', 'key' => 'total_payment_dues', 'type' => 'amount'],
                ['title' => 'Credit Balance', 'key' => 'total_credit_balance', 'type' => 'amount'],
                ['title' => 'Ledger Balance', 'key' => 'total_ledger_balance', 'type' => 'amount'],
            ];
        }
    
        return [
            'table' => [
                'tableTitle' => [
                    'Nonmembers Receivable',
                    'Summary'
                ],
                'select_by' => [
                    'payment_dues_less' => 'Payment dues less than equal to',
                    'payment_dues_greater' => 'Payment dues greater than equal to',
                    'due_date' => 'Due on date'
                ],
                'filter_by' => [
                    'type' => [
                        'title' => 'Filter By',
                        'options' => [
                            'payment_due' => 'Payment due',
                            'ledger' => 'Ledger'
                        ],
                        'select_single' => true,
                    ]
                ],
                'actions' => [
                    [
                        'title' => 'Export Report',
                        'icon' => 'ri-download-2-line',
                        'options' => [
                            [
                                'title' => 'Excel',
                                'icon' => 'ri-file-excel-2-line',
                                'api' => [
                                    'method' => 'GET',
                                    'url' => '/admin/reports/nonMembersReceivable/download/excel',
                                    'type' => 'download'
                                ]
                            ],
                            [
                                'title' => 'PDF',
                                'icon' => 'ri-file-pdf-2-line',
                                'api' => [
                                    'method' => 'GET',
                                    'url' => '/admin/reports/nonMembersReceivable/download/pdf',
                                    'type' => 'download'
                                ]
                            ]
                        ]
                    ]
                ],
                'columns' => array_merge($columns, $summary)
            ]
        ];
    }
}
