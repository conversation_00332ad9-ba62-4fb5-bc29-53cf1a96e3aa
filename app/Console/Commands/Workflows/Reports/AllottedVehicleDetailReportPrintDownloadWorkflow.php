<?php

namespace App\Console\Commands\Workflows\Reports;

use Illuminate\Console\Command;
use App\Console\Commands\Workflow;

class AllottedVehicleDetailReportPrintDownloadWorkflow extends Workflow
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'workflow:allottedVehicleDetailReportPrintDownload {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Allotted Vehicle Detail Report Print Download';

    protected $rules = [];
    protected $rulesMessage = [];

    protected $formatterByKeys = ["id"];

    protected $headings = [
        'Building/Unit',
        'Parking Unit',
        'Vehicle Occupied By',
        'Vehicle Registered',
        'Vehicle Type',
        'Vehicle Color',
        'Vehicle Model'
    ];

    public function apply()
    {
        $type = $this->input['type'];

        if(!$type || $type == '' || $type == ':type')
        {
            $this->status = 'error';
            $this->message = 'Please provide a valid type either excel or pdf';
            $this->statusCode = 400;
            $this->data = [];
        }
        else
        {
            // Set parameters to get all records for the report
            $this->request['per_page'] = 100000;

            // Get data from allottedVehicleList datasource
            $vehicleDetailData = $this->action('datasource:allottedVehicleList', $this->pointer, $this->request);

            // Format the data for Excel/PDF generation
            if($vehicleDetailData && count($vehicleDetailData) > 0)
            {
                $formattedData = [];
                
                foreach($vehicleDetailData as $item)
                {
                    $formattedData[] = [
                        'building_unit' => $item['building_unit'] ?? '',
                        'parking_unit' => $item['parking_unit'] ?? '',
                        'vehicle_occupied_by' => $item['vehicle_occupied_by'] ?? '',
                        'vehicle_registration_number' => $item['vehicle_registration_number'] ?? '',
                        'vehicle_type' => $item['vehicle_type'] ?? '',
                        'vehicle_colour' => $item['vehicle_colour'] ?? '',
                        'vehicle_model_number' => $item['vehicle_model_number'] ?? ''
                    ];
                }

                // Generate Excel or PDF based on type
                if($type == 'excel')
                {
                    $data = $this->hitCURLForGenerateCSV($formattedData, $this->headings, 'allotted_vehicle_detail_report_');
                    $this->data['url'] = $data['data'];
                }
                else if($type == 'pdf')
                {
                    $data = $this->hitCURLForGeneratePDF($formattedData, $this->headings, 'allotted_vehicle_detail_report');
                    $this->data['url'] = $data['data'];
                }
                else
                {
                    $this->status = 'error';
                    $this->message = 'Invalid type. Please provide either excel or pdf';
                    $this->statusCode = 400;
                    $this->data = [];
                }
            }
            else
            {
                $this->status = 'error';
                $this->message = 'No data found for the report';
                $this->statusCode = 404;
                $this->data = [];
            }
        }
    }
}
