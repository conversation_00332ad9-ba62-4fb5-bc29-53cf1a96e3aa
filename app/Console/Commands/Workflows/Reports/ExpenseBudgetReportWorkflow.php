<?php

namespace App\Console\Commands\Workflows\Reports;

use App\Console\Commands\Workflow;

class ExpenseBudgetReportWorkflow extends Workflow
{
    protected $signature = 'workflow:expenseBudgetReport {input}';
    protected $description = 'Expense Budget Report Workflow';

    public function getSchema()
    {
        $financialYear = $this->input['financial_year'] ?? date('Y') . '-' . (date('Y') + 1);
        
        return [
            "table" => [
                'tableTitle' => 'Expense Budget Report',
                'subtitle' => 'Financial Year : ' . $financialYear,
                'columns' => [
                    [
                        'title' => 'Expense Account',
                        'key' => 'expense_account',
                    ],
                    [
                        'title' => 'Expense Ledger',
                        'key' => 'expense_ledger',
                    ],
                    [
                        'title' => 'Budget',
                        'key' => 'budget',
                    ],
                    [
                        'title' => 'Actual Expense',
                        'key' => 'actual_expense',
                    ],
                    [
                        'title' => 'Variance',
                        'key' => 'variance',
                    ]
                ],
                "extraFilters" => [
                    [
                      "type"=> "financial_year",
                      "api"=> "/admin/accountsetting/accountset",
                    ]
                ],
                'actions' => [
                    [
                        "title" => "Print/Export",
                        "icon" => "ri-printer-line",
                        "options" => [
                            [
                                "title" => "PDF",
                                "icon" => "ri-file-pdf-2-line",
                                "api" => [
                                    "method" => "GET",
                                    "url" => "/admin/reports/expenseBudgetReport/download/pdf",
                                    "type" => "download"
                                ]
                            ],
                            [
                                "title" => "Excel",
                                "icon" => "ri-file-excel-2-line",
                                "api" => [
                                    "method" => "GET",
                                    "url" => "/admin/reports/expenseBudgetReport/download/excel",
                                    "type" => "download"
                                ]
                            ]
                        ]
                    ]
                ]
            ]
        ];
    }

    public function apply()
    {
        $this->data = $this->action('datasource:expenseBudgetReport', $this->pointer, $this->request);
        $this->meta['schema'] = $this->getSchema();
    }

    private function getFinancialYearOptions()
    {
        // Get current year
        $currentYear = date('Y');
        $options = [];
        
        // Generate options for last 5 years and next year
        for ($i = -5; $i <= 1; $i++) {
            $year = $currentYear + $i;
            $nextYear = $year + 1;
            $option = [
                'label' => $year . '-' . $nextYear,
                'value' => $year . '-' . $nextYear
            ];
            $options[] = $option;
        }
        
        return $options;
    }
}
