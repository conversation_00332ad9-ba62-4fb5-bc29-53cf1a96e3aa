<?php

namespace App\Console\Commands\Workflows\Reports;

use App\Console\Commands\Workflow;

class ExpensePaymentReportWorkflow extends Workflow
{
    protected $signature = 'workflow:expensePaymentReport {input}';
    protected $description = 'Expense Payment Report Workflow';
    protected $schema = [
        "table" => [
            "tableTitle" => "Expense Payment Report",
            // "is_searchable" => true,
            "columns" => [
                [
                    "title" => "Payment Date",
                    "key" => "payment_date",
                    "type" => "date"
                ],
                [
                    "title" => "Payment Number",
                    "key" => "payment_number",
                ],
                [
                    "title" => "Mode",
                    "key" => "payment_mode"
                ],
                [
                    "title" => "Type",
                    "key" => "bill_type"
                ],
                [
                    "title" => "Payment Of",
                    "key" => "invoice_number"
                ],
                [
                    "title" => "Payment Reference",
                    "key" => "transaction_reference"
                ],
                [
                    "title" => "Vendor",
                    "key" => "vendor_name"
                ],
                [
                    "title" => "Paid Amount",
                    "key" => "payment_amount",
                    "type" => "amount"
                ],
                [
                    "title" => "Write Off",
                    "key" => "writeoff_amount",
                    "type" => "amount"
                ],
                [
                    "title" => "Status",
                    "key" => "status",
                    "type" => "chip",
                    "options" => [
                        "Y"=>[
                            "title" => "Cleared",
                            "color" => "success"
                        ],
                        "P"=>[
                            "title" => "Submitted",
                            "color" => "warning"
                        ],
                        "reversed"=>[
                            "title" => "Reversed",
                            "color" => "info"
                        ],
                        "N"=>[
                            "title" => "Bounced",
                            "color" => "error"
                        ]
                    ]
                ]
            ],
            // "filter" => [
            //     "date_range" => [
            //         "type" => "daterange",
            //         "label" => "Date Range",
            //         "required" => true,
            //         "keys" => [
            //             "from" => "from_date",
            //             "to" => "to_date"
            //         ]
            //     ],
            //     "filter_by" => [
            //         "type" => "select",
            //         "label" => "Filter By",
            //         "required" => false,
            //         "options" => [
            //             ["label" => "All", "value" => "all"],
            //             ["label" => "Vendor", "value" => "vendor"],
            //             ["label" => "Expense Head", "value" => "expense_head"],
            //             ["label" => "Bill Type", "value" => "bill_type"]
            //         ]
            //     ]
            // ],
            "extraFilters" => [
                "transaction_date" => [
                    "title" => "Select Date Range",
                    "type" => "daterange",
                    "minDateTime" => "financialYearStart",
                    "maxDateTime" => "financialYearEnd",
                ]
            ],
            "filter_by" => [
                "vendor" => [
                    "title" => "Vendor",
                    "options" => [],
                ],
                "status" => [
                    "title" => "Status",
                    "options" => [
                        "Y" => "Cleared",
                        "P" => "Submitted",
                        "N" => "Bounced",
                        "reversed" => "Reversed"
                    ],
                    'select_single' => true,
                ],
                "mode" => [
                    "title" => "Payment Mode",
                    "options" => [
                        "cash" => "Cash",
                        "cheque" => "Cheque",
                        "cashtransfer" => "Electronic Fund Transfer",
                        "YESPG" => "PG Yes Bank",
                        "srvybecollect" => "Ecollect Yes Bank",
                        "srvpaytm" => "Paytm",
                        "srvmobikwik" => "Mobikwik",
                        "srvmobikwikpg" => "PG Mobikwik",
                        "CASHFREEPG" => "PG Cashfree",
                    ],
                    'select_single' => true,
                ],
                "type" => [
                    "title" => "Type",
                    "options" => [
                        "advance" => "Advance",
                        "Vendor Bill" => "Vendor Bill",
                        "Cash Purchase" => "Cash Purchase"
                    ],
                    'select_single' => true,
                ]
            ],
            "export" => [
                "enabled" => true,
                "filename" => "Expense_Report",
                "formats" => ["xlsx", "pdf"]
            ],
            'actions' => [
                [
                    "title" => "Print/Export",
                    "icon" => "ri-printer-line",
                    "options" => [
                        [
                            "title" => "PDF",
                            "icon" => "ri-file-pdf-2-line",
                            "api" => [
                                "method" => "GET",
                                "url" => "/admin/reports/expensePaymentReport/download/pdf",
                                "type" => "download"
                            ]
                        ],
                        [
                            "title" => "Excel",
                            "icon" => "ri-file-excel-2-line",
                            "api" => [
                                "method" => "GET",
                                "url" => "/admin/reports/expensePaymentReport/download/excel",
                                "type" => "download"
                            ]
                        ]
                    ]
                ]
            ]
        ],
    ];

    public function apply()
    {
        $report = $this->action('datasource:expensePaymentReport', $this->pointer, $this->request);
        
        // vendor wil be displayed now
        $data = $this->action('datasource:ActiveVendors', $this->pointer, $this->request);
        $vendorKeyValue = [];
        foreach ($data as $vendor) {
            if (isset($vendor['vendor_name'])) {
                $vendorKeyValue[$vendor['id']] = $vendor['vendor_name'];
            }
        }
    
        $this->data = $report;
        $this->meta['schema'] = $this->schema;
        $this->meta['schema']['table']['filter_by']['vendor']['options'] = $vendorKeyValue;
    }
}
