<?php

namespace App\Console\Commands\Workflows\Reports;

use App\Console\Commands\Workflow;

class BankRecoReportWorkflow extends Workflow
{
    protected $signature = 'workflow:bankRecoReport {input}';
    protected $description = 'Bank Reconciliation Report Workflow';
    protected $schema = [];

    public function getSchema()
    {
        return [
            "table" => [
                "tableTitle" => "Bank Reco Statement",
                "fields" => [
                    "*"
                ],
                "extraFilters" => [
                    "date_range" => [
                        "title" => "Select Date Range",
                        "type" => "daterange",
                        "minDateTime" => "financialYearStart",
                        "maxDateTime" => "financialYearEnd",
                    ]
                ],
                "filter_by" => [
                    "transaction_date" => [
                        "type" => "select",
                        "title" => "Filter By",
                        "options" => [
                            "Transaction Date" => "Transaction Date",
                            "Bank Date" => "Bank Date"
                        ],
                        "select_single" => true,
                    ],
                    "type" => [
                        "title" => "Type",
                        "type" => "select",
                        "options" => [
                            "0" => "All",
                            "1" => "Reconciled",
                            "2" => "Unconciled"
                        ],
                        "select_single" => true,
                    ],
                    "bank_account" => [
                        "title" => "Bank Account",
                        "type" => "select",
                        "options" => [],
                        "select_single" => true,
                    ]

                ],
                "columns" => [
                    [
                        "title" => "Sr. No",
                        "key" => "sr_no",
                        "type" => "number"
                    ],
                    [
                        "title" => "Reconciliation Status",
                        "key" => "reconciliation_status"
                    ],
                    [
                        "title" => "From Account",
                        "key" => "from_account"
                    ],
                    [
                        "title" => "Date",
                        "key" => "transaction_date",
                        "type" => "date"
                    ],
                    [
                        "title" => "Particulars/Narration",
                        "key" => "memo_desc"
                    ],
                    [
                        "title" => "Payment Mode",
                        "key" => "payment_mode"
                    ],
                    [
                        "title" => "Payment Reference",
                        "key" => "payment_reference"
                    ],
                    [
                        "title" => "Bank Date",
                        "key" => "bank_date",
                        "type" => "date"
                    ],
                    [
                        "title" => "Deposit",
                        "key" => "deposit",
                        "type" => "amount"
                    ],
                    [
                        "title" => "Withdrawal",
                        "key" => "withdrawal",
                        "type" => "amount"
                    ],
                    [
                        "title" => "Type(Payment/Receipt)",
                        "key" => "type"
                    ]
                ],
                'actions' => [
                [
                    "title" => "Print/Export",
                    "icon" => "ri-printer-line",
                    "options" => [
                        [
                            "title" => "PDF",
                            "icon" => "ri-file-pdf-2-line",
                            "api" => [
                                "method" => "GET",
                                "url" => "/admin/reports/bankRecoReport/download/pdf",
                                "type" => "download"
                            ]
                        ],
                        [
                            "title" => "Excel",
                            "icon" => "ri-file-excel-2-line",
                            "api" => [
                                "method" => "GET",
                                "url" => "/admin/reports/bankRecoReport/download/excel",
                                "type" => "download"
                            ]
                        ]
                    ]
                ]
            ]
            ],
            "export" => [
                "enabled" => true,
                "filename" => "Member_Receipt_Report",
                "formats" => ["xlsx", "pdf"]
            ]
        ];
    }

    public function apply()
    {
        $this->schema = $this->getSchema();
        $report = $this->action('datasource:bankRecoReport', $this->pointer, $this->request);

        $bankAccountList = $this->action('datasource:viewBankAccountsList', $this->pointer, $this->request);
        
        $bankAccounts = collect($bankAccountList)
        ->pluck('bank_name', 'account_number')
        ->toArray();

        $this->data = $report;
        $this->meta = [
            'schema' => $this->schema
        ];
        $this->meta['schema']['table']['filter_by']['bank_account']['options'] = $bankAccounts;
    }
}
