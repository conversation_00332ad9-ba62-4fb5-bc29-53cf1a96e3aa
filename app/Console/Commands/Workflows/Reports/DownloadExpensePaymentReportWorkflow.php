<?php

namespace App\Console\Commands\Workflows\Reports;

use App\Console\Commands\Workflow;

class DownloadExpensePaymentReportWorkflow extends Workflow
{
    protected $signature = 'workflow:downloadExpensePaymentReport {input}';

    protected $description = 'Download Expense Payment Report in Excel or PDF';

    protected $rules = [];

    protected $rulesMessage = [];

    protected $headings = [
        'Payment Date',
        'Payment Number',
        'Mode',
        'Type',
        'Payment Of',
        'Payment Reference',
        'Vendor',
        'Paid Amount',
        'Write Off',
        'Status'
    ];

    protected $formatter = [
        'payment_date' => '',
        'payment_number' => '',
        'payment_mode' => '',
        'bill_type' => '',
        'invoice_number' => '',
        'transaction_reference' => '',
        'vendor_name' => '',
        'payment_amount' => '',
        'writeoff_amount' => '',
        'status' => ''
    ];

    protected $formatterByKeys = [];

    protected $mapper = [];

    public function apply()
    {
        $type = $this->input['type'];

        if(!$type || $type == '' || $type == ':type')
        {
            $this->status = 'error';
            $this->message = 'Please provide a valid type either excel or pdf';
            $this->statusCode = 400;
            $this->data = [];
        }
        else{
            $expensePayments = $this->action('datasource:expensePaymentReport', $this->pointer, $this->request);

            $this->data = [];

            // Format status for display
            foreach($expensePayments as $key => $payment)
            {
                $expensePayments[$key]['status'] = $this->formatStatus($payment['status']);
                $expensePayments[$key]['writeoff_amount'] = $this->formatWriteOffAmount($payment['writeoff_amount']);

            }
            if($type == 'excel')
            {
                // Calculate totals
                $total_payment_amount = 0;
                $total_writeoff_amount = 0;
                foreach ($expensePayments as $row) {
                    $total_payment_amount += isset($row['payment_amount']) ? (float)$row['payment_amount'] : 0;
                    $total_writeoff_amount += isset($row['writeoff_amount']) ? (float)$row['writeoff_amount'] : 0;
                }

                // Prepare total row
                $totalRow = [
                    'payment_date' => 'Total',
                    'payment_number' => '',
                    'payment_mode' => '',
                    'bill_type' => '',
                    'invoice_number' => '',
                    'transaction_reference' => '',
                    'vendor_name' => '',
                    'payment_amount' => ($total_payment_amount == 0 ? '0' : $total_payment_amount),
                    'writeoff_amount' => ($total_writeoff_amount == 0 ? '0' : $total_writeoff_amount),
                    'status' => '',
                ];

                // Append total row
                $expensePayments[] = $totalRow;

                $data = $this->hitCURLForGenerateCSV($expensePayments, $this->headings, 'expense_payment_');
                $this->data['url'] = $data['data'];
            }
            else{
                $data = $this->hitCURLForGeneratePDF($expensePayments, $this->headings, 'expense_payment');
                $this->data['url'] = $data['data'];
            }
        }
    }

    private function formatStatus($status)
    {
        $statusMapping = [
            'Y' => 'Cleared',
            'N' => 'Bounced',
            'P' => 'Submitted',
            'reversed' => 'Reversed'
        ];

        return $statusMapping[$status] ?? $status;
    }
    private function formatWriteOffAmount($writeoff_amount)
    {
       if($writeoff_amount == 0)
       {
           return '0';
       }
       return $writeoff_amount;
    }
}
