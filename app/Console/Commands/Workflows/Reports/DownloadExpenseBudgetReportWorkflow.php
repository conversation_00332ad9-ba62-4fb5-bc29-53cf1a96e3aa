<?php

namespace App\Console\Commands\Workflows\Reports;

use Illuminate\Console\Command;
use App\Console\Commands\Workflow;

class DownloadExpenseBudgetReportWorkflow extends Workflow
{
    protected $signature = 'workflow:downloadExpenseBudgetReport {input}';

    protected $description = 'Download Expense Budget Report Workflow';

    protected $rules = [
    ];

    protected $rulesMessage = [
    ];

    protected $formatter = [
        'expense_account' => '',
        'expense_ledger' => '',
        'budget' => '',
        'actual_expense' => '',
        'variance' => ''
    ];

    protected $formatterByKeys = [
        'id'
    ];

    protected $headings = [
        'Expense Account',
        'Expense Ledger',
        'Budget',
        'Actual Expense',
        'Variance'
    ];

    public function apply()
    {
        $type = $this->input['type'];

        if(!$type || $type == '' || $type == ':type')
        {
            $this->status = 'error';
            $this->message = 'Please provide a valid type either excel or pdf';
            $this->statusCode = 400;
            $this->data = [];
        }
        else
        {
            $expenseBudgetList = $this->action('datasource:expenseBudgetReport', $this->pointer, $this->request);
            // Format data to match screenshot headers
            $outputData = [];
            $budgetSum = 0;
            $actualExpenseSum = 0;
            $varianceSum = 0;
            
            foreach ($expenseBudgetList as $item) {
                // Clean and convert numbers
                $budget = floatval(str_replace(',', '', $item['budget']));
                $actualExpense = floatval(str_replace(',', '', $item['actual_expense']));
                $variance = floatval(str_replace(',', '', $item['variance']));
            
                $outputData[] = [
                    'Expense Account' => $item['expense_account'] ?? '',
                    'Expense Ledger' => $item['expense_ledger'] ?? '',
                    'Budget' => $item['budget'] ?? '',
                    'Actual Expense' => $item['actual_expense'] ?? '',
                    'Variance' => $item['variance'] ?? '',
                ];
            
                $budgetSum += $budget;
                $actualExpenseSum += $actualExpense;
                $varianceSum += $variance;
            }
            
            $totalRow = [
                'Expense Account' => 'Total',
                'Expense Ledger' => '',
                'Budget' => number_format($budgetSum, 2),
                'Actual Expense' => number_format($actualExpenseSum, 2),
                'Variance' => number_format($varianceSum, 2),
            ];
            
            $outputData[] = $totalRow;
            
            $this->data = [];

            if($type == 'excel')
            {
                $data = $this->hitCURLForGenerateCSV($outputData, $this->headings, 'expenseBudgetReport_');
                $this->data['url'] = $data['data'];
            }
            else{
                $data = $this->hitCURLForGeneratePDF($expenseBudgetList, $this->headings, 'expenseBudgetReport');
                $this->data['url'] = $data['data'];
            }
        }
    }
}
