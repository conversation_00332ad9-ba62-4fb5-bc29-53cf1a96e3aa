<?php

namespace App\Console\Commands\Workflows\Reports;

use App\Console\Commands\Workflow;

class IncidentalReceivableReportDownloadWorkflow extends Workflow
{
    protected $signature = 'workflow:incidentalReceivableReportDownload {input}';

    protected $description = 'Download Incidental Receivable Report';

    protected $rules = [
    ];

    protected $rulesMessage = [
    ];

    public function apply()
    {
        $type = $this->input['type'];

        if(!$type || $type == '' || $type == ':type')
        {
            $this->status = 'error';
            $this->message = 'Please provide a valid type either excel or pdf';
            $this->statusCode = 400;
            $this->data = [];
        }
        else
        {
            $incidentalData = $this->action('datasource:incidentalRecieableReport', $this->pointer, $this->request);
            
            // The datasource returns [detailRows, summaryTotals]
            $detailRows = $incidentalData[0] ?? [];
            $summaryTotals = $incidentalData[1][0] ?? [];
            
            if (empty($detailRows)) {
                $this->status = 'error';
                $this->message = 'No data found for the selected criteria';
                $this->statusCode = 404;
                $this->data = [];
                return;
            }

            // Get the first row to determine dynamic columns
            $firstRow = $detailRows[0];
            
            // Fixed columns that always appear first and last
            $fixedStart = ['building_unit_name', 'member_name'];
            $fixedEnd = ['total_due'];
            
            // Get dynamic columns (everything except fixed columns)
            $allKeys = array_keys($firstRow);
            $dynamicKeys = array_diff($allKeys, array_merge($fixedStart, $fixedEnd));
            
            // Build headers array with proper formatting
            $headers = [];
            
            // Add fixed start headers
            $headers['Building / Unit'] = 'Building / Unit';
            $headers['Primary Member'] = 'Primary Member';
            
            // Add dynamic headers with proper formatting
            foreach ($dynamicKeys as $key) {
                // Convert keys like "Major_Repair_Fund___Painting" to "Major Repair Fund - Painting"
                $formattedTitle = $this->formatColumnTitle($key);
                $headers[$formattedTitle] = $formattedTitle;
            }
            
            // Add fixed end headers
            $headers['Total Due'] = 'Total Due';
            
            // Format output data
            $outputData = [];
            foreach ($detailRows as $item) {
                $row = [];
                
                // Add fixed start columns
                $row['Building / Unit'] = $item['building_unit_name'] ?? '';
                $row['Primary Member'] = $item['member_name'] ?? '';
                
                // Add dynamic columns
                foreach ($dynamicKeys as $key) {
                    $formattedTitle = $this->formatColumnTitle($key);
                    $row[$formattedTitle] = $this->formatWriteOffAmount($item[$key] ?? 0);
                }
                
                // Add fixed end columns
                $row['Total Due'] = $this->formatWriteOffAmount($item['total_due'] ?? 0);
                
                $outputData[] = $row;
            }
            
            // Add Total row if we have summary data
            if (!empty($summaryTotals)) {
                $totalRow = [];
                
                // Fixed start columns for total row
                $totalRow['Building / Unit'] = 'Total';
                $totalRow['Primary Member'] = '';
                
                // Add dynamic totals
                foreach ($dynamicKeys as $key) {
                    $formattedTitle = $this->formatColumnTitle($key);
                    $summaryKey = $key . '_summary';
                    $totalRow[$formattedTitle] = $this->formatWriteOffAmount($summaryTotals[$summaryKey] ?? 0);
                }
                
                // Calculate total due sum
                $totalDueSum = round(array_sum(array_column($detailRows, 'total_due')), 2);
                $totalRow['Total Due'] = $totalDueSum;
                
                $outputData[] = $totalRow;
            }

            $this->data = [];

            if($type == 'excel')
            {
                $data = $this->hitCURLForGenerateCSV($outputData, $headers, 'incidentalReceivableReport');
                $this->data['url'] = $data['data'];
            }
            else{
                // For PDF, prepare data in the format expected by the PDF generator
                $finall = [
                    $outputData,
                    []  // Empty array for compatibility
                ];
                $data = $this->hitCURLForGeneratePDF($outputData, $headers, 'incidentalReceivableReport');
                $this->data['url'] = $data['data'];
            }
        }
    }
    
    /**
     * Format column titles from database keys to human-readable format
     * Example: "Major_Repair_Fund___Painting" becomes "Major Repair Fund - Painting"
     * Example: "Scrap_Sale_summary" becomes "Scrap Sale Summary"
     */
    private function formatColumnTitle($key)
    {
        // First handle triple underscores (___) by replacing with a special marker
        $formatted = str_replace('___', ' - ', $key);

        // Then replace remaining single underscores with spaces
        $formatted = str_replace('_', ' ', $formatted);

        // Capitalize each word
        $formatted = ucwords(strtolower($formatted));

        return trim($formatted);
    }

    private function formatWriteOffAmount($value)
    {
        if ($value == 0 || $value === '0') {
            return '0';
        }
        return round($value, 2);
    }
}
