<?php

namespace App\Console\Commands\Workflows\Reports;

use App\Console\Commands\Workflow;

class DownloadMembersInvoiceDetailReportWorkflow extends Workflow
{
    protected $signature = 'workflow:downloadMembersInvoiceDetailReport {input}';

    protected $description = 'Download Members Invoice Detail Report in Excel or PDF';

    protected $rules = [];

    protected $rulesMessage = [];

    protected $headings = [
        'Unit Name' => 'Unit Name',
        'Bill To' => 'Bill To',
        'GSTIN' => 'GSTIN',
        'Invoice Number' => 'Invoice Number',
        'Maintenance Fee' => 'Maintenance Fee',
        'Period' => 'Period',
        'Sinking Fund' => 'Sinking Fund',
        'Interest' => 'Interest',
        'Tax' => 'Tax',
        'Invoice Amount' => 'Invoice Amount',
        'P Arrears' => 'P Arrears',
        'I Arrears' => 'I Arrears',
        'Payable' => 'Payable',
        'Receipt' => 'Receipt',
        'Net Due' => 'Net Due',
        'Advance Credit' => 'Advance Credit'
    ];

    protected $formatter = [
        'unit_name' => '',
        'bill_to' => '',
        'gstin' => '',
        'invoice_number' => '',
        'maintenance_fee' => '',
        'period' => '',
        'sinking_fund' => '',
        'interest' => '',
        'tax' => '',
        'invoice_amount' => '',
        'p_arrears' => '',
        'i_arrears' => '',
        'payable' => '',
        'receipt' => '',
        'net_due' => '',
        'advance_credit' => ''
    ];

    protected $formatterByKeys = [];

    protected $mapper = [];

    public function apply()
    {
        $type = $this->input['type'];

        if(!$type || $type == '' || $type == ':type')
        {
            $this->status = 'error';
            $this->message = 'Please provide a valid type either excel or pdf';
            $this->statusCode = 400;
            $this->data = [];
        }
        else{
            $membersInvoiceDetail = $this->action('datasource:membersInvoiceDetailReport', $this->pointer, $this->request);

            // The datasource returns [detailRows, summaryTotals]
            $detailRows = $membersInvoiceDetail[0] ?? [];
            $summaryTotals = $membersInvoiceDetail[1][0] ?? [];

            $this->data = [];

            if($type == 'excel')
            {
                // Format data for Excel with proper formatting
                $outputData = [];
                foreach ($detailRows as $item) {
                    $outputData[] = [
                        'Unit Name' => $item['unit_name'] ?? '',
                        'Bill To' => $item['bill_to'] ?? '',
                        'GSTIN' => $item['gstin'] ?? '',
                        'Invoice Number' => $item['invoice_number'] ?? '',
                        'Maintenance Fee' => $this->formatWriteOffAmount($item['maintenance_fee'] ?? 0),
                        'Period' => $item['period'] ?? '',
                        'Sinking Fund' => $this->formatWriteOffAmount($item['sinking_fund'] ?? 0),
                        'Interest' => $this->formatWriteOffAmount($item['interest'] ?? 0),
                        'Tax' => $this->formatWriteOffAmount($item['tax'] ?? 0),
                        'Invoice Amount' => $this->formatWriteOffAmount($item['invoice_amount'] ?? 0),
                        'P Arrears' => $this->formatWriteOffAmount($item['p_arrears'] ?? 0),
                        'I Arrears' => $this->formatWriteOffAmount($item['i_arrears'] ?? 0),
                        'Payable' => $this->formatWriteOffAmount($item['payable'] ?? 0),
                        'Receipt' => $this->formatWriteOffAmount($item['receipt'] ?? 0),
                        'Net Due' => $this->formatWriteOffAmount($item['net_due'] ?? 0),
                        'Advance Credit' => $this->formatWriteOffAmount($item['advance_credit'] ?? 0)
                    ];
                }
                $sinking_fund = array_sum(array_column($detailRows, 'sinking_fund'));
                $interest = array_sum(array_column($detailRows, 'interest'));
                $tax = array_sum(array_column($detailRows, 'tax'));
                $invoice_amount = array_sum(array_column($detailRows, 'invoice_amount'));
                $p_arrears = array_sum(array_column($detailRows, 'p_arrears'));
                $i_arrears = array_sum(array_column($detailRows, 'i_arrears'));
                $payable = array_sum(array_column($detailRows, 'payable'));
                $receipt = array_sum(array_column($detailRows, 'receipt'));
                $net_due = array_sum(array_column($detailRows, 'net_due'));
                $advance_credit = array_sum(array_column($detailRows, 'advance_credit'));


                // Add Total row if we have summary data
                if (!empty($summaryTotals)) {
                    $outputData[] = [
                        'Unit Name' => 'Total',
                        'Bill To' => '',
                        'GSTIN' => '',
                        'Invoice Number' => '',
                        'Maintenance Fee' => '',
                        'Period' => '',
                        'Sinking Fund' => $this->formatWriteOffAmount($sinking_fund ?? 0),
                        'Interest' => $this->formatWriteOffAmount($interest ?? 0),
                        'Tax' => $this->formatWriteOffAmount($tax ?? 0),
                        'Invoice Amount' => $this->formatWriteOffAmount($invoice_amount ?? 0),
                        'P Arrears' => $this->formatWriteOffAmount($p_arrears ?? 0),
                        'I Arrears' => $this->formatWriteOffAmount($i_arrears ?? 0),
                        'Payable' => $this->formatWriteOffAmount($payable ?? 0),
                        'Receipt' => $this->formatWriteOffAmount($receipt ?? 0),
                        'Net Due' => $this->formatWriteOffAmount($net_due ?? 0),
                        'Advance Credit' => $this->formatWriteOffAmount($advance_credit ?? 0)
                    ];
                }

                $data = $this->hitCURLForGenerateCSV($outputData, $this->headings, 'members_invoice_detail_');
                $this->data['url'] = $data['data'];
            }
            else{
                $data = $this->hitCURLForGeneratePDF($membersInvoiceDetail, $this->headings, 'membersInvoiceDetailReport');
                $this->data['url'] = $data['data'];
            }
        }
    }

    private function formatWriteOffAmount($value)
    {
        if($value == 0)
       {
           return '0';
       }
       return round($value, 2);
    }
}
