<?php

namespace App\Console\Commands\Workflows\Reports;

use Illuminate\Console\Command;
use App\Console\Commands\Workflow;

class BankRecoReportDownloadWorkflow extends Workflow
{
    protected $signature = 'workflow:bankRecoReportDownload {input}';
    protected $description = 'Bank Reconciliation Report Download';

    protected $headings = [
        'Sr. No',
        'Reconciliation Status',
        'From Account',
        'Date',
        'Particulars/Narration',
        'Payment Mode',
        'Payment Reference',
        'Bank Date',
        'Deposit',
        'Withdrawal',
        'Type(Payment / Receipt)'
    ];

    protected $summaryHeadings = [
        'Deposit',
        'Withdrawal'
    ];

    public function apply()
    {
        try{
            $type = $this->input['type'];
            if (!$type || $type == '' || $type == ':type') {
                $this->status = 'error';
                $this->message = 'Please provide a valid type either excel or pdf';
                $this->statusCode = 400;
                $this->data = [];
                return;
            }

            $bankRecoReport = $this->action('datasource:bankRecoReport', $this->pointer, $this->request);
            
            $reportData = $bankRecoReport ?? [];
            $summaryData = $bankRecoReport ?? [];

            $formattedData = [];
            $srNo = 1;
            foreach ($reportData as $member) {
                $formattedData[] = [
                    'sr_no' => $srNo++,
                    'reconciliation_status' => $member['reconciliation_status'] ?? '',
                    'from_account' => $member['from_account'] ?? '',
                    'date' => $member['bank_date'] ?? '',
                    'particulars_narration' => $member['memo_desc'] ?? '',
                    'payment_mode' => $member['payment_mode'] ?? '',
                    'payment_reference' => $member['payment_reference'] ?? '',
                    'bank_date' => $member['bank_date'] ?? '',
                    'deposit' => $this->formatWriteOffAmount(   $member['deposit'] ?? ''),
                    'withdrawal' => $this->formatWriteOffAmount($member['withdrawal'] ?? ''),
                    'type' => $member['type'] ?? '',
                ];
            }

            // Add a blank row
            $blankRow = [
                'id' => '',
                'unit_name' => '',
                'member_name' => '',
                'unit_name' => '',
                'member_name' => '',
                'unit_name' => '',
                'member_name' => '',
                'withdrawal' => '',
                'deposit' => '',
            ];
            $formattedData[] = $blankRow;
            
            // Calculate total due_amount and add summary row for Excel
            $withdrawal = array_sum(array_map(function($item) {
                return is_numeric($item['withdrawal']) ? $item['withdrawal'] : 0;
            }, $formattedData));

            $deposit = array_sum(array_map(function($item) {
                return is_numeric($item['deposit']) ? $item['deposit'] : 0;
            }, $formattedData));
            $withdrawal = $this->formatWriteOffAmount($withdrawal);
            $deposit = $this->formatWriteOffAmount($deposit);
            
            $summaryRow = [
                'id' => 'Total',
                'unit_name' => '',
                'member_name' => '',
                'member_name1' => '',
                'member_name2' => '',
                'member_name3' => '',
                'member_name4' => '',
                'member_name5' => '',
                'withdrawal' => $withdrawal,
                'deposit' => $deposit,
            ];
            $formattedData[] = $summaryRow;


            $this->data = [];

            if ($type == 'excel') {
                $data = $this->hitCURLForGenerateCSV($formattedData, $this->headings, 'bankRecoReport_');
                $this->data['url'] = $data['data'];
            } else {
                // PDF will be implemented later
                $data = $this->hitCURLForGeneratePDF($formattedData, $this->headings, 'bankRecoReport');
                $this->data['url'] = $data['data'];
            }
        
        } catch(\Exception $e) {

        }
}
private function formatWriteOffAmount($writeoff_amount)
    {
       if($writeoff_amount == 0)
       {
           return '0';
       }
       return $writeoff_amount;
    }
}
