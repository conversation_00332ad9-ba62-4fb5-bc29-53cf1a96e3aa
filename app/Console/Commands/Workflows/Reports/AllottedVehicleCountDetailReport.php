<?php

namespace App\Console\Commands\Workflows\Reports;

use Illuminate\Console\Command;
use App\Console\Commands\Workflow;

// STEP 4: Create a workflow with same name specified in controller. Extend the Workflow class.
class AllottedVehicleCountDetailReport extends Workflow
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'workflow:allottedVehicleCountDetailReport {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Vehicle Count Detail Report';

    protected $rules = [];
    protected $rulesMessage = [];

    protected $formatterByKeys = ["id"];

    public function apply()
    {
        $allottedVehicleList = $this->action('datasource:allottedVehicleCount', $this->pointer, $this->request);
        $this->data = $allottedVehicleList;
        $this->meta['schema'] = $this->schema();
    }

    public function schema()
    {
        return $meta = [
            "table" => [
                "tableTitle" => "Vehicle Count",
                "actions" => [
                    [
                        "title" => "Export Report",
                        "icon" => "ri-export-line",
                        "options" => [
                            [
                                "title" => "Print",
                                "icon" => "ri-file-2-line",
                                "api" => [
                                    "method" => "GET",
                                    "url" => "/admin/parking-allotments/allottedVehicleCountDetailReport/download/pdf",
                                    "type" => "print"
                                ]
                            ],
                            [
                                "title" => "PDF",
                                "icon" => "ri-file-pdf-2-line",
                                "api" => [
                                    "method" => "GET",
                                    "url" => "/admin/parking-allotments/allottedVehicleCountDetailReport/download/pdf",
                                    "type" => "download"
                                ]
                            ],
                            [
                                "title" => "Excel",
                                "icon" => "ri-file-excel-2-line",
                                "api" => [
                                    "method" => "GET",
                                    "url" => "/admin/parking-allotments/allottedVehicleCountDetailReport/download/excel",
                                    "type" => "download"
                                ]
                            ]
                        ]
                    ]
                ],
                "fields" => [
                    "*"
                ],
                "columns" => [
                    [
                        "title" => "Building/Unit",
                        "key" => "building_unit"
                    ],
                    [
                        "title" => "Shaded Parking",
                        "key" => "shaded_parking"
                    ],
                    [
                        "title" => "Open Parking",
                        "key" => "open_parking"
                    ],
                    [
                        "title" => "2wheeler (Owner|Tenant)",
                        "key" => "two_wheeler_owner_tenant",
                    ],
                    [
                        "title" => "4wheeler (Owner|Tenant)",
                        "key" => "four_wheeler_owner_tenant",
                    ]
                ]
            ]
        ];
    }
}
