<?php

namespace App\Console\Commands\Workflows\Reports;

use Illuminate\Console\Command;
use App\Console\Commands\Workflow;

// STEP 4: Create a workflow with same name specified in controller. Extend the Workflow class.
class AllottedVehicleDetailReportWorkflow extends Workflow
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'workflow:allottedVehicleDetailReport {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Vehicle Detail List Report';

    protected $rules = [];
    protected $rulesMessage = [];

    protected $formatterByKeys = ["id"];

    public function apply()
    {
        $allottedVehicleList = $this->action('datasource:allottedVehicleList', $this->pointer, $this->request);
        $this->data = $allottedVehicleList;
        $this->meta['schema'] = $this->schema();
    }

    public function schema()
    {
        return $meta = [
            "table" => [
                "tableTitle" => "Vehicle Registration",
                "filter_by" => [
                    "occupied_by" => [
                        "title" => "Occupied By",
                        'select_single' => true,
                        "options" => [
                            "all" => "All",
                            "owner" => "Owner",
                            "tenant" => "Tenant",
                        ],
                        "default" => "all"
                    ],
                ],
                "actions" => [
                    [
                        "title" => "Export Report",
                        "icon" => "ri-export-line",
                        "options" => [
                            [
                                "title" => "Print",
                                "icon" => "ri-file-2-line",
                                "api" =>[
                                    "type" => "download",
                                    "url" => "/admin/parking-allotments/allottedVehicleDetailReportPrint/download/print",
                                    "method" => "GET",
                                ]
                            ],
                            [
                                "title" => "PDF",
                                "icon" => "ri-file-pdf-2-line",
                                "api" =>[
                                    "type" => "download",
                                    "url" => "/admin/parking-allotments/allottedVehicleDetailReportPrint/download/pdf",
                                    "method" => "GET",
                                ]
                            ],
                            [
                                "title" => "Excel",
                                "icon" => "ri-file-excel-2-line",
                                "api" =>[
                                    "type" => "download",
                                    "url" => "/admin/parking-allotments/allottedVehicleDetailReportPrint/download/excel",
                                    "method" => "GET",
                                ]
                            ]
                        ]
                    ]
                ],
                "fields" => [
                    "*"
                ],
                "columns" => [
                    [
                        "title" => "Building/Unit",
                        "key" => "building_unit"
                    ],
                    [
                        "title" => "Parking Unit",
                        "key" => "parking_unit"
                    ],
                    [
                        "title" => "Vehicle Occupied By",
                        "key" => "vehicle_occupied_by",
                    ],
                    [
                        "title" => "Vehicle Registered",
                        "key" => "vehicle_registration_number",
                    ],
                    [
                        "title" => "Vehicle Type",
                        "key" => "vehicle_type"
                    ],
                    [
                        "title" => "Vehicle Color",
                        "key" => "vehicle_colour"
                    ],
                    [
                        "title" => "Vehicle Model",
                        "key" => "vehicle_model_number"
                    ]
                ]
            ]
        ];
    }
}
