<?php

namespace App\Console\Commands\Workflows\Reports;

use App\Console\Commands\Workflow;

class ExpenseReportDownloadWorkflow extends Workflow
{
    protected $signature = 'workflow:expenseReportDownload {input}';
    protected $description = 'Expense Report Download Workflow';

    protected $headings = [
        'Sr.No' => 'Sr.No',
        'Bill Date' => 'Bill Date', 
        'Vendor Name' => 'Vendor Name',
        'GST Number' => 'GST Number',
        'Bill No' => 'Bill No',
        'Purchase Type' => 'Purchase Type',
        'Particular Expense Head' => 'Particular Expense Head',
        'Particular' => 'Particular',
        'Gross Bill Amount' => 'Gross Bill Amount',
        'Net Bill Amount' => 'Net Bill Amount',
        'GST Rate' => 'GST Rate',
        'GST Amount' => 'GST Amount',
        'TDS Amount' => 'TDS Amount',
        'Writeoff Amount' => 'Writeoff Amount',
        'Paid Date' => 'Paid Date',
        'Paid Amount' => 'Paid Amount',
        'Balance Due' => 'Balance Due'
    ];

    public function apply()
    {
        $type = $this->input['type'];

        if(!$type || $type == '' || $type == ':type') {
            $this->status = 'error';
            $this->message = 'Please provide a valid type either excel or pdf';
            $this->statusCode = 400;
            $this->data = [];
        } else {
            // Call the expenseReport datasource and apply same processing as ExpenseReportWorkflow
            $report = $this->action('datasource:expenseReport', $this->pointer, $this->request);
            
            // Apply the same processing logic as ExpenseReportWorkflow
            foreach ($report as &$record) {
                // Convert all values to float before calculation
                $billAmount = floatval($record['vendor_bill_amount']);
                $paidAmount = floatval($record['paid_amount'] ?? 0);
                $tdsAmount = floatval($record['vendor_bill_tds'] ?? 0);
                $writeoffAmount = floatval($record['vendor_bill_writeoff'] ?? 0);

                // Balance due = Bill Amount - (Paid Amount + TDS + Writeoff)
                // $record['balance_due'] = $billAmount - ($paidAmount + $tdsAmount + $writeoffAmount);
                $record['balance_due'] = $billAmount - ($paidAmount);

                // Format amounts to 2 decimal places
                $record['balance_due'] = round($record['balance_due'], 2);
                $record['vendor_bill_amount'] = round($billAmount, 2);
                $record['paid_amount'] = round($paidAmount, 2);
                $record['vendor_bill_tds'] = round($tdsAmount, 2);
                $record['vendor_bill_writeoff'] = round($writeoffAmount, 2);
            }

            // Format data to match the screenshot headers
            $outputData = [];
            $srNo = 1;
            foreach ($report as $item) {
                $outputData[] = [
                    'Sr.No' => $srNo++,
                    'Bill Date' => $item['vendor_bill_date'] ?? '',
                    'Vendor Name' => $item['vendor_name_report'] ?? '',
                    'GST Number' => $item['vendor_service_regn'] ?? '',
                    'Bill No' => $item['vendor_bill_num'] ?? '',
                    'Purchase Type' => $item['vendor_bill_type_purchase'] ?? '',
                    'Particular Expense Head' => $item['et_name'] ?? '',
                    'Particular' => $item['particulars'] ?? '',
                    'Gross Bill Amount' => $item['gross_bill_amount'] ?? '',
                    'Net Bill Amount' => $item['net_bill_amount'] ?? '',
                    'GST Rate' => $item['particulars_igst_rate'] ?? '',
                    'GST Amount' => $item['particulars_igst_amount'] ?? '',
                    'TDS Amount' => $this->formatWriteOffAmount($item['vendor_bill_tds'] ?? 0),
                    'Writeoff Amount' => $this->formatWriteOffAmount($item['vendor_bill_writeoff'] ?? 0),
                    'Paid Date' => $item['paid_date'] ?? '',
                    'Paid Amount' => $this->formatWriteOffAmount($item['paid_amount'] ?? 0),
                    'Balance Due' => $this->formatWriteOffAmount($item['balance_due'] ?? 0)
                ];
            }

            // Calculate totals for columns from 'Gross Bill Amount' to 'Balance Due', excluding 'Paid Date'
            $totalGrossBillAmount = 0;
            $totalNetBillAmount = 0;
            $totalGstRate = 0;
            $totalGstAmount = 0;
            $totalTdsAmount = 0;
            $totalWriteoffAmount = 0;
            $totalPaidAmount = 0;
            $totalBalanceDue = 0;
            $count = count($outputData);
            foreach ($outputData as $row) {
                $totalGrossBillAmount += floatval($row['Gross Bill Amount'] ?? 0);
                $totalNetBillAmount += floatval($row['Net Bill Amount'] ?? 0);
                $totalGstRate += floatval($row['GST Rate'] ?? 0);
                $totalGstAmount += floatval($row['GST Amount'] ?? 0);
                $totalTdsAmount += floatval($row['TDS Amount'] ?? 0);
                $totalWriteoffAmount += floatval($row['Writeoff Amount'] ?? 0);
                $totalPaidAmount += floatval($row['Paid Amount'] ?? 0);
                $totalBalanceDue += floatval($row['Balance Due'] ?? 0);
            }
            // Average GST Rate if needed
            $avgGstRate = $count > 0 ? ($totalGstRate / $count) : 0;
            $outputData[] = [
                'Sr.No' => '',
                'Bill Date' => '',
                'Vendor Name' => 'Total',
                'GST Number' => '',
                'Bill No' => '',
                'Purchase Type' => '',
                'Particular Expense Head' => '',
                'Particular' => '',
                'Gross Bill Amount' => $this->formatWriteOffAmount($totalGrossBillAmount),
                'Net Bill Amount' => $this->formatWriteOffAmount($totalNetBillAmount),
                'GST Rate' => $this->formatWriteOffAmount($avgGstRate),
                'GST Amount' => $this->formatWriteOffAmount($totalGstAmount),
                'TDS Amount' => $this->formatWriteOffAmount($totalTdsAmount),
                'Writeoff Amount' => $this->formatWriteOffAmount($totalWriteoffAmount),
                'Paid Date' => '', // Exclude from sum
                'Paid Amount' => $this->formatWriteOffAmount($totalPaidAmount),
                'Balance Due' => $this->formatWriteOffAmount($totalBalanceDue)
            ];

            $this->data = [];

            if($type == 'excel') {
                $data = $this->hitCURLForGenerateCSV($outputData, $this->headings, 'expenseReport_');
                $this->data['url'] = $data['data'];
            }
            // Note: PDF generation not required as per user request
        }
    }

    private function formatWriteOffAmount($writeoff_amount)
    {
        if($writeoff_amount == 0 || $writeoff_amount === '0')
        {
            return '0';
        }
        return round($writeoff_amount, 2);
    }
}
