<?php

namespace App\Console\Commands\Workflows\Reports;

use Illuminate\Console\Command;
use App\Console\Commands\Workflow;

class NonMembersReceivableReportDownloadWorkflow extends Workflow
{
    protected $signature = 'workflow:nonMembersReceivableReportDownload {input}';

    protected $description = 'Download Non-Members Receivable Report Workflow';

    protected $rules = [
    ];

    protected $rulesMessage = [
    ];



    protected $headings = [
        'Name' => 'Name',
        'Payment Dues' => 'Payment Dues',
        'Credit Balance' => 'Credit Balance',
        'Ledger Balance' => 'Ledger Balance'
    ];

    public function apply()
    {
        $type = $this->input['type'];

        if(!$type || $type == '' || $type == ':type')
        {
            $this->status = 'error';
            $this->message = 'Please provide a valid type either excel or pdf';
            $this->statusCode = 400;
            $this->data = [];
        }
        else
        {
            $nonMembersReceivableData = $this->action('datasource:nonMembersReceivableReport', $this->pointer, $this->request);

            // Since data comes as flat array, we need to process it and create summary
            $detailRows = $nonMembersReceivableData;

            // Calculate totals from the data
            $totalPaymentDues = array_sum(array_column($detailRows, 'due_amount'));
            $totalCreditBalance = array_sum(array_column($detailRows, 'cr_bal'));
            $totalLedgerBalance = array_sum(array_column($detailRows, 'ledger_bal'));

            // Create summary array
            $summaryTotals = [
                'total_payment_dues' => $totalPaymentDues,
                'total_credit_balance' => $totalCreditBalance,
                'total_ledger_balance' => $totalLedgerBalance
            ];

            // Format data for Excel output
            $outputData = [];
            foreach ($detailRows as $item) {
                $outputData[] = [
                    'Name' => $item['full_name'] ?? '',
                    'Payment Dues' => $this->formatWriteOffAmount(round(floatval($item['due_amount'] ?? 0), 2)),
                    'Credit Balance' => $this->formatWriteOffAmount(round(floatval($item['cr_bal'] ?? 0), 2)),
                    'Ledger Balance' => $this->formatWriteOffAmount(round(floatval($item['ledger_bal'] ?? 0), 2)),
                ];
            }

            // Add Total row
            $outputData[] = [
                'Name' => 'Total',
                'Payment Dues' =>   $this->formatWriteOffAmount(round($totalPaymentDues, 2)),
                'Credit Balance' => $this->formatWriteOffAmount(round($totalCreditBalance, 2)),
                'Ledger Balance' => $this->formatWriteOffAmount(round($totalLedgerBalance, 2)),
            ];

            // Prepare data in the format expected by PDF blade (for compatibility)
            $formattedDataForPDF = [
                $detailRows,  // Original detail rows
                [$summaryTotals]  // Summary totals in expected format
            ];

            $this->data = [];

            if($type == 'excel')
            {
                $data = $this->hitCURLForGenerateCSV($outputData, $this->headings, 'nonMembersReceivableReport_');
                $this->data['url'] = $data['data'];
            }
            else{
                $data = $this->hitCURLForGeneratePDF($formattedDataForPDF, $this->headings, 'nonMembersReceivableReport');
                $this->data['url'] = $data['data'];
            }
        }
    }
    private function formatWriteOffAmount($writeoff_amount)
    {
       if($writeoff_amount == 0)
       {
           return '0';
       }
       return $writeoff_amount;
    }
}
