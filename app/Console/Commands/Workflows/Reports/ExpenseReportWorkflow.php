<?php

namespace App\Console\Commands\Workflows\Reports;

use App\Console\Commands\Workflow;

class ExpenseReportWorkflow extends Workflow
{
    protected $signature = 'workflow:expenseReport {input}';
    protected $description = 'Expense Report Workflow';
    protected $schema = [];

    public function getSchema()
    {
        return [
            "table" => [
                "tableTitle" => "Expense Report",
                // "is_searchable" => true,
                // "select_by" => [
                //     "vendor_name" => "Vendor Name",
                //     "vendor_bill_num" => "Bill Number",
                //     "et_name" => "Expense Head"
                // ],
                "fields" => [
                    "*"
                ],
                "extraFilters" => [
                    "transaction_date" => [
                        "title" => "Select Date Range",
                        "type" => "daterange",
                        "minDateTime" => "financialYearStart",
                        "maxDateTime" => "financialYearEnd",
                    ]
                ],
                "filter_by" => [
                    "vendor" => [
                        "title" => "Vendor",
                        "options" => [
                            'static vendor' => "Static Vendor"
                        ],
                    ]
                    // ]
                ],
                "columns" => [
                    [
                        "title" => "Sr No",
                        "key" => "id",
                        "type" => "number"
                    ],
                    [
                        "title" => "Bill Date",
                        "key" => "vendor_bill_date",
                        // "type" => "date"
                    ],
                    [
                        "title" => "Vendor Name",
                        "key" => "vendor_name_report"
                    ],
                    [
                        "title" => "GST Number",
                        "key" => "vendor_service_regn"
                    ],
                    [
                        "title" => "Bill No",
                        "key" => "vendor_bill_num"
                    ],
                    [
                        "title" => "Purchase Type",
                        "key" => "vendor_bill_type_purchase"
                    ],
                    [
                        "title" => "Particular Expense Head",
                        "key" => "et_name"
                    ],
                    [
                        "title" => "Particular",
                        "key" => "particulars"
                    ],
                    [
                        "title" => "Gross Bill Amount",
                        "key" => "gross_bill_amount",
                        "type" => "amount"
                    ],
                    [
                        "title" => "Net Bill Amount",
                        "key" => "net_bill_amount",
                        "type" => "amount"
                    ],
                    [
                        "title" => "GST Rate",
                        "key" => "particulars_igst_rate",
                        "type" => "number"
                    ],
                    [
                        "title" => "GST Amount",
                        "key" => "particulars_igst_amount",
                        "type" => "amount"
                    ],
                    [
                        "title" => "TDS Amount",
                        "key" => "vendor_bill_tds",
                        "type" => "amount"
                    ],
                    [
                        "title" => "Writeoff Amount",
                        "key" => "vendor_bill_writeoff",
                        "type" => "amount"
                    ],
                    [
                        "title" => "Paid Date",
                        "key" => "paid_date",
                        "type" => "date"
                    ],
                    [
                        "title" => "Paid Amount",
                        "key" => "paid_amount",
                        "type" => "amount"
                    ],
                    [
                        "title" => "Balance Due",
                        "key" => "balance_due",
                        "type" => "amount"
                    ]
                ],
                // "summary" => [
                //     [
                //         "type" => "sum",
                //         "columns" => [
                //             "gross_bill_amount",
                //             "net_bill_amount",
                //             "particulars_igst_amount",
                //             "vendor_bill_tds",
                //             "vendor_bill_writeoff",
                //             "paid_amount",
                //             "balance_due"
                //         ]
                //     ]
                // ]
                'actions' => [
                    [
                        "title" => "Export",
                        "icon" => "ri-printer-line",
                        "options" => [
                            [
                                "title" => "Excel",
                                "icon" => "ri-file-excel-2-line",
                                "api" => [
                                    "method" => "GET",
                                    "url" => "/admin/reports/expenseReport/download/excel",
                                    "type" => "download"
                                ]
                            ]
                        ]
                    ]
                ]
            ],

            "export" => [
                "enabled" => true,
                "filename" => "Expense_Report",
                "formats" => ["xlsx", "pdf"]
            ]
        ];
    }

    public function apply()
    {
        $this->schema = $this->getSchema();
        $report = $this->action('datasource:expenseReport', $this->pointer, $this->request);
        foreach ($report as &$record) {
            // Convert all values to float before calculation
            $billAmount = floatval($record['vendor_bill_amount']);
            $paidAmount = floatval($record['paid_amount'] ?? 0);
            $tdsAmount = floatval($record['vendor_bill_tds'] ?? 0);
            $writeoffAmount = floatval($record['vendor_bill_writeoff'] ?? 0);
            
            // Balance due = Bill Amount - (Paid Amount + TDS + Writeoff)
            // $record['balance_due'] = $billAmount - ($paidAmount + $tdsAmount + $writeoffAmount);
            $record['balance_due'] = $billAmount - ($paidAmount);
            
            // Format amounts to 2 decimal places
            $record['balance_due'] = round($record['balance_due'], 2);
            $record['vendor_bill_amount'] = round($billAmount, 2);
            $record['paid_amount'] = round($paidAmount, 2);
            $record['vendor_bill_tds'] = round($tdsAmount, 2);
            $record['vendor_bill_writeoff'] = round($writeoffAmount, 2);
        }
        
        
        $this->request['per_page'] = 100;
        $viewVendor = $this->action('datasource:viewVendor', $this->pointer, $this->request);
        $optionArray = [];
        
        foreach ($viewVendor as $vendor) {
            $vendorName = $vendor['vendor_name'];
            $optionArray[$vendorName] = $vendorName;
        }

        foreach ($report as $key => $value) {
            $report[$key]['id'] = $key + 1;
        }
        $this->data = $report;

        $this->meta = [
            'schema' => $this->schema
        ];
        $this->meta['schema']['table']['filter_by']['vendor']['options'] = $optionArray;
        // select single = true
        $this->meta['schema']['table']['filter_by']['vendor']['select_single'] = true;

    }
}
