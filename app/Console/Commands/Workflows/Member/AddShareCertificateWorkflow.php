<?php

namespace App\Console\Commands\Workflows\Member;

use App\Console\Commands\Workflow;

class AddShareCertificateWorkflow extends Workflow
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'workflow:addShareCertificate {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Add Share Certificate Workflow';

    /**
     * Execute the workflow.
     */
    public function apply()
    {
        // Validation logic can be added here
        $result = $this->action('datasource:addShareCertificate', $this->pointer, $this->input);
        $this->data = $result;
    }
}
