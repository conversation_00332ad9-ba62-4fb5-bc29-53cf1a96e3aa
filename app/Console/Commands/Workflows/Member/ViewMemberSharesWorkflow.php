<?php

namespace App\Console\Commands\Workflows\Member;

use App\Console\Commands\Workflow;

class ViewMemberSharesWorkflow extends Workflow
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'workflow:viewMemberShares {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'View Member Shares Workflow';

    /**
     * Execute the workflow.
     */
    public function apply()
    {
        $id = $this->input['id'] ?? null;
        if (!$id) {
            $this->status = 'error';
            $this->statusCode = '404';
            $this->message = 'ID is required';
            $this->data = [];
            return;
        }
        $result = $this->action('datasource:viewMemberShares', $this->pointer, $this->input);
        $this->data = $result;
    }
}
