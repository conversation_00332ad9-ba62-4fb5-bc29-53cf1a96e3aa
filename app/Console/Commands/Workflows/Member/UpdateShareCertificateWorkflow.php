<?php

namespace App\Console\Commands\Workflows\Member;

use App\Console\Commands\Workflow;

class UpdateShareCertificateWorkflow extends Workflow
{
    /**
     * Validation rules for updating a share certificate
     */
    protected $rules = [
        'certificate_no' => 'required|string|max:255',
        'member_reg_no' => 'required|string|max:255',
        'unit_id' => 'required|integer|min:1',
        'admission_date' => 'nullable|date',
        'entrance_fee' => 'required|numeric|min:0',
        'full_name' => 'required|string|max:255',
        'address' => 'nullable|string|max:1000',
        'occupation' => 'nullable|string|max:255',
        'age_on_admission' => 'nullable|integer|min:0',
        'nominee_name' => 'nullable|string|max:255',
        'nominee_address' => 'nullable|string|max:1000',
        'ceasing_reason' => 'nullable|string|max:255',
        'no_of_shares' => 'required|integer|min:1',
        'share_series_start' => 'required|integer|min:1',
        'share_series_end' => 'required|integer|min:1',
        'share_value' => 'required|numeric|min:0',
        'cancel_date' => 'nullable|date',
        // Nominee details array
        'nominees' => 'nullable|array',
        'nominees.*.nominee_name' => 'required_with:nominees|string|max:255',
        'nominees.*.percentage' => 'required_with:nominees|numeric|min:0|max:100',
        'nominees.*.address' => 'nullable|string|max:1000',
        'nominees.*.is_minor' => 'nullable|boolean',
        'nominees.*.guardian_name' => 'nullable|string|max:255',
        'nominees.*.guardian_address' => 'nullable|string|max:1000',
        'nominees.*.relation_with_minor' => 'nullable|string|max:255',
    ];

    protected $rulesMessage = [
        'certificate_no.required' => 'Certificate Number is required',
        'member_reg_no.required' => 'Member Registration Number is required',
        'unit_id.required' => 'Unit ID is required',
        'entrance_fee.required' => 'Entrance Fee is required',
        'full_name.required' => 'Full Name is required',
        'no_of_shares.required' => 'Number of Shares is required',
        'share_series_start.required' => 'Share Series From is required',
        'share_series_end.required' => 'Share Series To is required',
        'share_value.required' => 'Share Value is required',
        // Nominee messages
        'nominees.*.nominee_name.required_with' => 'Nominee Name is required for each nominee',
        'nominees.*.percentage.required_with' => 'Nominee Percentage is required for each nominee',
        'nominees.*.percentage.numeric' => 'Nominee Percentage must be a number',
        'nominees.*.percentage.min' => 'Nominee Percentage must be at least 0',
        'nominees.*.percentage.max' => 'Nominee Percentage must not exceed 100',
    ];
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'workflow:updateShareCertificate {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Update Share Certificate Workflow';

    /**
     * Execute the workflow.
     */
    public function apply()
    {   
        $result = $this->action('datasource:updateShareCertificate', $this->pointer, $this->request);
        $this->data = $result;
    }
}
