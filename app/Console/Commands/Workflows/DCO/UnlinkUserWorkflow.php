<?php

namespace App\Console\Commands\Workflows\DCO;

use App\Console\Commands\Workflow;

class UnlinkUserWorkflow extends Workflow
{
    protected $signature = 'workflow:unlinkUser {input}';
    protected $description = 'Unlink a user from a member';

    public function apply()
    {
        $result = $this->action('datasource:unlinkUser', $this->pointer, $this->request);
        $this->data = $result;
    }
} 