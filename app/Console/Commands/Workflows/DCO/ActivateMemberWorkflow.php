<?php

namespace App\Console\Commands\Workflows\DCO;

use Illuminate\Console\Command;
use App\Console\Commands\Workflow;

class ActivateMemberWorkflow extends Workflow
{
    protected $signature = 'workflow:activateMember {input}';

    protected $description = 'Activate Member Workflow';

    protected $rules = [
        'member_id' => 'required|integer|min:1'
    ];

    protected $rulesMessage = [
        'member_id.required' => 'Member ID is required',
        'member_id.integer' => 'Member ID must be a valid integer',
        'member_id.min' => 'Member ID must be greater than 0'
    ];

    public function apply()
    {
        $member_id = $this->input['member_id'];

        if (!$member_id || $member_id == '' || $member_id == ':member_id') {
            $this->status = 'error';
            $this->message = 'Please provide a valid member id';
            $this->statusCode = 400;
            $this->data = [];
        } else {
            $this->action('datasource:activateMember', $this->pointer, $this->request);
        }
    }
}
