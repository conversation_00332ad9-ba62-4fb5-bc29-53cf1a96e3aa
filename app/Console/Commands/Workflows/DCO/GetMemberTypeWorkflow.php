<?php

namespace App\Console\Commands\Workflows\DCO;

use Illuminate\Console\Command;
use App\Console\Commands\Workflow;

class GetMemberTypeWorkflow extends Workflow
{
    protected $signature = 'workflow:getMemberType {input}';

    protected $description = 'Get Member Type';

    public function apply()
    {
        $company_id = $this->input['company_id'];

            $allotteesDetails = $this->action('datasource:GetPrimaryMember', $this->pointer, $this->request);
            $this->data = $allotteesDetails;
    }
}