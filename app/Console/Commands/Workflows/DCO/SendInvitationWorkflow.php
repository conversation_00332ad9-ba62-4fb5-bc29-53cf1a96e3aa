<?php

namespace App\Console\Commands\Workflows\DCO;

use Illuminate\Console\Command;
use App\Console\Commands\Workflow;

class SendInvitationWorkflow extends Workflow
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'workflow:sendInvitation {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Send Invitation to Member Workflow';

    /**
     * Execute the console command.
     */
    public function apply()
    {
        $id = $this->input['id'];

        if(!$id || $id == '' || $id == ':id')
        {
            $this->status = 'error';
            $this->message = 'Please provide a valid member id';
            $this->statusCode = 400;
            $this->data = [];
        }
        else{
            $invitationResult = $this->action('datasource:sendInvitation', $this->pointer, $this->request);
        }
    }
}
