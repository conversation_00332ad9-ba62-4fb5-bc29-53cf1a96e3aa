<?php

namespace App\Console\Commands\Workflows\DCO;

use Illuminate\Console\Command;
use App\Console\Commands\Workflow;
use Illuminate\Support\Facades\Route;

class unitWiseMembersListWorkflow extends Workflow
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'workflow:unitWiseMembersList {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Unit Wise Members List';

    protected $formatter = [
        "id" => "",
        "member_id" => "",
        "fk_unit_id" => "",
        "salute" => "",
        "member_name" => "",
        "member_email_id" => "",
        "member_mobile_number" => "",
        "member_effective_date" => "",
        "member_type_name" => "",
        "member_intercom" => "",
        "member_status" => "",
        "status" => "",
        "soc_building_name" => "",
        "unit_flat_number" => "",
        "building_unit" => "",
        "approved" => "",
        "user_id" => ""
    ];

    protected $formatterByKeys = ["id"];

    /**
     * Execute the console command.
     */
    public function apply()
    {
        $allottees = $this->action('datasource:unitWiseMembersList', $this->pointer, $this->request);
        // $this->data = $this->format($allottees);
        $this->data = $allottees;
    }
}
