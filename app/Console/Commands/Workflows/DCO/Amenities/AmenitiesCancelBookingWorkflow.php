<?php

namespace App\Console\Commands\Workflows\DCO\Amenities;

use App\Console\Commands\Workflow;

class AmenitiesCancelBookingWorkflow extends Workflow
{
    protected $signature = 'workflow:amenitiesCancelBooking {input}';
    protected $description = 'Workflow for Cancel Booking';

    protected $rules = [
        'booking_id' => 'required',
        'status' => 'required',
    ];
    protected $rulesMessages = [
        'booking_id.required' => 'Booking ID is required.',
        'status.required' => 'Status is required.',
    ];

    public function apply()
    {
        $result = $this->action('datasource:amenitiesCancelBooking', $this->pointer, $this->input);
        $this->data = $result;
    }
}
