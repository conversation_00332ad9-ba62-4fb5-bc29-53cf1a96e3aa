<?php

namespace App\Console\Commands\Workflows\DCO\Amenities;

use Illuminate\Console\Command;
use App\Console\Commands\Workflow;

class AmenitiesBookWorkflow extends Workflow
{
    protected $signature = 'workflow:amenitiesBook {input}';

    protected $description = 'Workflow for Amenities Book';

    protected $rules = [
        'amenity_id' => 'required|integer',
        'user_id' => 'required|integer',
        'start_time' => 'required|date_format:H:i:s',
        'end_time' => 'required|date_format:H:i:s|after:start_time',
        // Custom rules for open day and slot vacancy are enforced in the DataSource logic
    ];
    public function apply()
    {
        if (!$this->input || $this->input == '' || $this->input == ':input') {
            $this->status = 'error';
            $this->message = 'Please provide a valid Amenity to book';
            $this->statusCode = 400;
            $this->data = [];
        }
        else{
            $result = $this->action('datasource:amenitiesBook', $this->pointer, $this->input);
        }
    }
}