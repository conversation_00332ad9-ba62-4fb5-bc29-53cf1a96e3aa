<?php

namespace App\Console\Commands\Workflows\DCO\Amenities;

use Illuminate\Console\Command;
use App\Console\Commands\Workflow;

class AmenitiesGetByIdWorkflow extends Workflow
{
    protected $signature = 'workflow:amenitiesGetById {input}';
    protected $description = 'Workflow for Get Amenity By ID';

    public function apply()
    {
        if (!$this->input || $this->input == '' || $this->input == ':input') {
            $this->status = 'error';
            $this->message = 'Please provide a valid Amenity ID';
            $this->statusCode = 400;
            $this->data = [];
        }
        else{
            $result = $this->action('datasource:amenitiesGetById', $this->pointer, $this->input);
        }
    }
}
