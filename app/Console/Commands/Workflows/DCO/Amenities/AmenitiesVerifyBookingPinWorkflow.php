<?php

namespace App\Console\Commands\Workflows\DCO\Amenities;

use App\Console\Commands\Workflow;

class AmenitiesVerifyBookingPinWorkflow extends Workflow
{
    protected $signature = 'workflow:amenitiesVerifyBookingPin {input}';
    protected $description = 'Workflow for Verifying Booking Pin';

    protected $rules = [
        'booking_id' => 'required',
        'pin' => 'required',
    ];

    protected $rulesMessages = [
        'booking_id.required' => 'Booking ID is required.',
        'pin.required' => 'Pin is required.',
    ];

    public function apply()
    {
        $result = $this->action('datasource:amenitiesVerifyBookingPin', $this->pointer, $this->input);
        $this->data = $result;
    }
}
