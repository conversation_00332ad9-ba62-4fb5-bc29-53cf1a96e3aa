<?php

namespace App\Console\Commands\Workflows\DCO\Amenities;

use App\Console\Commands\Workflow;

class AmenitiesGetUserAllBookingsWorkflow extends Workflow
{
    protected $signature = 'workflow:amenitiesGetUserAllBookings {input}';
    protected $description = 'Workflow for Get All Bookings by User';

    protected $rules = [
        'user_id' => 'required',
        'status' => 'nullable|in:cancelled,booked,completed',
    ];
    protected $rulesMessages = [
        'user_id.required' => 'User ID is required.',
        'status.in' => 'Status must be cancelled, booked or completed.',
    ];

    public function apply()
    {
        $result = $this->action('datasource:amenitiesGetUserAllBookings', $this->pointer, $this->input);
        $this->data = $result;
    }
}
