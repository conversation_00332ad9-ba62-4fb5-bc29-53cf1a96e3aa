<?php

namespace App\Console\Commands\Workflows\DCO\Amenities;

use Illuminate\Console\Command;
use App\Console\Commands\Workflow;

class AmenitiesDeleteWorkflow extends Workflow
{
    protected $signature = 'workflow:amenitiesDelete {input}';
    protected $description = 'Workflow for Delete Amenity';

    public function apply()
    {
        if (!$this->input || $this->input == '' || $this->input == ':input') {
            $this->status = 'error';
            $this->message = 'Please provide a valid Amenity ID to delete';
            $this->statusCode = 400;
            $this->data = [];
        }
        else{
            $result = $this->action('datasource:amenitiesDelete', $this->pointer, $this->input);
        }
    }
}
