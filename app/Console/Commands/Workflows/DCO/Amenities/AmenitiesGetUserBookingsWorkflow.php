<?php

namespace App\Console\Commands\Workflows\DCO\Amenities;

use App\Console\Commands\Workflow;

class AmenitiesGetUserBookingsWorkflow extends Workflow
{
    protected $signature = 'workflow:amenitiesGetUserBookings {input}';
    protected $description = 'Workflow for Get Amenity Bookings by User';

    protected $rules = [
        'amenity_id' => 'required',
        'user_id' => 'required',
        'status' => 'required|in:cancelled,booked,completed',
    ];
    protected $rulesMessages = [
        'amenity_id.required' => 'Amenity ID is required.',
        'user_id.required' => 'User ID is required.',
        'status.required' => 'Status is required.',
        'status.in' => 'Status must be cancelled, booked or completed.',
    ];

    public function apply()
    {
        $result = $this->action('datasource:amenitiesGetUserBookings', $this->pointer, $this->input);
        $this->data = $result;
    }
}
