<?php

namespace App\Console\Commands\Workflows\DCO\Amenities;

use Illuminate\Console\Command;
use App\Console\Commands\Workflow;

class AmenitiesUpdateWorkflow extends Workflow
{
    protected $signature = 'workflow:amenitiesUpdate {input}';
    protected $description = 'Workflow for Update Amenity';

    protected $rules = [
        'soc_amen_name' => 'required|string',
        'status' => 'nullable|boolean',
        'chargeable' => 'required|boolean',
        'is_multi_subscriber' => 'nullable|boolean',
        'is_recurring' => 'nullable|boolean',
        'capacity' => 'nullable|integer|min:1',
        'charge' => 'nullable|numeric|min:0',
        'deposit_amount' => 'nullable|numeric|min:0',
        'slot_duration_mins' => 'nullable|integer|min:1',
        'recurrence_type' => 'nullable|in:daily,weekly,monthly,one_time,yearly',
        'recurrence_value' => 'nullable|integer|min:1',
        'daily_start_time' => 'nullable|date_format:H:i:s',
        'daily_end_time' => 'nullable|date_format:H:i:s|after:daily_start_time',
        'active_days' => 'nullable|array',
        'active_days.*' => 'in:Monday,Tuesday,Wednesday,Thursday,Friday,Saturday,Sunday',
        'is_active' => 'nullable|boolean',
        'image' => 'nullable|string',
        'message' => 'nullable|string',
    ];

    protected $rulesMessage = [
        'soc_amen_name.required' => 'Amenity Name is required.',
        'soc_amen_name.string' => 'Amenity Name must be a string.',
        'status.boolean' => 'Status must be true or false.',
        'chargeable.required' => 'Chargeable is required.',
        'chargeable.boolean' => 'Chargeable must be true or false.',
        'is_multi_subscriber.boolean' => 'Is multi subscriber must be true or false.',
        'is_recurring.boolean' => 'Is recurring must be true or false.',
        'capacity.integer' => 'Capacity must be an integer.',
        'capacity.min' => 'Capacity must be at least 1.',
        'charge.numeric' => 'Charge must be a number.',
        'charge.min' => 'Charge must be at least 0.',
        'deposit_amount.numeric' => 'Deposit amount must be a number.',
        'deposit_amount.min' => 'Deposit amount must be at least 0.',
        'slot_duration_mins.integer' => 'Slot duration must be an integer.',
        'slot_duration_mins.min' => 'Slot duration must be at least 1 minute.',
        'recurrence_type.in' => 'Recurrence type must be one of: daily, weekly, monthly,one_time,yearly',
        'recurrence_value.integer' => 'Recurrence value must be an integer.',
        'recurrence_value.min' => 'Recurrence value must be at least 1.',
        'daily_start_time.date_format' => 'Daily start time must be in H:i:s format.',
        'daily_end_time.date_format' => 'Daily end time must be in H:i:s format.',
        'daily_end_time.after' => 'Daily end time must be after daily start time.',
        'active_days.array' => 'Active days must be an array.',
        'active_days.*.in' => 'Active days must be valid weekdays (Monday to Sunday).',
        'is_active.boolean' => 'Is active must be true or false.',
        'image.string' => 'Image must be a string.',
        'message.string' => 'Message must be a string.',
    ];

    public function apply()
    {
        if (!$this->input || $this->input == '' || $this->input == ':input') {
            $this->status = 'error';
            $this->message = 'Please provide a valid Amenity Update input';
            $this->statusCode = 400;
            $this->data = [];
        }
        else{
            $result = $this->action('datasource:amenitiesUpdate', $this->pointer, $this->input);
        }
    }
}
