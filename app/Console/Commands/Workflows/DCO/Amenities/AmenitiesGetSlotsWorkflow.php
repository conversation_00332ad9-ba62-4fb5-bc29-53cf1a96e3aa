<?php

namespace App\Console\Commands\Workflows\DCO\Amenities;

use App\Console\Commands\Command;
use App\Console\Commands\Workflow;

class AmenitiesGetSlotsWorkflow extends Workflow
{
    protected $signature = 'workflow:amenitiesGetSlots {input}';
    protected $description = 'Workflow for Get Amenity Slots';

    public function apply()
    {
        if (!$this->input || $this->input == '' || $this->input == ':input') {
            $this->status = 'error';
            $this->message = 'Please provide a valid Amenity ID';
            $this->statusCode = 400;
            $this->data = [];
        }
        else{
            $result = $this->action('datasource:amenitiesGetSlots', $this->pointer, $this->input);
            $this->data = $result;
        }
    }
}
