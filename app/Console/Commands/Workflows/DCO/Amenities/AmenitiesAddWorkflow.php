<?php

namespace App\Console\Commands\Workflows\DCO\Amenities;

use Illuminate\Console\Command;
use App\Console\Commands\Workflow;

class AmenitiesAddWorkflow extends Workflow
{
    protected $signature = 'workflow:amenitiesAdd {input}';

    protected $description = 'Workflow for Amenities Add';

    protected $rules = [
        'soc_amen_name' => 'required|string',
        'status' => 'nullable|boolean',
        'chargeable' => 'required|boolean',
        'is_multi_subscriber' => 'nullable|boolean',
        'is_recurring' => 'nullable|boolean',
        'capacity' => 'nullable|integer|min:1',
        'charge' => 'nullable|numeric|min:0',
        'deposit' => 'nullable|numeric|min:0',
        'slot_duration_mins' => 'nullable|integer|min:1',
        'recurrence_type' => 'nullable|in:daily,weekly,monthly,one_time,yearly',
        'recurrence_value' => 'nullable|integer|min:1',
        'daily_start_time' => 'nullable|date_format:H:i:s',
        'daily_end_time' => 'nullable|date_format:H:i:s|after:daily_start_time',
        'active_days' => 'nullable|array',
        'active_days.*' => 'in:Monday,Tuesday,Wednesday,Thursday,Friday,Saturday,Sunday',
        'is_active' => 'nullable|boolean',
        'image' => 'nullable|string',
        'message' => 'nullable|string',
    ];

    protected $rulesMessage = [
        'soc_amen_name.required' => 'Amenity Name is required',
        'soc_amen_name.string' => 'Amenity Name must be a string',
        'capacity.integer' => 'Capacity must be an integer',
        'charge.numeric' => 'Charge must be a numeric value',
        'deposit_amount.numeric' => 'Deposit amount must be a numeric value',
        'daily_end_time.after' => 'End time must be after start time',
        'active_days.array' => 'Active days must be an array',
        'active_days.*.in' => 'Active days must be valid weekdays (Monday to Sunday)',
        'image.string' => 'Image must be a string',
        'daily_start_time.date_format' => 'Daily start time must be in H:i:s format',
        'daily_end_time.date_format' => 'Daily end time must be in H:i:s format',
        'daily_end_time.after' => 'Daily end time must be after daily start time',
        'recurrence_value.min' => 'Recurrence value must be at least 1',
        'recurrence_type.in' => 'Recurrence type must be daily, weekly, monthly, one_time, yearly',
        'slot_duration_mins.min' => 'Slot duration must be at least 1',
        'is_active.in' => 'Is active must be 0 or 1',
        'is_multi_subscriber.in' => 'Is multi subscriber must be 0 or 1',
        'is_recurring.in' => 'Is recurring must be 0 or 1',
        'chargeable.in' => 'chargeable must be 0 or 1',
        'status.in' => 'Status must be 0 or 1',
        'capacity.min' => 'Capacity must be at least 1',
        'charge.min' => 'Charge must be at least 0',
        'deposit_amount.min' => 'Deposit amount must be at least 0',
        'message.string' => 'Message must be a string',
    ];

    public function apply()
    {
        if (!$this->input || $this->input == '' || $this->input == ':input') {
            $this->status = 'error';
            $this->message = 'Please provide a valid Amenity to add';
            $this->statusCode = 400;
            $this->data = [];
        }
        else{
            $result = $this->action('datasource:amenitiesAdd', $this->pointer, $this->input);
        }
    }
}