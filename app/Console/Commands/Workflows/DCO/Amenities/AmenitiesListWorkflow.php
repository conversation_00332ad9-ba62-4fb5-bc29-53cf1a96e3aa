<?php

namespace App\Console\Commands\Workflows\DCO\Amenities;

use Illuminate\Console\Command;
use App\Console\Commands\Workflow;

class AmenitiesListWorkflow extends Workflow
{
    protected $signature = 'workflow:amenitiesList {input}';

    protected $description = 'Workflow for Amenities List';

    protected $schema = [
        "table" => [
            "tableTitle" => "Amenities",
            // "select_by" => [
            //     "amen_name" => "Amenity Name",
            //     "amen_num" => "Amenity Number"
            // ],
            // "filter_by" => [
            //     "status" => [
            //         "title" => "Status",
            //         "options" => [
            //             "cancelled" => "Cancelled"
            //         ]
            //     ],
            //     "purchase_mode" => [
            //         "title" => "Purchase Mode",
            //         "options" => [
            //             "credit" => "Credit",
            //             "cash" => "Cash"
            //         ]
            //     ],
            //     "vendor_bill_due_date" => [
            //         "title" => "Vendor Bill Due Date",
            //         "options" => [
            //             "overdue" => "Overdue",
            //             "due_in_one_month" => "Due in One month",
            //             "due_in_one_week" => "Due in One Week"
            //         ]
            //     ],
            //     "is_rcm" => [
            //         "title" => "RCM"
            //     ],
            //     "financial_year" => [
            //         "title" => "Financial Year",
            //         "options" => [
            //             "2019-04-01 to 2020-03-31" => "2019-04-01 to 2020-03-31",
            //             "2020-04-01 to 2021-03-31" => "2020-04-01 to 2021-03-31",
            //             "2021-04-01 to 2022-03-31" => "2021-04-01 to 2022-03-31"
            //         ]
            //     ]
            // ],
            "actions" => [
                [
                    "title" => "Add New Amenity",
                    "icon" => "ri-add-circle-line",
                    "color" => "success",
                    "variant" => "contained",
                    "form" => "amenitiesAdd",
                ]
            ],
            "fields" => [
                "*"
            ],
            "columns" => [
                [
                    "title" => "Amenity ID",
                    "key" => "id",
                ],
                [
                    "title" => "Amenity Name",
                    "key" => "soc_amen_name",
                ],
                [
                    "title" => "Chargeable",
                    "key" => "bookable",
                    "type" => "checkbox",
                    "options" => [
                        "1" => [
                            "color" => "success"
                        ],
                        "0" => [
                            "color" => "error",
                            
                        ]
                    ]
                ],
                // [
                //     "title" => "Multi Subscriber",
                //     "key" => "is_multi_subscriber",
                //     "type" => "chip",
                //     "options" => [
                //         "1" => [
                //             "title" => "Yes",
                //             "color" => "info"
                //         ],
                //         "0" => [
                //             "title" => "No",
                //             "color" => "default"
                //         ]
                //     ]
                // ],
                // [
                //     "title" => "Max Capacity",
                //     "key" => "capacity",
                // ],
                // [
                //     "title" => "Recurring Subscription",
                //     "key" => "is_recurring",
                //     "type" => "chip",
                //     "options" => [
                //         "1" => [
                //             "title" => "Yes",
                //             "color" => "info"
                //         ],
                //         "0" => [
                //             "title" => "No",
                //             "color" => "default"
                //         ]
                //     ]
                // ],
                [
                    "title" => "Charge ₹",
                    "key" => "charge",
                ],
                [
                    "title" => "Deposit Amount ₹",
                    "key" => "deposit",
                ],
                [
                    "title" => "Billing Cycle",
                    "key" => "recurrence_type",
                    "type" => "chip",
                    "options" => [
                        "monthly" => [
                            "title" => "Monthly",
                            "color" => "info"
                        ],
                        "yearly" => [
                            "title" => "Yearly",
                            "color" => "default"
                        ],
                        "one_time" => [
                            "title" => "One Time",
                            "color" => "warning"
                        ],
                    ]
                ],
                // [
                //     "title" => "Created At",
                //     "key" => "added_on",
                // ],
                [
                    "title" => "Actions",
                    "type" => "actions",
                    "key" => "actions",
                    "actions" => [
                        [
                            "title" => "Edit",
                            "icon" => "ri-edit-line",
                            "form" => "amenitiesEdit",
                        ],
                        [
                            "title" => "Delete",
                            "icon" => "ri-delete-bin-line",
                            "api" => [
                                "method" => "delete",
                                "url" => "/admin/amenities/:id",
                            ]
                        ]
                    ]
                ]
            ]
        ]
    ];

    public function apply()
    {
        if (!$this->input || $this->input == '' || $this->input == ':input') {
            $this->status = 'error';
            $this->message = 'Please provide a valid Amenities List';
            $this->statusCode = 400;
            $this->data = [];
        }
        else{
            $result = $this->action('datasource:amenitiesList', $this->pointer, $this->input);
            $this->data = $result;
            $this->meta['schema'] = $this->schema;
        }
    }
}