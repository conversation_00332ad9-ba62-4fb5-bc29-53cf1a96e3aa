<?php

namespace App\Console\Commands\Workflows\DCO\Helpdesk;

use App\Console\Commands\Workflow;

class HelpdeskGetFirstBodyWorkflow extends Workflow
{
    protected $signature = 'workflow:helpdeskGetFirstBody {input}';
    protected $description = 'Workflow for Get First Body from Helpdesk Threads1';

    public function apply()
    {
        if (!$this->input || $this->input == '' || $this->input == ':input' || empty($this->input['issue_id'])) {
            $this->status = 'error';
            $this->message = 'Please provide a valid Issue ID';
            $this->statusCode = 400;
            $this->data = [];
        } else {
            $result = $this->action('datasource:helpdeskGetFirstBody', $this->pointer, $this->input);
            $this->data = $result;
        }
    }
}
