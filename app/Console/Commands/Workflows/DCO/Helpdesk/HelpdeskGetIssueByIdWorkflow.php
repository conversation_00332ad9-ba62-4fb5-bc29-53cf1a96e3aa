<?php

namespace App\Console\Commands\Workflows\DCO\Helpdesk;

use App\Console\Commands\Workflow;

class HelpdeskGetIssueByIdWorkflow extends Workflow
{
    protected $signature = 'workflow:helpdeskGetIssueById {input}';
    protected $description = 'Workflow for Get Helpdesk Issue By Id';

    public function apply()
    {
        if (!$this->input || $this->input == '' || $this->input == ':input' || empty($this->input['issue_id'])) {
            $this->status = 'error';
            $this->message = 'Please provide a valid Issue ID';
            $this->statusCode = 400;
            $this->data = [];
        } else {
            $result = $this->action('datasource:helpdeskGetIssueById', $this->pointer, $this->input);
            $this->data = $result;
        }
    }
}
