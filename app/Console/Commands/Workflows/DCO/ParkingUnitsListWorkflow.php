<?php

namespace App\Console\Commands\Workflows\DCO;

use Illuminate\Console\Command;
use App\Console\Commands\Workflow;

class ParkingUnitsListWorkflow extends Workflow
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'workflow:parkingUnitsList {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Parking Allotment List';

    protected $hugeData = true;

    /**
     * Execute the console command.
     */
    public function apply()
    {
        $parkingUnitsList = $this->action('datasource:parkingUnitsList', $this->pointer, $this->request);
        $this->data = $parkingUnitsList;
    }
}
