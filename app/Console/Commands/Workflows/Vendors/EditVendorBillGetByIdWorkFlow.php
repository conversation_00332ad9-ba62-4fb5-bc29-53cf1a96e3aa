<?php

namespace App\Console\Commands\Workflows\Vendors;

use Illuminate\Console\Command;
use App\Console\Commands\Workflow;

class EditVendorBillGetByIdWorkFlow extends Workflow
{

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'workflow:editVendorBillGetById {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Vendors List';

    protected $formatter = [];

    protected $formatterByKeys = ['id'];
    protected $mapper = [];
    // protected $hugeData = true;


    public function apply()
    {
        $viewVendor = $this->action('datasource:editVendorBillGetById', $this->pointer, $this->request);
        $this->data = $viewVendor;
    }

}
