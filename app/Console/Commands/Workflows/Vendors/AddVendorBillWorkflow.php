<?php

namespace App\Console\Commands\Workflows\Vendors;

use Illuminate\Console\Command;
use App\Console\Commands\Workflow;

class AddVendorBillWorkflow extends workflow
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'workflow:addVendorBill {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Workflow for addVendorBill';

    protected $rules = [
    ];

    protected $rulesMessage = [
    ];

    /**
     * Execute the console command.
     */
    public function apply()
    {
        $addVendorCashPurchase = $this->action('datasource:addVendorBill', $this->pointer, $this->request);
    }
}
