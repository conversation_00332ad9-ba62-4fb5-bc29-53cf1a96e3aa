<?php

namespace App\Console\Commands\Workflows\Settings\Generalsettings;

use Illuminate\Console\Command;
use App\Console\Commands\Workflow;

class listUnitTypeDropDownWorkflow extends Workflow
{
    protected $signature = 'workflow:listUnitTypeDropDown {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'List Unit Type Drop Down Workflow';

    // protected $hugeData = true;

    /**
     * Execute the console command.
     */
    public function apply()
    {
        $primaryDS = $this->action('datasource:listUnitTypeDropDown', $this->pointer, $this->request);
        $this->data = $primaryDS;
    }
    public function concat($soc_building_name, $unit_flat_number)
    {
        return $soc_building_name . ' / ' . $unit_flat_number;
    }
}
