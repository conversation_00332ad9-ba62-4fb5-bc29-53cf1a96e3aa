<?php

namespace App\Console\Commands\Workflows\Settings\AutoInvoicingRule;

use Illuminate\Console\Command;
use App\Console\Commands\Workflow;

class GetRuleByIdWorkflow extends Workflow
{
    protected $signature = 'workflow:getRuleById {input?}';

    protected $description = 'Get Invoice Listing';
    protected $schema = [];

    public function apply()
    {
            $this->data = $this->action('datasource:getRuleById', $this->pointer, $this->input);
            return $this->data;
    }
}
