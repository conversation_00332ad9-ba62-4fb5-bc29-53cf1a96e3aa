<?php

namespace App\Console\Commands\Workflows\Settings\Staff;

use Illuminate\Console\Command;
use App\Console\Commands\Workflow;

class sendSmsWorkflow extends Workflow
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'workflow:sendSms {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Send SMS';

    /**
     * Execute the console command.
     */

    // request validation
    protected $validation = [
        'mobile_number' => 'required|regex:/^(\+?\d{1,3}[- ]?)?\d{10}$/',
        'staff_id' => 'required|string|max:160',
    ];
    public function apply()
    {
        $this->data = $this->action('datasource:sendSms', $this->pointer, $this->input);
    }
}
