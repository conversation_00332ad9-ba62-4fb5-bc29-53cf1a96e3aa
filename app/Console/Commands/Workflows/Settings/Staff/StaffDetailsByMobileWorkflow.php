<?php

namespace App\Console\Commands\Workflows\Settings\Staff;

use Illuminate\Console\Command;
use App\Console\Commands\Workflow;

class StaffDetailsByMobileWorkflow extends Workflow
{
    protected $signature = 'workflow:staffDetailsByMobile {input}';

    protected $description = 'Workflow for staff details by mobile';

    protected $rules = [
        'mobile' => 'required|string|max:15',
    ];

    protected $rulesMessage = [
        'mobile.required' => 'Mobile number is required',
        'mobile.string' => 'Mobile number must be a string',
        'mobile.max' => 'Mobile number must not exceed 15 characters',
    ];

    public function apply()
    {
        $mobile = $this->input['mobile'] ?? null;
        if(!$mobile || $mobile == '' || $mobile == ':mobile')
        {
            $this->status = 'error';
            $this->message = 'Please provide a valid mobile number';
            $this->statusCode = 400;
            return;
        }
        else{
            $this->data = $this->action('datasource:staffDetails', $this->pointer, $this->input);
        }
    }
}
