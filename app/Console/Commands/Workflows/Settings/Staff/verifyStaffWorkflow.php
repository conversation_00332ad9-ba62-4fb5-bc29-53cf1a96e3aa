<?php

namespace App\Console\Commands\Workflows\Settings\Staff;

use Illuminate\Console\Command;
use App\Console\Commands\Workflow;

class verifyStaffWorkflow extends Workflow
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'workflow:verifyStaff {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Verify staff';

    /**
     * Execute the console command.
     */
    public function apply()
    {
        $this->data = $this->action('datasource:verifyStaff', $this->pointer, $this->input);
    }
}
