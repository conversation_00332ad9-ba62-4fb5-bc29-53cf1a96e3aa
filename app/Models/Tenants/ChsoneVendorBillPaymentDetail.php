<?php

/**
 * Created by Reliese Model.
 */

namespace App\Models\Tenants;

use App\Models\TenantModel;
use Carbon\Carbon;

/**
 * Class ChsoneVendorBillPaymentDetail
 *
 * @property int $vendor_bill_payment_id
 * @property int $vendor_bill_id
 * @property int|null $vendor_id
 * @property int $soc_id
 * @property int|null $to_ledger_id
 * @property int|null $from_ledger_id
 * @property float|null $vendor_bill_payment_amount
 * @property float|null $tds
 * @property float|null $write_off
 * @property int|null $expense_tracker_id
 * @property Carbon $vendor_bill_payment_date
 * @property string $vendor_bill_payment_mode
 * @property string|null $vendor_bill_payment_comments
 * @property string|null $vendor_bill_payment_bank_name
 * @property string|null $vendor_bill_payment_cheque_number
 * @property string|null $vendor_bill_payment_card_num
 * @property Carbon|null $added_on
 * @property Carbon|null $updated_on
 * @property string $status
 *
 * @package App\Models\Tenants
 */
class ChsoneVendorBillPaymentDetail extends TenantModel
{
    protected $table = 'chsone_vendor_bill_payment_details';
    protected $primaryKey = 'vendor_bill_payment_id';
    public $timestamps = false;

    protected $casts = [
        'vendor_bill_id' => 'int',
        'vendor_id' => 'int',
        'soc_id' => 'int',
        'to_ledger_id' => 'int',
        'from_ledger_id' => 'int',
        'vendor_bill_payment_amount' => 'float',
        'tds' => 'float',
        'write_off' => 'float',
        'expense_tracker_id' => 'int',
        'vendor_bill_payment_date' => 'datetime',
        'added_on' => 'datetime',
        'updated_on' => 'datetime'
    ];

    protected $fillable = [
        'vendor_bill_id',
        'vendor_id',
        'soc_id',
        'to_ledger_id',
        'from_ledger_id',
        'vendor_bill_payment_amount',
        'tds',
        'write_off',
        'expense_tracker_id',
        'vendor_bill_payment_date',
        'vendor_bill_payment_mode',
        'vendor_bill_payment_comments',
        'vendor_bill_payment_bank_name',
        'vendor_bill_payment_cheque_number',
        'vendor_bill_payment_card_num',
        'added_on',
        'updated_on',
        'status'
    ];


    public function quickPayment($soc_id, $data = [])
    {
        $ChsoneGrpLedgerTreeObj = new ChsoneGrpLedgerTree();
        $context = $data['vendor_bill_type_purchase'];
        if($context == 'cashtransfer') {
            $context = 'bank';
        }
        $fromLedgerAccountDetails = $ChsoneGrpLedgerTreeObj->getLedgerDetail($soc_id, array("context_ref_id" => $data['vendor_id'], "context" => "vendor", "entity_type" => "ledger"));
        $toLedgerAccountDetails = $ChsoneGrpLedgerTreeObj->getLedgerDetail($soc_id, array("context" => $context, "entity_type" => "ledger"));
        $TotalBillPaymentAmount = $data['vendor_bill_payment_amount'];
        foreach ($data['sequence'] as $sequence) {
            if ($TotalBillPaymentAmount <= 0) {
                break;
            }
            $vednor_bill_details = ChsoneVendorBillMaster::where('soc_id', $soc_id)
                ->where('vendor_bill_id', $sequence)
                ->first();
            $comment = "Bill number " . $vednor_bill_details['vendor_bill_num'] . " paid on date " . $data['vendor_bill_payment_date'];
            // fetch bank account name from chsone_grp_ledger_tree whos ledger_account_id is $data['bank_ledger']
            if (isset($data['bank_ledger'])) {
                $bankAccountName = ChsoneGrpLedgerTree::where('ledger_account_id', $data['bank_ledger'])->first();
            }

            $payment_to_make = min($TotalBillPaymentAmount, $vednor_bill_details['vendor_bill_amount']);
            $TotalBillPaymentAmount -= $payment_to_make;

            if ($payment_to_make > 0) {
                $paymentData = [
                    'vendor_id' => $data['vendor_id'] ?? null,
                    'soc_id' => $soc_id ?? null,
                    'vendor_bill_id' => $sequence ?? null,
                    'to_ledger_id' => $toLedgerAccountDetails['ledger_account_id'] ?? null,
                    'from_ledger_id' => $fromLedgerAccountDetails['ledger_account_id'] ?? null,
                    'vendor_bill_payment_amount' => $payment_to_make ?? 0,
                    'tds' => $data['tds'] ?? 0,
                    'write_off' => $data['vendor_bill_wo'] ?? 0,
                    'expense_tracker_id' => $data['expense_tracker_id'] ?? null,
                    'vendor_bill_payment_comments' => $data['vendor_bill_payment_comments'] ?? $comment,
                    'vendor_bill_payment_bank_name' => $bankAccountName->ledger_account_name ?? null,
                    'vendor_bill_payment_cheque_number' => $data['vendor_bill_payment_cheque_number'] ?? null,
                    'vendor_bill_payment_card_num' => $data['vendor_bill_payment_card_num'] ?? null,
                    'vendor_bill_payment_date' => $data['vendor_bill_payment_date'] ?? Carbon::now(),
                    'vendor_bill_payment_mode' => $data['vendor_bill_type_purchase'],
                    'status' => 1,
                    'added_on' => $data['added_on'] ?? Carbon::now(),
                    'updated_on' => $data['updated_on'] ?? Carbon::now(),
                ];

                $newPaymentDetail = new self();
                $newPaymentDetail->fill($paymentData);

                if ($newPaymentDetail->save()) {
                    if ($payment_to_make >= $vednor_bill_details['vendor_bill_amount']) {
                        $updateBillStatus = ChsoneVendorBillMaster::where('soc_id', $soc_id)
                            ->where('vendor_bill_id', $sequence)
                            ->update(['payment_status' => 'paid']);
                    } else {
                        $updateBillStatus = ChsoneVendorBillMaster::where('soc_id', $soc_id)
                            ->where('vendor_bill_id', $sequence)
                            ->update(['payment_status' => 'partialpaid']);
                    }
                }
            }
            $this->addTransactionEntry($data, $soc_id, $fromLedgerAccountDetails, $toLedgerAccountDetails, $sequence);
        }
        $income_account_id = '';
        if ($data['vendor_bill_type_purchase'] == 'cash' || $data['vendor_bill_type_purchase'] == 'cashtransfer') {
            // Handle empty arrays and ensure we have a string value
            if (is_array($data['bank_ledger'])) {
                $income_account_id = !empty($data['bank_ledger']) ? (string)reset($data['bank_ledger']) : '';
            } else {
                $income_account_id = (string)$data['bank_ledger'];
            }
        }
        $vendorName = ChsoneVendorsMaster::where('vendor_id', $data['vendor_id'])->first();
        if ($TotalBillPaymentAmount > 0) {
            // make antry in chsone_credit_accounts
            $creditAccountData = [
                'soc_id' => $soc_id,
                'payment_tracker_id' => $data['expense_tracker_id'],
                'invoice_number' => '',
                'payment_date' => $data['vendor_bill_payment_date'],
                'account_id' => $data['vendor_id'],
                'account_name' => $vendorName->vendor_name,
                'account_context' => 'Advance',
                'amount' => $TotalBillPaymentAmount,
                'type' => 'expense',
                'payment_mode' => $data['vendor_bill_type_purchase'],
                'trasaction_type' => 'dr',
                'narration' => "Adjustable advance received via {$data['vendor_bill_type_purchase']} (" . ($data['vendor_bill_payment_comments'] ?? '') . ")",
                'use_credit' => 'adjustable',
                'is_invoice_rectification' => '',
                'is_locked' => '',
                'income_account_id' => $income_account_id,
                'use_credit_for' => '',
                'reference_no' => '',
                'context' => 'user',
                'created_by' => $fromLedgerAccountDetails['created_by'],
                'updated_by' => $fromLedgerAccountDetails['created_by'],
                'created_date' => now(),
                'updated_date' => now(),
            ];
            $newCreditAccount = new ChsoneCreditAccount();
            $newCreditAccount->fill($creditAccountData);
            if ($newCreditAccount->save()) {
                $txn = new ChsoneLedgerTransaction();
                $txn->soc_id = $soc_id;
                $txn->transaction_date = $data['vendor_bill_payment_date'];
                $txn->ledger_account_id = $fromLedgerAccountDetails['ledger_account_id'];
                $txn->ledger_account_name = $fromLedgerAccountDetails['ledger_account_name'];
                $txn->voucher_type = 'payment';
                $txn->transaction_type = 'dr';
                $txn->payment_mode = $data['vendor_bill_type_purchase'];
                $txn->payment_reference = $data['vendor_bill_payment_cheque_number'] ?? '';
                $txn->transaction_amount = $TotalBillPaymentAmount;
                $txn->txn_from_id = 0;
                $txn->memo_desc = "Adjustable advance received via {$data['vendor_bill_type_purchase']} (" . ($data['vendor_bill_payment_comments'] ?? '') . ")";
                $txn->is_opening_balance = 0;
                $txn->is_reconciled = 0;
                $txn->created_by = $fromLedgerAccountDetails['created_by'];
                $txn->added_on = now();
                $txn->voucher_reference_number = '';
                $txn->voucher_reference_id = $data['expense_tracker_id'];
                $txn->is_cancelled = 0;

                if ($txn->save()) {
                    $transaction_entry_id = $txn->txn_id;
                } else {
                    return false;
                }

                // Need to add above entry in ledger transaction table with just changing the ledger_account_id.
                $txn1 = new ChsoneLedgerTransaction();
                $txn1->soc_id = $soc_id;
                $txn1->transaction_date = $data['vendor_bill_payment_date'];
                $txn1->ledger_account_id = $toLedgerAccountDetails['ledger_account_id'];
                $txn1->ledger_account_name = $toLedgerAccountDetails['ledger_account_name'];
                $txn1->voucher_type = 'payment';
                $txn1->transaction_type = 'cr';
                $txn1->payment_mode = $data['vendor_bill_type_purchase'];
                $txn1->payment_reference = $data['vendor_bill_payment_cheque_number'] ?? '';
                $txn1->transaction_amount = $TotalBillPaymentAmount;
                $txn1->txn_from_id = $transaction_entry_id;
                $txn1->memo_desc = "Adjustable advance received via {$data['vendor_bill_type_purchase']} (" . ($data['vendor_bill_payment_comments'] ?? '') . ")";
                $txn1->is_opening_balance = 0;
                $txn1->is_reconciled = 0;
                $txn1->created_by = $fromLedgerAccountDetails['created_by'];
                $txn1->added_on = now();
                $txn1->voucher_reference_number = '';
                $txn1->voucher_reference_id = $data['expense_tracker_id'];
                $txn1->is_cancelled = 0;

                if ($txn1->save()) {
                    $transaction_entry_id = $txn1->txn_id;
                } else {
                    return false;
                }
            }
        }


        return $vendorName->vendor_name;
    }

    public function addTransactionEntry($data, $soc_id, $fromLedgerAccountDetails, $toLedgerAccountDetails, $sequence)
    {
        $txn = new ChsoneLedgerTransaction();
        $txn->soc_id = $soc_id;
        $txn->transaction_date = $data['vendor_bill_payment_date'];
        $txn->ledger_account_id = $fromLedgerAccountDetails['ledger_account_id'];
        $txn->ledger_account_name = $fromLedgerAccountDetails['ledger_account_name'];
        $txn->voucher_type = 'payment';
        $txn->transaction_type = 'dr';
        $txn->payment_mode = $data['vendor_bill_type_purchase'] ?? '';
        $txn->payment_reference = $data['vendor_bill_payment_cheque_number'] ?? '';
        $txn->transaction_amount = $data['vendor_bill_payment_amount'] ?? 0;
        $txn->txn_from_id = 0;
        $txn->memo_desc = "Bill number " . $sequence . " on date " . $data['vendor_bill_payment_date'] . "  [payment via cash transfer]";
        $txn->is_opening_balance = 0;
        $txn->is_reconciled = 0;
        $txn->created_by = $fromLedgerAccountDetails['created_by'] ?? $this->input['user_id'];
        $txn->added_on = now();
        $txn->voucher_reference_number = '';
        $txn->voucher_reference_id = $data['expense_tracker_id'];
        $txn->is_cancelled = 0;

        if ($txn->save()) {
            $transaction_entry_id = $txn->txn_id;
        } else {
            return false;
        }

        // Need to add above entry in ledger transaction table with just changing the ledger_account_id.
        $txn1 = new ChsoneLedgerTransaction();
        $txn1->soc_id = $soc_id;
        $txn1->transaction_date = $data['vendor_bill_payment_date'];
        $txn1->ledger_account_id = $toLedgerAccountDetails['ledger_account_id'];
        $txn1->ledger_account_name = $toLedgerAccountDetails['ledger_account_name'];
        $txn1->voucher_type = 'payment';
        $txn1->transaction_type = 'cr';
        $txn1->payment_mode = $data['vendor_bill_type_purchase'] ?? '';
        $txn1->payment_reference = $data['vendor_bill_payment_cheque_number'] ?? '';
        $txn1->transaction_amount = $data['vendor_bill_payment_amount'];
        $txn1->txn_from_id = $transaction_entry_id;
        $txn1->memo_desc = "Bill number " . $sequence . " on date " . $data['vendor_bill_payment_date'] . "  [payment via cash transfer]";
        $txn1->is_opening_balance = 0;
        $txn1->is_reconciled = 0;
        $txn1->created_by = $fromLedgerAccountDetails['created_by'] ?? $this->input['user_id'];
        $txn1->added_on = now();
        $txn1->voucher_reference_number = '';
        $txn1->voucher_reference_id = $data['expense_tracker_id'];
        $txn1->is_cancelled = 0;

        if ($txn1->save()) {
            $transaction_entry_id = $txn->txn_id;
        } else {
            return false;
        }
    }
}
