# Amenities Booking API Frontend Integration Guide

This guide explains how to consume the Amenities Booking API endpoints in your frontend application.

## Base URL
```
http://localhost/api
```

## Endpoints

### 1. List Amenities
- **GET** `/amenities`
- Returns a list of all amenities.

### 2. Add Amenity
- **POST** `/amenities`
- Body: `{ ...amenityFields }`
- Returns the created amenity.

### 3. Get Amenity Details
- **GET** `/amenities/{id}`
- Returns details for a specific amenity.

### 4. Edit Amenity
- **PUT** `/amenities/{id}`
- Body: `{ ...fieldsToUpdate }`
- Updates the amenity.

### 5. Delete Amenity
- **DELETE** `/amenities/{id}`
- Deletes the amenity.

### 6. Book Amenity
- **POST** `/amenities/{amenity_id}/bookings`
- Body: `{ user_id, slot_id, date }`
- Books a slot for a user.

### 7. Get Amenity Bookings & Slots
- **GET** `/amenities/{amenity_id}/bookings?page=1&perPage=10`
- Returns slot configuration and bookings for an amenity. Supports pagination.

### 8. Get All User Bookings
- **GET** `/users/{user_id}/bookings`
- Returns all bookings for a user.

### 9. Get User Bookings for Amenity
- **GET** `/users/{user_id}/amenities/{amenity_id}/bookings`
- Returns bookings for a user for a specific amenity.

### 10. Cancel Booking
- **PATCH** `/amenities/{amenity_id}/bookings/{booking_id}`
- Cancels a booking. No body required.
- Returns success or error message.

### 11. Verify Booking Pin
- **POST** `/amenities/verify-pin`
- Body: `{ booking_id, pin }`
- Returns success if pin is valid and in correct time window, else error.

## Example Usage (JavaScript)

```js
// Book an amenity
fetch('/api/amenities/1/bookings', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({ user_id: 123, slot_id: 5, date: '2025-07-10' })
})
  .then(res => res.json())
  .then(data => console.log(data));

// Cancel a booking
fetch('/api/amenities/1/bookings/42', {
  method: 'PATCH',
})
  .then(res => res.json())
  .then(data => console.log(data));

// Verify a booking pin
fetch('/api/amenities/verify-pin', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({ booking_id: 42, pin: '123456' })
})
  .then(res => res.json())
  .then(data => console.log(data));
```

## Notes
- All endpoints return JSON.
- Use appropriate HTTP methods as described.
- For protected endpoints, include authentication headers if required by your backend.
- Handle error messages and status codes for user feedback.
